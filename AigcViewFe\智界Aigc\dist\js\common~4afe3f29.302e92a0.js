(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~4afe3f29"],{"0151":function(e,t,a){},"01fe":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-editable-table",{attrs:{columns:e.columns,dataSource:e.dataSource,rowNumber:!0,actionButton:!0,rowSelection:!0,maxHeight:400},on:{valueChange:e.handleValueChange}})},n=[],o=a("e2e0"),l=a("7550"),s={name:"ThreeLinkage",components:{JEditableTable:l["default"]},data:function(){return{columns:[{title:"省/直辖市/自治区",key:"s1",type:o["a"].select,width:"240px",options:[],placeholder:"请选择${title}"},{title:"市",key:"s2",type:o["a"].select,width:"240px",options:[],placeholder:"请选择${title}"},{title:"县/区",key:"s3",type:o["a"].select,width:"240px",options:[],placeholder:"请选择${title}"}],dataSource:[],mockData:[{label:"北京市",value:"110000",parent:null},{label:"天津市",value:"120000",parent:null},{label:"河北省",value:"130000",parent:null},{label:"上海市",value:"310000",parent:null},{label:"北京市",value:"110100",parent:"110000"},{label:"天津市市",value:"120100",parent:"120000"},{label:"石家庄市",value:"130100",parent:"130000"},{label:"唐山市",value:"130200",parent:"130000"},{label:"秦皇岛市",value:"130300",parent:"130000"},{label:"上海市",value:"310100",parent:"310000"},{label:"东城区",value:"110101",parent:"110100"},{label:"西城区",value:"110102",parent:"110100"},{label:"朝阳区",value:"110105",parent:"110100"},{label:"和平区",value:"120101",parent:"120000"},{label:"河东区",value:"120102",parent:"120000"},{label:"河西区",value:"120103",parent:"120000"},{label:"黄浦区",value:"310101",parent:"310100"},{label:"徐汇区",value:"310104",parent:"310100"},{label:"长宁区",value:"310105",parent:"310100"},{label:"长安区",value:"130102",parent:"130100"},{label:"桥西区",value:"130104",parent:"130100"},{label:"新华区",value:"130105",parent:"130100"},{label:"路南区",value:"130202",parent:"130200"},{label:"路北区",value:"130203",parent:"130200"},{label:"古冶区",value:"130204",parent:"130200"},{label:"海港区",value:"130302",parent:"130300"},{label:"山海关区",value:"130303",parent:"130300"},{label:"北戴河区",value:"130304",parent:"130300"}]}},mounted:function(){this.columns[0].options=this.request(null)},methods:{request:function(e){return this.mockData.filter((function(t){return t.parent===e}))},handleValueChange:function(e){var t=e.type,a=e.row,r=e.column,n=e.value,l=e.target;t===o["a"].select&&("s1"===r.key?(this.columns[1].options=this.request(n),l.setValues([{rowKey:a.id,values:{s2:"",s3:""}}]),this.columns[2].options=[]):"s2"===r.key&&(this.columns[2].options=this.request(n),l.setValues([{rowKey:a.id,values:{s3:""}}])))}}},i=s,c=a("2877"),d=Object(c["a"])(i,r,n,!1,null,"bd50bcca",null);t["default"]=d.exports},"02d5":function(e,t,a){"use strict";var r=a("9c24"),n=a.n(r);n.a},"0467":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("a-table",{attrs:{rowKey:"rowIndex",bordered:"",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination},on:{change:e.handleTableChange}})],1)},n=[];function o(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var l={name:"TableTotal",data:function(){return{columns:[{title:"#",width:"180px",align:"center",dataIndex:"rowIndex",customRender:function(e,t,a){return"合计"!==e?parseInt(a)+1:e}},{title:"姓名",dataIndex:"name"},{title:"贡献点",dataIndex:"point"},{title:"等级",dataIndex:"level"},{title:"更新时间",dataIndex:"updateTime"}],ipagination:{current:1,pageSize:10,pageSizeOptions:["10","20","30"],showTotal:function(e,t){return t[0]+"-"+t[1]+" 共"+e+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0},dataSource:[{id:"1",name:"张三",point:23,level:3,updateTime:"2019-8-14"},{name:"小王",point:6,level:1,updateTime:"2019-8-13"},{name:"李四",point:53,level:8,updateTime:"2019-8-12"},{name:"小红",point:44,level:5,updateTime:"2019-8-11"},{name:"王五",point:97,level:10,updateTime:"2019-8-10"},{name:"小明",point:33,level:2,updateTime:"2019-8-10"},{name:"小张",point:33,level:2,updateTime:"2019-8-10"},{name:"小六",point:33,level:2,updateTime:"2019-8-10"},{name:"小五",point:33,level:2,updateTime:"2019-8-10"},{name:"小赵",point:33,level:2,updateTime:"2019-8-10"},{name:"李华",point:33,level:2,updateTime:"2019-8-10"},{name:"小康",point:33,level:2,updateTime:"2019-8-10"},{name:"小鹿",point:33,level:2,updateTime:"2019-8-10"}],newArr:[],newDataSource:[]}},mounted:function(){this.newDataSource=this.dataSource,this.dataHandling(this.ipagination.pageSize-1)},watch:{"ipagination.pageSize":function(e){this.dataHandling(e-1)}},methods:{tableAddTotalRow:function(e,t){var a="rowIndex",r=o({},a,"合计");e.forEach((function(e){var n=e.key,o=e.dataIndex;if(![n,o].includes(a)){var l=0;t.forEach((function(e){l+=/^\d+\.?\d?$/.test(e[o])?Number.parseInt(e[o]):Number.NaN})),Number.isNaN(l)&&(l="-"),r[o]=l}})),t.push(r)},handleTableChange:function(e,t,a){this.ipagination=e},dataHandling:function(e){this.newArr=[];for(var t=this.newDataSource.length,a=0,r=0;r<t;r++)r%e===0&&0!==r&&(this.newArr.push(this.newDataSource.slice(a,r)),a=r),r+1===t&&this.newArr.push(this.newDataSource.slice(a,r+1));for(var n=this.newArr,o=0;o<n.length;o++){for(var l=n[o],s={name:"-",updateTime:"-",rowIndex:"合计"},i=0,c=0,d=0;d<l.length;d++)i+=l[d].level,c+=l[d].point;s.level=i,s.point=c,n[o].push(s)}for(var u=[],m=0;m<n.length;m++){var p=n[m];for(var f in p)u.push(p[f])}this.dataSource=Object.values(u)}}},s=l,i=a("2877"),c=Object(i["a"])(s,r,n,!1,null,"bf07d0ec",null);t["default"]=c.exports},"17d8":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{md:6,sm:8}},[a("a-form-item",{attrs:{label:"消息标题"}},[a("a-input",{attrs:{placeholder:"请输入消息标题"},model:{value:e.queryParam.esTitle,callback:function(t){e.$set(e.queryParam,"esTitle",t)},expression:"queryParam.esTitle"}})],1)],1),a("a-col",{attrs:{md:6,sm:8}},[a("a-form-item",{attrs:{label:"发送内容"}},[a("a-input",{attrs:{placeholder:"请输入发送内容"},model:{value:e.queryParam.esContent,callback:function(t){e.$set(e.queryParam,"esContent",t)},expression:"queryParam.esContent"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{md:6,sm:8}},[a("a-form-item",{attrs:{label:"接收人"}},[a("a-input",{attrs:{placeholder:"请输入接收人"},model:{value:e.queryParam.esReceiver,callback:function(t){e.$set(e.queryParam,"esReceiver",t)},expression:"queryParam.esReceiver"}})],1)],1)]:e._e(),a("a-col",{attrs:{md:6,sm:8}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("消息")}}},[e._v("导出")]),a("a-upload",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("\n          删除\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"esContent",fn:function(e){return a("span",{},[a("j-ellipsis",{attrs:{value:e,length:10}})],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{attrs:{href:"javascript:;"},on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多"),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}]},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("sysMessage-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},n=[],o=a("89ba"),l=a("b65a"),s=a("d579"),i={name:"SysMessageList",mixins:[l["a"]],components:{JEllipsis:s["default"],SysMessageModal:o["default"]},data:function(){return{description:"消息管理页面",show:!1,columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"消息标题",align:"center",dataIndex:"esTitle"},{title:"发送内容",align:"center",dataIndex:"esContent",scopedSlots:{customRender:"esContent"}},{title:"接收人",align:"center",dataIndex:"esReceiver"},{title:"发送次数",align:"center",dataIndex:"esSendNum"},{title:"发送状态",align:"center",dataIndex:"esSendStatus_dictText"},{title:"发送时间",align:"center",dataIndex:"esSendTime"},{title:"发送方式",align:"center",dataIndex:"esType_dictText"},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{customRender:"action"}}],url:{list:"/sys/message/sysMessage/list",delete:"/sys/message/sysMessage/delete",deleteBatch:"/sys/message/sysMessage/deleteBatch",exportXlsUrl:"sys/message/sysMessage/exportXls",importExcelUrl:"sys/message/sysMessage/importExcel"}}},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{}},c=i,d=(a("570f"),a("2877")),u=Object(d["a"])(c,r,n,!1,null,"440e02b6",null);t["default"]=u.exports},"1c31":function(e,t,a){},"1fad":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:1200,visible:e.visible,destroyOnClose:!0,maskClosable:!1,confirmLoading:e.confirmLoading},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{"label-col":e.labelCol,"wrapper-col":e.wrapperCol,model:e.model}},[a("a-row",{staticClass:"form-row",attrs:{gutter:0}},[a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单号",prop:"orderCode",rules:[{required:!0,message:"请输入订单号!"}]}},[a("a-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.model.orderCode,callback:function(t){e.$set(e.model,"orderCode",t)},expression:"model.orderCode"}})],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单类型"}},[a("a-select",{attrs:{placeholder:"请选择订单类型"},model:{value:e.model.ctype,callback:function(t){e.$set(e.model,"ctype",t)},expression:"model.ctype"}},[a("a-select-option",{attrs:{value:"1"}},[e._v("国内订单")]),a("a-select-option",{attrs:{value:"2"}},[e._v("国际订单")])],1)],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单日期"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss"},model:{value:e.model.orderDate,callback:function(t){e.$set(e.model,"orderDate",t)},expression:"model.orderDate"}})],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:0}},[a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单金额"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入订单金额"},model:{value:e.model.orderMoney,callback:function(t){e.$set(e.model,"orderMoney",t)},expression:"model.orderMoney"}})],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单备注"}},[a("a-input",{attrs:{placeholder:"请输入订单备注"},model:{value:e.model.content,callback:function(t){e.$set(e.model,"content",t)},expression:"model.content"}})],1)],1)],1)],1),a("a-tabs",{on:{change:e.handleChangeTabs},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[a("a-tab-pane",{key:"1",attrs:{tab:"客户信息",forceRender:!0}},[a("j-vxe-table",{ref:"editableTable1",staticStyle:{"margin-top":"8px"},attrs:{toolbar:"","row-number":"","row-selection":"","keep-source":"",height:300,loading:e.table1.loading,dataSource:e.table1.dataSource,columns:e.table1.columns}})],1),a("a-tab-pane",{key:"2",attrs:{tab:"机票信息",forceRender:!0}},[a("j-vxe-table",{ref:"editableTable2",staticStyle:{"margin-top":"8px"},attrs:{toolbar:"","row-number":"","row-selection":"","keep-source":"",height:300,loading:e.table2.loading,dataSource:e.table2.dataSource,columns:e.table2.columns}})],1)],1)],1)],1)},n=[],o=a("7550"),l=a("54ac"),s=a("0fea"),i=a("2475"),c=a("2dab");function d(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function u(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?d(Object(a),!0).forEach((function(t){m(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):d(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function m(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var p={name:"JeecgOrderModalForJvexTable",components:{JDate:c["default"],JEditableTable:o["default"]},data:function(){return{title:"操作",visible:!1,confirmLoading:!1,model:{},labelCol:{xs:{span:24},sm:{span:6}},wrapperCol:{xs:{span:24},sm:{span:18}},activeKey:"1",table1:{loading:!1,dataSource:[],columns:[{title:"客户名",key:"name",width:"24%",type:i["b"].input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"性别",key:"sex",width:"18%",type:i["b"].select,options:[{title:"男",value:"1"},{title:"女",value:"2"}],defaultValue:"",placeholder:"请选择${title}"},{title:"身份证号",key:"idcard",width:"24%",type:i["b"].input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{pattern:"^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|[xX])$",message:"${title}格式不正确"}]},{title:"手机号",key:"telphone",width:"24%",type:i["b"].input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{pattern:"^1(3|4|5|7|8)\\d{9}$",message:"${title}格式不正确"}]}]},table2:{loading:!1,dataSource:[],columns:[{title:"航班号",key:"ticketCode",width:"40%",type:i["b"].input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"航班时间",key:"tickectDate",width:"30%",type:i["b"].date,placeholder:"请选择${title}",defaultValue:""}]},url:{add:"/test/jeecgOrderMain/add",edit:"/test/jeecgOrderMain/edit",orderCustomerList:"/test/jeecgOrderMain/queryOrderCustomerListByMainId",orderTicketList:"/test/jeecgOrderMain/queryOrderTicketListByMainId"}}},created:function(){},methods:{getAllTable:function(){return Promise.all([Object(l["c"])(this,"editableTable1"),Object(l["c"])(this,"editableTable2")])},add:function(){this.getAllTable().then((function(e){})),this.edit({})},edit:function(e){if(this.visible=!0,this.activeKey="1",this.model=Object.assign({},e),this.model.id){var t={id:this.model.id};this.requestTableData(this.url.orderCustomerList,t,this.table1),this.requestTableData(this.url.orderTicketList,t,this.table2)}},close:function(){var e=this;this.visible=!1,this.getAllTable().then((function(t){e.table1.dataSource=[],e.table2.dataSource=[]})),this.$emit("close")},requestTableData:function(e,t,a){a.loading=!0,Object(s["c"])(e,t).then((function(e){a.dataSource=e.result||[]})).finally((function(){a.loading=!1}))},handleOk:function(){this.validateFields()},handleCancel:function(){this.close()},handleChangeTabs:function(e){Object(l["c"])(this,"editableTable".concat(e)).then((function(e){e.resetScrollTop()}))},validateFields:function(){var e=this;this.getAllTable().then((function(t){return Object(l["d"])(e.$refs.form,e.model,t)})).then((function(t){var a=e.classifyIntoFormData(t);return e.requestAddOrEdit(a)})).catch((function(t){t.error===l["a"]&&(e.activeKey=null==t.index?e.activeKey:(t.index+1).toString())}))},classifyIntoFormData:function(e){var t=Object.assign(this.model,e.formValue);return u(u({},t),{},{jeecgOrderCustomerList:e.tablesValue[0].tableData,jeecgOrderTicketList:e.tablesValue[1].tableData})},requestAddOrEdit:function(e){var t=this,a=this.url.add,r="post";this.model.id&&(a=this.url.edit,r="put"),this.confirmLoading=!0,Object(s["h"])(a,e,r).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok"),t.close()):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}},f=p,h=a("2877"),b=Object(h["a"])(f,r,n,!1,null,"5a14fcb0",null);t["default"]=b.exports},2832:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("a-tabs",{attrs:{defaultActiveKey:"1"},on:{change:e.callback}},[a("a-tab-pane",{key:"1",attrs:{tab:"柱状图"}},[a("a-row",[a("a-col",{attrs:{span:10}},[a("a-radio-group",{attrs:{value:e.barType},on:{change:e.statisticst}},[a("a-radio-button",{attrs:{value:"year"}},[e._v("按年统计")]),a("a-radio-button",{attrs:{value:"month"}},[e._v("按月统计")]),a("a-radio-button",{attrs:{value:"category"}},[e._v("按类别统计")]),a("a-radio-button",{attrs:{value:"cabinet"}},[e._v("按柜号统计")])],1)],1),a("a-col",{attrs:{span:14}},[(e.barType,e._e())],1),a("bar",{staticClass:"statistic",attrs:{title:"档案统计",dataSource:e.countSource,height:400}})],1)],1),a("a-tab-pane",{key:"2",attrs:{tab:"饼状图"}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:10}},[a("a-radio-group",{attrs:{value:e.pieType},on:{change:e.statisticst}},[a("a-radio-button",{attrs:{value:"year"}},[e._v("按年统计")]),a("a-radio-button",{attrs:{value:"month"}},[e._v("按月统计")]),a("a-radio-button",{attrs:{value:"category"}},[e._v("按类别统计")]),a("a-radio-button",{attrs:{value:"cabinet"}},[e._v("按柜号统计")])],1)],1),a("a-col",{attrs:{span:14}},[(e.pieType,e._e())],1),a("pie",{staticClass:"statistic",attrs:{title:"档案统计",dataSource:e.countSource,height:450}})],1)],1)],1)],1)},n=[],o=a("edd9"),l=a("6cb2"),s=a("da05"),i=a("0fea"),c={name:"ArchivesStatisticst",components:{ACol:s["b"],Bar:o["default"],Pie:l["default"]},data:function(){return{description:"档案统计页面",queryParam:{},countSource:[],barType:"year",barDate:["month","month"],barValue:[],pieType:"year",pieDate:["month","month"],pieValue:[],tabStatus:"bar",url:{getYearCountInfo:"/mock/api/report/getYearCountInfo",getMonthCountInfo:"/mock/api/report/getMonthCountInfo",getCntrNoCountInfo:"/mock/api/report/getCntrNoCountInfo",getCabinetCountInfo:"/mock/api/report/getCabinetCountInfo"}}},created:function(){var e=this.url.getYearCountInfo;this.loadDate(e,"year",{})},methods:{loadDate:function(e,t,a){var r=this;Object(i["c"])(e,a,"get").then((function(e){if(e.success)r.countSource=[],"year"===t&&r.getYearCountSource(e.result),"month"===t&&r.getMonthCountSource(e.result),"category"===t&&r.getCategoryCountSource(e.result),"cabinet"===t&&r.getCabinetCountSource(e.result);else{var a=r;a.$message.warning(e.message)}}))},getYearCountSource:function(e){for(var t=0;t<e.length;t++)"bar"===this.tabStatus?this.countSource.push({x:"".concat(e[t].year,"年"),y:e[t].yearcount}):this.countSource.push({item:"".concat(e[t].year,"年"),count:e[t].yearcount})},getMonthCountSource:function(e){for(var t=0;t<e.length;t++)"bar"===this.tabStatus?this.countSource.push({x:e[t].month,y:e[t].monthcount}):this.countSource.push({item:e[t].month,count:e[t].monthcount})},getCategoryCountSource:function(e){for(var t=0;t<e.length;t++)"bar"===this.tabStatus?this.countSource.push({x:e[t].classifyname,y:e[t].cntrnocount}):this.countSource.push({item:e[t].classifyname,count:e[t].cntrnocount})},getCabinetCountSource:function(e){for(var t=0;t<e.length;t++)"bar"===this.tabStatus?this.countSource.push({x:e[t].cabinetname,y:e[t].cabinetcocunt}):this.countSource.push({item:e[t].cabinetname,count:e[t].cabinetcocunt})},callback:function(e){"1"===e?(this.tabStatus="bar",this.queryDatebar()):(this.tabStatus="pie",this.queryDatepie())},statisticst:function(e){"pie"===this.tabStatus?(this.pieType=e.target.value,this.queryDatepie()):(this.barType=e.target.value,this.queryDatebar())},queryDatebar:function(){this.barValue.length>0?this.getUrl(this.barType,{startTime:this.barValue[0]._d,endTime:this.barValue[1]._d}):this.getUrl(this.barType,{})},queryDatepie:function(){this.pieValue.length>0?this.getUrl(this.pieType,{startTime:this.pieValue[0]._d,endTime:this.pieValue[1]._d}):this.getUrl(this.pieType,{})},searchReset:function(){"pie"===this.tabStatus?this.pieValue=[]:this.barValue=[],this.getUrl(this.barType,{})},getUrl:function(e,t){var a="";"year"===e&&(a=this.url.getYearCountInfo),"month"===e&&(a=this.url.getMonthCountInfo),"category"===e&&(a=this.url.getCntrNoCountInfo),"cabinet"===e&&(a=this.url.getCabinetCountInfo),this.loadDate(a,e,t)},handleBarDate:function(e,t){this.barValue=e,this.barDate=["date"===t[0]?"month":t[0],"date"===t[1]?"month":t[1]]},handlePieDate:function(e,t){this.pieValue=e,this.pieDate=["date"===t[0]?"month":t[0],"date"===t[1]?"month":t[1]]}}},d=c,u=(a("4b55"),a("2877")),m=Object(u["a"])(d,r,n,!1,null,"1e0c0d3c",null);t["default"]=m.exports},"2a1c":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:"cron表达式",width:e.modalWidth,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.change,cancel:e.close}},[a("div",{staticClass:"card-container"},[a("a-tabs",{attrs:{type:"card"}},[a("a-tab-pane",{key:"1",attrs:{type:"card"}},[a("span",{attrs:{slot:"tab"},slot:"tab"},[a("a-icon",{attrs:{type:"schedule"}}),e._v(" 秒")],1),a("a-radio-group",{model:{value:e.result.second.cronEvery,callback:function(t){e.$set(e.result.second,"cronEvery",t)},expression:"result.second.cronEvery"}},[a("a-row",[a("a-radio",{attrs:{value:"1"}},[e._v("每一秒钟")])],1),a("a-row",[a("a-radio",{attrs:{value:"2"}},[e._v("每隔\n              "),a("a-input-number",{attrs:{size:"small",min:1,max:60},model:{value:e.result.second.incrementIncrement,callback:function(t){e.$set(e.result.second,"incrementIncrement",t)},expression:"result.second.incrementIncrement"}}),e._v("\n              秒执行 从\n              "),a("a-input-number",{attrs:{size:"small",min:0,max:59},model:{value:e.result.second.incrementStart,callback:function(t){e.$set(e.result.second,"incrementStart",t)},expression:"result.second.incrementStart"}}),e._v("\n              秒开始\n            ")],1)],1),a("a-row",[a("a-radio",{attrs:{value:"3"}},[e._v("具体秒数(可多选)")]),a("a-select",{staticStyle:{width:"354px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.second.specificSpecific,callback:function(t){e.$set(e.result.second,"specificSpecific",t)},expression:"result.second.specificSpecific"}},e._l(60,(function(t,r){return a("a-select-option",{key:r,attrs:{value:r}},[e._v(e._s(r))])})),1)],1),a("a-row",[a("a-radio",{attrs:{value:"4"}},[e._v("周期从\n              "),a("a-input-number",{attrs:{size:"small",min:1,max:60},model:{value:e.result.second.rangeStart,callback:function(t){e.$set(e.result.second,"rangeStart",t)},expression:"result.second.rangeStart"}}),e._v("\n              到\n              "),a("a-input-number",{attrs:{size:"small",min:0,max:59},model:{value:e.result.second.rangeEnd,callback:function(t){e.$set(e.result.second,"rangeEnd",t)},expression:"result.second.rangeEnd"}}),e._v("\n              秒\n            ")],1)],1)],1)],1),a("a-tab-pane",{key:"2"},[a("span",{attrs:{slot:"tab"},slot:"tab"},[a("a-icon",{attrs:{type:"schedule"}}),e._v("分")],1),a("div",{staticClass:"tabBody"},[a("a-radio-group",{model:{value:e.result.minute.cronEvery,callback:function(t){e.$set(e.result.minute,"cronEvery",t)},expression:"result.minute.cronEvery"}},[a("a-row",[a("a-radio",{attrs:{value:"1"}},[e._v("每一分钟")])],1),a("a-row",[a("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:60},model:{value:e.result.minute.incrementIncrement,callback:function(t){e.$set(e.result.minute,"incrementIncrement",t)},expression:"result.minute.incrementIncrement"}}),e._v("\n                分执行 从\n                "),a("a-input-number",{attrs:{size:"small",min:0,max:59},model:{value:e.result.minute.incrementStart,callback:function(t){e.$set(e.result.minute,"incrementStart",t)},expression:"result.minute.incrementStart"}}),e._v("\n                分开始\n              ")],1)],1),a("a-row",[a("a-radio",{attrs:{value:"3"}},[e._v("具体分钟数(可多选)")]),a("a-select",{staticStyle:{width:"340px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.minute.specificSpecific,callback:function(t){e.$set(e.result.minute,"specificSpecific",t)},expression:"result.minute.specificSpecific"}},e._l(Array(60),(function(t,r){return a("a-select-option",{key:r,attrs:{value:r}},[e._v(" "+e._s(r))])})),1)],1),a("a-row",[a("a-radio",{attrs:{value:"4"}},[e._v("周期从\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:60},model:{value:e.result.minute.rangeStart,callback:function(t){e.$set(e.result.minute,"rangeStart",t)},expression:"result.minute.rangeStart"}}),e._v("\n                到\n                "),a("a-input-number",{attrs:{size:"small",min:0,max:59},model:{value:e.result.minute.rangeEnd,callback:function(t){e.$set(e.result.minute,"rangeEnd",t)},expression:"result.minute.rangeEnd"}}),e._v("\n                分\n              ")],1)],1)],1)],1)]),a("a-tab-pane",{key:"3"},[a("span",{attrs:{slot:"tab"},slot:"tab"},[a("a-icon",{attrs:{type:"schedule"}}),e._v(" 时")],1),a("div",{staticClass:"tabBody"},[a("a-radio-group",{model:{value:e.result.hour.cronEvery,callback:function(t){e.$set(e.result.hour,"cronEvery",t)},expression:"result.hour.cronEvery"}},[a("a-row",[a("a-radio",{attrs:{value:"1"}},[e._v("每一小时")])],1),a("a-row",[a("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),a("a-input-number",{attrs:{size:"small",min:0,max:23},model:{value:e.result.hour.incrementIncrement,callback:function(t){e.$set(e.result.hour,"incrementIncrement",t)},expression:"result.hour.incrementIncrement"}}),e._v("\n                小时执行 从\n                "),a("a-input-number",{attrs:{size:"small",min:0,max:23},model:{value:e.result.hour.incrementStart,callback:function(t){e.$set(e.result.hour,"incrementStart",t)},expression:"result.hour.incrementStart"}}),e._v("\n                小时开始\n              ")],1)],1),a("a-row",[a("a-radio",{staticClass:"long",attrs:{value:"3"}},[e._v("具体小时数(可多选)")]),a("a-select",{staticStyle:{width:"340px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.hour.specificSpecific,callback:function(t){e.$set(e.result.hour,"specificSpecific",t)},expression:"result.hour.specificSpecific"}},e._l(Array(24),(function(t,r){return a("a-select-option",{key:r},[e._v(e._s(r))])})),1)],1),a("a-row",[a("a-radio",{attrs:{value:"4"}},[e._v("周期从\n                "),a("a-input-number",{attrs:{size:"small",min:0,max:23},model:{value:e.result.hour.rangeStart,callback:function(t){e.$set(e.result.hour,"rangeStart",t)},expression:"result.hour.rangeStart"}}),e._v("\n                到\n                "),a("a-input-number",{attrs:{size:"small",min:0,max:23},model:{value:e.result.hour.rangeEnd,callback:function(t){e.$set(e.result.hour,"rangeEnd",t)},expression:"result.hour.rangeEnd"}}),e._v("\n                小时\n              ")],1)],1)],1)],1)]),a("a-tab-pane",{key:"4"},[a("span",{attrs:{slot:"tab"},slot:"tab"},[a("a-icon",{attrs:{type:"schedule"}}),e._v("  天")],1),a("div",{staticClass:"tabBody"},[a("a-radio-group",{model:{value:e.result.day.cronEvery,callback:function(t){e.$set(e.result.day,"cronEvery",t)},expression:"result.day.cronEvery"}},[a("a-row",[a("a-radio",{attrs:{value:"1"}},[e._v("每一天")])],1),a("a-row",[a("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:7},model:{value:e.result.week.incrementIncrement,callback:function(t){e.$set(e.result.week,"incrementIncrement",t)},expression:"result.week.incrementIncrement"}}),e._v("\n                周执行 从\n                "),a("a-select",{attrs:{size:"small"},model:{value:e.result.week.incrementStart,callback:function(t){e.$set(e.result.week,"incrementStart",t)},expression:"result.week.incrementStart"}},e._l(Array(7),(function(t,r){return a("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(e.weekDays[r]))])})),1),e._v("\n                开始\n              ")],1)],1),a("a-row",[a("a-radio",{attrs:{value:"3"}},[e._v("每隔\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:31},model:{value:e.result.day.incrementIncrement,callback:function(t){e.$set(e.result.day,"incrementIncrement",t)},expression:"result.day.incrementIncrement"}}),e._v("\n                天执行 从\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:31},model:{value:e.result.day.incrementStart,callback:function(t){e.$set(e.result.day,"incrementStart",t)},expression:"result.day.incrementStart"}}),e._v("\n                天开始\n              ")],1)],1),a("a-row",[a("a-radio",{staticClass:"long",attrs:{value:"4"}},[e._v("具体星期几(可多选)")]),a("a-select",{staticStyle:{width:"340px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.week.specificSpecific,callback:function(t){e.$set(e.result.week,"specificSpecific",t)},expression:"result.week.specificSpecific"}},e._l(Array(7),(function(t,r){return a("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(e.weekDays[r]))])})),1)],1),a("a-row",[a("a-radio",{staticClass:"long",attrs:{value:"5"}},[e._v("具体天数(可多选)")]),a("a-select",{staticStyle:{width:"354px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.day.specificSpecific,callback:function(t){e.$set(e.result.day,"specificSpecific",t)},expression:"result.day.specificSpecific"}},e._l(Array(31),(function(t,r){return a("a-select-option",{key:r,attrs:{value:r}},[e._v(e._s(r+1))])})),1)],1),a("a-row",[a("a-radio",{attrs:{value:"6"}},[e._v("在这个月的最后一天")])],1),a("a-row",[a("a-radio",{attrs:{value:"7"}},[e._v("在这个月的最后一个工作日")])],1),a("a-row",[a("a-radio",{attrs:{value:"8"}},[e._v("在这个月的最后一个\n                "),a("a-select",{attrs:{size:"small"},model:{value:e.result.day.cronLastSpecificDomDay,callback:function(t){e.$set(e.result.day,"cronLastSpecificDomDay",t)},expression:"result.day.cronLastSpecificDomDay"}},e._l(Array(7),(function(t,r){return a("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(e.weekDays[r]))])})),1)],1)],1),a("a-row",[a("a-radio",{attrs:{value:"9"}},[a("a-input-number",{attrs:{size:"small",min:1,max:31},model:{value:e.result.day.cronDaysBeforeEomMinus,callback:function(t){e.$set(e.result.day,"cronDaysBeforeEomMinus",t)},expression:"result.day.cronDaysBeforeEomMinus"}}),e._v("\n                在本月底前\n              ")],1)],1),a("a-row",[a("a-radio",{attrs:{value:"10"}},[e._v("最近的工作日（周一至周五）至本月\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:31},model:{value:e.result.day.cronDaysNearestWeekday,callback:function(t){e.$set(e.result.day,"cronDaysNearestWeekday",t)},expression:"result.day.cronDaysNearestWeekday"}}),e._v("\n                日\n              ")],1)],1),a("a-row",[a("a-radio",{attrs:{value:"11"}},[e._v("在这个月的第\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:5},model:{value:e.result.week.cronNthDayNth,callback:function(t){e.$set(e.result.week,"cronNthDayNth",t)},expression:"result.week.cronNthDayNth"}}),e._v("\n                个\n                "),a("a-select",{attrs:{size:"small"},model:{value:e.result.week.cronNthDayDay,callback:function(t){e.$set(e.result.week,"cronNthDayDay",t)},expression:"result.week.cronNthDayDay"}},e._l(Array(7),(function(t,r){return a("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(e.weekDays[r]))])})),1)],1)],1)],1)],1)]),a("a-tab-pane",{key:"5"},[a("span",{attrs:{slot:"tab"},slot:"tab"},[a("a-icon",{attrs:{type:"schedule"}}),e._v(" 月")],1),a("div",{staticClass:"tabBody"},[a("a-radio-group",{model:{value:e.result.month.cronEvery,callback:function(t){e.$set(e.result.month,"cronEvery",t)},expression:"result.month.cronEvery"}},[a("a-row",[a("a-radio",{attrs:{value:"1"}},[e._v("每一月")])],1),a("a-row",[a("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),a("a-input-number",{attrs:{size:"small",min:0,max:12},model:{value:e.result.month.incrementIncrement,callback:function(t){e.$set(e.result.month,"incrementIncrement",t)},expression:"result.month.incrementIncrement"}}),e._v("\n                月执行 从\n                "),a("a-input-number",{attrs:{size:"small",min:0,max:12},model:{value:e.result.month.incrementStart,callback:function(t){e.$set(e.result.month,"incrementStart",t)},expression:"result.month.incrementStart"}}),e._v("\n                月开始\n              ")],1)],1),a("a-row",[a("a-radio",{staticClass:"long",attrs:{value:"3"}},[e._v("具体月数(可多选)")]),a("a-select",{staticStyle:{width:"354px"},attrs:{size:"small",filterable:"",mode:"multiple"},model:{value:e.result.month.specificSpecific,callback:function(t){e.$set(e.result.month,"specificSpecific",t)},expression:"result.month.specificSpecific"}},e._l(Array(12),(function(t,r){return a("a-select-option",{key:r,attrs:{value:r}},[e._v(e._s(r+1))])})),1)],1),a("a-row",[a("a-radio",{attrs:{value:"4"}},[e._v("从\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:12},model:{value:e.result.month.rangeStart,callback:function(t){e.$set(e.result.month,"rangeStart",t)},expression:"result.month.rangeStart"}}),e._v("\n                到\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:12},model:{value:e.result.month.rangeEnd,callback:function(t){e.$set(e.result.month,"rangeEnd",t)},expression:"result.month.rangeEnd"}}),e._v("\n                月之间的每个月\n              ")],1)],1)],1)],1)]),a("a-tab-pane",{key:"6"},[a("span",{attrs:{slot:"tab"},slot:"tab"},[a("a-icon",{attrs:{type:"schedule"}}),e._v(" 年")],1),a("div",{staticClass:"tabBody"},[a("a-radio-group",{model:{value:e.result.year.cronEvery,callback:function(t){e.$set(e.result.year,"cronEvery",t)},expression:"result.year.cronEvery"}},[a("a-row",[a("a-radio",{attrs:{value:"1"}},[e._v("每一年")])],1),a("a-row",[a("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),a("a-input-number",{attrs:{size:"small",min:1,max:99},model:{value:e.result.year.incrementIncrement,callback:function(t){e.$set(e.result.year,"incrementIncrement",t)},expression:"result.year.incrementIncrement"}}),e._v("\n                年执行 从\n                "),a("a-input-number",{attrs:{size:"small",min:2019,max:2119},model:{value:e.result.year.incrementStart,callback:function(t){e.$set(e.result.year,"incrementStart",t)},expression:"result.year.incrementStart"}}),e._v("\n                年开始\n              ")],1)],1),a("a-row",[a("a-radio",{staticClass:"long",attrs:{value:"3"}},[e._v("具体年份(可多选)")]),a("a-select",{staticStyle:{width:"354px"},attrs:{size:"small",filterable:"",mode:"multiple"},model:{value:e.result.year.specificSpecific,callback:function(t){e.$set(e.result.year,"specificSpecific",t)},expression:"result.year.specificSpecific"}},e._l(Array(100),(function(t,r){return a("a-select-option",{key:r,attrs:{value:2019+r}},[e._v(e._s(2019+r))])})),1)],1),a("a-row",[a("a-radio",{attrs:{value:"4"}},[e._v("从\n                "),a("a-input-number",{attrs:{size:"small",min:2019,max:2119},model:{value:e.result.year.rangeStart,callback:function(t){e.$set(e.result.year,"rangeStart",t)},expression:"result.year.rangeStart"}}),e._v("\n                到\n                "),a("a-input-number",{attrs:{size:"small",min:2019,max:2119},model:{value:e.result.year.rangeEnd,callback:function(t){e.$set(e.result.year,"rangeEnd",t)},expression:"result.year.rangeEnd"}}),e._v("\n                年之间的每一年\n              ")],1)],1)],1)],1)])],1),a("div",{staticClass:"bottom"},[a("span",{staticClass:"value"},[e._v(e._s(this.cron.label))])])],1)])},n=[];a("88bc");function o(e){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}var l={name:"VueCron",props:["data","i18n"],data:function(){return{visible:!1,confirmLoading:!1,size:"large",weekDays:["天","一","二","三","四","五","六"].map((function(e){return"星期"+e})),result:{second:{cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:0,specificSpecific:[]},minute:{cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:"0",specificSpecific:[]},hour:{cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:"0",rangeEnd:"0",specificSpecific:[]},day:{cronEvery:"",incrementStart:1,incrementIncrement:"1",rangeStart:"",rangeEnd:"",specificSpecific:[],cronLastSpecificDomDay:1,cronDaysBeforeEomMinus:"",cronDaysNearestWeekday:""},week:{cronEvery:"",incrementStart:1,incrementIncrement:"1",specificSpecific:[],cronNthDayDay:1,cronNthDayNth:"1"},month:{cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:1,specificSpecific:[]},year:{cronEvery:"",incrementStart:2017,incrementIncrement:1,rangeStart:2019,rangeEnd:2019,specificSpecific:[]},label:""},output:{second:{cronEvery:"",incrementStart:"",incrementIncrement:"",rangeStart:"",rangeEnd:"",specificSpecific:[]},minute:{cronEvery:"",incrementStart:"",incrementIncrement:"",rangeStart:"",rangeEnd:"",specificSpecific:[]},hour:{cronEvery:"",incrementStart:"",incrementIncrement:"",rangeStart:"",rangeEnd:"",specificSpecific:[]},day:{cronEvery:"",incrementStart:"",incrementIncrement:"",rangeStart:"",rangeEnd:"",specificSpecific:[],cronLastSpecificDomDay:"",cronDaysBeforeEomMinus:"",cronDaysNearestWeekday:""},week:{cronEvery:"",incrementStart:"",incrementIncrement:"",specificSpecific:[],cronNthDayDay:"",cronNthDayNth:""},month:{cronEvery:"",incrementStart:"",incrementIncrement:"",rangeStart:"",rangeEnd:"",specificSpecific:[]},year:{cronEvery:"",incrementStart:"",incrementIncrement:"",rangeStart:"",rangeEnd:"",specificSpecific:[]}}}},computed:{modalWidth:function(){return 608},text:function(){return Language["cn"]},secondsText:function(){var e="",t=this.result.second.cronEvery;switch(t.toString()){case"1":e="*";break;case"2":e=this.result.second.incrementStart+"/"+this.result.second.incrementIncrement;break;case"3":this.result.second.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.second.rangeStart+"-"+this.result.second.rangeEnd;break}return e},minutesText:function(){var e="",t=this.result.minute.cronEvery;switch(t.toString()){case"1":e="*";break;case"2":e=this.result.minute.incrementStart+"/"+this.result.minute.incrementIncrement;break;case"3":this.result.minute.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.minute.rangeStart+"-"+this.result.minute.rangeEnd;break}return e},hoursText:function(){var e="",t=this.result.hour.cronEvery;switch(t.toString()){case"1":e="*";break;case"2":e=this.result.hour.incrementStart+"/"+this.result.hour.incrementIncrement;break;case"3":this.result.hour.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.hour.rangeStart+"-"+this.result.hour.rangeEnd;break}return e},daysText:function(){var e="",t=this.result.day.cronEvery;switch(t.toString()){case"1":break;case"2":case"4":case"11":e="?";break;case"3":e=this.result.day.incrementStart+"/"+this.result.day.incrementIncrement;break;case"5":this.result.day.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"6":e="L";break;case"7":e="LW";break;case"8":e=this.result.day.cronLastSpecificDomDay+"L";break;case"9":e="L-"+this.result.day.cronDaysBeforeEomMinus;break;case"10":e=this.result.day.cronDaysNearestWeekday+"W";break}return e},weeksText:function(){var e="",t=this.result.day.cronEvery;switch(t.toString()){case"1":case"3":case"5":e="?";break;case"2":e=this.result.week.incrementStart+"/"+this.result.week.incrementIncrement;break;case"4":this.result.week.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"6":case"7":case"8":case"9":case"10":e="?";break;case"11":e=this.result.week.cronNthDayDay+"#"+this.result.week.cronNthDayNth;break}return e},monthsText:function(){var e="",t=this.result.month.cronEvery;switch(t.toString()){case"1":e="*";break;case"2":e=this.result.month.incrementStart+"/"+this.result.month.incrementIncrement;break;case"3":this.result.month.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.month.rangeStart+"-"+this.result.month.rangeEnd;break}return e},yearsText:function(){var e="",t=this.result.year.cronEvery;switch(t.toString()){case"1":e="*";break;case"2":e=this.result.year.incrementStart+"/"+this.result.year.incrementIncrement;break;case"3":this.result.year.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.year.rangeStart+"-"+this.result.year.rangeEnd;break}return e},cron:function(){return{value:this.result,label:"".concat(this.secondsText||"*"," ").concat(this.minutesText||"*"," ").concat(this.hoursText||"*"," ").concat(this.daysText||"*"," ").concat(this.monthsText||"*"," ").concat(this.weeksText||"*"," ").concat(this.yearsText||"*")}}},watch:{data:function(){}},methods:{show:function(){Object.assign(this.data.value,this.result),this.visible=!0},getValue:function(){return this.cron},change:function(){this.$emit("change",this.cron),this.close(),this.visible=!1},close:function(){this.visible=!1},rest:function(e){for(var t in e)if(e[t]instanceof Object)this.rest(e[t]);else switch(o(e[t])){case"object":e[t]=[];break;case"string":e[t]="";break;case"number":e[t]=null;break}},callback:function(e){}}},s=l,i=(a("8b3e1"),a("d25c"),a("2877")),c=Object(i["a"])(s,r,n,!1,null,"2974cefe",null);t["default"]=c.exports},"2f9b":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._v("\n  hello world!\n")])},n=[],o={data:function(){return{description:"表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。",value:1,form:this.$form.createForm(this)}},methods:{handleSubmit:function(e){e.preventDefault(),this.form.validateFields((function(e,t){}))}}},l=o,s=a("2877"),i=Object(s["a"])(l,r,n,!1,null,null,null);t["default"]=i.exports},3335:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:1200,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-card",{staticClass:"card",attrs:{bordered:!1}},[a("a-row",{staticClass:"form-row",attrs:{gutter:16}},[a("a-col",{attrs:{lg:8}},[a("a-form-item",{attrs:{label:"任务名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.name",{rules:[{required:!0,message:"请输入任务名称",whitespace:!0}]}],expression:"[ 'task.name', {rules: [{ required: true, message: '请输入任务名称', whitespace: true}]} ]"}],attrs:{placeholder:"请输入任务名称"}})],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-item",{attrs:{label:"任务描述"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.description",{rules:[{required:!0,message:"请输入任务描述",whitespace:!0}]}],expression:"['task.description', {rules: [{ required: true, message: '请输入任务描述', whitespace: true}]} ]"}],attrs:{placeholder:"请输入任务描述"}})],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-item",{attrs:{label:"执行人"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.executor",{rules:[{required:!0,message:"请选择执行人"}]}],expression:"['task.executor',{rules: [{ required: true, message: '请选择执行人'}]}  ]"}],attrs:{placeholder:"请选择执行人"}},[a("a-select-option",{attrs:{value:"黄丽丽"}},[e._v("黄丽丽")]),a("a-select-option",{attrs:{value:"李大刀"}},[e._v("李大刀")])],1)],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:16}},[a("a-col",{attrs:{lg:8}},[a("a-form-item",{attrs:{label:"责任人"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.manager",{rules:[{required:!0,message:"请选择责任人"}]}],expression:"['task.manager',  {rules: [{ required: true, message: '请选择责任人'}]} ]"}],attrs:{placeholder:"请选择责任人"}},[a("a-select-option",{attrs:{value:"王伟"}},[e._v("王伟")]),a("a-select-option",{attrs:{value:"李红军"}},[e._v("李红军")])],1)],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-item",{attrs:{label:"提醒时间"}},[a("a-time-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.time",{rules:[{required:!0,message:"请选择提醒时间"}]}],expression:"['task.time', {rules: [{ required: true, message: '请选择提醒时间'}]} ]"}],staticStyle:{width:"100%"}})],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-item",{attrs:{label:"任务类型"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.type",{rules:[{required:!0,message:"请选择任务类型"}]}],expression:"['task.type', {rules: [{ required: true, message: '请选择任务类型'}]} ]"}],attrs:{placeholder:"请选择任务类型"}},[a("a-select-option",{attrs:{value:"定时执行"}},[e._v("定时执行")]),a("a-select-option",{attrs:{value:"周期执行"}},[e._v("周期执行")])],1)],1)],1)],1)],1),a("a-tabs",{attrs:{defaultActiveKey:"1"}},[a("a-tab-pane",{key:"1",attrs:{tab:"Tab 1"}},[a("a-table",{attrs:{columns:e.columns,dataSource:e.data,pagination:!1,size:"middle"},scopedSlots:e._u([e._l(["name","workId","department"],(function(t,r){return{key:t,fn:function(n,o,l){return[a("a-tooltip",{attrs:{title:"必填项",defaultVisible:!1,overlayStyle:{color:"red"}}},[o.editable?a("a-input",{key:t,staticStyle:{margin:"-5px 0"},attrs:{value:n,placeholder:e.columns[r].title},on:{change:function(a){return e.handlerRowChange(a.target.value,o.key,t)}}}):[e._v(e._s(n))]],2)]}}})),{key:"operation",fn:function(t,r,n){return[r.editable?[r.isNew?a("span",[a("a",{on:{click:function(t){return e.saveRow(r.key)}}},[e._v("添加")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-popconfirm",{attrs:{title:"是否要删除此行？"},on:{confirm:function(t){return e.removeRow(r.key)}}},[a("a",[e._v("删除")])])],1):a("span",[a("a",{on:{click:function(t){return e.saveRow(r.key)}}},[e._v("保存")]),a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(t){return e.cancelEditRow(r.key)}}},[e._v("取消")])],1)]:a("span",[a("a",{on:{click:function(t){return e.editRow(r.key)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-popconfirm",{attrs:{title:"是否要删除此行？"},on:{confirm:function(t){return e.removeRow(r.key)}}},[a("a",[e._v("删除")])])],1)]}}],null,!0)}),a("a-button",{staticStyle:{width:"100%","margin-top":"16px","margin-bottom":"8px"},attrs:{type:"dashed",icon:"plus"},on:{click:e.newRow}},[e._v("新增成员")])],1),a("a-tab-pane",{key:"2",attrs:{tab:"Tab 2",forceRender:""}},[e._v("\n          Content of Tab Pane 2\n        ")]),a("a-tab-pane",{key:"3",attrs:{tab:"Tab 3"}},[e._v("Content of Tab Pane 3")])],1)],1)],1)],1)},n=[],o=a("0fea"),l=a("88bc"),s=a.n(l),i=a("c1df"),c=a.n(i);function d(e){return f(e)||p(e)||m(e)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return h(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(e,t):void 0}}function p(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function f(e){if(Array.isArray(e))return h(e)}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}var b={name:"JeecgDemoTabsModal",data:function(){return{title:"操作",visible:!1,model:{},columns:[{title:"成员姓名",dataIndex:"name",key:"name",width:"20%",scopedSlots:{customRender:"name"}},{title:"工号",dataIndex:"workId",key:"workId",width:"20%",scopedSlots:{customRender:"workId"}},{title:"所属部门",dataIndex:"department",key:"department",width:"40%",scopedSlots:{customRender:"department"}},{title:"操作",key:"action",scopedSlots:{customRender:"operation"}}],data:[{key:"1",name:"小明",workId:"001",editable:!1,department:"行政部"},{key:"2",name:"李莉",workId:"002",editable:!1,department:"IT部"},{key:"3",name:"王小帅",workId:"003",editable:!1,department:"财务部"}],confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{},url:{add:"/test/jeecgDemo/add",edit:"/test/jeecgDemo/edit"}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){t.form.setFieldsValue(s()(t.model,"name","keyWord","sex","age","email","content")),t.form.setFieldsValue({punchTime:t.model.punchTime?c()(t.model.punchTime,"YYYY-MM-DD HH:mm:ss"):null}),t.form.setFieldsValue({birthday:t.model.birthday?c()(t.model.birthday):null})}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.form.validateFields((function(a,r){if(!a){t.confirmLoading=!0;var n="",l="";e.model.id?(n+=e.url.edit,l="put"):(n+=e.url.add,l="post");var s=Object.assign(e.model,r);s.punchTime=s.punchTime?s.punchTime.format("YYYY-MM-DD HH:mm:ss"):null,s.birthday=s.birthday?s.birthday.format():null,Object(o["h"])(n,s,l).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}}))},handleCancel:function(){this.close()},newRow:function(){var e=Math.round((new Date).getTime()).toString();this.data.push({key:e,name:"",workId:"",department:"",editable:!0,isNew:!0})},removeRow:function(e){var t=this.data.filter((function(t){return t.key!==e}));this.data=t},saveRow:function(e){var t=this.data.filter((function(t){return t.key===e}))[0];t.editable=!1,t.isNew=!1},handlerRowChange:function(e,t,a){var r=d(this.data),n=r.filter((function(e){return t===e.key}))[0];n&&(n[a]=e,this.data=r)},editRow:function(e){var t=this.data.filter((function(t){return t.key===e}))[0];t.editable=!t.editable},cancelEditRow:function(e){var t=this.data.filter((function(t){return t.key===e}))[0];t.editable=!1}}},v=b,g=(a("be66"),a("2877")),y=Object(g["a"])(v,r,n,!1,null,"28883139",null);t["default"]=y.exports},"33b1":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.rules},on:{submit:e.handleSubmit}},[a("a-row",[a("a-col",{attrs:{md:24,sm:24}},[a("a-form-model-item",{attrs:{label:"Note",prop:"note",labelCol:{span:7},wrapperCol:{span:15}}},[a("a-input",{model:{value:e.model.note,callback:function(t){e.$set(e.model,"note",t)},expression:"model.note"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{md:24,sm:24}},[a("a-form-model-item",{attrs:{label:"Gender",prop:"gender",labelCol:{span:7},wrapperCol:{span:15}}},[a("a-select",{attrs:{placeholder:"Select a option and change input text above"},on:{change:e.handleSelectChange},model:{value:e.model.gender,callback:function(t){e.$set(e.model,"gender",t)},expression:"model.gender"}},[a("a-select-option",{attrs:{value:"male"}},[e._v("male")]),a("a-select-option",{attrs:{value:"female"}},[e._v("female")])],1)],1)],1)],1),a("a-row",[a("a-col",{attrs:{md:24,sm:24}},[a("a-form-model-item",{attrs:{label:"Gender",prop:"cascader",labelCol:{span:7},wrapperCol:{span:15}}},[a("a-cascader",{attrs:{options:e.areaOptions,showSearch:{filter:e.filter},placeholder:"Please select"},on:{change:e.onChange}})],1)],1)],1),a("a-form-model-item",{attrs:{wrapperCol:{span:12,offset:5}}},[a("a-col",{attrs:{md:24,sm:24}},[a("a-form-model-item",{attrs:{wrapperCol:{span:12,offset:5}}},[a("a-button",{attrs:{type:"primary",htmlType:"submit"}},[e._v("Submit")])],1)],1)],1)],1)],1)},n=[],o=a("0fea"),l={props:["sex","name"],data:function(){return{formLayout:"horizontal",model:{},rules:{note:[{required:!0,message:"Please input your note!"}],gender:[{required:!0,message:"Please select your gender!"}]},areaOptions:[]}},methods:{handleSubmit:function(e){var t=this;e.preventDefault(),this.$refs.form.validate((function(e,a){e&&t.$message.success("succeed!")}))},handleSelectChange:function(e){this.model.note="Hi, ".concat("male"===e?"man":"lady","!")},onChange:function(e,t){},filter:function(e,t){return t.some((function(t){return t.label.toLowerCase().indexOf(e.toLowerCase())>-1}))}},created:function(){var e=this;Object(o["c"])("/mock/api/area").then((function(t){e.areaOptions=t}))},watch:{$route:{immediate:!0,handler:function(){this.$route.query.sex}}}},s=l,i=a("2877"),c=Object(i["a"])(s,r,n,!1,null,null,null);t["default"]=c.exports},"33df":function(e,t,a){},"39a5":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,okButtonProps:{props:{disabled:e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[e.editStatus?a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"客户姓名",prop:"name",required:"",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入客户姓名",readOnly:e.disableSubmit},model:{value:e.model.name,callback:function(t){e.$set(e.model,"name",t)},expression:"model.name"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"性别",hasFeedback:""}},[a("a-select",{attrs:{placeholder:"请选择性别"},model:{value:e.model.sex,callback:function(t){e.$set(e.model,"sex",t)},expression:"model.sex"}},[a("a-select-option",{attrs:{value:"1"}},[e._v("男性")]),a("a-select-option",{attrs:{value:"2"}},[e._v("女性")])],1)],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"身份证号码",prop:"idcard",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入身份证号码",readOnly:e.disableSubmit},model:{value:e.model.idcard,callback:function(t){e.$set(e.model,"idcard",t)},expression:"model.idcard"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"身份证扫描件",hasFeedback:""}},[a("j-image-upload",{attrs:{text:"上传",isMultiple:!0},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"联系方式",prop:"telphone",hasFeedback:""}},[a("a-input",{attrs:{readOnly:e.disableSubmit},model:{value:e.model.telphone,callback:function(t){e.$set(e.model,"telphone",t)},expression:"model.telphone"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"订单号码",hidden:e.hiding,hasFeedback:""}},[a("a-input",{attrs:{disabled:"disabled"},model:{value:e.model.orderId,callback:function(t){e.$set(e.model,"orderId",t)},expression:"model.orderId"}})],1)],1)],1):e._e()],1)},n=[],o=a("0fea"),l=a("2b0e"),s=a("9fb0"),i=a("e610"),c={name:"JeecgOrderCustomerModal",components:{JImageUpload:i["default"]},data:function(){return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},fileList:[],disableSubmit:!1,selectedRowKeys:[],orderId:"",hiding:!1,headers:{},picUrl:"",picArray:[],previewVisible:!1,previewImage:"",addStatus:!1,editStatus:!1,confirmLoading:!1,url:{add:"/test/order/addCustomer",edit:"/test/order/editCustomer",fileUpload:window._CONFIG["domianURL"]+"/sys/common/upload",getOrderCustomerList:"/test/order/listOrderCustomerByMainId"},validatorRules:{name:[{required:!0,message:"请输入客户姓名!"}],telphone:[{validator:this.validateMobile}],idcard:[{validator:this.validateIdCard}]}}},computed:{uploadAction:function(){return this.url.fileUpload}},created:function(){var e=l["default"].ls.get(s["a"]);this.headers={"X-Access-Token":e}},methods:{add:function(e){this.hiding=!0,e?this.edit({orderId:e},""):this.$message.warning("请选择一个客户信息")},detail:function(e){this.edit(e,"d")},edit:function(e,t){var a=this;"e"==t?(this.hiding=!1,this.disableSubmit=!1):"d"==t?(this.hiding=!1,this.disableSubmit=!0):(this.hiding=!0,this.disableSubmit=!1),this.model=Object.assign({},e),e.id?(this.hiding=!1,this.addStatus=!1,this.editStatus=!0,setTimeout((function(){a.fileList=e.idcardPic}),5)):(this.addStatus=!1,this.editStatus=!0),this.visible=!0},close:function(){this.$emit("close"),this.visible=!1,this.picUrl="",this.fileList=[],this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0;var r="",n="";e.model.id?(r+=e.url.edit,n="put"):(r+=e.url.add,n="post");var l=Object.assign({},e.model);""!=e.fileList?l.idcardPic=e.fileList:l.idcardPic="",Object(o["h"])(r,l,n).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()},validateMobile:function(e,t,a){!t||new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/).test(t)?a():a("您的手机号码格式不正确!")},validateIdCard:function(e,t,a){!t||new RegExp(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/).test(t)?a():a("您的身份证号码格式不正确!")}}},d=c,u=(a("02d5"),a("2877")),m=Object(u["a"])(d,r,n,!1,null,"e86d1f2e",null);t["default"]=m.exports},"3d75":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-skeleton",{attrs:{active:"",loading:e.loading,paragraph:{rows:17}}},[a("a-card",{staticClass:"card-area",attrs:{bordered:!1}},[a("a-alert",{attrs:{type:"info",showIcon:!0}},[a("div",{attrs:{slot:"message"},slot:"message"},[e._v("\n        共追踪到 "+e._s(e.dataSource.length)+" 条近期HTTP请求记录\n        "),a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:e.handleClickUpdate}},[e._v("立即刷新")])],1)]),a("a-table",{staticStyle:{"margin-top":"20px"},attrs:{columns:e.columns,dataSource:e.dataSource,pagination:e.pagination,loading:e.tableLoading,scroll:{x:900}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"timeTaken",fn:function(t){return[a("a-tag",t<500?{attrs:{color:"green"}}:t<1e3?{attrs:{color:"cyan"}}:t<1500?{attrs:{color:"orange"}}:{attrs:{color:"red"}},[e._v(e._s(t)+" ms")])]}},{key:"responseStatus",fn:function(t){return[t<200?a("a-tag",{attrs:{color:"pink"}},[e._v(e._s(t)+" ")]):t<201?a("a-tag",{attrs:{color:"green"}},[e._v(e._s(t)+" ")]):t<399?a("a-tag",{attrs:{color:"cyan"}},[e._v(e._s(t)+" ")]):t<403?a("a-tag",{attrs:{color:"orange"}},[e._v(e._s(t)+" ")]):t<501?a("a-tag",{attrs:{color:"red"}},[e._v(e._s(t)+" ")]):a("span",[e._v(e._s(t))])]}},{key:"requestMethod",fn:function(t){return["GET"===t?a("a-tag",{attrs:{color:"#87d068"}},[e._v(e._s(t))]):"POST"===t?a("a-tag",{attrs:{color:"#2db7f5"}},[e._v(e._s(t))]):"PUT"===t?a("a-tag",{attrs:{color:"#ffba5a"}},[e._v(e._s(t))]):"DELETE"===t?a("a-tag",{attrs:{color:"#f50"}},[e._v(e._s(t))]):a("span",[e._v(e._s(t)+" ms")])]}}])})],1)],1)},n=[],o=a("c1df"),l=a.n(o),s=a("0fea");function i(e,t){var a;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(a=c(e))||t&&e&&"number"===typeof e.length){a&&(e=a);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,s=!1;return{s:function(){a=e[Symbol.iterator]()},n:function(){var e=a.next();return l=e.done,e},e:function(e){s=!0,o=e},f:function(){try{l||null==a.return||a.return()}finally{if(s)throw o}}}}function c(e,t){if(e){if("string"===typeof e)return d(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}l.a.locale("zh-cn");var u={data:function(){return{advanced:!1,dataSource:[],pagination:{defaultPageSize:10,defaultCurrent:1,pageSizeOptions:["10","20","30","40","100"],showQuickJumper:!0,showSizeChanger:!0,showTotal:function(e,t){return"显示 ".concat(t[0]," ~ ").concat(t[1]," 条记录，共 ").concat(e," 条记录")}},loading:!0,tableLoading:!0}},computed:{columns:function(){return[{title:"请求时间",dataIndex:"timestamp",customRender:function(e){return l()(e).format("YYYY-MM-DD HH:mm:ss")}},{title:"请求方法",dataIndex:"request.method",scopedSlots:{customRender:"requestMethod"},filters:[{text:"GET",value:"GET"},{text:"POST",value:"POST"},{text:"PUT",value:"PUT"},{text:"DELETE",value:"DELETE"}],filterMultiple:!0,onFilter:function(e,t){return t.request.method.includes(e)}},{title:"请求URL",dataIndex:"request.uri",customRender:function(e){return e.split("?")[0]}},{title:"响应状态",dataIndex:"response.status",scopedSlots:{customRender:"responseStatus"}},{title:"请求耗时",dataIndex:"timeTaken",scopedSlots:{customRender:"timeTaken"}}]}},mounted:function(){this.fetch()},methods:{handleClickUpdate:function(){this.fetch()},handleTableChange:function(){this.fetch()},fetch:function(){var e=this;this.tableLoading=!0,Object(s["c"])("actuator/httptrace").then((function(t){var a,r=[],n=i(t.traces);try{for(n.s();!(a=n.n()).done;){var o=a.value;"OPTIONS"!==o.request.method&&-1===o.request.uri.indexOf("httptrace")&&r.push(o)}}catch(l){n.e(l)}finally{n.f()}e.dataSource=r})).catch((function(t){e.$message.error("获取HTTP信息失败")})).finally((function(){e.loading=!1,e.tableLoading=!1}))}}},m=u,p=a("2877"),f=Object(p["a"])(m,r,n,!1,null,null,null);t["default"]=f.exports},"48f7":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-skeleton",{attrs:{active:"",loading:e.loading,paragraph:{rows:17}}},[a("a-card",{attrs:{bordered:!1}},[a("a-alert",{attrs:{type:"info",showIcon:!0}},[a("div",{attrs:{slot:"message"},slot:"message"},[e._v("\n        上次更新时间："+e._s(this.time)+"\n        "),a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:e.handleClickUpdate}},[e._v("立即更新")])],1)]),a("a-table",{staticStyle:{"margin-top":"20px"},attrs:{rowKey:"id",size:"middle",columns:e.columns,dataSource:e.dataSource,pagination:!1,loading:e.tableLoading},scopedSlots:e._u([{key:"param",fn:function(t,r){return[a("a-tag",{attrs:{color:e.textInfo[r.param].color}},[e._v(e._s(t))])]}},{key:"text",fn:function(t,a){return[e._v("\n        "+e._s(e.textInfo[a.param].text)+"\n      ")]}},{key:"value",fn:function(t,a){return[e._v("\n        "+e._s(t)+" "+e._s(e.textInfo[a.param].unit)+"\n      ")]}}])})],1)],1)},n=[],o=a("c1df"),l=a.n(o),s=a("0fea");l.a.locale("zh-cn");var i={data:function(){return{time:"",loading:!0,tableLoading:!0,columns:[{title:"参数",width:"30%",dataIndex:"param",scopedSlots:{customRender:"param"}},{title:"描述",width:"40%",dataIndex:"text",scopedSlots:{customRender:"text"}},{title:"当前值",width:"30%",dataIndex:"value",scopedSlots:{customRender:"value"}}],dataSource:[],textInfo:{"system.cpu.count":{color:"green",text:"CPU 数量",unit:"核"},"system.cpu.usage":{color:"green",text:"系统 CPU 使用率",unit:"%"},"process.start.time":{color:"purple",text:"应用启动时间点",unit:""},"process.uptime":{color:"purple",text:"应用已运行时间",unit:"秒"},"process.cpu.usage":{color:"purple",text:"当前应用 CPU 使用率",unit:"%"}},moreInfo:{}}},mounted:function(){this.loadTomcatInfo()},methods:{handleClickUpdate:function(){this.loadTomcatInfo()},loadTomcatInfo:function(){var e=this;this.tableLoading=!0,this.time=l()().format("YYYY年MM月DD日 HH时mm分ss秒"),Promise.all([Object(s["c"])("actuator/metrics/system.cpu.count"),Object(s["c"])("actuator/metrics/system.cpu.usage"),Object(s["c"])("actuator/metrics/process.start.time"),Object(s["c"])("actuator/metrics/process.uptime"),Object(s["c"])("actuator/metrics/process.cpu.usage")]).then((function(t){var a=[];t.forEach((function(t,r){var n=e.moreInfo[t.name];n instanceof Array||(n=[""]),n.forEach((function(n,o){var l=t.name+n,s=t.measurements[o].value;"system.cpu.usage"!==l&&"process.cpu.usage"!==l||(s=e.convert(s,Number)),"process.start.time"===l&&(s=e.convert(s,Date)),a.push({id:l+r,param:l,text:"false value",value:s})}))})),e.dataSource=a})).catch((function(t){e.$message.error("获取服务器信息失败")})).finally((function(){e.loading=!1,e.tableLoading=!1}))},convert:function(e,t){return t===Number?Number(100*e).toFixed(2):t===Date?l()(1e3*e).format("YYYY-MM-DD HH:mm:ss"):e}}},c=i,d=a("2877"),u=Object(d["a"])(c,r,n,!1,null,null,null);t["default"]=u.exports},"4b55":function(e,t,a){"use strict";var r=a("b6bba"),n=a.n(r);n.a},5139:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("a-tabs",{attrs:{defaultActiveKey:"1"}},[a("a-tab-pane",{key:"1",attrs:{tab:"柱状图"}},[a("bar",{attrs:{title:"销售额排行",dataSource:e.barData,height:e.height}})],1),a("a-tab-pane",{key:"2",attrs:{tab:"多列柱状图"}},[a("bar-multid",{attrs:{title:"多列柱状图",height:e.height}})],1),a("a-tab-pane",{key:"3",attrs:{tab:"迷你柱状图"}},[a("mini-bar",{attrs:{dataSource:e.barData,width:400,height:200}})],1),a("a-tab-pane",{key:"4",attrs:{tab:"面积图"}},[a("area-chart-ty",{attrs:{title:"销售额排行",dataSource:e.areaData,x:"月份",y:"销售额",height:e.height}})],1),a("a-tab-pane",{key:"5",attrs:{tab:"迷你面积图"}},[a("div",{staticStyle:{"padding-top":"100px",width:"600px",height:"200px"}},[a("mini-area",{attrs:{dataSource:e.areaData,x:"月份",y:"销售额",height:e.height}})],1)]),a("a-tab-pane",{key:"6",attrs:{tab:"多行折线图"}},[a("line-chart-multid",{attrs:{title:"多行折线图",height:e.height}})],1),a("a-tab-pane",{key:"7",attrs:{tab:"饼图"}},[a("pie",{attrs:{title:"饼图",height:e.height}})],1),a("a-tab-pane",{key:"8",attrs:{tab:"雷达图"}},[a("radar",{attrs:{title:"雷达图",height:e.height}})],1),a("a-tab-pane",{key:"9",attrs:{tab:"仪表盘"}},[a("dash-chart-demo",{attrs:{title:"仪表盘",value:9,height:e.height}})],1),a("a-tab-pane",{key:"10",attrs:{tab:"进度条"}},[a("mini-progress",{attrs:{percentage:30,target:40,height:30}}),a("mini-progress",{attrs:{percentage:51,target:60,height:30,color:"#FFA500"}}),a("mini-progress",{attrs:{percentage:66,target:80,height:30,color:"#1E90FF"}}),a("mini-progress",{attrs:{percentage:74,target:70,height:30,color:"#FF4500"}}),a("mini-progress",{attrs:{percentage:92,target:100,height:30,color:"#49CC49"}})],1),a("a-tab-pane",{key:"11",attrs:{tab:"排名列表"}},[a("rank-list",{staticStyle:{width:"600px",margin:"0 auto"},attrs:{title:"门店销售排行榜",list:e.rankList}})],1),a("a-tab-pane",{key:"12",attrs:{tab:"TransferBar"}},[a("transfer-bar",{attrs:{title:"年度消耗流量一览表",data:e.barData,x:"月份",y:"流量(Mb)",height:e.height}})],1),a("a-tab-pane",{key:"13",attrs:{tab:"Trend"}},[a("trend",{attrs:{title:"Trend",term:"Trend：",percentage:30}})],1),a("a-tab-pane",{key:"14",attrs:{tab:"Liquid"}},[a("liquid",{attrs:{height:e.height}})],1),a("a-tab-pane",{key:"15",attrs:{tab:"BarAndLine"}},[a("bar-and-line",{attrs:{height:e.height}})],1)],1)],1)},n=[],o=a("f552"),l=a("edd9"),s=a("8191"),i=a("972f"),c=a("4ec6"),d=a("4ced"),u=a("942d"),m=a("1d43"),p=a("bf13"),f=a("6cb2"),h=a("3981"),b=a("0923"),v=a("1cf5"),g=a("527e"),y=a("e659");function _(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var k={name:"ViserChartDemo",components:{Bar:l["default"],MiniBar:u["default"],BarMultid:s["default"],AreaChartTy:o["default"],LineChartMultid:c["default"],Pie:f["default"],Radar:h["default"],DashChartDemo:i["default"],MiniProgress:p["default"],RankList:b["default"],TransferBar:v["default"],Trend:g["default"],Liquid:d["default"],MiniArea:m["default"],BarAndLine:y["default"]},data:function(){return{height:420,rankList:[],barData:[],areaData:[]}},created:function(){var e=this;setTimeout((function(){e.loadBarData(),e.loadAreaData(),e.loadRankListData()}),100)},methods:{loadData:function(e,t,a,r){for(var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"月",l=[],s=0;s<12;s+=1){var i;l.push((i={},_(i,e,"".concat(n).concat(s+1).concat(o)),_(i,t,Math.floor(Math.random()*a)+r),i))}return l},loadBarData:function(){this.barData=this.loadData("x","y",1e3,200)},loadAreaData:function(){this.areaData=this.loadData("x","y",500,100)},loadRankListData:function(){this.rankList=this.loadData("name","total",2e3,100,"北京朝阳 "," 号店")}}},x=k,w=a("2877"),C=Object(w["a"])(x,r,n,!1,null,"1fd923d1",null);t["default"]=C.exports},"54d3":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{staticStyle:{"min-width":"500px","overflow-x":"auto"}},[a("p",[e._v("我是左侧页面")]),a("img-turn-page")],1)},n=[],o=a("4874"),l={name:"SplitPanelAModal",components:{ImgTurnPage:o["default"]},data:function(){return{}},created:function(){},methods:{}},s=l,i=a("2877"),c=Object(i["a"])(s,r,n,!1,null,"045cdf58",null);t["default"]=c.exports},"570f":function(e,t,a){"use strict";var r=a("c997"),n=a.n(r);n.a},"588f":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"none"}},[a("iframe",{attrs:{id:e.id,src:e.url,frameborder:"0",width:"100%",height:"550px",scrolling:"auto"}})])},n=[],o=a("2b0e"),l=a("9fb0"),s={name:"PdfPreviewModal",data:function(){return{url:window._CONFIG["pdfDomainURL"],id:"pdfPreviewIframe",headers:{}}},created:function(){var e=o["default"].ls.get(l["a"]);this.headers={"X-Access-Token":e}},computed:{},mounted:function(){window.addEventListener("message",this.handleScanFileMessage)},methods:{handleScanFileMessage:function(e){e.data},previewFiles:function(e,t){var a=document.getElementById("pdfPreviewIframe"),r={title:e,token:t};a.contentWindow.postMessage(r,"*")}}},i=s,c=a("2877"),d=Object(c["a"])(i,r,n,!1,null,"5088ac45",null);t["default"]=d.exports},"5c7b":function(e,t,a){},"5cf8":function(e,t,a){"use strict";var r=a("b931"),n=a.n(r);n.a},"5d1e":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{staticStyle:{top:"0px"},attrs:{title:"分屏",width:e.modalWidth,visible:e.visible,bodyStyle:e.bodyStyle,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("split-pane",{attrs:{"min-percent":20,"default-percent":50,split:"vertical"}},[a("template",{slot:"paneL"},[a("split-panel-a")],1),a("template",{slot:"paneR"},[a("split-panel-b")],1)],2)],1)},n=[],o=a("19ab"),l=a.n(o),s=a("54d3"),i=a("9606"),c={name:"SplitPanelModal",components:{splitPane:l.a,SplitPanelA:s["default"],SplitPanelB:i["default"]},data:function(){return{visible:!1,bodyStyle:{padding:"0",height:window.innerHeight-150+"px"},modalWidth:800}},created:function(){this.modalWidth=window.innerWidth-0},methods:{show:function(){this.visible=!0},handleOk:function(){},handleCancel:function(){this.visible=!1}}},d=c,u=a("2877"),m=Object(u["a"])(d,r,n,!1,null,"79db68e4",null);t["default"]=m.exports},"5edf4":function(e,t,a){},"60f0":function(e,t,a){},"617d":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"page-header-index-wide"},[a("a-row",{attrs:{gutter:24}},[a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:e.loading,title:"受理量",total:e._f("NumberFormat")(e.cardCount.sll)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-area",{attrs:{dataSource:e.chartData.sll}})],1),a("template",{slot:"footer"},[e._v("今日受理量："),a("span",[e._v(e._s(e.todaySll))])])],2)],1),a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:e.loading,title:"办结量",total:e._f("NumberFormat")(e.cardCount.bjl)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-area",{attrs:{dataSource:e.chartData.bjl}})],1),a("template",{slot:"footer"},[e._v("今日办结量："),a("span",[e._v(e._s(e.todayBjl))])])],2)],1),a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:e.loading,title:"用户受理量",total:e._f("NumberFormat")(e.cardCount.isll)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-bar",{attrs:{dataSource:e.chartData.isll}})],1),a("template",{slot:"footer"},[e._v("用户今日受理量："),a("span",[e._v(e._s(e.todayISll))])])],2)],1),a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:e.loading,title:"用户办结量",total:e._f("NumberFormat")(e.cardCount.ibjl)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-bar",{attrs:{dataSource:e.chartData.ibjl}})],1),a("template",{slot:"footer"},[e._v("用户今日办结量："),a("span",[e._v(e._s(e.todayIBjl))])])],2)],1)],1),a("a-card",{attrs:{loading:e.loading,bordered:!1,"body-style":{padding:"0"}}},[a("div",{staticClass:"salesCard"},[a("a-tabs",{attrs:{"default-active-key":"1",size:"large","tab-bar-style":{marginBottom:"24px",paddingLeft:"16px"}}},[a("div",{staticClass:"extra-wrapper",attrs:{slot:"tabBarExtraContent"},slot:"tabBarExtraContent"},[a("div",{staticClass:"extra-item"},[a("a",[e._v("今日")]),a("a",[e._v("本周")]),a("a",[e._v("本月")]),a("a",[e._v("本年")])]),a("a-range-picker",{style:{width:"256px"}})],1),a("a-tab-pane",{key:"1",attrs:{loading:"true",tab:"受理监管"}},[a("a-row",[a("a-col",{attrs:{xl:16,lg:12,md:12,sm:24,xs:24}},[a("bar",{attrs:{title:"受理量统计"}})],1),a("a-col",{attrs:{xl:8,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticStyle:{"margin-bottom":"24px"},attrs:{title:"快速开始 / 便捷导航",bordered:!1,"body-style":{padding:0}}},[a("div",{staticClass:"item-group"},[a("a-row",e._l(e.registerTypeList,(function(t,r){return a("a-col",{key:"registerType"+r,class:"more-btn",attrs:{span:12}},[a("a-button",{staticStyle:{"margin-bottom":"10px"},attrs:{size:"small",type:"primary",ghost:""},on:{click:function(t){return e.goPage(r)}}},[e._v(e._s(t.text))])],1)})),1)],1)])],1)],1)],1),a("a-tab-pane",{key:"2",attrs:{tab:"交互监管"}},[a("a-row",[a("a-col",{attrs:{xl:16,lg:12,md:12,sm:24,xs:24}},[a("bar-multid",{attrs:{dataSource:e.jhjgData,fields:e.jhjgFields,title:"平台与部门交互量统计"}})],1),a("a-col",{attrs:{xl:8,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticStyle:{"margin-bottom":"24px"},attrs:{title:"快速开始 / 便捷导航",bordered:!1,"body-style":{padding:0}}},[a("div",{staticClass:"item-group"},[a("a-row",e._l(e.registerTypeList,(function(t,r){return a("a-col",{key:"registerType"+r,class:"more-btn",attrs:{span:12}},[a("a-button",{staticStyle:{"margin-bottom":"10px"},attrs:{size:"small",type:"primary",ghost:""},on:{click:function(t){return e.goPage(r)}}},[e._v(e._s(t.text))])],1)})),1)],1)])],1)],1)],1),a("a-tab-pane",{key:"3",attrs:{tab:"效率监管"}},[a("a-row",[a("a-col",{attrs:{xl:16,lg:12,md:12,sm:24,xs:24}},[a("line-chart-multid",{attrs:{dataSource:e.xljgData,fields:e.xljgFields,title:"平台与部门交互效率统计"}})],1),a("a-col",{attrs:{xl:8,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticStyle:{"margin-bottom":"24px"},attrs:{title:"快速开始 / 便捷导航",bordered:!1,"body-style":{padding:0}}},[a("div",{staticClass:"item-group"},[a("a-row",e._l(e.registerTypeList,(function(t,r){return a("a-col",{key:"registerType"+r,class:"more-btn",attrs:{span:12}},[a("a-button",{staticStyle:{"margin-bottom":"10px"},attrs:{size:"small",type:"primary",ghost:""},on:{click:function(t){return e.goPage(r)}}},[e._v(e._s(t.text))])],1)})),1)],1)])],1)],1)],1),a("a-tab-pane",{key:"4",attrs:{tab:"存储监管"}},[a("a-row",[a("a-col",{attrs:{xl:16,lg:12,md:12,sm:24,xs:24}},[a("a-row",[e.diskInfo&&e.diskInfo.length>0?e._l(e.diskInfo,(function(e,t){return a("a-col",{key:"diskInfo"+t,attrs:{span:12}},[a("dash-chart-demo",{attrs:{title:e.name,dataSource:e.restPPT}})],1)})):e._e()],2)],1),a("a-col",{attrs:{xl:8,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticStyle:{"margin-bottom":"24px"},attrs:{title:"快速开始 / 便捷导航",bordered:!1,"body-style":{padding:0}}},[a("div",{staticClass:"item-group"},[a("a-row",e._l(e.registerTypeList,(function(t,r){return a("a-col",{key:"registerType"+r,class:"more-btn",attrs:{span:10}},[a("a-button",{staticStyle:{"margin-bottom":"10px"},attrs:{size:"small",type:"primary",ghost:""},on:{click:function(t){return e.goPage(r)}}},[e._v(e._s(t.text))])],1)})),1)],1)])],1)],1)],1)],1)],1)]),a("a-row",{attrs:{gutter:12}},[a("a-card",{class:{"anty-list-cust":!0},style:{marginTop:"24px"},attrs:{loading:e.loading,bordered:!1}},[a("a-tabs",{attrs:{"default-active-key":"1",size:"large","tab-bar-style":{marginBottom:"24px",paddingLeft:"16px"}}},[a("div",{staticClass:"extra-wrapper",attrs:{slot:"tabBarExtraContent"},slot:"tabBarExtraContent"},[a("a-radio-group",{attrs:{defaultValue:"1"}},[a("a-radio-button",{attrs:{value:"1"}},[e._v("转移登记")]),a("a-radio-button",{attrs:{value:"2"}},[e._v("抵押登记")])],1)],1),a("a-tab-pane",{key:"1",attrs:{loading:"true",tab:"业务流程限时监管"}},[a("a-table",{attrs:{dataSource:e.dataSource1,size:"default",rowKey:"id",columns:e.columns,pagination:e.ipagination},scopedSlots:e._u([{key:"flowRate",fn:function(t,r,n){return[a("a-progress",{staticStyle:{width:"80px"},attrs:{percent:e.getFlowRateNumber(r.flowRate)}})]}}])})],1),a("a-tab-pane",{key:"2",attrs:{loading:"true",tab:"业务节点限时监管"}},[a("a-table",{attrs:{dataSource:e.dataSource2,size:"default",rowKey:"id",columns:e.columns2,pagination:e.ipagination},scopedSlots:e._u([{key:"flowRate",fn:function(t,r,n){return[a("span",{staticStyle:{color:"red"}},[e._v(e._s(r.flowRate)+"分钟")])]}}])})],1)],1)],1)],1)],1)},n=[],o=a("a42e"),l=o["a"],s=(a("75bf"),a("2877")),i=Object(s["a"])(l,r,n,!1,null,"18de3997",null);t["default"]=i.exports},6310:function(e,t,a){},6620:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单号"}},[a("a-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.queryParam.orderCode,callback:function(t){e.$set(e.queryParam,"orderCode",t)},expression:"queryParam.orderCode"}})],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单类型"}},[a("a-select",{attrs:{placeholder:"请输入订单类型"},model:{value:e.queryParam.ctype,callback:function(t){e.$set(e.queryParam,"ctype",t)},expression:"queryParam.ctype"}},[a("a-select-option",{attrs:{value:"1"}},[e._v("国内订单")]),a("a-select-option",{attrs:{value:"2"}},[e._v("国际订单")])],1)],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("\n          删除\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",filterMultiple:"filterMultiple",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange,type:e.type},customRow:e.clickThenCheck},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)}}])})],1),a("a-tabs",{attrs:{defaultActiveKey:"1"}},[a("a-tab-pane",{key:"1",attrs:{tab:"客户信息"}},[a("Jeecg-Order-Customer-List",{ref:"JeecgOrderCustomerList"})],1),a("a-tab-pane",{key:"2",attrs:{tab:"机票信息",forceRender:""}},[a("Jeecg-Order-Ticket-List",{ref:"JeecgOrderTicketList"})],1)],1),a("jeecgOrderDMain-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},n=[],o=a("dd9d"),l=a("d544"),s=a("8c22"),i=a("0fea"),c=a("39a5"),d=a("aa8e"),u=a("b65a"),m={name:"JeecgOrderDMainList",mixins:[u["a"]],components:{JeecgOrderTicketModal:d["default"],JeecgOrderCustomerModal:c["default"],JeecgOrderDMainModal:o["default"],JeecgOrderCustomerList:l["default"],JeecgOrderTicketList:s["default"]},data:function(){return{description:"订单管理页面",ipagination:{current:1,pageSize:5,pageSizeOptions:["5","10","20"],showTotal:function(e,t){return t[0]+"-"+t[1]+" 共"+e+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0},columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"订单号",align:"center",dataIndex:"orderCode"},{title:"订单类型",align:"center",dataIndex:"ctype",customRender:function(e){var t="";return"1"===e?t="国内订单":"2"===e&&(t="国际订单"),t}},{title:"订单日期",align:"center",dataIndex:"orderDate"},{title:"订单金额",align:"center",dataIndex:"orderMoney"},{title:"订单备注",align:"center",dataIndex:"content"},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{customRender:"action"}}],type:"radio",url:{list:"/test/order/orderList",delete:"/test/order/delete",deleteBatch:"/test/order/deleteBatch"}}},methods:{clickThenCheck:function(e){var t=this;return{on:{click:function(){t.onSelectChange(e.id.split(","),[e])}}}},onSelectChange:function(e,t){this.selectedRowKeys=e,this.selectionRows=t,this.$refs.JeecgOrderCustomerList.getOrderMain(this.selectedRowKeys[0]),this.$refs.JeecgOrderTicketList.getOrderMain(this.selectedRowKeys[0])},onClearSelected:function(){this.selectedRowKeys=[],this.selectionRows=[],this.$refs.JeecgOrderCustomerList.queryParam.mainId=null,this.$refs.JeecgOrderTicketList.queryParam.mainId=null,this.$refs.JeecgOrderCustomerList.loadData(),this.$refs.JeecgOrderTicketList.loadData(),this.$refs.JeecgOrderCustomerList.selectedRowKeys=[],this.$refs.JeecgOrderCustomerList.selectionRows=[],this.$refs.JeecgOrderTicketList.selectedRowKeys=[],this.$refs.JeecgOrderTicketList.selectionRows=[]},handleDelete:function(e){var t=this,a=this;Object(i["a"])(a.url.delete,{id:e}).then((function(e){e.success?(a.$message.success(e.message),a.loadData(),t.$refs.JeecgOrderCustomerList.loadData(),t.$refs.JeecgOrderTicketList.loadData()):a.$message.warning(e.message)}))},searchQuery:function(){this.selectedRowKeys=[],this.selectionRows=[],this.$refs.JeecgOrderCustomerList.queryParam.mainId=null,this.$refs.JeecgOrderTicketList.queryParam.mainId=null,this.$refs.JeecgOrderCustomerList.loadData(),this.$refs.JeecgOrderTicketList.loadData(),this.$refs.JeecgOrderCustomerList.selectedRowKeys=[],this.$refs.JeecgOrderCustomerList.selectionRows=[],this.$refs.JeecgOrderTicketList.selectedRowKeys=[],this.$refs.JeecgOrderTicketList.selectionRows=[],this.loadData()}}},p=m,f=(a("a468"),a("2877")),h=Object(f["a"])(p,r,n,!1,null,"1bacfb3c",null);t["default"]=h.exports},"66e5":function(e,t,a){},6741:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-skeleton",{attrs:{active:"",loading:e.loading,paragraph:{rows:17}}},[a("a-card",{attrs:{bordered:!1}},[a("a-alert",{attrs:{type:"info",showIcon:!0}},[a("div",{attrs:{slot:"message"},slot:"message"},[e._v("\n        上次更新时间："+e._s(this.time)+"\n        "),a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:e.handleClickUpdate}},[e._v("立即更新")])],1)]),a("a-table",{staticStyle:{"margin-top":"20px"},attrs:{rowKey:"id",size:"middle",columns:e.columns,dataSource:e.dataSource,pagination:!1,loading:e.tableLoading},scopedSlots:e._u([{key:"param",fn:function(t,r){return[a("a-tag",{attrs:{color:e.textInfo[r.param].color}},[e._v(e._s(t))])]}},{key:"text",fn:function(t,a){return[e._v("\n        "+e._s(e.textInfo[a.param].text)+"\n      ")]}},{key:"value",fn:function(t,a){return[e._v("\n        "+e._s(t)+" "+e._s(e.textInfo[a.param].unit)+"\n      ")]}}])})],1)],1)},n=[],o=a("c1df"),l=a.n(o),s=a("0fea");l.a.locale("zh-cn");var i={data:function(){return{time:"",loading:!0,tableLoading:!0,columns:[{title:"参数",width:"30%",dataIndex:"param",scopedSlots:{customRender:"param"}},{title:"描述",width:"40%",dataIndex:"text",scopedSlots:{customRender:"text"}},{title:"当前值",width:"30%",dataIndex:"value",scopedSlots:{customRender:"value"}}],dataSource:[],textInfo:{"jvm.memory.max":{color:"purple",text:"JVM 最大内存",unit:"MB"},"jvm.memory.committed":{color:"purple",text:"JVM 可用内存",unit:"MB"},"jvm.memory.used":{color:"purple",text:"JVM 已用内存",unit:"MB"},"jvm.buffer.memory.used":{color:"cyan",text:"JVM 缓冲区已用内存",unit:"MB"},"jvm.buffer.count":{color:"cyan",text:"当前缓冲区数量",unit:"个"},"jvm.threads.daemon":{color:"green",text:"JVM 守护线程数量",unit:"个"},"jvm.threads.live":{color:"green",text:"JVM 当前活跃线程数量",unit:"个"},"jvm.threads.peak":{color:"green",text:"JVM 峰值线程数量",unit:"个"},"jvm.classes.loaded":{color:"orange",text:"JVM 已加载 Class 数量",unit:"个"},"jvm.classes.unloaded":{color:"orange",text:"JVM 未加载 Class 数量",unit:"个"},"jvm.gc.memory.allocated":{color:"pink",text:"GC 时, 年轻代分配的内存空间",unit:"MB"},"jvm.gc.memory.promoted":{color:"pink",text:"GC 时, 老年代分配的内存空间",unit:"MB"},"jvm.gc.max.data.size":{color:"pink",text:"GC 时, 老年代的最大内存空间",unit:"MB"},"jvm.gc.live.data.size":{color:"pink",text:"FullGC 时, 老年代的内存空间",unit:"MB"},"jvm.gc.pause.count":{color:"blue",text:"系统启动以来GC 次数",unit:"次"},"jvm.gc.pause.totalTime":{color:"blue",text:"系统启动以来GC 总耗时",unit:"秒"}},moreInfo:{"jvm.gc.pause":[".count",".totalTime"]}}},mounted:function(){this.loadTomcatInfo()},methods:{handleClickUpdate:function(){this.loadTomcatInfo()},loadTomcatInfo:function(){var e=this;this.tableLoading=!0,this.time=l()().format("YYYY年MM月DD日 HH时mm分ss秒"),Promise.all([Object(s["c"])("actuator/metrics/jvm.memory.max"),Object(s["c"])("actuator/metrics/jvm.memory.committed"),Object(s["c"])("actuator/metrics/jvm.memory.used"),Object(s["c"])("actuator/metrics/jvm.buffer.memory.used"),Object(s["c"])("actuator/metrics/jvm.buffer.count"),Object(s["c"])("actuator/metrics/jvm.threads.daemon"),Object(s["c"])("actuator/metrics/jvm.threads.live"),Object(s["c"])("actuator/metrics/jvm.threads.peak"),Object(s["c"])("actuator/metrics/jvm.classes.loaded"),Object(s["c"])("actuator/metrics/jvm.classes.unloaded"),Object(s["c"])("actuator/metrics/jvm.gc.memory.allocated"),Object(s["c"])("actuator/metrics/jvm.gc.memory.promoted"),Object(s["c"])("actuator/metrics/jvm.gc.max.data.size"),Object(s["c"])("actuator/metrics/jvm.gc.live.data.size"),Object(s["c"])("actuator/metrics/jvm.gc.pause")]).then((function(t){var a=[];t.forEach((function(t,r){var n=e.moreInfo[t.name];n instanceof Array||(n=[""]),n.forEach((function(n,o){var l=t.name+n,s=t.measurements[o].value;"jvm.memory.max"!==l&&"jvm.memory.committed"!==l&&"jvm.memory.used"!==l&&"jvm.buffer.memory.used"!==l&&"jvm.gc.memory.allocated"!==l&&"jvm.gc.memory.promoted"!==l&&"jvm.gc.max.data.size"!==l&&"jvm.gc.live.data.size"!==l||(s=e.convert(s,Number)),a.push({id:l+r,param:l,text:"false value",value:s})}))})),e.dataSource=a})).catch((function(t){e.$message.error("获取JVM信息失败")})).finally((function(){e.loading=!1,e.tableLoading=!1}))},convert:function(e,t){return t===Number?Number(e/1048576).toFixed(3):t===Date?l()(1e3*e).format("YYYY-MM-DD HH:mm:ss"):e}}},c=i,d=a("2877"),u=Object(d["a"])(c,r,n,!1,null,null,null);t["default"]=u.exports},"75bf":function(e,t,a){"use strict";var r=a("0151"),n=a.n(r);n.a},7896:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",[a("a-button",{attrs:{type:"primary",icon:"desktop"},on:{click:e.splitPane}},[e._v("点我分屏")]),a("split-panel-modal",{ref:"splitPanelModal"})],1)},n=[],o=a("5d1e"),l={name:"SplitPanel",components:{SplitPanelModal:o["default"]},data:function(){return{description:"分屏"}},methods:{splitPane:function(){this.$refs.splitPanelModal.show()}}},s=l,i=a("2877"),c=Object(i["a"])(s,r,n,!1,null,"20bcecf1",null);t["default"]=c.exports},8428:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单号"}},[a("a-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.queryParam.orderCode,callback:function(t){e.$set(e.queryParam,"orderCode",t)},expression:"queryParam.orderCode"}})],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单类型"}},[a("a-select",{attrs:{placeholder:"请输入订单类型"},model:{value:e.queryParam.ctype,callback:function(t){e.$set(e.queryParam,"ctype",t)},expression:"queryParam.ctype"}},[a("a-select-option",{attrs:{value:"1"}},[e._v("国内订单")]),a("a-select-option",{attrs:{value:"2"}},[e._v("国际订单")])],1)],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("\n          删除\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",attrs:{bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:!1,expandedRowKeys:e.expandedRowKeys,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange,expand:e.handleExpand},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)}},{key:"expandedRowRender",fn:function(t){return a("a-table",{attrs:{columns:e.innerColumns,dataSource:e.innerData,size:"middle",bordered:"",rowKey:"id",pagination:!1,loading:e.loading}})}}])})],1),a("jeecgOrderDMain-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},n=[],o=a("0fea"),l=a("dd9d"),s=a("b65a"),i={name:"TableDemo",mixins:[s["a"]],components:{JeecgOrderDMainModal:l["default"]},data:function(){return{innerColumns:[{title:"客户名",align:"center",width:100,dataIndex:"name",key:"name"},{title:"性别",align:"center",dataIndex:"sex",customRender:function(e){return 1==e?"男":2==e?"女":e}},{title:"身份证号码",align:"center",dataIndex:"idcard"},{title:"电话",dataIndex:"telphone",align:"center"}],innerData:[],expandedRowKeys:[],id:" ",description:"列表展开子表Demo",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"订单号",align:"center",dataIndex:"orderCode"},{title:"订单类型",align:"center",dataIndex:"ctype",customRender:function(e){var t="";return"1"===e?t="国内订单":"2"===e&&(t="国际订单"),t}},{title:"订单日期",align:"center",dataIndex:"orderDate"},{title:"订单金额",align:"center",dataIndex:"orderMoney"},{title:"订单备注",align:"center",dataIndex:"content"},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{customRender:"action"}}],type:"radio",url:{list:"/test/order/orderList",delete:"/test/order/delete",deleteBatch:"/test/order/deleteBatch",customerListByMainId:"/test/order/listOrderCustomerByMainId"}}},computed:{currentId:function(){return this.id}},methods:{handleExpand:function(e,t){var a=this;this.expandedRowKeys=[],this.innerData=[],!0===e&&(this.loading=!0,this.expandedRowKeys.push(t.id),Object(o["c"])(this.url.customerListByMainId,{mainId:t.id}).then((function(e){e.success&&(a.loading=!1,a.innerData=e.result.records)})))}}},c=i,d=(a("b8f4"),a("2877")),u=Object(d["a"])(c,r,n,!1,null,"3387e46e",null);t["default"]=u.exports},"86a6":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",[a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"模板标题"}},[a("a-input",{attrs:{disabled:""},model:{value:e.templateName,callback:function(t){e.templateName=t},expression:"templateName"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"模板内容"}},[a("a-textarea",{attrs:{disabled:"",autosize:{minRows:5,maxRows:8}},model:{value:e.templateContent,callback:function(t){e.templateContent=t},expression:"templateContent"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"测试数据"}},[a("a-textarea",{attrs:{placeholder:"请输入json格式测试数据",autosize:{minRows:5,maxRows:8}},model:{value:e.testData,callback:function(t){e.testData=t},expression:"testData"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"消息类型"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择消息类型",dictCode:"msgType"},model:{value:e.msgType,callback:function(t){e.msgType=t},expression:"msgType"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"消息接收方"}},[a("a-input",{attrs:{placeholder:"请输入消息接收方"},model:{value:e.receiver,callback:function(t){e.receiver=t},expression:"receiver"}})],1)],1)],1)],1)},n=[],o=a("0fea"),l={name:"SysMessageTestModal",data:function(){return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,url:{send:"/sys/message/sysMessageTemplate/sendMsg"},templateName:"",templateContent:"",receiver:"",msgType:"",testData:"",sendParams:{}}},methods:{open:function(e){this.sendParams.templateCode=e.templateCode,this.templateName=e.templateName,this.templateContent=e.templateContent,this.testData=e.templateTestJson,this.visible=!0},close:function(){this.receiver="",this.msgType="",this.sendParams={},this.visible=!1},handleOk:function(){var e=this,t=this.url.send,a="post";this.sendParams.testData=this.testData,this.sendParams.receiver=this.receiver,this.sendParams.msgType=this.msgType,Object(o["h"])(t,this.sendParams,a).then((function(t){t.success?e.$message.success(t.message):e.$message.warning(t.message)})).finally((function(){e.confirmLoading=!1,e.close()}))},handleCancel:function(){this.close()}}},s=l,i=a("2877"),c=Object(i["a"])(s,r,n,!1,null,"34150026",null);t["default"]=c.exports},"89ba":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{staticStyle:{overflow:"auto","padding-bottom":"53px"},attrs:{title:e.title,maskClosable:!0,width:"650",placement:"right",closable:!0,visible:e.visible},on:{close:e.close}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"消息标题"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["esTitle",{}],expression:"['esTitle', {}]"}],attrs:{placeholder:"请输入消息标题"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"发送内容"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["esContent",{}],expression:"['esContent', {}]"}],attrs:{placeholder:"请输入发送内容"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"发送所需参数"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["esParam",{}],expression:"['esParam', {}]"}],attrs:{placeholder:"请输入发送所需参数Json格式"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"接收人"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["esReceiver",{}],expression:"['esReceiver', {}]"}],attrs:{placeholder:"请输入接收人"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"发送方式"}},[a("j-dict-select-tag",{directives:[{name:"decorator",rawName:"v-decorator",value:["esType",{}],expression:"[ 'esType', {}]"}],attrs:{triggerChange:!0,dictCode:"msgType",placeholder:"请选择发送方式"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"发送时间"}},[a("a-date-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["esSendTime",{}],expression:"[ 'esSendTime', {}]"}],attrs:{showTime:"",format:"YYYY-MM-DD HH:mm:ss"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"发送状态"}},[a("j-dict-select-tag",{directives:[{name:"decorator",rawName:"v-decorator",value:["esSendStatus",{}],expression:"[ 'esSendStatus', {}]"}],attrs:{triggerChange:!0,dictCode:"msgSendStatus",placeholder:"请选择发送状态"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"发送次数"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["esSendNum",{}],expression:"[ 'esSendNum', {}]"}]})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"发送失败原因"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["esResult",{}],expression:"['esResult', {}]"}]})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"备注"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["remark",{}],expression:"['remark', {}]"}]})],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.disableSubmit,expression:"!disableSubmit"}]},[a("a-button",{staticStyle:{"margin-right":".8rem"},on:{confirm:e.handleCancel}},[e._v("取消")]),a("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},n=[],o=a("0fea"),l=a("88bc"),s=a.n(l),i=a("c1df"),c=a.n(i),d={name:"SysMessageModal",data:function(){return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{},disableSubmit:!0,url:{add:"/sys/message/sysMessage/add",edit:"/sys/message/sysMessage/edit"}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){t.form.setFieldsValue(s()(t.model,"esContent","esParam","esReceiver","esResult","esSendNum","esSendStatus","esTitle","esType","remark")),t.form.setFieldsValue({esSendTime:t.model.esSendTime?c()(t.model.esSendTime):null})}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.form.validateFields((function(a,r){if(!a){t.confirmLoading=!0;var n="",l="";e.model.id?(n+=e.url.edit,l="put"):(n+=e.url.add,l="post");var s=Object.assign(e.model,r);s.esSendTime=s.esSendTime?s.esSendTime.format("YYYY-MM-DD HH:mm:ss"):null,Object(o["h"])(n,s,l).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}}))},handleCancel:function(){this.close()}}},u=d,m=a("2877"),p=Object(m["a"])(u,r,n,!1,null,"2b0b6d98",null);t["default"]=p.exports},"8a45":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:1200,visible:e.visible,confirmLoading:e.confirmLoading},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{"label-col":e.labelCol,"wrapper-col":e.wrapperCol,model:e.orderMainModel,rules:e.validatorRules}},[a("a-row",{staticClass:"form-row",attrs:{gutter:16}},[a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单号",required:"",prop:"orderCode"}},[a("a-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.orderMainModel.orderCode,callback:function(t){e.$set(e.orderMainModel,"orderCode",t)},expression:"orderMainModel.orderCode"}})],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单类型",prop:"ctype"}},[a("a-select",{attrs:{placeholder:"请选择订单类型"},model:{value:e.orderMainModel.ctype,callback:function(t){e.$set(e.orderMainModel,"ctype",t)},expression:"orderMainModel.ctype"}},[a("a-select-option",{attrs:{value:"1"}},[e._v("国内订单")]),a("a-select-option",{attrs:{value:"2"}},[e._v("国际订单")])],1)],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单日期",prop:"orderDate"}},[a("a-date-picker",{attrs:{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss"},model:{value:e.orderMainModel.orderDate,callback:function(t){e.$set(e.orderMainModel,"orderDate",t)},expression:"orderMainModel.orderDate"}})],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:16}},[a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单金额",prop:"orderMoney"}},[a("a-input-number",{staticStyle:{width:"200px"},model:{value:e.orderMainModel.orderMoney,callback:function(t){e.$set(e.orderMainModel,"orderMoney",t)},expression:"orderMainModel.orderMoney"}})],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单备注",prop:"content"}},[a("a-input",{attrs:{placeholder:"请输入订单备注"},model:{value:e.orderMainModel.content,callback:function(t){e.$set(e.orderMainModel,"content",t)},expression:"orderMainModel.content"}})],1)],1)],1),a("a-tabs",{attrs:{defaultActiveKey:"1"}},[a("a-tab-pane",{key:"1",attrs:{tab:"客户信息"}},[a("div",[a("a-row",{staticStyle:{"margin-bottom":"10px"},attrs:{type:"flex",gutter:16}},[a("a-col",{attrs:{span:5}},[e._v("客户名")]),a("a-col",{attrs:{span:5}},[e._v("性别")]),a("a-col",{attrs:{span:6}},[e._v("身份证号码")]),a("a-col",{attrs:{span:6}},[e._v("手机号")]),a("a-col",{attrs:{span:2}},[e._v("操作")])],1),e._l(e.orderMainModel.jeecgOrderCustomerList,(function(t,r){return a("a-row",{key:r,staticStyle:{"margin-bottom":"10px"},attrs:{type:"flex",gutter:16}},[a("a-col",{staticStyle:{display:"none"},attrs:{span:6}},[a("a-form-model-item",[a("a-input",{attrs:{placeholder:"id"},model:{value:t.id,callback:function(a){e.$set(t,"id",a)},expression:"item.id"}})],1)],1),a("a-col",{attrs:{span:5}},[a("a-form-model-item",[a("a-input",{attrs:{placeholder:"客户名"},model:{value:t.name,callback:function(a){e.$set(t,"name",a)},expression:"item.name"}})],1)],1),a("a-col",{attrs:{span:5}},[a("a-form-model-item",[a("a-select",{attrs:{placeholder:"性别"},model:{value:t.sex,callback:function(a){e.$set(t,"sex",a)},expression:"item.sex"}},[a("a-select-option",{attrs:{value:"1"}},[e._v("男")]),a("a-select-option",{attrs:{value:"2"}},[e._v("女")])],1)],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-model-item",{attrs:{prop:"jeecgOrderCustomerList."+r+".idcard",rules:[{required:!0,message:"请输入身份证号",trigger:"blur"},{pattern:e.rules.IDCard,message:"身份证号格式不对!"}]}},[a("a-input",{attrs:{placeholder:"身份证号"},model:{value:t.idcard,callback:function(a){e.$set(t,"idcard",a)},expression:"item.idcard"}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-model-item",{attrs:{prop:"jeecgOrderCustomerList."+r+".telphone",rules:e.rules.mobile}},[a("a-input",{attrs:{placeholder:"手机号"},model:{value:t.telphone,callback:function(a){e.$set(t,"telphone",a)},expression:"item.telphone"}})],1)],1),a("a-col",{attrs:{span:2}},[a("a-form-model-item",[a("a-icon",{staticStyle:{fontSize:"20px"},attrs:{type:"minus-circle"},on:{click:function(t){return e.delRowCustom(r)}}})],1)],1)],1)})),a("a-button",{staticStyle:{width:"98%","margin-top":"10px"},attrs:{type:"dashed"},on:{click:e.addRowCustom}},[a("a-icon",{attrs:{type:"plus"}}),e._v(" 添加客户信息\n            ")],1)],2)]),a("a-tab-pane",{key:"2",attrs:{tab:"机票信息",forceRender:""}},[a("div",[a("a-row",{staticStyle:{"margin-bottom":"10px"},attrs:{type:"flex",gutter:16}},[a("a-col",{attrs:{span:6}},[e._v("航班号")]),a("a-col",{attrs:{span:6}},[e._v("航班时间")]),a("a-col",{attrs:{span:6}},[e._v("操作")])],1),e._l(e.orderMainModel.jeecgOrderTicketList,(function(t,r){return a("a-row",{key:r,staticStyle:{"margin-bottom":"10px"},attrs:{type:"flex",gutter:16}},[a("a-col",{staticStyle:{display:"none"},attrs:{span:6}},[a("a-form-model-item",[a("a-input",{attrs:{placeholder:"id"},model:{value:t.id,callback:function(a){e.$set(t,"id",a)},expression:"item.id"}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-model-item",{attrs:{prop:"jeecgOrderTicketList."+r+".ticketCode",rules:{required:!0,message:"请输入航班号",trigger:"blur"}}},[a("a-input",{attrs:{placeholder:"航班号"},model:{value:t.ticketCode,callback:function(a){e.$set(t,"ticketCode",a)},expression:"item.ticketCode"}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-model-item",[a("a-date-picker",{attrs:{placeholder:"航班时间",valueFormat:"YYYY-MM-DD"},model:{value:t.tickectDate,callback:function(a){e.$set(t,"tickectDate",a)},expression:"item.tickectDate"}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-model-item",[a("a-icon",{staticStyle:{fontSize:"20px"},attrs:{type:"minus-circle"},on:{click:function(t){return e.delRowTicket(r)}}})],1)],1)],1)})),a("a-button",{staticStyle:{width:"98%","margin-top":"10px"},attrs:{type:"dashed"},on:{click:e.addRowTicket}},[a("a-icon",{attrs:{type:"plus"}}),e._v(" 添加机票信息\n            ")],1)],2)])],1)],1)],1)],1)},n=[],o=a("0fea"),l=a("2dab"),s={name:"JeecgOrderMainModal",components:{JDate:l["default"]},data:function(){return{title:"操作",visible:!1,orderMainModel:{jeecgOrderCustomerList:[{}],jeecgOrderTicketList:[{}]},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{orderCode:[{required:!0,message:"订单号不能为空",trigger:"blur"}]},url:{add:"/test/jeecgOrderMain/add",edit:"/test/jeecgOrderMain/edit",orderCustomerList:"/test/jeecgOrderMain/queryOrderCustomerListByMainId",orderTicketList:"/test/jeecgOrderMain/queryOrderTicketListByMainId"}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){var t=this;if(this.orderMainModel=Object.assign({jeecgOrderCustomerList:[{}],jeecgOrderTicketList:[{}]},e),this.orderMainModel.id){var a={id:this.orderMainModel.id};Object(o["c"])(this.url.orderCustomerList,a).then((function(e){e.success&&(t.orderMainModel.jeecgOrderCustomerList=e.result,t.$forceUpdate())})),Object(o["c"])(this.url.orderTicketList,a).then((function(e){e.success&&(t.orderMainModel.jeecgOrderTicketList=e.result,t.$forceUpdate())}))}this.visible=!0},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",n="";e.orderMainModel.id?(r+=e.url.edit,n="put"):(r+=e.url.add,n="post"),Object(o["h"])(r,e.orderMainModel,n).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}}))},handleCancel:function(){this.close()},addRowCustom:function(){this.orderMainModel.jeecgOrderCustomerList.push({}),this.$forceUpdate()},delRowCustom:function(e){var t=this.orderMainModel;t["jeecgOrderCustomerList"].splice(e,1),this.orderMainModel.jeecgOrderCustomerList.splice(e,1),this.$forceUpdate()},addRowTicket:function(){this.orderMainModel.jeecgOrderTicketList.push({}),this.$forceUpdate()},delRowTicket:function(e){var t=this.orderMainModel;t["jeecgOrderTicketList"].splice(e,1),this.orderMainModel.jeecgOrderTicketList.splice(e,1),this.$forceUpdate()}}},i=s,c=(a("aec5"),a("2877")),d=Object(c["a"])(i,r,n,!1,null,"fe614744",null);t["default"]=d.exports},"8b3e1":function(e,t,a){"use strict";var r=a("1c31"),n=a.n(r);n.a},"8c22":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-operator",staticStyle:{margin:"-25px 0px 10px 2px"},attrs:{md:24,sm:24}},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("\n          删除\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("\n            更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{attrs:{href:"javascript:;"},on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("JeecgOrderTicket-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},n=[],o=a("aa8e"),l=a("b65a"),s=a("0fea"),i={name:"JeecgOrderTicketList",mixins:[l["a"]],components:{JeecgOrderTicketModal:o["default"]},data:function(){return{description:"机票信息",columns:[{title:"航班号",align:"center",dataIndex:"ticketCode"},{title:"航班时间",align:"center",dataIndex:"tickectDate"},{title:"订单号码",align:"center",dataIndex:"orderId"},{title:"创建人",align:"center",dataIndex:"createBy"},{title:"创建时间",align:"center",dataIndex:"createTime",sorter:!0},{title:"操作",key:"operation",align:"center",width:130,scopedSlots:{customRender:"action"}}],url:{list:"/test/order/listOrderTicketByMainId",delete:"/test/order/deleteTicket",deleteBatch:"/test/order/deleteBatchTicket"}}},methods:{loadData:function(e){var t=this;1===e&&(this.ipagination.current=1);var a=this.getQueryParams();Object(s["c"])(this.url.list,{orderId:a.mainId,pageNo:this.ipagination.current,pageSize:this.ipagination.pageSize}).then((function(e){e.success?(t.dataSource=e.result.records,t.ipagination.total=e.result.total):t.dataSource=null}))},getOrderMain:function(e){this.queryParam.mainId=e,this.loadData(1)},handleAdd:function(){this.$refs.modalForm.add(this.queryParam.mainId),this.$refs.modalForm.title="添加机票信息"}}},c=i,d=(a("facd9"),a("2877")),u=Object(d["a"])(c,r,n,!1,null,"3e5b2200",null);t["default"]=u.exports},9606:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{staticStyle:{"min-width":"500px","overflow-x":"auto"}},[a("p",[e._v("我是右侧页面")]),a("img-turn-page")],1)},n=[],o=a("4874"),l={name:"SplitPanelAModal",components:{ImgTurnPage:o["default"]},data:function(){return{}},created:function(){},methods:{}},s=l,i=a("2877"),c=Object(i["a"])(s,r,n,!1,null,"233ad4e8",null);t["default"]=c.exports},"9ba5":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-editable-table",{attrs:{columns:e.columns,dataSource:e.dataSource,rowNumber:!0,rowSelection:!0,maxHeight:400,disabled:!0}})},n=[],o=a("e2e0"),l=a("7550"),s={name:"ReadOnlyTable",components:{JEditableTable:l["default"]},data:function(){return{columns:[{title:"输入框",key:"input",type:o["a"].input,placeholder:"清输入"},{title:"下拉框",key:"select",type:o["a"].select,options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}],placeholder:"请选择"},{title:"多选框",key:"checkbox",type:o["a"].checkbox,customValue:[!0,!1]},{title:"日期",key:"datetime",type:o["a"].datetime}],dataSource:[{input:"hello",select:"int",checkbox:!0,datetime:"2019-6-17 14:50:48"},{input:"world",select:"string",checkbox:!1,datetime:"2019-6-16 14:50:48"},{input:"one",select:"double",checkbox:!0,datetime:"2019-6-17 15:50:48"},{input:"two",select:"boolean",checkbox:!1,datetime:"2019-6-14 14:50:48"},{input:"three",select:"",checkbox:!1,datetime:"2019-6-13 14:50:48"}]}},mounted:function(){}},i=s,c=a("2877"),d=Object(c["a"])(i,r,n,!1,null,"967c037a",null);t["default"]=d.exports},"9c24":function(e,t,a){},"9eee":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{md:6,sm:8}},[a("a-form-item",{attrs:{label:"文件名称"}},[a("a-input",{attrs:{placeholder:"请输入文件名称"},model:{value:e.queryParam.fileName,callback:function(t){e.$set(e.queryParam,"fileName",t)},expression:"queryParam.fileName"}})],1)],1),a("a-col",{attrs:{md:6,sm:8}},[a("a-form-item",{attrs:{label:"文件地址"}},[a("a-input",{attrs:{placeholder:"请输入文件地址"},model:{value:e.queryParam.url,callback:function(t){e.$set(e.queryParam,"url",t)},expression:"queryParam.url"}})],1)],1),a("a-col",{attrs:{md:6,sm:8}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-upload",{attrs:{name:"file",multiple:!1,action:e.uploadAction,headers:e.tokenHeader,showUploadList:!1,beforeUpload:e.beforeUpload},on:{change:e.handleChange}},[a("a-button",[a("a-icon",{attrs:{type:"upload"}}),e._v("\n        OSS文件上传\n      ")],1)],1),a("a-upload",{attrs:{name:"file",multiple:!1,action:e.minioUploadAction,headers:e.tokenHeader,showUploadList:!1,beforeUpload:e.beforeUpload},on:{change:e.handleChange}},[a("a-button",[a("a-icon",{attrs:{type:"upload"}}),e._v("\n        MINIO文件上传\n      ")],1)],1)],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handlePreview(r)}}},[e._v("预览")]),a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(t){return e.ossDelete(r.id)}}},[e._v("删除")])],1)}}])})],1)])},n=[],o=a("b65a"),l={name:"OSSFileList",mixins:[o["a"]],data:function(){return{description:"文件列表",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"文件名称",align:"center",dataIndex:"fileName"},{title:"文件地址",align:"center",dataIndex:"url"},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{customRender:"action"}}],url:{upload:"/sys/oss/file/upload",list:"/sys/oss/file/list",delete:"/sys/oss/file/delete",minioUpload:"/sys/upload/uploadMinio"}}},computed:{uploadAction:function(){return window._CONFIG["domianURL"]+this.url.upload},minioUploadAction:function(){return window._CONFIG["domianURL"]+this.url.minioUpload}},methods:{beforeUpload:function(e){var t=e.type;if("image"===t){if(t.indexOf("image")<0)return this.$message.warning("请上传图片"),!1}else if("file"===t&&t.indexOf("image")>=0)return this.$message.warning("请上传文件"),!1;return!0},handleChange:function(e){"done"===e.file.status?e.file.response.success?(this.loadData(),this.$message.success("".concat(e.file.name," 上传成功!"))):this.$message.error("".concat(e.file.response.message)):"error"===e.file.status&&this.$message.error("".concat(e.file.response.message))},ossDelete:function(e){var t=this;t.$confirm({title:"确认删除",content:"是否删除选中文件?",onOk:function(){t.handleDelete(e)}})},handlePreview:function(e){if(e&&e.url){var t=window._CONFIG["onlinePreviewDomainURL"]+"?url="+encodeURIComponent(e.url);window.open(t,"_blank")}}}},s=l,i=(a("5cf8"),a("2877")),c=Object(i["a"])(s,r,n,!1,null,"2486fdc0",null);t["default"]=c.exports},a11f:function(e,t,a){"use strict";var r=a("c55c"),n=a.n(r);n.a},a3fd:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{title:"磁盘监控"}},[a("a-row",[e.diskInfo&&e.diskInfo.length>0?e._l(e.diskInfo,(function(e,t){return a("a-col",{key:"diskInfo"+t,attrs:{span:8}},[a("dash-chart-demo",{attrs:{title:e.name,datasource:e.restPPT}})],1)})):e._e()],2)],1)},n=[],o=a("0fea"),l=a("972f"),s=a("290c"),i={name:"DiskMonitoring",components:{ARow:s["a"],DashChartDemo:l["default"]},data:function(){return{description:"磁盘监控",diskInfo:[],url:{queryDiskInfo:"sys/actuator/redis/queryDiskInfo"}}},created:function(){var e=this;Object(o["c"])(this.url.queryDiskInfo).then((function(t){if(t.success){for(var a=0;a<t.result.length;a++)t.result[a].restPPT=t.result[a].restPPT/10;e.diskInfo=t.result}}))}},c=i,d=a("2877"),u=Object(d["a"])(c,r,n,!1,null,"24f47d75",null);t["default"]=u.exports},a42e:function(module,__webpack_exports__,__webpack_require__){"use strict";for(var _components_ChartCard__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("05ed"),ant_design_vue_es_grid_Col__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("da05"),ant_design_vue_es_tooltip_Tooltip__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("3896"),_components_chart_MiniArea__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("1d43"),_components_chart_MiniBar__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("942d"),_components_chart_LineChartMultid__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("4ec6"),_components_chart_AreaChartTy__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("f552"),_components_chart_DashChartDemo__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__("972f"),_components_chart_BarMultid__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__("8191"),_components_chart_MiniProgress__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__("bf13"),_components_chart_RankList__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__("0923"),_components_chart_Bar__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__("edd9"),_components_Trend__WEBPACK_IMPORTED_MODULE_12__=__webpack_require__("611e"),_api_manage__WEBPACK_IMPORTED_MODULE_13__=__webpack_require__("0fea"),_utils_util__WEBPACK_IMPORTED_MODULE_14__=__webpack_require__("ca00"),dayjs__WEBPACK_IMPORTED_MODULE_15__=__webpack_require__("5a0c"),dayjs__WEBPACK_IMPORTED_MODULE_15___default=__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_15__),rankList=[],i=0;i<7;i++)rankList.push({name:"白鹭岛 "+(i+1)+" 号店",total:1234.56-100*i});var dataCol1=[{title:"业务号",align:"center",dataIndex:"reBizCode"},{title:"权利类型",align:"center",dataIndex:"droitType"},{title:"登记类型",align:"center",dataIndex:"registeType"},{title:"座落",align:"center",dataIndex:"beLocated"},{title:"权利人",align:"center",dataIndex:"qlr"},{title:"义务人",align:"center",dataIndex:"ywr"},{title:"受理人",align:"center",dataIndex:"acceptBy"},{title:"受理时间",align:"center",dataIndex:"acceptDate"},{title:"当前节点",align:"center",dataIndex:"curNode"},{title:"办理进度",align:"center",dataIndex:"flowRate",scopedSlots:{customRender:"flowRate"}}],dataCol2=[{title:"业务号",align:"center",dataIndex:"reBizCode"},{title:"权利类型",align:"center",dataIndex:"droitType"},{title:"登记类型",align:"center",dataIndex:"registeType"},{title:"座落",align:"center",dataIndex:"beLocated"},{title:"权利人",align:"center",dataIndex:"qlr"},{title:"义务人",align:"center",dataIndex:"ywr"},{title:"受理人",align:"center",dataIndex:"acceptBy"},{title:"发起时间",align:"center",dataIndex:"acceptDate"},{title:"当前节点",align:"center",dataIndex:"curNode"},{title:"超时时间",align:"center",dataIndex:"flowRate",scopedSlots:{customRender:"flowRate"}}],jhjgData=[{type:"房管","一月":900,"二月":1120,"三月":1380,"四月":1480,"五月":1450,"六月":1100,"七月":1300,"八月":900,"九月":1e3,"十月":1200,"十一月":600,"十二月":900},{type:"税务","一月":1200,"二月":1500,"三月":1980,"四月":2e3,"五月":1e3,"六月":600,"七月":900,"八月":1100,"九月":1300,"十月":2e3,"十一月":900,"十二月":1100},{type:"不动产","一月":2e3,"二月":1430,"三月":1300,"四月":1400,"五月":900,"六月":500,"七月":600,"八月":1e3,"九月":600,"十月":1e3,"十一月":1500,"十二月":1200}],jhjgFields=["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],xljgData=[{type:"一月","房管":1.12,"税务":1.55,"不动产":1.2},{type:"二月","房管":1.65,"税务":1.32,"不动产":1.42},{type:"三月","房管":1.85,"税务":1.1,"不动产":1.5},{type:"四月","房管":1.33,"税务":1.63,"不动产":1.4},{type:"五月","房管":1.63,"税务":1.8,"不动产":1.7},{type:"六月","房管":1.85,"税务":1.98,"不动产":1.8},{type:"七月","房管":1.98,"税务":1.5,"不动产":1.76},{type:"八月","房管":1.48,"税务":1.2,"不动产":1.3},{type:"九月","房管":1.41,"税务":1.9,"不动产":1.6},{type:"十月","房管":1.1,"税务":1.1,"不动产":1.4},{type:"十一月","房管":1.85,"税务":1.6,"不动产":1.5},{type:"十二月","房管":1.5,"税务":1.4,"不动产":1.3}],xljgFields=["房管","税务","不动产"];__webpack_exports__["a"]={name:"Analysis",components:{ATooltip:ant_design_vue_es_tooltip_Tooltip__WEBPACK_IMPORTED_MODULE_2__["a"],ACol:ant_design_vue_es_grid_Col__WEBPACK_IMPORTED_MODULE_1__["b"],ChartCard:_components_ChartCard__WEBPACK_IMPORTED_MODULE_0__["default"],MiniArea:_components_chart_MiniArea__WEBPACK_IMPORTED_MODULE_3__["default"],MiniBar:_components_chart_MiniBar__WEBPACK_IMPORTED_MODULE_4__["default"],MiniProgress:_components_chart_MiniProgress__WEBPACK_IMPORTED_MODULE_9__["default"],RankList:_components_chart_RankList__WEBPACK_IMPORTED_MODULE_10__["default"],Bar:_components_chart_Bar__WEBPACK_IMPORTED_MODULE_11__["default"],Trend:_components_Trend__WEBPACK_IMPORTED_MODULE_12__["a"],LineChartMultid:_components_chart_LineChartMultid__WEBPACK_IMPORTED_MODULE_5__["default"],AreaChartTy:_components_chart_AreaChartTy__WEBPACK_IMPORTED_MODULE_6__["default"],DashChartDemo:_components_chart_DashChartDemo__WEBPACK_IMPORTED_MODULE_7__["default"],BarMultid:_components_chart_BarMultid__WEBPACK_IMPORTED_MODULE_8__["default"]},data:function(){return{xljgData:xljgData,xljgFields:xljgFields,jhjgData:jhjgData,jhjgFields:jhjgFields,loading:!0,rankList:rankList,zsll:0,zbjl:0,todaySll:0,todayBjl:0,todayISll:0,todayIBjl:0,registerTypeList:[{text:"业务受理"},{text:"业务管理"},{text:"文件管理"},{text:"信息查询"}],ipagination:{current:1,pageSize:5,pageSizeOptions:["10","20","30"],showTotal:function(e,t){return t[0]+"-"+t[1]+" 共"+e+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0},dataSource:[],dataSource1:[],dataSource2:[],url:{analysis:"/sps/register/analysis",list:"sps/register/virtualList",countSll:"sps/register/sllTenDaysCount",countBjl:"sps/register/bjlTenDaysCount",countISll:"sps/register/ISllTenDaysCount",countIBjl:"sps/register/IBjlTenDaysCount",queryDiskInfo:"sys/actuator/redis/queryDiskInfo"},chartData:{sll:[],bjl:[],isll:[],ibjl:[]},cardCount:{sll:0,bjl:0,isll:0,ibjl:0},columns:dataCol1,columns2:dataCol2,diskInfo:[]}},methods:{goPage:function(e){0==e?this.$router.push({path:"/isps/registerStepForm",name:"isps-registerStepForm"}):1==e?this.$router.push({path:"/isps/registerList",name:"isps-registerList"}):2==e?this.$router.push({path:"/isps/fileManage",name:"isps-fileManage"}):3==e&&this.$router.push({path:"/isps/infoSearch",name:"isps-infoSearch"})},loadList:function(e){var t=this;1===e&&(this.ipagination.current=1);var a=this.getQueryParams();Object(_api_manage__WEBPACK_IMPORTED_MODULE_13__["c"])(this.url.list,a).then((function(e){t.dataSource1=e.result.data1,t.dataSource2=e.result.data2,t.ipagination.total=5}))},getQueryParams:function(){var e={flowStatus:"-3"};return e.pageNo=this.ipagination.current,e.pageSize=this.ipagination.pageSize,Object(_utils_util__WEBPACK_IMPORTED_MODULE_14__["d"])(e)},formatRespectiveHoldCert:function formatRespectiveHoldCert(value){return"1"==value||eval(value)?"是":"否"},formatCertFormat:function(e){return"1"==e?"单一版":"2"==e?"集成版":e},getFlowRateNumber:function(e){return Number(e)},getFlowPercent:function(e){return"3"==e.flowStatus?100:"0"==e.flowStatus?0:e.flowRate},getFlowStatus:function(e){return"4"==e?"exception":"0"==e?"normal":"3"==e?"success":"active"},queryCount:function(){var e=this;Object(_api_manage__WEBPACK_IMPORTED_MODULE_13__["c"])(this.url.analysis).then((function(t){t.success&&(e.cardCount=t.result)}))},loadDiskInfo:function(){var e=this;Object(_api_manage__WEBPACK_IMPORTED_MODULE_13__["c"])(this.url.queryDiskInfo).then((function(t){if(t.success){var a=0,r="";for(var n in t.result){var o=Number(t.result[n].max);o>a&&(a=o,r=t.result[n].name)}var l=t.result.filter((function(e){return e.name==r}))[0];if(l.restPPT=l.restPPT/10,e.diskInfo.push(l),t.result.length>1){var s=0,i="";for(var c in t.result)if(t.result[c].name!=r){var d=Number(t.result[c].max);d>s&&(s=d,i=t.result[c].name)}var u=t.result.filter((function(e){return e.name==i}))[0];u.restPPT=u.restPPT/10,e.diskInfo.push(u)}}}))},loadChartData:function(){var e=this;Object(_api_manage__WEBPACK_IMPORTED_MODULE_13__["c"])(this.url.countSll).then((function(t){if(t.success){var a=t.result;for(var r in a){var n=r,o=a[r],l=dayjs__WEBPACK_IMPORTED_MODULE_15___default()(new Date).format("YYYY-MM-DD");n==l&&(e.todaySll=a[l]),e.chartData.sll.push({x:n,y:o})}}})),Object(_api_manage__WEBPACK_IMPORTED_MODULE_13__["c"])(this.url.countBjl).then((function(t){if(t.success){var a=t.result;for(var r in a){var n=r,o=a[r],l=dayjs__WEBPACK_IMPORTED_MODULE_15___default()(new Date).format("YYYY-MM-DD");n==l&&(e.todayBjl=a[l]),e.chartData.bjl.push({x:n,y:o})}}})),Object(_api_manage__WEBPACK_IMPORTED_MODULE_13__["c"])(this.url.countISll).then((function(t){if(t.success){var a=t.result;for(var r in a){var n=r,o=a[r],l=dayjs__WEBPACK_IMPORTED_MODULE_15___default()(new Date).format("YYYY-MM-DD");n==l&&(e.todayISll=a[l]),e.chartData.isll.push({x:r,y:o})}}})),Object(_api_manage__WEBPACK_IMPORTED_MODULE_13__["c"])(this.url.countIBjl).then((function(t){if(t.success){var a=t.result;for(var r in a){var n=r,o=a[r],l=dayjs__WEBPACK_IMPORTED_MODULE_15___default()(new Date).format("YYYY-MM-DD");n==l&&(e.todayIBjl=a[l]),e.chartData.ibjl.push({x:r,y:o})}}}))}},created:function(){var e=this;this.loadDiskInfo(),this.queryCount(),this.loadChartData(),this.loadList(1),setTimeout((function(){e.loading=!e.loading}),1e3)}}},a468:function(e,t,a){"use strict";var r=a("33df"),n=a.n(r);n.a},a8c3:function(e,t,a){"use strict";var r=a("5c7b"),n=a.n(r);n.a},aa8e:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,okButtonProps:{props:{disabled:e.disableSubmit}},confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"航班号",prop:"ticketCode",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入航班号",readOnly:e.disableSubmit},model:{value:e.model.ticketCode,callback:function(t){e.$set(e.model,"ticketCode",t)},expression:"model.ticketCode"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"航班时间",prop:"tickectDate",hasFeedback:""}},[a("j-date",{model:{value:e.model.tickectDate,callback:function(t){e.$set(e.model,"tickectDate",t)},expression:"model.tickectDate"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"订单号码",hidden:e.hiding,hasFeedback:""},model:{value:this.orderId,callback:function(t){e.$set(this,"orderId",t)},expression:"this.orderId"}},[a("a-input",{attrs:{disabled:"disabled"},model:{value:e.model.orderId,callback:function(t){e.$set(e.model,"orderId",t)},expression:"model.orderId"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"创建人",hidden:e.hiding,hasFeedback:""}},[a("a-input",{attrs:{readOnly:e.disableSubmit},model:{value:e.model.createBy,callback:function(t){e.$set(e.model,"createBy",t)},expression:"model.createBy"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"创建时间",hidden:e.hiding,hasFeedback:""}},[a("a-input",{attrs:{readOnly:e.disableSubmit},model:{value:e.model.createTime,callback:function(t){e.$set(e.model,"createTime",t)},expression:"model.createTime"}})],1)],1)],1)],1)},n=[],o=a("0fea"),l=a("c1df"),s=a.n(l),i=a("2dab"),c={components:{JDate:i["default"]},name:"JeecgOrderTicketModal",data:function(){return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},moment:s.a,format:"YYYY-MM-DD HH:mm:ss",disableSubmit:!1,orderId:"",hiding:!1,confirmLoading:!1,validatorRules:{ticketCode:[{required:!0,message:"请输入航班号!"}],tickectDate:[{required:!0,message:"请输入航班时间!"}]},url:{add:"/test/order/addTicket",edit:"/test/order/editTicket"}}},created:function(){},methods:{add:function(e){e?this.edit({orderId:e},""):this.$message.warning("请选择一条航班数据")},detail:function(e){this.edit(e,"d")},edit:function(e,t){"e"==t?(this.hiding=!1,this.disableSubmit=!1):"d"==t?(this.hiding=!1,this.disableSubmit=!0):(this.hiding=!0,this.disableSubmit=!1),this.model=Object.assign({},e),this.visible=!0},close:function(){this.$emit("close"),this.visible=!1,this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0;var r="",n="";e.model.id?(r+=e.url.edit,n="put"):(r+=e.url.add,n="post"),e.model.mainId=e.model.orderId,Object(o["h"])(r,e.model,n).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()}}},d=c,u=a("2877"),m=Object(u["a"])(d,r,n,!1,null,"1c960cda",null);t["default"]=m.exports},add4:function(e,t,a){},aec5:function(e,t,a){"use strict";var r=a("5edf4"),n=a.n(r);n.a},b02a:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},n=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",[e._v("Redis终端")])])}],o={name:"RedisTerminal"},l=o,s=a("2877"),i=Object(s["a"])(l,r,n,!1,null,null,null);t["default"]=i.exports},b05d:function(e,t,a){},b2b7:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{staticStyle:{height:"100%","padding-bottom":"200px"},attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form-model",{ref:"form",attrs:{model:e.formData,layout:"inline"}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"性别",prop:"sex"}},[a("j-dict-select-tag",{attrs:{title:"性别",dictCode:"sex",placeholder:"请选择性别"},model:{value:e.formData.sex,callback:function(t){e.$set(e.formData,"sex",t)},expression:"formData.sex"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中值："+e._s(e.formData.sex))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"性别2",prop:"sex2"}},[a("j-dict-select-tag",{attrs:{type:"radioButton",title:"性别2",dictCode:"sex",placeholder:"请选择性别2"},model:{value:e.formData.sex2,callback:function(t){e.$set(e.formData,"sex2",t)},expression:"formData.sex2"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中值："+e._s(e.formData.sex2))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"字典表下拉",prop:"user"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择用户",dictCode:"sys_user,realname,id"},model:{value:e.formData.user,callback:function(t){e.$set(e.formData,"user",t)},expression:"formData.user"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中值："+e._s(e.formData.user))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"字典表下拉(带条件)",prop:"user2"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择用户",dictCode:"sys_user,realname,id,username!='admin' order by create_time"},model:{value:e.formData.user2,callback:function(t){e.$set(e.formData,"user2",t)},expression:"formData.user2"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中值："+e._s(e.formData.user2))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"字典搜索(同步)",prop:"searchValue"}},[a("j-search-select-tag",{attrs:{placeholder:"请做出你的选择",dictOptions:e.searchOptions},model:{value:e.formData.searchValue,callback:function(t){e.$set(e.formData,"searchValue",t)},expression:"formData.searchValue"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中值："+e._s(e.formData.searchValue))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"字典搜索(异步)",prop:"asyncSelectValue"}},[a("j-search-select-tag",{attrs:{placeholder:"请做出你的选择",dict:"sys_depart,depart_name,id",pageSize:6,async:!0},model:{value:e.formData.asyncSelectValue,callback:function(t){e.$set(e.formData,"asyncSelectValue",t)},expression:"formData.asyncSelectValue"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中值："+e._s(e.formData.asyncSelectValue))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"字典下拉(多选)",prop:"selMuti"}},[a("j-multi-select-tag",{attrs:{dictCode:"sex",placeholder:"请选择"},model:{value:e.formData.selMuti,callback:function(t){e.$set(e.formData,"selMuti",t)},expression:"formData.selMuti"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("多选组合(v-model)："+e._s(e.formData.selMuti))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"选择部门 自定义返回值",prop:"orgCodes"}},[a("j-select-depart",{attrs:{"trigger-change":!0,customReturnField:"orgCode",multi:!0},model:{value:e.formData.orgCodes,callback:function(t){e.$set(e.formData,"orgCodes",t)},expression:"formData.orgCodes"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中的部门Code(v-model):"+e._s(e.formData.orgCodes))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"选择部门",prop:"departId"}},[a("j-select-depart",{attrs:{multi:!0},model:{value:e.formData.departId,callback:function(t){e.$set(e.formData,"departId",t)},expression:"formData.departId"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中的部门ID(v-model):"+e._s(e.formData.departId))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"选择用户",prop:"userIds"}},[a("j-select-user-by-dep",{attrs:{multi:!0},model:{value:e.formData.userIds,callback:function(t){e.$set(e.formData,"userIds",t)},expression:"formData.userIds"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中的用户(v-model):"+e._s(e.formData.userIds))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"选择用户",prop:"multiUser"}},[a("j-select-multi-user",{attrs:{"query-config":e.selectUserQueryConfig},model:{value:e.formData.multiUser,callback:function(t){e.$set(e.formData,"multiUser",t)},expression:"formData.multiUser"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中的用户(v-model):"+e._s(e.formData.multiUser))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"选择角色",prop:"selectRole"}},[a("j-select-role",{on:{change:e.changeMe},model:{value:e.formData.selectRole,callback:function(t){e.$set(e.formData,"selectRole",t)},expression:"formData.selectRole"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中值："+e._s(e.formData.selectRole))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"选择职务",prop:"selectPosition"}},[a("j-select-position",{attrs:{buttons:!1},model:{value:e.formData.selectPosition,callback:function(t){e.$set(e.formData,"selectPosition",t)},expression:"formData.selectPosition"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中值："+e._s(e.formData.selectPosition))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"多选组合",prop:"jCheckbox"}},[a("j-checkbox",{attrs:{options:e.jCheckboxOptions},model:{value:e.formData.jCheckbox,callback:function(t){e.$set(e.formData,"jCheckbox",t)},expression:"formData.jCheckbox"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("多选组合(v-model)："+e._s(e.formData.jCheckbox))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{staticStyle:{"min-height":"120px"},attrs:{label:"代码输入框",prop:"jCodeEditor"}},[a("j-code-editor",{staticStyle:{"min-height":"100px"},attrs:{language:"javascript",fullScreen:!0},model:{value:e.formData.jCodeEditor,callback:function(t){e.$set(e.formData,"jCodeEditor",t)},expression:"formData.jCodeEditor"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("代码输入框(v-model)："+e._s(e.formData.jCodeEditor))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"日期选择框",prop:"jDate"}},[a("j-date",{attrs:{showTime:!0,dateFormat:"YYYY-MM-DD HH:mm:ss"},model:{value:e.formData.jDate,callback:function(t){e.$set(e.formData,"jDate",t)},expression:"formData.jDate"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("日期选择框(v-model)："+e._s(e.formData.jDate))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{staticStyle:{"min-height":"300px"},attrs:{label:"富文本编辑器",prop:"jEditor"}},[a("j-editor",{model:{value:e.formData.jEditor,callback:function(t){e.$set(e.formData,"jEditor",t)},expression:"formData.jEditor"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("富文本编辑器(v-model)："+e._s(e.formData.jEditor))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"过长剪切",prop:"jEllipsis"}},[a("j-ellipsis",{attrs:{value:e.formData.jEllipsis,length:30}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("过长剪切："+e._s(e.formData.jEllipsis))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"滑块验证码",prop:"jSlider"}},[a("j-slider",{on:{onSuccess:e.handleJSliderSuccess}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("滑块验证码验证通过："+e._s(e.formData.jSlider))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"多选下拉框",prop:"jSelectMultiple"}},[a("j-select-multiple",{attrs:{options:e.jSelectMultipleOptions},model:{value:e.formData.jSelectMultiple,callback:function(t){e.$set(e.formData,"jSelectMultiple",t)},expression:"formData.jSelectMultiple"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("多选下拉框(v-model)："+e._s(e.formData.jSelectMultiple))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",[a("a-form-model-item",{attrs:{label:"JModal弹窗"}},[a("a-button",{staticStyle:{"margin-right":"8px"},on:{click:function(){return e.modal.visible=!0}}},[e._v("点击弹出JModal")]),a("span",{staticStyle:{"margin-right":"8px"}},[e._v("全屏化："),a("a-switch",{model:{value:e.modal.fullscreen,callback:function(t){e.$set(e.modal,"fullscreen",t)},expression:"modal.fullscreen"}})],1),a("span",{staticStyle:{"margin-right":"8px"}},[e._v("允许切换全屏："),a("a-switch",{model:{value:e.modal.switchFullscreen,callback:function(t){e.$set(e.modal,"switchFullscreen",t)},expression:"modal.switchFullscreen"}})],1)],1),a("j-modal",{attrs:{visible:e.modal.visible,width:1200,title:e.modal.title,fullscreen:e.modal.fullscreen,switchFullscreen:e.modal.switchFullscreen},on:{"update:visible":function(t){return e.$set(e.modal,"visible",t)},"update:fullscreen":function(t){return e.$set(e.modal,"fullscreen",t)}}},[e._l(30,(function(t,r){return[a("p",{key:r},[e._v("这是主体内容，高度是自适应的")])]}))],2)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"树字典",prop:"treeDict"}},[a("j-tree-dict",{attrs:{placeholder:"请选择树字典",parentCode:"A01"},model:{value:e.formData.treeDict,callback:function(t){e.$set(e.formData,"treeDict",t)},expression:"formData.treeDict"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中的值(v-model)："+e._s(e.formData.treeDict))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"下拉树选择",prop:"treeSelect"}},[a("j-tree-select",{attrs:{placeholder:"请选择菜单",dict:"sys_permission,name,id",pidField:"parent_id",pidValue:""},model:{value:e.formData.treeSelect,callback:function(t){e.$set(e.formData,"treeSelect",t)},expression:"formData.treeSelect"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中的值(v-model)："+e._s(e.formData.treeSelect))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"下拉树多选",prop:"treeSelectMultiple"}},[a("j-tree-select",{attrs:{placeholder:"请选择菜单",dict:"sys_permission,name,id",pidField:"parent_id",pidValue:"",multiple:""},model:{value:e.formData.treeSelectMultiple,callback:function(t){e.$set(e.formData,"treeSelectMultiple",t)},expression:"formData.treeSelectMultiple"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中的值(v-model)："+e._s(e.formData.treeSelectMultiple))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"分类字典树",prop:"selectCategory"}},[a("j-category-select",{attrs:{pcode:"B01",multiple:!0},model:{value:e.formData.selectCategory,callback:function(t){e.$set(e.formData,"selectCategory",t)},expression:"formData.selectCategory"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中的值(v-model)："+e._s(e.formData.selectCategory))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"cron表达式",prop:"cronExpression"}},[a("j-easy-cron",{model:{value:e.formData.cronExpression,callback:function(t){e.$set(e.formData,"cronExpression",t)},expression:"formData.cronExpression"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"高级查询"}},[a("j-super-query",{attrs:{fieldList:e.superQuery.fieldList}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"高级查询（自定义按钮）"}},[a("j-super-query",{attrs:{fieldList:e.superQuery.fieldList},scopedSlots:e._u([{key:"button",fn:function(t){var r=t.isActive,n=t.isMobile,o=t.open,l=t.reset;return[r?a("a-button-group",[a("a-button",{attrs:{type:"primary",ghost:""},on:{click:function(e){return o()}}},[a("a-icon",{attrs:{type:"plus-circle",spin:""}}),a("span",[e._v("高级查询")])],1),n?a("a-button",{attrs:{type:"primary",ghost:"",icon:"delete"},on:{click:function(e){return l()}}}):e._e()],1):a("a-button",{attrs:{type:"primary",ghost:"",icon:"clock-circle"},on:{click:function(e){return o()}}},[e._v("高级查询")])]}}])})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"图片上传",prop:"imgList"}},[a("j-image-upload",{attrs:{bizPath:"scott/pic"},model:{value:e.formData.imgList,callback:function(t){e.$set(e.formData,"imgList",t)},expression:"formData.imgList"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选中的值(v-model)："+e._s(e.formData.imgList))])],1),a("a-row",{staticStyle:{"margin-top":"65px","margin-bottom":"50px"},attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"文件上传",prop:"fileList"}},[a("j-upload",{model:{value:e.formData.fileList,callback:function(t){e.$set(e.formData,"fileList",t)},expression:"formData.fileList"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("\n          选中的值(v-model)：\n          "),e.formData.fileList.length>0?a("j-ellipsis",{attrs:{value:e.formData.fileList,length:30}}):e._e()],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"特殊查询组件",prop:"jInput"}},[a("a-row",[a("a-col",{attrs:{span:15}},[a("j-input",{attrs:{type:e.jInput.type},model:{value:e.formData.jInput,callback:function(t){e.$set(e.formData,"jInput",t)},expression:"formData.jInput"}})],1),a("a-col",{staticStyle:{"text-align":"right"},attrs:{span:4}},[e._v("查询类型：")]),a("a-col",{attrs:{span:5}},[a("a-select",{attrs:{options:e.jInput.options},model:{value:e.jInput.type,callback:function(t){e.$set(e.jInput,"type",t)},expression:"jInput.type"}})],1)],1)],1)],1),a("a-col",{attrs:{span:12}},[e._v("输入的值(v-model)："+e._s(e.formData.jInput))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:15}},[a("a-form-model-item",{staticStyle:{"min-height":"300px"},attrs:{label:"MarkdownEditor",prop:"content"}},[a("j-markdown-editor",{model:{value:e.formData.content,callback:function(t){e.$set(e.formData,"content",t)},expression:"formData.content"}})],1)],1),a("a-col",{attrs:{span:9}},[e._v("\n          输入的值(v-model)："+e._s(e.formData.content)+"\n        ")])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"省市县级联",prop:"areaLinkage1"}},[a("j-area-linkage",{attrs:{type:"cascader"},model:{value:e.formData.areaLinkage1,callback:function(t){e.$set(e.formData,"areaLinkage1",t)},expression:"formData.areaLinkage1"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("输入的值(v-model)："+e._s(e.formData.areaLinkage1))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"省市县级联",prop:"areaLinkage2"}},[a("j-area-linkage",{attrs:{type:"select"},model:{value:e.formData.areaLinkage2,callback:function(t){e.$set(e.formData,"areaLinkage2",t)},expression:"formData.areaLinkage2"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("输入的值(v-model)："+e._s(e.formData.areaLinkage2))])],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"功能示例：关闭当前页面"}},[a("a-button",{attrs:{type:"primary"},on:{click:e.handleCloseCurrentPage}},[e._v("点击关闭当前页面")])],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"JPopup示例",prop:"jPopup"}},[a("j-popup",{attrs:{code:"demo",field:"name",orgFields:"name",destFields:"name",multi:!0},model:{value:e.formData.jPopup,callback:function(t){e.$set(e.formData,"jPopup",t)},expression:"formData.jPopup"}})],1)],1),a("a-col",{attrs:{span:12}},[e._v("选择的值(v-model)："+e._s(e.formData.jPopup))])],1)],1)],1)])},n=[],o=a("7b16"),l=a("fe54"),s=a("c14a"),i=a("0f9d"),c=a("1e8c"),d=a("a726"),u=a("a876"),m=a("2dab"),p=a("a061"),f=a("d579"),h=a("ae14"),b=a("13d2"),v=a("4165"),g=a("1e29"),y=a("9fa5"),_=a("b098"),k=a("8c6e"),x=a("cf74"),w=a("e610"),C=a("61fc"),S=a("49a8"),D=a("f680"),M=a("4349"),O=a("9e8f"),T=a("6f9a"),j=a("c010"),I={name:"SelectDemo",inject:["closeCurrent"],components:{JMarkdownEditor:T["default"],JAreaLinkage:O["default"],JInput:M["default"],JCategorySelect:S["default"],JSelectPosition:C["default"],JImageUpload:w["default"],JUpload:x["default"],JTreeDict:v["default"],JDictSelectTag:o["default"],JSelectDepart:l["default"],JSelectUserByDep:s["default"],JSelectMultiUser:i["default"],JSelectRole:c["default"],JCheckbox:d["default"],JCodeEditor:u["default"],JDate:m["default"],JEditor:p["default"],JEllipsis:f["default"],JSlider:h["default"],JSelectMultiple:b["default"],JCron:g["default"],JEasyCron:y["a"],JTreeSelect:_["default"],JSuperQuery:k["default"],JMultiSelectTag:D["default"],JSearchSelectTag:j["default"]},data:function(){return{selectList:[],selectedDepUsers:"",formData:{areaLinkage1:"110105",areaLinkage2:"140221",sex:1,orgCodes:"A02A01,A02A02",departId:"57197590443c44f083d42ae24ef26a2c,a7d7e77e06c84325a40932163adcdaa6",userIds:"admin",multiUser:"admin,jeecg",jCheckbox:"spring,jeecgboot",jCodeEditor:"function sayHi(word) {\n  alert(word)\n}\nsayHi('hello, world!')",jDate:"2019-5-10 15:33:06",jEditor:'<h2 style="text-align: center;">富文本编辑器</h2> <p>这里是富文本编辑器。</p>',jEllipsis:"这是一串很长很长的文字段落。这是一串很长很长的文字段落。这是一串很长很长的文字段落。这是一串很长很长的文字段落。",jSlider:!1,jSelectMultiple:"Integer,Boolean",imgList:[],fileList:[],content:"",cronExpression:"* * * * * ? *"},jCheckboxOptions:[{label:"Jeecg",value:"jeecg"},{label:"Jeecg-Boot",value:"jeecgboot"},{label:"Spring",value:"spring",disabled:!0},{label:"MyBaits",value:"mybatis"}],jSelectMultipleOptions:[{text:"字符串",value:"String"},{text:"整数型",value:"Integer"},{text:"浮点型",value:"Double"},{text:"布尔型",value:"Boolean"}],modal:{title:"这里是标题",visible:!1,fullscreen:!0,switchFullscreen:!0},cron:"",superQuery:{fieldList:[{type:"input",value:"name",text:"姓名"},{type:"select",value:"sex",text:"性别",dictCode:"sex"},{type:"number",value:"age",text:"年龄"},{type:"select",value:"hobby",text:"爱好",options:[{label:"音乐",value:"1"},{label:"游戏",value:"2"},{label:"电影",value:"3"},{label:"读书",value:"4"}]}]},jInput:{type:"like",options:[{value:"like",label:"模糊（like）"},{value:"ne",label:"不等于（ne）"},{value:"ge",label:"大于等于（ge）"},{value:"le",label:"小于等于（le)"}]},searchOptions:[{text:"选项一",value:"1"},{text:"选项二",value:"2"},{text:"选项三",value:"3"}],selectUserQueryConfig:[{key:"phone",label:"电话"}]}},computed:{nameList:function(){for(var e=[],t=0;t<this.selectList.length;t++)e.push(this.selectList[t].name);return e}},methods:{handleChange:function(){},getDepartIdValue:function(){return this.formData.departId},getOrgCodesValue:function(){return this.formData.orgCodes},changeMe:function(){},selectOK:function(e){this.selectList=e},handleSelect:function(){this.$refs.selectDemoModal.add()},selectReset:function(){this.selectList=[]},onSearchDepUser:function(){this.$refs.JSearchUserByDep.showModal(),this.selectedDepUsers="",this.$refs.JSearchUserByDep.title="根据部门查询用户"},onSearchDepUserCallBack:function(e){this.selectedDepUsers=e},handleJSliderSuccess:function(e){this.formData.jSlider=e},handleCloseCurrentPage:function(){this.closeCurrent()}}},E=I,$=(a("da7e"),a("2877")),L=Object($["a"])(E,r,n,!1,null,"0ec57441",null);t["default"]=L.exports},b6bba:function(e,t,a){},b8ad3:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:1200,visible:e.visible,maskClosable:!1,confirmLoading:e.confirmLoading},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{"label-col":e.labelCol,"wrapper-col":e.wrapperCol,model:e.model}},[a("a-row",{staticClass:"form-row",attrs:{gutter:0}},[a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单号",prop:"orderCode",rules:[{required:!0,message:"请输入订单号!"}]}},[a("a-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.model.orderCode,callback:function(t){e.$set(e.model,"orderCode",t)},expression:"model.orderCode"}})],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单类型"}},[a("a-select",{attrs:{placeholder:"请选择订单类型"},model:{value:e.model.ctype,callback:function(t){e.$set(e.model,"ctype",t)},expression:"model.ctype"}},[a("a-select-option",{attrs:{value:"1"}},[e._v("国内订单")]),a("a-select-option",{attrs:{value:"2"}},[e._v("国际订单")])],1)],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单日期"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss"},model:{value:e.model.orderDate,callback:function(t){e.$set(e.model,"orderDate",t)},expression:"model.orderDate"}})],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:0}},[a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单金额"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入订单金额"},model:{value:e.model.orderMoney,callback:function(t){e.$set(e.model,"orderMoney",t)},expression:"model.orderMoney"}})],1)],1),a("a-col",{attrs:{lg:8}},[a("a-form-model-item",{attrs:{label:"订单备注"}},[a("a-input",{attrs:{placeholder:"请输入订单备注"},model:{value:e.model.content,callback:function(t){e.$set(e.model,"content",t)},expression:"model.content"}})],1)],1)],1)],1),a("a-tabs",{on:{change:e.handleChangeTabs},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[a("a-tab-pane",{key:"1",attrs:{tab:"客户信息",forceRender:!0}},[a("j-editable-table",{ref:"editableTable1",attrs:{loading:e.table1.loading,columns:e.table1.columns,dataSource:e.table1.dataSource,maxHeight:300,rowNumber:!0,rowSelection:!0,actionButton:!0}})],1),a("a-tab-pane",{key:"2",attrs:{tab:"机票信息",forceRender:!0}},[a("j-editable-table",{ref:"editableTable2",attrs:{loading:e.table2.loading,columns:e.table2.columns,dataSource:e.table2.dataSource,maxHeight:300,rowNumber:!0,rowSelection:!0,actionButton:!0}})],1)],1)],1)],1)},n=[],o=a("7550"),l=a("e2e0"),s=a("0fea"),i=a("2dab");function c(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?c(Object(a),!0).forEach((function(t){u(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function u(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var m={name:"JeecgOrderModalForJEditableTable",components:{JDate:i["default"],JEditableTable:o["default"]},data:function(){return{title:"操作",visible:!1,confirmLoading:!1,model:{},labelCol:{xs:{span:24},sm:{span:6}},wrapperCol:{xs:{span:24},sm:{span:18}},activeKey:"1",table1:{loading:!1,dataSource:[],columns:[{title:"客户名",key:"name",width:"24%",type:l["a"].input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"性别",key:"sex",width:"18%",type:l["a"].select,options:[{title:"男",value:"1"},{title:"女",value:"2"}],defaultValue:"",placeholder:"请选择${title}"},{title:"身份证号",key:"idcard",width:"24%",type:l["a"].input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{pattern:"^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|[xX])$",message:"${title}格式不正确"}]},{title:"手机号",key:"telphone",width:"24%",type:l["a"].input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{pattern:"^1(3|4|5|7|8)\\d{9}$",message:"${title}格式不正确"}]}]},table2:{loading:!1,dataSource:[],columns:[{title:"航班号",key:"ticketCode",width:"40%",type:l["a"].input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"航班时间",key:"tickectDate",width:"30%",type:l["a"].date,placeholder:"请选择${title}",defaultValue:""}]},url:{add:"/test/jeecgOrderMain/add",edit:"/test/jeecgOrderMain/edit",orderCustomerList:"/test/jeecgOrderMain/queryOrderCustomerListByMainId",orderTicketList:"/test/jeecgOrderMain/queryOrderTicketListByMainId"}}},created:function(){},methods:{getAllTable:function(){return Promise.all([Object(l["c"])(this,"editableTable1"),Object(l["c"])(this,"editableTable2")])},add:function(){this.getAllTable().then((function(e){e[0].add(),e[1].add()})),this.edit({})},edit:function(e){if(this.visible=!0,this.activeKey="1",this.model=Object.assign({},e),this.model.id){var t={id:this.model.id};this.requestTableData(this.url.orderCustomerList,t,this.table1),this.requestTableData(this.url.orderTicketList,t,this.table2)}},close:function(){this.visible=!1,this.getAllTable().then((function(e){e[0].initialize(),e[1].initialize()})),this.$emit("close"),this.$refs.form.resetFields()},requestTableData:function(e,t,a){a.loading=!0,Object(s["c"])(e,t).then((function(e){a.dataSource=e.result||[]})).finally((function(){a.loading=!1}))},handleOk:function(){this.validateFields()},handleCancel:function(){this.close()},handleChangeTabs:function(e){Object(l["c"])(this,"editableTable".concat(e)).then((function(e){e.resetScrollTop()}))},validateFields:function(){var e=this;this.getAllTable().then((function(t){return Object(l["d"])(e.$refs.form,e.model,t)})).then((function(t){var a=e.classifyIntoFormData(t);return e.requestAddOrEdit(a)})).catch((function(t){t.error===l["b"]&&(e.activeKey=null==t.index?e.activeKey:(t.index+1).toString())}))},classifyIntoFormData:function(e){var t=Object.assign(this.model,e.formValue);return d(d({},t),{},{jeecgOrderCustomerList:e.tablesValue[0].values,jeecgOrderTicketList:e.tablesValue[1].values})},requestAddOrEdit:function(e){var t=this,a=this.url.add,r="post";this.model.id&&(a=this.url.edit,r="put"),this.confirmLoading=!0,Object(s["h"])(a,e,r).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok"),t.close()):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}},p=m,f=a("2877"),h=Object(f["a"])(p,r,n,!1,null,"675e8eb2",null);t["default"]=h.exports},b8f4:function(e,t,a){"use strict";var r=a("6310"),n=a.n(r);n.a},b931:function(e,t,a){},be66:function(e,t,a){"use strict";var r=a("b05d"),n=a.n(r);n.a},c3e2:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-skeleton",{attrs:{active:"",loading:e.loading,paragraph:{rows:17}}},[a("a-card",{attrs:{bordered:!1}},[a("a-alert",{attrs:{type:"info",showIcon:!0}},[a("div",{attrs:{slot:"message"},slot:"message"},[e._v("\n        上次更新时间："+e._s(this.time)+"\n        "),a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:e.handleClickUpdate}},[e._v("立即更新")])],1)]),a("a-table",{staticStyle:{"margin-top":"20px"},attrs:{rowKey:"id",size:"middle",columns:e.columns,dataSource:e.dataSource,pagination:!1,loading:e.tableLoading},scopedSlots:e._u([{key:"param",fn:function(t,r){return[a("a-tag",{attrs:{color:e.textInfo[r.param].color}},[e._v(e._s(t))])]}},{key:"text",fn:function(t,a){return[e._v("\n        "+e._s(e.textInfo[a.param].text)+"\n      ")]}},{key:"value",fn:function(t,a){return[e._v("\n        "+e._s(t)+" "+e._s(e.textInfo[a.param].unit)+"\n      ")]}}])})],1)],1)},n=[],o=a("c1df"),l=a.n(o),s=a("0fea");l.a.locale("zh-cn");var i={data:function(){return{time:"",loading:!0,tableLoading:!0,columns:[{title:"参数",width:"30%",dataIndex:"param",scopedSlots:{customRender:"param"}},{title:"描述",width:"40%",dataIndex:"text",scopedSlots:{customRender:"text"}},{title:"当前值",width:"30%",dataIndex:"value",scopedSlots:{customRender:"value"}}],dataSource:[],textInfo:{"tomcat.sessions.created":{color:"green",text:"tomcat 已创建 session 数",unit:"个"},"tomcat.sessions.expired":{color:"green",text:"tomcat 已过期 session 数",unit:"个"},"tomcat.sessions.active.current":{color:"green",text:"tomcat 当前活跃 session 数",unit:"个"},"tomcat.sessions.active.max":{color:"green",text:"tomcat 活跃 session 数峰值",unit:"个"},"tomcat.sessions.rejected":{color:"green",text:"超过session 最大配置后，拒绝的 session 个数",unit:"个"},"tomcat.global.sent":{color:"purple",text:"发送的字节数",unit:"bytes"},"tomcat.global.request.max":{color:"purple",text:"request 请求最长耗时",unit:"秒"},"tomcat.global.request.count":{color:"purple",text:"全局 request 请求次数",unit:"次"},"tomcat.global.request.totalTime":{color:"purple",text:"全局 request 请求总耗时",unit:"秒"},"tomcat.servlet.request.max":{color:"cyan",text:"servlet 请求最长耗时",unit:"秒"},"tomcat.servlet.request.count":{color:"cyan",text:"servlet 总请求次数",unit:"次"},"tomcat.servlet.request.totalTime":{color:"cyan",text:"servlet 请求总耗时",unit:"秒"},"tomcat.threads.current":{color:"pink",text:"tomcat 当前线程数（包括守护线程）",unit:"个"},"tomcat.threads.config.max":{color:"pink",text:"tomcat 配置的线程最大数",unit:"个"}},moreInfo:{"tomcat.global.request":[".count",".totalTime"],"tomcat.servlet.request":[".count",".totalTime"]}}},mounted:function(){this.loadTomcatInfo()},methods:{handleClickUpdate:function(){this.loadTomcatInfo()},loadTomcatInfo:function(){var e=this;this.tableLoading=!0,this.time=l()().format("YYYY年MM月DD日 HH时mm分ss秒"),Promise.all([Object(s["c"])("actuator/metrics/tomcat.sessions.created"),Object(s["c"])("actuator/metrics/tomcat.sessions.expired"),Object(s["c"])("actuator/metrics/tomcat.sessions.active.current"),Object(s["c"])("actuator/metrics/tomcat.sessions.active.max"),Object(s["c"])("actuator/metrics/tomcat.sessions.rejected")]).then((function(t){var a=[];t.forEach((function(t,r){var n=e.moreInfo[t.name];n instanceof Array||(n=[""]),n.forEach((function(e,n){var o=t.name+e;a.push({id:o+r,param:o,text:"false value",value:t.measurements[n].value})}))})),e.dataSource=a})).catch((function(t){e.$message.error("获取Tomcat信息失败")})).finally((function(){e.loading=!1,e.tableLoading=!1}))}}},c=i,d=a("2877"),u=Object(d["a"])(c,r,n,!1,null,null,null);t["default"]=u.exports},c55c:function(e,t,a){},c683:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("a-spin",{attrs:{spinning:e.loading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.rules}},[a("a-tabs",[a("a-tab-pane",{key:"1",attrs:{tab:"消息选项"}},[a("a-form-model-item",{attrs:{label:"测试APP",prop:"app",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{attrs:{placeholder:"请选择测试APP",options:e.appOptions},model:{value:e.model.app,callback:function(t){e.$set(e.model,"app",t)},expression:"model.app"}})],1),a("a-form-model-item",{attrs:{label:"发送给所有人",prop:"sendAll",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"},on:{change:e.onSendAllChange},model:{value:e.model.sendAll,callback:function(t){e.$set(e.model,"sendAll",t)},expression:"model.sendAll"}})],1),a("a-form-model-item",{attrs:{label:"接收人",prop:"receiver",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-select-multi-user",{attrs:{disabled:e.model.sendAll,placeholder:"请选择接收人"},model:{value:e.model.receiver,callback:function(t){e.$set(e.model,"receiver",t)},expression:"model.receiver"}})],1),a("a-form-model-item",{attrs:{label:"消息内容",prop:"content",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{attrs:{rows:5,placeholder:"请输入消息内容"},model:{value:e.model.content,callback:function(t){e.$set(e.model,"content",t)},expression:"model.content"}})],1),a("div",{staticStyle:{"text-align":"center"}},[a("a-button",{staticStyle:{width:"120px"},attrs:{type:"primary",size:"large"},on:{click:e.onSend}},[e._v("发送")])],1)],1)],1),a("a-tabs",[a("a-tab-pane",{key:"1",attrs:{tab:"测试结果（刷新自动清空）"}},[a("a-table",{attrs:{rowKey:"id",bordered:"",size:"middle",columns:e.columns,dataSource:e.dataSource},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("div",{},["DINGTALK"===r.app?[r.recalled?a("span",[e._v("已撤回")]):a("a-popconfirm",{attrs:{title:"确定吗？"},on:{confirm:function(t){return e.handleRecall(r)}}},[a("a",{on:{click:function(e){}}},[e._v("撤回")])])]:[e._v("-")]],2)}}])})],1)],1)],1)],1)],1)},n=[],o=a("a34a"),l=a.n(o),s=a("0d34"),i=a("0fea"),c=a("ca00");function d(e,t,a,r,n,o,l){try{var s=e[o](l),i=s.value}catch(c){return void a(c)}s.done?t(i):Promise.resolve(i).then(r,n)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(r,n){var o=e.apply(t,a);function l(e){d(o,r,n,l,s,"next",e)}function s(e){d(o,r,n,l,s,"throw",e)}l(void 0)}))}}var m={name:"ThirdAppMessageTest",data:function(){var e=this;return{loading:!1,labelCol:{span:6},wrapperCol:{span:12},model:{sendAll:!1},enabledTypes:{},columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(t,a,r){return e.dataSource.length-r}},{title:"测试APP",align:"center",dataIndex:"app",customRender:function(e,t,a){return"WECHAT_ENTERPRISE"===e?"企业微信":"DINGTALK"===e?"钉钉":e}},{title:"接收人",align:"center",dataIndex:"receiver",customRender:function(e,t,a){return t.sendAll?"【全体】":e}},{title:"消息内容",align:"center",dataIndex:"content"},{title:"response",align:"center",dataIndex:"response"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:80,scopedSlots:{customRender:"action"}}],dataSource:[]}},computed:{rules:function(){return{app:[{required:!0,message:"请选择测试APP"}],url:[{required:this.show,message:"请输入菜单路径!"}],receiver:[{required:!this.model.sendAll,message:"请选择接收人"}],content:[{required:!0,message:"消息内容不能为空"}]}},appOptions:function(){return[{label:"企业微信".concat(this.enabledTypes.wechatEnterprise?"":"（已禁用）"),value:"WECHAT_ENTERPRISE",disabled:!this.enabledTypes.wechatEnterprise},{label:"钉钉".concat(this.enabledTypes.dingtalk?"":"（已禁用）"),value:"DINGTALK",disabled:!this.enabledTypes.dingtalk}]}},created:function(){this.loadEnabledTypes()},methods:{loadEnabledTypes:function(){var e=u(l.a.mark((function e(){return l.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(s["loadEnabledTypes"])();case 2:this.enabledTypes=e.sent;case 3:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),onSendAllChange:function(){this.$refs.form.clearValidate("receiver")},onSend:function(){var e=this;this.$refs.form.validate((function(t,a){t&&(e.loading=!0,Object(i["i"])("/sys/thirdApp/sendMessageTest",e.model).then((function(t){var a=t.success,r=t.result,n=t.message;if(a){var o="";try{o=JSON.stringify(r)}catch(l){o=r}e.dataSource.unshift(Object.assign({id:Object(c["m"])()},e.model,{response:o}))}else e.$message.warning(n)})).finally((function(){return e.loading=!1})))}))},handleRecall:function(e){var t=this;try{var a=JSON.parse(e.response);Object(i["i"])("/sys/thirdApp/recallMessageTest",{app:e.app,msg_task_id:a.result}).then((function(a){a.success?(t.$set(e,"recalled",!0),t.$message.success(a.message)):t.$message.warning(a.message)})).catch((function(e){return t.$message.warning(e.message||e)}))}catch(r){this.$message.warning(r.message||r)}}}},p=m,f=a("2877"),h=Object(f["a"])(p,r,n,!1,null,"6672cbf1",null);t["default"]=h.exports},c943:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{"label-col":e.labelCol,"wrapper-col":e.wrapperCol,model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{label:"姓名",required:"",prop:"name",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入姓名"},model:{value:e.model.name,callback:function(t){e.$set(e.model,"name",t)},expression:"model.name"}})],1),a("a-form-model-item",{attrs:{label:"关键词",prop:"keyWord",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入关键词"},model:{value:e.model.keyWord,callback:function(t){e.$set(e.model,"keyWord",t)},expression:"model.keyWord"}})],1),a("a-form-model-item",{attrs:{label:"打卡时间",prop:"punchTime",hasFeedback:""}},[a("a-date-picker",{attrs:{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss"},model:{value:e.model.punchTime,callback:function(t){e.$set(e.model,"punchTime",t)},expression:"model.punchTime"}})],1),a("a-form-model-item",{attrs:{label:"性别",prop:"sex",hasFeedback:""}},[a("j-dict-select-tag",{attrs:{type:"radio","trigger-change":!0,dictCode:"sex"},model:{value:e.model.sex,callback:function(t){e.$set(e.model,"sex",t)},expression:"model.sex"}})],1),a("a-form-model-item",{attrs:{label:"年龄",prop:"age",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入年龄"},model:{value:e.model.age,callback:function(t){e.$set(e.model,"age",t)},expression:"model.age"}})],1),a("a-form-model-item",{attrs:{label:"生日",prop:"age",hasFeedback:""}},[a("a-date-picker",{attrs:{valueFormat:"YYYY-MM-DD"},model:{value:e.model.birthday,callback:function(t){e.$set(e.model,"birthday",t)},expression:"model.birthday"}})],1),a("a-form-model-item",{attrs:{label:"邮箱",prop:"email",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入邮箱"},model:{value:e.model.email,callback:function(t){e.$set(e.model,"email",t)},expression:"model.email"}})],1),a("a-form-model-item",{attrs:{label:"个人简介",prop:"content",hasFeedback:""}},[a("a-input",{attrs:{type:"textarea",placeholder:"请输入个人简介"},model:{value:e.model.content,callback:function(t){e.$set(e.model,"content",t)},expression:"model.content"}})],1)],1)],1)],1)},n=[],o=a("0fea"),l={name:"JeecgDemoModal",data:function(){return{title:"操作",visible:!1,model:{},layout:{labelCol:{span:3},wrapperCol:{span:14}},labelCol:{xs:{span:24},sm:{span:3}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{name:[{required:!0,message:"请输入姓名!"},{min:2,max:30,message:"长度在 2 到 30 个字符",trigger:"blur"}],email:[{required:!1,type:"email",message:"邮箱格式不正确",trigger:"blur"}]},url:{add:"/test/jeecgDemo/add",edit:"/test/jeecgDemo/edit"}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){this.model=Object.assign({},e),this.visible=!0},close:function(){this.$refs.form.resetFields(),this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",n="";e.model.id?(r+=e.url.edit,n="put"):(r+=e.url.add,n="post"),Object(o["h"])(r,e.model,n).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}}))},handleCancel:function(){this.close()}}},s=l,i=a("2877"),c=Object(i["a"])(s,r,n,!1,null,"64dcf271",null);t["default"]=c.exports},c997:function(e,t,a){},d18d:function(e,t,a){"use strict";var r=a("66e5"),n=a.n(r);n.a},d25c:function(e,t,a){"use strict";var r=a("60f0"),n=a.n(r);n.a},d544:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-operator",staticStyle:{margin:"-25px 0px 10px 0px"},attrs:{md:24,sm:24}},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("\n          删除\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("\n            更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{attrs:{href:"javascript:;"},on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("jeecgOrderCustomer-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},n=[],o=a("39a5"),l=a("6620"),s=a("b65a"),i=a("0fea"),c={name:"JeecgOrderCustomerList",mixins:[s["a"]],components:{JeecgOrderDMainList:l["default"],JeecgOrderCustomerModal:o["default"]},data:function(){return{description:"订单客户信息",columns:[{title:"客户名",align:"center",width:100,dataIndex:"name",key:"name"},{title:"性别",align:"center",dataIndex:"sex",customRender:function(e){return 1==e?"男":2==e?"女":e}},{title:"身份证号码",align:"center",dataIndex:"idcard"},{title:"电话",dataIndex:"telphone",align:"center"},{title:"操作",key:"operation",align:"center",width:130,scopedSlots:{customRender:"action"}}],url:{list:"/test/order/listOrderCustomerByMainId",delete:"/test/order/deleteCustomer",deleteBatch:"/test/order/deleteBatchCustomer"}}},methods:{loadData:function(e){var t=this;1===e&&(this.ipagination.current=1);var a=this.getQueryParams();Object(i["c"])(this.url.list,{orderId:a.mainId,pageNo:this.ipagination.current,pageSize:this.ipagination.pageSize}).then((function(e){e.success?(t.dataSource=e.result.records,t.ipagination.total=e.result.total):t.dataSource=null}))},getOrderMain:function(e){this.queryParam.mainId=e,this.loadData(1)},handleAdd:function(){this.$refs.modalForm.add(this.queryParam.mainId),this.$refs.modalForm.title="添加客户信息"}}},d=c,u=(a("d18d"),a("2877")),m=Object(u["a"])(d,r,n,!1,null,"032e064e",null);t["default"]=m.exports},da7e:function(e,t,a){"use strict";var r=a("dec7"),n=a.n(r);n.a},dd9d:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:1e3,visible:e.visible,confirmLoading:e.confirmLoading},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.orderMainModel,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"orderCode",label:"订单号",required:"",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.orderMainModel.orderCode,callback:function(t){e.$set(e.orderMainModel,"orderCode",t)},expression:"orderMainModel.orderCode"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"订单类型"}},[a("a-select",{attrs:{placeholder:"请输入订单类型"},model:{value:e.orderMainModel.ctype,callback:function(t){e.$set(e.orderMainModel,"ctype",t)},expression:"orderMainModel.ctype"}},[a("a-select-option",{attrs:{value:"1"}},[e._v("国内订单")]),a("a-select-option",{attrs:{value:"2"}},[e._v("国际订单")])],1)],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"订单日期"}},[a("a-date-picker",{attrs:{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss"},model:{value:e.orderMainModel.orderDate,callback:function(t){e.$set(e.orderMainModel,"orderDate",t)},expression:"orderMainModel.orderDate"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"订单金额"}},[a("a-input-number",{staticStyle:{width:"200px"},model:{value:e.orderMainModel.orderMoney,callback:function(t){e.$set(e.orderMainModel,"orderMoney",t)},expression:"orderMainModel.orderMoney"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"订单备注"}},[a("a-input",{attrs:{placeholder:"请输入订单备注"},model:{value:e.orderMainModel.content,callback:function(t){e.$set(e.orderMainModel,"content",t)},expression:"orderMainModel.content"}})],1)],1)],1)],1)},n=[],o=a("0fea"),l=a("2dab"),s=(a("88bc"),a("c1df"),{name:"JeecgOrderDMainModal",components:{JDate:l["default"]},data:function(){return{title:"操作",visible:!1,orderMainModel:{jeecgOrderCustomerList:[{}],jeecgOrderTicketList:[{}]},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{orderCode:[{required:!0,message:"请输入订单号!"}]},url:{add:"/test/order/add",edit:"/test/order/edit",orderCustomerList:"/test/order/listOrderCustomerByMainId",orderTicketList:"/test/order/listOrderTicketByMainId"}}},methods:{add:function(){this.edit({})},edit:function(e){this.orderMainModel=Object.assign({},e),this.visible=!0},close:function(){this.$emit("close"),this.visible=!1,this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0;var r="",n="";e.orderMainModel.id?(r+=e.url.edit,n="put"):(r+=e.url.add,n="post"),Object(o["h"])(r,e.orderMainModel,n).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()}}}),i=s,c=(a("a8c3"),a("2877")),d=Object(c["a"])(i,r,n,!1,null,"2de11e5c",null);t["default"]=d.exports},dec7:function(e,t,a){},e5ab:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-row",{attrs:{gutter:{xs:8,sm:16,md:24,lg:32}}},[a("a-col",{attrs:{span:12}},[a("a-form-item",{staticStyle:{"margin-right":"-35px"},attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"模板CODE"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["templateCode",e.validatorRules.templateCode],expression:"['templateCode', validatorRules.templateCode ]"}],attrs:{disabled:e.disable,placeholder:"请输入模板编码"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"模板类型"}},[a("j-dict-select-tag",{directives:[{name:"decorator",rawName:"v-decorator",value:["templateType",e.validatorRules.templateType],expression:"['templateType', validatorRules.templateType ]"}],attrs:{triggerChange:!0,dictCode:"msgType",placeholder:"请选择模板类型"},on:{change:e.handleChangeTemplateType}})],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:24}},[a("a-col",{attrs:{span:24,pull:"2"}},[a("a-form-item",{staticStyle:{"margin-left":"-15px"},attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"模板标题"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["templateName",e.validatorRules.templateName],expression:"['templateName', validatorRules.templateName]"}],staticStyle:{width:"122%"},attrs:{placeholder:"请输入模板标题"}})],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:24}},[a("a-col",{attrs:{span:24,pull:"4"}},[a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:!e.useEditor,expression:"!useEditor"}],staticStyle:{"margin-left":"4px",width:"126%"},attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"模板内容"}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["templateContent",e.validatorRules.templateContent],expression:"['templateContent', validatorRules.templateContent ]"}],attrs:{placeholder:"请输入模板内容",autosize:{minRows:8,maxRows:8}}})],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:24}},[a("a-col",{attrs:{span:24,pull:"4"}},[a("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.useEditor,expression:"useEditor"}],staticStyle:{"margin-left":"4px",width:"126%"},attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"模板内容"}},[a("j-editor",{model:{value:e.templateEditorContent,callback:function(t){e.templateEditorContent=t},expression:"templateEditorContent"}})],1)],1)],1)],1)],1)],1)},n=[],o=a("0fea"),l=a("88bc"),s=a.n(l),i=a("4ec3"),c=a("a061"),d={name:"SysMessageTemplateModal",components:{JEditor:c["default"]},data:function(){return{title:"操作",visible:!1,disable:!0,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{templateCode:{rules:[{required:!0,message:"请输入模板CODE!"},{validator:this.validateTemplateCode}]},templateName:{rules:[{required:!0,message:"请输入模板标题!"}]},templateContent:{rules:[]},templateType:{rules:[{required:!0,message:"请输入模板类型!"}]}},url:{add:"/sys/message/sysMessageTemplate/add",edit:"/sys/message/sysMessageTemplate/edit"},useEditor:!1,templateEditorContent:""}},created:function(){},methods:{add:function(){this.disable=!1,this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.useEditor=2==e.templateType||4==e.templateType,this.useEditor?this.templateEditorContent=e.templateContent:this.templateEditorContent="",this.visible=!0,this.$nextTick((function(){t.useEditor?t.form.setFieldsValue(s()(t.model,"templateCode","templateName","templateTestJson","templateType")):t.form.setFieldsValue(s()(t.model,"templateCode","templateContent","templateName","templateTestJson","templateType"))}))},close:function(){this.$emit("close"),this.visible=!1,this.disable=!0},handleOk:function(){var e=this;this.model.templateType=this.templateType;var t=this;this.form.validateFields((function(a,r){if(!a){t.confirmLoading=!0;var n="",l="";e.model.id?(n+=e.url.edit,l="put"):(n+=e.url.add,l="post");var s=Object.assign(e.model,r);e.useEditor&&(s.templateContent=e.templateEditorContent),Object(o["h"])(n,s,l).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}}))},validateTemplateCode:function(e,t,a){var r={tableName:"sys_sms_template",fieldName:"template_code",fieldVal:t,dataId:this.model.id};Object(i["m"])(r).then((function(e){e.success?a():a(e.message)}))},handleCancel:function(){this.close()},handleChangeTemplateType:function(e){this.useEditor=2==e||4==e}}},u=d,m=a("2877"),p=Object(m["a"])(u,r,n,!1,null,"45efade7",null);t["default"]=p.exports},ebfa:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-skeleton",{attrs:{active:"",loading:e.loading,paragraph:{rows:17}}},[a("a-card",[a("a-row",{attrs:{gutter:8}},[a("a-col",{attrs:{sm:24,xl:12}},[a("area-chart-ty",e._b({},"area-chart-ty",e.memory,!1))],1),a("a-col",{attrs:{sm:24,xl:12}},[a("area-chart-ty",e._b({},"area-chart-ty",e.key,!1))],1)],1),a("h3",[e._v("Redis 详细信息")]),a("a-table",{attrs:{loading:e.tableLoading,columns:e.columns,dataSource:e.redisInfo,pagination:!1}})],1)],1)},n=[],o=a("c1df"),l=a.n(o),s=a("0fea"),i=a("f552");function c(e,t){return f(e)||p(e,t)||u(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"===typeof e)return m(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function p(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,n=!1,o=void 0;try{for(var l,s=e[Symbol.iterator]();!(r=(l=s.next()).done);r=!0)if(a.push(l.value),t&&a.length===t)break}catch(i){n=!0,o=i}finally{try{r||null==s["return"]||s["return"]()}finally{if(n)throw o}}return a}}function f(e){if(Array.isArray(e))return e}var h={name:"RedisInfo",components:{AreaChartTy:i["default"]},data:function(){return{loading:!0,tableLoading:!0,timer:null,millisec:3e3,key:{title:"Radis Key 实时数量（个）",dataSource:[],y:"数量（个）",height:340,min:0,max:100,color:"#FF6987",lineSize:8,lineColor:"#DC143C"},memory:{title:"Radis 内存实时占用情况（KB）",dataSource:[],y:"内存（KB）",min:0,max:3e3,height:340,lineSize:8},redisInfo:[],columns:[{title:"Key",align:"center",dataIndex:"key"},{title:"Description",align:"left",dataIndex:"description"},{title:"Value",align:"center",dataIndex:"value"}],url:{keysSize:"/sys/actuator/redis/keysSize",memoryInfo:"/sys/actuator/redis/memoryInfo",info:"/sys/actuator/redis/info"},path:"/monitor/redis/info"}},mounted:function(){var e=this;this.openTimer(),this.loadRedisInfo(),setTimeout((function(){e.loadData()}),1e3)},beforeDestroy:function(){this.closeTimer()},methods:{openTimer:function(){var e=this;this.loadData(),this.closeTimer(),this.timer=setInterval((function(){e.$route.path===e.path&&e.loadData()}),this.millisec)},closeTimer:function(){this.timer&&clearInterval(this.timer)},loadData:function(){var e=this;Promise.all([Object(s["c"])(this.url.keysSize),Object(s["c"])(this.url.memoryInfo)]).then((function(t){var a=l()().format("hh:mm:ss"),r=c(t,2),n=r[0].dbSize,o=r[1],s=o.used_memory/1e3;e.key.dataSource.push({x:a,y:n}),e.memory.dataSource.push({x:a,y:s}),e.key.dataSource.length>6&&(e.key.dataSource.splice(0,1),e.memory.dataSource.splice(0,1));var i=e.getMaxAndMin(e.key.dataSource,"y");e.key.max=Math.floor(i[0])+10,e.key.min=Math.floor(i[1])-10,e.key.min<0&&(e.key.min=0);var d=e.getMaxAndMin(e.memory.dataSource,"y");e.memory.max=Math.floor(d[0])+100,e.memory.min=Math.floor(d[1])-100,e.memory.min<0&&(e.memory.min=0)})).catch((function(t){e.closeTimer(),e.$message.error("获取 Redis 信息失败")})).finally((function(){e.loading=!1}))},getMaxAndMin:function(e,t){var a=null,r=null;return e.forEach((function(e){var n=Number.parseInt(e[t]);(null==a||n>a)&&(a=n),(null==r||n<r)&&(r=n)})),[a,r]},loadRedisInfo:function(){var e=this;this.tableLoading=!0,Object(s["c"])(this.url.info).then((function(t){e.redisInfo=t.result})).finally((function(){e.tableLoading=!1}))}}},b=h,v=a("2877"),g=Object(v["a"])(b,r,n,!1,null,null,null);t["default"]=g.exports},f534:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:"高级查询构造器",width:800,visible:e.visible,confirmLoading:e.confirmLoading,mask:!1,okText:"查询",cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",[a("div",e._l(e.queryParamsModel,(function(t,r){return a("a-row",{key:r,staticStyle:{"margin-bottom":"10px"},attrs:{type:"flex",gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-select",{attrs:{placeholder:"选择查询字段"},model:{value:t.field,callback:function(a){e.$set(t,"field",a)},expression:"item.field"}},[a("a-select-option",{attrs:{value:"name"}},[e._v("用户名")]),a("a-select-option",{attrs:{value:"key_word"}},[e._v("关键词")]),a("a-select-option",{attrs:{value:"birthday"}},[e._v("生日")]),a("a-select-option",{attrs:{value:"age"}},[e._v("年龄")])],1)],1),a("a-col",{attrs:{span:6}},[a("a-select",{attrs:{placeholder:"选择匹配规则"},model:{value:t.rule,callback:function(a){e.$set(t,"rule",a)},expression:"item.rule"}},[a("a-select-option",{attrs:{value:"="}},[e._v("等于")]),a("a-select-option",{attrs:{value:"!="}},[e._v("不等于")]),a("a-select-option",{attrs:{value:">"}},[e._v("大于")]),a("a-select-option",{attrs:{value:">="}},[e._v("大于等于")]),a("a-select-option",{attrs:{value:"<"}},[e._v("小于")]),a("a-select-option",{attrs:{value:"<="}},[e._v("小于等于")]),a("a-select-option",{attrs:{value:"LEFT_LIKE"}},[e._v("以..开始")]),a("a-select-option",{attrs:{value:"RIGHT_LIKE"}},[e._v("以..结尾")]),a("a-select-option",{attrs:{value:"LIKE"}},[e._v("包含")]),a("a-select-option",{attrs:{value:"IN"}},[e._v("在...中")])],1)],1),a("a-col",{attrs:{span:6}},[a("a-input",{attrs:{placeholder:"请输入值"},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"item.val"}})],1),a("a-col",{attrs:{span:6}},[a("a-button",{attrs:{icon:"plus"},on:{click:e.handleAdd}}),e._v(" \n            "),a("a-button",{attrs:{icon:"minus"},on:{click:function(t){return e.handleDel(r)}}})],1)],1)})),1)])],1)],1)},n=[],o=(a("0fea"),{name:"SuperQueryModal",data:function(){return{visible:!1,queryParamsModel:[{},{}],confirmLoading:!1}},created:function(){},methods:{show:function(){this.visible=!0},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$emit("handleSuperQuery",this.queryParamsModel)},handleCancel:function(){this.close()},handleAdd:function(){this.queryParamsModel.push({})},handleDel:function(e){this.queryParamsModel.splice(e,1)}}}),l=o,s=a("2877"),i=Object(s["a"])(l,r,n,!1,null,"7800fec9",null);t["default"]=i.exports},facd9:function(e,t,a){"use strict";var r=a("add4"),n=a.n(r);n.a},fd15:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{md:6,sm:8}},[a("a-form-item",{attrs:{label:"模板CODE"}},[a("a-input",{attrs:{placeholder:"请输入模板CODE"},model:{value:e.queryParam.templateCode,callback:function(t){e.$set(e.queryParam,"templateCode",t)},expression:"queryParam.templateCode"}})],1)],1),a("a-col",{attrs:{md:6,sm:8}},[a("a-form-item",{attrs:{label:"模板内容"}},[a("a-input",{attrs:{placeholder:"请输入模板内容"},model:{value:e.queryParam.templateContent,callback:function(t){e.$set(e.queryParam,"templateContent",t)},expression:"queryParam.templateContent"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{md:6,sm:8}},[a("a-form-item",{attrs:{label:"模板标题"}},[a("a-input",{attrs:{placeholder:"请输入模板标题"},model:{value:e.queryParam.templateName,callback:function(t){e.$set(e.queryParam,"templateName",t)},expression:"queryParam.templateName"}})],1)],1),a("a-col",{attrs:{md:6,sm:8}},[a("a-form-item",{attrs:{label:"模板类型"}},[a("a-input",{attrs:{placeholder:"请输入模板类型"},model:{value:e.queryParam.templateType,callback:function(t){e.$set(e.queryParam,"templateType",t)},expression:"queryParam.templateType"}})],1)],1)]:e._e(),a("a-col",{attrs:{md:6,sm:8}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("消息模板")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("\n          删除\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"templateContent",fn:function(e){return a("span",{},[a("j-ellipsis",{attrs:{value:e,length:25}})],1)}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1),a("a-menu-item",[a("a",{on:{click:function(t){return e.handleTest(r)}}},[e._v("发送测试")])])],1)],1)],1)}}])})],1),a("sysMessageTemplate-modal",{ref:"modalForm",on:{ok:e.modalFormOk}}),a("sysMessageTest-modal",{ref:"testModal"})],1)},n=[],o=a("e5ab"),l=a("86a6"),s=a("b65a"),i=a("d579"),c={name:"SysMessageTemplateList",mixins:[s["a"]],components:{JEllipsis:i["default"],SysMessageTemplateModal:o["default"],SysMessageTestModal:l["default"]},data:function(){return{description:"消息模板管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"模板CODE",align:"center",dataIndex:"templateCode"},{title:"模板标题",align:"center",dataIndex:"templateName"},{title:"模板内容",align:"center",dataIndex:"templateContent",scopedSlots:{customRender:"templateContent"}},{title:"模板类型",align:"center",dataIndex:"templateType",customRender:function(e){return"1"==e?"短信":"2"==e?"邮件":"3"==e?"微信":"4"==e?"系统":void 0}},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{customRender:"action"}}],url:{list:"/sys/message/sysMessageTemplate/list",delete:"/sys/message/sysMessageTemplate/delete",deleteBatch:"/sys/message/sysMessageTemplate/deleteBatch",exportXlsUrl:"sys/message/sysMessageTemplate/exportXls",importExcelUrl:"sys/message/sysMessageTemplate/importExcel"}}},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{handleTest:function(e){this.$refs.testModal.open(e),this.$refs.testModal.title="发送测试"}}},d=c,u=(a("a11f"),a("2877")),m=Object(u["a"])(d,r,n,!1,null,"1de4bd96",null);t["default"]=m.exports},ff3c:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("a-button",{attrs:{type:"primary"},on:{click:e.handleTableCheck}},[e._v("表单验证")]),a("span",{staticStyle:{"padding-left":"8px"}}),a("a-tooltip",{attrs:{placement:"top",title:"获取值，忽略表单验证",autoAdjustOverflow:!0}},[a("a-button",{attrs:{type:"primary"},on:{click:e.handleTableGet}},[e._v("获取值")])],1),a("span",{staticStyle:{"padding-left":"8px"}}),a("a-tooltip",{attrs:{placement:"top",title:"模拟加载1000条数据",autoAdjustOverflow:!0}},[a("a-button",{attrs:{type:"primary"},on:{click:e.handleTableSet}},[e._v("设置值")])],1),a("j-editable-table",{ref:"editableTable",staticStyle:{"margin-top":"8px"},attrs:{loading:e.loading,columns:e.columns,dataSource:e.dataSource,rowNumber:!0,rowSelection:!0,actionButton:!0,dragSort:!0},on:{selectRowChange:e.handleSelectRowChange},scopedSlots:e._u([{key:"action",fn:function(t){return[a("a",{on:{click:function(a){return e.handleDelete(t)}}},[e._v("删除")])]}}])})],1)},n=[],o=a("c1df"),l=a.n(o),s=a("e2e0"),i=a("ca00"),c=a("7550"),d={name:"DefaultTable",components:{JEditableTable:c["default"]},data:function(){return{loading:!1,columns:[{title:"字段名称",key:"dbFieldName",width:"300px",type:s["a"].input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"请输入${title}"},{pattern:/^[a-z|A-Z][a-z|A-Z\d_-]{0,}$/,message:"${title}必须以字母开头，可包含数字、下划线、横杠"},{unique:!0,message:"${title}不能重复"},{handler:function(e,t,a,r,n,o){"blur"===e&&"abc"===t?n(!1,"${title}不能是abc"):n(!0)},message:"${title}默认提示"}]},{title:"文件域",key:"upload",type:s["a"].upload,width:"300px",placeholder:"点击上传",token:!0,responseName:"message",action:window._CONFIG["domianURL"]+"/sys/common/upload",data:{biz:"temp"}},{title:"字段类型",key:"dbFieldType",width:"300px",type:s["a"].select,options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}],allowInput:!0,defaultValue:"",placeholder:"请选择${title}",validateRules:[{required:!0,message:"请选择${title}"}]},{title:"性别（字典）",key:"sex_dict",width:"300px",type:s["a"].select,options:[],dictCode:"sex",placeholder:"请选择${title}",validateRules:[{required:!0,message:"请选择${title}"}]},{title:"多选测试",key:"multipleSelect",width:"300px",type:s["a"].select,props:{mode:"multiple"},options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}],defaultValue:["int","boolean"],placeholder:"这里可以多选",validateRules:[{required:!0,message:"请选择${title}"}]},{title:"字段长度",key:"dbLength",width:"100px",type:s["a"].inputNumber,defaultValue:32,placeholder:"${title}",statistics:!0,validateRules:[{required:!0,message:"请输入${title}"}]},{title:"日期",key:"datetime",width:"320px",type:s["a"].datetime,defaultValue:"2019-4-30 14:52:22",placeholder:"请选择${title}",validateRules:[{required:!0,message:"请选择${title}"}]},{title:"数字",key:"money",width:"320px",type:s["a"].inputNumber,defaultValue:"100.32",placeholder:"请选择${title}",validateRules:[{required:!0,message:"请选择${title}"}]},{title:"可以为空",key:"isNull",width:"100px",type:s["a"].checkbox,customValue:["Y","N"],defaultChecked:!1},{type:s["a"].popup,key:"popup",title:"JPopup",width:"180px",popupCode:"demo",field:"name",orgFields:"name",destFields:"name"},{title:"操作",key:"action",width:"100px",type:s["a"].slot,slotName:"action"}],dataSource:[],selectedRowIds:[]}},mounted:function(){this.randomData(23,!1)},methods:{handleTableCheck:function(){var e=this;this.$refs.editableTable.getValues((function(t){0===t?e.$message.success("验证通过"):e.$message.error("验证未通过")}))},handleTableGet:function(){this.$refs.editableTable.getValues((function(e,t){}),!1),this.$message.info("获取值成功，请看控制台输出")},handleTableSet:function(){this.randomData(1e3,!0)},handleSelectRowChange:function(e){this.selectedRowIds=e},randomData:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];a&&(this.loading=!0);for(var r=function(){var e=parseInt(Object(i["k"])(1e3,9999999999999));return l()(new Date(e)).format("YYYY-MM-DD HH:mm:ss")},n=Date.now(),o=[],s=0;s<e;s++)o.push({id:Object(i["m"])(),dbFieldName:"name_".concat(s+1),multipleSelect:["string",["int","double","boolean"][Object(i["k"])(0,2)]],dbFieldType:["string","int","double","boolean"][Object(i["k"])(0,3)],dbLength:Object(i["k"])(0,233),datetime:r(),isNull:["Y","N"][Object(i["k"])(0,1)]});this.dataSource=o;var c=Date.now(),d=c-n;a&&d<e&&setTimeout((function(){t.loading=!1}),e-d)},handleDelete:function(e){var t=e.rowId,a=e.target;a.removeRows(t)}}},u=d,m=a("2877"),p=Object(m["a"])(u,r,n,!1,null,"7b1eae65",null);t["default"]=p.exports}}]);