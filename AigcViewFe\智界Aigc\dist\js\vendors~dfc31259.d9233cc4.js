(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~dfc31259"],{"2d33":function(e,n,t){t("303c")},"303c":function(e,n){(function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");function n(){e.add("colorpicker",(function(){}))}n()})()},3154:function(e,n,t){t("f034")},"4ea8":function(e,n,t){t("cdd9")},"5e4c":function(e,n){(function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");function n(){e.add("contextmenu",(function(){}))}n()})()},cdd9:function(e,n){(function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),n=function(){return n=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t],n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},n.apply(this,arguments)},t=function(e){var n=typeof e;return null===e?"null":"object"===n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n},r=function(e){return function(n){return t(n)===e}},o=function(e){return function(n){return typeof n===e}},i=function(e){return function(n){return e===n}},u=r("string"),a=r("object"),c=r("array"),s=i(null),l=o("boolean"),f=function(e){return null===e||void 0===e},m=function(e){return!f(e)},d=o("function"),g=o("number"),h=function(){},p=function(e){return function(){return e}},v=function(e){return e},b=p(!1),y=p(!0),w=function(){return S},S=function(){var e=function(e){return e()},n=v,t={fold:function(e,n){return e()},isSome:b,isNone:y,getOr:n,getOrThunk:e,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:p(null),getOrUndefined:p(void 0),or:n,orThunk:e,map:w,each:h,bind:w,exists:b,forall:y,filter:function(){return w()},toArray:function(){return[]},toString:p("none()")};return t}(),x=function(e){var n=p(e),t=function(){return o},r=function(n){return n(e)},o={fold:function(n,t){return t(e)},isSome:y,isNone:b,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:t,orThunk:t,map:function(n){return x(n(e))},each:function(n){n(e)},bind:r,exists:r,forall:r,filter:function(n){return n(e)?o:S},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return o},D=function(e){return null===e||void 0===e?S:x(e)},A={some:x,none:w,from:D},T=Object.keys,O=Object.hasOwnProperty,E=function(e,n){for(var t=T(e),r=0,o=t.length;r<o;r++){var i=t[r],u=e[i];n(u,i)}},C=function(e){return function(n,t){e[t]=n}},k=function(e,n,t,r){var o={};return E(e,(function(e,o){(n(e,o)?t:r)(e,o)})),o},N=function(e,n){var t={};return k(e,n,C(t),h),t},F=function(e,n){return O.call(e,n)},P=function(e,n){return F(e,n)&&void 0!==e[n]&&null!==e[n]},I=Array.prototype.push,L=function(e){for(var n=[],t=0,r=e.length;t<r;++t){if(!c(e[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+e);I.apply(n,e[t])}return n},R=function(e,n){return n>=0&&n<e.length?A.some(e[n]):A.none()},U=function(e){return R(e,0)},M=function(e,n){for(var t=0;t<e.length;t++){var r=n(e[t],t);if(r.isSome())return r}return A.none()};"undefined"!==typeof window?window:Function("return this;")();var _=function(e,n,t){if(!(u(t)||l(t)||g(t)))throw new Error("Attribute value was not simple");e.setAttribute(n,t+"")},z=function(e,n,t){_(e.dom,n,t)},H=function(e,n){e.dom.removeAttribute(n)},j=function(e,n){var t=n||document,r=t.createElement("div");if(r.innerHTML=e,!r.hasChildNodes()||r.childNodes.length>1)throw new Error("HTML must have a single root node");return q(r.childNodes[0])},B=function(e,n){var t=n||document,r=t.createElement(e);return q(r)},W=function(e,n){var t=n||document,r=t.createTextNode(e);return q(r)},q=function(e){if(null===e||void 0===e)throw new Error("Node cannot be null or undefined");return{dom:e}},G=function(e,n,t){return A.from(e.dom.elementFromPoint(n,t)).map(q)},V={fromHtml:j,fromTag:B,fromText:W,fromDom:q,fromPoint:G},X=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),$=tinymce.util.Tools.resolve("tinymce.util.Promise"),J=tinymce.util.Tools.resolve("tinymce.util.URI"),Y=tinymce.util.Tools.resolve("tinymce.util.XHR"),Z=function(e){return e.getParam("image_dimensions",!0,"boolean")},K=function(e){return e.getParam("image_advtab",!1,"boolean")},Q=function(e){return e.getParam("image_uploadtab",!0,"boolean")},ee=function(e){return e.getParam("image_prepend_url","","string")},ne=function(e){return e.getParam("image_class_list")},te=function(e){return e.getParam("image_description",!0,"boolean")},re=function(e){return e.getParam("image_title",!1,"boolean")},oe=function(e){return e.getParam("image_caption",!1,"boolean")},ie=function(e){return e.getParam("image_list",!1)},ue=function(e){return m(e.getParam("images_upload_url"))},ae=function(e){return m(e.getParam("images_upload_handler"))},ce=function(e){return e.getParam("a11y_advanced_options",!1,"boolean")},se=function(e){return e.getParam("automatic_uploads",!0,"boolean")},le=function(e,n){return Math.max(parseInt(e,10),parseInt(n,10))},fe=function(e){return new $((function(n){var t=document.createElement("img"),r=function(e){t.onload=t.onerror=null,t.parentNode&&t.parentNode.removeChild(t),n(e)};t.onload=function(){var e=le(t.width,t.clientWidth),n=le(t.height,t.clientHeight),o={width:e,height:n};r($.resolve(o))},t.onerror=function(){r($.reject("Failed to get image dimensions for: "+e))};var o=t.style;o.visibility="hidden",o.position="fixed",o.bottom=o.left="0px",o.width=o.height="auto",document.body.appendChild(t),t.src=e}))},me=function(e){return e&&(e=e.replace(/px$/,"")),e},de=function(e){return e.length>0&&/^[0-9]+$/.test(e)&&(e+="px"),e},ge=function(e){if(e.margin){var n=String(e.margin).split(" ");switch(n.length){case 1:e["margin-top"]=e["margin-top"]||n[0],e["margin-right"]=e["margin-right"]||n[0],e["margin-bottom"]=e["margin-bottom"]||n[0],e["margin-left"]=e["margin-left"]||n[0];break;case 2:e["margin-top"]=e["margin-top"]||n[0],e["margin-right"]=e["margin-right"]||n[1],e["margin-bottom"]=e["margin-bottom"]||n[0],e["margin-left"]=e["margin-left"]||n[1];break;case 3:e["margin-top"]=e["margin-top"]||n[0],e["margin-right"]=e["margin-right"]||n[1],e["margin-bottom"]=e["margin-bottom"]||n[2],e["margin-left"]=e["margin-left"]||n[1];break;case 4:e["margin-top"]=e["margin-top"]||n[0],e["margin-right"]=e["margin-right"]||n[1],e["margin-bottom"]=e["margin-bottom"]||n[2],e["margin-left"]=e["margin-left"]||n[3]}delete e.margin}return e},he=function(e,n){var t=ie(e);u(t)?Y.send({url:t,success:function(e){n(JSON.parse(e))}}):d(t)?t(n):n(t)},pe=function(e,n,t){var r=function(){t.onload=t.onerror=null,e.selection&&(e.selection.select(t),e.nodeChanged())};t.onload=function(){n.width||n.height||!Z(e)||e.dom.setAttribs(t,{width:String(t.clientWidth),height:String(t.clientHeight)}),r()},t.onerror=r},ve=function(e){return new $((function(n,t){var r=new FileReader;r.onload=function(){n(r.result)},r.onerror=function(){t(r.error.message)},r.readAsDataURL(e)}))},be=function(e){return"IMG"===e.nodeName&&(e.hasAttribute("data-mce-object")||e.hasAttribute("data-mce-placeholder"))},ye=function(e,n){return J.isDomSafe(n,"img",e.settings)},we=X.DOM,Se=function(e){return e.style.marginLeft&&e.style.marginRight&&e.style.marginLeft===e.style.marginRight?me(e.style.marginLeft):""},xe=function(e){return e.style.marginTop&&e.style.marginBottom&&e.style.marginTop===e.style.marginBottom?me(e.style.marginTop):""},De=function(e){return e.style.borderWidth?me(e.style.borderWidth):""},Ae=function(e,n){return e.hasAttribute(n)?e.getAttribute(n):""},Te=function(e,n){return e.style[n]?e.style[n]:""},Oe=function(e){return null!==e.parentNode&&"FIGURE"===e.parentNode.nodeName},Ee=function(e,n,t){""===t?e.removeAttribute(n):e.setAttribute(n,t)},Ce=function(e){var n=we.create("figure",{class:"image"});we.insertAfter(n,e),n.appendChild(e),n.appendChild(we.create("figcaption",{contentEditable:"true"},"Caption")),n.contentEditable="false"},ke=function(e){var n=e.parentNode;we.insertAfter(e,n),we.remove(n)},Ne=function(e){Oe(e)?ke(e):Ce(e)},Fe=function(e,n){var t=e.getAttribute("style"),r=n(null!==t?t:"");r.length>0?(e.setAttribute("style",r),e.setAttribute("data-mce-style",r)):e.removeAttribute("style")},Pe=function(e,n){return function(e,t,r){e.style[t]?(e.style[t]=de(r),Fe(e,n)):Ee(e,t,r)}},Ie=function(e,n){return e.style[n]?me(e.style[n]):Ae(e,n)},Le=function(e,n){var t=de(n);e.style.marginLeft=t,e.style.marginRight=t},Re=function(e,n){var t=de(n);e.style.marginTop=t,e.style.marginBottom=t},Ue=function(e,n){var t=de(n);e.style.borderWidth=t},Me=function(e,n){e.style.borderStyle=n},_e=function(e){return Te(e,"borderStyle")},ze=function(e){return"FIGURE"===e.nodeName},He=function(e){return"IMG"===e.nodeName},je=function(e){return 0===we.getAttrib(e,"alt").length&&"presentation"===we.getAttrib(e,"role")},Be=function(e){return je(e)?"":Ae(e,"alt")},We=function(){return{src:"",alt:"",title:"",width:"",height:"",class:"",style:"",caption:!1,hspace:"",vspace:"",border:"",borderStyle:"",isDecorative:!1}},qe=function(e,n){var t=document.createElement("img");return Ee(t,"style",n.style),(Se(t)||""!==n.hspace)&&Le(t,n.hspace),(xe(t)||""!==n.vspace)&&Re(t,n.vspace),(De(t)||""!==n.border)&&Ue(t,n.border),(_e(t)||""!==n.borderStyle)&&Me(t,n.borderStyle),e(t.getAttribute("style"))},Ge=function(e,t){var r=document.createElement("img");if(Ze(e,n(n({},t),{caption:!1}),r),$e(r,t.alt,t.isDecorative),t.caption){var o=we.create("figure",{class:"image"});return o.appendChild(r),o.appendChild(we.create("figcaption",{contentEditable:"true"},"Caption")),o.contentEditable="false",o}return r},Ve=function(e,n){return{src:Ae(n,"src"),alt:Be(n),title:Ae(n,"title"),width:Ie(n,"width"),height:Ie(n,"height"),class:Ae(n,"class"),style:e(Ae(n,"style")),caption:Oe(n),hspace:Se(n),vspace:xe(n),border:De(n),borderStyle:Te(n,"borderStyle"),isDecorative:je(n)}},Xe=function(e,n,t,r,o){t[r]!==n[r]&&o(e,r,t[r])},$e=function(e,n,t){if(t){we.setAttrib(e,"role","presentation");var r=V.fromDom(e);z(r,"alt","")}else{if(s(n)){r=V.fromDom(e);H(r,"alt")}else{r=V.fromDom(e);z(r,"alt",n)}"presentation"===we.getAttrib(e,"role")&&we.setAttrib(e,"role","")}},Je=function(e,n,t){t.alt===n.alt&&t.isDecorative===n.isDecorative||$e(e,t.alt,t.isDecorative)},Ye=function(e,n){return function(t,r,o){e(t,o),Fe(t,n)}},Ze=function(e,n,t){var r=Ve(e,t);Xe(t,r,n,"caption",(function(e,n,t){return Ne(e)})),Xe(t,r,n,"src",Ee),Xe(t,r,n,"title",Ee),Xe(t,r,n,"width",Pe("width",e)),Xe(t,r,n,"height",Pe("height",e)),Xe(t,r,n,"class",Ee),Xe(t,r,n,"style",Ye((function(e,n){return Ee(e,"style",n)}),e)),Xe(t,r,n,"hspace",Ye(Le,e)),Xe(t,r,n,"vspace",Ye(Re,e)),Xe(t,r,n,"border",Ye(Ue,e)),Xe(t,r,n,"borderStyle",Ye(Me,e)),Je(t,r,n)},Ke=function(e,n){var t=e.dom.styles.parse(n),r=ge(t),o=e.dom.styles.parse(e.dom.styles.serialize(r));return e.dom.styles.serialize(o)},Qe=function(e){var n=e.selection.getNode(),t=e.dom.getParent(n,"figure.image");return t?e.dom.select("img",t)[0]:n&&("IMG"!==n.nodeName||be(n))?null:n},en=function(e,n){var t=e.dom,r=N(e.schema.getTextBlockElements(),(function(n,t){return!e.schema.isValidChild(t,"figure")})),o=t.getParent(n.parentNode,(function(e){return P(r,e.nodeName)}),e.getBody());return o?t.split(o,n):n},nn=function(e){var n=Qe(e);return n?Ve((function(n){return Ke(e,n)}),n):We()},tn=function(e,n){var t=Ge((function(n){return Ke(e,n)}),n);e.dom.setAttrib(t,"data-mce-id","__mcenew"),e.focus(),e.selection.setContent(t.outerHTML);var r=e.dom.select('*[data-mce-id="__mcenew"]')[0];if(e.dom.setAttrib(r,"data-mce-id",null),ze(r)){var o=en(e,r);e.selection.select(o)}else e.selection.select(r)},rn=function(e,n){e.dom.setAttrib(n,"src",n.getAttribute("src"))},on=function(e,n){if(n){var t=e.dom.is(n.parentNode,"figure.image")?n.parentNode:n;e.dom.remove(t),e.focus(),e.nodeChanged(),e.dom.isEmpty(e.getBody())&&(e.setContent(""),e.selection.setCursorLocation())}},un=function(e,n){var t=Qe(e);if(Ze((function(n){return Ke(e,n)}),n,t),rn(e,t),ze(t.parentNode)){var r=t.parentNode;en(e,r),e.selection.select(t.parentNode)}else e.selection.select(t),pe(e,n,t)},an=function(e,t){var r=t.src;return n(n({},t),{src:ye(e,r)?r:""})},cn=function(e,t){var r=Qe(e);if(r){var o=Ve((function(n){return Ke(e,n)}),r),i=n(n({},o),t),u=an(e,i);i.src?un(e,u):on(e,r)}else t.src&&tn(e,n(n({},We()),t))},sn=function(e,n){var t=a(e)&&a(n);return t?fn(e,n):n},ln=function(e){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];if(0===n.length)throw new Error("Can't merge zero objects");for(var r={},o=0;o<n.length;o++){var i=n[o];for(var u in i)F(i,u)&&(r[u]=e(r[u],i[u]))}return r}},fn=ln(sn),mn=function(e){return e.length>0},dn=tinymce.util.Tools.resolve("tinymce.util.ImageUploader"),gn=tinymce.util.Tools.resolve("tinymce.util.Tools"),hn=function(e){return u(e.value)?e.value:""},pn=function(e){return u(e.text)?e.text:u(e.title)?e.title:""},vn=function(e,n){var t=[];return gn.each(e,(function(e){var r=pn(e);if(void 0!==e.menu){var o=vn(e.menu,n);t.push({text:r,items:o})}else{var i=n(e);t.push({text:r,value:i})}})),t},bn=function(e){return void 0===e&&(e=hn),function(n){return n?A.from(n).map((function(n){return vn(n,e)})):A.none()}},yn=function(e){return bn(hn)(e)},wn=function(e){return F(e,"items")},Sn=function(e,n){return M(e,(function(e){return wn(e)?Sn(e.items,n):e.value===n?A.some(e):A.none()}))},xn=function(e,n){return e.bind((function(e){return Sn(e,n)}))},Dn={sanitizer:bn,sanitize:yn,findEntry:xn},An=function(e){return{title:"Advanced",name:"advanced",items:[{type:"input",label:"Style",name:"style"},{type:"grid",columns:2,items:[{type:"input",label:"Vertical space",name:"vspace",inputMode:"numeric"},{type:"input",label:"Horizontal space",name:"hspace",inputMode:"numeric"},{type:"input",label:"Border width",name:"border",inputMode:"numeric"},{type:"listbox",name:"borderstyle",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]}]}]}},Tn={makeTab:An},On=function(e){var n=Dn.sanitizer((function(n){return e.convertURL(n.value||n.url,"src")})),t=new $((function(t){he(e,(function(e){t(n(e).map((function(e){return L([[{text:"None",value:""}],e])})))}))})),r=Dn.sanitize(ne(e)),o=K(e),i=Q(e),a=ue(e),c=ae(e),s=nn(e),l=te(e),f=re(e),m=Z(e),d=oe(e),g=ce(e),h=se(e),p=A.some(ee(e)).filter((function(e){return u(e)&&e.length>0}));return t.then((function(e){return{image:s,imageList:e,classList:r,hasAdvTab:o,hasUploadTab:i,hasUploadUrl:a,hasUploadHandler:c,hasDescription:l,hasImageTitle:f,hasDimensions:m,hasImageCaption:d,prependURL:p,hasAccessibilityOptions:g,automaticUploads:h}}))},En=function(e){var t={name:"src",type:"urlinput",filetype:"image",label:"Source"},r=e.imageList.map((function(e){return{name:"images",type:"listbox",label:"Image list",items:e}})),o={name:"alt",type:"input",label:"Alternative description",disabled:e.hasAccessibilityOptions&&e.image.isDecorative},i={name:"title",type:"input",label:"Image title"},u={name:"dimensions",type:"sizeinput"},a={type:"label",label:"Accessibility",items:[{name:"isDecorative",type:"checkbox",label:"Image is decorative"}]},c=e.classList.map((function(e){return{name:"classes",type:"listbox",label:"Class",items:e}})),s={type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]},l=function(e){return e?{type:"grid",columns:2}:{type:"panel"}};return L([[t],r.toArray(),e.hasAccessibilityOptions&&e.hasDescription?[a]:[],e.hasDescription?[o]:[],e.hasImageTitle?[i]:[],e.hasDimensions?[u]:[],[n(n({},l(e.classList.isSome()&&e.hasImageCaption)),{items:L([c.toArray(),e.hasImageCaption?[s]:[]])})]])},Cn=function(e){return{title:"General",name:"general",items:En(e)}},kn={makeTab:Cn,makeItems:En},Nn=function(e){var n=[{type:"dropzone",name:"fileinput"}];return{title:"Upload",name:"upload",items:n}},Fn={makeTab:Nn},Pn=function(e){return{prevImage:Dn.findEntry(e.imageList,e.image.src),prevAlt:e.image.alt,open:!0}},In=function(e){return{src:{value:e.src,meta:{}},images:e.src,alt:e.alt,title:e.title,dimensions:{width:e.width,height:e.height},classes:e.class,caption:e.caption,style:e.style,vspace:e.vspace,border:e.border,hspace:e.hspace,borderstyle:e.borderStyle,fileinput:[],isDecorative:e.isDecorative}},Ln=function(e,n){return{src:e.src.value,alt:0===e.alt.length&&n?null:e.alt,title:e.title,width:e.dimensions.width,height:e.dimensions.height,class:e.classes,style:e.style,caption:e.caption,hspace:e.hspace,vspace:e.vspace,border:e.border,borderStyle:e.borderstyle,isDecorative:e.isDecorative}},Rn=function(e,n){return/^(?:[a-zA-Z]+:)?\/\//.test(n)?A.none():e.prependURL.bind((function(e){return n.substring(0,e.length)!==e?A.some(e+n):A.none()}))},Un=function(e,n){var t=n.getData();Rn(e,t.src.value).each((function(e){n.setData({src:{value:e,meta:t.src.meta}})}))},Mn=function(e,n,t){e.hasDescription&&u(t.alt)&&(n.alt=t.alt),e.hasAccessibilityOptions&&(n.isDecorative=t.isDecorative||n.isDecorative||!1),e.hasImageTitle&&u(t.title)&&(n.title=t.title),e.hasDimensions&&(u(t.width)&&(n.dimensions.width=t.width),u(t.height)&&(n.dimensions.height=t.height)),u(t.class)&&Dn.findEntry(e.classList,t.class).each((function(e){n.classes=e.value})),e.hasImageCaption&&l(t.caption)&&(n.caption=t.caption),e.hasAdvTab&&(u(t.style)&&(n.style=t.style),u(t.vspace)&&(n.vspace=t.vspace),u(t.border)&&(n.border=t.border),u(t.hspace)&&(n.hspace=t.hspace),u(t.borderstyle)&&(n.borderstyle=t.borderstyle))},_n=function(e,n){var t=n.getData(),r=t.src.meta;if(void 0!==r){var o=fn({},t);Mn(e,o,r),n.setData(o)}},zn=function(e,n,t,r){var o=r.getData(),i=o.src.value,u=o.src.meta||{};u.width||u.height||!n.hasDimensions||(mn(i)?e.imageSize(i).then((function(e){t.open&&r.setData({dimensions:e})})).catch((function(e){})):r.setData({dimensions:{width:"",height:""}}))},Hn=function(e,n,t){var r=t.getData(),o=Dn.findEntry(e.imageList,r.src.value);n.prevImage=o,t.setData({images:o.map((function(e){return e.value})).getOr("")})},jn=function(e,n,t,r){Un(n,r),_n(n,r),zn(e,n,t,r),Hn(n,t,r)},Bn=function(e,n,t,r){var o=r.getData(),i=Dn.findEntry(n.imageList,o.images);i.each((function(e){var n=""===o.alt||t.prevImage.map((function(e){return e.text===o.alt})).getOr(!1);n?""===e.value?r.setData({src:e,alt:t.prevAlt}):r.setData({src:e,alt:e.text}):r.setData({src:e})})),t.prevImage=i,jn(e,n,t,r)},Wn=function(e){var n=e["margin-top"]&&e["margin-bottom"]&&e["margin-top"]===e["margin-bottom"];return n?me(String(e["margin-top"])):""},qn=function(e){var n=e["margin-right"]&&e["margin-left"]&&e["margin-right"]===e["margin-left"];return n?me(String(e["margin-right"])):""},Gn=function(e){return e["border-width"]?me(String(e["border-width"])):""},Vn=function(e){return e["border-style"]?String(e["border-style"]):""},Xn=function(e,n,t){return n(e(n(t)))},$n=function(e,n,t){var r=ge(e(t.style)),o=fn({},t);return o.vspace=Wn(r),o.hspace=qn(r),o.border=Gn(r),o.borderstyle=Vn(r),o.style=Xn(e,n,r),o},Jn=function(e,n){var t=n.getData(),r=$n(e.parseStyle,e.serializeStyle,t);n.setData(r)},Yn=function(e,n,t){var r=fn(In(n.image),t.getData()),o=qe(e.normalizeCss,Ln(r,!1));t.setData({style:o})},Zn=function(e,n,t,r){var o=r.getData();r.block("Uploading image"),U(o.fileinput).fold((function(){r.unblock()}),(function(o){var i=URL.createObjectURL(o),u=function(){r.unblock(),URL.revokeObjectURL(i)},a=function(o){r.setData({src:{value:o,meta:{}}}),r.showTab("general"),jn(e,n,t,r)};ve(o).then((function(t){var c=e.createBlobCache(o,i,t);n.automaticUploads?e.uploadImage(c).then((function(e){a(e.url),u()})).catch((function(n){u(),e.alertErr(n)})):(e.addToBlobCache(c),a(c.blobUri()),r.unblock())}))}))},Kn=function(e,n,t){return function(r,o){"src"===o.name?jn(e,n,t,r):"images"===o.name?Bn(e,n,t,r):"alt"===o.name?t.prevAlt=r.getData().alt:"style"===o.name?Jn(e,r):"vspace"===o.name||"hspace"===o.name||"border"===o.name||"borderstyle"===o.name?Yn(e,n,r):"fileinput"===o.name?Zn(e,n,t,r):"isDecorative"===o.name&&(r.getData().isDecorative?r.disable("alt"):r.enable("alt"))}},Qn=function(e){return function(){e.open=!1}},et=function(e){if(e.hasAdvTab||e.hasUploadUrl||e.hasUploadHandler){var n={type:"tabpanel",tabs:L([[kn.makeTab(e)],e.hasAdvTab?[Tn.makeTab(e)]:[],e.hasUploadTab&&(e.hasUploadUrl||e.hasUploadHandler)?[Fn.makeTab(e)]:[]])};return n}var t={type:"panel",items:kn.makeItems(e)};return t},nt=function(e){return function(n){var t=Pn(n);return{title:"Insert/Edit Image",size:"normal",body:et(n),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:In(n.image),onSubmit:e.onSubmit(n),onChange:Kn(e,n,t),onClose:Qn(t)}}},tt=function(e){return function(n){return function(t){var r=fn(In(n.image),t.getData());e.execCommand("mceUpdateImage",!1,Ln(r,n.hasAccessibilityOptions)),e.editorUpload.uploadImagesAuto(),t.close()}}},rt=function(e){return function(n){return ye(e,n)?fe(e.documentBaseURI.toAbsolute(n)).then((function(e){return{width:String(e.width),height:String(e.height)}})):$.resolve({width:"",height:""})}},ot=function(e){return function(n,t,r){return e.editorUpload.blobCache.create({blob:n,blobUri:t,name:n.name?n.name.replace(/\.[^\.]+$/,""):null,filename:n.name,base64:r.split(",")[1]})}},it=function(e){return function(n){e.editorUpload.blobCache.add(n)}},ut=function(e){return function(n){e.windowManager.alert(n)}},at=function(e){return function(n){return Ke(e,n)}},ct=function(e){return function(n){return e.dom.parseStyle(n)}},st=function(e){return function(n,t){return e.dom.serializeStyle(n,t)}},lt=function(e){return function(n){return dn(e).upload([n],!1).then((function(e){return 0===e.length?$.reject("Failed to upload image"):!1===e[0].status?$.reject(e[0].error.message):e[0]}))}},ft=function(e){var n={onSubmit:tt(e),imageSize:rt(e),addToBlobCache:it(e),createBlobCache:ot(e),alertErr:ut(e),normalizeCss:at(e),parseStyle:ct(e),serializeStyle:st(e),uploadImage:lt(e)},t=function(){On(e).then(nt(n)).then(e.windowManager.open)};return{open:t}},mt=function(e){e.addCommand("mceImage",ft(e).open),e.addCommand("mceUpdateImage",(function(n,t){e.undoManager.transact((function(){return cn(e,t)}))}))},dt=function(e){var n=e.attr("class");return n&&/\bimage\b/.test(n)},gt=function(e){return function(n){var t=n.length,r=function(n){n.attr("contenteditable",e?"true":null)};while(t--){var o=n[t];dt(o)&&(o.attr("contenteditable",e?"false":null),gn.each(o.getAll("figcaption"),r))}}},ht=function(e){e.on("PreInit",(function(){e.parser.addNodeFilter("figure",gt(!0)),e.serializer.addNodeFilter("figure",gt(!1))}))},pt=function(e){e.ui.registry.addToggleButton("image",{icon:"image",tooltip:"Insert/edit image",onAction:ft(e).open,onSetup:function(n){return n.setActive(m(Qe(e))),e.selection.selectorChangedWithUnbind("img:not([data-mce-object],[data-mce-placeholder]),figure.image",n.setActive).unbind}}),e.ui.registry.addMenuItem("image",{icon:"image",text:"Image...",onAction:ft(e).open}),e.ui.registry.addContextMenu("image",{update:function(e){return ze(e)||He(e)&&!be(e)?["image"]:[]}})};function vt(){e.add("image",(function(e){ht(e),pt(e),mt(e)}))}vt()})()},f034:function(e,n){(function(){"use strict";var e=function(e){var n=e,t=function(){return n},r=function(e){n=e};return{get:t,set:r}},n=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=function(e){return{isFullscreen:function(){return null!==e.get()}}},r=function(e){var n=typeof e;return null===e?"null":"object"===n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n},o=function(e){return function(n){return r(n)===e}},i=function(e){return function(n){return typeof n===e}},u=o("string"),a=o("array"),c=i("boolean"),s=function(e){return null===e||void 0===e},l=function(e){return!s(e)},f=i("function"),m=i("number"),d=function(){},g=function(e,n){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e(n.apply(null,t))}},h=function(e,n){return function(t){return e(n(t))}},p=function(e){return function(){return e}},v=function(e){return e};function b(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var o=n.concat(t);return e.apply(null,o)}}var y=p(!1),w=p(!0),S=function(){return x},x=function(){var e=function(e){return e()},n=v,t={fold:function(e,n){return e()},isSome:y,isNone:w,getOr:n,getOrThunk:e,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:p(null),getOrUndefined:p(void 0),or:n,orThunk:e,map:S,each:d,bind:S,exists:y,forall:w,filter:function(){return S()},toArray:function(){return[]},toString:p("none()")};return t}(),D=function(e){var n=p(e),t=function(){return o},r=function(n){return n(e)},o={fold:function(n,t){return t(e)},isSome:w,isNone:y,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:t,orThunk:t,map:function(n){return D(n(e))},each:function(n){n(e)},bind:r,exists:r,forall:r,filter:function(n){return n(e)?o:x},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return o},A=function(e){return null===e||void 0===e?x:D(e)},T={some:D,none:S,from:A},O=function(){return O=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t],n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},O.apply(this,arguments)},E=function(n){var t=e(T.none()),r=function(){return t.get().each(n)},o=function(){r(),t.set(T.none())},i=function(){return t.get().isSome()},u=function(){return t.get()},a=function(e){r(),t.set(T.some(e))};return{clear:o,isSet:i,get:u,set:a}},C=function(){return E((function(e){return e.unbind()}))},k=function(){var e=E(d),n=function(n){return e.get().each(n)};return O(O({},e),{on:n})},N=Array.prototype.push,F=function(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;o++){var i=e[o];r[o]=n(i,o)}return r},P=function(e,n){for(var t=0,r=e.length;t<r;t++){var o=e[t];n(o,t)}},I=function(e,n){for(var t=[],r=0,o=e.length;r<o;r++){var i=e[r];n(i,r)&&t.push(i)}return t},L=function(e,n,t){for(var r=0,o=e.length;r<o;r++){var i=e[r];if(n(i,r))return T.some(i);if(t(i,r))break}return T.none()},R=function(e,n){return L(e,n,y)},U=function(e){for(var n=[],t=0,r=e.length;t<r;++t){if(!a(e[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+e);N.apply(n,e[t])}return n},M=function(e,n){return U(F(e,n))},_=function(e,n){return n>=0&&n<e.length?T.some(e[n]):T.none()},z=function(e){return _(e,0)},H=function(e,n){for(var t=0;t<e.length;t++){var r=n(e[t],t);if(r.isSome())return r}return T.none()},j=Object.keys,B=function(e,n){for(var t=j(e),r=0,o=t.length;r<o;r++){var i=t[r],u=e[i];n(u,i)}},W=function(e,n){return-1!==e.indexOf(n)},q=function(e){return void 0!==e.style&&f(e.style.getPropertyValue)},G=function(e,n){var t=n||document,r=t.createElement("div");if(r.innerHTML=e,!r.hasChildNodes()||r.childNodes.length>1)throw new Error("HTML must have a single root node");return $(r.childNodes[0])},V=function(e,n){var t=n||document,r=t.createElement(e);return $(r)},X=function(e,n){var t=n||document,r=t.createTextNode(e);return $(r)},$=function(e){if(null===e||void 0===e)throw new Error("Node cannot be null or undefined");return{dom:e}},J=function(e,n,t){return T.from(e.dom.elementFromPoint(n,t)).map($)},Y={fromHtml:G,fromTag:V,fromText:X,fromDom:$,fromPoint:J};"undefined"!==typeof window?window:Function("return this;")();var Z=9,K=11,Q=1,ee=3,ne=function(e){return e.dom.nodeType},te=function(e){return function(n){return ne(n)===e}},re=te(Q),oe=te(ee),ie=te(Z),ue=te(K),ae=function(e){var n,t=!1;return function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return t||(t=!0,n=e.apply(null,r)),n}},ce=function(e,n,t,r){var o=e.isiOS()&&!0===/ipad/i.test(t),i=e.isiOS()&&!o,u=e.isiOS()||e.isAndroid(),a=u||r("(pointer:coarse)"),c=o||!i&&u&&r("(min-device-width:768px)"),s=i||u&&!c,l=n.isSafari()&&e.isiOS()&&!1===/safari/i.test(t),f=!s&&!c&&!l;return{isiPad:p(o),isiPhone:p(i),isTablet:p(c),isPhone:p(s),isTouch:p(a),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:p(l),isDesktop:p(f)}},se=function(e,n){for(var t=0;t<e.length;t++){var r=e[t];if(r.test(n))return r}},le=function(e,n){var t=se(e,n);if(!t)return{major:0,minor:0};var r=function(e){return Number(n.replace(t,"$"+e))};return de(r(1),r(2))},fe=function(e,n){var t=String(n).toLowerCase();return 0===e.length?me():le(e,t)},me=function(){return de(0,0)},de=function(e,n){return{major:e,minor:n}},ge={nu:de,detect:fe,unknown:me},he=function(e,n){return H(n.brands,(function(n){var t=n.brand.toLowerCase();return R(e,(function(e){var n;return t===(null===(n=e.brand)||void 0===n?void 0:n.toLowerCase())})).map((function(e){return{current:e.name,version:ge.nu(parseInt(n.version,10),0)}}))}))},pe=function(e,n){var t=String(n).toLowerCase();return R(e,(function(e){return e.search(t)}))},ve=function(e,n){return pe(e,n).map((function(e){var t=ge.detect(e.versionRegexes,n);return{current:e.name,version:t}}))},be=function(e,n){return pe(e,n).map((function(e){var t=ge.detect(e.versionRegexes,n);return{current:e.name,version:t}}))},ye=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,we=function(e){return function(n){return W(n,e)}},Se=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return W(e,"edge/")&&W(e,"chrome")&&W(e,"safari")&&W(e,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ye],search:function(e){return W(e,"chrome")&&!W(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return W(e,"msie")||W(e,"trident")}},{name:"Opera",versionRegexes:[ye,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:we("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:we("firefox")},{name:"Safari",versionRegexes:[ye,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(W(e,"safari")||W(e,"mobile/"))&&W(e,"applewebkit")}}],xe=[{name:"Windows",search:we("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return W(e,"iphone")||W(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:we("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:we("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:we("linux"),versionRegexes:[]},{name:"Solaris",search:we("sunos"),versionRegexes:[]},{name:"FreeBSD",search:we("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:we("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],De={browsers:p(Se),oses:p(xe)},Ae="Edge",Te="Chrome",Oe="IE",Ee="Opera",Ce="Firefox",ke="Safari",Ne=function(){return Fe({current:void 0,version:ge.unknown()})},Fe=function(e){var n=e.current,t=e.version,r=function(e){return function(){return n===e}};return{current:n,version:t,isEdge:r(Ae),isChrome:r(Te),isIE:r(Oe),isOpera:r(Ee),isFirefox:r(Ce),isSafari:r(ke)}},Pe={unknown:Ne,nu:Fe,edge:p(Ae),chrome:p(Te),ie:p(Oe),opera:p(Ee),firefox:p(Ce),safari:p(ke)},Ie="Windows",Le="iOS",Re="Android",Ue="Linux",Me="OSX",_e="Solaris",ze="FreeBSD",He="ChromeOS",je=function(){return Be({current:void 0,version:ge.unknown()})},Be=function(e){var n=e.current,t=e.version,r=function(e){return function(){return n===e}};return{current:n,version:t,isWindows:r(Ie),isiOS:r(Le),isAndroid:r(Re),isOSX:r(Me),isLinux:r(Ue),isSolaris:r(_e),isFreeBSD:r(ze),isChromeOS:r(He)}},We={unknown:je,nu:Be,windows:p(Ie),ios:p(Le),android:p(Re),linux:p(Ue),osx:p(Me),solaris:p(_e),freebsd:p(ze),chromeos:p(He)},qe=function(e,n,t){var r=De.browsers(),o=De.oses(),i=n.bind((function(e){return he(r,e)})).orThunk((function(){return ve(r,e)})).fold(Pe.unknown,Pe.nu),u=be(o,e).fold(We.unknown,We.nu),a=ce(u,i,e,t);return{browser:i,os:u,deviceType:a}},Ge={detect:qe},Ve=function(e){return window.matchMedia(e).matches},Xe=ae((function(){return Ge.detect(navigator.userAgent,T.from(navigator.userAgentData),Ve)})),$e=function(){return Xe()},Je=function(e,n){var t=e.dom;if(t.nodeType!==Q)return!1;var r=t;if(void 0!==r.matches)return r.matches(n);if(void 0!==r.msMatchesSelector)return r.msMatchesSelector(n);if(void 0!==r.webkitMatchesSelector)return r.webkitMatchesSelector(n);if(void 0!==r.mozMatchesSelector)return r.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},Ye=function(e){return e.nodeType!==Q&&e.nodeType!==Z&&e.nodeType!==K||0===e.childElementCount},Ze=function(e,n){var t=void 0===n?document:n.dom;return Ye(t)?[]:F(t.querySelectorAll(e),Y.fromDom)},Ke=function(e,n){return e.dom===n.dom},Qe=function(e){return Y.fromDom(e.dom.ownerDocument)},en=function(e){return ie(e)?e:Qe(e)},nn=function(e){return T.from(e.dom.parentNode).map(Y.fromDom)},tn=function(e,n){var t=f(n)?n:y,r=e.dom,o=[];while(null!==r.parentNode&&void 0!==r.parentNode){var i=r.parentNode,u=Y.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o},rn=function(e){var n=function(n){return I(n,(function(n){return!Ke(e,n)}))};return nn(e).map(on).map(n).getOr([])},on=function(e){return F(e.dom.childNodes,Y.fromDom)},un=function(e){return ue(e)&&l(e.dom.host)},an=f(Element.prototype.attachShadow)&&f(Node.prototype.getRootNode),cn=p(an),sn=an?function(e){return Y.fromDom(e.dom.getRootNode())}:en,ln=function(e){var n=sn(e);return un(n)?T.some(n):T.none()},fn=function(e){return Y.fromDom(e.dom.host)},mn=function(e){if(cn()&&l(e.target)){var n=Y.fromDom(e.target);if(re(n)&&dn(n)&&e.composed&&e.composedPath){var t=e.composedPath();if(t)return z(t)}}return T.from(e.target)},dn=function(e){return l(e.dom.shadowRoot)},gn=function(e){var n=oe(e)?e.dom.parentNode:e.dom;if(void 0===n||null===n||null===n.ownerDocument)return!1;var t=n.ownerDocument;return ln(Y.fromDom(n)).fold((function(){return t.body.contains(n)}),h(gn,fn))},hn=function(e){var n=e.dom.body;if(null===n||void 0===n)throw new Error("Body is not available yet");return Y.fromDom(n)},pn=function(e,n,t){if(!(u(t)||c(t)||m(t)))throw new Error("Attribute value was not simple");e.setAttribute(n,t+"")},vn=function(e,n,t){pn(e.dom,n,t)},bn=function(e,n){var t=e.dom.getAttribute(n);return null===t?void 0:t},yn=function(e,n){e.dom.removeAttribute(n)},wn=function(e,n,t){if(!u(t))throw new Error("CSS value must be a string: "+t);q(e)&&e.style.setProperty(n,t)},Sn=function(e,n){var t=e.dom;B(n,(function(e,n){wn(t,n,e)}))},xn=function(e,n){var t=e.dom,r=window.getComputedStyle(t),o=r.getPropertyValue(n);return""!==o||gn(e)?o:Dn(t,n)},Dn=function(e,n){return q(e)?e.style.getPropertyValue(n):""},An=function(e,n,t,r,o,i,u){return{target:e,x:n,y:t,stop:r,prevent:o,kill:i,raw:u}},Tn=function(e){var n=Y.fromDom(mn(e).getOr(e.target)),t=function(){return e.stopPropagation()},r=function(){return e.preventDefault()},o=g(r,t);return An(n,e.clientX,e.clientY,t,r,o,e)},On=function(e,n){return function(t){e(t)&&n(Tn(t))}},En=function(e,n,t,r,o){var i=On(t,r);return e.dom.addEventListener(n,i,o),{unbind:b(kn,e,n,i,o)}},Cn=function(e,n,t,r){return En(e,n,t,r,!1)},kn=function(e,n,t,r){e.dom.removeEventListener(n,t,r)},Nn=w,Fn=function(e,n,t){return Cn(e,n,Nn,t)},Pn=function(e,n){var t=function(t,r){return Pn(e+t,n+r)};return{left:e,top:n,translate:t}},In=Pn,Ln=function(e){var n=void 0!==e?e.dom:document,t=n.body.scrollLeft||n.documentElement.scrollLeft,r=n.body.scrollTop||n.documentElement.scrollTop;return In(t,r)},Rn=function(e){var n=void 0===e?window:e;return $e().browser.isFirefox()?T.none():T.from(n["visualViewport"])},Un=function(e,n,t,r){return{x:e,y:n,width:t,height:r,right:e+t,bottom:n+r}},Mn=function(e){var n=void 0===e?window:e,t=n.document,r=Ln(Y.fromDom(t));return Rn(n).fold((function(){var e=n.document.documentElement,t=e.clientWidth,o=e.clientHeight;return Un(r.left,r.top,t,o)}),(function(e){return Un(Math.max(e.pageLeft,r.left),Math.max(e.pageTop,r.top),e.width,e.height)}))},_n=function(e,n,t){return Rn(t).map((function(t){var r=function(e){return n(Tn(e))};return t.addEventListener(e,r),{unbind:function(){return t.removeEventListener(e,r)}}})).getOrThunk((function(){return{unbind:d}}))},zn=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Hn=tinymce.util.Tools.resolve("tinymce.Env"),jn=tinymce.util.Tools.resolve("tinymce.util.Delay"),Bn=function(e,n){e.fire("FullscreenStateChanged",{state:n}),e.fire("ResizeEditor")},Wn=function(e){return e.getParam("fullscreen_native",!1,"boolean")},qn=function(e){var n=Y.fromDom(e.getElement());return ln(n).map(fn).getOrThunk((function(){return hn(Qe(n))}))},Gn=function(e){return void 0!==e.fullscreenElement?e.fullscreenElement:void 0!==e.msFullscreenElement?e.msFullscreenElement:void 0!==e.webkitFullscreenElement?e.webkitFullscreenElement:null},Vn=function(){return void 0!==document.fullscreenElement?"fullscreenchange":void 0!==document.msFullscreenElement?"MSFullscreenChange":void 0!==document.webkitFullscreenElement?"webkitfullscreenchange":"fullscreenchange"},Xn=function(e){var n=e.dom;n.requestFullscreen?n.requestFullscreen():n.msRequestFullscreen?n.msRequestFullscreen():n.webkitRequestFullScreen&&n.webkitRequestFullScreen()},$n=function(e){var n=e.dom;n.exitFullscreen?n.exitFullscreen():n.msExitFullscreen?n.msExitFullscreen():n.webkitCancelFullScreen&&n.webkitCancelFullScreen()},Jn=function(e){return e.dom===Gn(Qe(e).dom)},Yn=function(e,n,t){return I(tn(e,t),n)},Zn=function(e,n){return I(rn(e),n)},Kn=function(e){return Ze(e)},Qn=function(e,n,t){return Yn(e,(function(e){return Je(e,n)}),t)},et=function(e,n){return Zn(e,(function(e){return Je(e,n)}))},nt="data-ephox-mobile-fullscreen-style",tt="display:none!important;",rt="position:absolute!important;",ot="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",it="background-color:rgb(255,255,255)!important;",ut=Hn.os.isAndroid(),at=function(e){var n=xn(e,"background-color");return void 0!==n&&""!==n?"background-color:"+n+"!important":it},ct=function(e,n,t){var r=function(e){return et(e,"*:not(.tox-silver-sink)")},o=function(n){return function(t){var r=bn(t,"style"),o=void 0===r?"no-styles":r.trim();o!==n&&(vn(t,nt,o),Sn(t,e.parseStyle(n)))}},i=Qn(n,"*"),u=M(i,r),a=at(t);P(u,o(tt)),P(i,o(rt+ot+a));var c=!0===ut?"":rt;o(c+ot+a)(n)},st=function(e){var n=Kn("["+nt+"]");P(n,(function(n){var t=bn(n,nt);"no-styles"!==t?Sn(n,e.parseStyle(t)):yn(n,"style"),yn(n,nt)}))},lt=zn.DOM,ft=function(){return Mn(window)},mt=function(e){return window.scrollTo(e.x,e.y)},dt=Rn().fold((function(){return{bind:d,unbind:d}}),(function(e){var n=k(),t=C(),r=C(),o=function(){document.body.scrollTop=0,document.documentElement.scrollTop=0},i=function(){window.requestAnimationFrame((function(){n.on((function(n){return Sn(n,{top:e.offsetTop+"px",left:e.offsetLeft+"px",height:e.height+"px",width:e.width+"px"})}))}))},u=jn.throttle((function(){o(),i()}),50),a=function(e){n.set(e),u(),t.set(_n("resize",u)),r.set(_n("scroll",u))},c=function(){n.on((function(){t.clear(),r.clear()})),n.clear()};return{bind:a,unbind:c}})),gt=function(e,n){var t=document.body,r=document.documentElement,o=e.getContainer(),i=Y.fromDom(o),u=qn(e),a=n.get(),c=Y.fromDom(e.getBody()),s=Hn.deviceType.isTouch(),l=o.style,f=e.iframeElement,m=f.style,d=function(e){e(t,"tox-fullscreen"),e(r,"tox-fullscreen"),e(o,"tox-fullscreen"),ln(i).map((function(e){return fn(e).dom})).each((function(n){e(n,"tox-fullscreen"),e(n,"tox-shadowhost")}))},g=function(){s&&st(e.dom),d(lt.removeClass),dt.unbind(),T.from(n.get()).each((function(e){return e.fullscreenChangeHandler.unbind()}))};if(a)a.fullscreenChangeHandler.unbind(),Wn(e)&&Jn(u)&&$n(Qe(u)),m.width=a.iframeWidth,m.height=a.iframeHeight,l.width=a.containerWidth,l.height=a.containerHeight,l.top=a.containerTop,l.left=a.containerLeft,g(),mt(a.scrollPos),n.set(null),Bn(e,!1),e.off("remove",g);else{var h=Fn(Qe(u),Vn(),(function(t){Wn(e)&&(Jn(u)||null===n.get()||gt(e,n))})),p={scrollPos:ft(),containerWidth:l.width,containerHeight:l.height,containerTop:l.top,containerLeft:l.left,iframeWidth:m.width,iframeHeight:m.height,fullscreenChangeHandler:h};s&&ct(e.dom,i,c),m.width=m.height="100%",l.width=l.height="",d(lt.addClass),dt.bind(i),e.on("remove",g),n.set(p),Wn(e)&&Xn(u),Bn(e,!0)}},ht=function(e,n){e.addCommand("mceFullScreen",(function(){gt(e,n)}))},pt=function(e,n){return function(t){t.setActive(null!==n.get());var r=function(e){return t.setActive(e.state)};return e.on("FullscreenStateChanged",r),function(){return e.off("FullscreenStateChanged",r)}}},vt=function(e,n){var t=function(){return e.execCommand("mceFullScreen")};e.ui.registry.addToggleMenuItem("fullscreen",{text:"Fullscreen",icon:"fullscreen",shortcut:"Meta+Shift+F",onAction:t,onSetup:pt(e,n)}),e.ui.registry.addToggleButton("fullscreen",{tooltip:"Fullscreen",icon:"fullscreen",onAction:t,onSetup:pt(e,n)})};function bt(){n.add("fullscreen",(function(n){var r=e(null);return n.inline||(ht(n,r),vt(n,r),n.addShortcut("Meta+Shift+F","","mceFullScreen")),t(r)}))}bt()})()},f557:function(e,n,t){t("5e4c")}}]);