(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~dcc6580e"],{a0ae:function(e,t,n){
/*!
 * @toast-ui/editor
 * @version 2.2.0 | Tue Jun 16 2020
 * <AUTHOR> FE Development Lab <<EMAIL>>
 * @license MIT
 */
(function(t,r){e.exports=r(n("56b3"))})(window,(function(e){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=65)}([function(e,t,n){"use strict";var r=n(2),i=n.n(r),o=n(8),a=n.n(o),s=n(9),l=n.n(s),c=n(3),u=n.n(c),d=n(5),h=n.n(d),f=n(6),p=n.n(f),g=n(16),m=n.n(g),v=n(13),b=n.n(v),_=/\u200B/g,y=window,C=y.getComputedStyle,w=function(e){return e&&e.nodeType===Node.TEXT_NODE},E=function(e){return e&&e.nodeType===Node.ELEMENT_NODE},T=function(e){return/^(ADDRESS|ARTICLE|ASIDE|BLOCKQUOTE|DETAILS|DIALOG|DD|DIV|DL|DT|FIELDSET|FIGCAPTION|FIGURE|FOOTER|FORM|H[\d]|HEADER|HGROUP|HR|LI|MAIN|NAV|OL|P|PRE|SECTION|UL)$/gi.test(this.getNodeName(e))},N=function(e){return E(e)?e.tagName:"TEXT"},k=function(e){var t;return E(e)?t=e.textContent.replace(_,"").length:w(e)&&(t=e.nodeValue.replace(_,"").length),t},S=function(e){var t;return E(e)?t=e.childNodes.length:w(e)&&(t=e.nodeValue.replace(_,"").length),t},x=function(e){var t,n,r,i=e.parentNode.childNodes;for(t=0,n=i.length;t<n;t+=1)if(i[t]===e){r=t;break}return r},L=function(e,t){var n;return w(e)?n=e:e.childNodes.length&&t>=0&&(n=e.childNodes[t]),n},M=function(e,t,n){var r,i,o=e+"Sibling";while(t&&!t[o]){if(r=N(t.parentNode),r===n||"BODY"===r)break;t=t.parentNode}return t[o]&&(i=t[o]),i},A=function(e,t,n){var r;return r=t>0?L(e,t-1):M("previous",e,n),r},B=function(e,t,n){var r;while(e.parentNode&&!t(e.parentNode))if(e=e.parentNode,n&&n(e.parentNode))break;return t(e.parentNode)&&(r=e),r},O=function(e,t){var n;return n=l()(t)?B(e,(function(e){return t===N(e)})):B(e,(function(e){return t===e})),n},D=function(e,t,n){var r,i=e+"Sibling";return t=O(t,n),t&&t[i]&&(r=t[i]),r},R=function(e,t){return D("previous",e,t)},I=function(e,t){return D("next",e,t)},P=function(e){return O(e,"BODY")},H=function(e){e=e.previousSibling||e.parentNode;while(!w(e)&&"BODY"!==N(e))if(e.previousSibling){e=e.previousSibling;while(e.lastChild)e=e.lastChild}else e=e.parentNode;return"BODY"===N(e)&&(e=null),e},F=function(e,t){var n=document.createTreeWalker(e,4,null,!1),r=e===t;while(!r&&n.nextNode())r=n.currentNode===t;return r},U=function(e,t,n){var r,i=[],o="",s=0;if(!t.length)return i;var l=t.shift(),c=document.createTreeWalker(e,4,null,!1);while(c.nextNode()){o=c.currentNode.nodeValue||"",n&&(o=n(o)),r=s+o.length;while(r>=l){if(i.push({container:c.currentNode,offsetInContainer:l-s,offset:l}),!t.length)return i;l=t.shift()}s=r}do{i.push({container:c.currentNode,offsetInContainer:o.length,offset:l}),l=t.shift()}while(!a()(l));return i},q=function(e){var t={};t.tagName=e.nodeName,e.id&&(t.id=e.id);var n=e.className.trim();return n&&(t.className=n),t},j=function(e,t){var n=[];while(e&&e!==t)E(e)&&n.unshift(q(e)),e=e.parentNode;return n},W=function(e,t){var n=null;return a()(t)||"next"!==t&&"previous"!==t||(n="next"===t?e.nextElementSibling:e.previousElementSibling),n},V=function(e,t,n){var r,i,o,s,l,c=null;return a()(t)||"next"!==t&&"previous"!==t||e&&("next"===t?(i=e.parentNode&&e.parentNode.nextSibling,o=me(e,"thead"),s=o[0]&&o[0].nextSibling,l=s&&"TBODY"===N(s),r=0):(i=e.parentNode&&e.parentNode.previousSibling,o=me(e,"tbody"),s=o[0]&&o[0].previousSibling,l=s&&"THEAD"===N(s),r=e.parentNode.childNodes.length-1),!a()(n)&&n||(r=x(e)),i?c=be(i,"td,th")[r]:o[0]&&l&&(c=he(s,"td,th")[r])),c},z=function(e){return/^(A|B|BR|CODE|DEL|EM|I|IMG|S|SPAN|STRONG)$/gi.test(e.nodeName)},K=function(e){return/^(A|ABBR|ACRONYM|B|BDI|BDO|BIG|CITE|CODE|DEL|DFN|EM|I|INS|KBD|MARK|Q|S|SAMP|SMALL|SPAN|STRONG|SUB|SUP|U|VAR)$/gi.test(e.nodeName)},G=function(e,t,n){var r=t;if(r&&e===r.parentNode)while(r!==n){var i=r.nextSibling;e.removeChild(r),r=i}},$=function(e,t,n){var r=t;while(r!==e){var i=r.parentNode,o=r,a=o.nextSibling,s=o.previousSibling;!n&&a?G(i,a,null):n&&s&&G(i,i.childNodes[0],r),r=i}},Y=function(e){var t=e;while(t.childNodes&&t.childNodes.length){var n=t,r=n.firstChild;t=w(r)&&!k(r)&&r.nextSibling||r}return t},X=function(e,t,n){var r={left:parseInt(e.left,10),top:parseInt(e.top,10),width:parseInt(e.width,10),height:parseInt(e.height,10)};return t>=r.left&&t<=r.left+r.width&&n>=r.top&&n<=r.top+r.height},Z=function(e){return!!e&&("UL"===e.nodeName||"OL"===e.nodeName)},Q=function(e){var t=e.nodeName,n=e.parentNode;return"LI"===t&&e===n.firstChild},J=function(e){var t=e.nodeName,n=e.parentNode,r=n.parentNode;return"LI"===t&&!Z(r)},ee=function(e,t){e.hasChildNodes()&&(i()(e.childNodes).forEach((function(){t.appendChild(e.firstChild)})),t.normalize()),e.parentNode&&e.parentNode.removeChild(e)},te=function(){var e=document.createElement("div"),t=document.createElement("hr");return e.setAttribute("contenteditable",!1),t.setAttribute("contenteditable",!1),e.appendChild(t),e},ne=function(){var e=document.createElement("div");return e.appendChild(document.createElement("br")),e},re=function(e,t){if("SPAN"!==e.nodeName){var n=e.parentNode,r=e;while(r.childNodes&&1===r.childNodes.length&&!w(r.firstChild)){if(r=r.firstChild,"SPAN"===r.nodeName)break;if(r.nodeName===t){var i=document.createElement(t);return ee(r,r.parentNode),n.replaceChild(i,e),i.appendChild(e),i}}}return e},ie=function(e,t,n){var r=re(e,n);if(r.nodeName===n){var i=re(t,n),o=r,a=r.nextSibling;while(a){var s=a.nextSibling;if(a=re(a,n),a.nodeName===n?o?ee(a,o):o=a:o=null,a===i)break;a=s}}},oe=function(e,t){var n=e.collapsed,r=e.commonAncestorContainer,i=e.startContainer,o=e.endContainer;if(!n){var a=null;if(i!==o){var s=O(i,r),l=O(o,r);s&&l&&ie(s,l,t),a=r}else w(i)&&(a=i.parentNode);if(a&&a.nodeName===t){var c,u=a,d=u.previousSibling;d&&(c=re(d),c.nodeName===t&&ee(a,c));var h=a,f=h.nextSibling;f&&(c=re(f),c.nodeName===t&&ee(c,a))}}},ae=function(e){var t=document.createTreeWalker(e,4,null,!1),n=[];while(t.nextNode()){var r=t.currentNode;w(r)&&n.push(r)}return n},se=function(e){return!!e&&("TD"===e.nodeName||"TH"===e.nodeName)},le=function(e,t){var n=e&&e.lastChild;while(n&&t(n))n=n.lastChild;return n},ce=function(e,t){while(e&&t(e.parentNode,e))e=e.parentNode;return e},ue=function(e,t,n){var r=t+"Sibling";while(e&&n(e[r],e))e=e[r];return e};function de(e,t){var n=document.createElement("div");l()(e)?n.innerHTML=e:n.appendChild(e);var r=n.firstChild;return t&&t.appendChild(r),r}function he(e,t){var n=i()(e.querySelectorAll(t));return n.length?n:[]}function fe(e,t){return e!==t&&e.contains(t)}function pe(e,t){var n;n=l()(t)?function(e){return b()(e,t)}:function(e){return e===t};while(e&&e!==document){if(E(e)&&n(e))return e;e=e.parentNode}return null}function ge(e,t){var n=e.parentNode;return t?n&&b()(n,t)?n:null:n}function me(e,t){var n=[];while(e&&e!==document)e=pe(e.parentNode,t),e&&n.push(e);return n}function ve(e,t){var n=[];while(e.parentNode&&!b()(e.parentNode,t))e=e.parentNode,e&&n.push(e);return n}function be(e,t){var n;return n=e.nodeType===Node.DOCUMENT_FRAGMENT_NODE?e.childNodes:e.children,i()(n).filter((function(e){return b()(e,t)}))}function _e(e,t){if(l()(t))e.insertAdjacentHTML("beforeEnd",t);else{t=t.length?i()(t):[t];for(var n=0,r=t.length;n<r;n+=1)e.appendChild(t[n])}}function ye(e,t){if(l()(t))e.insertAdjacentHTML("afterBegin",t);else{t=t.length?i()(t):[t];for(var n=t.length-1,r=0;n>=r;n-=1)e.insertBefore(t[n],e.firstChild)}}function Ce(e,t){var n=t.parentNode;n&&n.insertBefore(e,t)}function we(e,t){var n=t.parentNode;n&&n.insertBefore(e,t.nextSibling)}function Ee(e,t){e=e.length?i()(e):[e],e.forEach((function(e){e.insertAdjacentHTML("afterEnd",t),e.parentNode.removeChild(e)}))}function Te(e,t){e=e.length?i()(e):[e],e.forEach((function(e){var n=document.createElement(t);e.parentNode.insertBefore(n,e),n.appendChild(e)}))}function Ne(e,t){e=e.length?i()(e):[e],e.forEach((function(e){var n=document.createElement(t);e.appendChild(n);while(e.firstChild!==n)n.appendChild(e.firstChild)}))}function ke(e){var t=[];while(e.firstChild)t.push(e.firstChild),e.parentNode.insertBefore(e.firstChild,e);return Se(e),t}function Se(e){e.parentNode&&e.parentNode.removeChild(e)}function xe(e){while(e.firstChild)e.removeChild(e.firstChild)}function Le(e,t){var n=e.parentNode.getBoundingClientRect(),r=n.top,i=n.left;u()(e,{top:t.top-r-document.body.scrollTop+"px"}),u()(e,{left:t.left-i-document.body.scrollLeft+"px"})}function Me(e,t){void 0===t&&(t="document");var n=0,r=0;do{n+=e.offsetTop||0,r+=e.offsetLeft||0,e=e.offsetParent}while(e&&!b()(e,t));return{top:n,left:r}}function Ae(e,t){var n=e.offsetWidth;if(t){var r=C(e),i=r.marginLeft,o=r.marginRight;n+=parseInt(i,10)+parseInt(o,10)}return n}function Be(e,t){var n=e.offsetHeight;if(t){var r=C(e),i=r.marginTop,o=r.marginBottom;n+=parseInt(i,10)+parseInt(o,10)}return n}var Oe=function(e,t,n){a()(n)&&(n=!m()(e,t));var r=n?h.a:p.a;r(e,t)};function De(e,t){var n;if(t)n=e.innerHTML;else{for(var r=document.createDocumentFragment(),o=i()(e.childNodes),a=o.length,s=0;s<a;s+=1)r.appendChild(o[s]);n=r}return n}t["a"]={getNodeName:N,isTextNode:w,isElemNode:E,isBlockNode:T,getTextLength:k,getOffsetLength:S,getPrevOffsetNodeUntil:A,getNodeOffsetOfParent:x,getChildNodeByOffset:L,getNodeWithDirectionUntil:M,containsNode:F,getTopPrevNodeUnder:R,getTopNextNodeUnder:I,getParentUntilBy:B,getParentUntil:O,getTopBlockNode:P,getPrevTextNode:H,findOffsetNode:U,getPath:j,getNodeInfo:q,getTableCellByDirection:W,getSiblingRowCellByDirection:V,isMDSupportInlineNode:z,isStyledNode:K,removeChildFromStartToEndNode:G,removeNodesByDirection:$,getLeafNode:Y,isInsideTaskBox:X,isListNode:Z,isFirstListItem:Q,isFirstLevelListItem:J,mergeNode:ee,createHorizontalRule:te,createEmptyLine:ne,changeTagOrder:re,mergeSameNodes:ie,optimizeRange:oe,getAllTextNode:ae,isCellNode:se,getLastNodeBy:le,getParentNodeBy:ce,getSiblingNodeBy:ue,createElementWith:de,findAll:he,isContain:fe,closest:pe,parent:ge,parents:me,parentsUntil:ve,children:be,append:_e,prepend:ye,insertBefore:Ce,insertAfter:we,replaceWith:Ee,wrap:Te,wrapInner:Ne,unwrap:ke,remove:Se,empty:xe,setOffset:Le,getOffset:Me,getOuterWidth:Ae,getOuterHeight:Be,toggleClass:Oe,finalizeHtml:De}},function(e,t,n){"use strict";var r=n(7),i=n.n(r),o=function(){function e(e,t,n){this.name=e,this.type=t,n&&this.setKeyMap(n)}var t=e.prototype;return t.getName=function(){return this.name},t.getType=function(){return this.type},t.isMDType=function(){return this.type===e.TYPE.MD},t.isWWType=function(){return this.type===e.TYPE.WW},t.isGlobalType=function(){return this.type===e.TYPE.GB},t.setKeyMap=function(e,t){this.keyMap=[e,t]},e}();o.factory=function(e,t){var n;"markdown"===e?n=o.TYPE.MD:"wysiwyg"===e?n=o.TYPE.WW:"global"===e&&(n=o.TYPE.GB);var r=new o(t.name,n);return i()(r,t),r},o.TYPE={MD:0,WW:1,GB:2};var a=o,s=n(12),l=n(23),c=s["b"]?1:0,u=function(){function e(e,t){void 0===t&&(t={}),this._command=new l["a"],this._mdCommand=new l["a"],this._wwCommand=new l["a"],this._options=i()({useCommandShortcut:!0},t),this.base=e,this.keyMapCommand={},this._initEvent()}var t=e.prototype;return t._addCommandBefore=function(e){var t={command:e};return this.base.eventManager.emit("addCommandBefore",t),t.command||e},t.addCommand=function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];r.length&&(t=e.command.apply(e,[t].concat(r))),t=this._addCommandBefore(t);var o,a=t.getName();return t.isMDType()?o=this._mdCommand:t.isWWType()?o=this._wwCommand:t.isGlobalType()&&(o=this._command),o.set(a,t),t.keyMap&&(this.keyMapCommand[t.keyMap[c]]=a),t},t._initEvent=function(){var e=this;this.base.eventManager.listen("command",(function(){e.exec.apply(e,arguments)})),this.base.eventManager.listen("keyMap",(function(t){if(e._options.useCommandShortcut){var n=e.keyMapCommand[t.keyMap];n&&(t.data.preventDefault(),e.exec(n))}}))},t.exec=function(e){var t,n,r=this.base;if(t=this._command.get(e),t||(this.base.isMarkdownMode()?(t=this._mdCommand.get(e),r=this.base.mdEditor):(t=this._wwCommand.get(e),r=this.base.wwEditor)),t){for(var i,o=arguments.length,a=new Array(o>1?o-1:0),s=1;s<o;s++)a[s-1]=arguments[s];a.unshift(r),n=(i=t).exec.apply(i,a)}return n},e}();u.command=function(e,t){var n=a.factory(e,t.name,t.keyMap);return i()(n,t),n};t["a"]=u},function(e,t,n){"use strict";var r=n(21);function i(e){var t;try{t=Array.prototype.slice.call(e)}catch(n){t=[],r(e,(function(e){t.push(e)}))}return t}e.exports=i},function(e,t,n){"use strict";var r=n(9),i=n(26);function o(e,t,n){var o=e.style;r(t)?o[t]=n:i(t,(function(e,t){o[t]=e}))}e.exports=o},function(e,t,n){"use strict";n.d(t,"f",(function(){return r})),n.d(t,"d",(function(){return i})),n.d(t,"e",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"j",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"k",(function(){return c})),n.d(t,"i",(function(){return u})),n.d(t,"h",(function(){return d})),n.d(t,"b",(function(){return h})),n.d(t,"m",(function(){return f})),n.d(t,"a",(function(){return p})),n.d(t,"l",(function(){return g}));n(12);function r(e){return e.sourcepos[0][0]}function i(e){return e.sourcepos[1][0]}function o(e){return e.sourcepos[0][1]}function a(e){return e.sourcepos[1][1]}function s(e){var t=e.type;return"codeBlock"===t||"paragraph"===t}function l(e){var t=e.type;return"htmlBlock"===t||"htmlInline"===t}function c(e){var t=e.type;return"strike"===t||"strong"===t||"emph"===t}function u(e){return"item"===e.type}function d(e){switch(e.type){case"code":case"text":case"emph":case"strong":case"strike":case"link":case"image":case"htmlInline":case"linebreak":case"softbreak":return!0;default:return!1}}function h(e,t,n){void 0===n&&(n=!0),e=n?e:e.parent;while(e&&"document"!==e.type){if(t(e))return e;e=e.parent}return null}function f(e,t,n){void 0===n&&(n=!0),e=n?e:e.parent;while(e&&"document"!==e.type)t(e),e=e.parent}function p(e,t){return{line:e.line,ch:e.ch+t}}function g(e,t){return{line:e.line,ch:t}}},function(e,t,n){"use strict";var r=n(26),i=n(14),o=n(33),a=n(39);function s(e){var t,n=Array.prototype.slice.call(arguments,1),s=e.classList,l=[];s?r(n,(function(t){e.classList.add(t)})):(t=o(e),t&&(n=[].concat(t.split(/\s+/),n)),r(n,(function(e){i(e,l)<0&&l.push(e)})),a(e,l))}e.exports=s},function(e,t,n){"use strict";var r=n(21),i=n(14),o=n(33),a=n(39);function s(e){var t,n,s=Array.prototype.slice.call(arguments,1),l=e.classList;l?r(s,(function(e){l.remove(e)})):(t=o(e).split(/\s+/),n=[],r(t,(function(e){i(e,s)<0&&n.push(e)})),a(e,n))}e.exports=s},function(e,t,n){"use strict";function r(e,t){var n,r,i,o,a=Object.prototype.hasOwnProperty;for(i=1,o=arguments.length;i<o;i+=1)for(r in n=arguments[i],n)a.call(n,r)&&(e[r]=n[r]);return e}e.exports=r},function(e,t,n){"use strict";function r(e){return void 0===e}e.exports=r},function(e,t,n){"use strict";function r(e){return"string"===typeof e||e instanceof String}e.exports=r},function(e,t,n){"use strict";function r(e,t,n){var r;for(r in n=n||null,e)if(e.hasOwnProperty(r)&&!1===t.call(n,e[r],r,e))break}e.exports=r},function(e,t,n){"use strict";var r={chrome:!1,firefox:!1,safari:!1,msie:!1,edge:!1,others:!1,version:0};function i(){var e,t,n=window.navigator,i=n.appName.replace(/\s/g,"_"),o=n.userAgent,a=/MSIE\s([0-9]+[.0-9]*)/,s=/Trident.*rv:11\./,l=/Edge\/(\d+)\./,c={firefox:/Firefox\/(\d+)\./,chrome:/Chrome\/(\d+)\./,safari:/Version\/([\d.]+).*Safari\/(\d+)/},u={Microsoft_Internet_Explorer:function(){var e=o.match(a);e?(r.msie=!0,r.version=parseFloat(e[1])):r.others=!0},Netscape:function(){var n=!1;if(s.exec(o))r.msie=!0,r.version=11,n=!0;else if(l.exec(o))r.edge=!0,r.version=o.match(l)[1],n=!0;else for(e in c)if(c.hasOwnProperty(e)&&(t=o.match(c[e]),t&&t.length>1)){r[e]=n=!0,r.version=parseFloat(t[1]||0);break}n||(r.others=!0)}},d=u[i];d&&u[i]()}"undefined"!==typeof window&&window.navigator&&i(),e.exports=r},function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"d",(function(){return l})),n.d(t,"a",(function(){return c})),n.d(t,"c",(function(){return d}));var r=n(8),i=n.n(r),o=n(43),a=n.n(o),s=/Mac/.test(navigator.platform);function l(){a()("editor","UA-129966929-1")}function c(e,t){return-1!==e.indexOf(t)}var u=["rel","target","contenteditable","hreflang","type"];function d(e){if(!e)return null;var t={};return u.forEach((function(n){i()(e[n])||(t[n]=e[n])})),t}},function(e,t,n){"use strict";var r=n(14),i=n(2),o=Element.prototype,a=o.matches||o.webkitMatchesSelector||o.mozMatchesSelector||o.msMatchesSelector||function(e){var t=this.document||this.ownerDocument;return r(this,i(t.querySelectorAll(e)))>-1};function s(e,t){return a.call(e,t)}e.exports=s},function(e,t,n){"use strict";var r=n(17);function i(e,t,n){var i,o;if(n=n||0,!r(t))return-1;if(Array.prototype.indexOf)return Array.prototype.indexOf.call(t,e,n);for(o=t.length,i=n;n>=0&&i<o;i+=1)if(t[i]===e)return i;return-1}e.exports=i},function(t,n){t.exports=e},function(e,t,n){"use strict";var r=n(14),i=n(33);function o(e,t){var n;return e.classList?e.classList.contains(t):(n=i(e).split(/\s+/),r(t,n)>-1)}e.exports=o},function(e,t,n){"use strict";function r(e){return e instanceof Array}e.exports=r},function(e,t,n){"use strict";n.d(t,"h",(function(){return l})),n.d(t,"b",(function(){return c})),n.d(t,"f",(function(){return u})),n.d(t,"c",(function(){return f})),n.d(t,"e",(function(){return p})),n.d(t,"g",(function(){return g})),n.d(t,"a",(function(){return m})),n.d(t,"d",(function(){return b}));var r=n(12),i=n(4),o=["list","blockQuote"],a=["UL","OL","BLOCKQUOTE"],s=["TR","TH","TBODY","TD"];function l(e){return!Object(r["a"])(o,e.type)}function c(e,t,n,r){var i=(e-t)/n;return i<1?i*r:r}function u(e){var t=document.querySelector('[data-nodeid="'+e.id+'"]');while(!t||Object(r["a"])(s,e.type)||Object(i["k"])(e))e=e.parent,t=document.querySelector('[data-nodeid="'+e.id+'"]');return h(d(e))}function d(e){var t=e;while(e&&"document"!==e){if("item"===e.type){t=e;break}e=e.parent}return{mdNode:t,node:document.querySelector('[data-nodeid="'+t.id+'"]')}}function h(e){var t=e.mdNode,n=e.node;while(Object(r["a"])(o,t.type)&&t.firstChild)t=t.firstChild,n=n.firstElementChild;return{mdNode:t,node:n}}function f(e,t){var n=Object(i["f"])(e),r=Object(i["d"])(e),o=t.lineInfo(n-1).handle.height,a=t.heightAtLine(r,"local")-t.heightAtLine(n-1,"local");return a<=0?o:a+p(t,Object(i["d"])(e))}function p(e,t,n){void 0===n&&(n=Number.MAX_VALUE);var r=e.lineInfo(t);if(!r)return 0;var i=r.handle,o=0;while(t<=n&&!i.text.trim())o+=i.height,t+=1,i=e.lineInfo(t).handle;return o}function g(e,t){var n=0;while(e&&e!==t){if(Object(r["a"])(a,e.tagName)||(n+=e.offsetTop),e.offsetParent===t.offsetParent)break;e=e.parentElement}return n}function m(e,t){var n=t,r=null;while(n){var i=n,o=i.firstElementChild;if(!o)break;var a=v(o,e,g(n,t));r=n,n=a}var s=n||r;return s===t?null:s}function v(e,t,n){return e&&t>n+e.offsetTop?v(e.nextElementSibling,t,n)||e:null}function b(e){var t=e.latestScrollTop,n=e.scrollTop,r=e.targetScrollTop,i=e.sourceScrollTop;return null===t?r:t<n?Math.max(r,i):Math.min(r,i)}},function(e,t,n){"use strict";var r=n(9),i=n(26),o=n(40);function a(e,t,n,o){r(t)?i(t.split(/\s+/g),(function(t){s(e,t,n,o)})):i(t,(function(t,r){s(e,r,t,n)}))}function s(e,t,n,r){function i(t){n.call(r||e,t||window.event)}"addEventListener"in e?e.addEventListener(t,i):"attachEvent"in e&&e.attachEvent("on"+t,i),l(e,t,n,i)}function l(e,t,n,r){var a=o(e,t),s=!1;i(a,(function(e){return e.handler!==n||(s=!0,!1)})),s||a.push({handler:n,wrappedHandler:r})}e.exports=a},function(e,t,n){"use strict";var r=n(9),i=n(26),o=n(40);function a(e,t,n){r(t)?i(t.split(/\s+/g),(function(t){s(e,t,n)})):i(t,(function(t,n){s(e,n,t)}))}function s(e,t,n){var r,a=o(e,t);n?(i(a,(function(i,o){return n!==i.handler||(l(e,t,i.wrappedHandler),r=o,!1)})),a.splice(r,1)):(i(a,(function(n){l(e,t,n.wrappedHandler)})),a.splice(0,a.length))}function l(e,t,n){"removeEventListener"in e?e.removeEventListener(t,n):"detachEvent"in e&&e.detachEvent("on"+t,n)}e.exports=a},function(e,t,n){"use strict";function r(e,t,n){var r=0,i=e.length;for(n=n||null;r<i;r+=1)if(!1===t.call(n,e[r],r,e))break}e.exports=r},function(e,t){!function(e,t){for(var n in t)e[n]=t[n]}(t,function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/dist",n(n.s=10)}([function(e,t,n){"use strict";n.r(t),n.d(t,"__extends",(function(){return i})),n.d(t,"__assign",(function(){return o})),n.d(t,"__rest",(function(){return a})),n.d(t,"__decorate",(function(){return s})),n.d(t,"__param",(function(){return l})),n.d(t,"__metadata",(function(){return c})),n.d(t,"__awaiter",(function(){return u})),n.d(t,"__generator",(function(){return d})),n.d(t,"__exportStar",(function(){return h})),n.d(t,"__values",(function(){return f})),n.d(t,"__read",(function(){return p})),n.d(t,"__spread",(function(){return g})),n.d(t,"__spreadArrays",(function(){return m})),n.d(t,"__await",(function(){return v})),n.d(t,"__asyncGenerator",(function(){return b})),n.d(t,"__asyncDelegator",(function(){return _})),n.d(t,"__asyncValues",(function(){return y})),n.d(t,"__makeTemplateObject",(function(){return C})),n.d(t,"__importStar",(function(){return w})),n.d(t,"__importDefault",(function(){return E}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */
var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function i(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}function s(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a}function l(e,t){return function(n,r){t(n,r,e)}}function c(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function u(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){e.done?i(e.value):new n((function(t){t(e.value)})).then(a,s)}l((r=r.apply(e,t||[])).next())}))}function d(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function h(e,t){for(var n in e)t.hasOwnProperty(n)||(t[n]=e[n])}function f(e){var t="function"==typeof Symbol&&e[Symbol.iterator],n=0;return t?t.call(e):{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}}}function p(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function g(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(p(arguments[t]));return e}function m(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var o=arguments[t],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r}function v(e){return this instanceof v?(this.v=e,this):new v(e)}function b(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(e,t||[]),o=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(e){i[e]&&(r[e]=function(t){return new Promise((function(n,r){o.push([e,t,n,r])>1||s(e,t)}))})}function s(e,t){try{(n=i[e](t)).value instanceof v?Promise.resolve(n.value.v).then(l,c):u(o[0][2],n)}catch(e){u(o[0][3],e)}var n}function l(e){s("next",e)}function c(e){s("throw",e)}function u(e,t){e(t),o.shift(),o.length&&s(o[0][0],o[0][1])}}function _(e){var t,n;return t={},r("next"),r("throw",(function(e){throw e})),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,i){t[r]=e[r]?function(t){return(n=!n)?{value:v(e[r](t)),done:"return"===r}:i?i(t):t}:i}}function y(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=f(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,i,(t=e[n](t)).done,t.value)}))}}}function C(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}function w(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function E(e){return e&&e.__esModule?e:{default:e}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=r.__importDefault(n(17));function o(e){switch(e.type){case"document":case"blockQuote":case"list":case"item":case"paragraph":case"heading":case"emph":case"strong":case"strike":case"link":case"image":case"table":case"tableHead":case"tableBody":case"tableRow":case"tableCell":case"tableDelimRow":return!0;default:return!1}}t.isContainer=o;var a=1,s={};t.getNodeById=function(e){return s[e]},t.removeNodeById=function(e){delete s[e]},t.removeAllNode=function(){s={}};var l=function(){function e(e,t){this.parent=null,this.prev=null,this.next=null,this.firstChild=null,this.lastChild=null,this.literal=null,this.id="document"===e?-1:a++,this.type=e,this.sourcepos=t,s[this.id]=this}return e.prototype.isContainer=function(){return o(this)},e.prototype.unlink=function(){this.prev?this.prev.next=this.next:this.parent&&(this.parent.firstChild=this.next),this.next?this.next.prev=this.prev:this.parent&&(this.parent.lastChild=this.prev),this.parent=null,this.next=null,this.prev=null},e.prototype.replaceWith=function(e){this.insertBefore(e),this.unlink()},e.prototype.insertAfter=function(e){e.unlink(),e.next=this.next,e.next&&(e.next.prev=e),e.prev=this,this.next=e,this.parent&&(e.parent=this.parent,e.next||(e.parent.lastChild=e))},e.prototype.insertBefore=function(e){e.unlink(),e.prev=this.prev,e.prev&&(e.prev.next=e),e.next=this,this.prev=e,e.parent=this.parent,e.prev||(e.parent.firstChild=e)},e.prototype.appendChild=function(e){e.unlink(),e.parent=this,this.lastChild?(this.lastChild.next=e,e.prev=this.lastChild,this.lastChild=e):(this.firstChild=e,this.lastChild=e)},e.prototype.prependChild=function(e){e.unlink(),e.parent=this,this.firstChild?(this.firstChild.prev=e,e.next=this.firstChild,this.firstChild=e):(this.firstChild=e,this.lastChild=e)},e.prototype.walker=function(){return new i.default(this)},e}();t.Node=l;var c=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.open=!0,r.lineOffsets=null,r.stringContent=null,r.lastLineBlank=!1,r.lastLineChecked=!1,r.type=t,r}return r.__extends(t,e),t}(l);t.BlockNode=c;var u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.listData=null,t}return r.__extends(t,e),t}(c);t.ListNode=u;var d=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.level=0,t.headingType="atx",t}return r.__extends(t,e),t}(c);t.HeadingNode=d;var h=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.destination=null,t.title=null,t.extendedAutolink=!1,t}return r.__extends(t,e),t}(l);t.LinkNode=h;var f=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.isFenced=!1,t.fenceChar=null,t.fenceLength=0,t.fenceOffset=-1,t.info=null,t.infoPadding=0,t}return r.__extends(t,e),t}(c);t.CodeBlockNode=f;var p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.htmlBlockType=-1,t}return r.__extends(t,e),t}(c);t.HtmlBlockNode=p;var g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.tickCount=0,t}return r.__extends(t,e),t}(l);t.CodeNode=g;var m=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.columns=[],t}return r.__extends(t,e),t}(c);t.TableNode=m;var v=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.startIdx=0,t.endIdx=0,t.paddingLeft=0,t.paddingRight=0,t.ignored=!1,t}return r.__extends(t,e),t}(c);t.TableCellNode=v;var b=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.title="",t.dest="",t.label="",t}return r.__extends(t,e),t}(c);function _(e,t){switch(e){case"heading":return new d(e,t);case"list":case"item":return new u(e,t);case"link":case"image":return new h(e,t);case"codeBlock":return new f(e,t);case"htmlBlock":return new p(e,t);case"table":return new m(e,t);case"tableCell":return new v(e,t);case"document":case"paragraph":case"blockQuote":case"thematicBreak":case"tableRow":case"tableBody":case"tableHead":return new c(e,t);case"code":return new g(e,t);case"refDef":return new b(e,t);default:return new l(e,t)}}t.RefDefNode=b,t.createNode=_,t.isCodeBlock=function(e){return"codeBlock"===e.type},t.isHtmlBlock=function(e){return"htmlBlock"===e.type},t.isHeading=function(e){return"heading"===e.type},t.isList=function(e){return"list"===e.type},t.isTable=function(e){return"table"===e.type},t.isRefDef=function(e){return"refDef"===e.type},t.text=function(e,t){var n=_("text",t);return n.literal=e,n}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0).__importDefault(n(11)),i=n(7);t.ENTITY="&(?:#x[a-f0-9]{1,6}|#[0-9]{1,7}|[a-z][a-z0-9]{1,31});";var o=/[\\&]/;t.ESCAPABLE="[!\"#$%&'()*+,./:;<=>?@[\\\\\\]^_`{|}~-]";var a=new RegExp("\\\\"+t.ESCAPABLE+"|"+t.ENTITY,"gi"),s=new RegExp('[&<>"]',"g"),l=function(e){return 92===e.charCodeAt(0)?e.charAt(1):i.decodeHTML(e)};function c(e){switch(e){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";default:return e}}t.unescapeString=function(e){return o.test(e)?e.replace(a,l):e},t.normalizeURI=function(e){try{return r.default(e)}catch(t){return e}},t.escapeXml=function(e){return s.test(e)?e.replace(s,c):e},t.repeat=function(e,t){for(var n=[],r=0;r<t;r++)n.push(e);return n.join("")},t.last=function(e){return e.length?e[e.length-1]:null},t.isEmpty=function(e){return!e||!/[^ \t]+/.test(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);t.last=function(e){return e[e.length-1]},t.normalizeReference=function(e){return e.slice(1,e.length-1).trim().replace(/[ \t\r\n]+/," ").toLowerCase().toUpperCase()},t.iterateObject=function(e,t){Object.keys(e).forEach((function(n){t(n,e[n])}))},t.omit=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=r.__assign({},e);return t.forEach((function(e){delete i[e]})),i},t.isEmptyObj=function(e){return!Object.keys(e).length},t.clearObj=function(e){Object.keys(e).forEach((function(t){delete e[t]}))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CODE_INDENT=4,t.C_TAB=9,t.C_NEWLINE=10,t.C_GREATERTHAN=62,t.C_LESSTHAN=60,t.C_SPACE=32,t.C_OPEN_BRACKET=91,t.reNonSpace=/[^ \t\f\v\r\n]/,t.reClosingCodeFence=/^(?:`{3,}|~{3,})(?= *$)/,t.endsWithBlankLine=function(e){for(var t=e;t;){if(t.lastLineBlank)return!0;var n=t.type;if(t.lastLineChecked||"list"!==n&&"item"!==n){t.lastLineChecked=!0;break}t.lastLineChecked=!0,t=t.lastChild}return!1},t.peek=function(e,t){return t<e.length?e.charCodeAt(t):-1},t.isBlank=function(e){return!t.reNonSpace.test(e)},t.isSpaceOrTab=function(e){return e===t.C_SPACE||e===t.C_TAB}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(6),o=n(1),a=n(25),s=n(9),l=n(3),c=n(4),u=/\r\n|\n|\r/;function d(e){return{id:e.id,title:e.title,sourcepos:e.sourcepos,unlinked:!1,destination:e.dest}}t.createRefDefState=d;var h=function(){function e(e,t){var n;this.refMap={},this.refLinkCandidateMap={},this.refDefCandidateMap={},this.referenceDefinition=!!(null===(n=t)||void 0===n?void 0:n.referenceDefinition),this.parser=new i.Parser(t),this.parser.setRefMaps(this.refMap,this.refLinkCandidateMap,this.refDefCandidateMap),this.eventHandlerMap={change:[]},e=e||"",this.lineTexts=e.split(u),this.root=this.parser.parse(e)}return e.prototype.updateLineTexts=function(e,t,n){var i,o=e[0],a=e[1],s=t[0],l=t[1],c=n.split(u),d=c.length,h=this.lineTexts[o-1],f=this.lineTexts[s-1];c[0]=h.slice(0,a-1)+c[0],c[d-1]=c[d-1]+f.slice(l-1);var p=s-o+1;return(i=this.lineTexts).splice.apply(i,r.__spreadArrays([o-1,p],c)),d-p},e.prototype.updateRootNodeState=function(){if(1===this.lineTexts.length&&""===this.lineTexts[0])return this.root.lastLineBlank=!0,void(this.root.sourcepos=[[1,1],[1,0]]);this.root.lastChild&&(this.root.lastLineBlank=this.root.lastChild.lastLineBlank);for(var e=this.lineTexts,t=e.length-1;""===e[t];)t-=1;e.length-2>t&&(t+=1),this.root.sourcepos[1]=[t+1,e[t].length]},e.prototype.replaceRangeNodes=function(e,t,n){e?(a.insertNodesBefore(e,n),a.removeNextUntil(e,t),[e.id,t.id].forEach((function(e){return o.removeNodeById(e)})),e.unlink()):t?(a.insertNodesBefore(t,n),o.removeNodeById(t.id),t.unlink()):a.prependChildNodes(this.root,n)},e.prototype.getNodeRange=function(e,t){var n=a.findChildNodeAtLine(this.root,e[0]),r=a.findChildNodeAtLine(this.root,t[0]);return r&&r.next&&t[0]+1===r.next.sourcepos[0][0]&&(r=r.next),[n,r]},e.prototype.trigger=function(e,t){this.eventHandlerMap[e].forEach((function(e){e(t)}))},e.prototype.extendEndLine=function(e){for(;""===this.lineTexts[e];)e+=1;return e},e.prototype.parseRange=function(e,t,n,r){var i;e&&e.prev&&(o.isList(e.prev)&&function(e){var t=e.match(/^[ \t]+/);if(t&&(t[0].length>=2||/\t/.test(t[0])))return!0;var n=t?e.slice(t.length):e;return s.reBulletListMarker.test(n)||s.reOrderedListMarker.test(n)}(this.lineTexts[n-1])||o.isTable(e.prev)&&(i=this.lineTexts[n-1],!c.isBlank(i)&&-1!==i.indexOf("|")))&&(n=(e=e.prev).sourcepos[0][0]);for(var l=this.lineTexts.slice(n-1,r),u=this.parser.partialParseStart(n,l),d=t?t.next:this.root.firstChild;u.lastChild&&o.isList(u.lastChild)&&d&&("list"===d.type||d.sourcepos[0][1]>=2);){var h=this.extendEndLine(d.sourcepos[1][0]);this.parser.partialParseExtends(this.lineTexts.slice(r,h)),e||(e=t),t=d,r=h,d=d.next}return this.parser.partialParseFinish(),{newNodes:a.getChildNodes(u),extStartNode:e,extEndNode:t}},e.prototype.getRemovedNodeRange=function(e,t){return!e||e&&o.isRefDef(e)||t&&o.isRefDef(t)?null:{id:[e.id,t.id],line:[e.sourcepos[0][0]-1,t.sourcepos[1][0]-1]}},e.prototype.markDeletedRefMap=function(e,t){var n=this;if(!l.isEmptyObj(this.refMap)){var r=function(e){if(o.isRefDef(e)){var t=n.refMap[e.label];t&&e.id===t.id&&(t.unlinked=!0)}};e&&a.invokeNextUntil(r,e.parent,t),t&&a.invokeNextUntil(r,t)}},e.prototype.replaceWithNewRefDefState=function(e){var t=this;if(!l.isEmptyObj(this.refMap)){var n=function(e){if(o.isRefDef(e)){var n=e.label,r=t.refMap[n];r&&!r.unlinked||(t.refMap[n]=d(e))}};e.forEach((function(e){a.invokeNextUntil(n,e)}))}},e.prototype.replaceWithRefDefCandidate=function(){var e=this;l.isEmptyObj(this.refDefCandidateMap)||l.iterateObject(this.refDefCandidateMap,(function(t,n){var r=n.label,i=n.sourcepos,o=e.refMap[r];(!o||o.unlinked||o.sourcepos[0][0]>i[0][0])&&(e.refMap[r]=d(n))}))},e.prototype.getRangeWithRefDef=function(e,t,n,r,i){if(this.referenceDefinition&&!l.isEmptyObj(this.refMap)){var s=a.findChildNodeAtLine(this.root,e-1),c=a.findChildNodeAtLine(this.root,t+1);s&&o.isRefDef(s)&&s!==n&&s!==r&&(e=(n=s).sourcepos[0][0]),c&&o.isRefDef(c)&&c!==n&&c!==r&&(r=c,t=this.extendEndLine(r.sourcepos[1][0]+i))}return[n,r,e,t]},e.prototype.parse=function(e,t,n){void 0===n&&(n=0);var r=this.getNodeRange(e,t),i=r[0],o=r[1],a=i?Math.min(i.sourcepos[0][0],e[0]):e[0],s=this.extendEndLine((o?Math.max(o.sourcepos[1][0],t[0]):t[0])+n),l=this.parseRange.apply(this,this.getRangeWithRefDef(a,s,i,o,n)),c=l.newNodes,u=l.extStartNode,d=l.extEndNode,h=this.getRemovedNodeRange(u,d),f=d?d.next:this.root.firstChild;return this.referenceDefinition?(this.markDeletedRefMap(u,d),this.replaceRangeNodes(u,d,c),this.replaceWithNewRefDefState(c)):this.replaceRangeNodes(u,d,c),{nodes:c,removedNodeRange:h,nextNode:f}},e.prototype.parseRefLink=function(){var e=this,t=[];return l.isEmptyObj(this.refMap)||l.iterateObject(this.refMap,(function(n,r){r.unlinked&&delete e.refMap[n],l.iterateObject(e.refLinkCandidateMap,(function(r,i){var o=i.node;i.refLabel===n&&t.push(e.parse(o.sourcepos[0],o.sourcepos[1]))}))})),t},e.prototype.removeUnlinkedCandidate=function(){l.isEmptyObj(this.refDefCandidateMap)||[this.refLinkCandidateMap,this.refDefCandidateMap].forEach((function(e){l.iterateObject(e,(function(t){a.isUnlinked(t)&&delete e[t]}))}))},e.prototype.editMarkdown=function(e,t,n){var r=this.updateLineTexts(e,t,n),i=this.parse(e,t,r),o=l.omit(i,"nextNode");a.updateNextLineNumbers(i.nextNode,r),this.updateRootNodeState();var s=[o];return this.referenceDefinition&&(this.removeUnlinkedCandidate(),this.replaceWithRefDefCandidate(),s=s.concat(this.parseRefLink())),this.trigger("change",s),s},e.prototype.getLineTexts=function(){return this.lineTexts},e.prototype.getRootNode=function(){return this.root},e.prototype.findNodeAtPosition=function(e){var t=a.findNodeAtPosition(this.root,e);return t&&t!==this.root?t:null},e.prototype.findFirstNodeAtLine=function(e){return a.findFirstNodeAtLine(this.root,e)},e.prototype.on=function(e,t){this.eventHandlerMap[e].push(t)},e.prototype.off=function(e,t){var n=this.eventHandlerMap[e],r=n.indexOf(t);n.splice(r,1)},e.prototype.findNodeById=function(e){return a.findNodeById(e)},e.prototype.removeAllNode=function(){o.removeAllNode()},e}();t.ToastMark=h},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(2),o=n(1),a=n(18),s=n(21),l=n(4),c=n(9),u=n(3),d=[/./,/<\/(?:script|pre|style)>/i,/-->/,/\?>/,/>/,/\]\]>/],h=/^[#`~*+_=<>0-9-]/,f=/\r\n|\n|\r/;function p(){return o.createNode("document",[[1,1],[0,0]])}var g={smart:!1,tagFilter:!1,extendedAutolinks:!1,disallowedHtmlBlockTags:[],referenceDefinition:!1,disallowDeepHeading:!1,customParser:null},m=function(){function e(e){this.options=r.__assign(r.__assign({},g),e),this.doc=p(),this.tip=this.doc,this.oldtip=this.doc,this.lineNumber=0,this.offset=0,this.column=0,this.nextNonspace=0,this.nextNonspaceColumn=0,this.indent=0,this.currentLine="",this.indented=!1,this.blank=!1,this.partiallyConsumedTab=!1,this.allClosed=!0,this.lastMatchedContainer=this.doc,this.refMap={},this.refLinkCandidateMap={},this.refDefCandidateMap={},this.lastLineLength=0,this.inlineParser=new a.InlineParser(this.options)}return e.prototype.advanceOffset=function(e,t){void 0===t&&(t=!1);for(var n,r,i,o=this.currentLine;e>0&&(i=o[this.offset]);)"\t"===i?(n=4-this.column%4,t?(this.partiallyConsumedTab=n>e,r=n>e?e:n,this.column+=r,this.offset+=this.partiallyConsumedTab?0:1,e-=r):(this.partiallyConsumedTab=!1,this.column+=n,this.offset+=1,e-=1)):(this.partiallyConsumedTab=!1,this.offset+=1,this.column+=1,e-=1)},e.prototype.advanceNextNonspace=function(){this.offset=this.nextNonspace,this.column=this.nextNonspaceColumn,this.partiallyConsumedTab=!1},e.prototype.findNextNonspace=function(){for(var e,t=this.currentLine,n=this.offset,r=this.column;""!==(e=t.charAt(n));)if(" "===e)n++,r++;else{if("\t"!==e)break;n++,r+=4-r%4}this.blank="\n"===e||"\r"===e||""===e,this.nextNonspace=n,this.nextNonspaceColumn=r,this.indent=this.nextNonspaceColumn-this.column,this.indented=this.indent>=l.CODE_INDENT},e.prototype.addLine=function(){if(this.partiallyConsumedTab){this.offset+=1;var e=4-this.column%4;this.tip.stringContent+=i.repeat(" ",e)}this.tip.lineOffsets?this.tip.lineOffsets.push(this.offset):this.tip.lineOffsets=[this.offset],this.tip.stringContent+=this.currentLine.slice(this.offset)+"\n"},e.prototype.addChild=function(e,t){for(;!s.blockHandlers[this.tip.type].canContain(e);)this.finalize(this.tip,this.lineNumber-1);var n=t+1,r=o.createNode(e,[[this.lineNumber,n],[0,0]]);return r.stringContent="",this.tip.appendChild(r),this.tip=r,r},e.prototype.closeUnmatchedBlocks=function(){if(!this.allClosed){for(;this.oldtip!==this.lastMatchedContainer;){var e=this.oldtip.parent;this.finalize(this.oldtip,this.lineNumber-1),this.oldtip=e}this.allClosed=!0}},e.prototype.finalize=function(e,t){var n=e.parent;e.open=!1,e.sourcepos[1]=[t,this.lastLineLength],s.blockHandlers[e.type].finalize(this,e),this.tip=n},e.prototype.processInlines=function(e){var t,n=this.options.customParser,r=e.walker();for(this.inlineParser.refMap=this.refMap,this.inlineParser.refLinkCandidateMap=this.refLinkCandidateMap,this.inlineParser.refDefCandidateMap=this.refDefCandidateMap,this.inlineParser.options=this.options;t=r.next();){var i=t.node,o=t.entering,a=i.type;n&&n[a]&&n[a](i,{entering:o}),o||"paragraph"!==a&&"heading"!==a&&("tableCell"!==a||i.ignored)||this.inlineParser.parse(i)}},e.prototype.incorporateLine=function(e){var t=this.doc;this.oldtip=this.tip,this.offset=0,this.column=0,this.blank=!1,this.partiallyConsumedTab=!1,this.lineNumber+=1,-1!==e.indexOf("\0")&&(e=e.replace(/\0/g,"�")),this.currentLine=e;for(var n,r=!0;(n=t.lastChild)&&n.open;){switch(t=n,this.findNextNonspace(),s.blockHandlers[t.type].continue(this,t)){case 0:break;case 1:r=!1;break;case 2:return void(this.lastLineLength=e.length);default:throw new Error("continue returned illegal value, must be 0, 1, or 2")}if(!r){t=t.parent;break}}this.allClosed=t===this.oldtip,this.lastMatchedContainer=t;for(var i="paragraph"!==t.type&&s.blockHandlers[t.type].acceptsLines,a=c.blockStarts.length;!i;){if(this.findNextNonspace(),"table"!==t.type&&"tableBody"!==t.type&&"paragraph"!==t.type&&!this.indented&&!h.test(e.slice(this.nextNonspace))){this.advanceNextNonspace();break}for(var l=0;l<a;){var u=c.blockStarts[l](this,t);if(1===u){t=this.tip;break}if(2===u){t=this.tip,i=!0;break}l++}if(l===a){this.advanceNextNonspace();break}}if(this.allClosed||this.blank||"paragraph"!==this.tip.type){this.closeUnmatchedBlocks(),this.blank&&t.lastChild&&(t.lastChild.lastLineBlank=!0);for(var f=t.type,p=this.blank&&!("blockQuote"===f||o.isCodeBlock(t)&&t.isFenced||"item"===f&&!t.firstChild&&t.sourcepos[0][0]===this.lineNumber),g=t;g;)g.lastLineBlank=p,g=g.parent;s.blockHandlers[f].acceptsLines?(this.addLine(),o.isHtmlBlock(t)&&t.htmlBlockType>=1&&t.htmlBlockType<=5&&d[t.htmlBlockType].test(this.currentLine.slice(this.offset))&&(this.lastLineLength=e.length,this.finalize(t,this.lineNumber))):this.offset<e.length&&!this.blank&&(t=this.addChild("paragraph",this.offset),this.advanceNextNonspace(),this.addLine())}else this.addLine();this.lastLineLength=e.length},e.prototype.parse=function(e){this.doc=p(),this.tip=this.doc,this.lineNumber=0,this.lastLineLength=0,this.offset=0,this.column=0,this.lastMatchedContainer=this.doc,this.currentLine="";var t=e.split(f),n=t.length;this.options.referenceDefinition&&this.clearRefMaps(),e.charCodeAt(e.length-1)===a.C_NEWLINE&&(n-=1);for(var r=0;r<n;r++)this.incorporateLine(t[r]);for(;this.tip;)this.finalize(this.tip,n);return this.processInlines(this.doc),this.doc},e.prototype.partialParseStart=function(e,t){this.doc=p(),this.tip=this.doc,this.lineNumber=e-1,this.lastLineLength=0,this.offset=0,this.column=0,this.lastMatchedContainer=this.doc,this.currentLine="";for(var n=t.length,r=0;r<n;r++)this.incorporateLine(t[r]);return this.doc},e.prototype.partialParseExtends=function(e){for(var t=0;t<e.length;t++)this.incorporateLine(e[t])},e.prototype.partialParseFinish=function(){for(;this.tip;)this.finalize(this.tip,this.lineNumber);this.processInlines(this.doc)},e.prototype.setRefMaps=function(e,t,n){this.refMap=e,this.refLinkCandidateMap=t,this.refDefCandidateMap=n},e.prototype.clearRefMaps=function(){[this.refMap,this.refLinkCandidateMap,this.refDefCandidateMap].forEach((function(e){u.clearObj(e)}))},e}();t.Parser=m},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var i=r(n(12)),o=r(n(13)),a=r(n(14)),s=r(n(15));function l(e){var t=Object.keys(e).join("|"),n=u(e),r=new RegExp("&(?:"+(t+="|#[xX][\\da-fA-F]+|#\\d+")+");","g");return function(e){return String(e).replace(r,n)}}t.decodeXML=l(a.default),t.decodeHTMLStrict=l(i.default);var c=function(e,t){return e<t?1:-1};function u(e){return function(t){return"#"===t.charAt(1)?"X"===t.charAt(2)||"x"===t.charAt(2)?s.default(parseInt(t.substr(3),16)):s.default(parseInt(t.substr(2),10)):e[t.slice(1,-1)]}}t.decodeHTML=function(){for(var e=Object.keys(o.default).sort(c),t=Object.keys(i.default).sort(c),n=0,r=0;n<t.length;n++)e[r]===t[n]?(t[n]+=";?",r++):t[n]+=";";var a=new RegExp("&(?:"+t.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),s=u(i.default);function l(e){return";"!==e.substr(-1)&&(e+=";"),s(e)}return function(e){return String(e).replace(a,l)}}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OPENTAG="<[A-Za-z][A-Za-z0-9-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*/?>",t.CLOSETAG="</[A-Za-z][A-Za-z0-9-]*\\s*[>]";var r="(?:"+t.OPENTAG+"|"+t.CLOSETAG+"|\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e|[<][?].*?[?][>]|<![A-Z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)";t.reHtmlTag=new RegExp("^"+r,"i")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1),i=n(8),o=n(4),a=n(24),s=/^`{3,}(?!.*`)|^~{3,}/,l=[/./,/^<(?:script|pre|style)(?:\s|>|$)/i,/^<!--/,/^<[?]/,/^<![A-Z]/,/^<!\[CDATA\[/,/^<[/]?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[123456]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\s|[/]?[>]|$)/i,new RegExp("^(?:"+i.OPENTAG+"|"+i.CLOSETAG+")\\s*$","i")],c=/^(?:=+|-+)[ \t]*$/,u=/^#{1,6}(?:[ \t]+|$)/,d=/^(?:(?:\*[ \t]*){3,}|(?:_[ \t]*){3,}|(?:-[ \t]*){3,})[ \t]*$/;function h(e,t){return e.options.disallowDeepHeading&&("blockQuote"===t.type||"item"===t.type)}t.reBulletListMarker=/^[*+-]/,t.reOrderedListMarker=/^(\d{1,9})([.)])/,t.blockStarts=[function(e){return e.indented||o.peek(e.currentLine,e.nextNonspace)!==o.C_GREATERTHAN?0:(e.advanceNextNonspace(),e.advanceOffset(1,!1),o.isSpaceOrTab(o.peek(e.currentLine,e.offset))&&e.advanceOffset(1,!0),e.closeUnmatchedBlocks(),e.addChild("blockQuote",e.nextNonspace),1)},function(e,t){var n;if(!e.indented&&!h(e,t)&&(n=e.currentLine.slice(e.nextNonspace).match(u))){e.advanceNextNonspace(),e.advanceOffset(n[0].length,!1),e.closeUnmatchedBlocks();var r=e.addChild("heading",e.nextNonspace);return r.level=n[0].trim().length,r.headingType="atx",r.stringContent=e.currentLine.slice(e.offset).replace(/^[ \t]*#+[ \t]*$/,"").replace(/[ \t]+#+[ \t]*$/,""),e.advanceOffset(e.currentLine.length-e.offset),2}return 0},function(e){var t;if(!e.indented&&(t=e.currentLine.slice(e.nextNonspace).match(s))){var n=t[0].length;e.closeUnmatchedBlocks();var r=e.addChild("codeBlock",e.nextNonspace);return r.isFenced=!0,r.fenceLength=n,r.fenceChar=t[0][0],r.fenceOffset=e.indent,e.advanceNextNonspace(),e.advanceOffset(n,!1),2}return 0},function(e,t){if(!e.indented&&o.peek(e.currentLine,e.nextNonspace)===o.C_LESSTHAN){var n=e.currentLine.slice(e.nextNonspace),r=e.options.disallowedHtmlBlockTags,i=void 0;for(i=1;i<=7;i++){var a=n.match(l[i]);if(a){if(7===i){if("paragraph"===t.type)return 0;if(r.length>0&&new RegExp("</?(?:"+r.join("|")+")","i").test(a[0]))return 0}return e.closeUnmatchedBlocks(),e.addChild("htmlBlock",e.offset).htmlBlockType=i,2}}}return 0},function(e,t){var n;if(null!==t.stringContent&&!e.indented&&"paragraph"===t.type&&!h(e,t.parent)&&(n=e.currentLine.slice(e.nextNonspace).match(c))){e.closeUnmatchedBlocks();for(var i=void 0;o.peek(t.stringContent,0)===o.C_OPEN_BRACKET&&(i=e.inlineParser.parseReference(t,e.refMap));)t.stringContent=t.stringContent.slice(i);if(t.stringContent.length>0){var a=r.createNode("heading",t.sourcepos);return a.level="="===n[0][0]?1:2,a.headingType="setext",a.stringContent=t.stringContent,t.insertAfter(a),t.unlink(),e.tip=a,e.advanceOffset(e.currentLine.length-e.offset,!1),2}return 0}return 0},function(e){return!e.indented&&d.test(e.currentLine.slice(e.nextNonspace))?(e.closeUnmatchedBlocks(),e.addChild("thematicBreak",e.nextNonspace),e.advanceOffset(e.currentLine.length-e.offset,!1),2):0},function(e,n){var r,i,a,s=n;return e.indented&&"list"!==n.type||!(r=function(e,n){var r,i,a=e.currentLine.slice(e.nextNonspace),s={type:"bullet",tight:!0,bulletChar:"",start:0,delimiter:"",padding:0,markerOffset:e.indent,task:!1,checked:!1};if(e.indent>=4)return null;if(r=a.match(t.reBulletListMarker))s.type="bullet",s.bulletChar=r[0][0];else{if(!(r=a.match(t.reOrderedListMarker))||"paragraph"===n.type&&"1"!==r[1])return null;s.type="ordered",s.start=parseInt(r[1],10),s.delimiter=r[2]}if(-1!==(i=o.peek(e.currentLine,e.nextNonspace+r[0].length))&&i!==o.C_TAB&&i!==o.C_SPACE)return null;if("paragraph"===n.type&&!e.currentLine.slice(e.nextNonspace+r[0].length).match(o.reNonSpace))return null;e.advanceNextNonspace(),e.advanceOffset(r[0].length,!0);var l=e.column,c=e.offset;do{e.advanceOffset(1,!0),i=o.peek(e.currentLine,e.offset)}while(e.column-l<5&&o.isSpaceOrTab(i));var u=-1===o.peek(e.currentLine,e.offset),d=e.column-l;return d>=5||d<1||u?(s.padding=r[0].length+1,e.column=l,e.offset=c,o.isSpaceOrTab(o.peek(e.currentLine,e.offset))&&e.advanceOffset(1,!0)):s.padding=r[0].length+d,s}(e,s))?0:(e.closeUnmatchedBlocks(),"list"===e.tip.type&&(i=s.listData,a=r,i.type===a.type&&i.delimiter===a.delimiter&&i.bulletChar===a.bulletChar)||((s=e.addChild("list",e.nextNonspace)).listData=r),(s=e.addChild("item",e.nextNonspace)).listData=r,1)},function(e){return e.indented&&"paragraph"!==e.tip.type&&!e.blank?(e.advanceOffset(o.CODE_INDENT,!0),e.closeUnmatchedBlocks(),e.addChild("codeBlock",e.offset),2):0},a.tableHead,a.tableBody]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(5);t.ToastMark=r.ToastMark;var i=n(26);t.createRenderHTML=i.createRenderHTML;var o=n(6);t.Parser=o.Parser},function(e,t,n){"use strict";var r={};function i(e,t,n){var o,a,s,l,c,u="";for("string"!=typeof t&&(n=t,t=i.defaultChars),void 0===n&&(n=!0),c=function(e){var t,n,i=r[e];if(i)return i;for(i=r[e]=[],t=0;t<128;t++)n=String.fromCharCode(t),/^[0-9a-z]$/i.test(n)?i.push(n):i.push("%"+("0"+t.toString(16).toUpperCase()).slice(-2));for(t=0;t<e.length;t++)i[e.charCodeAt(t)]=e[t];return i}(t),o=0,a=e.length;o<a;o++)if(s=e.charCodeAt(o),n&&37===s&&o+2<a&&/^[0-9a-f]{2}$/i.test(e.slice(o+1,o+3)))u+=e.slice(o,o+3),o+=2;else if(s<128)u+=c[s];else if(s>=55296&&s<=57343){if(s>=55296&&s<=56319&&o+1<a&&(l=e.charCodeAt(o+1))>=56320&&l<=57343){u+=encodeURIComponent(e[o]+e[o+1]),o++;continue}u+="%EF%BF%BD"}else u+=encodeURIComponent(e[o]);return u}i.defaultChars=";/?:@&=+$,-_.!~*'()#",i.componentChars="-_.!~*'()",e.exports=i},function(e){e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Abreve":"Ă","abreve":"ă","ac":"∾","acd":"∿","acE":"∾̳","Acirc":"Â","acirc":"â","acute":"´","Acy":"А","acy":"а","AElig":"Æ","aelig":"æ","af":"⁡","Afr":"𝔄","afr":"𝔞","Agrave":"À","agrave":"à","alefsym":"ℵ","aleph":"ℵ","Alpha":"Α","alpha":"α","Amacr":"Ā","amacr":"ā","amalg":"⨿","amp":"&","AMP":"&","andand":"⩕","And":"⩓","and":"∧","andd":"⩜","andslope":"⩘","andv":"⩚","ang":"∠","ange":"⦤","angle":"∠","angmsdaa":"⦨","angmsdab":"⦩","angmsdac":"⦪","angmsdad":"⦫","angmsdae":"⦬","angmsdaf":"⦭","angmsdag":"⦮","angmsdah":"⦯","angmsd":"∡","angrt":"∟","angrtvb":"⊾","angrtvbd":"⦝","angsph":"∢","angst":"Å","angzarr":"⍼","Aogon":"Ą","aogon":"ą","Aopf":"𝔸","aopf":"𝕒","apacir":"⩯","ap":"≈","apE":"⩰","ape":"≊","apid":"≋","apos":"\'","ApplyFunction":"⁡","approx":"≈","approxeq":"≊","Aring":"Å","aring":"å","Ascr":"𝒜","ascr":"𝒶","Assign":"≔","ast":"*","asymp":"≈","asympeq":"≍","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","awconint":"∳","awint":"⨑","backcong":"≌","backepsilon":"϶","backprime":"‵","backsim":"∽","backsimeq":"⋍","Backslash":"∖","Barv":"⫧","barvee":"⊽","barwed":"⌅","Barwed":"⌆","barwedge":"⌅","bbrk":"⎵","bbrktbrk":"⎶","bcong":"≌","Bcy":"Б","bcy":"б","bdquo":"„","becaus":"∵","because":"∵","Because":"∵","bemptyv":"⦰","bepsi":"϶","bernou":"ℬ","Bernoullis":"ℬ","Beta":"Β","beta":"β","beth":"ℶ","between":"≬","Bfr":"𝔅","bfr":"𝔟","bigcap":"⋂","bigcirc":"◯","bigcup":"⋃","bigodot":"⨀","bigoplus":"⨁","bigotimes":"⨂","bigsqcup":"⨆","bigstar":"★","bigtriangledown":"▽","bigtriangleup":"△","biguplus":"⨄","bigvee":"⋁","bigwedge":"⋀","bkarow":"⤍","blacklozenge":"⧫","blacksquare":"▪","blacktriangle":"▴","blacktriangledown":"▾","blacktriangleleft":"◂","blacktriangleright":"▸","blank":"␣","blk12":"▒","blk14":"░","blk34":"▓","block":"█","bne":"=⃥","bnequiv":"≡⃥","bNot":"⫭","bnot":"⌐","Bopf":"𝔹","bopf":"𝕓","bot":"⊥","bottom":"⊥","bowtie":"⋈","boxbox":"⧉","boxdl":"┐","boxdL":"╕","boxDl":"╖","boxDL":"╗","boxdr":"┌","boxdR":"╒","boxDr":"╓","boxDR":"╔","boxh":"─","boxH":"═","boxhd":"┬","boxHd":"╤","boxhD":"╥","boxHD":"╦","boxhu":"┴","boxHu":"╧","boxhU":"╨","boxHU":"╩","boxminus":"⊟","boxplus":"⊞","boxtimes":"⊠","boxul":"┘","boxuL":"╛","boxUl":"╜","boxUL":"╝","boxur":"└","boxuR":"╘","boxUr":"╙","boxUR":"╚","boxv":"│","boxV":"║","boxvh":"┼","boxvH":"╪","boxVh":"╫","boxVH":"╬","boxvl":"┤","boxvL":"╡","boxVl":"╢","boxVL":"╣","boxvr":"├","boxvR":"╞","boxVr":"╟","boxVR":"╠","bprime":"‵","breve":"˘","Breve":"˘","brvbar":"¦","bscr":"𝒷","Bscr":"ℬ","bsemi":"⁏","bsim":"∽","bsime":"⋍","bsolb":"⧅","bsol":"\\\\","bsolhsub":"⟈","bull":"•","bullet":"•","bump":"≎","bumpE":"⪮","bumpe":"≏","Bumpeq":"≎","bumpeq":"≏","Cacute":"Ć","cacute":"ć","capand":"⩄","capbrcup":"⩉","capcap":"⩋","cap":"∩","Cap":"⋒","capcup":"⩇","capdot":"⩀","CapitalDifferentialD":"ⅅ","caps":"∩︀","caret":"⁁","caron":"ˇ","Cayleys":"ℭ","ccaps":"⩍","Ccaron":"Č","ccaron":"č","Ccedil":"Ç","ccedil":"ç","Ccirc":"Ĉ","ccirc":"ĉ","Cconint":"∰","ccups":"⩌","ccupssm":"⩐","Cdot":"Ċ","cdot":"ċ","cedil":"¸","Cedilla":"¸","cemptyv":"⦲","cent":"¢","centerdot":"·","CenterDot":"·","cfr":"𝔠","Cfr":"ℭ","CHcy":"Ч","chcy":"ч","check":"✓","checkmark":"✓","Chi":"Χ","chi":"χ","circ":"ˆ","circeq":"≗","circlearrowleft":"↺","circlearrowright":"↻","circledast":"⊛","circledcirc":"⊚","circleddash":"⊝","CircleDot":"⊙","circledR":"®","circledS":"Ⓢ","CircleMinus":"⊖","CirclePlus":"⊕","CircleTimes":"⊗","cir":"○","cirE":"⧃","cire":"≗","cirfnint":"⨐","cirmid":"⫯","cirscir":"⧂","ClockwiseContourIntegral":"∲","CloseCurlyDoubleQuote":"”","CloseCurlyQuote":"’","clubs":"♣","clubsuit":"♣","colon":":","Colon":"∷","Colone":"⩴","colone":"≔","coloneq":"≔","comma":",","commat":"@","comp":"∁","compfn":"∘","complement":"∁","complexes":"ℂ","cong":"≅","congdot":"⩭","Congruent":"≡","conint":"∮","Conint":"∯","ContourIntegral":"∮","copf":"𝕔","Copf":"ℂ","coprod":"∐","Coproduct":"∐","copy":"©","COPY":"©","copysr":"℗","CounterClockwiseContourIntegral":"∳","crarr":"↵","cross":"✗","Cross":"⨯","Cscr":"𝒞","cscr":"𝒸","csub":"⫏","csube":"⫑","csup":"⫐","csupe":"⫒","ctdot":"⋯","cudarrl":"⤸","cudarrr":"⤵","cuepr":"⋞","cuesc":"⋟","cularr":"↶","cularrp":"⤽","cupbrcap":"⩈","cupcap":"⩆","CupCap":"≍","cup":"∪","Cup":"⋓","cupcup":"⩊","cupdot":"⊍","cupor":"⩅","cups":"∪︀","curarr":"↷","curarrm":"⤼","curlyeqprec":"⋞","curlyeqsucc":"⋟","curlyvee":"⋎","curlywedge":"⋏","curren":"¤","curvearrowleft":"↶","curvearrowright":"↷","cuvee":"⋎","cuwed":"⋏","cwconint":"∲","cwint":"∱","cylcty":"⌭","dagger":"†","Dagger":"‡","daleth":"ℸ","darr":"↓","Darr":"↡","dArr":"⇓","dash":"‐","Dashv":"⫤","dashv":"⊣","dbkarow":"⤏","dblac":"˝","Dcaron":"Ď","dcaron":"ď","Dcy":"Д","dcy":"д","ddagger":"‡","ddarr":"⇊","DD":"ⅅ","dd":"ⅆ","DDotrahd":"⤑","ddotseq":"⩷","deg":"°","Del":"∇","Delta":"Δ","delta":"δ","demptyv":"⦱","dfisht":"⥿","Dfr":"𝔇","dfr":"𝔡","dHar":"⥥","dharl":"⇃","dharr":"⇂","DiacriticalAcute":"´","DiacriticalDot":"˙","DiacriticalDoubleAcute":"˝","DiacriticalGrave":"`","DiacriticalTilde":"˜","diam":"⋄","diamond":"⋄","Diamond":"⋄","diamondsuit":"♦","diams":"♦","die":"¨","DifferentialD":"ⅆ","digamma":"ϝ","disin":"⋲","div":"÷","divide":"÷","divideontimes":"⋇","divonx":"⋇","DJcy":"Ђ","djcy":"ђ","dlcorn":"⌞","dlcrop":"⌍","dollar":"$","Dopf":"𝔻","dopf":"𝕕","Dot":"¨","dot":"˙","DotDot":"⃜","doteq":"≐","doteqdot":"≑","DotEqual":"≐","dotminus":"∸","dotplus":"∔","dotsquare":"⊡","doublebarwedge":"⌆","DoubleContourIntegral":"∯","DoubleDot":"¨","DoubleDownArrow":"⇓","DoubleLeftArrow":"⇐","DoubleLeftRightArrow":"⇔","DoubleLeftTee":"⫤","DoubleLongLeftArrow":"⟸","DoubleLongLeftRightArrow":"⟺","DoubleLongRightArrow":"⟹","DoubleRightArrow":"⇒","DoubleRightTee":"⊨","DoubleUpArrow":"⇑","DoubleUpDownArrow":"⇕","DoubleVerticalBar":"∥","DownArrowBar":"⤓","downarrow":"↓","DownArrow":"↓","Downarrow":"⇓","DownArrowUpArrow":"⇵","DownBreve":"̑","downdownarrows":"⇊","downharpoonleft":"⇃","downharpoonright":"⇂","DownLeftRightVector":"⥐","DownLeftTeeVector":"⥞","DownLeftVectorBar":"⥖","DownLeftVector":"↽","DownRightTeeVector":"⥟","DownRightVectorBar":"⥗","DownRightVector":"⇁","DownTeeArrow":"↧","DownTee":"⊤","drbkarow":"⤐","drcorn":"⌟","drcrop":"⌌","Dscr":"𝒟","dscr":"𝒹","DScy":"Ѕ","dscy":"ѕ","dsol":"⧶","Dstrok":"Đ","dstrok":"đ","dtdot":"⋱","dtri":"▿","dtrif":"▾","duarr":"⇵","duhar":"⥯","dwangle":"⦦","DZcy":"Џ","dzcy":"џ","dzigrarr":"⟿","Eacute":"É","eacute":"é","easter":"⩮","Ecaron":"Ě","ecaron":"ě","Ecirc":"Ê","ecirc":"ê","ecir":"≖","ecolon":"≕","Ecy":"Э","ecy":"э","eDDot":"⩷","Edot":"Ė","edot":"ė","eDot":"≑","ee":"ⅇ","efDot":"≒","Efr":"𝔈","efr":"𝔢","eg":"⪚","Egrave":"È","egrave":"è","egs":"⪖","egsdot":"⪘","el":"⪙","Element":"∈","elinters":"⏧","ell":"ℓ","els":"⪕","elsdot":"⪗","Emacr":"Ē","emacr":"ē","empty":"∅","emptyset":"∅","EmptySmallSquare":"◻","emptyv":"∅","EmptyVerySmallSquare":"▫","emsp13":" ","emsp14":" ","emsp":" ","ENG":"Ŋ","eng":"ŋ","ensp":" ","Eogon":"Ę","eogon":"ę","Eopf":"𝔼","eopf":"𝕖","epar":"⋕","eparsl":"⧣","eplus":"⩱","epsi":"ε","Epsilon":"Ε","epsilon":"ε","epsiv":"ϵ","eqcirc":"≖","eqcolon":"≕","eqsim":"≂","eqslantgtr":"⪖","eqslantless":"⪕","Equal":"⩵","equals":"=","EqualTilde":"≂","equest":"≟","Equilibrium":"⇌","equiv":"≡","equivDD":"⩸","eqvparsl":"⧥","erarr":"⥱","erDot":"≓","escr":"ℯ","Escr":"ℰ","esdot":"≐","Esim":"⩳","esim":"≂","Eta":"Η","eta":"η","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","euro":"€","excl":"!","exist":"∃","Exists":"∃","expectation":"ℰ","exponentiale":"ⅇ","ExponentialE":"ⅇ","fallingdotseq":"≒","Fcy":"Ф","fcy":"ф","female":"♀","ffilig":"ﬃ","fflig":"ﬀ","ffllig":"ﬄ","Ffr":"𝔉","ffr":"𝔣","filig":"ﬁ","FilledSmallSquare":"◼","FilledVerySmallSquare":"▪","fjlig":"fj","flat":"♭","fllig":"ﬂ","fltns":"▱","fnof":"ƒ","Fopf":"𝔽","fopf":"𝕗","forall":"∀","ForAll":"∀","fork":"⋔","forkv":"⫙","Fouriertrf":"ℱ","fpartint":"⨍","frac12":"½","frac13":"⅓","frac14":"¼","frac15":"⅕","frac16":"⅙","frac18":"⅛","frac23":"⅔","frac25":"⅖","frac34":"¾","frac35":"⅗","frac38":"⅜","frac45":"⅘","frac56":"⅚","frac58":"⅝","frac78":"⅞","frasl":"⁄","frown":"⌢","fscr":"𝒻","Fscr":"ℱ","gacute":"ǵ","Gamma":"Γ","gamma":"γ","Gammad":"Ϝ","gammad":"ϝ","gap":"⪆","Gbreve":"Ğ","gbreve":"ğ","Gcedil":"Ģ","Gcirc":"Ĝ","gcirc":"ĝ","Gcy":"Г","gcy":"г","Gdot":"Ġ","gdot":"ġ","ge":"≥","gE":"≧","gEl":"⪌","gel":"⋛","geq":"≥","geqq":"≧","geqslant":"⩾","gescc":"⪩","ges":"⩾","gesdot":"⪀","gesdoto":"⪂","gesdotol":"⪄","gesl":"⋛︀","gesles":"⪔","Gfr":"𝔊","gfr":"𝔤","gg":"≫","Gg":"⋙","ggg":"⋙","gimel":"ℷ","GJcy":"Ѓ","gjcy":"ѓ","gla":"⪥","gl":"≷","glE":"⪒","glj":"⪤","gnap":"⪊","gnapprox":"⪊","gne":"⪈","gnE":"≩","gneq":"⪈","gneqq":"≩","gnsim":"⋧","Gopf":"𝔾","gopf":"𝕘","grave":"`","GreaterEqual":"≥","GreaterEqualLess":"⋛","GreaterFullEqual":"≧","GreaterGreater":"⪢","GreaterLess":"≷","GreaterSlantEqual":"⩾","GreaterTilde":"≳","Gscr":"𝒢","gscr":"ℊ","gsim":"≳","gsime":"⪎","gsiml":"⪐","gtcc":"⪧","gtcir":"⩺","gt":">","GT":">","Gt":"≫","gtdot":"⋗","gtlPar":"⦕","gtquest":"⩼","gtrapprox":"⪆","gtrarr":"⥸","gtrdot":"⋗","gtreqless":"⋛","gtreqqless":"⪌","gtrless":"≷","gtrsim":"≳","gvertneqq":"≩︀","gvnE":"≩︀","Hacek":"ˇ","hairsp":" ","half":"½","hamilt":"ℋ","HARDcy":"Ъ","hardcy":"ъ","harrcir":"⥈","harr":"↔","hArr":"⇔","harrw":"↭","Hat":"^","hbar":"ℏ","Hcirc":"Ĥ","hcirc":"ĥ","hearts":"♥","heartsuit":"♥","hellip":"…","hercon":"⊹","hfr":"𝔥","Hfr":"ℌ","HilbertSpace":"ℋ","hksearow":"⤥","hkswarow":"⤦","hoarr":"⇿","homtht":"∻","hookleftarrow":"↩","hookrightarrow":"↪","hopf":"𝕙","Hopf":"ℍ","horbar":"―","HorizontalLine":"─","hscr":"𝒽","Hscr":"ℋ","hslash":"ℏ","Hstrok":"Ħ","hstrok":"ħ","HumpDownHump":"≎","HumpEqual":"≏","hybull":"⁃","hyphen":"‐","Iacute":"Í","iacute":"í","ic":"⁣","Icirc":"Î","icirc":"î","Icy":"И","icy":"и","Idot":"İ","IEcy":"Е","iecy":"е","iexcl":"¡","iff":"⇔","ifr":"𝔦","Ifr":"ℑ","Igrave":"Ì","igrave":"ì","ii":"ⅈ","iiiint":"⨌","iiint":"∭","iinfin":"⧜","iiota":"℩","IJlig":"Ĳ","ijlig":"ĳ","Imacr":"Ī","imacr":"ī","image":"ℑ","ImaginaryI":"ⅈ","imagline":"ℐ","imagpart":"ℑ","imath":"ı","Im":"ℑ","imof":"⊷","imped":"Ƶ","Implies":"⇒","incare":"℅","in":"∈","infin":"∞","infintie":"⧝","inodot":"ı","intcal":"⊺","int":"∫","Int":"∬","integers":"ℤ","Integral":"∫","intercal":"⊺","Intersection":"⋂","intlarhk":"⨗","intprod":"⨼","InvisibleComma":"⁣","InvisibleTimes":"⁢","IOcy":"Ё","iocy":"ё","Iogon":"Į","iogon":"į","Iopf":"𝕀","iopf":"𝕚","Iota":"Ι","iota":"ι","iprod":"⨼","iquest":"¿","iscr":"𝒾","Iscr":"ℐ","isin":"∈","isindot":"⋵","isinE":"⋹","isins":"⋴","isinsv":"⋳","isinv":"∈","it":"⁢","Itilde":"Ĩ","itilde":"ĩ","Iukcy":"І","iukcy":"і","Iuml":"Ï","iuml":"ï","Jcirc":"Ĵ","jcirc":"ĵ","Jcy":"Й","jcy":"й","Jfr":"𝔍","jfr":"𝔧","jmath":"ȷ","Jopf":"𝕁","jopf":"𝕛","Jscr":"𝒥","jscr":"𝒿","Jsercy":"Ј","jsercy":"ј","Jukcy":"Є","jukcy":"є","Kappa":"Κ","kappa":"κ","kappav":"ϰ","Kcedil":"Ķ","kcedil":"ķ","Kcy":"К","kcy":"к","Kfr":"𝔎","kfr":"𝔨","kgreen":"ĸ","KHcy":"Х","khcy":"х","KJcy":"Ќ","kjcy":"ќ","Kopf":"𝕂","kopf":"𝕜","Kscr":"𝒦","kscr":"𝓀","lAarr":"⇚","Lacute":"Ĺ","lacute":"ĺ","laemptyv":"⦴","lagran":"ℒ","Lambda":"Λ","lambda":"λ","lang":"⟨","Lang":"⟪","langd":"⦑","langle":"⟨","lap":"⪅","Laplacetrf":"ℒ","laquo":"«","larrb":"⇤","larrbfs":"⤟","larr":"←","Larr":"↞","lArr":"⇐","larrfs":"⤝","larrhk":"↩","larrlp":"↫","larrpl":"⤹","larrsim":"⥳","larrtl":"↢","latail":"⤙","lAtail":"⤛","lat":"⪫","late":"⪭","lates":"⪭︀","lbarr":"⤌","lBarr":"⤎","lbbrk":"❲","lbrace":"{","lbrack":"[","lbrke":"⦋","lbrksld":"⦏","lbrkslu":"⦍","Lcaron":"Ľ","lcaron":"ľ","Lcedil":"Ļ","lcedil":"ļ","lceil":"⌈","lcub":"{","Lcy":"Л","lcy":"л","ldca":"⤶","ldquo":"“","ldquor":"„","ldrdhar":"⥧","ldrushar":"⥋","ldsh":"↲","le":"≤","lE":"≦","LeftAngleBracket":"⟨","LeftArrowBar":"⇤","leftarrow":"←","LeftArrow":"←","Leftarrow":"⇐","LeftArrowRightArrow":"⇆","leftarrowtail":"↢","LeftCeiling":"⌈","LeftDoubleBracket":"⟦","LeftDownTeeVector":"⥡","LeftDownVectorBar":"⥙","LeftDownVector":"⇃","LeftFloor":"⌊","leftharpoondown":"↽","leftharpoonup":"↼","leftleftarrows":"⇇","leftrightarrow":"↔","LeftRightArrow":"↔","Leftrightarrow":"⇔","leftrightarrows":"⇆","leftrightharpoons":"⇋","leftrightsquigarrow":"↭","LeftRightVector":"⥎","LeftTeeArrow":"↤","LeftTee":"⊣","LeftTeeVector":"⥚","leftthreetimes":"⋋","LeftTriangleBar":"⧏","LeftTriangle":"⊲","LeftTriangleEqual":"⊴","LeftUpDownVector":"⥑","LeftUpTeeVector":"⥠","LeftUpVectorBar":"⥘","LeftUpVector":"↿","LeftVectorBar":"⥒","LeftVector":"↼","lEg":"⪋","leg":"⋚","leq":"≤","leqq":"≦","leqslant":"⩽","lescc":"⪨","les":"⩽","lesdot":"⩿","lesdoto":"⪁","lesdotor":"⪃","lesg":"⋚︀","lesges":"⪓","lessapprox":"⪅","lessdot":"⋖","lesseqgtr":"⋚","lesseqqgtr":"⪋","LessEqualGreater":"⋚","LessFullEqual":"≦","LessGreater":"≶","lessgtr":"≶","LessLess":"⪡","lesssim":"≲","LessSlantEqual":"⩽","LessTilde":"≲","lfisht":"⥼","lfloor":"⌊","Lfr":"𝔏","lfr":"𝔩","lg":"≶","lgE":"⪑","lHar":"⥢","lhard":"↽","lharu":"↼","lharul":"⥪","lhblk":"▄","LJcy":"Љ","ljcy":"љ","llarr":"⇇","ll":"≪","Ll":"⋘","llcorner":"⌞","Lleftarrow":"⇚","llhard":"⥫","lltri":"◺","Lmidot":"Ŀ","lmidot":"ŀ","lmoustache":"⎰","lmoust":"⎰","lnap":"⪉","lnapprox":"⪉","lne":"⪇","lnE":"≨","lneq":"⪇","lneqq":"≨","lnsim":"⋦","loang":"⟬","loarr":"⇽","lobrk":"⟦","longleftarrow":"⟵","LongLeftArrow":"⟵","Longleftarrow":"⟸","longleftrightarrow":"⟷","LongLeftRightArrow":"⟷","Longleftrightarrow":"⟺","longmapsto":"⟼","longrightarrow":"⟶","LongRightArrow":"⟶","Longrightarrow":"⟹","looparrowleft":"↫","looparrowright":"↬","lopar":"⦅","Lopf":"𝕃","lopf":"𝕝","loplus":"⨭","lotimes":"⨴","lowast":"∗","lowbar":"_","LowerLeftArrow":"↙","LowerRightArrow":"↘","loz":"◊","lozenge":"◊","lozf":"⧫","lpar":"(","lparlt":"⦓","lrarr":"⇆","lrcorner":"⌟","lrhar":"⇋","lrhard":"⥭","lrm":"‎","lrtri":"⊿","lsaquo":"‹","lscr":"𝓁","Lscr":"ℒ","lsh":"↰","Lsh":"↰","lsim":"≲","lsime":"⪍","lsimg":"⪏","lsqb":"[","lsquo":"‘","lsquor":"‚","Lstrok":"Ł","lstrok":"ł","ltcc":"⪦","ltcir":"⩹","lt":"<","LT":"<","Lt":"≪","ltdot":"⋖","lthree":"⋋","ltimes":"⋉","ltlarr":"⥶","ltquest":"⩻","ltri":"◃","ltrie":"⊴","ltrif":"◂","ltrPar":"⦖","lurdshar":"⥊","luruhar":"⥦","lvertneqq":"≨︀","lvnE":"≨︀","macr":"¯","male":"♂","malt":"✠","maltese":"✠","Map":"⤅","map":"↦","mapsto":"↦","mapstodown":"↧","mapstoleft":"↤","mapstoup":"↥","marker":"▮","mcomma":"⨩","Mcy":"М","mcy":"м","mdash":"—","mDDot":"∺","measuredangle":"∡","MediumSpace":" ","Mellintrf":"ℳ","Mfr":"𝔐","mfr":"𝔪","mho":"℧","micro":"µ","midast":"*","midcir":"⫰","mid":"∣","middot":"·","minusb":"⊟","minus":"−","minusd":"∸","minusdu":"⨪","MinusPlus":"∓","mlcp":"⫛","mldr":"…","mnplus":"∓","models":"⊧","Mopf":"𝕄","mopf":"𝕞","mp":"∓","mscr":"𝓂","Mscr":"ℳ","mstpos":"∾","Mu":"Μ","mu":"μ","multimap":"⊸","mumap":"⊸","nabla":"∇","Nacute":"Ń","nacute":"ń","nang":"∠⃒","nap":"≉","napE":"⩰̸","napid":"≋̸","napos":"ŉ","napprox":"≉","natural":"♮","naturals":"ℕ","natur":"♮","nbsp":" ","nbump":"≎̸","nbumpe":"≏̸","ncap":"⩃","Ncaron":"Ň","ncaron":"ň","Ncedil":"Ņ","ncedil":"ņ","ncong":"≇","ncongdot":"⩭̸","ncup":"⩂","Ncy":"Н","ncy":"н","ndash":"–","nearhk":"⤤","nearr":"↗","neArr":"⇗","nearrow":"↗","ne":"≠","nedot":"≐̸","NegativeMediumSpace":"​","NegativeThickSpace":"​","NegativeThinSpace":"​","NegativeVeryThinSpace":"​","nequiv":"≢","nesear":"⤨","nesim":"≂̸","NestedGreaterGreater":"≫","NestedLessLess":"≪","NewLine":"\\n","nexist":"∄","nexists":"∄","Nfr":"𝔑","nfr":"𝔫","ngE":"≧̸","nge":"≱","ngeq":"≱","ngeqq":"≧̸","ngeqslant":"⩾̸","nges":"⩾̸","nGg":"⋙̸","ngsim":"≵","nGt":"≫⃒","ngt":"≯","ngtr":"≯","nGtv":"≫̸","nharr":"↮","nhArr":"⇎","nhpar":"⫲","ni":"∋","nis":"⋼","nisd":"⋺","niv":"∋","NJcy":"Њ","njcy":"њ","nlarr":"↚","nlArr":"⇍","nldr":"‥","nlE":"≦̸","nle":"≰","nleftarrow":"↚","nLeftarrow":"⇍","nleftrightarrow":"↮","nLeftrightarrow":"⇎","nleq":"≰","nleqq":"≦̸","nleqslant":"⩽̸","nles":"⩽̸","nless":"≮","nLl":"⋘̸","nlsim":"≴","nLt":"≪⃒","nlt":"≮","nltri":"⋪","nltrie":"⋬","nLtv":"≪̸","nmid":"∤","NoBreak":"⁠","NonBreakingSpace":" ","nopf":"𝕟","Nopf":"ℕ","Not":"⫬","not":"¬","NotCongruent":"≢","NotCupCap":"≭","NotDoubleVerticalBar":"∦","NotElement":"∉","NotEqual":"≠","NotEqualTilde":"≂̸","NotExists":"∄","NotGreater":"≯","NotGreaterEqual":"≱","NotGreaterFullEqual":"≧̸","NotGreaterGreater":"≫̸","NotGreaterLess":"≹","NotGreaterSlantEqual":"⩾̸","NotGreaterTilde":"≵","NotHumpDownHump":"≎̸","NotHumpEqual":"≏̸","notin":"∉","notindot":"⋵̸","notinE":"⋹̸","notinva":"∉","notinvb":"⋷","notinvc":"⋶","NotLeftTriangleBar":"⧏̸","NotLeftTriangle":"⋪","NotLeftTriangleEqual":"⋬","NotLess":"≮","NotLessEqual":"≰","NotLessGreater":"≸","NotLessLess":"≪̸","NotLessSlantEqual":"⩽̸","NotLessTilde":"≴","NotNestedGreaterGreater":"⪢̸","NotNestedLessLess":"⪡̸","notni":"∌","notniva":"∌","notnivb":"⋾","notnivc":"⋽","NotPrecedes":"⊀","NotPrecedesEqual":"⪯̸","NotPrecedesSlantEqual":"⋠","NotReverseElement":"∌","NotRightTriangleBar":"⧐̸","NotRightTriangle":"⋫","NotRightTriangleEqual":"⋭","NotSquareSubset":"⊏̸","NotSquareSubsetEqual":"⋢","NotSquareSuperset":"⊐̸","NotSquareSupersetEqual":"⋣","NotSubset":"⊂⃒","NotSubsetEqual":"⊈","NotSucceeds":"⊁","NotSucceedsEqual":"⪰̸","NotSucceedsSlantEqual":"⋡","NotSucceedsTilde":"≿̸","NotSuperset":"⊃⃒","NotSupersetEqual":"⊉","NotTilde":"≁","NotTildeEqual":"≄","NotTildeFullEqual":"≇","NotTildeTilde":"≉","NotVerticalBar":"∤","nparallel":"∦","npar":"∦","nparsl":"⫽⃥","npart":"∂̸","npolint":"⨔","npr":"⊀","nprcue":"⋠","nprec":"⊀","npreceq":"⪯̸","npre":"⪯̸","nrarrc":"⤳̸","nrarr":"↛","nrArr":"⇏","nrarrw":"↝̸","nrightarrow":"↛","nRightarrow":"⇏","nrtri":"⋫","nrtrie":"⋭","nsc":"⊁","nsccue":"⋡","nsce":"⪰̸","Nscr":"𝒩","nscr":"𝓃","nshortmid":"∤","nshortparallel":"∦","nsim":"≁","nsime":"≄","nsimeq":"≄","nsmid":"∤","nspar":"∦","nsqsube":"⋢","nsqsupe":"⋣","nsub":"⊄","nsubE":"⫅̸","nsube":"⊈","nsubset":"⊂⃒","nsubseteq":"⊈","nsubseteqq":"⫅̸","nsucc":"⊁","nsucceq":"⪰̸","nsup":"⊅","nsupE":"⫆̸","nsupe":"⊉","nsupset":"⊃⃒","nsupseteq":"⊉","nsupseteqq":"⫆̸","ntgl":"≹","Ntilde":"Ñ","ntilde":"ñ","ntlg":"≸","ntriangleleft":"⋪","ntrianglelefteq":"⋬","ntriangleright":"⋫","ntrianglerighteq":"⋭","Nu":"Ν","nu":"ν","num":"#","numero":"№","numsp":" ","nvap":"≍⃒","nvdash":"⊬","nvDash":"⊭","nVdash":"⊮","nVDash":"⊯","nvge":"≥⃒","nvgt":">⃒","nvHarr":"⤄","nvinfin":"⧞","nvlArr":"⤂","nvle":"≤⃒","nvlt":"<⃒","nvltrie":"⊴⃒","nvrArr":"⤃","nvrtrie":"⊵⃒","nvsim":"∼⃒","nwarhk":"⤣","nwarr":"↖","nwArr":"⇖","nwarrow":"↖","nwnear":"⤧","Oacute":"Ó","oacute":"ó","oast":"⊛","Ocirc":"Ô","ocirc":"ô","ocir":"⊚","Ocy":"О","ocy":"о","odash":"⊝","Odblac":"Ő","odblac":"ő","odiv":"⨸","odot":"⊙","odsold":"⦼","OElig":"Œ","oelig":"œ","ofcir":"⦿","Ofr":"𝔒","ofr":"𝔬","ogon":"˛","Ograve":"Ò","ograve":"ò","ogt":"⧁","ohbar":"⦵","ohm":"Ω","oint":"∮","olarr":"↺","olcir":"⦾","olcross":"⦻","oline":"‾","olt":"⧀","Omacr":"Ō","omacr":"ō","Omega":"Ω","omega":"ω","Omicron":"Ο","omicron":"ο","omid":"⦶","ominus":"⊖","Oopf":"𝕆","oopf":"𝕠","opar":"⦷","OpenCurlyDoubleQuote":"“","OpenCurlyQuote":"‘","operp":"⦹","oplus":"⊕","orarr":"↻","Or":"⩔","or":"∨","ord":"⩝","order":"ℴ","orderof":"ℴ","ordf":"ª","ordm":"º","origof":"⊶","oror":"⩖","orslope":"⩗","orv":"⩛","oS":"Ⓢ","Oscr":"𝒪","oscr":"ℴ","Oslash":"Ø","oslash":"ø","osol":"⊘","Otilde":"Õ","otilde":"õ","otimesas":"⨶","Otimes":"⨷","otimes":"⊗","Ouml":"Ö","ouml":"ö","ovbar":"⌽","OverBar":"‾","OverBrace":"⏞","OverBracket":"⎴","OverParenthesis":"⏜","para":"¶","parallel":"∥","par":"∥","parsim":"⫳","parsl":"⫽","part":"∂","PartialD":"∂","Pcy":"П","pcy":"п","percnt":"%","period":".","permil":"‰","perp":"⊥","pertenk":"‱","Pfr":"𝔓","pfr":"𝔭","Phi":"Φ","phi":"φ","phiv":"ϕ","phmmat":"ℳ","phone":"☎","Pi":"Π","pi":"π","pitchfork":"⋔","piv":"ϖ","planck":"ℏ","planckh":"ℎ","plankv":"ℏ","plusacir":"⨣","plusb":"⊞","pluscir":"⨢","plus":"+","plusdo":"∔","plusdu":"⨥","pluse":"⩲","PlusMinus":"±","plusmn":"±","plussim":"⨦","plustwo":"⨧","pm":"±","Poincareplane":"ℌ","pointint":"⨕","popf":"𝕡","Popf":"ℙ","pound":"£","prap":"⪷","Pr":"⪻","pr":"≺","prcue":"≼","precapprox":"⪷","prec":"≺","preccurlyeq":"≼","Precedes":"≺","PrecedesEqual":"⪯","PrecedesSlantEqual":"≼","PrecedesTilde":"≾","preceq":"⪯","precnapprox":"⪹","precneqq":"⪵","precnsim":"⋨","pre":"⪯","prE":"⪳","precsim":"≾","prime":"′","Prime":"″","primes":"ℙ","prnap":"⪹","prnE":"⪵","prnsim":"⋨","prod":"∏","Product":"∏","profalar":"⌮","profline":"⌒","profsurf":"⌓","prop":"∝","Proportional":"∝","Proportion":"∷","propto":"∝","prsim":"≾","prurel":"⊰","Pscr":"𝒫","pscr":"𝓅","Psi":"Ψ","psi":"ψ","puncsp":" ","Qfr":"𝔔","qfr":"𝔮","qint":"⨌","qopf":"𝕢","Qopf":"ℚ","qprime":"⁗","Qscr":"𝒬","qscr":"𝓆","quaternions":"ℍ","quatint":"⨖","quest":"?","questeq":"≟","quot":"\\"","QUOT":"\\"","rAarr":"⇛","race":"∽̱","Racute":"Ŕ","racute":"ŕ","radic":"√","raemptyv":"⦳","rang":"⟩","Rang":"⟫","rangd":"⦒","range":"⦥","rangle":"⟩","raquo":"»","rarrap":"⥵","rarrb":"⇥","rarrbfs":"⤠","rarrc":"⤳","rarr":"→","Rarr":"↠","rArr":"⇒","rarrfs":"⤞","rarrhk":"↪","rarrlp":"↬","rarrpl":"⥅","rarrsim":"⥴","Rarrtl":"⤖","rarrtl":"↣","rarrw":"↝","ratail":"⤚","rAtail":"⤜","ratio":"∶","rationals":"ℚ","rbarr":"⤍","rBarr":"⤏","RBarr":"⤐","rbbrk":"❳","rbrace":"}","rbrack":"]","rbrke":"⦌","rbrksld":"⦎","rbrkslu":"⦐","Rcaron":"Ř","rcaron":"ř","Rcedil":"Ŗ","rcedil":"ŗ","rceil":"⌉","rcub":"}","Rcy":"Р","rcy":"р","rdca":"⤷","rdldhar":"⥩","rdquo":"”","rdquor":"”","rdsh":"↳","real":"ℜ","realine":"ℛ","realpart":"ℜ","reals":"ℝ","Re":"ℜ","rect":"▭","reg":"®","REG":"®","ReverseElement":"∋","ReverseEquilibrium":"⇋","ReverseUpEquilibrium":"⥯","rfisht":"⥽","rfloor":"⌋","rfr":"𝔯","Rfr":"ℜ","rHar":"⥤","rhard":"⇁","rharu":"⇀","rharul":"⥬","Rho":"Ρ","rho":"ρ","rhov":"ϱ","RightAngleBracket":"⟩","RightArrowBar":"⇥","rightarrow":"→","RightArrow":"→","Rightarrow":"⇒","RightArrowLeftArrow":"⇄","rightarrowtail":"↣","RightCeiling":"⌉","RightDoubleBracket":"⟧","RightDownTeeVector":"⥝","RightDownVectorBar":"⥕","RightDownVector":"⇂","RightFloor":"⌋","rightharpoondown":"⇁","rightharpoonup":"⇀","rightleftarrows":"⇄","rightleftharpoons":"⇌","rightrightarrows":"⇉","rightsquigarrow":"↝","RightTeeArrow":"↦","RightTee":"⊢","RightTeeVector":"⥛","rightthreetimes":"⋌","RightTriangleBar":"⧐","RightTriangle":"⊳","RightTriangleEqual":"⊵","RightUpDownVector":"⥏","RightUpTeeVector":"⥜","RightUpVectorBar":"⥔","RightUpVector":"↾","RightVectorBar":"⥓","RightVector":"⇀","ring":"˚","risingdotseq":"≓","rlarr":"⇄","rlhar":"⇌","rlm":"‏","rmoustache":"⎱","rmoust":"⎱","rnmid":"⫮","roang":"⟭","roarr":"⇾","robrk":"⟧","ropar":"⦆","ropf":"𝕣","Ropf":"ℝ","roplus":"⨮","rotimes":"⨵","RoundImplies":"⥰","rpar":")","rpargt":"⦔","rppolint":"⨒","rrarr":"⇉","Rrightarrow":"⇛","rsaquo":"›","rscr":"𝓇","Rscr":"ℛ","rsh":"↱","Rsh":"↱","rsqb":"]","rsquo":"’","rsquor":"’","rthree":"⋌","rtimes":"⋊","rtri":"▹","rtrie":"⊵","rtrif":"▸","rtriltri":"⧎","RuleDelayed":"⧴","ruluhar":"⥨","rx":"℞","Sacute":"Ś","sacute":"ś","sbquo":"‚","scap":"⪸","Scaron":"Š","scaron":"š","Sc":"⪼","sc":"≻","sccue":"≽","sce":"⪰","scE":"⪴","Scedil":"Ş","scedil":"ş","Scirc":"Ŝ","scirc":"ŝ","scnap":"⪺","scnE":"⪶","scnsim":"⋩","scpolint":"⨓","scsim":"≿","Scy":"С","scy":"с","sdotb":"⊡","sdot":"⋅","sdote":"⩦","searhk":"⤥","searr":"↘","seArr":"⇘","searrow":"↘","sect":"§","semi":";","seswar":"⤩","setminus":"∖","setmn":"∖","sext":"✶","Sfr":"𝔖","sfr":"𝔰","sfrown":"⌢","sharp":"♯","SHCHcy":"Щ","shchcy":"щ","SHcy":"Ш","shcy":"ш","ShortDownArrow":"↓","ShortLeftArrow":"←","shortmid":"∣","shortparallel":"∥","ShortRightArrow":"→","ShortUpArrow":"↑","shy":"­","Sigma":"Σ","sigma":"σ","sigmaf":"ς","sigmav":"ς","sim":"∼","simdot":"⩪","sime":"≃","simeq":"≃","simg":"⪞","simgE":"⪠","siml":"⪝","simlE":"⪟","simne":"≆","simplus":"⨤","simrarr":"⥲","slarr":"←","SmallCircle":"∘","smallsetminus":"∖","smashp":"⨳","smeparsl":"⧤","smid":"∣","smile":"⌣","smt":"⪪","smte":"⪬","smtes":"⪬︀","SOFTcy":"Ь","softcy":"ь","solbar":"⌿","solb":"⧄","sol":"/","Sopf":"𝕊","sopf":"𝕤","spades":"♠","spadesuit":"♠","spar":"∥","sqcap":"⊓","sqcaps":"⊓︀","sqcup":"⊔","sqcups":"⊔︀","Sqrt":"√","sqsub":"⊏","sqsube":"⊑","sqsubset":"⊏","sqsubseteq":"⊑","sqsup":"⊐","sqsupe":"⊒","sqsupset":"⊐","sqsupseteq":"⊒","square":"□","Square":"□","SquareIntersection":"⊓","SquareSubset":"⊏","SquareSubsetEqual":"⊑","SquareSuperset":"⊐","SquareSupersetEqual":"⊒","SquareUnion":"⊔","squarf":"▪","squ":"□","squf":"▪","srarr":"→","Sscr":"𝒮","sscr":"𝓈","ssetmn":"∖","ssmile":"⌣","sstarf":"⋆","Star":"⋆","star":"☆","starf":"★","straightepsilon":"ϵ","straightphi":"ϕ","strns":"¯","sub":"⊂","Sub":"⋐","subdot":"⪽","subE":"⫅","sube":"⊆","subedot":"⫃","submult":"⫁","subnE":"⫋","subne":"⊊","subplus":"⪿","subrarr":"⥹","subset":"⊂","Subset":"⋐","subseteq":"⊆","subseteqq":"⫅","SubsetEqual":"⊆","subsetneq":"⊊","subsetneqq":"⫋","subsim":"⫇","subsub":"⫕","subsup":"⫓","succapprox":"⪸","succ":"≻","succcurlyeq":"≽","Succeeds":"≻","SucceedsEqual":"⪰","SucceedsSlantEqual":"≽","SucceedsTilde":"≿","succeq":"⪰","succnapprox":"⪺","succneqq":"⪶","succnsim":"⋩","succsim":"≿","SuchThat":"∋","sum":"∑","Sum":"∑","sung":"♪","sup1":"¹","sup2":"²","sup3":"³","sup":"⊃","Sup":"⋑","supdot":"⪾","supdsub":"⫘","supE":"⫆","supe":"⊇","supedot":"⫄","Superset":"⊃","SupersetEqual":"⊇","suphsol":"⟉","suphsub":"⫗","suplarr":"⥻","supmult":"⫂","supnE":"⫌","supne":"⊋","supplus":"⫀","supset":"⊃","Supset":"⋑","supseteq":"⊇","supseteqq":"⫆","supsetneq":"⊋","supsetneqq":"⫌","supsim":"⫈","supsub":"⫔","supsup":"⫖","swarhk":"⤦","swarr":"↙","swArr":"⇙","swarrow":"↙","swnwar":"⤪","szlig":"ß","Tab":"\\t","target":"⌖","Tau":"Τ","tau":"τ","tbrk":"⎴","Tcaron":"Ť","tcaron":"ť","Tcedil":"Ţ","tcedil":"ţ","Tcy":"Т","tcy":"т","tdot":"⃛","telrec":"⌕","Tfr":"𝔗","tfr":"𝔱","there4":"∴","therefore":"∴","Therefore":"∴","Theta":"Θ","theta":"θ","thetasym":"ϑ","thetav":"ϑ","thickapprox":"≈","thicksim":"∼","ThickSpace":"  ","ThinSpace":" ","thinsp":" ","thkap":"≈","thksim":"∼","THORN":"Þ","thorn":"þ","tilde":"˜","Tilde":"∼","TildeEqual":"≃","TildeFullEqual":"≅","TildeTilde":"≈","timesbar":"⨱","timesb":"⊠","times":"×","timesd":"⨰","tint":"∭","toea":"⤨","topbot":"⌶","topcir":"⫱","top":"⊤","Topf":"𝕋","topf":"𝕥","topfork":"⫚","tosa":"⤩","tprime":"‴","trade":"™","TRADE":"™","triangle":"▵","triangledown":"▿","triangleleft":"◃","trianglelefteq":"⊴","triangleq":"≜","triangleright":"▹","trianglerighteq":"⊵","tridot":"◬","trie":"≜","triminus":"⨺","TripleDot":"⃛","triplus":"⨹","trisb":"⧍","tritime":"⨻","trpezium":"⏢","Tscr":"𝒯","tscr":"𝓉","TScy":"Ц","tscy":"ц","TSHcy":"Ћ","tshcy":"ћ","Tstrok":"Ŧ","tstrok":"ŧ","twixt":"≬","twoheadleftarrow":"↞","twoheadrightarrow":"↠","Uacute":"Ú","uacute":"ú","uarr":"↑","Uarr":"↟","uArr":"⇑","Uarrocir":"⥉","Ubrcy":"Ў","ubrcy":"ў","Ubreve":"Ŭ","ubreve":"ŭ","Ucirc":"Û","ucirc":"û","Ucy":"У","ucy":"у","udarr":"⇅","Udblac":"Ű","udblac":"ű","udhar":"⥮","ufisht":"⥾","Ufr":"𝔘","ufr":"𝔲","Ugrave":"Ù","ugrave":"ù","uHar":"⥣","uharl":"↿","uharr":"↾","uhblk":"▀","ulcorn":"⌜","ulcorner":"⌜","ulcrop":"⌏","ultri":"◸","Umacr":"Ū","umacr":"ū","uml":"¨","UnderBar":"_","UnderBrace":"⏟","UnderBracket":"⎵","UnderParenthesis":"⏝","Union":"⋃","UnionPlus":"⊎","Uogon":"Ų","uogon":"ų","Uopf":"𝕌","uopf":"𝕦","UpArrowBar":"⤒","uparrow":"↑","UpArrow":"↑","Uparrow":"⇑","UpArrowDownArrow":"⇅","updownarrow":"↕","UpDownArrow":"↕","Updownarrow":"⇕","UpEquilibrium":"⥮","upharpoonleft":"↿","upharpoonright":"↾","uplus":"⊎","UpperLeftArrow":"↖","UpperRightArrow":"↗","upsi":"υ","Upsi":"ϒ","upsih":"ϒ","Upsilon":"Υ","upsilon":"υ","UpTeeArrow":"↥","UpTee":"⊥","upuparrows":"⇈","urcorn":"⌝","urcorner":"⌝","urcrop":"⌎","Uring":"Ů","uring":"ů","urtri":"◹","Uscr":"𝒰","uscr":"𝓊","utdot":"⋰","Utilde":"Ũ","utilde":"ũ","utri":"▵","utrif":"▴","uuarr":"⇈","Uuml":"Ü","uuml":"ü","uwangle":"⦧","vangrt":"⦜","varepsilon":"ϵ","varkappa":"ϰ","varnothing":"∅","varphi":"ϕ","varpi":"ϖ","varpropto":"∝","varr":"↕","vArr":"⇕","varrho":"ϱ","varsigma":"ς","varsubsetneq":"⊊︀","varsubsetneqq":"⫋︀","varsupsetneq":"⊋︀","varsupsetneqq":"⫌︀","vartheta":"ϑ","vartriangleleft":"⊲","vartriangleright":"⊳","vBar":"⫨","Vbar":"⫫","vBarv":"⫩","Vcy":"В","vcy":"в","vdash":"⊢","vDash":"⊨","Vdash":"⊩","VDash":"⊫","Vdashl":"⫦","veebar":"⊻","vee":"∨","Vee":"⋁","veeeq":"≚","vellip":"⋮","verbar":"|","Verbar":"‖","vert":"|","Vert":"‖","VerticalBar":"∣","VerticalLine":"|","VerticalSeparator":"❘","VerticalTilde":"≀","VeryThinSpace":" ","Vfr":"𝔙","vfr":"𝔳","vltri":"⊲","vnsub":"⊂⃒","vnsup":"⊃⃒","Vopf":"𝕍","vopf":"𝕧","vprop":"∝","vrtri":"⊳","Vscr":"𝒱","vscr":"𝓋","vsubnE":"⫋︀","vsubne":"⊊︀","vsupnE":"⫌︀","vsupne":"⊋︀","Vvdash":"⊪","vzigzag":"⦚","Wcirc":"Ŵ","wcirc":"ŵ","wedbar":"⩟","wedge":"∧","Wedge":"⋀","wedgeq":"≙","weierp":"℘","Wfr":"𝔚","wfr":"𝔴","Wopf":"𝕎","wopf":"𝕨","wp":"℘","wr":"≀","wreath":"≀","Wscr":"𝒲","wscr":"𝓌","xcap":"⋂","xcirc":"◯","xcup":"⋃","xdtri":"▽","Xfr":"𝔛","xfr":"𝔵","xharr":"⟷","xhArr":"⟺","Xi":"Ξ","xi":"ξ","xlarr":"⟵","xlArr":"⟸","xmap":"⟼","xnis":"⋻","xodot":"⨀","Xopf":"𝕏","xopf":"𝕩","xoplus":"⨁","xotime":"⨂","xrarr":"⟶","xrArr":"⟹","Xscr":"𝒳","xscr":"𝓍","xsqcup":"⨆","xuplus":"⨄","xutri":"△","xvee":"⋁","xwedge":"⋀","Yacute":"Ý","yacute":"ý","YAcy":"Я","yacy":"я","Ycirc":"Ŷ","ycirc":"ŷ","Ycy":"Ы","ycy":"ы","yen":"¥","Yfr":"𝔜","yfr":"𝔶","YIcy":"Ї","yicy":"ї","Yopf":"𝕐","yopf":"𝕪","Yscr":"𝒴","yscr":"𝓎","YUcy":"Ю","yucy":"ю","yuml":"ÿ","Yuml":"Ÿ","Zacute":"Ź","zacute":"ź","Zcaron":"Ž","zcaron":"ž","Zcy":"З","zcy":"з","Zdot":"Ż","zdot":"ż","zeetrf":"ℨ","ZeroWidthSpace":"​","Zeta":"Ζ","zeta":"ζ","zfr":"𝔷","Zfr":"ℨ","ZHcy":"Ж","zhcy":"ж","zigrarr":"⇝","zopf":"𝕫","Zopf":"ℤ","Zscr":"𝒵","zscr":"𝓏","zwj":"‍","zwnj":"‌"}')},function(e){e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Acirc":"Â","acirc":"â","acute":"´","AElig":"Æ","aelig":"æ","Agrave":"À","agrave":"à","amp":"&","AMP":"&","Aring":"Å","aring":"å","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","brvbar":"¦","Ccedil":"Ç","ccedil":"ç","cedil":"¸","cent":"¢","copy":"©","COPY":"©","curren":"¤","deg":"°","divide":"÷","Eacute":"É","eacute":"é","Ecirc":"Ê","ecirc":"ê","Egrave":"È","egrave":"è","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","frac12":"½","frac14":"¼","frac34":"¾","gt":">","GT":">","Iacute":"Í","iacute":"í","Icirc":"Î","icirc":"î","iexcl":"¡","Igrave":"Ì","igrave":"ì","iquest":"¿","Iuml":"Ï","iuml":"ï","laquo":"«","lt":"<","LT":"<","macr":"¯","micro":"µ","middot":"·","nbsp":" ","not":"¬","Ntilde":"Ñ","ntilde":"ñ","Oacute":"Ó","oacute":"ó","Ocirc":"Ô","ocirc":"ô","Ograve":"Ò","ograve":"ò","ordf":"ª","ordm":"º","Oslash":"Ø","oslash":"ø","Otilde":"Õ","otilde":"õ","Ouml":"Ö","ouml":"ö","para":"¶","plusmn":"±","pound":"£","quot":"\\"","QUOT":"\\"","raquo":"»","reg":"®","REG":"®","sect":"§","shy":"­","sup1":"¹","sup2":"²","sup3":"³","szlig":"ß","THORN":"Þ","thorn":"þ","times":"×","Uacute":"Ú","uacute":"ú","Ucirc":"Û","ucirc":"û","Ugrave":"Ù","ugrave":"ù","uml":"¨","Uuml":"Ü","uuml":"ü","Yacute":"Ý","yacute":"ý","yen":"¥","yuml":"ÿ"}')},function(e){e.exports=JSON.parse('{"amp":"&","apos":"\'","gt":">","lt":"<","quot":"\\""}')},function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var i=r(n(16));t.default=function(e){if(e>=55296&&e<=57343||e>1114111)return"�";e in i.default&&(e=i.default[e]);var t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+String.fromCharCode(e)}},function(e){e.exports=JSON.parse('{"0":65533,"128":8364,"130":8218,"131":402,"132":8222,"133":8230,"134":8224,"135":8225,"136":710,"137":8240,"138":352,"139":8249,"140":338,"142":381,"145":8216,"146":8217,"147":8220,"148":8221,"149":8226,"150":8211,"151":8212,"152":732,"153":8482,"154":353,"155":8250,"156":339,"158":382,"159":376}')},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1),i=function(){function e(e){this.current=e,this.root=e,this.entering=!0}return e.prototype.next=function(){var e=this.current,t=this.entering;if(null===e)return null;var n=r.isContainer(e);return t&&n?e.firstChild?(this.current=e.firstChild,this.entering=!0):this.entering=!1:e===this.root?this.current=null:null===e.next?(this.current=e.parent,this.entering=!1):(this.current=e.next,this.entering=!0),{entering:t,node:e}},e.prototype.resumeAt=function(e,t){this.current=e,this.entering=!0===t},e}();t.default=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(1),o=n(2),a=n(8),s=r.__importDefault(n(19)),l=n(7),c=n(20),u=n(3),d=n(5);t.C_NEWLINE=10;var h="\\\\"+o.ESCAPABLE,f=new RegExp(/[!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u0AF0\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E42\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC9\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDF3C-\uDF3E]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]/),p=new RegExp('^(?:"('+h+'|[^"\\x00])*"|\'('+h+"|[^'\\x00])*'|\\(("+h+"|[^()\\x00])*\\))"),g=/^(?:<(?:[^<>\n\\\x00]|\\.)*>)/,m=new RegExp("^"+o.ESCAPABLE),v=new RegExp("^"+o.ENTITY,"i"),b=/`+/,_=/^`+/,y=/\.\.\./g,C=/--+/g,w=/^<([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)>/,E=/^<[A-Za-z][A-Za-z0-9.+-]{1,31}:[^<>\x00-\x20]*>/i,T=/^ *(?:\n *)?/,N=/^[ \t\n\x0b\x0c\x0d]/,k=/^\s/,S=/ *$/,x=/^ */,L=/^ *(?:\n|$)/,M=/^\[(?:[^\\\[\]]|\\.){0,1000}\]/,A=/^[^\n`\[\]\\!<&*_'"~]+/m,B=function(){function e(e){this.subject="",this.delimiters=null,this.brackets=null,this.pos=0,this.lineStartNum=0,this.lineIdx=0,this.lineOffsets=[0],this.linePosOffset=0,this.refMap={},this.refLinkCandidateMap={},this.refDefCandidateMap={},this.options=e}return e.prototype.sourcepos=function(e,t){var n=this.linePosOffset+this.lineOffsets[this.lineIdx],r=this.lineStartNum+this.lineIdx,i=[r,e+n];return"number"==typeof t?[i,[r,t+n]]:i},e.prototype.nextLine=function(){this.lineIdx+=1,this.linePosOffset=-this.pos},e.prototype.match=function(e){var t=e.exec(this.subject.slice(this.pos));return null===t?null:(this.pos+=t.index+t[0].length,t[0])},e.prototype.peek=function(){return this.pos<this.subject.length?this.subject.charCodeAt(this.pos):-1},e.prototype.spnl=function(){return this.match(T),!0},e.prototype.parseBackticks=function(e){var t=this.pos+1,n=this.match(_);if(null===n)return!1;for(var r,o=this.pos;null!==(r=this.match(b));)if(r===n){var a=this.subject.slice(o,this.pos-n.length),s=this.sourcepos(t,this.pos),l=a.split("\n");if(l.length>1){var c=u.last(l);this.lineIdx+=l.length-1,this.linePosOffset=-(this.pos-c.length-n.length),s[1]=this.sourcepos(this.pos),a=l.join(" ")}var d=i.createNode("code",s);return a.length>0&&null!==a.match(/[^ ]/)&&" "==a[0]&&" "==a[a.length-1]?d.literal=a.slice(1,a.length-1):d.literal=a,d.tickCount=n.length,e.appendChild(d),!0}return this.pos=o,e.appendChild(i.text(n,this.sourcepos(t,this.pos-1))),!0},e.prototype.parseBackslash=function(e){var n,r=this.subject;this.pos+=1;var o=this.pos;return this.peek()===t.C_NEWLINE?(this.pos+=1,n=i.createNode("linebreak",this.sourcepos(this.pos-1,this.pos)),e.appendChild(n),this.nextLine()):m.test(r.charAt(this.pos))?(e.appendChild(i.text(r.charAt(this.pos),this.sourcepos(o,this.pos))),this.pos+=1):e.appendChild(i.text("\\",this.sourcepos(o,o))),!0},e.prototype.parseAutolink=function(e){var t,n,r,a=this.pos+1;return(t=this.match(w))?(n=t.slice(1,t.length-1),(r=i.createNode("link",this.sourcepos(a,this.pos))).destination=o.normalizeURI("mailto:"+n),r.title="",r.appendChild(i.text(n,this.sourcepos(a+1,this.pos-1))),e.appendChild(r),!0):!!(t=this.match(E))&&(n=t.slice(1,t.length-1),(r=i.createNode("link",this.sourcepos(a,this.pos))).destination=o.normalizeURI(n),r.title="",r.appendChild(i.text(n,this.sourcepos(a+1,this.pos-1))),e.appendChild(r),!0)},e.prototype.parseHtmlTag=function(e){var t=this.pos+1,n=this.match(a.reHtmlTag);if(null===n)return!1;var r=i.createNode("htmlInline",this.sourcepos(t,this.pos));return r.literal=n,e.appendChild(r),!0},e.prototype.scanDelims=function(e){var t=0,n=this.pos;if(39===e||34===e)t++,this.pos++;else for(;this.peek()===e;)t++,this.pos++;if(0===t||t<2&&126===e)return this.pos=n,null;var r,i=0===n?"\n":this.subject.charAt(n-1),o=this.peek();r=-1===o?"\n":s.default(o);var a,l,c=k.test(r),u=f.test(r),d=k.test(i),h=f.test(i),p=!c&&(!u||d||h),g=!d&&(!h||c||u);return 95===e?(a=p&&(!g||h),l=g&&(!p||u)):39===e||34===e?(a=p&&!g,l=g):(a=p,l=g),this.pos=n,{numdelims:t,canOpen:a,canClose:l}},e.prototype.handleDelim=function(e,t){var n=this.scanDelims(e);if(!n)return!1;var r,o=n.numdelims,a=this.pos+1;this.pos+=o,r=39===e?"’":34===e?"“":this.subject.slice(a-1,this.pos);var s=i.text(r,this.sourcepos(a,this.pos));return t.appendChild(s),(n.canOpen||n.canClose)&&(this.options.smart||39!==e&&34!==e)&&(this.delimiters={cc:e,numdelims:o,origdelims:o,node:s,previous:this.delimiters,next:null,canOpen:n.canOpen,canClose:n.canClose},this.delimiters.previous&&(this.delimiters.previous.next=this.delimiters)),!0},e.prototype.removeDelimiter=function(e){null!==e.previous&&(e.previous.next=e.next),null===e.next?this.delimiters=e.previous:e.next.previous=e.previous},e.prototype.removeDelimitersBetween=function(e,t){e.next!==t&&(e.next=t,t.previous=e)},e.prototype.processEmphasis=function(e){var t,n,r,o,a,s,l,c=!1,u=((t={})[95]=[e,e,e],t[42]=[e,e,e],t[39]=[e],t[34]=[e],t[126]=[e],t);for(r=this.delimiters;null!==r&&r.previous!==e;)r=r.previous;for(;null!==r;){var d=r.cc,h=95===d||42===d;if(r.canClose){for(n=r.previous,l=!1;null!==n&&n!==e&&n!==u[d][h?r.origdelims%3:0];){if(c=h&&(r.canOpen||n.canClose)&&r.origdelims%3!=0&&(n.origdelims+r.origdelims)%3==0,n.cc===r.cc&&n.canOpen&&!c){l=!0;break}n=n.previous}if(o=r,h||126===d)if(l){if(n){var f=r.numdelims>=2&&n.numdelims>=2?2:1,p=h?0:1;a=n.node,s=r.node;var g=i.createNode(h?1===f?"emph":"strong":"strike"),m=a.sourcepos[1],v=s.sourcepos[0];g.sourcepos=[[m[0],m[1]-f+1],[v[0],v[1]+f-1]],a.sourcepos[1][1]-=f,s.sourcepos[0][1]+=f,a.literal=a.literal.slice(f),s.literal=s.literal.slice(f),n.numdelims-=f,r.numdelims-=f;for(var b=a.next,_=void 0;b&&b!==s;)_=b.next,b.unlink(),g.appendChild(b),b=_;if(a.insertAfter(g),this.removeDelimitersBetween(n,r),n.numdelims<=p&&(0===n.numdelims&&a.unlink(),this.removeDelimiter(n)),r.numdelims<=p){0===r.numdelims&&s.unlink();var y=r.next;this.removeDelimiter(r),r=y}}}else r=r.next;else 39===d?(r.node.literal="’",l&&(n.node.literal="‘"),r=r.next):34===d&&(r.node.literal="”",l&&(n.node.literal="“"),r=r.next);l||(u[d][h?o.origdelims%3:0]=o.previous,o.canOpen||this.removeDelimiter(o))}else r=r.next}for(;null!==this.delimiters&&this.delimiters!==e;)this.removeDelimiter(this.delimiters)},e.prototype.parseLinkTitle=function(){var e=this.match(p);return null===e?null:o.unescapeString(e.substr(1,e.length-2))},e.prototype.parseLinkDestination=function(){var e=this.match(g);if(null===e){if(60===this.peek())return null;for(var t=this.pos,n=0,r=void 0;-1!==(r=this.peek());)if(92===r&&m.test(this.subject.charAt(this.pos+1)))this.pos+=1,-1!==this.peek()&&(this.pos+=1);else if(40===r)this.pos+=1,n+=1;else if(41===r){if(n<1)break;this.pos+=1,n-=1}else{if(null!==N.exec(s.default(r)))break;this.pos+=1}return this.pos===t&&41!==r||0!==n?null:(e=this.subject.substr(t,this.pos-t),o.normalizeURI(o.unescapeString(e)))}return o.normalizeURI(o.unescapeString(e.substr(1,e.length-2)))},e.prototype.parseLinkLabel=function(){var e=this.match(M);return null===e||e.length>1001?0:e.length},e.prototype.parseOpenBracket=function(e){var t=this.pos;this.pos+=1;var n=i.text("[",this.sourcepos(this.pos,this.pos));return e.appendChild(n),this.addBracket(n,t,!1),!0},e.prototype.parseBang=function(e){var t=this.pos;if(this.pos+=1,91===this.peek()){this.pos+=1;var n=i.text("![",this.sourcepos(this.pos-1,this.pos));e.appendChild(n),this.addBracket(n,t+1,!0)}else n=i.text("!",this.sourcepos(this.pos,this.pos)),e.appendChild(n);return!0},e.prototype.parseCloseBracket=function(e){var t=null,n=null,r=!1;this.pos+=1;var o=this.pos,a=this.brackets;if(null===a)return e.appendChild(i.text("]",this.sourcepos(o,o))),!0;if(!a.active)return e.appendChild(i.text("]",this.sourcepos(o,o))),this.removeBracket(),!0;var s=a.image,l=this.pos;40===this.peek()&&(this.pos++,this.spnl()&&null!==(t=this.parseLinkDestination())&&this.spnl()&&(N.test(this.subject.charAt(this.pos-1))&&(n=this.parseLinkTitle()),1)&&this.spnl()&&41===this.peek()?(this.pos+=1,r=!0):this.pos=l);var c="";if(!r){var d=this.pos,h=this.parseLinkLabel();if(h>2?c=this.subject.slice(d,d+h):a.bracketAfter||(c=this.subject.slice(a.index,o)),0===h&&(this.pos=l),c){c=u.normalizeReference(c);var f=this.refMap[c];f&&(t=f.destination,n=f.title,r=!0)}}if(r){var p=i.createNode(s?"image":"link");p.destination=t,p.title=n||"",p.sourcepos=[a.startpos,this.sourcepos(this.pos)];for(var g=a.node.next,m=void 0;g;)m=g.next,g.unlink(),p.appendChild(g),g=m;if(e.appendChild(p),this.processEmphasis(a.previousDelimiter),this.removeBracket(),a.node.unlink(),!s)for(a=this.brackets;null!==a;)a.image||(a.active=!1),a=a.previous;return this.options.referenceDefinition&&(this.refLinkCandidateMap[e.id]={node:e,refLabel:c}),!0}return this.removeBracket(),this.pos=o,e.appendChild(i.text("]",this.sourcepos(o,o))),this.options.referenceDefinition&&(this.refLinkCandidateMap[e.id]={node:e,refLabel:c}),!0},e.prototype.addBracket=function(e,t,n){null!==this.brackets&&(this.brackets.bracketAfter=!0),this.brackets={node:e,startpos:this.sourcepos(t+(n?0:1)),previous:this.brackets,previousDelimiter:this.delimiters,index:t,image:n,active:!0}},e.prototype.removeBracket=function(){this.brackets&&(this.brackets=this.brackets.previous)},e.prototype.parseEntity=function(e){var t,n=this.pos+1;return!!(t=this.match(v))&&(e.appendChild(i.text(l.decodeHTML(t),this.sourcepos(n,this.pos))),!0)},e.prototype.parseString=function(e){var t,n=this.pos+1;if(t=this.match(A)){if(this.options.smart){var r=t.replace(y,"…").replace(C,(function(e){var t=0,n=0;return e.length%3==0?n=e.length/3:e.length%2==0?t=e.length/2:e.length%3==2?(t=1,n=(e.length-2)/3):(t=2,n=(e.length-4)/3),o.repeat("—",n)+o.repeat("–",t)}));e.appendChild(i.text(r,this.sourcepos(n,this.pos)))}else{var a=i.text(t,this.sourcepos(n,this.pos));e.appendChild(a)}return!0}return!1},e.prototype.parseNewline=function(e){this.pos+=1;var t=e.lastChild;if(t&&"text"===t.type&&" "===t.literal[t.literal.length-1]){var n=" "===t.literal[t.literal.length-2],r=t.literal.length;t.literal=t.literal.replace(S,"");var o=r-t.literal.length;t.sourcepos[1][1]-=o,e.appendChild(i.createNode(n?"linebreak":"softbreak",this.sourcepos(this.pos-o,this.pos)))}else e.appendChild(i.createNode("softbreak",this.sourcepos(this.pos,this.pos)));return this.nextLine(),this.match(x),!0},e.prototype.parseReference=function(e,t){if(!this.options.referenceDefinition)return 0;this.subject=e.stringContent,this.pos=0;var n=null,r=this.pos,o=this.parseLinkLabel();if(0===o)return 0;var a=this.subject.substr(0,o);if(58!==this.peek())return this.pos=r,0;this.pos++,this.spnl();var s=this.parseLinkDestination();if(null===s)return this.pos=r,0;var l=this.pos;this.spnl(),this.pos!==l&&(n=this.parseLinkTitle()),null===n&&(n="",this.pos=l);var c=!0;if(null===this.match(L)&&(""===n?c=!1:(n="",this.pos=l,c=null!==this.match(L))),!c)return this.pos=r,0;var h=u.normalizeReference(a);if(""===h)return this.pos=r,0;var f=this.getReferenceDefSourcepos(e);e.sourcepos[0][0]=f[1][0]+1;var p=i.createNode("refDef",f);return p.title=n,p.dest=s,p.label=h,e.insertBefore(p),t[h]?this.refDefCandidateMap[p.id]=p:t[h]=d.createRefDefState(p),this.pos-r},e.prototype.mergeTextNodes=function(e){for(var t,n=[];t=e.next();){var r=t.entering,i=t.node;if(r&&"text"===i.type)n.push(i);else if(1===n.length)n=[];else if(n.length>1){var o=n[0],a=n[n.length-1];o.sourcepos&&a.sourcepos&&(o.sourcepos[1]=a.sourcepos[1]),o.next=a.next,o.next&&(o.next.prev=o);for(var s=1;s<n.length;s+=1)o.literal+=n[s].literal,n[s].unlink();n=[]}}},e.prototype.getReferenceDefSourcepos=function(e){for(var t=e.stringContent.split(/\n|\r\n/),n=!1,r=0,i={line:0,ch:0},o=0;o<t.length;o+=1){var a=t[o];if(N.test(a))break;if(/\:/.test(a)&&0===r){if(n)break;var s=a.indexOf(":")===a.length-1?o+1:o;i={line:s,ch:t[s].length},n=!0}var l=a.match(/'|"/g);if(l&&(r+=l.length),2===r){i={line:o,ch:a.length};break}}return[[e.sourcepos[0][0],e.sourcepos[0][1]],[e.sourcepos[0][0]+i.line,i.ch]]},e.prototype.parseInline=function(e){var n,r=!1,o=this.peek();if(-1===o)return!1;switch(o){case t.C_NEWLINE:r=this.parseNewline(e);break;case 92:r=this.parseBackslash(e);break;case 96:r=this.parseBackticks(e);break;case 42:case 95:case 126:r=this.handleDelim(o,e);break;case 39:case 34:r=!!(null===(n=this.options)||void 0===n?void 0:n.smart)&&this.handleDelim(o,e);break;case 91:r=this.parseOpenBracket(e);break;case 33:r=this.parseBang(e);break;case 93:r=this.parseCloseBracket(e);break;case 60:r=this.parseAutolink(e)||this.parseHtmlTag(e);break;case 38:r=this.parseEntity(e);break;default:r=this.parseString(e)}return r||(this.pos+=1,e.appendChild(i.text(s.default(o),this.sourcepos(this.pos,this.pos+1)))),!0},e.prototype.parse=function(e){for(this.subject=e.stringContent.trim(),this.pos=0,this.delimiters=null,this.brackets=null,this.lineOffsets=e.lineOffsets||[0],this.lineIdx=0,this.linePosOffset=0,this.lineStartNum=e.sourcepos[0][0],i.isHeading(e)&&(this.lineOffsets[0]+=e.level+1);this.parseInline(e););e.stringContent=null,this.processEmphasis(null),this.mergeTextNodes(e.walker());var t=this.options,n=t.extendedAutolinks,r=t.customParser;if(n&&c.convertExtAutoLinks(e.walker(),n),r&&e.firstChild)for(var o,a=e.firstChild.walker();o=a.next();){var s=o.node,l=o.entering;r[s.type]&&r[s.type](s,{entering:l})}},e}();t.InlineParser=B},function(e,t,n){"use strict";
/*! http://mths.be/fromcodepoint v0.2.1 by @mathias */var r;if(Object.defineProperty(t,"__esModule",{value:!0}),String.fromCodePoint)r=function(e){try{return String.fromCodePoint(e)}catch(e){if(e instanceof RangeError)return String.fromCharCode(65533);throw e}};else{var i=String.fromCharCode,o=Math.floor;r=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n,r,a=16384,s=[],l=-1,c=e.length;if(!c)return"";for(var u="";++l<c;){var d=Number(e[l]);if(!isFinite(d)||d<0||d>1114111||o(d)!==d)return String.fromCharCode(65533);d<=65535?s.push(d):(n=55296+((d-=65536)>>10),r=d%1024+56320,s.push(n,r)),(l+1===c||s.length>a)&&(u+=i.apply(void 0,s),s.length=0)}return u}}t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(1);function o(e){var t=/\)+$/.exec(e);if(t){for(var n=0,r=0,i=e;r<i.length;r++){var o=i[r];"("===o?n<0?n=1:n+=1:")"===o&&(n-=1)}if(n<0){var a=Math.min(-n,t[0].length);return e.substring(0,e.length-a)}}return e}function a(e){return e.replace(/&[A-Za-z0-9]+;$/,"")}function s(e){for(var t,n=new RegExp("[\\w.+-]+@(?:[\\w-]+\\.)+[\\w-]+","g"),r=[];t=n.exec(e);){var i=t[0];/[_-]+$/.test(i)||r.push({text:i,range:[t.index,t.index+i.length-1],url:"mailto:"+i})}return r}function l(e){for(var t,n=new RegExp("(www|https?://).(?:[w-]+.)*[A-Za-z0-9-]+.[A-Za-z0-9-]+[^<\\s]*[^<?!.,:*_?~\\s]","g"),r=[];t=n.exec(e);){var i=a(o(t[0])),s="www"===t[1]?"http://":"";r.push({text:i,range:[t.index,t.index+i.length-1],url:""+s+i})}return r}function c(e){return r.__spreadArrays(l(e),s(e)).sort((function(e,t){return e.range[0]-t.range[0]}))}t.parseEmailLink=s,t.parseUrlLink=l,t.convertExtAutoLinks=function(e,t){var n;"boolean"==typeof t&&(t=c);for(var r=function(){var e=n.entering,r=n.node;if(e&&"text"===r.type&&"link"!==r.parent.type){var o=r.literal,a=t(o);if(!a||!a.length)return"continue";for(var s=0,l=r.sourcepos[0],c=l[0],u=l[1],d=function(e,t){return[[c,u+e],[c,u+t]]},h=[],f=0,p=a;f<p.length;f++){var g=p[f],m=g.range,v=g.url,b=g.text;m[0]>s&&h.push(i.text(o.substring(s,m[0]),d(s,m[0]-1)));var _=i.createNode("link",d.apply(void 0,m));_.appendChild(i.text(b,d.apply(void 0,m))),_.destination=v,_.extendedAutolink=!0,h.push(_),s=m[1]+1}s<o.length&&h.push(i.text(o.substring(s),d(s,o.length-1)));for(var y=0,C=h;y<C.length;y++){var w=C[y];r.insertBefore(w)}r.unlink()}};n=e.next();)r()}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(22),i=n(23),o=n(4),a=n(2),s={continue:function(){return 0},finalize:function(e,t){for(var n=t.firstChild;n;){if(o.endsWithBlankLine(n)&&n.next){t.listData.tight=!1;break}for(var r=n.firstChild;r;){if(o.endsWithBlankLine(r)&&(n.next||r.next)){t.listData.tight=!1;break}r=r.next}n=n.next}},canContain:function(e){return"item"===e},acceptsLines:!1},l={continue:function(e){var t=e.currentLine;return e.indented||o.peek(t,e.nextNonspace)!==o.C_GREATERTHAN?1:(e.advanceNextNonspace(),e.advanceOffset(1,!1),o.isSpaceOrTab(o.peek(t,e.offset))&&e.advanceOffset(1,!0),0)},finalize:function(){},canContain:function(e){return"item"!==e},acceptsLines:!1},c={continue:function(e,t){if(e.blank){if(null===t.firstChild)return 1;e.advanceNextNonspace()}else{if(!(e.indent>=t.listData.markerOffset+t.listData.padding))return 1;e.advanceOffset(t.listData.markerOffset+t.listData.padding,!0)}return 0},finalize:r.taskListItemFinalize,canContain:function(e){return"item"!==e},acceptsLines:!1},u={continue:function(e,t){var n=e.currentLine,r=e.indent;if(t.isFenced){var i=r<=3&&n.charAt(e.nextNonspace)===t.fenceChar&&n.slice(e.nextNonspace).match(o.reClosingCodeFence);if(i&&i[0].length>=t.fenceLength)return e.lastLineLength=e.offset+r+i[0].length,e.finalize(t,e.lineNumber),2;for(var a=t.fenceOffset;a>0&&o.isSpaceOrTab(o.peek(n,e.offset));)e.advanceOffset(1,!0),a--}else if(r>=o.CODE_INDENT)e.advanceOffset(o.CODE_INDENT,!0);else{if(!e.blank)return 1;e.advanceNextNonspace()}return 0},finalize:function(e,t){var n;if(null!==t.stringContent){if(t.isFenced){var r=t.stringContent,i=r.indexOf("\n"),o=r.slice(0,i),s=r.slice(i+1),l=o.match(/^(\s*)(.*)/);t.infoPadding=l[1].length,t.info=a.unescapeString(l[2].trim()),t.literal=s}else t.literal=null===(n=t.stringContent)||void 0===n?void 0:n.replace(/(\n *)+$/,"\n");t.stringContent=null}},canContain:function(){return!1},acceptsLines:!0},d={continue:function(e){return e.blank?1:0},finalize:function(e,t){if(null!==t.stringContent){for(var n,r=!1;o.peek(t.stringContent,0)===o.C_OPEN_BRACKET&&(n=e.inlineParser.parseReference(t,e.refMap));)t.stringContent=t.stringContent.slice(n),r=!0;r&&o.isBlank(t.stringContent)&&t.unlink()}},canContain:function(){return!1},acceptsLines:!0};t.blockHandlers={document:{continue:function(){return 0},finalize:function(){},canContain:function(e){return"item"!==e},acceptsLines:!1},list:s,blockQuote:l,item:c,heading:{continue:function(){return 1},finalize:function(){},canContain:function(){return!1},acceptsLines:!1},thematicBreak:{continue:function(){return 1},finalize:function(){},canContain:function(){return!1},acceptsLines:!1},codeBlock:u,htmlBlock:{continue:function(e,t){return!e.blank||6!==t.htmlBlockType&&7!==t.htmlBlockType?0:1},finalize:function(e,t){var n;t.literal=(null===(n=t.stringContent)||void 0===n?void 0:n.replace(/(\n *)+$/,""))||null,t.stringContent=null},canContain:function(){return!1},acceptsLines:!0},paragraph:d,table:i.table,tableBody:i.tableBody,tableHead:i.tableHead,tableRow:i.tableRow,tableCell:i.tableCell,tableDelimRow:i.tableDelimRow,tableDelimCell:i.tableDelimCell,refDef:{continue:function(){return 1},finalize:function(){},canContain:function(){return!1},acceptsLines:!0}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=/^\[([ \txX])\][ \t]+/;t.taskListItemFinalize=function(e,t){if(t.firstChild&&"paragraph"===t.firstChild.type){var n=t.firstChild,i=n.stringContent.match(r);if(i){var o=i[0].length;n.stringContent=n.stringContent.substring(o-1),n.sourcepos[0][1]+=o,n.lineOffsets[0]+=o,t.listData.task=!0,t.listData.checked=/[xX]/.test(i[1])}}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.table={continue:function(){return 0},finalize:function(){},canContain:function(e){return"tableHead"===e||"tableBody"===e},acceptsLines:!1},t.tableBody={continue:function(){return 0},finalize:function(){},canContain:function(e){return"tableRow"===e},acceptsLines:!1},t.tableHead={continue:function(){return 1},finalize:function(){},canContain:function(e){return"tableRow"===e||"tableDelimRow"===e},acceptsLines:!1},t.tableDelimRow={continue:function(){return 1},finalize:function(){},canContain:function(e){return"tableDelimCell"===e},acceptsLines:!1},t.tableDelimCell={continue:function(){return 1},finalize:function(){},canContain:function(){return!1},acceptsLines:!1},t.tableRow={continue:function(){return 1},finalize:function(){},canContain:function(e){return"tableCell"===e},acceptsLines:!1},t.tableCell={continue:function(){return 1},finalize:function(){},canContain:function(){return!1},acceptsLines:!1}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(2),i=n(1),o=n(3);function a(e){for(var t=0,n=0,i=[],o=0;o<e.length;o+=1)if("|"===e[o]&&"\\"!==e[o-1]){var a=e.substring(t,o);0===t&&r.isEmpty(a)?n=o+1:i.push(a),t=o+1}return t<e.length&&(a=e.substring(t,e.length),r.isEmpty(a)||i.push(a)),[n,i]}function s(e,t,n,r){for(var o=[],a=0,s=t;a<s.length;a++){var l=s[a],c=l.match(/^[ \t]+/),u=c?c[0].length:0,d=void 0,h=void 0;if(u===l.length)u=0,d=0,h="";else{var f=l.match(/[ \t]+$/);d=f?f[0].length:0,h=l.slice(u,l.length-d)}var p=r+u,g=i.createNode(e,[[n,r],[n,r+l.length-1]]);g.stringContent=h.replace(/\\\|/g,"|"),g.startIdx=o.length,g.endIdx=o.length,g.lineOffsets=[p-1],g.paddingLeft=u,g.paddingRight=d,o.push(g),r+=l.length+1}return o}function l(e){var t="left",n=e.stringContent,r=n[0];return":"===n[n.length-1]&&(t=":"===r?"center":"right"),{align:t}}t.tableHead=function(e,t){var n=t.stringContent;if("paragraph"===t.type&&!e.indented&&!e.blank){var r=n.length-1,c=n.lastIndexOf("\n",r-1)+1,u=n.slice(c,r),d=e.currentLine.slice(e.nextNonspace),h=a(u),f=h[0],p=h[1],g=a(d),m=g[0],v=g[1],b=/^[ \t]*:?-+:?[ \t]*$/;if(!p.length||!v.length||v.some((function(e){return!b.test(e)}))||1===v.length&&0!==d.indexOf("|"))return 0;var _=t.lineOffsets,y=e.lineNumber-1,C=o.last(_)+1,w=i.createNode("table",[[y,C],[e.lineNumber,e.offset]]);if(w.columns=v.map((function(){return{align:"left"}})),t.insertAfter(w),1===_.length)t.unlink();else{t.stringContent=n.slice(0,c);var E=c-(n.lastIndexOf("\n",c-2)+1)-1;e.lastLineLength=_[_.length-2]+E,e.finalize(t,y-1)}e.advanceOffset(e.currentLine.length-e.offset,!1);var T=i.createNode("tableHead",[[y,C],[e.lineNumber,e.offset]]);w.appendChild(T);var N=i.createNode("tableRow",[[y,C],[y,C+u.length-1]]),k=i.createNode("tableDelimRow",[[e.lineNumber,e.nextNonspace+1],[e.lineNumber,e.offset]]);T.appendChild(N),T.appendChild(k),s("tableCell",p,y,C+f).forEach((function(e){N.appendChild(e)}));var S=s("tableDelimCell",v,e.lineNumber,e.nextNonspace+1+m);return S.forEach((function(e){k.appendChild(e)})),w.columns=S.map(l),e.tip=w,2}return 0},t.tableBody=function(e,t){if("table"!==t.type&&"tableBody"!==t.type||!e.blank&&-1===e.currentLine.indexOf("|"))return 0;if(e.advanceOffset(e.currentLine.length-e.offset,!1),e.blank){var n=t;return"tableBody"===t.type&&(n=t.parent,e.finalize(t,e.lineNumber)),e.finalize(n,e.lineNumber),0}var r=t;"table"===t.type&&((r=e.addChild("tableBody",e.nextNonspace)).stringContent=null);var o=i.createNode("tableRow",[[e.lineNumber,e.nextNonspace+1],[e.lineNumber,e.currentLine.length]]);r.appendChild(o);var l=r.parent,c=a(e.currentLine.slice(e.nextNonspace)),u=c[0];return s("tableCell",c[1],e.lineNumber,e.nextNonspace+1+u).forEach((function(e,t){t>=l.columns.length&&(e.ignored=!0),o.appendChild(e)})),2}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1);function i(e,t){return e[0]<t[0]?1:e[0]>t[0]?-1:e[1]<t[1]?1:e[1]>t[1]?-1:0}function o(e,t){var n=e[0];return 1===i(e[1],t)?1:-1===i(n,t)?-1:0}function a(e,t){var n=e[0];return e[1][0]<t?1:n[0]>t?-1:0}function s(e){return r.getNodeById(e)||null}t.getAllParents=function(e){for(var t=[];e.parent;)t.push(e.parent),e=e.parent;return t.reverse()},t.removeNextUntil=function(e,t){if(e.parent===t.parent&&e!==t){for(var n=e.next;n&&n!==t;){for(var i=n.next,o=0,a=["parent","prev","next"];o<a.length;o++){var s=a[o];n[s]&&(r.removeNodeById(n[s].id),n[s]=null)}n=i}e.next=t.next,t.next?t.next.prev=e:e.parent.lastChild=e}},t.getChildNodes=function(e){for(var t=[],n=e.firstChild;n;)t.push(n),n=n.next;return t},t.insertNodesBefore=function(e,t){for(var n=0,r=t;n<r.length;n++){var i=r[n];e.insertBefore(i)}},t.prependChildNodes=function(e,t){for(var n=t.length-1;n>=0;n-=1)e.prependChild(t[n])},t.updateNextLineNumbers=function(e,t){if(e&&e.parent&&0!==t){var n,r=e.parent.walker();for(r.resumeAt(e,!0);n=r.next();){var i=n.node;n.entering&&(i.sourcepos[0][0]+=t,i.sourcepos[1][0]+=t)}}},t.findChildNodeAtLine=function(e,t){for(var n=e.firstChild;n;){var r=a(n.sourcepos,t);if(0===r)return n;if(-1===r)return n.prev||n;n=n.next}return e.lastChild},t.findFirstNodeAtLine=function(e,t){for(var n=e.firstChild,r=null;n;){var i=a(n.sourcepos,t);if(0===i){if(n.sourcepos[0][0]===t||!n.firstChild)return n;r=n,n=n.firstChild}else{if(-1===i)break;r=n,n=n.next}}return r?function(e){for(;e.parent&&"document"!==e.parent.type&&e.parent.sourcepos[0][0]===e.sourcepos[0][0];)e=e.parent;return e}(function(e){for(;e.lastChild;)e=e.lastChild;return e}(r)):null},t.findNodeAtPosition=function(e,t){for(var n=e,r=null;n;){var i=o(n.sourcepos,t);if(0===i){if(!n.firstChild)return n;r=n,n=n.firstChild}else{if(-1===i)return r;if(!n.next)return r;n=n.next}}return n},t.toString=function(e){return e?"type: "+e.type+", sourcepos: "+e.sourcepos+", firstChild: "+(e.firstChild&&e.firstChild.type)+", lastChild: "+(e.lastChild&&e.lastChild.type)+", prev: "+(e.prev&&e.prev.type)+", next: "+(e.next&&e.next.type):"null"},t.findNodeById=s,t.invokeNextUntil=function(e,t,n){if(void 0===n&&(n=null),t)for(var r=t.walker();t&&t!==n;){e(t);var i=r.next();if(!i)break;t=i.node}},t.isUnlinked=function(e){var t=s(e);if(!t)return!0;for(;t&&"document"!==t.type;){if(!t.parent&&!t.prev&&!t.next)return!0;t=t.parent}return!1}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(1),o=n(2),a=n(3),s=n(27),l=n(29),c={softbreak:"\n",gfm:!1,tagFilter:!1,nodeId:!1};function u(e){e.length&&"\n"!==a.last(a.last(e))&&e.push("\n")}function d(e,t){e.outerNewLine&&u(t)}function h(e,t){e.innerNewLine&&u(t)}function f(e){for(var t=[],n=e.walker(),r=null;r=n.next();){var i=r.node;"text"===i.type&&t.push(i.literal)}return t.join("")}t.createRenderHTML=function(e){var t=r.__assign(r.__assign({},c),e),n=r.__assign({},s.baseConvertors);if(t.gfm&&(n=r.__assign(r.__assign({},n),l.gfmConvertors)),t.convertors){var a=t.convertors;Object.keys(a).forEach((function(e){var t=n[e],r=a[e];n[e]=t?function(e,n){return n.origin=function(){return t(e,n)},r(e,n)}:r})),delete t.convertors}return function(e){return function(e,t,n){for(var r=[],a=e.walker(),s=null,l=function(){var e=s.node,l=s.entering,c=t[e.type];if(!c)return"continue";var u=!1,p={entering:l,leaf:!i.isContainer(e),options:n,getChildrenText:f,skipChildren:function(){u=!0}},g=c(e,p);g&&((Array.isArray(g)?g:[g]).forEach((function(t,i){"openTag"===t.type&&n.nodeId&&0===i&&(t.attributes||(t.attributes={}),t.attributes["data-nodeid"]=String(e.id)),function(e,t){switch(e.type){case"openTag":case"closeTag":!function(e,t){"openTag"===e.type?(d(e,t),t.push(function(e){var t=[],n=e.tagName,r=e.classNames,i=e.attributes;return t.push("<"+n),r&&r.length>0&&t.push(' class="'+r.join(" ")+'"'),i&&Object.keys(i).forEach((function(e){var n=i[e];t.push(" "+e+'="'+n+'"')})),e.selfClose&&t.push(" /"),t.push(">"),t.join("")}(e)),e.selfClose?d(e,t):h(e,t)):(h(e,t),t.push("</"+e.tagName+">"),d(e,t))}(e,t);break;case"text":!function(e,t){t.push(o.escapeXml(e.content))}(e,t);break;case"html":!function(e,t){d(e,t),t.push(e.content),d(e,t)}(e,t)}}(t,r)})),u&&(a.resumeAt(e,!1),a.next()))};s=a.next();)l();return u(r),r.join("")}(e,n,t)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(2),o=n(28);t.baseConvertors={heading:function(e,t){return{type:t.entering?"openTag":"closeTag",tagName:"h"+e.level,outerNewLine:!0}},text:function(e){return{type:"text",content:e.literal}},softbreak:function(e,t){return{type:"html",content:t.options.softbreak}},linebreak:function(){return{type:"html",content:"<br />\n"}},emph:function(e,t){return{type:t.entering?"openTag":"closeTag",tagName:"em"}},strong:function(e,t){return{type:t.entering?"openTag":"closeTag",tagName:"strong"}},paragraph:function(e,t){var n,r=t.entering,i=null===(n=e.parent)||void 0===n?void 0:n.parent;return i&&"list"===i.type&&i.listData.tight?null:{type:r?"openTag":"closeTag",tagName:"p",outerNewLine:!0}},thematicBreak:function(){return{type:"openTag",tagName:"hr",outerNewLine:!0,selfClose:!0}},blockQuote:function(e,t){return{type:t.entering?"openTag":"closeTag",tagName:"blockquote",outerNewLine:!0,innerNewLine:!0}},list:function(e,t){var n=t.entering,r=e.listData,i=r.type,o=r.start,a="bullet"===i?"ul":"ol",s={};return"ol"===a&&null!==o&&1!==o&&(s.start=o.toString()),{type:n?"openTag":"closeTag",tagName:a,attributes:s,outerNewLine:!0}},item:function(e,t){return{type:t.entering?"openTag":"closeTag",tagName:"li",outerNewLine:!0}},htmlInline:function(e,t){return{type:"html",content:t.options.tagFilter?o.filterDisallowedTags(e.literal):e.literal}},htmlBlock:function(e,t){var n=t.options,r=n.tagFilter?o.filterDisallowedTags(e.literal):e.literal;return n.nodeId?[{type:"openTag",tagName:"div",outerNewLine:!0},{type:"html",content:r},{type:"closeTag",tagName:"div",outerNewLine:!0}]:{type:"html",content:r,outerNewLine:!0}},code:function(e){return[{type:"openTag",tagName:"code"},{type:"text",content:e.literal},{type:"closeTag",tagName:"code"}]},codeBlock:function(e){var t=e.info,n=t?t.split(/\s+/):[],r=[];return n.length>0&&n[0].length>0&&r.push("language-"+i.escapeXml(n[0])),[{type:"openTag",tagName:"pre",outerNewLine:!0},{type:"openTag",tagName:"code",classNames:r},{type:"text",content:e.literal},{type:"closeTag",tagName:"code"},{type:"closeTag",tagName:"pre",outerNewLine:!0}]},link:function(e,t){if(t.entering){var n=e,o=n.title,a=n.destination;return{type:"openTag",tagName:"a",attributes:r.__assign({href:i.escapeXml(a)},o&&{title:i.escapeXml(o)})}}return{type:"closeTag",tagName:"a"}},image:function(e,t){var n=t.getChildrenText,o=t.skipChildren,a=e,s=a.title,l=a.destination;return o(),{type:"openTag",tagName:"img",selfClose:!0,attributes:r.__assign({src:i.escapeXml(l),alt:n(e)},s&&{title:i.escapeXml(s)})}}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=new RegExp("<(/?(?:"+["title","textarea","style","xmp","iframe","noembed","noframes","script","plaintext"].join("|")+")[^>]*>)","ig");t.filterDisallowedTags=function(e){return r.test(e)?e.replace(r,(function(e,t){return"&lt;"+t})):e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);t.gfmConvertors={strike:function(e,t){return{type:t.entering?"openTag":"closeTag",tagName:"del"}},item:function(e,t){var n=t.entering,i=e.listData,o=i.checked,a=i.task;if(n){var s={type:"openTag",tagName:"li",outerNewLine:!0};return a?[s,{type:"openTag",tagName:"input",selfClose:!0,attributes:r.__assign(r.__assign({},o&&{checked:""}),{disabled:"",type:"checkbox"})},{type:"text",content:" "}]:s}return{type:"closeTag",tagName:"li",outerNewLine:!0}},table:function(e,t){return{type:t.entering?"openTag":"closeTag",tagName:"table",outerNewLine:!0}},tableHead:function(e,t){return{type:t.entering?"openTag":"closeTag",tagName:"thead",outerNewLine:!0}},tableBody:function(e,t){return{type:t.entering?"openTag":"closeTag",tagName:"tbody",outerNewLine:!0}},tableRow:function(e,t){if(t.entering)return{type:"openTag",tagName:"tr",outerNewLine:!0};var n=[];if(e.lastChild)for(var r=e.parent.parent.columns.length,i=e.lastChild.endIdx+1;i<r;i+=1)n.push({type:"openTag",tagName:"td",outerNewLine:!0},{type:"closeTag",tagName:"td",outerNewLine:!0});return n.push({type:"closeTag",tagName:"tr",outerNewLine:!0}),n},tableCell:function(e,t){var n=t.entering;if(e.ignored)return{type:"text",content:""};var i=e.parent.parent,o="tableHead"===i.type?"th":"td",a=i.parent.columns[e.startIdx],s=a&&"left"!==a.align?a.align:null,l=s?{align:s}:null;return n?r.__assign({type:"openTag",tagName:o,outerNewLine:!0},l&&{attributes:l}):{type:"closeTag",tagName:o,outerNewLine:!0}}}}]))},function(e,t,n){"use strict";var r=n(14),i=n.n(r),o=n(21),a=n.n(o),s=function(){function e(){this._keys=[],this._values=[]}var t=e.prototype;return t._getKeyIndex=function(e){return i()(e,this._keys)},t.get=function(e){return this._values[this._getKeyIndex(e)]},t.set=function(e,t){var n=this._getKeyIndex(e);n>-1?this._values[n]=t:(this._keys.push(e),this._values.push(t))},t.has=function(e){return this._getKeyIndex(e)>-1},t.delete=function(e){var t=this._getKeyIndex(e);t>-1&&(this._keys.splice(t,1),this._values.splice(t,1))},t.forEach=function(e,t){var n=this;void 0===t&&(t=this),a()(this._values,(function(r,i){r&&n._keys[i]&&e.call(t,r,n._keys[i],n)}))},e}();t["a"]=s},function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"e",(function(){return s})),n.d(t,"a",(function(){return l})),n.d(t,"b",(function(){return c})),n.d(t,"c",(function(){return u}));var r=n(2),i=n.n(r),o={};function a(e,t){o[e]=o[e]||{},o[e].height=t}function s(e,t){o[e]=o[e]||{},o[e].offsetTop=t}function l(e){return o[e]&&o[e].height}function c(e){return o[e]&&o[e].offsetTop}function u(e){e&&(delete o[e.getAttribute("data-nodeid")],i()(e.children).forEach((function(e){u(e)})))}},function(e,t,n){"use strict";var r=n(2),i=n.n(r),o=n(9),a=n.n(o),s=n(0),l=new RegExp("^(abbr|align|alt|axis|bgcolor|border|cellpadding|cellspacing|class|clear|color|cols|compact|coords|dir|face|headers|height|hreflang|hspace|ismap|lang|language|nohref|nowrap|rel|rev|rows|rules|scope|scrolling|shape|size|span|start|summary|tabindex|target|title|type|valign|value|vspace|width|checked|mathvariant|encoding|id|name|background|cite|href|longdesc|src|usemap|xlink:href|data-+|checked|style)","g"),c=new RegExp("^(accent-height|accumulate|additive|alphabetic|arabic-form|ascent|baseProfile|bbox|begin|by|calcMode|cap-height|class|color|color-rendering|content|cx|cy|d|dx|dy|descent|display|dur|end|fill|fill-rule|font-family|font-size|font-stretch|font-style|font-variant|font-weight|from|fx|fy|g1|g2|glyph-name|gradientUnits|hanging|height|horiz-adv-x|horiz-origin-x|ideographic|k|keyPoints|keySplines|keyTimes|lang|marker-end|marker-mid|marker-start|markerHeight|markerUnits|markerWidth|mathematical|max|min|offset|opacity|orient|origin|overline-position|overline-thickness|panose-1|path|pathLength|points|preserveAspectRatio|r|refX|refY|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|rotate|rx|ry|slope|stemh|stemv|stop-color|stop-opacity|strikethrough-position|strikethrough-thickness|stroke|stroke-dasharray|stroke-dashoffset|stroke-linecap|stroke-linejoin|stroke-miterlimit|stroke-opacity|stroke-width|systemLanguage|target|text-anchor|to|transform|type|u1|u2|underline-position|underline-thickness|unicode|unicode-range|units-per-em|values|version|viewBox|visibility|width|widths|x|x-height|x1|x2|xlink:actuate|xlink:arcrole|xlink:role|xlink:show|xlink:title|xlink:type|xml:base|xml:lang|xml:space|xmlns|xmlns:xlink|y|y1|y2|zoomAndPan)","g"),u=/href|src|background/gi,d=/((java|vb|live)script|x):/gi,h=/^on\S+/;function f(e,t){var n=document.createElement("div");return a()(e)?(e=e.replace(/<!--[\s\S]*?-->/g,""),n.innerHTML=e):n.appendChild(e),p(n),v(n),s["a"].finalizeHtml(n,t)}function p(e){var t=s["a"].findAll(e,"script, iframe, textarea, form, button, select, input, meta, style, link, title, embed, object, details, summary");t.forEach((function(e){s["a"].remove(e)}))}function g(e,t){return e.match(u)&&t.match(d)}function m(e,t){i()(t).forEach((function(t){var n=t.name;h.test(n)&&(e[n]=null),e.getAttribute(n)&&e.removeAttribute(n)}))}function v(e){s["a"].findAll(e,"*").forEach((function(e){var t=e.attributes,n=i()(t).filter((function(e){var t=e.name,n=e.value,r=t.match(l),i=t.match(c),o=r&&g(t,n);return!r&&!i||o}));m(e,n)}))}t["a"]=f},function(e,t,n){"use strict";var r=n(17),i=n(21),o=n(10);function a(e,t,n){r(e)?i(e,t,n):o(e,t,n)}e.exports=a},function(e,t,n){"use strict";function r(e){return e instanceof Function}e.exports=r},function(e,t,n){"use strict";var r=n(8),i=n(48);function o(e){return!r(e)&&!i(e)}e.exports=o},function(e,t,n){"use strict";function r(){return r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}n.d(t,"a",(function(){return o}));var i={paragraph:function(e,t){var n=t.entering,r=t.origin,i=t.options;return i.nodeId?{type:n?"openTag":"closeTag",outerNewLine:!0,tagName:"p"}:r()},softbreak:function(e){var t=e.prev&&"htmlInline"===e.prev.type,n=t&&/<br ?\/?>/.test(e.prev.literal),r=n?"\n":"<br>\n";return{type:"html",content:r}},item:function(e,t){var n=t.entering;if(n){var r={},i=[];return e.listData.task&&(r["data-te-task"]="",i.push("task-list-item"),e.listData.checked&&i.push("checked")),{type:"openTag",tagName:"li",classNames:i,attributes:r,outerNewLine:!0}}return{type:"closeTag",tagName:"li",outerNewLine:!0}},code:function(e){var t={"data-backticks":e.tickCount};return[{type:"openTag",tagName:"code",attributes:t},{type:"text",content:e.literal},{type:"closeTag",tagName:"code"}]},codeBlock:function(e){var t=e.info?e.info.split(/\s+/):[],n=[],r={};if(e.fenceLength>3&&(r["data-backticks"]=e.fenceLength),t.length>0&&t[0].length>0){var i=t[0];n.push("lang-"+i),r["data-language"]=i}return[{type:"openTag",tagName:"pre",classNames:n},{type:"openTag",tagName:"code",attributes:r},{type:"text",content:e.literal},{type:"closeTag",tagName:"code"},{type:"closeTag",tagName:"pre"}]}};function o(e,t){var n=r({},i);return e&&(n.link=function(t,n){var i=n.entering,o=n.origin,a=o();return i&&(a.attributes=r({},a.attributes,{},e)),a}),t&&Object.keys(t).forEach((function(e){var i=n[e],o=t[e];n[e]=i?function(e,t){var n=r({},t);return n.origin=function(){return i(e,t)},o(e,n)}:o})),n}},function(e,t,n){"use strict";var r=function(){function e(){this._replacers={}}var t=e.prototype;return t.setReplacer=function(e,t){e=e.toLowerCase(),this._replacers[e]=t},t.getReplacer=function(e){return this._replacers[e]},t.createCodeBlockHtml=function(e,t){e=e.toLowerCase();var n=this.getReplacer(e);return n?n(t,e):i(t,!1)},e}();function i(e,t){return e.replace(t?/&/g:/&(?!#?\w+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}t["a"]=new r},function(e,t,n){"use strict";n.d(t,"b",(function(){return l})),n.d(t,"a",(function(){return c}));var r=n(17),i=n.n(r),o=n(27),a=n.n(o);function s(){return s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(this,arguments)}function l(e,t){e.forEach((function(e){if(a()(e))e(t);else if(i()(e)){var n=e[0],r=e[1],o=void 0===r?{}:r;n(t,o)}}))}function c(e){return e?e.reduce((function(e,t){var n=i()(t)?t[0]:t;if(!a()(n)){var r=n.renderer,o=n.parser,l=n.pluginFn;t=i()(t)?[l,t[1]]:l,r&&(e.renderer=s({},e.renderer,{},r)),o&&(e.parser=s({},e.parser,{},o))}return e.plugins.push(t),e}),{plugins:[],renderer:{},parser:{}}):{}}},function(e,t,n){(function(t,n){e.exports=n()})("undefined"!==typeof self&&self,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=3)}([function(e,t,n){"use strict";var r=n(1),i=/\n$/g,o=/[ \xA0]+\n\n/g,a=/([ \xA0]+\n){2,}/g,s=/href\=\"(.*?)\"/,l=/^/gm,c=r.factory({TEXT_NODE:function(e){var t=this.trim(this.getSpaceCollapsedText(e.nodeValue));return this._isNeedEscapeBackSlash(t)&&(t=this.escapeTextBackSlash(t)),t=this.escapePairedCharacters(t),this._isNeedEscapeHtml(t)&&(t=this.escapeTextHtml(t)),this._isNeedEscape(t)&&(t=this.escapeText(t)),this.getSpaceControlled(t,e)},"CODE TEXT_NODE":function(e){return e.nodeValue},"EM, I":function(e,t){var n="";return this.isEmptyText(t)||(n="*"+t+"*"),n},"STRONG, B":function(e,t){var n="";return this.isEmptyText(t)||(n="**"+t+"**"),n},A:function(e,t){var n,r,i=t,o="";return n=s.exec(e.outerHTML),n&&(r=n[1].replace(/&amp;/g,"&")),e.title&&(o=' "'+e.title+'"'),!this.isEmptyText(t)&&r&&(i="["+this.escapeTextForLink(t)+"]("+r+o+")"),i},IMG:function(e){var t="",n=e.getAttribute("src"),r=e.alt;return n&&(t="!["+this.escapeTextForLink(r)+"]("+n+")"),t},BR:function(){return"  \n"},CODE:function(e,t){var n,r,i="";return this.isEmptyText(t)||(r=parseInt(e.getAttribute("data-backticks"),10),n=isNaN(r)?"`":Array(r+1).join("`"),i=n+t+n),i},P:function(e,t){var n="";return t=t.replace(a,"  \n"),this.isEmptyText(t)||(n="\n\n"+t+"\n\n"),n},"BLOCKQUOTE P":function(e,t){return t},"LI P":function(e,t){var n="";return this.isEmptyText(t)||(n=t),n},"H1, H2, H3, H4, H5, H6":function(e,t){var n="",r=parseInt(e.tagName.charAt(1),10);while(r)n+="#",r-=1;return n+=" ",n+=t,"\n\n"+n+"\n\n"},"LI H1, LI H2, LI H3, LI H4, LI H5, LI H6":function(e,t){var n=parseInt(e.tagName.charAt(1),10);return Array(n+1).join("#")+" "+t},"UL, OL":function(e,t){return"\n\n"+t+"\n\n"},"LI OL, LI UL":function(e,t){var n,r;return r=t.replace(o,"\n"),r=r.replace(i,""),n=r.replace(l,"    "),"\n"+n},"UL LI":function(e,t){var n="";return t=t.replace(a,"  \n"),e.firstChild&&"P"===e.firstChild.tagName&&(n+="\n"),n+="* "+t+"\n",n},"OL LI":function(e,t){var n="",r=parseInt(e.parentNode.getAttribute("start")||1,10);while(e.previousSibling)e=e.previousSibling,1===e.nodeType&&"LI"===e.tagName&&(r+=1);return t=t.replace(a,"  \n"),e.firstChild&&"P"===e.firstChild.tagName&&(n+="\n"),n+=r+". "+t+"\n",n},HR:function(){return"\n\n- - -\n\n"},BLOCKQUOTE:function(e,t){var n,r;return t=t.replace(a,"\n\n"),r=this.trim(t),n=r.replace(l,"> "),"\n\n"+n+"\n\n"},"PRE CODE":function(e,t){var n,r;return r=t.replace(i,""),n=r.replace(l,"    "),"\n\n"+n+"\n\n"}});e.exports=c},function(e,t,n){"use strict";var r=/^\u0020/,i=/.+\u0020$/,o=/[\n\s\t]+/g,a=/^[\u0020\r\n\t]+|[\u0020\r\n\t]+$/g,s=/[\u0020]+/g,l=/[>(){}\[\]+-.!#|]/g,c=/[\[\]]/g,u=/!\[.*\]\(.*\)/g,d=3;function h(e,t,n){var r;for(r in n=n||null,e)if(e.hasOwnProperty(r)&&!1===t.call(n,e[r],r,e))break}function f(e){this.rules={},e&&this.addRules(e)}function p(e){var t=e.tagName;return"S"===t||"B"===t||"I"===t||"EM"===t||"STRONG"===t||"A"===t||"IMG"===t||"CODE"===t}function g(e,t){var n=e.cloneNode(!1);return n.innerHTML=t,n.outerHTML}function m(e,t){h(t,(function(t,n){"converter"!==n?(e[n]||(e[n]={}),m(e[n],t)):e[n]=t}))}f.prototype.lineFeedReplacement="​​",f.prototype.addRule=function(e,t){var n=e.split(", "),r=n.pop();t.fname=e;while(r)this._setConverterWithSelector(r,t),r=n.pop()},f.prototype.addRules=function(e){h(e,(function(e,t){this.addRule(t,e)}),this)},f.prototype.getSpaceControlled=function(e,t){var n,o="",a="";return t.previousSibling&&(t.previousSibling.nodeType===d||p(t.previousSibling))&&(n=t.previousSibling.innerHTML||t.previousSibling.nodeValue,(i.test(n)||r.test(t.innerHTML||t.nodeValue))&&(o=" ")),t.nextSibling&&(t.nextSibling.nodeType===d||p(t.nextSibling))&&(n=t.nextSibling.innerHTML||t.nextSibling.nodeValue,(r.test(n)||i.test(t.innerHTML||t.nodeValue))&&(a=" ")),o+e+a},f.prototype.convert=function(e,t){var n,r=this._getConverter(e);return e&&e.nodeType===Node.ELEMENT_NODE&&e.hasAttribute("data-tomark-pass")?(e.removeAttribute("data-tomark-pass"),n=g(e,t)):r?n=r.call(this,e,t):e&&(n=this.getSpaceControlled(this._getInlineHtml(e,t),e)),n||""},f.prototype._getInlineHtml=function(e,t){var n=e.outerHTML,r=e.tagName,i=t.replace(/\$/g,"$$$$");return n.replace(new RegExp("(<"+r+" ?.*?>).*(</"+r+">)","i"),"$1"+i+"$2")},f.prototype._getConverter=function(e){var t,n=this.rules;while(e&&n)n=this._getNextRule(n,this._getRuleNameFromNode(e)),e=this._getPrevNode(e),n&&n.converter&&(t=n.converter);return t},f.prototype._getNextRule=function(e,t){return e[t]},f.prototype._getRuleNameFromNode=function(e){return e.tagName||"TEXT_NODE"},f.prototype._getPrevNode=function(e){var t,n=e.parentNode;return n&&!n.__htmlRootByToMark&&(t=n),t},f.prototype._setConverterWithSelector=function(e,t){var n=this.rules;this._eachSelector(e,(function(e){n[e]||(n[e]={}),n=n[e]})),n.converter=t},f.prototype._eachSelector=function(e,t){var n,r;n=e.split(" "),r=n.length-1;while(r>=0)t(n[r]),r-=1},f.prototype.trim=function(e){return e.replace(a,"")},f.prototype.isEmptyText=function(e){return""===e.replace(o,"")},f.prototype.getSpaceCollapsedText=function(e){return e.replace(s," ")},f.prototype.escapeText=function(e){return e.replace(l,(function(e){return"\\"+e}))},f.prototype.escapeTextForLink=function(e){var t=[],n=u.exec(e);while(n)t.push([n.index,n.index+n[0].length]),n=u.exec(e);return e.replace(c,(function(e,n){var r=t.some((function(e){return n>e[0]&&n<e[1]}));return r?e:"\\"+e}))},f.prototype.escapeTextHtml=function(e){return e.replace(new RegExp(f.markdownTextToEscapeHtmlRx.source,"g"),(function(e){return"\\"+e}))},f.prototype.escapeTextBackSlash=function(e){return e.replace(new RegExp(f.markdownTextToEscapeBackSlashRx.source,"g"),(function(e){return"\\"+e}))},f.prototype.escapePairedCharacters=function(e){return e.replace(new RegExp(f.markdownTextToEscapePairedCharsRx.source,"g"),(function(e){return"\\"+e}))},f.markdownTextToEscapeRx={codeblock:/(^ {4}[^\n]+\n*)+/,hr:/^ *((\* *){3,}|(- *){3,} *|(_ *){3,}) */,heading:/^(#{1,6}) +[\s\S]+/,lheading:/^([^\n]+)\n *(=|-){2,} */,blockquote:/^( *>[^\n]+.*)+/,list:/^ *(\*+|-+|\d+\.) [\s\S]+/,def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +["(]([^\n]+)[")])? */,link:/!?\[.*\]\(.*\)/,reflink:/!?\[.*\]\s*\[([^\]]*)\]/,verticalBar:/\u007C/,codeblockGfm:/^(`{3,})/,codeblockTildes:/^(~{3,})/},f.markdownTextToEscapeHtmlRx=/<([a-zA-Z_][a-zA-Z0-9\-\._]*)(\s|[^\\/>])*\/?>|<(\/)([a-zA-Z_][a-zA-Z0-9\-\._]*)\s*\/?>|<!--[^-]+-->|<([a-zA-Z_][a-zA-Z0-9\-\.:/]*)>/,f.markdownTextToEscapeBackSlashRx=/\\[!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~\\]/,f.markdownTextToEscapePairedCharsRx=/[*_~`]/,f.prototype._isNeedEscape=function(e){var t,n=!1,r=f.markdownTextToEscapeRx;for(t in r)if(r.hasOwnProperty(t)&&r[t].test(e)){n=!0;break}return n},f.prototype._isNeedEscapeHtml=function(e){return f.markdownTextToEscapeHtmlRx.test(e)},f.prototype._isNeedEscapeBackSlash=function(e){return f.markdownTextToEscapeBackSlashRx.test(e)},f.prototype.mix=function(e){m(this.rules,e.rules)},f.factory=function(e,t){var n=new f;return t?n.mix(e):t=e,n.addRules(t),n},e.exports=f},function(e,t,n){"use strict";var r=n(1),i=n(0),o=r.factory(i,{"DEL, S":function(e,t){return"~~"+t+"~~"},"PRE CODE":function(e,t){var n,r="",i=e.getAttribute("data-backticks");return e.getAttribute("data-language")&&(r=" "+e.getAttribute("data-language")),i=parseInt(i,10),n=isNaN(i)?"```":Array(i+1).join("`"),t=t.replace(/(\r\n)|(\r)|(\n)/g,this.lineFeedReplacement),"\n\n"+n+r+"\n"+t+"\n"+n+"\n\n"},PRE:function(e,t){return t},"UL LI":function(e,t){return i.convert(e,a(e,t))},"OL LI":function(e,t){return i.convert(e,a(e,t))},TABLE:function(e,t){return"\n\n"+t+"\n\n"},"TBODY, TFOOT":function(e,t){return t},"TR TD, TR TH":function(e,t){return t=t.replace(/(\r\n)|(\r)|(\n)/g,"")," "+t+" |"},"TD BR, TH BR":function(){return"<br>"},TR:function(e,t){return"|"+t+"\n"},THEAD:function(e,t){var n,r,i,o="";for(r=l(l(e,"TR")[0],"TH"),i=r.length,n=0;n<i;n+=1)o+=" "+s(r[n])+" |";return t?t+"|"+o+"\n":""}});function a(e,t){var n;return-1!==e.className.indexOf("task-list-item")&&(n=-1!==e.className.indexOf("checked")?"x":" ",t="["+n+"] "+t),t}function s(e){var t,n,r,i;return t=e.align,i=e.textContent?e.textContent.length:e.innerText.length,n="",r="",t&&("left"===t?(n=":",i-=1):"right"===t?(r=":",i-=1):"center"===t&&(r=":",n=":",i-=2)),n+c("-",i)+r}function l(e,t){var n,r=e.childNodes,i=r.length,o=[];for(n=0;n<i;n+=1)r[n].tagName&&r[n].tagName===t&&o.push(r[n]);return o}function c(e,t){var n=e;t=Math.max(t,3);while(t>1)n+=e,t-=1;return n}e.exports=o},function(e,t,n){"use strict";var r=n(4),i=n(1),o=n(0),a=n(2);r.Renderer=i,r.basicRenderer=o,r.gfmRenderer=a,e.exports=r},function(e,t,n){"use strict";var r=n(5),i=n(6),o=n(0),a=n(2),s=/[ \xA0]+(\n\n)/g,l=/^[\n]+|[\s\n]+$/g,c=/([ \xA0]+\n){2,}/g,u=/([ \xA0]){2,}\n/g,d=/[ \xA0\n]+/g;function h(e,t){var n,s,l=!0;return e?(s=a,t&&(l=t.gfm,!1===l&&(s=o),s=t.renderer||s),n=new r(i(e)),p(f(n,s),l,s.lineFeedReplacement)):""}function f(e,t){var n="";while(e.next())n+=g(e,t);return n}function p(e,t,n){return e=e.replace(s,"\n"),e=e.replace(c,"\n\n"),e=e.replace(d,(function(e){var t=(e.match(/\n/g)||[]).length;return t>=3?"\n\n":e>=1?"\n":e})),e=e.replace(l,""),e=e.replace(new RegExp(n,"g"),"\n"),t&&(e=e.replace(u,"\n")),e}function g(e,t){var n,r,i,o="",a=e.getNode();for(n=0,r=a.childNodes.length;n<r;n+=1)e.next(),o+=g(e,t);return i=t.convert(a,o),i}e.exports=h},function(e,t,n){"use strict";var r={ELEMENT_NODE:1,ATTRIBUTE_NODE:2,TEXT_NODE:3};function i(e){this._normalizeTextChildren(e),this._root=e,this._current=e}i.prototype.next=function(){var e,t=this._current;if(this._current){e=this._getNextNode(t);while(this._isNeedNextSearch(e,t))t=t.parentNode,e=t.nextSibling;this._current=e}return this._current},i.prototype.getNode=function(){return this._normalizeTextChildren(this._current),this._current},i.prototype._normalizeTextChildren=function(e){var t,n;if(e&&!(e.childNodes.length<2)){t=e.firstChild;while(t.nextSibling)n=t.nextSibling,t.nodeType===r.TEXT_NODE&&n.nodeType===r.TEXT_NODE?(t.nodeValue+=n.nodeValue,e.removeChild(n)):t=n}},i.prototype.getNodeText=function(){var e,t=this.getNode();return e=t.nodeType===r.TEXT_NODE?t.nodeValue:t.textContent||t.innerText,e},i.prototype._isNeedNextSearch=function(e,t){return!e&&t!==this._root&&t.parentNode!==this._root},i.prototype._getNextNode=function(e){return e.firstChild||e.nextSibling},i.NODE_TYPE=r,e.exports=i},function(e,t,n){"use strict";var r=/^[\s\r\n\t]+|[\s\r\n\t]+$/g,i=/>[\r\n\t]+</g,o=/>[ ]+</g;function a(e){var t;return"[object String]"===Object.prototype.toString.call(e)?(t=document.createElement("div"),t.innerHTML=s(e)):t=e,t.__htmlRootByToMark=!0,t}function s(e){return e=e.replace(r,""),e=e.replace(i,"><"),e=e.replace(o,"> <"),e}a.preProcess=s,e.exports=a}])}))},function(e,t,n){"use strict";var r=n(8);function i(e){return e&&e.className?r(e.className.baseVal)?e.className:e.className.baseVal:""}e.exports=i},function(e,t,n){"use strict";function r(e){return"number"===typeof e||e instanceof Number}e.exports=r},function(e,t,n){"use strict";var r=n(19),i=n.n(r),o=n(20),a=n.n(o),s=n(5),l=n.n(s),c=n(6),u=n.n(c),d=n(22),h=n(38),f=n(0),p=n(29),g=n(18),m=n(24),v=n(4);function b(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var y="te-preview-highlight";function C(e,t){var n=t.ch,r=e.firstChild;while(r&&r.next){if(Object(v["e"])(r.next)>n+1)break;r=r.next}return r}var w=function(e){function t(t,n,r,i){var o;o=e.call(this,t,n,r,i.isViewer)||this,o.lazyRunner.registerLazyRunFunction("invokeCodeBlock",o.invokeCodeBlockPlugins,o.delayCodeBlockTime,b(o));var a=i.linkAttribute,s=i.customHTMLRenderer,l=i.highlight,c=void 0!==l&&l;return o.renderHTML=Object(d["createRenderHTML"])({gfm:!0,nodeId:!0,convertors:Object(p["a"])(a,s)}),o.cursorNodeId=null,o._initEvent(c),o}_(t,e);var n=t.prototype;return n._initEvent=function(e){var t=this;this.eventManager.listen("contentChangedFromMarkdown",this.update.bind(this)),e&&this.eventManager.listen("cursorActivity",(function(e){var n=e.markdownNode,r=e.cursor;t._updateCursorNode(n,r)})),i()(this.el,"scroll",(function(e){t.eventManager.emit("scroll",{source:"preview",data:Object(g["a"])(e.target.scrollTop,t._previewContent)})}))},n._updateCursorNode=function(e,t){e&&(e=Object(v["b"])(e,(function(e){return!Object(v["h"])(e)})),"tableRow"===e.type?e=C(e,t):"tableBody"===e.type&&(e=null));var n=e?e.id:null;if(this.cursorNodeId!==n){var r=this._getElementByNodeId(this.cursorNodeId),i=this._getElementByNodeId(n);r&&u()(r,y),i&&l()(i,y),this.cursorNodeId=n}},n._getElementByNodeId=function(e){return e?this._previewContent.querySelector('[data-nodeid="'+e+'"]'):null},n.update=function(e){var t=this;e.forEach((function(e){return t.replaceRangeNodes(e)})),this.eventManager.emit("previewRenderAfter",this)},n.replaceRangeNodes=function(e){var t=this,n=e.nodes,r=e.removedNodeRange,i=this._previewContent,o=this.eventManager.emitReduce("convertorAfterMarkdownToHtmlConverted",n.map((function(e){return t.renderHTML(e)})).join(""));if(r){var a=r.id,s=a[0],l=a[1],c=this._getElementByNodeId(s),u=this._getElementByNodeId(l);if(c){c.insertAdjacentHTML("beforebegin",o);var d=c;while(d!==u){var h=d.nextElementSibling;d.parentNode.removeChild(d),Object(m["c"])(d),d=h}d.parentNode&&(f["a"].remove(d),Object(m["c"])(d))}}else i.insertAdjacentHTML("afterbegin",o);var p=this.getCodeBlockElements(n.map((function(e){return e.id})));p.length&&this.lazyRunner.run("invokeCodeBlock",p)},n.render=function(t){e.prototype.render.call(this,t),this.eventManager.emit("previewRenderAfter",this)},n.remove=function(){a()(this.el,"scroll"),this.el=null},t}(h["a"]);t["a"]=w},function(e,t,n){"use strict";var r=n(10),i=n.n(r),o=n(8),a=n.n(o),s=n(44),l=n.n(s),c=n(45),u=n.n(c),d=n(23),h=["previewBeforeHook","previewRenderAfter","previewNeedsRefresh","addImageBlobHook","setMarkdownAfter","contentChangedFromWysiwyg","changeFromWysiwyg","contentChangedFromMarkdown","changeFromMarkdown","change","changeModeToWysiwyg","changeModeToMarkdown","changeModeBefore","changeMode","changePreviewStyle","changePreviewTabPreview","changePreviewTabWrite","openPopupAddLink","openPopupAddImage","openPopupAddTable","openPopupTableUtils","openHeadingSelect","openPopupCodeBlockLanguages","openPopupCodeBlockEditor","openDropdownToolbar","closePopupCodeBlockLanguages","closePopupCodeBlockEditor","closeAllPopup","command","addCommandBefore","htmlUpdate","markdownUpdate","renderedHtmlUpdated","removeEditor","convertorAfterMarkdownToHtmlConverted","convertorBeforeHtmlToMarkdownConverted","convertorAfterHtmlToMarkdownConverted","stateChange","wysiwygSetValueAfter","wysiwygSetValueBefore","wysiwygGetValueBefore","wysiwygProcessHTMLText","wysiwygRangeChangeAfter","wysiwygKeyEvent","scroll","click","mousedown","mouseover","mouseout","mouseup","contextmenu","keydown","keyup","keyMap","load","focus","blur","paste","pasteBefore","willPaste","copy","copyBefore","copyAfter","cut","cutAfter","drop","show","hide","changeLanguage","cursorActivity","requireScrollSync","requireScrollIntoView","setCodeBlockLanguages"],f=function(){function e(){this.events=new d["a"],this.TYPE=new u.a(h)}var t=e.prototype;return t.listen=function(e,t){var n=this._getTypeInfo(e),r=this.events.get(n.type)||[];if(!this._hasEventType(n.type))throw new Error("There is no event type "+n.type);n.namespace&&(t.namespace=n.namespace),r.push(t),this.events.set(n.type,r)},t.emit=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,o=t.shift(),s=this._getTypeInfo(o),l=this.events.get(s.type);return l&&i()(l,(function(e){var n=e.apply(void 0,t);a()(n)||(r=r||[],r.push(n))})),r},t.emitReduce=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.shift(),o=this.events.get(r);return o&&i()(o,(function(e){var n=e.apply(void 0,t);l()(n)||(t[0]=n)})),t[0]},t._getTypeInfo=function(e){var t=e.split(".");return{type:t[0],namespace:t[1]}},t._hasEventType=function(e){return!a()(this.TYPE[this._getTypeInfo(e).type])},t.addEventType=function(e){if(this._hasEventType(e))throw new Error("There is already have event type "+e);this.TYPE.set(e)},t.removeEventHandler=function(e,t){var n=this,r=this._getTypeInfo(e),i=r.type,o=r.namespace;i&&t?this._removeEventHandlerWithHandler(i,t):i&&!o?this.events.delete(i):!i&&o?this.events.forEach((function(e,t){n._removeEventHandlerWithTypeInfo(t,o)})):i&&o&&this._removeEventHandlerWithTypeInfo(i,o)},t._removeEventHandlerWithHandler=function(e,t){var n=this.events.get(e)||[],r=n.indexOf(t);r>=0&&n.splice(r,1)},t._removeEventHandlerWithTypeInfo=function(e,t){var n=[],r=this.events.get(e);r&&(r.map((function(e){return e.namespace!==t&&n.push(e),null})),this.events.set(e,n))},e}();t["a"]=f},function(e,t,n){"use strict";var r=n(32),i=n.n(r),o=n(22),a=n(29),s=n(0),l="[a-zA-Z_:][a-zA-Z0-9:._-]*",c="[^\"'=<>`\\x00-\\x20]+",u="'[^']*'",d='"[^"]*"',h="(?:"+c+"|"+u+"|"+d+")",f="(?:\\s+"+l+"(?:\\s*=\\s*"+h+")?)*\\s*",p="(\\\\<|<)([A-Za-z][A-Za-z0-9\\-]*"+f+")(\\/?>)",g=new RegExp(p,"g"),m=function(){function e(e,t){void 0===t&&(t={});var n=t,r=n.linkAttribute,i=n.customHTMLRenderer,s=n.extendedAutolinks,l=n.referenceDefinition,c=n.customParser;this.mdReader=new o["Parser"]({extendedAutolinks:s,disallowedHtmlBlockTags:["br"],referenceDefinition:l,disallowDeepHeading:!0,customParser:c}),this.renderHTML=Object(o["createRenderHTML"])({gfm:!0,convertors:Object(a["a"])(r,i)}),this.eventManager=e}var t=e.prototype;return t._markdownToHtmlWithCodeHighlight=function(e){return this.renderHTML(this.mdReader.parse(e))},t._markdownToHtml=function(e){return e=e.replace(g,(function(e,t,n,r){return"\\"!==e[0]?""+t+n+" data-tomark-pass "+r:e})),this.renderHTML(this.mdReader.parse(e))},t._removeBrToMarkPassAttributeInCode=function(e){var t=s["a"].createElementWith("<div>"+e+"</div>");return s["a"].findAll(t,"code, pre").forEach((function(e){var t=e;t.innerHTML=t.innerHTML.replace(/\sdata-tomark-pass\s(\/?)&gt;/g,"$1&gt;")})),e=t.innerHTML,e},t.toHTMLWithCodeHighlight=function(e){var t=this._markdownToHtmlWithCodeHighlight(e);return t=this.eventManager.emitReduce("convertorAfterMarkdownToHtmlConverted",t),t},t.toHTML=function(e){var t=this._markdownToHtml(e);return t=this.eventManager.emitReduce("convertorAfterMarkdownToHtmlConverted",t),t=this._removeBrToMarkPassAttributeInCode(t),t},t.initHtmlSanitizer=function(e){this.eventManager.listen("convertorAfterMarkdownToHtmlConverted",(function(t){return e(t,!0)}))},t.toMarkdown=function(e,t){var n=[];e=this.eventManager.emitReduce("convertorBeforeHtmlToMarkdownConverted",e),e=this._appendAttributeForLinkIfNeed(e),e=this._appendAttributeForBrIfNeed(e);var r=i()(e,t);return r=this.eventManager.emitReduce("convertorAfterHtmlToMarkdownConverted",r),r=this._removeNewlinesBeforeAfterAndBlockElement(r),r.split("\n").forEach((function(e,t){var r=/^(<br>)+\||\|[^|]*\|/gi,i=/`[^`]*<br>[^`]*`/gi,o=/^(<br>)+\|/gi;r.test(e)?e=e.replace(o,(function(e){return e.replace(/<br>/gi,"<br>\n")})):i.test(e)||(e=e.replace(/<br>/gi,"<br>\n")),n[t]=e})),n.join("\n")},t._removeNewlinesBeforeAfterAndBlockElement=function(e){var t=/<br>\n\n(#{1,6} .*|```|\||(\*+|-+|\d+\.) .*| *>[^\n]+.*)/g,n=/(#{1,6} .*|```|\|)\n\n<br>/g;return e=e.replace(t,"<br>$1"),e=e.replace(n,"$1\n<br>"),e},t._appendAttributeForLinkIfNeed=function(e){var t=/!?\[.*\]\(<\s*a[^>]*>(.*?)<\s*\/\s*a>\)/gi;return e.replace(t,(function(e){return e.replace(/<a /gi,'<a data-tomark-pass="" ')}))},t._appendAttributeForBrIfNeed=function(e){var t=/<br>/gi,n=/<br \/><br \/>/gi,r=/<br data-tomark-pass \/><br \/>(.)/gi,i=/([^>]|<\/a>|<\/code>|<\/span>|<\/b>|<\/i>|<\/s>|<img [^>]*>)/,o=/<br data-tomark-pass \/><br data-tomark-pass \/>/,a=new RegExp(i.source+o.source,"g"),s=/<br data-tomark-pass="">/gi;return e=e.replace(t,"<br />"),e=e.replace(n,"<br data-tomark-pass /><br data-tomark-pass />"),e=e.replace(s,"<br data-tomark-pass />"),e=e.replace(r,"<br data-tomark-pass /><br data-tomark-pass />$1"),e=e.replace(a,"$1<br /><br />"),e=e.replace(/(.)<br \/><br \/>(<h[1-6]>|<pre>|<table>|<ul>|<ol>|<blockquote>)/g,"$1<br /><br data-tomark-pass />$2"),e=e.replace(/(<\/h[1-6]>|<\/pre>|<\/table>|<\/ul>|<\/ol>|<\/blockquote>)<br \/>(.)/g,"$1<br data-tomark-pass />$2"),e},e}();t["a"]=m},function(e,t,n){"use strict";var r=n(3),i=n.n(r),o=n(9),a=n.n(o),s=function(){function e(){this.globalTOID=null,this.lazyRunFunctions={}}var t=e.prototype;return t.run=function(e,t,n,r){var i;return a()(e)?i=this._runRegisteredRun(e,t,n,r):(i=this._runSingleRun(e,t,n,r,this.globalTOID),this.globalTOID=i),i},t.registerLazyRunFunction=function(e,t,n,r){r=r||this,this.lazyRunFunctions[e]={fn:t,delay:n,context:r,TOID:null}},t._runSingleRun=function(e,t,n,r,i){return this._clearTOIDIfNeed(i),i=setTimeout((function(){e.call(n,t)}),r),i},t._runRegisteredRun=function(e,t,n,r){var i=this.lazyRunFunctions[e],o=i.fn,a=i.TOID;return r=r||i.delay,n=n||i.context,a=this._runSingleRun(o,t,n,r,a),i.TOID=a,a},t._clearTOIDIfNeed=function(e){e&&clearTimeout(e)},e}(),l=s,c=n(0),u=n(30),d=function(){function e(e,t,n,r){this.eventManager=t,this.convertor=n,this.el=e,this.isViewer=!!r,this.delayCodeBlockTime=500,this._initContentSection(),this.lazyRunner=new l}var t=e.prototype;return t._initContentSection=function(){this._previewContent=c["a"].createElementWith('<div class="tui-editor-contents"></div>'),this.el.appendChild(this._previewContent)},t.getCodeBlockElements=function(e){var t,n=this._previewContent,r=[];return t=e?e.map((function(e){return n.querySelector('[data-nodeid="'+e+'"]')})).filter(Boolean):[n],t.forEach((function(e){r.push.apply(r,c["a"].findAll(e,"code[data-language]"))})),r},t.invokeCodeBlockPlugins=function(e){e.forEach((function(e){var t=e.getAttribute("data-language"),n=u["a"].createCodeBlockHtml(t,e.textContent);e.innerHTML=n}))},t.refresh=function(e){void 0===e&&(e=""),this.render(this.convertor.toHTMLWithCodeHighlight(e)),this.invokeCodeBlockPlugins(this.getCodeBlockElements())},t.getHTML=function(){return this._previewContent.innerHTML},t.setHTML=function(e){this._previewContent.innerHTML=e},t.render=function(e){var t=this._previewContent;e=this.eventManager.emit("previewBeforeHook",e)||e,c["a"].empty(t),t.innerHTML=e},t.setHeight=function(e){i()(this.el,{height:e+"px"})},t.setMinHeight=function(e){i()(this.el,{minHeight:e+"px"})},t.isVisible=function(){return"none"!==this.el.style.display},e}();t["a"]=d},function(e,t,n){"use strict";var r=n(17),i=n(8);function o(e,t){t=r(t)?t.join(" "):t,t=t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),i(e.className.baseVal)?e.className=t:e.className.baseVal=t}e.exports=o},function(e,t,n){"use strict";var r="_feEventKey";function i(e,t){var n,i=e[r];return i||(i=e[r]={}),n=i[t],n||(n=i[t]=[]),n}e.exports=i},function(e,t,n){},function(e,t,n){"use strict";var r=n(28);function i(e){return r(e)&&!1!==e}e.exports=i},function(e,t,n){"use strict";var r=n(8),i=n(49),o=6048e5;function a(e){var t=(new Date).getTime();return t-e>o}function s(e,t){var n="https://www.google-analytics.com/collect",o=location.hostname,s="event",l="use",c="TOAST UI "+e+" for "+o+": Statistics",u=window.localStorage.getItem(c);(r(window.tui)||!1!==window.tui.usageStatistics)&&(u&&!a(u)||(window.localStorage.setItem(c,(new Date).getTime()),setTimeout((function(){"interactive"!==document.readyState&&"complete"!==document.readyState||i(n,{v:1,t:s,tid:t,cid:o,dp:o,dh:e,el:e,ec:l})}),1e3)))}e.exports=s},function(e,t,n){"use strict";var r=n(42);function i(e){return!r(e)}e.exports=i},function(e,t,n){"use strict";var r=n(34),i=n(17),o=n(2),a=n(26),s=function(){try{return Object.defineProperty({},"x",{}),!0}catch(e){return!1}}(),l=0;function c(e){e&&this.set.apply(this,arguments)}c.prototype.set=function(e){var t=this;i(e)||(e=o(arguments)),a(e,(function(e){t._addItem(e)}))},c.prototype.getName=function(e){var t,n=this;return a(this,(function(r,i){if(n._isEnumItem(i)&&e===r)return t=i,!1})),t},c.prototype._addItem=function(e){var t;this.hasOwnProperty(e)||(t=this._makeEnumValue(),s?Object.defineProperty(this,e,{enumerable:!0,configurable:!1,writable:!1,value:t}):this[e]=t)},c.prototype._makeEnumValue=function(){var e;return e=l,l+=1,e},c.prototype._isEnumItem=function(e){return r(this[e])},e.exports=c},function(e,t,n){"use strict";var r=n(10),i=n.n(r),o=n(7),a=n.n(o),s=n(19),l=n.n(s),c=n(20),u=n.n(c),d=n(35),h=n(36),f=n(1),p=n(37),g=n(0),m=n(30),v=n(31),b=n(12),_=n(25);function y(){return y=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},y.apply(this,arguments)}var C="data-te-task",w="checked",E=function(){function e(e){var t=this;this.options=a()({useDefaultHTMLSanitizer:!0,linkAttribute:null,extendedAutolinks:!1,customConvertor:null,customHTMLRenderer:null,referenceDefinition:!1,customHTMLSanitizer:null},e),this.codeBlockLanguages=[],this.eventManager=new h["a"],this.commandManager=new f["a"](this);var n=Object(b["c"])(this.options.linkAttribute),r=Object(v["a"])(this.options.plugins),o=r.renderer,s=r.parser,c=r.plugins,u=this.options,g=u.customHTMLRenderer,m=u.customHTMLSanitizer,C=u.extendedAutolinks,w=u.referenceDefinition,E={linkAttribute:n,customHTMLRenderer:y({},o,{},g),extendedAutolinks:C,referenceDefinition:w,customParser:s};this.options.customConvertor?this.convertor=new this.options.customConvertor(this.eventManager,E):this.convertor=new p["a"](this.eventManager,E);var T=m||(this.options.useDefaultHTMLSanitizer?_["a"]:null);T&&this.convertor.initHtmlSanitizer(T),this.options.hooks&&i()(this.options.hooks,(function(e,n){t.addHook(n,e)})),this.options.events&&i()(this.options.events,(function(e,n){t.on(n,e)}));var N=this.options,k=N.el,S=N.initialValue,x=k.innerHTML;k.innerHTML="",this.preview=new d["a"](k,this.eventManager,this.convertor,y({},E,{isViewer:!0})),l()(this.preview.el,"mousedown",this._toggleTask.bind(this)),c&&Object(v["b"])(c,this),S?this.setMarkdown(S):x&&this.preview.setHTML(x),this.eventManager.emit("load",this)}var t=e.prototype;return t._toggleTask=function(e){var t=getComputedStyle(e.target,":before");e.target.hasAttribute(C)&&g["a"].isInsideTaskBox(t,e.offsetX,e.offsetY)&&(g["a"].toggleClass(e.target,w),this.eventManager.emit("change",{source:"viewer",data:e}))},t.setMarkdown=function(e){this.markdownValue=e=e||"",this.preview.refresh(this.markdownValue),this.eventManager.emit("setMarkdownAfter",this.markdownValue)},t.on=function(e,t){this.eventManager.listen(e,t)},t.off=function(e){this.eventManager.removeEventHandler(e)},t.remove=function(){this.eventManager.emit("removeEditor"),u()(this.preview.el,"mousedown",this._toggleTask.bind(this)),this.preview.remove(),this.options=null,this.eventManager=null,this.commandManager=null,this.convertor=null,this.preview=null},t.addHook=function(e,t){this.eventManager.removeEventHandler(e),this.eventManager.listen(e,t)},t.isViewer=function(){return!0},t.isMarkdownMode=function(){return!1},t.isWysiwygMode=function(){return!1},t.setCodeBlockLanguages=function(e){var t=this;void 0===e&&(e=[]),e.forEach((function(e){t.codeBlockLanguages.indexOf(e)<0&&t.codeBlockLanguages.push(e)}))},e}();E.isViewer=!0,E.domUtils=g["a"],E.codeBlockManager=m["a"],E.WwCodeBlockManager=null,E.WwTableManager=null,E.WwTableSelectionManager=null,t["a"]=E},function(e,t,n){"use strict";function r(e,t){var n,r;function i(){r=Array.prototype.slice.call(arguments),window.clearTimeout(n),n=window.setTimeout((function(){e.apply(null,r)}),t)}return t=t||0,i}e.exports=r},function(e,t,n){"use strict";function r(e){return null===e}e.exports=r},function(e,t,n){"use strict";var r=n(10);function i(e,t){var n=document.createElement("img"),i="";return r(t,(function(e,t){i+="&"+t+"="+e})),i=i.substring(1),n.src=e+"?"+i,n.style.display="none",document.body.appendChild(n),document.body.removeChild(n),n}e.exports=i},function(e,t,n){},function(e,t,n){},function(e,t,n){},,function(e,t,n){"use strict";function r(e){return e===Object(e)}e.exports=r},function(e,t,n){!function(t,n){"use strict";function r(e,t,n){this.root=this.currentNode=e,this.nodeType=t,this.filter=n||fe}function i(e,t){for(var n=e.length;n--;)if(!t(e[n]))return!1;return!0}function o(e){return e.nodeType===U&&!!ge[e.nodeName]}function a(e){switch(e.nodeType){case q:return ve;case U:case W:if(ce&&ye.has(e))return ye.get(e);break;default:return me}var t;return t=i(e.childNodes,s)?pe.test(e.nodeName)?ve:be:_e,ce&&ye.set(e,t),t}function s(e){return a(e)===ve}function l(e){return a(e)===be}function c(e){return a(e)===_e}function u(e,t){var n=new r(t,V,l);return n.currentNode=e,n}function d(e,t){return e=u(e,t).previousNode(),e!==t?e:null}function h(e,t){return e=u(e,t).nextNode(),e!==t?e:null}function f(e){return!e.textContent&&!e.querySelector("IMG")}function p(e,t){return!o(e)&&e.nodeType===t.nodeType&&e.nodeName===t.nodeName&&"A"!==e.nodeName&&e.className===t.className&&(!e.style&&!t.style||e.style.cssText===t.style.cssText)}function g(e,t,n){if(e.nodeName!==t)return!1;for(var r in n)if(e.getAttribute(r)!==n[r])return!1;return!0}function m(e,t,n,r){for(;e&&e!==t;){if(g(e,n,r))return e;e=e.parentNode}return null}function v(e,t,n){for(;e&&e!==t;){if(n.test(e.nodeName))return e;e=e.parentNode}return null}function b(e,t){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function _(e,t,n){var r,i,o,a,s,l="";return e&&e!==t&&(l=_(e.parentNode,t,n),e.nodeType===U&&(l+=(l?">":"")+e.nodeName,(r=e.id)&&(l+="#"+r),(i=e.className.trim())&&(o=i.split(/\s\s*/),o.sort(),l+=".",l+=o.join(".")),(a=e.dir)&&(l+="[dir="+a+"]"),o&&(s=n.classNames,de.call(o,s.highlight)>-1&&(l+="[backgroundColor="+e.style.backgroundColor.replace(/ /g,"")+"]"),de.call(o,s.colour)>-1&&(l+="[color="+e.style.color.replace(/ /g,"")+"]"),de.call(o,s.fontFamily)>-1&&(l+="[fontFamily="+e.style.fontFamily.replace(/ /g,"")+"]"),de.call(o,s.fontSize)>-1&&(l+="[fontSize="+e.style.fontSize+"]")))),l}function y(e){var t=e.nodeType;return t===U||t===W?e.childNodes.length:e.length||0}function C(e){var t=e.parentNode;return t&&t.removeChild(e),e}function w(e,t){var n=e.parentNode;n&&n.replaceChild(t,e)}function E(e){for(var t=e.ownerDocument.createDocumentFragment(),n=e.childNodes,r=n?n.length:0;r--;)t.appendChild(e.firstChild);return t}function T(e,t,r,i){var o,a,s,l=e.createElement(t);if(r instanceof Array&&(i=r,r=null),r)for(o in r)r[o]!==n&&l.setAttribute(o,r[o]);if(i)for(a=0,s=i.length;a<s;a+=1)l.appendChild(i[a]);return l}function N(e,t){var n,r,i=t.__squire__,a=e.ownerDocument,l=e;if(e===t&&((r=e.firstChild)&&"BR"!==r.nodeName||(n=i.createDefaultBlock(),r?e.replaceChild(n,r):e.appendChild(n),e=n,n=null)),e.nodeType===q)return l;if(s(e)){for(r=e.firstChild;ae&&r&&r.nodeType===q&&!r.data;)e.removeChild(r),r=e.firstChild;r||(ae?(n=a.createTextNode(z),i._didAddZWS()):n=a.createTextNode(""))}else if(oe){for(;e.nodeType!==q&&!o(e);){if(!(r=e.firstChild)){n=a.createTextNode("");break}e=r}e.nodeType===q?/^ +$/.test(e.data)&&(e.data=""):o(e)&&e.parentNode.insertBefore(a.createTextNode(""),e)}else if("HR"!==e.nodeName&&!e.querySelector("BR"))for(n=T(a,"BR");(r=e.lastElementChild)&&!s(r);)e=r;if(n)try{e.appendChild(n)}catch(t){i.didError({name:"Squire: fixCursor – "+t,message:"Parent: "+e.nodeName+"/"+e.innerHTML+" appendChild: "+n.nodeName})}return l}function k(e,t){var n,r,i,o,a=e.childNodes,l=e.ownerDocument,u=null,d=t.__squire__._config;for(n=0,r=a.length;n<r;n+=1)i=a[n],o="BR"===i.nodeName,!o&&s(i)?(u||(u=T(l,d.blockTag,d.blockAttributes)),u.appendChild(i),n-=1,r-=1):(o||u)&&(u||(u=T(l,d.blockTag,d.blockAttributes)),N(u,t),o?e.replaceChild(u,i):(e.insertBefore(u,i),n+=1,r+=1),u=null),c(i)&&k(i,t);return u&&e.appendChild(N(u,t)),e}function S(e,t,n,r){var i,o,a,s=e.nodeType;if(s===q&&e!==n)return S(e.parentNode,e.splitText(t),n,r);if(s===U){if("number"==typeof t&&(t=t<e.childNodes.length?e.childNodes[t]:null),e===n)return t;for(i=e.parentNode,o=e.cloneNode(!1);t;)a=t.nextSibling,o.appendChild(t),t=a;return"OL"===e.nodeName&&m(e,r,"BLOCKQUOTE")&&(o.start=(+e.start||1)+e.childNodes.length-1),N(e,r),N(o,r),(a=e.nextSibling)?i.insertBefore(o,a):i.appendChild(o),S(i,o,n,r)}return t}function x(e,t){for(var n,r,i,o=e.childNodes,a=o.length,l=[];a--;)if(n=o[a],r=a&&o[a-1],a&&s(n)&&p(n,r)&&!ge[n.nodeName])t.startContainer===n&&(t.startContainer=r,t.startOffset+=y(r)),t.endContainer===n&&(t.endContainer=r,t.endOffset+=y(r)),t.startContainer===e&&(t.startOffset>a?t.startOffset-=1:t.startOffset===a&&(t.startContainer=r,t.startOffset=y(r))),t.endContainer===e&&(t.endOffset>a?t.endOffset-=1:t.endOffset===a&&(t.endContainer=r,t.endOffset=y(r))),C(n),n.nodeType===q?r.appendData(n.data):l.push(E(n));else if(n.nodeType===U){for(i=l.length;i--;)n.appendChild(l.pop());x(n,t)}}function L(e,t){if(e.nodeType===q&&(e=e.parentNode),e.nodeType===U){var n={startContainer:t.startContainer,startOffset:t.startOffset,endContainer:t.endContainer,endOffset:t.endOffset};x(e,n),t.setStart(n.startContainer,n.startOffset),t.setEnd(n.endContainer,n.endOffset)}}function M(e){var t=e.nodeName;return"TD"===t||"TH"===t||"TR"===t||"TBODY"===t||"THEAD"===t}function A(e,t,n,r){var i,o,a,s=t;if(!M(e)||!M(t)){for(;(i=s.parentNode)&&i!==r&&i.nodeType===U&&1===i.childNodes.length;)s=i;C(s),a=e.childNodes.length,o=e.lastChild,o&&"BR"===o.nodeName&&(e.removeChild(o),a-=1),e.appendChild(E(t)),n.setStart(e,a),n.collapse(!0),L(e,n),ee&&(o=e.lastChild)&&"BR"===o.nodeName&&e.removeChild(o)}}function B(e,t){var n,r,i=e.previousSibling,o=e.firstChild,a=e.ownerDocument,s="LI"===e.nodeName;if((!s||o&&/^[OU]L$/.test(o.nodeName))&&!M(e))if(i&&p(i,e)&&i.isContentEditable&&e.isContentEditable){if(!c(i)){if(!s)return;r=T(a,"DIV"),r.appendChild(E(i)),i.appendChild(r)}C(e),n=!c(e),i.appendChild(E(e)),n&&k(i,t),o&&B(o,t)}else s&&(i=T(a,"DIV"),e.insertBefore(i,o),N(i,t))}function O(e){this.isShiftDown=e.shiftKey}function D(e,t,n){var r,i;if(e||(e={}),t)for(r in t)!n&&r in e||(i=t[r],e[r]=i&&i.constructor===Object?D(e[r],i,n):i);return e}function R(e,t){e.nodeType===j&&(e=e.body);var n,r=e.ownerDocument,i=r.defaultView;this._win=i,this._doc=r,this._root=e,this._events={},this._isFocused=!1,this._lastSelection=null,se&&this.addEventListener("beforedeactivate",this.getSelection),this._hasZWS=!1,this._lastAnchorNode=null,this._lastFocusNode=null,this._path="",this._willUpdatePath=!1,"onselectionchange"in r?this.addEventListener("selectionchange",this._updatePathOnEvent):(this.addEventListener("keyup",this._updatePathOnEvent),this.addEventListener("mouseup",this._updatePathOnEvent)),this._undoIndex=-1,this._undoStack=[],this._undoStackLength=0,this._isInUndoState=!1,this._ignoreChange=!1,this._ignoreAllChanges=!1,le?(n=new MutationObserver(this._docWasChanged.bind(this)),n.observe(e,{childList:!0,attributes:!0,characterData:!0,subtree:!0}),this._mutation=n):this.addEventListener("keyup",this._keyUpDetectChange),this._restoreSelection=!1,this.addEventListener("blur",I),this.addEventListener("mousedown",P),this.addEventListener("touchstart",P),this.addEventListener("focus",H),this._awaitingPaste=!1,this.addEventListener(J?"beforecut":"cut",rt),this.addEventListener("copy",it),this.addEventListener("keydown",O),this.addEventListener("keyup",O),this.addEventListener(J?"beforepaste":"paste",ot),this.addEventListener("drop",at),this.addEventListener(ee?"keypress":"keydown",Pe),this._keyHandlers=Object.create(qe),this.setConfig(t),J&&(i.Text.prototype.splitText=function(e){var t=this.ownerDocument.createTextNode(this.data.slice(e)),n=this.nextSibling,r=this.parentNode,i=this.length-e;return n?r.insertBefore(t,n):r.appendChild(t),i&&this.deleteData(e,i),t}),e.setAttribute("contenteditable","true");try{r.execCommand("enableObjectResizing",!1,"false"),r.execCommand("enableInlineTableEditing",!1,"false")}catch(e){}e.__squire__=this,this.setHTML("")}function I(){this._restoreSelection=!0}function P(){this._restoreSelection=!1}function H(){this._restoreSelection&&this.setSelection(this._lastSelection)}function F(e,t,n){var r,i;for(r=t.firstChild;r;r=i){if(i=r.nextSibling,s(r)){if(r.nodeType===q||"BR"===r.nodeName||"IMG"===r.nodeName){n.appendChild(r);continue}}else if(l(r)){n.appendChild(e.createDefaultBlock([F(e,r,e._doc.createDocumentFragment())]));continue}F(e,r,n)}return n}var U=1,q=3,j=9,W=11,V=1,z="​",K=t.defaultView,G=navigator.userAgent,$=/Android/.test(G),Y=/iP(?:ad|hone|od)/.test(G),X=/Mac OS X/.test(G),Z=/Windows NT/.test(G),Q=/Gecko\//.test(G),J=/Trident\/[456]\./.test(G),ee=!!K.opera,te=/Edge\//.test(G),ne=!te&&/WebKit\//.test(G),re=/Trident\/[4567]\./.test(G),ie=X?"meta-":"ctrl-",oe=J||ee,ae=J||ne,se=J,le="undefined"!=typeof MutationObserver,ce="undefined"!=typeof WeakMap,ue=/[^ \t\r\n]/,de=Array.prototype.indexOf;Object.create||(Object.create=function(e){var t=function(){};return t.prototype=e,new t});var he={1:1,2:2,3:4,8:128,9:256,11:1024},fe=function(){return!0};r.prototype.nextNode=function(){for(var e,t=this.currentNode,n=this.root,r=this.nodeType,i=this.filter;;){for(e=t.firstChild;!e&&t&&t!==n;)(e=t.nextSibling)||(t=t.parentNode);if(!e)return null;if(he[e.nodeType]&r&&i(e))return this.currentNode=e,e;t=e}},r.prototype.previousNode=function(){for(var e,t=this.currentNode,n=this.root,r=this.nodeType,i=this.filter;;){if(t===n)return null;if(e=t.previousSibling)for(;t=e.lastChild;)e=t;else e=t.parentNode;if(!e)return null;if(he[e.nodeType]&r&&i(e))return this.currentNode=e,e;t=e}},r.prototype.previousPONode=function(){for(var e,t=this.currentNode,n=this.root,r=this.nodeType,i=this.filter;;){for(e=t.lastChild;!e&&t&&t!==n;)(e=t.previousSibling)||(t=t.parentNode);if(!e)return null;if(he[e.nodeType]&r&&i(e))return this.currentNode=e,e;t=e}};var pe=/^(?:#text|A(?:BBR|CRONYM)?|B(?:R|D[IO])?|C(?:ITE|ODE)|D(?:ATA|EL|FN)|EM|FONT|I(?:FRAME|MG|NPUT|NS)?|KBD|Q|R(?:P|T|UBY)|S(?:AMP|MALL|PAN|TR(?:IKE|ONG)|U[BP])?|TIME|U|VAR|WBR)$/,ge={BR:1,HR:1,IFRAME:1,IMG:1,INPUT:1},me=0,ve=1,be=2,_e=3,ye=ce?new WeakMap:null,Ce=function(e,t){for(var n=e.childNodes;t&&e.nodeType===U;)e=n[t-1],n=e.childNodes,t=n.length;return e},we=function(e,t){if(e.nodeType===U){var n=e.childNodes;if(t<n.length)e=n[t];else{for(;e&&!e.nextSibling;)e=e.parentNode;e&&(e=e.nextSibling)}}return e},Ee=function(e,t){var n,r,i,o,a=e.startContainer,s=e.startOffset,l=e.endContainer,c=e.endOffset;a.nodeType===q?(n=a.parentNode,r=n.childNodes,s===a.length?(s=de.call(r,a)+1,e.collapsed&&(l=n,c=s)):(s&&(o=a.splitText(s),l===a?(c-=s,l=o):l===n&&(c+=1),a=o),s=de.call(r,a)),a=n):r=a.childNodes,i=r.length,s===i?a.appendChild(t):a.insertBefore(t,r[s]),a===l&&(c+=r.length-i),e.setStart(a,s),e.setEnd(l,c)},Te=function(e,t,n){var r=e.startContainer,i=e.startOffset,o=e.endContainer,a=e.endOffset;t||(t=e.commonAncestorContainer),t.nodeType===q&&(t=t.parentNode);for(var s,l,c,u,d,h=S(o,a,t,n),f=S(r,i,t,n),p=t.ownerDocument.createDocumentFragment();f!==h;)s=f.nextSibling,p.appendChild(f),f=s;return r=t,i=h?de.call(t.childNodes,h):t.childNodes.length,c=t.childNodes[i],l=c&&c.previousSibling,l&&l.nodeType===q&&c.nodeType===q&&(r=l,i=l.length,u=l.data,d=c.data," "===u.charAt(u.length-1)&&" "===d.charAt(0)&&(d=" "+d.slice(1)),l.appendData(d),C(c)),e.setStart(r,i),e.collapse(!0),N(t,n),p},Ne=function(e,t){var n,r,i=Me(e,t),o=Ae(e,t),a=i!==o;return xe(e),Le(e,i,o,t),n=Te(e,null,t),xe(e),a&&(o=Ae(e,t),i&&o&&i!==o&&A(i,o,e,t)),i&&N(i,t),r=t.firstChild,r&&"BR"!==r.nodeName?e.collapse(!0):(N(t,t),e.selectNodeContents(t.firstChild)),n},ke=function(e,t,n){var r,i,o,a,l,u,f,p,g,b;for(k(t,n),r=t;r=h(r,n);)N(r,n);if(e.collapsed||Ne(e,n),xe(e),e.collapse(!1),a=m(e.endContainer,n,"BLOCKQUOTE")||n,i=Me(e,n),f=h(t,t),i&&f&&!v(f,t,/PRE|TABLE|H[1-6]|OL|UL|BLOCKQUOTE/)){if(Le(e,i,i,n),e.collapse(!0),l=e.endContainer,u=e.endOffset,tt(i,n,!1),s(l)&&(p=S(l,u,d(l,n),n),l=p.parentNode,u=de.call(l.childNodes,p)),u!==y(l))for(o=n.ownerDocument.createDocumentFragment();r=l.childNodes[u];)o.appendChild(r);A(l,f,e,n),u=de.call(l.parentNode.childNodes,l)+1,l=l.parentNode,e.setEnd(l,u)}y(t)&&(Le(e,a,a,n),p=S(e.endContainer,e.endOffset,a,n),g=p?p.previousSibling:a.lastChild,a.insertBefore(t,p),p?e.setEndBefore(p):e.setEnd(a,y(a)),i=Ae(e,n),xe(e),l=e.endContainer,u=e.endOffset,p&&c(p)&&B(p,n),p=g&&g.nextSibling,p&&c(p)&&B(p,n),e.setEnd(l,u)),o&&(b=e.cloneRange(),A(i,o,b,n),e.setEnd(b.endContainer,b.endOffset)),xe(e)},Se=function(e,t,n){var r=t.ownerDocument.createRange();if(r.selectNode(t),n){var i=e.compareBoundaryPoints(3,r)>-1,o=e.compareBoundaryPoints(1,r)<1;return!i&&!o}var a=e.compareBoundaryPoints(0,r)<1,s=e.compareBoundaryPoints(2,r)>-1;return a&&s},xe=function(e){for(var t,n=e.startContainer,r=e.startOffset,i=e.endContainer,a=e.endOffset,s=!0;n.nodeType!==q&&(t=n.childNodes[r])&&!o(t);)n=t,r=0;if(a)for(;i.nodeType!==q;){if(!(t=i.childNodes[a-1])||o(t)){if(s&&t&&"BR"===t.nodeName){a-=1,s=!1;continue}break}i=t,a=y(i)}else for(;i.nodeType!==q&&(t=i.firstChild)&&!o(t);)i=t;e.collapsed?(e.setStart(i,a),e.setEnd(n,r)):(e.setStart(n,r),e.setEnd(i,a))},Le=function(e,t,n,r){var i,o=e.startContainer,a=e.startOffset,s=e.endContainer,l=e.endOffset,c=!0;for(t||(t=e.commonAncestorContainer),n||(n=t);!a&&o!==t&&o!==r;)i=o.parentNode,a=de.call(i.childNodes,o),o=i;for(;;){if(c&&s.nodeType!==q&&s.childNodes[l]&&"BR"===s.childNodes[l].nodeName&&(l+=1,c=!1),s===n||s===r||l!==y(s))break;i=s.parentNode,l=de.call(i.childNodes,s)+1,s=i}e.setStart(o,a),e.setEnd(s,l)},Me=function(e,t){var n,r=e.startContainer;return s(r)?n=d(r,t):r!==t&&l(r)?n=r:(n=Ce(r,e.startOffset),n=h(n,t)),n&&Se(e,n,!0)?n:null},Ae=function(e,t){var n,r,i=e.endContainer;if(s(i))n=d(i,t);else if(i!==t&&l(i))n=i;else{if(!(n=we(i,e.endOffset))||!b(t,n))for(n=t;r=n.lastChild;)n=r;n=d(n,t)}return n&&Se(e,n,!0)?n:null},Be=new r(null,4|V,(function(e){return e.nodeType===q?ue.test(e.data):"IMG"===e.nodeName})),Oe=function(e,t){var n,r=e.startContainer,i=e.startOffset;if(Be.root=null,r.nodeType===q){if(i)return!1;n=r}else if(n=we(r,i),n&&!b(t,n)&&(n=null),!n&&(n=Ce(r,i),n.nodeType===q&&n.length))return!1;return Be.currentNode=n,Be.root=Me(e,t),!Be.previousNode()},De=function(e,t){var n,r=e.endContainer,i=e.endOffset;if(Be.root=null,r.nodeType===q){if((n=r.data.length)&&i<n)return!1;Be.currentNode=r}else Be.currentNode=Ce(r,i);return Be.root=Ae(e,t),!Be.nextNode()},Re=function(e,t){var n,r=Me(e,t),i=Ae(e,t);r&&i&&(n=r.parentNode,e.setStart(n,de.call(n.childNodes,r)),n=i.parentNode,e.setEnd(n,de.call(n.childNodes,i)+1))},Ie={8:"backspace",9:"tab",13:"enter",32:"space",33:"pageup",34:"pagedown",37:"left",39:"right",46:"delete",219:"[",221:"]"},Pe=function(e){var t=e.keyCode,n=Ie[t],r="",i=this.getSelection();e.defaultPrevented||(n||(n=String.fromCharCode(t).toLowerCase(),/^[A-Za-z0-9]$/.test(n)||(n="")),ee&&46===e.which&&(n="."),111<t&&t<124&&(n="f"+(t-111)),"backspace"!==n&&"delete"!==n&&(e.altKey&&(r+="alt-"),e.ctrlKey&&(r+="ctrl-"),e.metaKey&&(r+="meta-")),e.shiftKey&&(r+="shift-"),n=r+n,this._keyHandlers[n]?this._keyHandlers[n](this,e,i):i.collapsed||e.isComposing||e.ctrlKey||e.metaKey||1!==(re?n:e.key||n).length||(this.saveUndoState(i),Ne(i,this._root),this._ensureBottomLine(),this.setSelection(i),this._updatePath(i,!0)))},He=function(e){return function(t,n){n.preventDefault(),t[e]()}},Fe=function(e,t){return t=t||null,function(n,r){r.preventDefault();var i=n.getSelection();n.hasFormat(e,null,i)?n.changeFormat(null,{tag:e},i):n.changeFormat({tag:e},t,i)}},Ue=function(e,t){try{t||(t=e.getSelection());var n,r=t.startContainer;for(r.nodeType===q&&(r=r.parentNode),n=r;s(n)&&(!n.textContent||n.textContent===z);)r=n,n=r.parentNode;r!==n&&(t.setStart(n,de.call(n.childNodes,r)),t.collapse(!0),n.removeChild(r),l(n)||(n=d(n,e._root)),N(n,e._root),xe(t)),r===e._root&&(r=r.firstChild)&&"BR"===r.nodeName&&C(r),e._ensureBottomLine(),e.setSelection(t),e._updatePath(t,!0)}catch(t){e.didError(t)}},qe={enter:function(e,t,n){var r,i,o,a,s,l=e._root;if(t.preventDefault(),e._recordUndoState(n),m(n.startContainer,l,"PRE",{"data-te-codeblock":""})||Et(n.startContainer,l,e),e._removeZWS(),e._getRangeAndRemoveBookmark(n),n.collapsed||Ne(n,l),(r=Me(n,l))&&(i=m(r,l,"PRE")))return xe(n),o=n.startContainer,a=n.startOffset,o.nodeType!==q&&(o=e._doc.createTextNode(""),i.insertBefore(o,i.firstChild)),t.shiftKey||"\n"!==o.data.charAt(a-1)&&!Oe(n,l)||"\n"!==o.data.charAt(a)&&!De(n,l)?(o.insertData(a,"\n"),N(i,l),o.length===a+1?n.setStartAfter(o):n.setStart(o,a+1)):(o.deleteData(a&&a-1,a?2:1),s=S(o,a&&a-1,l,l),o=s.previousSibling,o.textContent||C(o),o=e.createDefaultBlock(),s.parentNode.insertBefore(o,s),s.textContent||C(s),n.setStart(o,0)),n.collapse(!0),e.setSelection(n),e._updatePath(n,!0),void e._docWasChanged();if(!r||t.shiftKey||/^T[HD]$/.test(r.nodeName))return i=m(n.endContainer,l,"A"),i&&(i=i.parentNode,Le(n,i,i,l),n.collapse(!1)),Ee(n,e.createElement("BR")),n.collapse(!1),e.setSelection(n),void e._updatePath(n,!0);if((i=m(r,l,"LI"))&&(r=i),f(r)){if(m(r,l,"UL")||m(r,l,"OL"))return e.decreaseListLevel(n);if(m(r,l,"BLOCKQUOTE"))return e.modifyBlocks(vt,n)}for(s=pt(e,r,n.startContainer,n.startOffset),dt(r),Qe(r),N(r,l);s.nodeType===U;){var c,u=s.firstChild;if("A"===s.nodeName&&(!s.textContent||s.textContent===z)){u=e._doc.createTextNode(""),w(s,u),s=u;break}for(;u&&u.nodeType===q&&!u.data&&(c=u.nextSibling)&&"BR"!==c.nodeName;)C(u),u=c;if(!u||"BR"===u.nodeName||u.nodeType===q&&!ee)break;s=u}n=e.createRange(s,0),e.setSelection(n),e._updatePath(n,!0)},"shift-enter":function(e,t,n){return e._keyHandlers.enter(e,t,n)},backspace:function(e,t,n){var r=e._root;if(e._removeZWS(),e.saveUndoState(n),n.collapsed)if(Oe(n,r)){t.preventDefault();var i,o=Me(n,r);if(!o)return;if(k(o.parentNode,r),i=d(o,r)){if(!i.isContentEditable){for(;!i.parentNode.isContentEditable;)i=i.parentNode;return void C(i)}for(A(i,o,n,r),o=i.parentNode;o!==r&&!o.nextSibling;)o=o.parentNode;o!==r&&(o=o.nextSibling)&&B(o,r),e.setSelection(n)}else if(o){if(m(o,r,"UL")||m(o,r,"OL"))return e.decreaseListLevel(n);if(m(o,r,"BLOCKQUOTE"))return e.modifyBlocks(mt,n);e.setSelection(n),e._updatePath(n,!0)}}else e.setSelection(n),setTimeout((function(){Ue(e)}),0);else t.preventDefault(),Ne(n,r),Ue(e,n)},delete:function(e,t,n){var r,i,o,a,s,l,c=e._root;if(e._removeZWS(),e.saveUndoState(n),n.collapsed)if(De(n,c)){if(t.preventDefault(),!(r=Me(n,c)))return;if(k(r.parentNode,c),i=h(r,c)){if(!i.isContentEditable){for(;!i.parentNode.isContentEditable;)i=i.parentNode;return void C(i)}for(A(r,i,n,c),i=r.parentNode;i!==c&&!i.nextSibling;)i=i.parentNode;i!==c&&(i=i.nextSibling)&&B(i,c),e.setSelection(n),e._updatePath(n,!0)}}else{if(o=n.cloneRange(),Le(n,c,c,c),a=n.endContainer,s=n.endOffset,a.nodeType===U&&(l=a.childNodes[s])&&"IMG"===l.nodeName)return t.preventDefault(),C(l),xe(n),void Ue(e,n);e.setSelection(o),setTimeout((function(){Ue(e)}),0)}else t.preventDefault(),Ne(n,c),Ue(e,n)},tab:function(e,t,n){var r,i,o=e._root;if(e._removeZWS(),n.collapsed&&Oe(n,o))for(r=Me(n,o);i=r.parentNode;){if("UL"===i.nodeName||"OL"===i.nodeName){t.preventDefault(),e.increaseListLevel(n);break}r=i}},"shift-tab":function(e,t,n){var r,i=e._root;e._removeZWS(),n.collapsed&&Oe(n,i)&&(r=n.startContainer,(m(r,i,"UL")||m(r,i,"OL"))&&(t.preventDefault(),e.decreaseListLevel(n)))},space:function(e,t,n){var r,i=e._root;if(e._recordUndoState(n),Et(n.startContainer,i,e),e._getRangeAndRemoveBookmark(n),r=n.endContainer,n.collapsed&&n.endOffset===y(r))do{if("A"===r.nodeName){n.setStartAfter(r);break}}while(!r.nextSibling&&(r=r.parentNode)&&r!==i);n.collapsed||(Ne(n,i),e._ensureBottomLine(),e.setSelection(n),e._updatePath(n,!0)),e.setSelection(n)},left:function(e){e._removeZWS()},right:function(e){e._removeZWS()}};X&&Q&&(qe["meta-left"]=function(e,t){t.preventDefault();var n=ut(e);n&&n.modify&&n.modify("move","backward","lineboundary")},qe["meta-right"]=function(e,t){t.preventDefault();var n=ut(e);n&&n.modify&&n.modify("move","forward","lineboundary")}),X||(qe.pageup=function(e){e.moveCursorToStart()},qe.pagedown=function(e){e.moveCursorToEnd()}),qe[ie+"b"]=Fe("B"),qe[ie+"i"]=Fe("I"),qe[ie+"u"]=Fe("U"),qe[ie+"shift-7"]=Fe("S"),qe[ie+"shift-5"]=Fe("SUB",{tag:"SUP"}),qe[ie+"shift-6"]=Fe("SUP",{tag:"SUB"}),qe[ie+"shift-8"]=He("makeUnorderedList"),qe[ie+"shift-9"]=He("makeOrderedList"),qe[ie+"["]=He("decreaseQuoteLevel"),qe[ie+"]"]=He("increaseQuoteLevel"),qe[ie+"d"]=He("toggleCode"),qe[ie+"y"]=He("redo"),qe[ie+"z"]=He("undo"),qe[ie+"shift-z"]=He("redo");var je={1:10,2:13,3:16,4:18,5:24,6:32,7:48},We={backgroundColor:{regexp:ue,replace:function(e,t,n){return T(e,"SPAN",{class:t.highlight,style:"background-color:"+n})}},color:{regexp:ue,replace:function(e,t,n){return T(e,"SPAN",{class:t.colour,style:"color:"+n})}},fontWeight:{regexp:/^bold|^700/i,replace:function(e){return T(e,"B")}},fontStyle:{regexp:/^italic/i,replace:function(e){return T(e,"I")}},fontFamily:{regexp:ue,replace:function(e,t,n){return T(e,"SPAN",{class:t.fontFamily,style:"font-family:"+n})}},fontSize:{regexp:ue,replace:function(e,t,n){return T(e,"SPAN",{class:t.fontSize,style:"font-size:"+n})}},textDecoration:{regexp:/^underline/i,replace:function(e){return T(e,"U")}}},Ve=function(e){return function(t,n){var r=T(t.ownerDocument,e);return n.replaceChild(r,t),r.appendChild(E(t)),r}},ze=function(e,t,n){var r,i,o,a,s,l,c=e.style,u=e.ownerDocument;for(r in We)i=We[r],(o=c[r])&&i.regexp.test(o)&&(l=i.replace(u,n.classNames,o),s||(s=l),a&&a.appendChild(l),a=l,e.style[r]="");return s&&(a.appendChild(E(e)),"SPAN"===e.nodeName?t.replaceChild(s,e):e.appendChild(s)),a||e},Ke={P:ze,SPAN:ze,STRONG:Ve("B"),EM:Ve("I"),INS:Ve("U"),STRIKE:Ve("S"),FONT:function(e,t,n){var r,i,o,a,s,l=e.face,c=e.size,u=e.color,d=e.ownerDocument,h=n.classNames;return l&&(r=T(d,"SPAN",{class:h.fontFamily,style:"font-family:"+l}),s=r,a=r),c&&(i=T(d,"SPAN",{class:h.fontSize,style:"font-size:"+je[c]+"px"}),s||(s=i),a&&a.appendChild(i),a=i),u&&/^#?([\dA-F]{3}){1,2}$/i.test(u)&&("#"!==u.charAt(0)&&(u="#"+u),o=T(d,"SPAN",{class:h.colour,style:"color:"+u}),s||(s=o),a&&a.appendChild(o),a=o),s||(s=a=T(d,"SPAN")),t.replaceChild(s,e),a.appendChild(E(e)),a},TT:function(e,t,n){var r=T(e.ownerDocument,"SPAN",{class:n.classNames.fontFamily,style:'font-family:menlo,consolas,"courier new",monospace'});return t.replaceChild(r,e),r.appendChild(E(e)),r}},Ge=/^(?:A(?:DDRESS|RTICLE|SIDE|UDIO)|BLOCKQUOTE|CAPTION|D(?:[DLT]|IV)|F(?:IGURE|IGCAPTION|OOTER)|H[1-6]|HEADER|HR|L(?:ABEL|EGEND|I)|O(?:L|UTPUT)|P(?:RE)?|SECTION|T(?:ABLE|BODY|D|FOOT|H|HEAD|R)|COL(?:GROUP)?|UL)$/,$e=/^(?:HEAD|META|STYLE)/,Ye=new r(null,4|V),Xe=function(e,t){var n,r=t.allowedBlocks,i=!1,o=r.length;if(o){for(n=0;n<o;n+=1)r[n]=r[n].toUpperCase();i=new RegExp(r.join("|")).test(e)}return Ge.test(e)||i},Ze=function e(t,n,r){var i,o,a,l,c,u,d,h,f,p,g,m,v=t.childNodes;for(i=t;s(i);)i=i.parentNode;for(Ye.root=i,o=0,a=v.length;o<a;o+=1)if(l=v[o],c=l.nodeName,u=l.nodeType,d=Ke[c],u===U){if(h=l.childNodes.length,d)l=d(l,t,n);else{if($e.test(c)){t.removeChild(l),o-=1,a-=1;continue}if(!Xe(c,n)&&!s(l)){o-=1,a+=h-1,t.replaceChild(E(l),l);continue}}h&&e(l,n,r||"PRE"===c)}else{if(u===q){if(g=l.data,f=!ue.test(g.charAt(0)),p=!ue.test(g.charAt(g.length-1)),r||!f&&!p)continue;if(f){for(Ye.currentNode=l;(m=Ye.previousPONode())&&!("IMG"===(c=m.nodeName)||"#text"===c&&ue.test(m.data));)if(!s(m)){m=null;break}g=g.replace(/^[ \t\r\n]+/g,m?" ":"")}if(p){for(Ye.currentNode=l;(m=Ye.nextNode())&&!("IMG"===c||"#text"===c&&ue.test(m.data));)if(!s(m)){m=null;break}g=g.replace(/[ \t\r\n]+$/g,m?" ":"")}if(g){l.data=g;continue}}t.removeChild(l),o-=1,a-=1}return t},Qe=function e(t){for(var n,r=t.childNodes,i=r.length;i--;)if(n=r[i],n.nodeType!==U||o(n))n.nodeType!==q||n.data||t.removeChild(n);else{e(n);var a="FIGURE"===n.tagName;!s(n)&&!a||n.firstChild||t.removeChild(n)}},Je=function(e){return e.nodeType===U?"BR"===e.nodeName||"IMG"===e.nodeName:ue.test(e.data)},et=function(e,t){for(var n,i=e.parentNode;s(i);)i=i.parentNode;return n=new r(i,4|V,Je),n.currentNode=e,!!n.nextNode()||t&&!n.previousNode()},tt=function(e,t,n){var r,i,o,a=e.querySelectorAll("BR"),l=[],c=a.length;for(r=0;r<c;r+=1)l[r]=et(a[r],n);for(;c--;)i=a[c],(o=i.parentNode)&&(l[c]?s(o)||k(o,t):C(i))},nt=function(e,t,n,r){var i,o,a=t.ownerDocument.body,s=r.willCutCopy;tt(t,n,!0),t.setAttribute("style","position:fixed;overflow:hidden;bottom:100%;right:100%;"),a.appendChild(t),i=t.innerHTML,o=t.innerText||t.textContent,s&&(i=s(i)),Z&&(o=o.replace(/\r?\n/g,"\r\n")),e.setData("text/html",i),e.setData("text/plain",o),a.removeChild(t)},rt=function(e){var t,n,r,i,o,a,s,l=e.clipboardData,c=this.getSelection(),u=this._root,d=this;if(c.collapsed)e.preventDefault();else{if(this.saveUndoState(c),te||Y||!l)setTimeout((function(){try{d._ensureBottomLine()}catch(e){d.didError(e)}}),0);else{for(t=Me(c,u),n=Ae(c,u),r=t===n&&t||u,i=Ne(c,u),o=c.commonAncestorContainer,o.nodeType===q&&(o=o.parentNode);o&&o!==r;)a=o.cloneNode(!1),a.appendChild(i),i=a,o=o.parentNode;s=this.createElement("div"),s.appendChild(i),nt(l,s,u,this._config),e.preventDefault()}this.setSelection(c)}},it=function(e){var t,n,r,i,o,a,s,l=e.clipboardData,c=this.getSelection(),u=this._root;if(!te&&!Y&&l){for(t=Me(c,u),n=Ae(c,u),r=t===n&&t||u,c=c.cloneRange(),xe(c),Le(c,r,r,u),i=c.cloneContents(),o=c.commonAncestorContainer,o.nodeType===q&&(o=o.parentNode);o&&o!==r;)a=o.cloneNode(!1),a.appendChild(i),i=a,o=o.parentNode;s=this.createElement("div"),s.appendChild(i),nt(l,s,u,this._config),e.preventDefault()}},ot=function(e){var t,n,r,i,o,a=e.clipboardData,s=a&&a.items,l=this.isShiftDown,c=!1,u=!1,d=null,h=this;if(te&&s){for(t=s.length;t--;)!l&&/^image\/.*/.test(s[t].type)&&(u=!0);u||(s=null)}if(s){for(e.preventDefault(),t=s.length;t--;){if(n=s[t],r=n.type,!l&&"text/html"===r)return void n.getAsString((function(e){h.insertHTML(e,!0)}));"text/plain"===r&&(d=n),!l&&/^image\/.*/.test(r)&&(u=!0)}u?(this.fireEvent("dragover",{dataTransfer:a,preventDefault:function(){c=!0}}),c&&this.fireEvent("drop",{dataTransfer:a})):d&&d.getAsString((function(e){h.insertPlainText(e,!0)}))}else{if(i=a&&a.types,!te&&i&&(de.call(i,"text/html")>-1||!Q&&de.call(i,"text/plain")>-1&&de.call(i,"text/rtf")<0))return e.preventDefault(),void(!l&&(o=a.getData("text/html"))?this.insertHTML(o,!0):((o=a.getData("text/plain"))||(o=a.getData("text/uri-list")))&&this.insertPlainText(o,!0));this._awaitingPaste=!0;var f=this._doc.body,p=this.getSelection(),g=p.startContainer,m=p.startOffset,v=p.endContainer,b=p.endOffset,_=this.createElement("DIV",{contenteditable:"true",style:"position:fixed; overflow:hidden; top:0; right:100%; width:1px; height:1px;"});f.appendChild(_),p.selectNodeContents(_),this.setSelection(p),setTimeout((function(){try{h._awaitingPaste=!1;for(var e,t,n="",r=_;_=r;)r=_.nextSibling,C(_),e=_.firstChild,e&&e===_.lastChild&&"DIV"===e.nodeName&&(_=e),n+=_.innerHTML;t=h.createRange(g,m,v,b),h.setSelection(t),n&&h.insertHTML(n,!0)}catch(e){h.didError(e)}}),0)}},at=function(e){for(var t=e.dataTransfer.types,n=t.length,r=!1,i=!1;n--;)switch(t[n]){case"text/plain":r=!0;break;case"text/html":i=!0;break;default:return}(i||r)&&this.saveUndoState()},st=R.prototype,lt=function(e,t,n){var r=n._doc,i=e?DOMPurify.sanitize(e,{ALLOW_UNKNOWN_PROTOCOLS:!0,WHOLE_DOCUMENT:!1,RETURN_DOM:!0,RETURN_DOM_FRAGMENT:!0}):null;return i?r.importNode(i,!0):r.createDocumentFragment()};st.setConfig=function(e){return e=D({blockTag:"DIV",blockAttributes:null,tagAttributes:{blockquote:null,ul:null,ol:null,li:null,a:null},classNames:{colour:"colour",fontFamily:"font",fontSize:"size",highlight:"highlight"},leafNodeNames:ge,undo:{documentSizeThreshold:-1,undoLimit:-1},isInsertedHTMLSanitized:!0,isSetHTMLSanitized:!0,sanitizeToDOMFragment:"undefined"!=typeof DOMPurify&&DOMPurify.isSupported?lt:null,willCutCopy:null,allowedBlocks:[]},e,!0),e.blockTag=e.blockTag.toUpperCase(),this._config=e,this},st.createElement=function(e,t,n){return T(this._doc,e,t,n)},st.createDefaultBlock=function(e){var t=this._config;return N(this.createElement(t.blockTag,t.blockAttributes,e),this._root)},st.didError=function(e){},st.getDocument=function(){return this._doc},st.getRoot=function(){return this._root},st.modifyDocument=function(e){var t=this._mutation;t&&(t.takeRecords().length&&this._docWasChanged(),t.disconnect()),this._ignoreAllChanges=!0,e(),this._ignoreAllChanges=!1,t&&(t.observe(this._root,{childList:!0,attributes:!0,characterData:!0,subtree:!0}),this._ignoreChange=!1)};var ct={pathChange:1,select:1,input:1,undoStateChange:1};st.fireEvent=function(e,t){var n,r,i,o=this._events[e];if(/^(?:focus|blur)/.test(e))if(n=this._root===this._doc.activeElement,"focus"===e){if(!n||this._isFocused)return this;this._isFocused=!0}else{if(n||!this._isFocused)return this;this._isFocused=!1}if(o)for(t||(t={}),t.type!==e&&(t.type=e),o=o.slice(),r=o.length;r--;){i=o[r];try{i.handleEvent?i.handleEvent(t):i.call(this,t)}catch(t){t.details="Squire: fireEvent error. Event type: "+e,this.didError(t)}}return this},st.destroy=function(){var e,t=this._events;for(e in t)this.removeEventListener(e);this._mutation&&this._mutation.disconnect(),delete this._root.__squire__,this._undoIndex=-1,this._undoStack=[],this._undoStackLength=0},st.handleEvent=function(e){this.fireEvent(e.type,e)},st.addEventListener=function(e,t){var n=this._events[e],r=this._root;return t?(n||(n=this._events[e]=[],ct[e]||("selectionchange"===e&&(r=this._doc),r.addEventListener(e,this,!0))),n.push(t),this):(this.didError({name:"Squire: addEventListener with null or undefined fn",message:"Event type: "+e}),this)},st.removeEventListener=function(e,t){var n,r=this._events[e],i=this._root;if(r){if(t)for(n=r.length;n--;)r[n]===t&&r.splice(n,1);else r.length=0;r.length||(delete this._events[e],ct[e]||("selectionchange"===e&&(i=this._doc),i.removeEventListener(e,this,!0)))}return this},st.createRange=function(e,t,n,r){if(e instanceof this._win.Range)return e.cloneRange();var i=this._doc.createRange();return i.setStart(e,t),n?i.setEnd(n,r):i.setEnd(e,t),i},st.getCursorPosition=function(e){if(!e&&!(e=this.getSelection())||!e.getBoundingClientRect)return null;var t,n,r=e.getBoundingClientRect();return r&&!r.top&&(this._ignoreChange=!0,t=this._doc.createElement("SPAN"),t.textContent=z,Ee(e,t),r=t.getBoundingClientRect(),n=t.parentNode,n.removeChild(t),L(n,e),this._ignoreChange=!1),r},st._moveCursorTo=function(e){var t=this._root,n=this.createRange(t,e?0:t.childNodes.length);return xe(n),this.setSelection(n),this},st.moveCursorToStart=function(){return this._moveCursorTo(!0)},st.moveCursorToEnd=function(){return this._moveCursorTo(!1)};var ut=function(e){return e._win.getSelection()||null};st.setSelection=function(e){if(e)if(this._lastSelection=e,this._isFocused)if($&&!this._restoreSelection)I.call(this),this.blur(),this.focus();else{Y&&this._win.focus();var t=ut(this);t&&(t.removeAllRanges(),t.addRange(e))}else I.call(this);return this},st.getSelection=function(){var e,t,n,r,i=ut(this),a=this._root;return this._isFocused&&i&&i.rangeCount&&(e=i.getRangeAt(0).cloneRange(),t=e.startContainer,n=e.endContainer,t&&o(t)&&e.setStartBefore(t),n&&o(n)&&e.setEndBefore(n)),e&&b(a,e.commonAncestorContainer)?this._lastSelection=e:(e=this._lastSelection,r=e.commonAncestorContainer,b(r.ownerDocument,r)||(e=null)),e||(e=this.createRange(a.firstChild,0)),e},st.getSelectedText=function(){var e=this.getSelection();if(!e||e.collapsed)return"";var t,n=new r(e.commonAncestorContainer,4|V,(function(t){return Se(e,t,!0)})),i=e.startContainer,o=e.endContainer,a=n.currentNode=i,l="",c=!1;for(n.filter(a)||(a=n.nextNode());a;)a.nodeType===q?(t=a.data)&&/\S/.test(t)&&(a===o&&(t=t.slice(0,e.endOffset)),a===i&&(t=t.slice(e.startOffset)),l+=t,c=!0):("BR"===a.nodeName||c&&!s(a))&&(l+="\n",c=!1),a=n.nextNode();return l},st.getPath=function(){return this._path};var dt=function(e,t){for(var n,i,o,a=new r(e,4);i=a.nextNode();)for(;(o=i.data.indexOf(z))>-1&&(!t||i.parentNode!==t);){if(1===i.length){do{n=i.parentNode,n.removeChild(i),i=n,a.currentNode=n}while(s(i)&&!y(i));break}i.deleteData(o,1)}};st._didAddZWS=function(){this._hasZWS=!0},st._removeZWS=function(){this._hasZWS&&(dt(this._root),this._hasZWS=!1)},st._updatePath=function(e,t){if(e){var n,r=e.startContainer,i=e.endContainer;(t||r!==this._lastAnchorNode||i!==this._lastFocusNode)&&(this._lastAnchorNode=r,this._lastFocusNode=i,n=r&&i?r===i?_(i,this._root,this._config):"(selection)":"",this._path!==n&&(this._path=n,this.fireEvent("pathChange",{path:n}))),this.fireEvent(e.collapsed?"cursor":"select",{range:e})}},st._updatePathOnEvent=function(e){var t=this;t._isFocused&&!t._willUpdatePath&&(t._willUpdatePath=!0,setTimeout((function(){t._willUpdatePath=!1,t._updatePath(t.getSelection())}),0))},st.focus=function(){if(re){try{this._root.setActive()}catch(t){}this.fireEvent("focus")}else this._root.focus();return this},st.blur=function(){return this._root.blur(),re&&this.fireEvent("blur"),this};var ht="squire-selection-end";st._saveRangeToBookmark=function(e){var t,n=this.createElement("INPUT",{id:"squire-selection-start",type:"hidden"}),r=this.createElement("INPUT",{id:ht,type:"hidden"});Ee(e,n),e.collapse(!1),Ee(e,r),2&n.compareDocumentPosition(r)&&(n.id=ht,r.id="squire-selection-start",t=n,n=r,r=t),e.setStartAfter(n),e.setEndBefore(r)},st._getRangeAndRemoveBookmark=function(e){var t=this._root,n=t.querySelector("#squire-selection-start"),r=t.querySelector("#"+ht);if(n&&r){var i=n.parentNode,o=r.parentNode,a=de.call(i.childNodes,n),s=de.call(o.childNodes,r);i===o&&(s-=1),C(n),C(r),e||(e=this._doc.createRange()),e.setStart(i,a),e.setEnd(o,s),L(i,e),i!==o&&L(o,e),e.collapsed&&(i=e.startContainer,i.nodeType===q&&(o=i.childNodes[e.startOffset],o&&o.nodeType===q||(o=i.childNodes[e.startOffset-1]),o&&o.nodeType===q&&(e.setStart(o,0),e.collapse(!0))))}return e||null},st._keyUpDetectChange=function(e){var t=e.keyCode;e.ctrlKey||e.metaKey||e.altKey||!(t<16||t>20)||!(t<33||t>45)||this._docWasChanged()},st._docWasChanged=function(){if(ce&&(ye=new WeakMap),!this._ignoreAllChanges){if(le&&this._ignoreChange)return void(this._ignoreChange=!1);this._isInUndoState&&(this._isInUndoState=!1,this.fireEvent("undoStateChange",{canUndo:!0,canRedo:!1})),this.fireEvent("input")}},st._recordUndoState=function(e,t){if(!this._isInUndoState||t){var n,r=this._undoIndex,i=this._undoStack,o=this._config.undo,a=o.documentSizeThreshold,s=o.undoLimit;t||(r+=1),r<this._undoStackLength&&(i.length=this._undoStackLength=r),e&&this._saveRangeToBookmark(e),n=this._getHTML(),a>-1&&2*n.length>a&&s>-1&&r>s&&(i.splice(0,r-s),r=s,this._undoStackLength=s),i[r]=n,this._undoIndex=r,this._undoStackLength+=1,this._isInUndoState=!0}},st.saveUndoState=function(e){return e===n&&(e=this.getSelection()),this._recordUndoState(e,this._isInUndoState),this._getRangeAndRemoveBookmark(e),this},st.undo=function(){if(0!==this._undoIndex||!this._isInUndoState){this._recordUndoState(this.getSelection(),!1),this._undoIndex-=1,this._setHTML(this._undoStack[this._undoIndex]);var e=this._getRangeAndRemoveBookmark();e&&this.setSelection(e),this._isInUndoState=!0,this.fireEvent("undoStateChange",{canUndo:0!==this._undoIndex,canRedo:!0}),this.fireEvent("input")}return this},st.redo=function(){var e=this._undoIndex,t=this._undoStackLength;if(e+1<t&&this._isInUndoState){this._undoIndex+=1,this._setHTML(this._undoStack[this._undoIndex]);var n=this._getRangeAndRemoveBookmark();n&&this.setSelection(n),this.fireEvent("undoStateChange",{canUndo:!0,canRedo:e+2<t}),this.fireEvent("input")}return this},st.hasFormat=function(e,t,n){if(e=e.toUpperCase(),t||(t={}),!n&&!(n=this.getSelection()))return!1;!n.collapsed&&n.startContainer.nodeType===q&&n.startOffset===n.startContainer.length&&n.startContainer.nextSibling&&n.setStartBefore(n.startContainer.nextSibling),!n.collapsed&&n.endContainer.nodeType===q&&0===n.endOffset&&n.endContainer.previousSibling&&n.setEndAfter(n.endContainer.previousSibling);var i,o,a=this._root,s=n.commonAncestorContainer;if(m(s,a,e,t))return!0;if(s.nodeType===q)return!1;i=new r(s,4,(function(e){return Se(n,e,!0)}));for(var l=!1;o=i.nextNode();){if(!m(o,a,e,t))return!1;l=!0}return l},st.getFontInfo=function(e){var t,r,i,o={color:n,backgroundColor:n,family:n,size:n},a=0;if(!e&&!(e=this.getSelection()))return o;if(t=e.commonAncestorContainer,e.collapsed||t.nodeType===q)for(t.nodeType===q&&(t=t.parentNode);a<4&&t;)(r=t.style)&&(!o.color&&(i=r.color)&&(o.color=i,a+=1),!o.backgroundColor&&(i=r.backgroundColor)&&(o.backgroundColor=i,a+=1),!o.family&&(i=r.fontFamily)&&(o.family=i,a+=1),!o.size&&(i=r.fontSize)&&(o.size=i,a+=1)),t=t.parentNode;return o},st._addFormat=function(e,t,n){var i,o,a,l,c,u,d,h,f=this._root;if(n.collapsed){for(i=N(this.createElement(e,t),f),Ee(n,i),n.setStart(i.firstChild,i.firstChild.length),n.collapse(!0),h=i;s(h);)h=h.parentNode;dt(h,i)}else{if(o=new r(n.commonAncestorContainer,4|V,(function(e){return(e.nodeType===q||"BR"===e.nodeName||"IMG"===e.nodeName)&&Se(n,e,!0)})),a=n.startContainer,c=n.startOffset,l=n.endContainer,u=n.endOffset,o.currentNode=a,o.filter(a)||(a=o.nextNode(),c=0),!a)return n;do{d=o.currentNode,!m(d,f,e,t)&&(d===l&&d.length>u&&d.splitText(u),d===a&&c&&(d=d.splitText(c),l===a&&(l=d,u-=c),a=d,c=0),i=this.createElement(e,t),w(d,i),i.appendChild(d))}while(o.nextNode());l.nodeType!==q&&(d.nodeType===q?(l=d,u=d.length):(l=d.parentNode,u=1)),n=this.createRange(a,c,l,u)}return n},st._removeFormat=function(e,t,n,r){this._saveRangeToBookmark(n);var i,o=this._doc;n.collapsed&&(ae?(i=o.createTextNode(z),this._didAddZWS()):i=o.createTextNode(""),Ee(n,i));for(var a=n.commonAncestorContainer;s(a);)a=a.parentNode;var l=n.startContainer,c=n.startOffset,u=n.endContainer,d=n.endOffset,h=[],f=function(e,t){if(!Se(n,e,!1)){var r,i,o=e.nodeType===q;if(!Se(n,e,!0))return void("INPUT"===e.nodeName||o&&!e.data||h.push([t,e]));if(o)e===u&&d!==e.length&&h.push([t,e.splitText(d)]),e===l&&c&&(e.splitText(c),h.push([t,e]));else for(r=e.firstChild;r;r=i)i=r.nextSibling,f(r,t)}},p=Array.prototype.filter.call(a.getElementsByTagName(e),(function(r){return Se(n,r,!0)&&g(r,e,t)}));return r||p.forEach((function(e){f(e,e)})),h.forEach((function(e){var t=e[0].cloneNode(!1),n=e[1];w(n,t),t.appendChild(n)})),p.forEach((function(e){w(e,E(e))})),this._getRangeAndRemoveBookmark(n),i&&n.collapse(!1),L(a,n),n},st.changeFormat=function(e,t,n,r){return n||(n=this.getSelection())?(this.saveUndoState(n),t&&(n=this._removeFormat(t.tag.toUpperCase(),t.attributes||{},n,r)),e&&(n=this._addFormat(e.tag.toUpperCase(),e.attributes||{},n)),this.setSelection(n),this._updatePath(n,!0),le||this._docWasChanged(),this):this};var ft={DT:"DD",DD:"DT",LI:"LI",PRE:"PRE"},pt=function(e,t,n,r){var i=ft[t.nodeName],o=null,a=S(n,r,t.parentNode,e._root),s=e._config;return i||(i=s.blockTag,o=s.blockAttributes),g(a,i,o)||(t=T(a.ownerDocument,i,o),a.dir&&(t.dir=a.dir),w(a,t),t.appendChild(E(a)),a=t),a};st.forEachBlock=function(e,t,n){if(!n&&!(n=this.getSelection()))return this;t&&this.saveUndoState(n);var r=this._root,i=Me(n,r),o=Ae(n,r);if(i&&o)do{if(e(i)||i===o)break}while(i=h(i,r));return t&&(this.setSelection(n),this._updatePath(n,!0),le||this._docWasChanged()),this},st.modifyBlocks=function(e,t){if(!t&&!(t=this.getSelection()))return this;this._recordUndoState(t,this._isInUndoState);var n,r=this._root;return Re(t,r),Le(t,r,r,r),n=Te(t,r,r),Ee(t,e.call(this,n)),t.endOffset<t.endContainer.childNodes.length&&B(t.endContainer.childNodes[t.endOffset],r),B(t.startContainer.childNodes[t.startOffset],r),this._getRangeAndRemoveBookmark(t),this.setSelection(t),this._updatePath(t,!0),le||this._docWasChanged(),this};var gt=function(e){return this.createElement("BLOCKQUOTE",this._config.tagAttributes.blockquote,[e])},mt=function(e){var t=this._root,n=e.querySelectorAll("blockquote");return Array.prototype.filter.call(n,(function(e){return!m(e.parentNode,t,"BLOCKQUOTE")})).forEach((function(e){w(e,E(e))})),e},vt=function(){return this.createDefaultBlock([this.createElement("INPUT",{id:"squire-selection-start",type:"hidden"}),this.createElement("INPUT",{id:ht,type:"hidden"})])},bt=function(e,t,n){for(var r,i,o,a,s=u(t,e._root),l=e._config.tagAttributes,c=l[n.toLowerCase()],d=l.li;r=s.nextNode();)"LI"===r.parentNode.nodeName&&(r=r.parentNode,s.currentNode=r.lastChild),"LI"!==r.nodeName?(a=e.createElement("LI",d),r.dir&&(a.dir=r.dir),(o=r.previousSibling)&&o.nodeName===n?(o.appendChild(a),C(r)):w(r,e.createElement(n,c,[a])),a.appendChild(E(r)),s.currentNode=a):(r=r.parentNode,(i=r.nodeName)!==n&&/^[OU]L$/.test(i)&&w(r,e.createElement(n,c,[E(r)])))},_t=function(e){return bt(this,e,"UL"),e},yt=function(e){return bt(this,e,"OL"),e},Ct=function(e){var t,n,r,i,o,a=e.querySelectorAll("UL, OL"),s=e.querySelectorAll("LI"),c=this._root;for(t=0,n=a.length;t<n;t+=1)r=a[t],i=E(r),k(i,c),w(r,i);for(t=0,n=s.length;t<n;t+=1)o=s[t],l(o)?w(o,this.createDefaultBlock([E(o)])):(k(o,c),w(o,E(o)));return e},wt=function(e,t){for(var n=e.commonAncestorContainer,r=e.startContainer,i=e.endContainer;n&&n!==t&&!/^[OU]L$/.test(n.nodeName);)n=n.parentNode;if(!n||n===t)return null;for(r===n&&(r=r.childNodes[e.startOffset]),i===n&&(i=i.childNodes[e.endOffset]);r&&r.parentNode!==n;)r=r.parentNode;for(;i&&i.parentNode!==n;)i=i.parentNode;return[n,r,i]};st.increaseListLevel=function(e){if(!e&&!(e=this.getSelection()))return this.focus();var t=this._root,n=wt(e,t);if(!n)return this.focus();var r=n[0],i=n[1],o=n[2];if(!i||i===r.firstChild)return this.focus();this._recordUndoState(e,this._isInUndoState);var a,s,l=r.nodeName,c=i.previousSibling;c.nodeName!==l&&(a=this._config.tagAttributes[l.toLowerCase()],c=this.createElement(l,a),r.insertBefore(c,i));do{s=i===o?null:i.nextSibling,c.appendChild(i)}while(i=s);return s=c.nextSibling,s&&B(s,t),this._getRangeAndRemoveBookmark(e),this.setSelection(e),this._updatePath(e,!0),le||this._docWasChanged(),this.focus()},st.decreaseListLevel=function(e){if(!e&&!(e=this.getSelection()))return this.focus();var t=this._root,n=wt(e,t);if(!n)return this.focus();var r=n[0],i=n[1],o=n[2];i||(i=r.firstChild),o||(o=r.lastChild),this._recordUndoState(e,this._isInUndoState);var a,s=r.parentNode,l=o.nextSibling?S(r,o.nextSibling,s,t):r.nextSibling;if(s!==t&&"LI"===s.nodeName){for(s=s.parentNode;l;)a=l.nextSibling,o.appendChild(l),l=a;l=r.parentNode.nextSibling}var c=!/^[OU]L$/.test(s.nodeName);do{a=i===o?null:i.nextSibling,r.removeChild(i),c&&"LI"===i.nodeName&&(i=this.createDefaultBlock([E(i)])),s.insertBefore(i,l)}while(i=a);return r.firstChild||C(r),l&&B(l,t),this._getRangeAndRemoveBookmark(e),this.setSelection(e),this._updatePath(e,!0),le||this._docWasChanged(),this.focus()},st._ensureBottomLine=function(){var e=this._root,t=e.lastElementChild;t&&t.nodeName===this._config.blockTag&&l(t)||e.appendChild(this.createDefaultBlock())},st.setKeyHandler=function(e,t){return this._keyHandlers[e]=t,this},st._getHTML=function(){return this._root.innerHTML},st._setHTML=function(e){var t=this._root,n=t;n.innerHTML=e;do{N(n,t)}while(n=h(n,t));this._ignoreChange=!0},st.getHTML=function(e){var t,n,r,i,o,a,s=[];if(e&&(a=this.getSelection())&&this._saveRangeToBookmark(a),oe)for(t=this._root,n=t;n=h(n,t);)n.textContent||n.querySelector("BR")||(r=this.createElement("BR"),n.appendChild(r),s.push(r));if(i=this._getHTML().replace(/\u200B/g,""),oe)for(o=s.length;o--;)C(s[o]);return a&&this._getRangeAndRemoveBookmark(a),i},st.setHTML=function(e){var t,n,r,i=this._config,o=i.isSetHTMLSanitized?i.sanitizeToDOMFragment:null,a=this._root;"function"==typeof o?n=o(e,!1,this):(t=this.createElement("DIV"),t.innerHTML=e,n=this._doc.createDocumentFragment(),n.appendChild(E(t))),Ze(n,i),tt(n,a,!1),k(n,a);for(var s=n;s=h(s,a);)N(s,a);for(this._ignoreChange=!0;r=a.lastChild;)a.removeChild(r);a.appendChild(n),N(a,a),this._undoIndex=-1,this._undoStack.length=0,this._undoStackLength=0,this._isInUndoState=!1;var l=this._getRangeAndRemoveBookmark()||this.createRange(a.firstChild,0);return this.saveUndoState(l),this._lastSelection=l,I.call(this),this._updatePath(l,!0),this},st.insertElement=function(e,t){if(t||(t=this.getSelection()),t.collapse(!0),s(e))Ee(t,e),t.setStartAfter(e);else{for(var n,r,i=this._root,o=Me(t,i)||i;o!==i&&!o.nextSibling;)o=o.parentNode;o!==i&&(n=o.parentNode,r=S(n,o.nextSibling,i,i)),r?i.insertBefore(e,r):(i.appendChild(e),r=this.createDefaultBlock(),i.appendChild(r)),t.setStart(r,0),t.setEnd(r,0),xe(t)}return this.focus(),this.setSelection(t),this._updatePath(t),le||this._docWasChanged(),this},st.insertImage=function(e,t){var n=this.createElement("IMG",D({src:e},t,!0));return this.insertElement(n),n},st.linkRegExp=/\b((?:(?:ht|f)tps?:\/\/|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,}\/)(?:[^\s()<>]+|\([^\s()<>]+\))+(?:\((?:[^\s()<>]+|(?:\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))|([\w\-.%+]+@(?:[\w\-]+\.)+[A-Z]{2,}\b)(?:\?[^&?\s]+=[^&?\s]+(?:&[^&?\s]+=[^&?\s]+)*)?/i;var Et=function(e,t,n){var i,o,a,s,l,c,u,d=e.ownerDocument,h=new r(e,4,(function(e){return!m(e,t,"A")})),f=n.linkRegExp,p=n._config.tagAttributes.a;if(f)for(;i=h.nextNode();)for(o=i.data,a=i.parentNode;s=f.exec(o);)l=s.index,c=l+s[0].length,l&&(u=d.createTextNode(o.slice(0,l)),a.insertBefore(u,i)),u=n.createElement("A",D({href:s[1]?/^(?:ht|f)tps?:/i.test(s[1])?s[1]:"http://"+s[1]:"mailto:"+s[0]},p,!1)),u.textContent=o.slice(l,c),a.insertBefore(u,i),i.data=o=o.slice(c)};st.insertHTML=function(e,t){var n,r,i,o,a,s,l,c=this._config,u=c.isInsertedHTMLSanitized?c.sanitizeToDOMFragment:null,d=this.getSelection(),f=this._doc;"function"==typeof u?o=u(e,t,this):(t&&(n=e.indexOf("\x3c!--StartFragment--\x3e"),r=e.lastIndexOf("\x3c!--EndFragment--\x3e"),n>-1&&r>-1&&(e=e.slice(n+20,r))),/<\/td>((?!<\/tr>)[\s\S])*$/i.test(e)&&(e="<TR>"+e+"</TR>"),/<\/tr>((?!<\/table>)[\s\S])*$/i.test(e)&&(e="<TABLE>"+e+"</TABLE>"),i=this.createElement("DIV"),i.innerHTML=e,o=f.createDocumentFragment(),o.appendChild(E(i))),this.saveUndoState(d);try{for(a=this._root,s=o,l={fragment:o,preventDefault:function(){this.defaultPrevented=!0},defaultPrevented:!1},Et(o,o,this),Ze(o,c),tt(o,a,!1),Qe(o),o.normalize();s=h(s,o);)N(s,a);t&&this.fireEvent("willPaste",l),l.defaultPrevented||(ke(d,l.fragment,a),le||this._docWasChanged(),d.collapse(!1),this._ensureBottomLine()),this.setSelection(d),this._updatePath(d,!0),t&&this.focus()}catch(e){this.didError(e)}return this};var Tt=function(e){return e.split("&").join("&amp;").split("<").join("&lt;").split(">").join("&gt;").split('"').join("&quot;")};st.insertPlainText=function(e,t){var n=this.getSelection();if(n.collapsed&&m(n.startContainer,this._root,"PRE")){var r,i,o=n.startContainer,a=n.startOffset;return o&&o.nodeType===q||(r=this._doc.createTextNode(""),o.insertBefore(r,o.childNodes[a]),o=r,a=0),i={text:e,preventDefault:function(){this.defaultPrevented=!0},defaultPrevented:!1},t&&this.fireEvent("willPaste",i),i.defaultPrevented||(e=i.text,o.insertData(a,e),n.setStart(o,a+e.length),n.collapse(!0)),this.setSelection(n),this}var s,l,c,u,d=e.split("\n"),h=this._config,f=h.blockTag,p=h.blockAttributes,g="</"+f+">",v="<"+f;for(s in p)v+=" "+s+'="'+Tt(p[s])+'"';for(v+=">",l=0,c=d.length;l<c;l+=1)u=d[l],u=Tt(u).replace(/ (?= )/g,"&nbsp;"),d[l]=v+(u||"<BR>")+g;return this.insertHTML(d.join(""),t)};var Nt=function(e,t,n){return function(){return this[e](t,n),this.focus()}};st.addStyles=function(e){if(e){var t=this._doc.documentElement.firstChild,n=this.createElement("STYLE",{type:"text/css"});n.appendChild(this._doc.createTextNode(e)),t.appendChild(n)}return this},st.bold=Nt("changeFormat",{tag:"B"}),st.italic=Nt("changeFormat",{tag:"I"}),st.underline=Nt("changeFormat",{tag:"U"}),st.strikethrough=Nt("changeFormat",{tag:"S"}),st.subscript=Nt("changeFormat",{tag:"SUB"},{tag:"SUP"}),st.superscript=Nt("changeFormat",{tag:"SUP"},{tag:"SUB"}),st.removeBold=Nt("changeFormat",null,{tag:"B"}),st.removeItalic=Nt("changeFormat",null,{tag:"I"}),st.removeUnderline=Nt("changeFormat",null,{tag:"U"}),st.removeStrikethrough=Nt("changeFormat",null,{tag:"S"}),st.removeSubscript=Nt("changeFormat",null,{tag:"SUB"}),st.removeSuperscript=Nt("changeFormat",null,{tag:"SUP"}),st.makeLink=function(e,t){var n=this.getSelection();if(n.collapsed){var r=e.indexOf(":")+1;if(r)for(;"/"===e[r];)r+=1;Ee(n,this._doc.createTextNode(e.slice(r)))}return t=D(D({href:e},t,!0),this._config.tagAttributes.a,!1),this.changeFormat({tag:"A",attributes:t},{tag:"A"},n),this.focus()},st.removeLink=function(){return this.changeFormat(null,{tag:"A"},this.getSelection(),!0),this.focus()},st.setFontFace=function(e){var t=this._config.classNames.fontFamily;return this.changeFormat(e?{tag:"SPAN",attributes:{class:t,style:"font-family: "+e+", sans-serif;"}}:null,{tag:"SPAN",attributes:{class:t}}),this.focus()},st.setFontSize=function(e){var t=this._config.classNames.fontSize;return this.changeFormat(e?{tag:"SPAN",attributes:{class:t,style:"font-size: "+("number"==typeof e?e+"px":e)}}:null,{tag:"SPAN",attributes:{class:t}}),this.focus()},st.setTextColour=function(e){var t=this._config.classNames.colour;return this.changeFormat(e?{tag:"SPAN",attributes:{class:t,style:"color:"+e}}:null,{tag:"SPAN",attributes:{class:t}}),this.focus()},st.setHighlightColour=function(e){var t=this._config.classNames.highlight;return this.changeFormat(e?{tag:"SPAN",attributes:{class:t,style:"background-color:"+e}}:e,{tag:"SPAN",attributes:{class:t}}),this.focus()},st.setTextAlignment=function(e){return this.forEachBlock((function(t){var n=t.className.split(/\s+/).filter((function(e){return!!e&&!/^align/.test(e)})).join(" ");e?(t.className=n+" align-"+e,t.style.textAlign=e):(t.className=n,t.style.textAlign="")}),!0),this.focus()},st.setTextDirection=function(e){return this.forEachBlock((function(t){e?t.dir=e:t.removeAttribute("dir")}),!0),this.focus()};var kt=function(e){for(var t,n=this._root,i=this._doc,o=i.createDocumentFragment(),a=u(e,n);t=a.nextNode();){var s,l,c=t.querySelectorAll("BR"),d=[],h=c.length;for(s=0;s<h;s+=1)d[s]=et(c[s],!1);for(;h--;)l=c[h],d[h]?w(l,i.createTextNode("\n")):C(l);for(c=t.querySelectorAll("CODE"),h=c.length;h--;)C(c[h]);o.childNodes.length&&o.appendChild(i.createTextNode("\n")),o.appendChild(E(t))}for(a=new r(o,4);t=a.nextNode();)t.data=t.data.replace(/ /g," ");return o.normalize(),N(this.createElement("PRE",this._config.tagAttributes.pre,[o]),n)},St=function(e){for(var t,n,i,o,a,s,l=this._doc,c=this._root,u=e.querySelectorAll("PRE"),d=u.length;d--;){for(t=u[d],n=new r(t,4);i=n.nextNode();){for(o=i.data,o=o.replace(/ (?= )/g," "),a=l.createDocumentFragment();(s=o.indexOf("\n"))>-1;)a.appendChild(l.createTextNode(o.slice(0,s))),a.appendChild(l.createElement("BR")),o=o.slice(s+1);i.parentNode.insertBefore(a,i),i.data=o}k(t,c),w(t,E(t))}return e};st.code=function(){var e=this.getSelection();return e.collapsed||c(e.commonAncestorContainer)?this.modifyBlocks(kt,e):this.changeFormat({tag:"CODE",attributes:this._config.tagAttributes.code},null,e),this.focus()},st.removeCode=function(){var e=this.getSelection();return m(e.commonAncestorContainer,this._root,"PRE")?this.modifyBlocks(St,e):this.changeFormat(null,{tag:"CODE"},e),this.focus()},st.toggleCode=function(){return this.hasFormat("PRE")||this.hasFormat("CODE")?this.removeCode():this.code(),this},st.removeAllFormatting=function(e){if(!e&&!(e=this.getSelection())||e.collapsed)return this;for(var t=this._root,n=e.commonAncestorContainer;n&&!l(n);)n=n.parentNode;if(n||(Re(e,t),n=t),n.nodeType===q)return this;this.saveUndoState(e),Le(e,n,n,t);for(var r,i,o=n.ownerDocument,a=e.startContainer,s=e.startOffset,c=e.endContainer,u=e.endOffset,d=o.createDocumentFragment(),h=o.createDocumentFragment(),f=S(c,u,n,t),p=S(a,s,n,t);p!==f;)r=p.nextSibling,d.appendChild(p),p=r;return F(this,d,h),h.normalize(),p=h.firstChild,r=h.lastChild,i=n.childNodes,p?(n.insertBefore(h,f),s=de.call(i,p),u=de.call(i,r)+1):(s=de.call(i,f),u=s),e.setStart(n,s),e.setEnd(n,u),L(n,e),xe(e),this.setSelection(e),this._updatePath(e,!0),this.focus()},st.increaseQuoteLevel=Nt("modifyBlocks",gt),st.decreaseQuoteLevel=Nt("modifyBlocks",mt),st.makeUnorderedList=Nt("modifyBlocks",_t),st.makeOrderedList=Nt("modifyBlocks",yt),st.removeList=Nt("modifyBlocks",Ct),R.isInline=s,R.isBlock=l,R.isContainer=c,R.getBlockWalker=u,R.getPreviousBlock=d,R.getNextBlock=h,R.areAlike=p,R.hasTagAttributes=g,R.getNearest=m,R.isOrContains=b,R.detach=C,R.replaceWith=w,R.empty=E,R.getNodeBefore=Ce,R.getNodeAfter=we,R.insertNodeInRange=Ee,R.extractContentsOfRange=Te,R.deleteContentsOfRange=Ne,R.insertTreeFragmentIntoRange=ke,R.isNodeContainedInRange=Se,R.moveRangeBoundariesDownTree=xe,R.moveRangeBoundariesUpTree=Le,R.getStartBlockOfRange=Me,R.getEndBlockOfRange=Ae,R.contentWalker=Be,R.rangeDoesStartAtBlockBoundary=Oe,R.rangeDoesEndAtBlockBoundary=De,R.expandRangeToBlockBoundaries=Re,R.onPaste=ot,R.addLinks=Et,R.splitBlock=pt,R.startSelectionId="squire-selection-start",R.endSelectionId=ht,e.exports=R}(document)},function(e,t,n){"use strict";function r(e){return"boolean"===typeof e||e instanceof Boolean}e.exports=r},function(e,t,n){"use strict";(function(e){var n=function(){if("undefined"!==typeof Map)return Map;function e(e,t){var n=-1;return e.some((function(e,r){return e[0]===t&&(n=r,!0)})),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var i=r[n];e.call(t,i[1],i[0])}},t}()}(),r="undefined"!==typeof window&&"undefined"!==typeof document&&window.document===document,i=function(){return"undefined"!==typeof e&&e.Math===Math?e:"undefined"!==typeof self&&self.Math===Math?self:"undefined"!==typeof window&&window.Math===Math?window:Function("return this")()}(),o=function(){return"function"===typeof requestAnimationFrame?requestAnimationFrame.bind(i):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)}}(),a=2;function s(e,t){var n=!1,r=!1,i=0;function s(){n&&(n=!1,e()),r&&c()}function l(){o(s)}function c(){var e=Date.now();if(n){if(e-i<a)return;r=!0}else n=!0,r=!1,setTimeout(l,t);i=e}return c}var l=20,c=["top","right","bottom","left","width","height","size","weight"],u="undefined"!==typeof MutationObserver,d=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=s(this.refresh.bind(this),l)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var e=this.updateObservers_();e&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){r&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),u?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){r&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t,r=c.some((function(e){return!!~n.indexOf(e)}));r&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),h=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];Object.defineProperty(e,i,{value:t[i],enumerable:!1,writable:!1,configurable:!0})}return e},f=function(e){var t=e&&e.ownerDocument&&e.ownerDocument.defaultView;return t||i},p=T(0,0,0,0);function g(e){return parseFloat(e)||0}function m(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){var r=e["border-"+n+"-width"];return t+g(r)}),0)}function v(e){for(var t=["top","right","bottom","left"],n={},r=0,i=t;r<i.length;r++){var o=i[r],a=e["padding-"+o];n[o]=g(a)}return n}function b(e){var t=e.getBBox();return T(0,0,t.width,t.height)}function _(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return p;var r=f(e).getComputedStyle(e),i=v(r),o=i.left+i.right,a=i.top+i.bottom,s=g(r.width),l=g(r.height);if("border-box"===r.boxSizing&&(Math.round(s+o)!==t&&(s-=m(r,"left","right")+o),Math.round(l+a)!==n&&(l-=m(r,"top","bottom")+a)),!C(e)){var c=Math.round(s+o)-t,u=Math.round(l+a)-n;1!==Math.abs(c)&&(s-=c),1!==Math.abs(u)&&(l-=u)}return T(i.left,i.top,s,l)}var y=function(){return"undefined"!==typeof SVGGraphicsElement?function(e){return e instanceof f(e).SVGGraphicsElement}:function(e){return e instanceof f(e).SVGElement&&"function"===typeof e.getBBox}}();function C(e){return e===f(e).document.documentElement}function w(e){return r?y(e)?b(e):_(e):p}function E(e){var t=e.x,n=e.y,r=e.width,i=e.height,o="undefined"!==typeof DOMRectReadOnly?DOMRectReadOnly:Object,a=Object.create(o.prototype);return h(a,{x:t,y:n,width:r,height:i,top:n,right:t+r,bottom:i+n,left:t}),a}function T(e,t,n,r){return{x:e,y:t,width:n,height:r}}var N=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=T(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=w(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),k=function(){function e(e,t){var n=E(t);h(this,{target:e,contentRect:n})}return e}(),S=function(){function e(e,t,r){if(this.activeObservations_=[],this.observations_=new n,"function"!==typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=r}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(e instanceof f(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new N(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(e instanceof f(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new k(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),x="undefined"!==typeof WeakMap?new WeakMap:new n,L=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=d.getInstance(),r=new S(t,n,this);x.set(this,r)}return e}();["observe","unobserve","disconnect"].forEach((function(e){L.prototype[e]=function(){var t;return(t=x.get(this))[e].apply(t,arguments)}}));var M=function(){return"undefined"!==typeof i.ResizeObserver?i.ResizeObserver:L}();t["a"]=M}).call(this,n(59))},function(e,t,n){"use strict";var r=n(7),i=n(28),o=n(9),a=n(54),s=n(17),l=n(27),c=n(26),u=/\s+/g;function d(){this.events=null,this.contexts=null}d.mixin=function(e){r(e.prototype,d.prototype)},d.prototype._getHandlerItem=function(e,t){var n={handler:e};return t&&(n.context=t),n},d.prototype._safeEvent=function(e){var t,n=this.events;return n||(n=this.events={}),e&&(t=n[e],t||(t=[],n[e]=t),n=t),n},d.prototype._safeContext=function(){var e=this.contexts;return e||(e=this.contexts=[]),e},d.prototype._indexOfContext=function(e){var t=this._safeContext(),n=0;while(t[n]){if(e===t[n][0])return n;n+=1}return-1},d.prototype._memorizeContext=function(e){var t,n;i(e)&&(t=this._safeContext(),n=this._indexOfContext(e),n>-1?t[n][1]+=1:t.push([e,1]))},d.prototype._forgetContext=function(e){var t,n;i(e)&&(t=this._safeContext(),n=this._indexOfContext(e),n>-1&&(t[n][1]-=1,t[n][1]<=0&&t.splice(n,1)))},d.prototype._bindEvent=function(e,t,n){var r=this._safeEvent(e);this._memorizeContext(n),r.push(this._getHandlerItem(t,n))},d.prototype.on=function(e,t,n){var r=this;o(e)?(e=e.split(u),c(e,(function(e){r._bindEvent(e,t,n)}))):a(e)&&(n=t,c(e,(function(e,t){r.on(t,e,n)})))},d.prototype.once=function(e,t,n){var r=this;if(a(e))return n=t,void c(e,(function(e,t){r.once(t,e,n)}));function i(){t.apply(n,arguments),r.off(e,i,n)}this.on(e,i,n)},d.prototype._spliceMatches=function(e,t){var n,r=0;if(s(e))for(n=e.length;r<n;r+=1)!0===t(e[r])&&(e.splice(r,1),n-=1,r-=1)},d.prototype._matchHandler=function(e){var t=this;return function(n){var r=e===n.handler;return r&&t._forgetContext(n.context),r}},d.prototype._matchContext=function(e){var t=this;return function(n){var r=e===n.context;return r&&t._forgetContext(n.context),r}},d.prototype._matchHandlerAndContext=function(e,t){var n=this;return function(r){var i=e===r.handler,o=t===r.context,a=i&&o;return a&&n._forgetContext(r.context),a}},d.prototype._offByEventName=function(e,t){var n=this,r=l(t),i=n._matchHandler(t);e=e.split(u),c(e,(function(e){var t=n._safeEvent(e);r?n._spliceMatches(t,i):(c(t,(function(e){n._forgetContext(e.context)})),n.events[e]=[])}))},d.prototype._offByHandler=function(e){var t=this,n=this._matchHandler(e);c(this._safeEvent(),(function(e){t._spliceMatches(e,n)}))},d.prototype._offByObject=function(e,t){var n,r=this;this._indexOfContext(e)<0?c(e,(function(e,t){r.off(t,e)})):o(t)?(n=this._matchContext(e),r._spliceMatches(this._safeEvent(t),n)):l(t)?(n=this._matchHandlerAndContext(t,e),c(this._safeEvent(),(function(e){r._spliceMatches(e,n)}))):(n=this._matchContext(e),c(this._safeEvent(),(function(e){r._spliceMatches(e,n)})))},d.prototype.off=function(e,t){o(e)?this._offByEventName(e,t):arguments.length?l(e)?this._offByHandler(e):a(e)&&this._offByObject(e,t):(this.events={},this.contexts=[])},d.prototype.fire=function(e){this.invoke.apply(this,arguments)},d.prototype.invoke=function(e){var t,n,r,i;if(!this.hasListener(e))return!0;t=this._safeEvent(e),n=Array.prototype.slice.call(arguments,1),r=0;while(t[r]){if(i=t[r],!1===i.handler.apply(i.context,n))return!1;r+=1}return!0},d.prototype.hasListener=function(e){return this.getListenerLength(e)>0},d.prototype.getListenerLength=function(e){var t=this._safeEvent(e);return t.length},e.exports=d},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"===typeof window&&(n=window)}e.exports=n},,,,,,function(e,t,n){"use strict";n.r(t);var r=n(10),i=n.n(r),o=n(28),a=n.n(o),s=n(34),l=n.n(s),c=n(7),u=n.n(c),d=n(3),h=n.n(d),f=n(5),p=n.n(f),g=n(6),m=n.n(g),v=n(22),b=n(29);function _(e){var t=e.extendedAutolinks,n=e.customHTMLRenderer,r=e.referenceDefinition,i=e.customParser,o=new v["Parser"]({disallowedHtmlBlockTags:["br"],extendedAutolinks:t,referenceDefinition:r,disallowDeepHeading:!0,customParser:i}),a=Object(v["createRenderHTML"])({gfm:!0,convertors:Object(b["a"])(null,n)});return function(e){return a(o.parse(e))}}var y=n(12),C=n(56),w=n.n(C),E=n(15),T=n.n(E),N=/^(\s*)((\d+)([.)]\s(?:\[(?:x|\s)\]\s)?))(.*)/;function k(e,t,n,r){var i,o,a,s,l=n,c=r.getLine(e);do{var u=N.exec(c);if(i=u[1],o=u[4],a=u[5],s=i.length,s===t)r.replaceRange(""+i+l+o+a,{line:e,ch:0},{line:e,ch:c.length}),l+=1,e+=1;else{if(!(s>t))return e;e=k(e,s,1,r)}c=r.getLine(e)}while(N.test(c));return e}function S(e,t){var n=e,r=t.getLine(e);while(N.test(r))n-=1,r=t.getLine(n);return e===n?n=-1:n+=1,n}T.a.commands.indentLessOrderedList=function(e){return e.getOption("disableInput")?T.a.Pass:(e.execCommand("indentLess"),e.execCommand("fixOrderedListNumber"),null)},T.a.commands.fixOrderedListNumber=function(e){if(e.getOption("disableInput")||e.state.isCursorInCodeBlock)return T.a.Pass;for(var t=e.listSelections(),n=0;n<t.length;n+=1){var r=t[n].head,i=S(r.line,e);if(i>=0){var o=e.getLine(i),a=N.exec(o),s=a[1],l=a[3];k(i,s.length,parseInt(l,10),e)}}return null},T.a.overlayMode=function(e,t,n){return{startState:function(){return{base:T.a.startState(e),overlay:T.a.startState(t),basePos:0,baseCur:null,overlayPos:0,overlayCur:null,streamSeen:null}},copyState:function(n){return{base:T.a.copyState(e,n.base),overlay:T.a.copyState(t,n.overlay),basePos:n.basePos,baseCur:null,overlayPos:n.overlayPos,overlayCur:null}},token:function(r,i){return(r!=i.streamSeen||Math.min(i.basePos,i.overlayPos)<r.start)&&(i.streamSeen=r,i.basePos=i.overlayPos=r.start),r.start==i.basePos&&(i.baseCur=e.token(r,i.base),i.basePos=r.pos),r.start==i.overlayPos&&(r.pos=r.start,i.overlayCur=t.token(r,i.overlay),i.overlayPos=r.pos),r.pos=Math.min(i.basePos,i.overlayPos),null==i.overlayCur?i.baseCur:null!=i.baseCur&&i.overlay.combineTokens||n&&null==i.overlay.combineTokens?i.baseCur+" "+i.overlayCur:i.overlayCur},indent:e.indent&&function(t,n){return e.indent(t.base,n)},electricChars:e.electricChars,innerMode:function(t){return{state:t.base,mode:e}},blankLine:function(n){e.blankLine&&e.blankLine(n.base),t.blankLine&&t.blankLine(n.overlay)}}};var x=/^(\s*)(>[> ]*|[*+-] \[[x ]\]\s|[*+-]\s|(\d+)([.)]\s))(\s*)/,L=/^(\s*)(>[> ]*|[*+-] \[[x ]\]|[*+-]|(\d+)[.)])(\s*)$/,M=/[*+-]\s/;function A(e,t){var n=t.line,r=0,i=0,o=x.exec(e.getLine(n)),a=o[1];do{r+=1;var s=n+r,l=e.getLine(s),c=x.exec(l);if(c){var u=c[1],d=parseInt(o[3],10)+r-i,h=parseInt(c[3],10),f=h;if(a!==u||isNaN(h)){if(a.length>u.length)return;if(a.length<u.length&&1===r)return;i+=1}else d===h&&(f=h+1),d>h&&(f=d+1),e.replaceRange(l.replace(x,u+f+c[4]+c[5]),{line:s,ch:0},{line:s,ch:l.length})}}while(c)}function B(e){return O(e)&&e.anchor.ch===e.head.ch}function O(e){return e.anchor.line===e.head.line}function D(e,t,n,r){var i=e.getLine(n.line),o=e.getLine(n.line+r),a={anchor:t,head:n};e.replaceRange(o,{line:n.line,ch:0},{line:n.line,ch:i.length},"+input"),e.replaceRange(i,{line:n.line+r,ch:0},{line:n.line+r,ch:o.length},"+input"),B(a)?e.setCursor({line:n.line+r,ch:n.ch}):e.setSelection({line:t.line+r,ch:t.ch},{line:n.line+r,ch:n.ch})}function R(e,t,n,r){var i,o=e.getRange({line:t.line,ch:0},{line:n.line,ch:e.getLine(n.line).length}),a=r>0?n:t,s=e.getLine(a.line+r);i=r>0?t:n,e.replaceRange(s,{line:i.line,ch:0},{line:i.line,ch:e.getLine(i.line).length},"+input"),e.replaceRange(o,{line:t.line+r,ch:0},{line:n.line+r,ch:e.getLine(n.line+r).length},"+input"),e.setSelection({line:t.line+r,ch:t.ch},{line:n.line+r,ch:n.ch})}function I(e){e.state.placeholder&&(e.state.placeholder.parentNode.removeChild(e.state.placeholder),e.state.placeholder=null)}function P(e){I(e);var t=e.state.placeholder=document.createElement("pre");t.style.cssText="height: 0; overflow: visible",t.className="CodeMirror-placeholder";var n=e.getOption("placeholder");"string"==typeof n&&(n=document.createTextNode(n)),t.appendChild(n),e.display.lineSpace.insertBefore(t,e.display.lineSpace.firstChild)}function H(e){U(e)&&P(e)}function F(e){var t=e.getWrapperElement(),n=U(e);t.className=t.className.replace(" CodeMirror-empty","")+(n?" CodeMirror-empty":""),n?P(e):I(e)}function U(e){return 1===e.lineCount()&&""===e.getLine(0)}function q(){return q=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},q.apply(this,arguments)}T.a.commands.indentOrderedList=function(e){if(e.getOption("disableInput"))return T.a.Pass;for(var t=e.listSelections(),n=0;n<t.length;n++){var r=t[n].head,i=e.getLine(r.line),o=i.substr(0,r.ch);x.test(o)||e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertSoftTab")}e.execCommand("fixOrderedListNumber")},T.a.commands.newlineAndIndentContinueMarkdownList=function(e){if(e.getOption("disableInput")||e.state.isCursorInCodeBlock)return T.a.Pass;for(var t=e.listSelections(),n=[],r=0;r<t.length;r++){var i=t[r].head,o=e.getLine(i.line),a=x.exec(o),s=/^\s*$/.test(o.slice(0,i.ch));if(!t[r].empty()||!a||s)return void e.execCommand("newlineAndIndent");if(L.test(o))/>\s*$/.test(o)||e.replaceRange("",{line:i.line,ch:0},{line:i.line,ch:i.ch+1}),n[r]="\n";else{var l=a[1],c=a[5],u=!(M.test(a[2])||a[2].indexOf(">")>=0),d=u?parseInt(a[3],10)+1+a[4]:a[2].replace("x"," ");n[r]="\n"+l+d+c,u&&A(e,i)}}e.replaceSelections(n)},T.a.commands.replaceLineTextToUpper=function(e){if(e.getOption("disableInput"))return T.a.Pass;for(var t=e.listSelections(),n=-1,r=0;r<t.length;r++){var i=t[r],o=i.anchor,a=i.head;if(O(i)&&a.line>0)D(e,o,a,n);else if(!B(i)){var s=o.line<a.line?o.line:a.line;if(s>0){var l=o.line===s?o:a,c=o.line===s?a:o;R(e,l,c,n)}}}},T.a.commands.replaceLineTextToLower=function(e){if(e.getOption("disableInput"))return T.a.Pass;for(var t=e.listSelections(),n=1,r=0;r<t.length;r++){var i=t[r],o=i.anchor,a=i.head,s=a.line===e.lastLine();if(O(i)&&!s)D(e,o,a,n);else if(!B(i)){var l=o.line<a.line?o.line:a.line,c=o.line===l?o:a,u=o.line===l?a:o;u.line<e.lastLine()&&R(e,c,u,n)}}},T.a.defineOption("placeholder","",(function(e,t,n){var r=n&&n!=T.a.Init;if(t&&!r)e.on("blur",H),e.on("change",F),e.on("swapDoc",F),F(e);else if(!t&&r){e.off("blur",H),e.off("change",F),e.off("swapDoc",F),I(e);var i=e.getWrapperElement();i.className=i.className.replace(" CodeMirror-empty","")}t&&!e.hasFocus()&&H(e)}));var j,W=function(){function e(e,t){void 0===t&&(t={}),this.editorContainerEl=e,this.cm=null,this._init(t)}var t=e.prototype;return t._init=function(e){var t=document.createElement("textarea");this.editorContainerEl.appendChild(t),e=q({},e,{lineWrapping:!0,theme:"default",extraKeys:q({"Shift-Tab":"indentLess","Alt-Up":"replaceLineTextToUpper","Alt-Down":"replaceLineTextToLower"},e.extraKeys),indentUnit:4,cursorScrollMargin:12,specialCharPlaceholder:function(){return document.createElement("span")}}),this.cm=T.a.fromTextArea(t,e)},t.getCurrentRange=function(){var e=this.cm.getCursor("from"),t=this.cm.getCursor("to");return{from:e,to:t,collapsed:e.line===t.line&&e.ch===t.ch}},t.focus=function(){this.cm.focus()},t.blur=function(){this.cm.getInputField().blur()},t.remove=function(){this.cm.toTextArea()},t.setValue=function(e,t){void 0===t&&(t=!0),this.cm.setValue(e),t&&this.moveCursorToEnd(),this.cm.doc.clearHistory(),this.cm.refresh()},t.getValue=function(){return this.cm.getValue("\n")},t.getEditor=function(){return this.cm},t.reset=function(){this.setValue("")},t.getCaretPosition=function(){return this.cm.cursorCoords()},t.addWidget=function(e,t,n,r){r&&(e.ch+=r),this.cm.addWidget(e.end,t,!0,n)},t.replaceSelection=function(e,t){t&&this.cm.setSelection(t.from,t.to),this.cm.replaceSelection(e),this.focus()},t.replaceRelativeOffset=function(e,t,n){var r=this.cm.getCursor(),i={from:{line:r.line,ch:r.ch+t},to:{line:r.line,ch:r.ch+t+n}};this.replaceSelection(e,i)},t.setHeight=function(e){var t=this.getWrapperElement();h()(t,{height:e+"px"})},t.setMinHeight=function(e){var t=this.getWrapperElement();h()(t,{minHeight:e+"px"})},t.setPlaceholder=function(e){e&&this.cm.setOption("placeholder",e)},t.getWrapperElement=function(){return this.cm.getWrapperElement()},t.getCursor=function(e){return this.cm.getCursor(e)},t.moveCursorToEnd=function(){var e=this.getEditor().getDoc(),t=e.lastLine();e.setCursor(t,e.getLine(t).length)},t.moveCursorToStart=function(){var e=this.getEditor().getDoc(),t=e.firstLine();e.setCursor(t,0)},t.scrollTop=function(e){return e&&this.cm.scrollTo(0,e),this.cm.getScrollInfo().top},t.getRange=function(){var e=this.cm.getCursor("from"),t=this.cm.getCursor("to");return{start:{line:e.line,ch:e.ch},end:{line:t.line,ch:t.ch}}},t.on=function(e,t){this.cm.on(e,t)},t.off=function(e,t){this.cm.off(e,t)},e}(),V=W,z=["","","","CANCEL","","","HELP","","BACK_SPACE","TAB","","","CLEAR","ENTER","ENTER_SPECIAL","","","","","PAUSE","CAPS_LOCK","KANA","EISU","JUNJA","FINAL","HANJA","","ESCAPE","CONVERT","NONCONVERT","ACCEPT","MODECHANGE","SPACE","PAGE_UP","PAGE_DOWN","END","HOME","LEFT","UP","RIGHT","DOWN","SELECT","PRINT","EXECUTE","PRINTSCREEN","INSERT","DELETE","","0","1","2","3","4","5","6","7","8","9",":",";","<","=",">","?","AT","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","","","CONTEXT_MENU","","SLEEP","NUMPAD0","NUMPAD1","NUMPAD2","NUMPAD3","NUMPAD4","NUMPAD5","NUMPAD6","NUMPAD7","NUMPAD8","NUMPAD9","MULTIPLY","ADD","SEPARATOR","SUBTRACT","DECIMAL","DIVIDE","F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12","F13","F14","F15","F16","F17","F18","F19","F20","F21","F22","F23","F24","","","","","","","","","NUM_LOCK","SCROLL_LOCK","WIN_OEM_FJ_JISHO","WIN_OEM_FJ_MASSHOU","WIN_OEM_FJ_TOUROKU","WIN_OEM_FJ_LOYA","WIN_OEM_FJ_ROYA","","","","","","","","","","@","!",'"',"#","$","%","&","_","(",")","*","+","|","-","{","}","~","","","","","VOLUME_MUTE","VOLUME_DOWN","VOLUME_UP","","",";","=",",","-",".","/","`","","","","","","","","","","","","","","","","","","","","","","","","","","","[","\\","]","'","","META","ALTGR","","WIN_ICO_HELP","WIN_ICO_00","","WIN_ICO_CLEAR","","","WIN_OEM_RESET","WIN_OEM_JUMP","WIN_OEM_PA1","WIN_OEM_PA2","WIN_OEM_PA3","WIN_OEM_WSCTRL","WIN_OEM_CUSEL","WIN_OEM_ATTN","WIN_OEM_FINISH","WIN_OEM_COPY","WIN_OEM_AUTO","WIN_OEM_ENLW","WIN_OEM_BACKTAB","ATTN","CRSEL","EXSEL","EREOF","PLAY","ZOOM","","PA1","WIN_OEM_CLEAR",""],K=function(){function e(e){this._setSplitter(e)}var t=e.prototype;return t._setSplitter=function(e){var t=e?e.splitter:"+";this._splitter=t},t.convert=function(e){var t=[];e.shiftKey&&t.push("SHIFT"),e.ctrlKey&&t.push("CTRL"),e.metaKey&&t.push("META"),e.altKey&&t.push("ALT");var n=this._getKeyCodeChar(e.keyCode);return n&&t.push(n),t.join(this._splitter)},t._getKeyCodeChar=function(e){var t=z[e];return t},e.getSharedInstance=function(){return j||(j=new e),j},e.keyCode=function(e){return z.indexOf(e)},e}(),G=K,$=/^[ \t]*([-*]|[\d]+\.)( \[[ xX]])? /,Y=/^[ \t]*([*-] |[\d]+\. )(\[[ xX]] )/,X=/^[ \t]*[-*] .*/,Z=/^[ \t]*[\d]+\. \[[ xX]] .*/,Q=/([*-] |[\d]+\. )/,J=/([-*] |[\d]+\. )(\[[ xX]] )/,ee=/([-*]|[\d]+\.)( \[[ xX]])? /,te=/([-*])( \[[ xX]]) /,ne=/([\d])+\.( \[[ xX]])? /,re=/^\|([-\s\w\d\t<>?!@#$%^&*()_=+\\/'";: \r[\]]*\|+)+/i,ie=/^#+\s/,oe=/^ {0,3}(```|\||>)/,ae=function(){function e(e){this.cm=e.getEditor(),this.doc=this.cm.getDoc(),this.toastMark=e.getToastMark(),this.name="list"}var t=e.prototype;return t._createSortedLineRange=function(e){var t=e.from.line>e.to.line,n={line:t?e.to.line:e.from.line,ch:0},r={line:t?e.from.line:e.to.line,ch:0};return{start:n.line,end:r.line}},t._calculateOrdinalNumber=function(e){for(var t=1,n=e-1;n>=0;n-=1){var r=this._getListDepth(n);if(1===r&&ne.exec(this.doc.getLine(n))){t=parseInt(RegExp.$1,10)+1;break}if(0===r)break}return t},t._isListLine=function(e){return!!$.exec(this.doc.getLine(e))},t._isCanBeList=function(e){var t=this.doc.getLine(e);return!oe.test(t)&&!re.test(t)&&!ie.test(t)},t._getChangeFn=function(e){var t,n=this;switch(e){case"ol":case"ul":t=function(t){return n._changeToList(t,e)};break;case"task":t=function(e){return n._changeToTask(e)};break;default:break}return t},t.changeSyntax=function(e,t){for(var n=[],r=this._createSortedLineRange(e),i=r.start,o=r.end,a=this._getChangeFn(t),s=i;s<=o;s+=1){if(!this._isCanBeList(s))break;this._isListLine(s)||n.push(s),a(s)}this._insertBlankLineForNewList(n),this.cm.focus()},t._replaceLineText=function(e,t){this.doc.replaceRange(e,{line:t,ch:0})},t._changeToList=function(e,t){var n=this;this._isListLine(e)?this._changeSameDepthList(e,"ol"===t?function(e,t){n._replaceListTypeToOL(e,t)}:function(e){n._replaceListTypeToUL(e)}):this._replaceLineText("ol"===t?this._calculateOrdinalNumber(e)+". ":"* ",e)},t._changeToTask=function(e){Y.exec(this.doc.getLine(e))?this._replaceLineTextByRegexp(e,J,"$1"):this._isListLine(e)?this._replaceLineTextByRegexp(e,Q,"$1[ ] "):this._replaceLineText("* [ ] ",e)},t._getListDepth=function(e){var t=0,n=this.doc.getLine(e);if(n){var r=this.toastMark.findFirstNodeAtLine(e+1);while(r&&"document"!==r.type)"list"===r.type&&(t+=1),r=r.parent}return t},t._findSameDepthList=function(e,t,n){var r,i=this.doc.lineCount(),o=[],a=e;while(n?a<i-1:a>0)if(a=n?a+1:a-1,r=this._getListDepth(a),r===t)o.push(a);else if(r<t)break;return o},t._changeSameDepthList=function(e,t){var n=this._getListDepth(e),r=this._findSameDepthList(e,n,!1).reverse(),i=this._findSameDepthList(e,n,!0),o=r.concat([e]).concat(i);o.forEach((function(e,n){t(e,n+1)}))},t._replaceLineTextByRegexp=function(e,t,n){var r=this.doc.getLine(e),i={line:e,ch:0},o={line:e,ch:r.length};r=r.replace(t,n),this.doc.replaceRange(r,i,o)},t._replaceListTypeToUL=function(e){var t=this.doc.getLine(e);te.exec(t)?this._replaceLineTextByRegexp(e,te,"$1 "):ne.exec(t)&&this._replaceLineTextByRegexp(e,ne,"* ")},t._replaceListTypeToOL=function(e,t){var n=this.doc.getLine(e);X.exec(n)||Z.exec(n)?this._replaceLineTextByRegexp(e,ee,t+". "):ne.exec(n)&&parseInt(RegExp.$1,10)!==t&&this._replaceLineTextByRegexp(e,ne,t+". ")},t._insertBlankLineForNewList=function(e){var t=e.length;if(t){var n=e[0],r=e[t-1];this._isNotBlankNotListLine(r+1)&&this.doc.replaceRange("\n",{line:r,ch:this.doc.getLine(r).length}),n>0&&this._isNotBlankNotListLine(n-1)&&this.doc.replaceRange("\n",{line:n,ch:0})}},t._isNotBlankNotListLine=function(e){return!!this.doc.getLine(e)&&!this._isListLine(e)},e}(),se=ae,le=function(){function e(e){this._managers={},this._editor=e}var t=e.prototype;return t.addManager=function(e,t){t||(t=e,e=null);var n=new t(this._editor);this._managers[e||n.name]=n},t.getManager=function(e){return this._managers[e]},t.removeManager=function(e){var t=this.getManager(e);t&&(t.destroy&&t.destroy(),delete this._managers[e])},e}(),ce=le,ue=function(){function e(e,t){this._mde=e,this.setRange(t||e.getRange())}var t=e.prototype;return t._setStart=function(e){this._start=e},t._setEnd=function(e){this._end=e},t.setRange=function(e){this._setStart(e.start),this._setEnd(e.end)},t.setEndBeforeRange=function(e){this._setEnd(e.start)},t.expandStartOffset=function(){var e=this._start;0!==e.ch&&(e.ch-=1)},t.expandEndOffset=function(){var e=this._end;e.ch<this._mde.getEditor().getDoc().getLine(e.line).length&&(e.ch+=1)},t.getTextContent=function(){return this._mde.getEditor().getRange(this._start,this._end)},t.replaceContent=function(e){this._mde.getEditor().replaceRange(e,this._start,this._end,"+input")},t.deleteContent=function(){this._mde.getEditor().replaceRange("",this._start,this._end,"+delete")},t.peekStartBeforeOffset=function(e){var t={line:this._start.line,ch:Math.max(this._start.ch-e,0)};return this._mde.getEditor().getRange(t,this._start)},e}(),de=ue,he=n(4),fe=n(27),pe=n.n(fe);function ge(){return ge=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ge.apply(this,arguments)}var me="tui-md-",ve=ye({DELIM:"delimiter",META:"meta",TEXT:"marked-text",THEMATIC_BREAK:"thematic-break",CODE_BLOCK:"code-block",TABLE:"table",HTML:"html"}),be={strong:2,emph:1,strike:2};function _e(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.map((function(e){return""+me+e})).join(" ")}function ye(e){return i()(e,(function(t,n){e[n]=_e(t)})),e}function Ce(e,t,n){return{start:e,end:t,className:n}}function we(e,t,n){var r=e.level,i=e.headingType,o=[Ce(t,n,_e("heading","heading"+r))];return"atx"===i?o.push(Ce(t,Object(he["a"])(t,r),ve.DELIM)):o.push(Ce(Object(he["l"])(n,0),n,ve.DELIM+" setext")),{marks:o}}function Ee(e,t,n){var r=e.type;return{marks:[Ce(t,n,_e(""+r)),Ce(t,Object(he["a"])(t,be[r]),ve.DELIM),Ce(Object(he["a"])(n,-be[r]),n,ve.DELIM)]}}function Te(e,t,n,r){return[Ce(e,t,_e("link")),Ce(n,Object(he["l"])(t,r),_e("link-desc")),Ce(Object(he["l"])(e,n.ch+1),Object(he["l"])(t,r-1),ve.TEXT),Ce(Object(he["l"])(t,r),t,_e("link-url")),Ce(Object(he["l"])(t,r+1),Object(he["a"])(t,-1),ve.TEXT)]}function Ne(e,t,n){var r=e.lastChild,i=r?Object(he["c"])(r)+1:3,o=Object(he["a"])(t,1);return{marks:[Ce(t,o,ve.META)].concat(Te(t,n,o,i))}}function ke(e,t,n){var r=e.lastChild,i=e.extendedAutolink,o=r?Object(he["c"])(r)+1:2,a=i?[Ce(t,n,_e("link","link-desc")+" "+ve.TEXT)]:Te(t,n,t,o);return{marks:a}}function Se(e,t,n){var r=e.tickCount,i=Object(he["a"])(t,r),o=Object(he["a"])(n,-r);return{marks:[Ce(t,n,_e("code")),Ce(t,i,ve.DELIM+" start"),Ce(i,o,ve.TEXT),Ce(o,n,ve.DELIM+" end")]}}function xe(e,t,n,r){var i=e.fenceOffset,o=e.fenceLength,a=e.fenceChar,s=e.info,l=e.infoPadding,c=e.parent,u=i+o,d=[Ce(Object(he["l"])(t,0),n,ve.CODE_BLOCK)];a&&d.push(Ce(t,Object(he["a"])(t,u),ve.DELIM)),s&&d.push(Ce(Object(he["l"])(t,u),Object(he["l"])(t,u+l+s.length),ve.META));var h="^(\\s{0,3})("+a+"{"+o+",})",f=new RegExp(h);f.test(r)&&d.push(Ce(Object(he["l"])(n,0),n,ve.DELIM));var p="item"!==c.type&&"blockQuote"!==c.type?{start:t.line,end:n.line,className:ve.CODE_BLOCK}:null;return{marks:d,lineBackground:ge({},p)}}function Le(e,t){var n=[];while(e){var r=e,i=r.type;"paragraph"!==i&&"codeBlock"!==i||n.push(Ce({line:Object(he["f"])(e)-1,ch:Object(he["e"])(e)-1},{line:Object(he["d"])(e)-1,ch:Object(he["c"])(e)},t)),e=e.next}return n}function Me(e){var t=[];while(e)t.push(Ce({line:Object(he["f"])(e)-1,ch:Object(he["e"])(e)-1},{line:Object(he["d"])(e)-1,ch:Object(he["c"])(e)},ve.TEXT)),e=e.next;return t}function Ae(e,t,n){var r=e.parent&&"blockQuote"!==e.parent.type?[Ce(t,n,_e("block-quote"))]:[];if(e.firstChild){var i=[];"paragraph"===e.firstChild.type?i=Me(e.firstChild.firstChild,ve.TEXT):"list"===e.firstChild.type&&(i=Le(e.firstChild,ve.TEXT)),r=[].concat(r,i)}return{marks:r}}function Be(e){var t=0;while(e.parent.parent&&"item"===e.parent.parent.type)e=e.parent.parent,t+=1;var n=["list-item-odd","list-item-even"][t%2],r=["fisrt","second","third"][t%3];return _e("list-item",""+n)+" "+r}function Oe(e,t){var n=Be(e),r=e.listData,i=r.padding,o=r.task;return{marks:[Ce(t,Object(he["a"])(t,i),n+" "+_e("list-item-bullet"))].concat(o?[Ce(Object(he["a"])(t,i),Object(he["a"])(t,i+3),n+" "+ve.DELIM),Ce(Object(he["a"])(t,i+1),Object(he["a"])(t,i+2),ve.META)]:[],Le(e.firstChild,n+" "+ve.TEXT))}}var De={heading:we,strong:Ee,emph:Ee,strike:Ee,link:ke,image:Ne,code:Se,codeBlock:xe,blockQuote:Ae,item:Oe},Re={thematicBreak:ve.THEMATIC_BREAK,table:ve.TABLE,tableCell:ve.TEXT,htmlInline:ve.HTML};function Ie(e,t,n,r){var i=e.type;return pe()(De[i])?De[i](e,t,n,r):Re[i]?{marks:[Ce(t,n,Re[i])]}:null}function Pe(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function He(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function Fe(){return Fe=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Fe.apply(this,arguments)}var Ue=G.getSharedInstance(),qe={strong:!1,emph:!1,strike:!1,thematicBreak:!1,blockQuote:!1,code:!1,codeBlock:!1,list:!1,taskList:!1,orderedList:!1,heading:!1,table:!1};function je(e){var t=e.type,n=e.listData;return"list"===t||"item"===t?n.task?"taskList":"ordered"===n.type?"orderedList":"list":-1!==t.indexOf("table")?"table":t}function We(e,t,n,r){var i=Fe({},qe),o=!1;return Object(he["m"])(e,(function(e){var t=je(e);w()(i[t])&&("list"===t||"orderedList"===t?o||(i[t]=!0,o=!0):i[t]=!0)})),Object(he["k"])(e)&&(r===t&&Object(he["d"])(e)===n||r===Object(he["c"])(e)+1&&n===Object(he["d"])(e)||r===Object(he["e"])(e)&&n===Object(he["f"])(e))&&(i[e.type]=!1),i}function Ve(e,t){return!(!e&&!t)&&(!!(!e&&t||e&&!t)||Object.keys(t).some((function(n){return e[n]!==t[n]})))}var ze="data-tui-mark",Ke=function(e){function t(t,n,r,i){var o;return o=e.call(this,t,{dragDrop:!0,allowDropFileTypes:["image"],extraKeys:{Enter:"newlineAndIndentContinueMarkdownList",Tab:"indentOrderedList","Shift-Tab":"indentLessOrderedList"},viewportMargin:i&&"auto"===i.height?1/0:10})||this,o.eventManager=n,o.componentManager=new ce(Pe(o)),o.toastMark=r,o.componentManager.addManager(se),o._latestState=null,o._markedLines={},o._initEvent(),o}He(t,e);var n=t.prototype;return n._initEvent=function(){var e=this;this.cm.getWrapperElement().addEventListener("click",(function(){e.eventManager.emit("click",{source:"markdown"})})),this.cm.on("beforeChange",(function(t,n){"paste"===n.origin&&e.eventManager.emit("pasteBefore",{source:"markdown",data:n})})),this.cm.on("change",(function(t,n){e._refreshCodeMirrorMarks(n),e._emitMarkdownEditorChangeEvent(n)})),this.cm.on("focus",(function(){e.eventManager.emit("focus",{source:"markdown"})})),this.cm.on("blur",(function(){e.eventManager.emit("blur",{source:"markdown"})})),this.cm.on("scroll",(function(t,n){e.eventManager.emit("scroll",{source:"markdown",data:n})})),this.cm.on("keydown",(function(t,n){e.eventManager.emit("keydown",{source:"markdown",data:n}),e.eventManager.emit("keyMap",{source:"markdown",keyMap:Ue.convert(n),data:n})})),this.cm.on("keyup",(function(t,n){e.eventManager.emit("keyup",{source:"markdown",data:n})})),this.cm.on("copy",(function(t,n){e.eventManager.emit("copy",{source:"markdown",data:n})})),this.cm.on("cut",(function(t,n){e.eventManager.emit("cut",{source:"markdown",data:n})})),this.cm.on("paste",(function(t,n){e.eventManager.emit("paste",{source:"markdown",data:n})})),this.cm.on("drop",(function(t,n){n.preventDefault(),e.eventManager.emit("drop",{source:"markdown",data:n})})),this.cm.on("cursorActivity",(function(){return e._onChangeCursorActivity()}))},n.setValue=function(t,n){e.prototype.setValue.call(this,t,n)},n.getTextObject=function(e){return new de(this,e)},n._emitMarkdownEditorContentChangedEvent=function(e){this.eventManager.emit("contentChangedFromMarkdown",e)},n._emitMarkdownEditorChangeEvent=function(e){if("setValue"!==e.origin){var t={source:"markdown"};this.eventManager.emit("changeFromMarkdown",t),this.eventManager.emit("change",t)}},n._refreshCodeMirrorMarks=function(e){var t=this,n=e.from,r=e.to,i=e.text,o=this.toastMark.editMarkdown([n.line+1,n.ch+1],[r.line+1,r.ch+1],i.join("\n"));this._emitMarkdownEditorContentChangedEvent(o),o.length&&o.forEach((function(e){return t._markNodes(e)}))},n._markNodes=function(e){var t=e.nodes,n=e.removedNodeRange;if(n&&this._removeBackgroundOfLines(n),t.length){var r=t[0].sourcepos,i=r[0],o=t[t.length-1].sourcepos,a=o[1],s={line:i[0]-1,ch:i[1]-1},l={line:a[0]-1,ch:a[1]},c=this.cm.findMarks(s,l),u=c,d=Array.isArray(u),h=0;for(u=d?u:u[Symbol.iterator]();;){var f;if(d){if(h>=u.length)break;f=u[h++]}else{if(h=u.next(),h.done)break;f=h.value}var p=f;p.attributes&&ze in p.attributes&&p.clear()}var g=t,m=Array.isArray(g),v=0;for(g=m?g:g[Symbol.iterator]();;){var b;if(m){if(v>=g.length)break;b=g[v++]}else{if(v=g.next(),v.done)break;b=v.value}var _=b,y=_.walker(),C=y.next();while(C){var w=C,E=w.node,T=w.entering;T&&this._markNode(E),C=y.next()}}}},n._removeBackgroundOfLines=function(e){for(var t=e.line,n=t[0],r=t[1],i=n;i<=r;i+=1)this._markedLines[i]&&(this.cm.removeLineClass(i,"background"),this._markedLines[i]=!1)},n._markCodeBlockBackground=function(e){for(var t=e.start,n=e.end,r=e.className,i=t;i<=n;i+=1){var o=r;i===t?o+=" start":i===n&&(o+=" end"),this.cm.addLineClass(i,"background",o),this._markedLines[i]=!0}},n._markNode=function(e){var t=this,n={line:Object(he["f"])(e)-1,ch:Object(he["e"])(e)-1},r={line:Object(he["d"])(e)-1,ch:Object(he["c"])(e)},i=Ie(e,n,r,this.cm.getLine(r.line));if(i){var o=i.marks,a=void 0===o?[]:o,s=i.lineBackground,l=void 0===s?{}:s;a.forEach((function(e){var n,r=e.start,i=e.end,o=e.className,a=(n={},n[ze]="",n);t.cm.markText(r,i,{className:o,attributes:a})})),this._markCodeBlockBackground(l)}},n._setToolbarState=function(e){if(Ve(this._latestState,e)){var t=Fe({source:"markdown"},e||qe);this.eventManager.emit("stateChange",t)}this._latestState=e},n._onChangeCursorActivity=function(){var e=this.cm.getCursor(),t=e.line,n=e.ch,r=t+1,i=this.cm.getLine(t).length===n?n:n+1,o=this.toastMark.findNodeAtPosition([r,i]),a=null;this.cm.state.isCursorInCodeBlock=o&&"codeBlock"===o.type,this.eventManager.emit("cursorActivity",{source:"markdown",cursor:{line:t,ch:n},markdownNode:o}),o&&(o="text"===o.type?o.parent:o,a=We(o,n,r,i)),this._setToolbarState(a)},n.resetState=function(){this._latestState=null},n.getToastMark=function(){return this.toastMark},t.factory=function(e,n,r,i){return new t(e,n,r,i)},t}(V),Ge=Ke,$e=n(35),Ye=n(2),Xe=n.n(Ye),Ze=n(21),Qe=n.n(Ze),Je=n(8),et=n.n(Je),tt=n(17),nt=n.n(tt),rt=n(11),it=n.n(rt),ot=n(47),at=n.n(ot),st=n(19),lt=n.n(st),ct=n(20),ut=n.n(ct),dt=n(0),ht=n(13),ft=n.n(ht),pt=n(25),gt="rgb(34, 34, 34)",mt=function(){function e(e){this.wwe=e}var t=e.prototype;return t.preparePaste=function(e){var t,n,r,i=this.wwe.getEditor().getSelection().cloneRange(),o=this.wwe.componentManager.getManager("codeblock"),a=!1,s=document.createElement("div");this._pasteFirstAid(e);var l=Xe()(e.childNodes);while(l.length)n=l[0],t=dt["a"].getNodeName(n),r="LI"===t||"UL"===t||"OL"===t,o.isInCodeBlock(i)?dt["a"].append(s,o.prepareToPasteOnCodeblock(l)):r?(dt["a"].append(s,this._prepareToPasteList(l,i,a)),a=!0):dt["a"].append(s,l.shift());e.innerHTML=s.innerHTML},t._wrapOrphanNodeWithDiv=function(e){var t,n=document.createElement("div");return Xe()(e.childNodes).forEach((function(e){var r=3===e.nodeType,i=/^(SPAN|A|CODE|EM|I|STRONG|B|S|U|ABBR|ACRONYM|CITE|DFN|KBD|SAMP|VAR|BDO|Q|SUB|SUP)$/gi.test(e.tagName),o="BR"===e.nodeName;r||i||o?(t||(t=document.createElement("div"),n.appendChild(t)),t.appendChild(e),o&&(t=null)):(t&&"BR"!==t.lastChild.tagName&&t.appendChild(document.createElement("br")),t=null,n.appendChild(e))})),n.innerHTML},t._sanitizeHtml=function(e){var t=this.wwe.getSanitizer(),n=Object(pt["a"])(e.innerHTML,!0);t&&t!==pt["a"]&&(n=t(n)),e.innerHTML=n},t._pasteFirstAid=function(e){var t=this;this._sanitizeHtml(e),dt["a"].findAll(e,"*").forEach((function(e){t._removeStyles(e)}));var n="div, section, article, aside, nav, menus, p";this._unwrapIfNonBlockElementHasBr(e),this._unwrapNestedBlocks(e,n),this._removeUnnecessaryBlocks(e,n),e.innerHTML=this._wrapOrphanNodeWithDiv(e),this._preElementAid(e),this._tableElementAid(e),Xe()(e.children).forEach((function(e){"BR"===dt["a"].getNodeName(e)&&dt["a"].remove(e)}))},t._preElementAid=function(e){var t=this.wwe.componentManager.getManager("codeblock");t.modifyCodeBlockForWysiwyg(e)},t._unwrapIfNonBlockElementHasBr=function(e){var t=dt["a"].findAll(e,"span, a, b, em, i, s");t.forEach((function(e){var t=dt["a"].children(e,"br");t.length&&"LI"!==e.nodeName&&"UL"!==e.nodeName&&dt["a"].unwrap(e)}))},t._unwrapNestedBlocks=function(e,t){var n=dt["a"].findAll(e,"*").filter((function(e){return!ft()(e,"b,s,i,em,code,span,hr")&&!e.firstChild}));n.forEach((function(n){var r="BR"===n.nodeName?n.parentNode:n;while(dt["a"].parents(r,t).length){var i=dt["a"].parent(r,t);i&&i!==e?dt["a"].unwrap(i):r=r.parentElement}}))},t._removeUnnecessaryBlocks=function(e,t){dt["a"].findAll(e,t).forEach((function(e){var n=e.tagName,r="DIV"===n,i=!!dt["a"].parent(e,"li"),o=!!dt["a"].parent(e,"blockquote"),a=!!dt["a"].children(e,t).length;r&&(i||o||!a)||(e.lastChild&&"BR"!==e.lastChild.nodeName&&e.appendChild(document.createElement("br")),dt["a"].replaceWith(e,e.innerHTML))}))},t._removeStyles=function(e){var t;"SPAN"!==dt["a"].getNodeName(e)?e.removeAttribute("style"):(e.getAttribute("style")&&(t=e.style.color),e.removeAttribute("style"),t&&t!==gt?h()(e,{color:t}):dt["a"].unwrap(e))},t._prepareToPasteList=function(e,t,n){var r=dt["a"].getNodeName(e[0]),i=e.shift(),o=this.wwe.getEditor().getDocument().createDocumentFragment();if("LI"!==r&&e.length&&"LI"===e[0].tagName&&(r="LI",i=this._makeNodeAndAppend({tagName:r},i)),"OL"===r||"UL"===r)!n&&this.wwe.getEditor().hasFormat("LI")?dt["a"].append(o,this._wrapCurrentFormat(i)):o.appendChild(i);else if("LI"===r){var a=this.wwe.getEditor().getDocument().createDocumentFragment();a.appendChild(i);while(e.length&&"LI"===e[0].tagName)a.appendChild(e.shift());!n&&this.wwe.getEditor().hasFormat("LI")?dt["a"].append(o,this._wrapCurrentFormat(a)):!t||"UL"!==t.commonAncestorName&&"OL"!==t.commonAncestorName?dt["a"].append(o,this._makeNodeAndAppend({tagName:"UL"},a)):dt["a"].append(o,this._makeNodeAndAppend({tagName:t.commonAncestorName},a))}return o},t._unwrapFragmentFirstChildForPasteAsInline=function(e){return dt["a"].findAll(e,"br").forEach((function(e){return dt["a"].remove(e)})),e.childNodes},t._wrapCurrentFormat=function(e){var t,n=this;return this._eachCurrentPath((function(r){"DIV"!==r.tagName&&(t=dt["a"].isElemNode(e)?e.tagName:e.firstChild.tagName,r.tagName!==t&&(e=n._makeNodeAndAppend(r,e)))})),e},t._eachCurrentPath=function(e){for(var t=dt["a"].getPath(this.wwe.getEditor().getSelection().startContainer,this.wwe.getBody()),n=t.length-1;n>-1;n-=1)e(t[n])},t._makeNodeAndAppend=function(e,t){var n=document.createElement(""+e.tagName);return n.appendChild(t),e.id&&n.setAttribute("id",e.id),e.className&&p()(n,e.className),n},t._tableElementAid=function(e){this._removeColgroup(e),this._completeTableIfNeed(e),this._updateTableIDClassName(e)},t._removeColgroup=function(e){var t=e.querySelector("colgroup");t&&dt["a"].remove(t)},t._completeTableIfNeed=function(e){var t=this.wwe.componentManager.getManager("table"),n=t.wrapDanglingTableCellsIntoTrIfNeed(e);n&&dt["a"].append(e,n);var r=t.wrapTrsIntoTbodyIfNeed(e);r&&dt["a"].append(e,r);var i=t.wrapTheadAndTbodyIntoTableIfNeed(e);i&&dt["a"].append(e,i)},t._updateTableIDClassName=function(e){var t=this.wwe.componentManager.getManager("table"),n=dt["a"].findAll(e,"table");n.forEach((function(e){var t=e.className.match(/.*\s*(te-content-table-\d+)\s*.*/);t&&m()(e,t[0])})),n.forEach((function(e){p()(e,t.getTableIDClassName())}))},e}(),vt=mt,bt=function(){function e(e){this.wwe=e}var t=e.prototype;return t.pasteClipboard=function(e){var t=e.clipboardData||window.clipboardData,n=t&&t.items;n?(this._pasteClipboardItem(n),e.preventDefault()):(this._pasteClipboardUsingPasteArea(),e.squirePrevented=!0)},t._pasteClipboardUsingPasteArea=function(){var e=this,t=this.wwe.getEditor(),n=t.getSelection(),r=n.startContainer,i=n.startOffset,o=n.endContainer,a=n.endOffset,s=document.createElement("div"),l=document,c=l.body;s.setAttribute("contenteditable",!0),s.setAttribute("style","position:fixed; overflow:hidden; top:0; right:100%; width:1px; height:1px;"),c.appendChild(s),n.selectNodeContents(s),t.setSelection(n),setTimeout((function(){var l=c.removeChild(s);n.setStart(r,i),n.setEnd(o,a),t.focus(),t.setSelection(n),e._pasteClipboardHtml(l.innerHTML)}))},t._pasteClipboardItem=function(e){var t=this,n=null,r=null;Xe()(e).forEach((function(e){"text/html"===e.type?r=e:"text/plain"===e.type&&(n=e)})),r?r.getAsString((function(e){t._pasteClipboardHtml(e)})):n&&n.getAsString((function(e){t._pasteClipboardContainer(document.createTextNode(e))}))},t._getSanitizedHtml=function(e){var t=this.wwe.getSanitizer();e=Object(pt["a"])(e,!0),t&&t!==pt["a"]&&(e=t(e));var n=document.createElement("div");return n.innerHTML=e,dt["a"].finalizeHtml(n)},t._pasteClipboardHtml=function(e){var t=document.createDocumentFragment(),n="\x3c!--StartFragment--\x3e",r="\x3c!--EndFragment--\x3e",i=e.indexOf(n),o=e.lastIndexOf(r);i>-1&&o>-1&&(e=e.slice(i+n.length,o)),/<\/td>((?!<\/tr>)[\s\S])*$/i.test(e)&&(e="<TR>"+e+"</TR>"),/<\/tr>((?!<\/table>)[\s\S])*$/i.test(e)&&(e="<TABLE>"+e+"</TABLE>"),t.appendChild(this._getSanitizedHtml(e)),this._pasteClipboardContainer(t)},t._pasteClipboardContainer=function(e){var t=this.wwe.getEditor(),n=e.childNodes,r=1===n.length&&"TABLE"===n[0].nodeName;if(r){var i=this.wwe.componentManager.getManager("table");i.pasteTableData(e)}else{var o=t.getSelection().cloneRange(),a=this._preparePasteDocumentFragment(e);t.saveUndoState(o),o.collapsed||this._deleteContentsRange(o),dt["a"].isTextNode(o.startContainer)?this._pasteIntoTextNode(o,a):this._pasteIntoElements(o,a),t.setSelection(o)}},t._preparePasteDocumentFragment=function(e){var t=e.childNodes,n=document.createDocumentFragment();return t.length?n.appendChild(this._unwrapBlock(e)):this._isPossibleInsertToTable(e)&&n.appendChild(e),n},t._unwrapBlock=function(e){var t=document.createDocumentFragment(),n=Xe()(e.childNodes);while(n.length){var r=n.shift();if(this._isPossibleInsertToTable(r))t.appendChild(r);else{t.appendChild(this._unwrapBlock(r));var i=t.lastChild;n.length&&i&&"BR"!==i.nodeName&&t.appendChild(document.createElement("br"))}}return t},t._isPossibleInsertToTable=function(e){var t=e.nodeName,n="CODE"===t&&e.childNodes.length>1,r="UL"===t||"OL"===t;return!n&&(r||dt["a"].isMDSupportInlineNode(e)||dt["a"].isTextNode(e))},t._pasteIntoElements=function(e,t){var n=e.startContainer,r=e.startOffset,i=dt["a"].getChildNodeByOffset(n,r);if(i)n.insertBefore(t,i),e.setStart(i,0);else if("TD"===n.nodeName)n.appendChild(t),e.setStart(n,n.childNodes.length);else{var o=n.parentNode,a=n.nextSibling;o.insertBefore(t,a),a?e.setStart(a,0):e.setStartAfter(o.lastChild)}e.collapse(!0)},t._pasteIntoTextNode=function(e,t){var n=e.startContainer,r=e.startOffset,i=n.parentNode,o=n.textContent,a=o.slice(0,r),s=o.slice(r,o.length),l=t.childNodes,c=l[0],u=1===l.length&&dt["a"].isTextNode(c);if(a)if(s)if(u){var d=c.textContent;n.textContent=""+a+d+s,e.setStart(n,a.length+d.length)}else{var h=document.createDocumentFragment();h.appendChild(document.createTextNode(a)),h.appendChild(t),h.appendChild(document.createTextNode(s)),i.replaceChild(h,n);var f=Xe()(i.childNodes),p=0;f.forEach((function(e,t){e.textContent===s&&(p=t)})),e.setStart(i.childNodes[p],0)}else{var g=n.nextSibling;i.insertBefore(t,g),e.setStartAfter(g)}else i.insertBefore(t,n),e.setStart(n,0);e.collapse(!0)},t._deleteContentsRange=function(e){var t=e.startContainer,n=e.startOffset,r=e.endContainer,i=e.endOffset;t===r?(this._deleteContentsByOffset(t,n,i),e.setStart(t,n),e.collapse(!0)):this._deleteNotCollapsedRangeContents(e)},t._deleteNotCollapsedRangeContents=function(e){var t=e.startContainer,n=e.startOffset,r=e.endContainer,i=e.endOffset,o=e.commonAncestorContainer,a=this._getBlock(t,o,n),s=this._getBlock(r,o,i-1);if(a===s)this._removeInSameBlock(a,t,r,n,i),s=r!==s?null:s;else{var l=a.nextSibling;"TD"===t.nodeName?l=this._removeOneLine(a):(this._deleteContentsByOffset(t,n,dt["a"].getOffsetLength(t)),dt["a"].removeNodesByDirection(a,t,!1)),"TD"===r.nodeName?s=this._removeOneLine(s):(this._deleteContentsByOffset(r,0,i),dt["a"].removeNodesByDirection(s,r,!0)),dt["a"].removeChildFromStartToEndNode(o,l,s)}s?e.setStart(s,0):e.setStartAfter(a),e.collapse(!0)},t._removeInSameBlock=function(e,t,n,r,i){var o=t===e?r:0,a=n===e?i:dt["a"].getOffsetLength(e);this._deleteContentsByOffset(e,o,a)},t._removeOneLine=function(e){var t=e.nextSibling,n=e.parentNode,r=t;return n.removeChild(e),t&&"BR"===t.nodeName&&(r=t.nextSibling,n.removeChild(t)),r},t._getBlock=function(e,t,n){return dt["a"].getParentUntil(e,t)||dt["a"].getChildNodeByOffset(e,n)},t._deleteContentsByOffset=function(e,t,n){if(dt["a"].isTextNode(e)){var r=e.textContent,i=r.slice(0,t),o=r.slice(n,r.length);e.textContent=""+i+o}else{var a=dt["a"].getChildNodeByOffset(e,t),s=dt["a"].getChildNodeByOffset(e,n);a&&dt["a"].removeChildFromStartToEndNode(e,a,s||null)}},e}(),_t=bt,yt="tui-paste-table-bookmark",Ct="tui-paste-table-cell-bookmark",wt=function(){function e(e){this.wwe=e,this._pch=new vt(this.wwe),this._tablePasteHelper=new _t(this.wwe),this._selectedSellCount=0,this._clipboardArea=null}var t=e.prototype;return t.init=function(){var e=this;this.wwe.eventManager.listen("willPaste",(function(t){return e._executeHandler(e._onWillPaste.bind(e),t)})),this.wwe.eventManager.listen("copy",(function(t){return e._executeHandler(e._onCopyCut.bind(e),t)})),this.wwe.eventManager.listen("copyAfter",(function(t){return e._executeHandler(e._onCopyAfter.bind(e),t)})),this.wwe.eventManager.listen("cut",(function(t){return e._executeHandler(e._onCopyCut.bind(e),t)})),this.wwe.eventManager.listen("cutAfter",(function(t){return e._executeHandler(e._onCutAfter.bind(e),t)})),this.wwe.eventManager.listen("paste",(function(t){return e._executeHandler(e._onPasteIntoTable.bind(e),t)}))},t._executeHandler=function(e,t){"wysiwyg"===t.source&&e(t)},t._onCopyCut=function(e){var t=this.wwe.componentManager.getManager("tableSelection"),n=t.getSelectedCells().length;if(n)if(t.mergedTableSelectionManager){var r=this.wwe.getEditor(),i=e.data,o=r.getSelection().cloneRange(),a=document.createElement("div");this._extendRange(o),a.innerHTML=o.cloneContents(),this._updateCopyDataForListTypeIfNeed(o,a),this.wwe.eventManager.emit("copyBefore",{source:"wysiwyg",clipboardContainer:a}),this._setClipboardData(i,a.innerHTML,a.textContent)}else t.createRangeBySelectedCells()},t._clearClipboardArea=function(){this._clipboardArea&&(dt["a"].remove(this._clipboardArea),this._clipboardArea=null)},t._onCopyAfter=function(){this.wwe.getEditor().getBody().focus(),this._clearClipboardArea()},t._onCutAfter=function(){var e=this.wwe.getEditor().getSelection();e.deleteContents(),this.wwe.getEditor().focus(),this._clearClipboardArea()},t._onPasteIntoTable=function(e){var t=e.data,n=this.wwe.getEditor().getSelection();this.wwe.isInTable(n)&&this._isSingleCellSelected(n)&&this._tablePasteHelper.pasteClipboard(t)},t._isSingleCellSelected=function(e){var t=e.startContainer,n=e.endContainer;return this._getCell(t)===this._getCell(n)},t._getCell=function(e){return"TD"===e.nodeName?e:dt["a"].getParentUntil(e,"TR")},t._replaceNewLineToBr=function(e){var t=dt["a"].getAllTextNode(e);t.forEach((function(e){/\n/.test(e.nodeValue)&&(e.parentNode.innerHTML=e.nodeValue.replace(/\n/g,"<br>"))}))},t._onWillPaste=function(e){var t=this,n=e.data,r=document.createElement("div");r.appendChild(n.fragment.cloneNode(!0)),this._preparePaste(r),this._setTableBookmark(r),n.fragment=document.createDocumentFragment(),Xe()(r.childNodes).forEach((function(e){"DIV"===dt["a"].getNodeName(e)&&t._replaceNewLineToBr(e),n.fragment.appendChild(e)}));var i=function e(){t.wwe.getEditor().removeEventListener("input",e),t.wwe.eventManager.emit("wysiwygRangeChangeAfter",t),t._focusTableBookmark()};this.wwe.getEditor().addEventListener("input",i)},t._setClipboardData=function(e,t,n){it.a.msie?(e.squirePrevented=!0,this._clipboardArea=this._createClipboardArea(),this._clipboardArea.innerHTML=t,this._clipboardArea.focus(),window.getSelection().selectAllChildren(this._clipboardArea)):(e.preventDefault(),e.stopPropagation(),e.clipboardData.setData("text/html",t),e.clipboardData.setData("text/plain",n))},t._createClipboardArea=function(){var e=document.createElement("div");return e.setAttribute("contenteditable",!0),h()(e,{position:"fixed",overflow:"hidden",top:0,right:"100%",width:"1px",height:"1px"}),document.body.appendChild(e),e},t._updateCopyDataForListTypeIfNeed=function(e,t){var n=e.commonAncestorContainer.nodeName;if("UL"===n||"OL"===n){var r=document.createElement(n);r.appendChild(t),t.innerHTML="",t.appendChild(r)}},t._removeEmptyFontElement=function(e){var t=dt["a"].children(e,"font");t.forEach((function(e){e.textContent.trim()||dt["a"].remove(e)}))},t._isFromMs=function(e){return/<p style="[^>]*mso-/.test(e)},t._preProcessPtag=function(e){dt["a"].findAll(e,"p").forEach((function(e){e.lastChild&&"BR"!==e.lastChild.nodeName&&e.appendChild(document.createElement("br")),e.appendChild(document.createElement("br"))}))},t._preparePaste=function(e){this._isFromMs(e.innerText)||this._preProcessPtag(e),this._removeEmptyFontElement(e),this._pch.preparePaste(e),this.wwe.eventManager.emit("pasteBefore",{source:"wysiwyg",clipboardContainer:e})},t._setTableBookmark=function(e){var t=e.lastChild,n=t&&"TABLE"===t.nodeName;n&&p()(t,yt)},t._focusTableBookmark=function(){var e=this.wwe.getEditor(),t=e.getSelection().cloneRange(),n=e.getBody().querySelector("."+yt),r=e.getBody().querySelector("."+Ct);n&&(m()(n,yt),t.setEndAfter(n),t.collapse(!1),e.setSelection(t)),r&&(m()(r,Ct),t.selectNodeContents(r),t.collapse(!1),e.setSelection(t))},t._extendRange=function(e){(!dt["a"].isTextNode(e.commonAncestorContainer)||0===e.startOffset&&e.commonAncestorContainer.textContent.length===e.endOffset||"TD"===e.commonAncestorContainer.nodeName)&&(0===e.startOffset&&(e=this._extendStartRange(e)),e.endOffset===dt["a"].getOffsetLength(e.endContainer)&&(e=this._extendEndRange(e)),this._isWholeCommonAncestorContainerSelected(e)&&e.selectNode(e.commonAncestorContainer),this.wwe.getEditor().setSelection(e))},t._extendStartRange=function(e){var t=e.startContainer;while(t.parentNode!==e.commonAncestorContainer&&t.parentNode!==this.wwe.getBody()&&!t.previousSibling)t=t.parentNode;return e.setStart(t.parentNode,dt["a"].getNodeOffsetOfParent(t)),e},t._extendEndRange=function(e){var t=e.endContainer,n=t.nextSibling;while(t.parentNode!==e.commonAncestorContainer&&t.parentNode!==this.wwe.getBody()&&(!n||"BR"===dt["a"].getNodeName(n)&&t.parentNode.lastChild===n))t=t.parentNode,n=t.nextSibling;return e.setEnd(t.parentNode,dt["a"].getNodeOffsetOfParent(t)+1),e},t._isWholeCommonAncestorContainerSelected=function(e){return e.commonAncestorContainer.nodeType===Node.ELEMENT_NODE&&e.commonAncestorContainer!==this.wwe.getBody()&&0===e.startOffset&&e.endOffset===e.commonAncestorContainer.childNodes.length&&e.commonAncestorContainer===e.startContainer&&e.commonAncestorContainer===e.endContainer},e}(),Et=wt,Tt=/<li/i,Nt="DIV,LI",kt="OL,UL",St=/(<(?:th|td)[^>]*>)(.*?)(<\/(?:th|td)>)/g,xt=/<(ul|ol|li)([^>]*)>/g,Lt=function(){function e(e){this.wwe=e,this.eventManager=e.eventManager,this.name="list",this._init()}var t=e.prototype;return t._init=function(){this._initEvent(),this._initKeyHandler()},t._initEvent=function(){var e=this;this.eventManager.listen("wysiwygSetValueBefore",(function(t){return e._convertToArbitraryNestingList(t)})),this.eventManager.listen("wysiwygRangeChangeAfter",(function(){e._findAndRemoveEmptyList(),e._removeBranchListAll()})),this.eventManager.listen("wysiwygSetValueAfter",(function(){e._removeBranchListAll()})),this.eventManager.listen("wysiwygProcessHTMLText",(function(t){return t=e._convertFromArbitraryNestingList(t),t})),this.eventManager.listen("convertorBeforeHtmlToMarkdownConverted",(function(t){return e._insertDataToMarkPassForListInTable(t)}))},t._initKeyHandler=function(){var e=this;this.wwe.addKeyEventHandler(["TAB","CTRL+]","META+]"],(function(t){var n;return e.wwe.getEditor().hasFormat("LI")&&(t.preventDefault(),e.eventManager.emit("command","Indent"),n=!1),n})),this.wwe.addKeyEventHandler(["SHIFT+TAB","CTRL+[","META+["],(function(t,n){var r;if(e.wwe.getEditor().hasFormat("LI")){t.preventDefault();var i=dt["a"].children(dt["a"].closest(n.startContainer,"li"),kt);e.eventManager.emit("command","Outdent"),i.length&&!i.previousSibling&&e._removeBranchList(i),r=!1}return r})),this.wwe.addKeyEventHandler("ENTER",(function(t,n){n.collapsed&&e.wwe.getEditor().hasFormat("LI")&&e.wwe.defer((function(){var t=e.wwe.getRange(),n=dt["a"].parents(t.startContainer,"li"),r=n[0];e._removeBranchListAll(r)}))})),this.wwe.addKeyEventHandler("BACK_SPACE",(function(t,n){n.collapsed&&e.wwe.getEditor().hasFormat("LI")&&e.wwe.defer((function(){e._removeBranchListAll()}))}))},t._findAndRemoveEmptyList=function(){dt["a"].findAll(this.wwe.getBody(),kt).forEach((function(e){Tt.test(e.innerHTML)||dt["a"].remove(e)}))},t._removeBranchListAll=function(e){var t=this;e=e||this.wwe.getBody(),dt["a"].findAll(e,"li > ul, li > ol").forEach((function(e){e&&!e.previousSibling&&t._removeBranchList(e)}))},t._removeBranchList=function(e){var t=e;while(!t.previousSibling&&t.parentElement.tagName.match(/UL|OL|LI/g))t=t.parentElement;var n=dt["a"].children(t,"li"),r=n[0],i=dt["a"].unwrap(e);dt["a"].prepend(t,i),dt["a"].remove(r)},t._convertToArbitraryNestingList=function(e){var t="li > ul, li > ol",n=dt["a"].createElementWith("<div>"+e+"</div>"),r=n.querySelector(t);while(null!==r){var i=r.parentNode,o=i.parentNode;o.insertBefore(r,i.nextElementSibling),r=n.querySelector(t)}return n.innerHTML},t._convertFromArbitraryNestingList=function(e){var t="ol > ol, ol > ul, ul > ol, ul > ul",n=dt["a"].createElementWith("<div>"+e+"</div>"),r=n.querySelector(t);while(null!==r){var i=r.previousElementSibling;while(i&&"LI"!==i.tagName)i=i.previousElementSibling;i?i.appendChild(r):this._unwrap(r),r=n.querySelector(t)}return n.innerHTML},t._unwrap=function(e){var t=document.createDocumentFragment();while(e.firstChild)t.appendChild(e.firstChild);e.parentNode.replaceChild(t,e)},t._insertDataToMarkPassForListInTable=function(e){var t=e.replace(St,(function(e,t,n,r){var i=n.replace(xt,'<$1 data-tomark-pass="" $2>');return""+t+i+r}));return t},t.getLinesOfSelection=function(e,t){var n,r=[],i=!1,o=!0;if(dt["a"].isTextNode(e)){var a=dt["a"].parents(e,Nt);e=a[0]}if(dt["a"].isTextNode(t)){var s=dt["a"].parents(t,Nt);t=s[0]}for(var l=e;o;l=n){if(!ft()(l,Nt))break;r.push(l),l===t?i=!0:n=this._getNextLine(l,t),o=n&&!i}return r},t._getNextLine=function(e,t){var n=e.nextElementSibling;return n?ft()(n,kt)&&(n=n.querySelector("li")):n=e.parentNode.nextElementSibling,ft()(n,Nt)||n===t?n:this._getNextLine(n)},t.mergeList=function(e){var t=e.parentNode,n=t.previousElementSibling,r=t.nextElementSibling;t.firstElementChild===e&&n&&ft()(n,kt)&&(this._mergeList(t,n),t=n),t.lastElementChild===e&&r&&ft()(r,kt)&&this._mergeList(r,t)},t._mergeList=function(e,t){var n=e.firstElementChild;if(t&&ft()(t,kt)){while(n){var r=n.nextElementSibling;t.appendChild(n),n=r}e.parentNode.removeChild(e)}},t.isAvailableMakeListInTable=function(){var e=this.wwe.componentManager.getManager("tableSelection"),t=e.getSelectedCells(),n=this.wwe.getEditor();return t&&n.hasFormat("table")&&!n.hasFormat("OL")&&!n.hasFormat("UL")},t._getParentNodeBeforeTD=function(e,t){var n=dt["a"].getParentUntil(e,"TD");if(!n){var r=e.childNodes,i=r?r.length:0,o=t>0&&t===i?t-1:t;n=dt["a"].getChildNodeByOffset(e,o)}return n},t._findLINodeInsideTD=function(e,t){var n=null,r=dt["a"].getParentUntilBy(e,(function(e){return e&&dt["a"].isListNode(e)}),(function(e){return e&&"TD"===e.nodeName}));if(r)n=r;else if("LI"===e.nodeName)n=e;else if(dt["a"].isListNode(e)){var i=e.childNodes.length;n=e.childNodes[t>=i?i-1:t]}return n},t._getFirstNodeInLineOfTable=function(e,t){var n=this._findLINodeInsideTD(e,t);if(!n){n=this._getParentNodeBeforeTD(e,t);var r=n,i=r.previousSibling;while(i&&"BR"!==i.nodeName&&!dt["a"].isListNode(i))n=i,i=n.previousSibling}return n},t._getLastNodeInLineOfTable=function(e,t){var n=this._findLINodeInsideTD(e,t);if(!n){n=this._getParentNodeBeforeTD(e,t);while(n.nextSibling){if("BR"===n.nodeName||dt["a"].isListNode(n))break;n=n.nextSibling}}return n},t._isLastNodeInLineOfTable=function(e){var t=e.nodeName;return"LI"===t||"BR"===t},t._getNextNodeInLineOfTable=function(e){var t=e.nextSibling;if("LI"!==e.nodeName||t)dt["a"].isListNode(t)&&(t=t.firstChild);else{var n=e.parentNode;while("TD"!==n.nodeName){if(n.nextSibling){t=n.nextSibling;break}n=n.parentNode}}return t},t._getLinesOfSelectionInTable=function(e){var t=e.startContainer,n=e.endContainer,r=e.startOffset,i=e.endOffset,o=this._getFirstNodeInLineOfTable(t,r),a=this._getLastNodeInLineOfTable(n,i),s=[],l=[];while(o){if(l.push(o),this._isLastNodeInLineOfTable(o)&&(s.push(l),l=[]),o===a){l.length&&s.push(l);break}o=this._getNextNodeInLineOfTable(o)}return s},t._createListElement=function(e){return document.createElement("TASK"===e?"UL":e)},t._createListItemElement=function(e,t){var n=document.createElement("li");if(e.forEach((function(e){n.appendChild(e)})),"TASK"===t){var r=this.wwe.componentManager.getManager("task");r.formatTask(n)}return n},t._mergeListWithPreviousSibiling=function(e){var t=e.previousSibling,n=e;return t&&e.nodeName===t.nodeName&&(this._mergeList(e,t),n=t),n},t._mergeListWithNextSibiling=function(e){var t=e.nextSibling;return t&&e.nodeName===t.nodeName&&this._mergeList(t,e),e},t.createListInTable=function(e,t){var n=this,r=this._getLinesOfSelectionInTable(e),i=r[r.length-1],o=i[i.length-1],a=o.nextSibling,s=o.parentNode,l=this._createListElement(t),c=l,u=c.nodeName,d=[];return r.forEach((function(e){var r,i=e[0];if("LI"===i.nodeName){var o=i.parentNode;if(r=i,o.nodeName!==u){var a=o.childNodes;Xe()(a).forEach((function(){l.appendChild(o.firstChild)})),o.parentNode.replaceChild(l,o)}l=r.parentNode}else r=n._createListItemElement(e,t),l.appendChild(r);d.push(r)})),l.parentNode||s.insertBefore(l,a),l=this._mergeListWithPreviousSibiling(l),this._mergeListWithNextSibiling(l),d},t.adjustRange=function(e,t,n,r,i){var o=dt["a"].containsNode(i[0],e)?e:i[0],a=dt["a"].containsNode(i[i.length-1],t)?t:i[i.length-1],s="TD"===e.nodeName?0:n,l="TD"===t.nodeName?0:r;this.wwe.setSelectionByContainerAndOffset(o,s,a,l)},e}(),Mt=Lt,At=n(16),Bt=n.n(At),Ot="task-list-item",Dt="data-te-task",Rt="checked",It=function(){function e(e){this.wwe=e,this.eventManager=e.eventManager,this.name="task",this._init()}var t=e.prototype;return t._init=function(){this._initKeyHandler(),this._initEvent(),this.wwe.getEditor().addEventListener("mousedown",(function(e){var t=getComputedStyle(e.target,":before");e.target.hasAttribute(Dt)&&dt["a"].isInsideTaskBox(t,e.offsetX,e.offsetY)&&(e.preventDefault(),dt["a"].toggleClass(e.target,Rt))}))},t._initEvent=function(){var e=this;this.eventManager.listen("wysiwygSetValueAfter",(function(){e._removeTaskListClass()}))},t._initKeyHandler=function(){var e=this;this.wwe.addKeyEventHandler("ENTER",(function(t,n){e.isInTaskList(n)&&e.wwe.defer((function(){var t=e.wwe.getRange(),n=dt["a"].closest(t.startContainer,"li");n&&m()(n,Rt)}))}))},t.isInTaskList=function(e){var t;if(e||(e=this.wwe.getEditor().getSelection().cloneRange()),e.startContainer.nodeType===Node.ELEMENT_NODE&&"LI"===e.startContainer.tagName)t=e.startContainer;else{var n=dt["a"].parents(e.startContainer,"li");t=n[0]}return!!t&&Bt()(t,Ot)},t.unformatTask=function(e){var t=dt["a"].closest(e,"li");m()(t,Ot),m()(t,Rt),t.removeAttribute(Dt),t.getAttribute("class")||t.removeAttribute("class")},t.formatTask=function(e){var t=dt["a"].closest(e,"li");p()(t,Ot),t.setAttribute(Dt,"")},t._formatTaskIfNeed=function(){var e=this.wwe.getEditor().getSelection().cloneRange();this.isInTaskList(e)&&this.formatTask(e.startContainer)},t._removeTaskListClass=function(){dt["a"].findAll(this.wwe.getBody(),".task-list").forEach((function(e){m()(e,"task-list")}))},e}(),Pt=It,Ht=it.a.msie&&10===it.a.version,Ft="te-content-table-",Ut=it.a.msie&&(10===it.a.version||11===it.a.version),qt=it.a.msie?"":"<br>",jt="te-cell-selected",Wt=function(){function e(e){this.wwe=e,this.eventManager=e.eventManager,this.name="table",this._lastCellNode=null,this._init()}var t=e.prototype;return t._init=function(){this._initKeyHandler(),this._initEvent(),this.tableID=0},t._initEvent=function(){var e=this;this.eventManager.listen("wysiwygRangeChangeAfter.table",(function(){var t=e.wwe.getEditor().getSelection(),n=e.wwe.isInTable(t);if(e._unwrapBlockInTable(),e._completeTableIfNeed(),!n){var r=e.wwe.componentManager.getManager("tableSelection");r.removeClassAttrbuteFromAllCellsIfNeed()}e._insertDefaultBlockBetweenTable()})),this.eventManager.listen("wysiwygSetValueAfter.table",(function(){e._unwrapBlockInTable(),e._insertDefaultBlockBetweenTable()})),this.eventManager.listen("wysiwygProcessHTMLText.table",(function(e){return e.replace(/<br \/>(<\/td>|<\/th>)/g,"$1")})),this.eventManager.listen("cut.table",(function(){var t=e.wwe.componentManager.getManager("tableSelection"),n=t.getSelectedCells();n.length&&n.forEach((function(e){e.innerHTML=qt})),t.removeClassAttrbuteFromAllCellsIfNeed()})),this.eventManager.listen("copyBefore.table",(function(t){var n=t.clipboardContainer;return e.updateTableHtmlOfClipboardIfNeed(n)}))},t.updateTableHtmlOfClipboardIfNeed=function(e){var t=this,n=this.wwe.componentManager.getManager("tableSelection"),r=n.getSelectedCells();if(r.length){n.createRangeBySelectedCells();var i=this.wwe.getEditor().getSelection().cloneContents();Xe()(i.children).forEach((function(e){if(t.isTableOrSubTableElement(e.nodeName))if("TABLE"===e.nodeName&&e.querySelector("thead")&&e.querySelector("tbody"))dt["a"].remove(e);else if(e.previousSibling&&"TABLE"===e.previousSibling.nodeName)e.previousSibling.appendChild(e);else if(t._completeIncompleteTable(e),"TABLE"!==e.nodeName&&"THEAD"!==e.nodeName){var n=dt["a"].closest(e,"table").querySelector("thead");dt["a"].remove(n)}})),e.appendChild(i),dt["a"].findAll(e,"."+jt).forEach((function(e){m()(e,jt)}))}},t.pasteTableData=function(e){this._expandTableIfNeed(e),this._pasteDataIntoTable(e)},t._initKeyHandler=function(){var e=this;this.keyEventHandlers={DEFAULT:function(t,n,r){var i=e.wwe.isInTable(n);i&&!e._isModifierKey(r)?(e._recordUndoStateIfNeed(n),e._removeContentsAndChangeSelectionIfNeed(n,r,t)):!i&&e._lastCellNode&&e._recordUndoStateAndResetCellNode(n),i&&!e._isModifierKeyPushed(t)&&e.wwe.getEditor().modifyDocument((function(){var t=e.wwe.componentManager.getManager("tableSelection");t.removeClassAttrbuteFromAllCellsIfNeed()}))},ENTER:function(t,n){var r;return e._isAfterTable(n)?(t.preventDefault(),n.setStart(n.startContainer,n.startOffset-1),e.wwe.breakToNewDefaultBlock(n),r=!1):e._isBeforeTable(n)?(t.preventDefault(),e.wwe.breakToNewDefaultBlock(n,"before"),r=!1):e.wwe.isInTable(n)&&(!e._isInList(n.startContainer)&&e._isInStyledText(n)?e.wwe.defer((function(){e._removeBRinStyleText()})):e._isEmptyFirstLevelLI(n)&&e.wwe.defer((function(){var t=e.wwe.getRange().cloneRange(),n=t.startContainer,r=document.createElement("br");n.parentNode.replaceChild(r,n),t.setStartBefore(r),t.collapse(!0),e.wwe.getEditor().setSelection(t)})),e._appendBrIfTdOrThNotHaveAsLastChild(n),r=!1),r},BACK_SPACE:function(t,n,r){return e._handleBackspaceAndDeleteKeyEvent(t,n,r)},DELETE:function(t,n,r){return e._handleBackspaceAndDeleteKeyEvent(t,n,r)},TAB:function(){return e._moveCursorTo("next","cell")},"SHIFT+TAB":function(t){return e._moveCursorTo("previous","cell",t)},UP:function(t){return e._moveCursorTo("previous","row",t)},DOWN:function(t){return e._moveCursorTo("next","row",t)}},i()(this.keyEventHandlers,(function(t,n){return e.wwe.addKeyEventHandler(n,t)}))},t._isEmptyListItem=function(e){var t=e.childNodes,n=e.nodeName;return"LI"===n&&1===t.length&&"BR"===t[0].nodeName},t._isEmptyFirstLevelLI=function(e){var t=e.collapsed,n=e.startContainer,r=e.startOffset;return t&&0===r&&this._isEmptyListItem(n)&&dt["a"].isFirstLevelListItem(n)},t._isInStyledText=function(e){var t,n=e.startContainer;return t=dt["a"].isTextNode(n)?n.parentNode:n,e.collapsed&&dt["a"].isStyledNode(t)},t._removeBRinStyleText=function(){var e,t=this.wwe.getRange(),n=t.startContainer,r=t.startOffset;e="TD"===n.nodeName?dt["a"].getChildNodeByOffset(n,r-1):dt["a"].getParentUntil(n,"TD");var i=e.querySelector("br");if(i){var o=e,a=o.parentNode,s=o.nodeName;if("CODE"!==s||i.previousSibling)if("CODE"!==s||i.nextSibling){var l=this._splitByBR(e,i);t.setStart(l,0)}else a.insertBefore(i,e.nextSibling),t.setStart(a,dt["a"].getNodeOffsetOfParent(i)+1);else a.insertBefore(i,e),t.setStart(e,0);t.collapse(!0),this.wwe.getEditor().setSelection(t)}},t._splitByBR=function(e,t){var n=e.cloneNode(!0),r=document.createElement("br"),i=e.parentNode;dt["a"].removeNodesByDirection(e,t,!1),t.parentNode.removeChild(t);var o=n.querySelector("br");dt["a"].removeNodesByDirection(n,o,!0),o.parentNode.removeChild(o),i.insertBefore(n,e.nextSibling),i.insertBefore(r,n);var a=dt["a"].getLeafNode(n);return dt["a"].getTextLength(a)||(a.textContent="​"),a},t._isBeforeTable=function(e){return"TABLE"===dt["a"].getNodeName(dt["a"].getChildNodeByOffset(e.startContainer,e.startOffset))},t._isAfterTable=function(e){var t=dt["a"].getPrevOffsetNodeUntil(e.startContainer,e.startOffset);return"TABLE"===dt["a"].getNodeName(t)&&e.commonAncestorContainer===this.wwe.getBody()},t._handleBackspaceAndDeleteKeyEvent=function(e,t,n){var r="BACK_SPACE"===n,i=this.wwe.componentManager.getManager("tableSelection"),o=i.getSelectedCells(),a=!0;if(t.collapsed){if(this.wwe.isInTable(t))r?this._tableHandlerOnBackspace(t,e):this._tableHandlerOnDelete(t,e),this._removeContentsAndChangeSelectionIfNeed(t,n,e),a=!1;else if(!r&&this._isBeforeTable(t)||r&&this._isAfterTable(t)){e.preventDefault();var s=r?t.startOffset-1:t.startOffset;this._removeTable(t,dt["a"].getChildNodeByOffset(t.startContainer,s)),a=!1}}else if(this.wwe.isInTable(t)&&o.length>0){var l=this._removeContentsAndChangeSelectionIfNeed(t,n,e);l&&(e.preventDefault(),a=!1)}return a},t._moveListItemToPreviousOfList=function(e,t){var n=e.parentNode,r=e.firstChild,i=document.createDocumentFragment();dt["a"].mergeNode(e,i),n.parentNode.insertBefore(i,n),t.setStart(r,0),t.collapse(!0),this.wwe.getEditor().setSelection(t),n.hasChildNodes()||n.parentNode.removeChild(n)},t._isInList=function(e){return dt["a"].getParentUntilBy(e,(function(e){return e&&(dt["a"].isListNode(e)||"LI"===e.nodeName)}),(function(e){return e&&("TD"===e.nodeName||"TH"===e.nodeName)}))},t._findListItem=function(e){return dt["a"].getParentUntilBy(e,(function(e){return e&&dt["a"].isListNode(e)}),(function(e){return e&&("TD"===e.nodeName||"TH"===e.nodeName)}))},t._tableHandlerOnBackspace=function(e,t){var n=e.startContainer,r=e.startOffset,i=this._findListItem(n);if(i&&0===r&&dt["a"].isFirstListItem(i)&&dt["a"].isFirstLevelListItem(i))this.wwe.getEditor().saveUndoState(e),this._moveListItemToPreviousOfList(i,e),t.preventDefault();else{var o=dt["a"].getPrevOffsetNodeUntil(n,r,"TR"),a=dt["a"].getNodeName(o);"BR"===a&&1!==o.parentNode.childNodes.length&&(t.preventDefault(),dt["a"].remove(o))}},t._isDeletingBR=function(e){var t=this._getCurrentNodeInCell(e),n=t&&t.nextSibling;return"BR"===dt["a"].getNodeName(t)&&!!n&&"BR"===dt["a"].getNodeName(n)},t._getCurrentNodeInCell=function(e){var t,n=e.startContainer,r=e.startOffset;return"TD"===dt["a"].getNodeName(n)?t=dt["a"].getChildNodeByOffset(n,r):dt["a"].getParentUntil(n,"TD")&&(t=n),t},t._isEndOfList=function(e,t){var n=t.startContainer,r=t.startOffset,i=!1;if(!e.nextSibling)if(e===n){var o=dt["a"].getOffsetLength(e);"BR"===e.lastChild.nodeName&&(o-=1),i=o===r}else{var a=dt["a"].getParentUntil(n,"li")||n,s=dt["a"].getOffsetLength(n),l=e.lastChild;"BR"===l.nodeName&&(l=l.previousSibling),i=l===a&&s===r}return i},t._getNextLineNode=function(e){var t=document.createDocumentFragment(),n=dt["a"].getParentUntil(e,"TD"),r=n.nextSibling;while(r){var i=r,o=i.nextSibling;if(t.appendChild(r),"BR"===r.nodeName)break;r=o}return t},t._tableHandlerOnDelete=function(e,t){var n=this._findListItem(e.startContainer);if(n&&this._isEndOfList(n,e))this.wwe.getEditor().saveUndoState(e),"BR"===n.lastChild.nodeName&&n.removeChild(n.lastChild),dt["a"].mergeNode(this._getNextLineNode(n),n),t.preventDefault();else if(this._isDeletingBR(e)){var r=this._getCurrentNodeInCell(e);r.parentNode.removeChild(r.nextSibling),t.preventDefault()}},t._appendBrIfTdOrThNotHaveAsLastChild=function(e){var t,n=dt["a"].getNodeName(e.startContainer);if("TD"===n||"TH"===n)t=e.startContainer;else{var r=dt["a"].parentsUntil(e.startContainer,"tr");t=r[r.length-1]}var i=dt["a"].getNodeName(t.lastChild);"BR"===i||"DIV"===i||"UL"===i||"OL"===i||Ut||dt["a"].append(t,"<br />")},t._unwrapBlockInTable=function(){var e=dt["a"].findAll(this.wwe.getBody(),"td div,th div,tr>br,td>br,th>br");e.forEach((function(e){if("BR"===dt["a"].getNodeName(e)){var t=dt["a"].getNodeName(e.parentNode),n=/TD|TH/.test(t),r=0===e.parentNode.textContent.length,i=e.parentNode.lastChild===e;("TR"===t||n&&!r&&i)&&dt["a"].remove(e)}else dt["a"].unwrap(e)}))},t._insertDefaultBlockBetweenTable=function(){var e=dt["a"].findAll(this.wwe.getBody(),"table");e.forEach((function(e){if(e.nextElementSibling&&"TABLE"===e.nextElementSibling.nodeName){var t=document.createElement("div");t.appendChild(document.createElement("br")),dt["a"].insertAfter(t,e)}}))},t._removeTable=function(e,t){"TABLE"===t.tagName&&(this.wwe.getEditor().saveUndoState(e),this.wwe.saveSelection(e),dt["a"].remove(t),this.wwe.restoreSavedSelection())},t._recordUndoStateIfNeed=function(e){var t=dt["a"].getParentUntil(e.startContainer,"TR");e.collapsed&&t&&this._lastCellNode!==t&&(this.wwe.getEditor().saveUndoState(e),this._lastCellNode=t)},t._recordUndoStateAndResetCellNode=function(e){this.wwe.getEditor().saveUndoState(e),this.resetLastCellNode()},t._pasteDataIntoTable=function(e){var t,n,r,i,o=this.wwe.getEditor().getSelection(),a=o.startContainer,s=this._getTableDataFromTable(e),l="TD"===a.nodeName||"TH"===a.nodeName,c=Ht?"":"<br />";l?t=a:(t=dt["a"].getParentUntilBy(a,(function(e){return e&&("TD"===e.nodeName||"TH"===e.nodeName)}),(function(e){return!!dt["a"].closest(e,"table")})),t=t?t.parentNode:null),t=t||a.querySelector("th,td"),n=t;while(s.length){r=s.shift();while(n&&r.length)i=r.shift(),i.length?n.textContent=i:n.innerHTML=c,n=dt["a"].getTableCellByDirection(n,"next");n=dt["a"].getSiblingRowCellByDirection(t,"next",!1),t=n}},t._getTableDataFromTable=function(e){var t=[];return dt["a"].findAll(e,"tr").forEach((function(e){var n=[];Xe()(e.children).forEach((function(e){n.push(e.textContent)})),n.length&&t.push(n)})),t},t._removeTableContents=function(e){this.wwe.getEditor().saveUndoState(),Xe()(e).forEach((function(e){var t=Ht?"":"<br />";e.innerHTML=t}))},t.wrapDanglingTableCellsIntoTrIfNeed=function(e){var t,n=dt["a"].children(e,"td,th");if(n.length){var r=document.createElement("tr");Xe()(n).forEach((function(e){dt["a"].append(r,e)})),t=r}return t},t.wrapTrsIntoTbodyIfNeed=function(e){var t,n=dt["a"].children(e,"tr"),r=[];if(Xe()(n).forEach((function(e){r=r.concat(e.querySelectorAll("th"))})),r.length&&Xe()(r).forEach((function(e){var t=document.createElement("td");t.innerHTML=e.innerHTML,dt["a"].insertBefore(e,t),dt["a"].remove(e)})),n.length){var i=document.createElement("tbody");Xe()(n).forEach((function(e){dt["a"].append(i,e)})),t=i}return t},t.wrapTheadAndTbodyIntoTableIfNeed=function(e){var t,n=dt["a"].children(e,"thead"),r=dt["a"].children(e,"tbody"),i=document.createElement("table");return!r.length&&n.length?(dt["a"].append(i,n[0]),dt["a"].append(i,this._createTheadOrTboday("tbody")),t=i):r.length&&!n.length?(dt["a"].append(i,this._createTheadOrTboday("thead")),dt["a"].append(i,r[0]),t=i):r.length&&n.length&&(dt["a"].append(i,n[0]),dt["a"].append(i,r[0]),t=i),t},t.isTableOrSubTableElement=function(e){return"TABLE"===e||"TBODY"===e||"THEAD"===e||"TR"===e||"TD"===e},t._createTheadOrTboday=function(e){var t=document.createElement(e),n=document.createElement("tr");return t.appendChild(n),t},t._stuffTableCellsIntoIncompleteRow=function(e,t){Xe()(e).forEach((function(e){for(var n=e.querySelectorAll("th,td"),r=dt["a"].getNodeName(e.parentNode),i="THEAD"===r?"th":"td",o=n.length;o<t;o+=1)dt["a"].append(e,Vt(1,i))}))},t.prepareToTableCellStuffing=function(e){var t=e[0].querySelectorAll("th,td").length,n=!1;return Xe()(e).forEach((function(e){var r=e.querySelectorAll("th,td").length;t!==r&&(n=!0,t<r&&(t=r))})),{maximumCellLength:t,needTableCellStuffingAid:n}},t._addTbodyOrTheadIfNeed=function(e){var t=!e.querySelector("thead"),n=!e.querySelector("tbody");t?dt["a"].prepend(e,"<thead><tr></tr></thead>"):n&&dt["a"].append(e,"<tbody><tr></tr></tbody>")},t.tableCellAppendAidForTableElement=function(e){this._addTbodyOrTheadIfNeed(e),this._addTrIntoContainerIfNeed(e);var t=e.querySelectorAll("tr"),n=this.prepareToTableCellStuffing(t),r=n.maximumCellLength,i=n.needTableCellStuffingAid;i&&this._stuffTableCellsIntoIncompleteRow(t,r)},t._generateTheadAndTbodyFromTbody=function(e){var t=document.createElement("tr"),n=document.createElement("thead");return dt["a"].append(t,Vt(e.querySelector("tr > td").length,"th")),dt["a"].append(n,t),{thead:n,tbody:e}},t._generateTheadAndTbodyFromThead=function(e){var t=document.createElement("tr"),n=document.createElement("tbody");return dt["a"].append(t,Vt(e.querySelectorAll("th").length,"td")),dt["a"].append(n,t),{thead:e,tbody:n}},t._generateTheadAndTbodyFromTr=function(e){var t,n,r=document.createElement("thead"),i=document.createElement("tbody");return"TH"===e.children[0].tagName?(t=e,n=dt["a"].createElementWith("<tr>"+Vt(e.querySelectorAll("th").length,"td")+"</tr>")):(t=dt["a"].createElementWith("<tr>"+Vt(e.querySelectorAll("td").length,"th")+"</tr>"),n=e),dt["a"].append(r,t),dt["a"].append(i,n),{thead:r,tbody:i}},t._completeIncompleteTable=function(e){var t,n,r=e.tagName;"TABLE"===r?t=e:(t=document.createElement("table"),e.parentNode.insertBefore(t,e.nextSibling),"TBODY"===r?n=this._generateTheadAndTbodyFromTbody(e):"THEAD"===r?n=this._generateTheadAndTbodyFromThead(e):"TR"===r&&(n=this._generateTheadAndTbodyFromTr(e)),t.appendChild(n.thead),t.appendChild(n.tbody)),this._removeEmptyRows(t),this.tableCellAppendAidForTableElement(t)},t._removeEmptyRows=function(e){dt["a"].findAll(e,"tr").forEach((function(e){e.cells.length||e.parentNode.removeChild(e)}))},t._completeTableIfNeed=function(){var e=this,t=this.wwe.getEditor().getBody();Xe()(t.children).forEach((function(t){e.isTableOrSubTableElement(t.nodeName)&&("TABLE"!==t.nodeName||t.querySelector("tbody")?e._completeIncompleteTable(t):dt["a"].remove(t))}))},t.resetLastCellNode=function(){this._lastCellNode=null},t.setLastCellNode=function(e){this._lastCellNode=e},t._isModifierKey=function(e){return/((META|SHIFT|ALT|CONTROL)\+?)/g.test(e)},t._isModifierKeyPushed=function(e){return e.metaKey||e.ctrlKey||e.altKey||e.shiftKey},t._addTrIntoContainerIfNeed=function(e){Xe()(e.children).forEach((function(e){var t=0===e.querySelectorAll("tr").length;t&&dt["a"].append(e,"<tr></tr>")}))},t._expandTableIfNeed=function(e){var t=this.wwe.getEditor().getSelection().cloneRange(),n=dt["a"].parents(t.startContainer,"table"),r=n[0],i=this._getColumnAndRowDifference(e,t);i.column<0&&this._appendCellForAllRow(r,i.column),i.row<0&&this._appendRow(r,i.row)},t._getColumnAndRowDifference=function(e,t){var n=this._getTableDataFromTable(e),r=n.length,i=n[0].length,o=dt["a"].closest(t.startContainer,"th,td"),a=o.parentNode,s=dt["a"].getNodeOffsetOfParent(o),l=dt["a"].getNodeOffsetOfParent(o.parentNode),c=dt["a"].parents(a,"table"),u=c[0],d=u.querySelector("tr").children.length,h=u.querySelectorAll("tr").length,f=!!dt["a"].parents(a,"tbody").length;return f&&(l+=1),{row:h-(l+r),column:d-(s+i)}},t._appendCellForAllRow=function(e,t){var n=Ht?"":"<br />";dt["a"].findAll(e,"tr").forEach((function(e,r){for(var i,o=t;o<0;o+=1)i=0===r?"th":"td",dt["a"].append(e,"<"+i+">"+n+"</"+i+">")}))},t._appendRow=function(e,t){var n=e.querySelectorAll("tr"),r=n[n.length-1].cloneNode(!0),i=Ht?"":"<br />";for(dt["a"].findAll(r,"td").forEach((function(e){e.innerHTML=i}));t<0;t+=1)dt["a"].append(e.querySelector("tbody"),r.cloneNode(!0))},t._changeSelectionToTargetCell=function(e,t,n,r){var i,o="next"===n,a="row"===r;if(a?i=dt["a"].getSiblingRowCellByDirection(e,n,!1):(i=dt["a"].getTableCellByDirection(e,n),i||(i=dt["a"].getSiblingRowCellByDirection(e,n,!0))),i)a&&!o?this._moveToCursorEndOfCell(i,t):t.setStart(i,0),t.collapse(!0);else{var s=dt["a"].parents(e,"table");i=s[0],o?t.setStart(i.nextElementSibling,0):i.previousElementSibling&&"TABLE"!==i.previousElementSibling.nodeName?t.setStart(i.previousElementSibling,1):t.setStartBefore(i),t.collapse(!0)}},t._moveToCursorEndOfCell=function(e,t){var n;dt["a"].isListNode(e.lastChild)&&(n=dt["a"].getLastNodeBy(e.lastChild,(function(e){return"LI"!==e.nodeName||null!==e.nextSibling})));var r=dt["a"].getLastNodeBy(n||e,(function(e){return!dt["a"].isTextNode(e)})),i=r||n||e,o=r?r.length:i.childNodes.length-1;t.setStart(i,o)},t._moveCursorTo=function(e,t,n){var r,i=this.wwe.getEditor(),o=i.getSelection().cloneRange(),a=dt["a"].closest(o.startContainer,"td,th");if(o.collapsed&&this.wwe.isInTable(o)&&a){if("row"===t&&!this._isMovedCursorToRow(o,e))return r;"previous"!==e&&"row"!==t||et()(n)||n.preventDefault(),this._changeSelectionToTargetCell(a,o,e,t),i.setSelection(o),r=!1}return r},t._isMovedCursorToRow=function(e,t){var n=e.startContainer;return this._isInList(n)?this._isMovedCursorFromListToRow(n,t):this._isMovedCursorFromTextToRow(e,t)},t._isMovedCursorFromListToRow=function(e,t){var n=t+"Sibling",r=this._findListItem(e),i=dt["a"].getParentNodeBy(r,(function(e,t){var r=null===t[n]&&null===e[n];return!dt["a"].isCellNode(e)&&r})),o=dt["a"].isListNode(i)&&null===i[n];return dt["a"].isCellNode(i.parentNode)&&o},t._isMovedCursorFromTextToRow=function(e,t){var n=e.startContainer,r=e.startOffset,i=dt["a"].isCellNode(n)?n.childNodes[r]:n,o=dt["a"].getParentNodeBy(i,(function(e){return!dt["a"].isCellNode(e)&&!dt["a"].isTextNode(e)})),a=dt["a"].getSiblingNodeBy(o,t,(function(e){return null!==e&&"BR"!==e.nodeName}));return a&&null===a[t+"Sibling"]},t._removeContentsAndChangeSelectionIfNeed=function(e,t,n){var r=t.length<=1,i="BACK_SPACE"===t||"DELETE"===t,o=this.wwe.componentManager.getManager("tableSelection").getSelectedCells(),a=o[0],s=!1;return(r||i)&&!this._isModifierKeyPushed(n)&&o.length&&(i&&this._recordUndoStateIfNeed(e),this._removeTableContents(o),this._lastCellNode=a,e.setStart(a,0),e.collapse(!0),this.wwe.getEditor().setSelection(e),s=!0),s},t.getTableIDClassName=function(){var e=Ft+this.tableID;return this.tableID+=1,e},t.destroy=function(){var e=this;this.eventManager.removeEventHandler("wysiwygRangeChangeAfter.table"),this.eventManager.removeEventHandler("wysiwygSetValueAfter.table"),this.eventManager.removeEventHandler("wysiwygProcessHTMLText.table"),this.eventManager.removeEventHandler("cut.table"),this.eventManager.removeEventHandler("copyBefore.table"),i()(this.keyEventHandlers,(function(t,n){return e.wwe.removeKeyEventHandler(n,t)}))},e}();function Vt(e,t){for(var n="<br />",r="<"+t+">"+n+"</"+t+">",i="",o=0;o<e;o+=1)i+=r;return i}var zt=Wt,Kt="te-cell-selected",Gt=function(){function e(e){this.wwe=e,this.eventManager=e.eventManager,this.name="tableSelection",this._init()}var t=e.prototype;return t._init=function(){this._initEvent(),it.a.firefox&&(document.execCommand("enableObjectResizing",!1,"false"),document.execCommand("enableInlineTableEditing",!1,"false"))},t._initEvent=function(){var e,t,n,r=this;this._tableSelectionTimer=null,this._removeSelectionTimer=null,this._isSelectionStarted=!1;var i=function(i){t=dt["a"].closest(i.data.target,"[contenteditable=true] td,th");var o=r.wwe.getEditor().getSelection(),a=dt["a"].parents(t,"[contenteditable=true] table"),s=e===t,l=r._isTextSelect(o,s)&&!Bt()(e,Kt);r._isSelectionStarted&&a&&!l&&(window.getSelection().removeAllRanges(),it.a.firefox&&!r._removeSelectionTimer&&(r._removeSelectionTimer=setInterval((function(){window.getSelection().removeAllRanges()}),10)),r.highlightTableCellsBy(e,t),n=t)},o=function(){r._isSelectionStarted&&(r._isSelectionStarted=!1,r.eventManager.removeEventHandler("mouseover.tableSelection"),r.eventManager.removeEventHandler("mouseup.tableSelection"))},a=function(i){t=dt["a"].closest(i.data.target,"[contenteditable=true] td,th");var a=r.wwe.getEditor().getSelection(),s=e===t,l=r._isTextSelect(a,s)&&!Bt()(e,Kt);r._clearTableSelectionTimerIfNeed(),r._isSelectionStarted&&(l||r._isListSelect(a)?r.removeClassAttrbuteFromAllCellsIfNeed():(r.wwe.componentManager.getManager("table").resetLastCellNode(),t=t||n,a=r.wwe.getEditor().getSelection(),a.setStart(t,0),it.a.msie?a.setEnd(t,1):(a.setEnd(t,0),a.collapse(!1)),r.wwe.getEditor().setSelection(a)),r.onDragEnd&&r.onDragEnd()),o()},s=function(n){var s=2;e=dt["a"].closest(n.data.target,"[contenteditable=true] td,th");var l=!!e&&Bt()(e,Kt);t=null,!l||l&&n.data.button!==s?(r.removeClassAttrbuteFromAllCellsIfNeed(),e&&(r.setTableSelectionTimerIfNeed(e),r.eventManager.listen("mouseover.tableSelection",i),r.eventManager.listen("mouseup.tableSelection",a),r.onDragStart&&r.onDragStart(e))):n.data.button===s&&o()};this.eventManager.listen("mousedown.tableSelection",s),this.eventManager.listen("copyBefore.tableSelection",o),this.eventManager.listen("pasteBefore.tableSelection",o)},t._isTextSelect=function(e,t){return/TD|TH|TEXT/i.test(e.commonAncestorContainer.nodeName)&&t},t._isListSelect=function(e){return/UL|OL|LI/i.test(e.commonAncestorContainer.nodeName)},t.setTableSelectionTimerIfNeed=function(e){var t=dt["a"].parents(e,"[contenteditable=true] table").length;t&&(this._isSelectionStarted=!0)},t._clearTableSelectionTimerIfNeed=function(){clearTimeout(this._tableSelectionTimer),it.a.firefox&&this._removeSelectionTimer&&(clearTimeout(this._removeSelectionTimer),this._removeSelectionTimer=null)},t._reArrangeSelectionIfneed=function(e,t){var n,r=dt["a"].parents(e,"[contenteditable=true] table").length,i=dt["a"].parents(t,"[contenteditable=true] table").length,o=i&&!r,a=!i&&r;if(o){var s=dt["a"].parents(t,"[contenteditable=true] table");n=s[0];var l=n.querySelectorAll("th");e=l[0]}else if(a){var c=dt["a"].parents(e,"[contenteditable=true] table");n=c[0];var u=n.querySelectorAll("td");t=u[u.length-1]}return{startContainer:e,endContainer:t}},t._applySelectionDirection=function(e,t){var n=dt["a"].getNodeOffsetOfParent,r=e.startContainer,i=e.endContainer,o=n(dt["a"].closest(r,"[contenteditable=true] tr"))-n(dt["a"].closest(i,"[contenteditable=true] tr")),a=n(r)-n(i),s=0===o,l=o<0,c=a>0;return s?c?(t.setStart(i,0),t.setEnd(r,1)):(t.setStart(r,0),t.setEnd(i,1)):l?(t.setStart(r,0),t.setEnd(i,1)):(t.setStart(i,0),t.setEnd(r,1)),t},t.getSelectionRangeFromTable=function(e,t){var n,r,i=dt["a"].getNodeOffsetOfParent,o=i(e.parentNode),a=i(t.parentNode),s=i(e),l=i(t),c=dt["a"].getParentUntil(e,"TABLE"),u=dt["a"].getParentUntil(t,"TABLE"),d="TBODY"===dt["a"].getNodeName(c)&&"THEAD"===dt["a"].getNodeName(u),h=c!==u,f=!!dt["a"].parents(e,"tbody").length&&!!dt["a"].parents(t,"tbody").length,p={row:o,cell:s},g={row:a,cell:l};return d?p.row+=1:h?g.row+=1:f&&(p.row+=1,g.row+=1),o>a||o===a&&s>l?(n=g,r=p):(n=p,r=g),{from:n,to:r}},t.highlightTableCellsBy=function(e,t){var n=dt["a"].findAll(dt["a"].parents(e,"[contenteditable=true] table")[0],"tr"),r=this.getSelectionRangeFromTable(e,t),i=r.from.row,o=r.from.cell,a=r.to.row,s=r.to.cell;n.forEach((function(e,t){dt["a"].findAll(e,"td,th").forEach((function(e,n){var r=t===i,l=t===a;r&&n<o||l&&n>s||t<i||t>a?m()(e,Kt):p()(e,Kt)}))}))},t.removeClassAttrbuteFromAllCellsIfNeed=function(){var e=dt["a"].findAll(this.wwe.getBody(),"td."+Kt+",th."+Kt);e.forEach((function(e){m()(e,Kt),e.getAttribute("class")||e.removeAttribute("class")}))},t.getSelectedCells=function(){return this.wwe.getBody().querySelectorAll("."+Kt)},t.createRangeBySelectedCells=function(){var e=this.wwe.getEditor(),t=e.getSelection().cloneRange(),n=this.getSelectedCells(),r=n[0],i=n[n.length-1];n.length&&this.wwe.isInTable(t)&&(t.setStart(r,0),t.setEnd(i,i.childNodes.length),e.setSelection(t))},t.styleToSelectedCells=function(e,t){this.createRangeBySelectedCells(),e(this.wwe.getEditor(),t)},t.destroy=function(){this.eventManager.removeEventHandler("mousedown.tableSelection"),this.eventManager.removeEventHandler("mouseover.tableSelection"),this.eventManager.removeEventHandler("mouseup.tableSelection"),this.eventManager.removeEventHandler("copyBefore.tableSelection"),this.eventManager.removeEventHandler("pasteBefore.tableSelection")},e}(),$t=Gt,Yt=function(){function e(e){this.wwe=e,this.eventManager=e.eventManager,this.name="hr",this._init()}var t=e.prototype;return t._init=function(){this._initEvent()},t._initEvent=function(){var e=this;this.eventManager.listen("wysiwygSetValueAfter",(function(){e._insertEmptyLineIfNeed(),e._changeHRForWysiwyg()}))},t._insertEmptyLineIfNeed=function(){var e=this.wwe.getBody(),t=e.firstChild,n=e.lastChild;t&&"HR"===t.nodeName?e.insertBefore(dt["a"].createEmptyLine(),t):n&&"HR"===n.nodeName&&e.appendChild(dt["a"].createEmptyLine())},t._changeHRForWysiwyg=function(){var e=this.wwe.getBody();dt["a"].findAll(e,"hr").forEach((function(e){var t=e.parentNode;t.replaceChild(dt["a"].createHorizontalRule(),e)}))},e}(),Xt=Yt,Zt=function(){function e(e){this.wwe=e,this.eventManager=e.eventManager,this.name="p",this._initEvent()}var t=e.prototype;return t._initEvent=function(){var e=this;this.eventManager.listen("wysiwygSetValueBefore",(function(t){return e._splitPtagContentLines(t)})),this.eventManager.listen("wysiwygSetValueAfter",(function(){e._ensurePtagContentWrappedWithDiv(),e._unwrapPtags()}))},t._splitPtagContentLines=function(e){if(e){var t=dt["a"].createElementWith("<div>"+e+"</div>");dt["a"].findAll(t,"p").forEach((function(e){var t=e.attributes,n=e.nextElementSibling,r=e.innerHTML,i=r.split(/<br>/gi),o=i.length-1,a="";a=i.map((function(e,n){if(n>0&&n<o&&(e=e||"<br>"),e){var r=document.createElement("div");return Object.keys(t).forEach((function(e){var n=t[e],i=n.name,o=n.value;r.setAttribute(i,o)})),r.innerHTML=e,r.outerHTML}return""})),(n&&"P"===n.nodeName||"false"===e.getAttribute("contenteditable"))&&a.push("<div><br></div>"),dt["a"].replaceWith(e,a.join(""))})),e=t.innerHTML}return e},t._ensurePtagContentWrappedWithDiv=function(){var e=this;dt["a"].findAll(this.wwe.getBody(),"p").forEach((function(t){t.querySelectorAll("div").length||dt["a"].wrapInner(t,"div"),e._findNextParagraph(t,"p")&&dt["a"].append(t,"<div><br></div>")}))},t._unwrapPtags=function(){dt["a"].findAll(this.wwe.getBody(),"div").forEach((function(e){var t=e.parentNode;"P"===t.tagName&&dt["a"].unwrap(t)}))},t._findNextParagraph=function(e,t){var n=e.nextElementSibling;return t?n&&ft()(n,t)?n:null:n},e}(),Qt=Zt,Jt=/h[\d]/i,en=it.a.msie&&10===it.a.version,tn=function(){function e(e){this.wwe=e,this.eventManager=e.eventManager,this.name="heading",this._init()}var t=e.prototype;return t._init=function(){this._initEvent(),this._initKeyHandler()},t._initEvent=function(){var e=this;this.eventManager.listen("wysiwygSetValueAfter",(function(){e._wrapDefaultBlockToHeadingInner()}))},t._initKeyHandler=function(){var e=this;this.wwe.addKeyEventHandler("ENTER",(function(t,n){return!e.wwe.hasFormatWithRx(Jt)||(e._onEnter(t,n),!1)})),this.wwe.addKeyEventHandler("BACK_SPACE",(function(t,n){return!e.wwe.hasFormatWithRx(Jt)||(e._addBrToEmptyBlock(n),e._removePrevTopNodeIfNeed(t,n),!1)}))},t._wrapDefaultBlockToHeadingInner=function(){var e=dt["a"].findAll(this.wwe.getBody(),"h1, h2, h3, h4, h5, h6");e.forEach((function(e){var t=!dt["a"].children(e,"div, p").length;t&&dt["a"].wrapInner(e,"div")}))},t._unwrapHeading=function(){this.wwe.unwrapBlockTag((function(e){return Jt.test(e)}))},t._onEnter=function(e,t){var n=this;t.startOffset>0?this.wwe.defer((function(e){n._unwrapHeading(),e.getEditor().removeLastUndoStack()})):(e.preventDefault(),this._insertEmptyBlockToPrevious(t))},t._insertEmptyBlockToPrevious=function(e){this.wwe.getEditor().saveUndoState(e);var t=dt["a"].createElementWith("<div><br></div>");dt["a"].insertBefore(t,dt["a"].getParentUntil(e.startContainer,this.wwe.getBody()))},t._removePrevTopNodeIfNeed=function(e,t){var n=!1;if(t.collapsed&&0===t.startOffset){var r=t.startContainer,i=dt["a"].getTopPrevNodeUnder(r,this.wwe.getBody()),o=i&&0===i.textContent.length,a=this.wwe.getEditor();0===r.textContent.length?n=this._removeHedingAndChangeSelection(e,t,i):o&&(e.preventDefault(),a.saveUndoState(t),dt["a"].remove(i),n=!0)}return n},t._getHeadingElement=function(e){var t=Jt.test(dt["a"].getNodeName(e));return t?e:dt["a"].parents(e,"h1,h2,h3,h4,h5,h6")[0]},t._addBrToEmptyBlock=function(e){var t=e.collapsed,n=e.startOffset,r=e.startContainer;if(t&&1===n){var i=this._getHeadingElement(r),o=dt["a"].children(i.firstChild,"br");if(!en&&!o.length){var a=document.createElement("br");r.parentNode.appendChild(a)}}},t._removeHedingAndChangeSelection=function(e,t,n){var r=t.startContainer,i=this.wwe.getEditor(),o=this.wwe.getBody(),a=this._getHeadingElement(r),s=n,l=1;if(e.defaultPrevented||(e.preventDefault(),i.saveUndoState(t)),dt["a"].remove(a),!n){var c=dt["a"].children(o,"div");s=c[0],l=0}return t.setStart(s,l),t.collapse(!0),i.setSelection(t),!0},e}(),nn=tn,rn=n(42),on=n.n(rn),an=it.a.msie&&10===it.a.version,sn=an?"":"<br>",ln={"&":"&amp;","<":"&lt;",">":"&gt;"},cn=/\u200B/g,un="data-te-codeblock",dn=function(){function e(e){this.wwe=e,this.eventManager=e.eventManager,this.name="codeblock",this._init()}var t=e.prototype;return t._init=function(){this._initKeyHandler(),this._initEvent()},t._initKeyHandler=function(){var e=this;this._keyEventHandlers={BACK_SPACE:this._onBackspaceKeyEventHandler.bind(this),ENTER:function(t,n){!e.wwe.isInTable(n)&&e.wwe.getEditor().hasFormat("CODE")&&e.wwe.defer((function(){var t=e.wwe.getRange(),n=t.startContainer,r=e._getCodeNode(n);r&&!dt["a"].getTextLength(r)&&r.parentNode.removeChild(r)}))}},i()(this._keyEventHandlers,(function(t,n){return e.wwe.addKeyEventHandler(n,t)}))},t._getCodeNode=function(e){var t;return"CODE"===e.nodeName?t=e:"CODE"===e.parentNode.nodeName&&(t=e.parentNode),t},t._initEvent=function(){var e=this;this.eventManager.listen("wysiwygSetValueAfter.codeblock",(function(){e.modifyCodeBlockForWysiwyg()})),this.eventManager.listen("wysiwygProcessHTMLText.codeblock",(function(t){return e._changePreToPreCode(t)}))},t.prepareToPasteOnCodeblock=function(e){var t=this.wwe.getEditor().getDocument().createDocumentFragment(),n=this.convertNodesToText(e);return n=n.replace(/\n$/,""),t.appendChild(document.createTextNode(n)),t},t.convertNodesToText=function(e){var t="",n=e.shift();while(on()(n)){var r=n,i=r.childNodes;i&&dt["a"].isBlockNode(n)?t+=this.convertNodesToText(Xe()(n.childNodes)):"BR"===n.nodeName?t+="\n":t+=n.textContent,n=e.shift()}return t},t._copyCodeblockTypeFromRangeCodeblock=function(e,t){var n=dt["a"].getParentUntil(t.commonAncestorContainer,this.wwe.getBody());if("PRE"===dt["a"].getNodeName(n)){var r=n.attributes;i()(r,(function(t){e.setAttribute(t.name,t.value)}))}return e},t._changePreToPreCode=function(e){return e.replace(/<pre( .*?)?>((.|\n)*?)<\/pre>/g,(function(e,t,n){return"<pre><code"+(t||"")+">"+n+"</code></pre>"}))},t.modifyCodeBlockForWysiwyg=function(e){e||(e=this.wwe.getBody()),dt["a"].findAll(e,"pre").forEach((function(e){var t,n,r=e.querySelector("code");r&&(t=r.getAttribute("data-language"),n=r.getAttribute("data-backticks")),e.children.length>1&&Xe()(e.children).forEach((function(e){"DIV"!==e.nodeName&&"P"!==e.nodeName||e.querySelectorAll("br").length||(e.innerHTML+=e.innerHTML+"\n")}));var i=e.querySelectorAll("br");i.length&&dt["a"].replaceWith(i,"\n");var o=e.textContent.replace(/\s+$/,"");dt["a"].empty(e),e.innerHTML=o?hn(o):sn,t&&(e.setAttribute("data-language",t),p()(e,"lang-"+t)),n&&e.setAttribute("data-backticks",n),e.setAttribute(un,"")}))},t._onBackspaceKeyEventHandler=function(e,t){var n=!0,r=this.wwe.getEditor(),i=t.commonAncestorContainer;if(this._isCodeBlockFirstLine(t)&&!this._isFrontCodeblock(t))this._removeCodeblockFirstLine(i),t.collapse(!0),n=!1;else if(t.collapsed&&this._isEmptyLine(i)&&this._isBetweenSameCodeblocks(i)){var o=i.previousSibling,a=i.nextSibling,s=o.textContent.length;r.saveUndoState(t),i.parentNode.removeChild(i),this._mergeCodeblocks(o,a),t.setStart(o.childNodes[0],s),t.collapse(!0),n=!1}return n||(r.setSelection(t),e.preventDefault()),n},t._isEmptyLine=function(e){var t=e.nodeName,n=e.childNodes,r=an?""===e.textContent:1===n.length&&"BR"===n[0].nodeName;return"DIV"===t&&r},t._isBetweenSameCodeblocks=function(e){var t=e.previousSibling,n=e.nextSibling;return"PRE"===dt["a"].getNodeName(t)&&"PRE"===dt["a"].getNodeName(n)&&t.getAttribute("data-language")===n.getAttribute("data-language")},t._mergeCodeblocks=function(e,t){var n=t.textContent;e.childNodes[0].textContent+="\n"+n,t.parentNode.removeChild(t)},t._isCodeBlockFirstLine=function(e){return this.isInCodeBlock(e)&&e.collapsed&&0===e.startOffset},t._isFrontCodeblock=function(e){var t=dt["a"].getParentUntil(e.startContainer,this.wwe.getEditor().getRoot()),n=t.previousSibling;return n&&"PRE"===n.nodeName},t._removeCodeblockFirstLine=function(e){var t=this.wwe.getEditor(),n="PRE"===e.nodeName?e:e.parentNode,r=n.textContent.replace(cn,"");t.modifyBlocks((function(){var e=t.getDocument().createDocumentFragment(),i=r.split("\n"),o=document.createElement("div"),a=i.shift();if(o.innerHTML=""+hn(a)+sn,e.appendChild(o),i.length){var s=n.cloneNode();s.textContent=i.join("\n"),e.appendChild(s)}return e}))},t.isInCodeBlock=function(e){var t;return t=e.collapsed?e.startContainer:e.commonAncestorContainer,!!dt["a"].closest(t,"pre")},t.destroy=function(){var e=this;this.eventManager.removeEventHandler("wysiwygSetValueAfter.codeblock"),this.eventManager.removeEventHandler("wysiwygProcessHTMLText.codeblock"),i()(this._keyEventHandlers,(function(t,n){return e.wwe.removeKeyEventHandler(n,t)}))},e}();function hn(e){return e?e.replace(/[<>&]/g,(function(e){return ln[e]||e})):""}var fn=dn,pn=n(55),gn=n.n(pn);function mn(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var vn=/\b(H[\d]|LI|P|BLOCKQUOTE|TD)\b/,bn=/Trident\/[456]\./.test(navigator.userAgent),_n=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return t=e.call.apply(e,[this].concat(r))||this,t._decorateHandlerToCancelable("copy"),t._decorateHandlerToCancelable(bn?"beforecut":"cut"),t._decorateHandlerToCancelable(bn?"beforepaste":"paste"),t.getBody=function(){return t.body=t.body||t.getRoot(),t.body},t}mn(t,e);var n=t.prototype;return n._decorateHandlerToCancelable=function(e){var t=this._events[e];if(t.length>1)throw new Error("too many"+e+"handlers in squire");var n=t[0].bind(this);t[0]=function(e){e.defaultPrevented||e.squirePrevented||n(e)}},n.changeBlockFormat=function(e,t){var n=this;this.modifyBlocks((function(r){var i,o,a,s,l,c,u;if(r.childNodes.length?i=r.childNodes.item(0):(i=n.createDefaultBlock(),r.appendChild(i)),e){while(i.firstChild)i=i.firstChild;u=function(e){s.appendChild(e)};while(i!==r){var d=i;if(l=d.tagName,pe()(e)?e(l):l===e){s=i.childNodes.item(0),(!dt["a"].isElemNode(s)||i.childNodes.length>1)&&(s=n.createDefaultBlock(),Xe()(i.childNodes).forEach(u),c=s.lastChild,c&&"BR"===dt["a"].getNodeName(c)&&s.removeChild(c)),a=t?n.createElement(t,[s]):s,o=n.getDocument().createDocumentFragment(),o.appendChild(a),r=o;break}i=i.parentNode}}return o&&e||!t||"DIV"!==dt["a"].getNodeName(r.childNodes[0])||(r=n.createElement(t,[r.childNodes[0]])),r}))},n.changeBlockFormatTo=function(e){this.changeBlockFormat((function(e){return vn.test(e)}),e)},n.getCaretPosition=function(){return this.getCursorPosition()},n.replaceSelection=function(e,t){t&&this.setSelection(t),this._ignoreChange=!0,this.insertHTML(e)},n.replaceRelativeOffset=function(e,t,n){var r=this.getSelection().cloneRange();this._replaceRelativeOffsetOfSelection(e,t,n,r)},n._replaceRelativeOffsetOfSelection=function(e,t,n,r){var i,o,a,s=r.endContainer,l=r.endOffset;"TEXT"!==dt["a"].getNodeName(s)&&(s=this._getClosestTextNode(s,l),s&&(l=dt["a"].isTextNode(s)?s.nodeValue.length:s.textContent.length)),s?(i=this.getSelectionInfoByOffset(s,l+t),r.setStart(i.element,i.offset),a=l+(t+n),o=this.getSelectionInfoByOffset(s,a),r.setEnd(o.element,o.offset),this.replaceSelection(e,r)):this.replaceSelection(e)},n._getClosestTextNode=function(e,t){var n=dt["a"].getChildNodeByOffset(e,t-1);return"TEXT"!==dt["a"].getNodeName(n)&&(n=n.previousSibling),n},n.getSelectionInfoByOffset=function(e,t){var n,r,i,o,a=t>=0?"next":"previous",s=Math.abs(t),l=n;n="next"===a?e:e.previousSibling,i=s,o=0;while(n){if(r=dt["a"].isTextNode(n)?n.nodeValue.length:n.textContent.length,o+=r,s<=o)break;i-=r,dt["a"].getTextLength(n)>0&&(l=n),n=n[a+"Sibling"]}return n||(n=l,i=dt["a"].getTextLength(n)),"previous"===a&&(i=dt["a"].getTextLength(n)-i),{element:n,offset:i}},n.getSelectionPosition=function(e,t,n){var r=this.createElement("INPUT"),i=e.cloneRange(),o=this.getSelectionInfoByOffset(e.endContainer,e.endOffset+(n||0));i.setStart(i.startContainer,i.startOffset),i.setEnd(o.element,o.offset),this._ignoreChange=!0,this.insertElement(r,i);var a=dt["a"].getOffset(r);return"over"!==t&&(a.top+=r.offsetHeight),r.parentNode.removeChild(r),e.setStart(e.endContainer,e.endOffset),e.collapse(!0),this.setSelection(e),a},n.removeLastUndoStack=function(){this._undoStack.length&&(this._undoStackLength-=1,this._undoIndex-=1,this._undoStack.pop(),this._isInUndoState=!1)},n.replaceParent=function(e,t,n){var r=dt["a"].closest(e,t);r&&(dt["a"].wrapInner(r,n),dt["a"].unwrap(r))},n.preserveLastLine=function(){var e=this.getBody().children,t=e[e.length-1];t&&"DIV"!==dt["a"].getNodeName(t)&&(this._ignoreChange=!0,dt["a"].insertAfter(this.createDefaultBlock(),t))},n.scrollTop=function(e){return et()(e)||(this.getBody().scrollTop=e),this.getBody().scrollTop},n.isIgnoreChange=function(){return this._ignoreChange},n.focus=function(){gn.a.prototype.focus.call(this)},n.blockCommandShortcuts=function(){var e=this,t=y["b"]?"meta":"ctrl",n=["b","i","u","shift-7","shift-5","shift-6","shift-8","shift-9","[","]","d"];n.forEach((function(n){e.setKeyHandler(t+"-"+n,(function(e,t){t.preventDefault()}))}))},t}(gn.a),yn=_n,Cn=it.a.msie&&11===it.a.version,wn=-1!==navigator.appVersion.indexOf("Win")&&it.a.chrome,En=/Windows (NT )?10/g.test(navigator.appVersion),Tn=Cn||wn&&!En,Nn=function(){function e(e,t){this._wwe=e,Tn&&(this.isComposition=!1,this._initCompositionEvent()),this.setRange(t||this._wwe.getRange())}var t=e.prototype;return t._initCompositionEvent=function(){var e=this;this._wwe.getEditor().addEventListener("compositionstart",(function(){e.isComposition=!0})),this._wwe.getEditor().addEventListener("compositionend",(function(){e.isComposition=!1}))},t.setRange=function(e){this._range&&this._range.detach(),this._range=e},t.expandStartOffset=function(){var e=this._range;dt["a"].isTextNode(e.startContainer)&&e.startOffset>0&&e.setStart(e.startContainer,e.startOffset-1)},t.expandEndOffset=function(){var e=this._range;dt["a"].isTextNode(e.endContainer)&&e.endOffset<e.endContainer.nodeValue.length&&e.setEnd(e.endContainer,e.endOffset+1)},t.setEndBeforeRange=function(e){var t=e.startOffset;this.isComposition&&(t+=1),this._range.setEnd(e.startContainer,t)},t.getTextContent=function(){return this._range.cloneContents().textContent},t.replaceContent=function(e){this._wwe.getEditor().setSelection(this._range),this._wwe.getEditor().insertHTML(e),this._wwe.isInTable(this._range)&&this._wwe.eventManager.emit("wysiwygRangeChangeAfter",this._wwe),this._range=this._wwe.getRange()},t.deleteContent=function(){this._wwe.getEditor().setSelection(this._range),this._wwe.getEditor().insertHTML(""),this._range=this._wwe.getRange()},t.peekStartBeforeOffset=function(e){var t=this._range.cloneRange();return t.setStart(t.startContainer,Math.max(t.startOffset-e,0)),t.setEnd(this._range.startContainer,this._range.startOffset),t.cloneContents().textContent},e}(),kn=Nn,Sn=function(){function e(e){var t=e.eventManager,n=e.container,r=e.attachedSelector;this._eventManager=t,this._attachedSelector="[contenteditable=true] "+r,this._container=n,this._attachedElement=null,this.active=!1,this._createElement(),this._initEvent()}var t=e.prototype;return t._createElement=function(){this.el=dt["a"].createElementWith('<div class="te-ww-block-overlay"></div>'),h()(this.el,{position:"absolute",display:"none",zIndex:1}),dt["a"].append(this._container,this.el)},t._initEvent=function(){var e=this;this._eventManager.listen("change",this._onChange.bind(this)),this._eventManager.listen("mouseover",this._onMouseOver.bind(this)),this._eventManager.listen("focus",(function(){e.setVisibility(!1)})),this._eventManager.listen("mousedown",(function(){e.setVisibility(!1)}))},t._onChange=function(){this._attachedElement&&dt["a"].isContain(document.body,this._attachedElement)?this.syncLayout():this.setVisibility(!1)},t._onMouseOver=function(e){var t=e.data,n=t.target,r=dt["a"].closest(n,this._attachedSelector);r?(this._attachedElement=r,this.setVisibility(!0)):dt["a"].closest(n,this.el)?this.setVisibility(!0):this.active||this.setVisibility(!1)},t.syncLayout=function(){var e=dt["a"].getOffset(this._attachedElement),t=dt["a"].getOuterWidth(this._attachedElement),n=dt["a"].getOuterHeight(this._attachedElement);dt["a"].setOffset(this.el,e),h()(this.el,{width:t+"px"}),h()(this.el,{height:n+"px"})},t.getAttachedElement=function(){return this._attachedElement||null},t.getVisibility=function(){return"block"===this.el.style.display},t.setVisibility=function(e){e&&this._attachedElement?this.getVisibility()||(h()(this.el,{display:"block"}),this.syncLayout(),this.onShow()):e||this.getVisibility()&&(h()(this.el,{display:"none"}),this.onHide())},t.onShow=function(){},t.onHide=function(){this.active=!1,this._attachedElement=null},e}(),xn=Sn;function Ln(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Mn=26,An=250,Bn=30,On=function(e){function t(t){var n,r=t.eventManager,i=t.container,o=t.wysiwygEditor;return n=e.call(this,{eventManager:r,container:i,attachedSelector:"pre"})||this,n._wysiwygEditor=o,n._popupCodeBlockLanguages=null,n._initDOM(),n._initDOMEvent(),n}Ln(t,e);var n=t.prototype;return n._initDOM=function(){var e=this;p()(this.el,"code-block-header"),this._languageLabel=dt["a"].createElementWith("<span>text</span>"),dt["a"].append(this.el,this._languageLabel),this._buttonOpenModalEditor=dt["a"].createElementWith('<button type="button">Editor</button>'),dt["a"].append(this.el,this._buttonOpenModalEditor),this._eventManager.emit("removeEditor",(function(){ut()(e._buttonOpenModalEditor,"click"),e._buttonOpenModalEditor=null}))},n._initDOMEvent=function(){var e=this;lt()(this._buttonOpenModalEditor,"click",(function(){return e._openPopupCodeBlockEditor()}))},n._openPopupCodeBlockEditor=function(){this._eventManager.emit("openPopupCodeBlockEditor",this.getAttachedElement())},n._updateLanguage=function(){var e=this.getAttachedElement(),t=e?e.getAttribute("data-language"):null;this._languageLabel.textContent=t||"text"},n.syncLayout=function(){var e=this.getAttachedElement(),t=dt["a"].getOffset(e,".te-editor"),n=t.top;h()(this.el,{top:n+"px",right:Mn+"px",width:An+"px",height:Bn+"px"})},n.onShow=function(){var t=this;e.prototype.onShow.call(this),this._onAttachedElementChange=function(){return t._updateLanguage()},this._eventManager.listen("changeLanguage",this._onAttachedElementChange),this._updateLanguage()},n.onHide=function(){this._eventManager.removeEventHandler("changeLanguage",this._onAttachedElementChange),e.prototype.onHide.call(this)},t}(xn),Dn=On,Rn=G.getSharedInstance(),In=/<([a-z]+|h\d)>(<br>|<br \/>)<\/\1>/gi,Pn=/(?:<br>|<br \/>)<\/(.+?)>/gi,Hn=/\b(H[\d]|LI|P|BLOCKQUOTE|TD|PRE)\b/,Fn=/<span([^>]*)>[\u0020]/g,Un=/[\u0020]<\/span>/g,qn=/^(TABLE|H[1-6])$/,jn="tui-editor-contents",Wn="tui-editor-contents-placeholder",Vn="undefined"!==typeof MutationObserver,zn=function(){function e(e,t,n){var r=this;void 0===n&&(n={}),this.componentManager=new ce(this),this.eventManager=t,this.editorContainerEl=e,this._height=0,this._silentChange=!1,this._keyEventHandlers={},this._managers={},this._linkAttribute=n.linkAttribute||{},this._sanitizer=n.sanitizer,this._initEvent(),this._initDefaultKeyEventHandler(),this.debouncedPostProcessForChange=at()((function(){return r.postProcessForChange()}),0)}var t=e.prototype;return t.init=function(){var e=document.createElement("div");this.editorContainerEl.appendChild(e),this.editor=new yn(e,{blockTag:"DIV",leafNodeNames:{HR:!1},allowedBlocks:this._sanitizer?[]:["details","summary"]}),this.editor.blockCommandShortcuts(),this._clipboardManager=new Et(this),this._initSquireEvent(),this._clipboardManager.init(),p()(this.getBody(),jn),h()(this.editorContainerEl,"position","relative"),this._togglePlaceholder(),this.codeBlockGadget=new Dn({eventManager:this.eventManager,container:this.editorContainerEl,wysiwygEditor:this})},t._initEvent=function(){var e=this;this.eventManager.listen("wysiwygKeyEvent",(function(t){return e._runKeyEventHandlers(t.data,t.keyMap)})),this.eventManager.listen("wysiwygRangeChangeAfter",(function(){return e.scrollIntoCursor()})),this.eventManager.listen("contentChangedFromWysiwyg",(function(){e._togglePlaceholder()}))},t.addKeyEventHandler=function(e,t){var n=this;t||(t=e,e="DEFAULT"),nt()(e)||(e=[e]),e.forEach((function(e){n._keyEventHandlers[e]||(n._keyEventHandlers[e]=[]),n._keyEventHandlers[e].push(t)}))},t.removeKeyEventHandler=function(e,t){t||(t=e,e="DEFAULT");var n=this._keyEventHandlers[e];n&&(this._keyEventHandlers[e]=n.filter((function(e){return e!==t})))},t._runKeyEventHandlers=function(e,t){var n,r,i=this.getRange();n=this._keyEventHandlers.DEFAULT,n&&Qe()(n,(function(n){return r=n(e,i,t),r})),n=this._keyEventHandlers[t],n&&!1!==r&&Qe()(n,(function(n){return n(e,i,t)}))},t._initSquireEvent=function(){var e=this,t=this.getEditor(),n=!1;t.addEventListener("copy",(function(t){e.eventManager.emit("copy",{source:"wysiwyg",data:t}),at()((function(){e.isEditorValid()&&e.eventManager.emit("copyAfter",{source:"wysiwyg",data:t})}))()})),t.addEventListener(it.a.msie?"beforecut":"cut",(function(t){e.eventManager.emit("cut",{source:"wysiwyg",data:t}),at()((function(){e.isEditorValid()&&e.eventManager.emit("cutAfter",{source:"wysiwyg",data:t})}))()})),t.addEventListener(it.a.msie?"beforepaste":"paste",(function(t){e.eventManager.emit("paste",{source:"wysiwyg",data:t})})),t.addEventListener("dragover",(function(e){return e.preventDefault(),!1})),t.addEventListener("drop",(function(t){return t.preventDefault(),e.eventManager.emit("drop",{source:"wysiwyg",data:t}),!1})),t.addEventListener("input",at()((function(){if(e.isEditorValid()){if(e._silentChange)e._silentChange=!1;else{var t={source:"wysiwyg"};e.eventManager.emit("changeFromWysiwyg",t),e.eventManager.emit("change",t),e.eventManager.emit("contentChangedFromWysiwyg",e)}e.getEditor().preserveLastLine()}}),0)),t.addEventListener("keydown",(function(t){var r=e.getEditor().getSelection();r.collapsed||(n=!0),e.eventManager.emit("keydown",{source:"wysiwyg",data:t}),e._onKeyDown(t)})),it.a.firefox&&(t.addEventListener("keypress",(function(t){var r=t.keyCode;if(13===r||9===r){var i=e.getEditor().getSelection();i.collapsed||(n=!0),e.eventManager.emit("keydown",{source:"wysiwyg",data:t}),e._onKeyDown(t)}})),t.addEventListener("keyup",(function(){var t=e.getRange();if(dt["a"].isTextNode(t.commonAncestorContainer)&&dt["a"].isTextNode(t.commonAncestorContainer.previousSibling)){var n=t.commonAncestorContainer.previousSibling.length,r=t.commonAncestorContainer;t.commonAncestorContainer.previousSibling.appendData(t.commonAncestorContainer.data),t.setStart(t.commonAncestorContainer.previousSibling,n+t.startOffset),t.collapse(!0),dt["a"].remove(r),e.setRange(t),t.detach()}}))),t.addEventListener("keyup",(function(t){n&&(e.debouncedPostProcessForChange(),n=!1),e.eventManager.emit("keyup",{source:"wysiwyg",data:t})})),lt()(this.editorContainerEl,"scroll",(function(t){e.eventManager.emit("scroll",{source:"wysiwyg",data:t})})),t.addEventListener("click",(function(t){e.eventManager.emit("click",{source:"wysiwyg",data:t})})),t.addEventListener("mousedown",(function(t){e.eventManager.emit("mousedown",{source:"wysiwyg",data:t})})),t.addEventListener("mouseover",(function(t){e.eventManager.emit("mouseover",{source:"wysiwyg",data:t})})),t.addEventListener("mouseout",(function(t){e.eventManager.emit("mouseout",{source:"wysiwyg",data:t})})),t.addEventListener("mouseup",(function(t){e.eventManager.emit("mouseup",{source:"wysiwyg",data:t})})),t.addEventListener("contextmenu",(function(t){e.eventManager.emit("contextmenu",{source:"wysiwyg",data:t})})),t.addEventListener("focus",(function(){e.eventManager.emit("focus",{source:"wysiwyg"})})),t.addEventListener("blur",(function(){e.fixIMERange(),e.eventManager.emit("blur",{source:"wysiwyg"})})),t.addEventListener("pathChange",(function(t){var n={strong:/(^B>|>B$|>B>|^B$|STRONG)/.test(t.path),emph:/(>I|>EM|^I$|^EM$)/.test(t.path),strike:/(^S>|>S$|>S>|^S$|DEL)/.test(t.path),code:/CODE/.test(t.path),codeBlock:/PRE/.test(t.path),blockQuote:/BLOCKQUOTE/.test(t.path),table:/TABLE/.test(t.path),heading:/H[1-6]/.test(t.path),list:/UL>LI(?!.task-list-item)/.test(t.path),orderedList:/OL>LI(?!.task-list-item)/.test(t.path),taskList:/[UL|OL]>LI.task-list-item/.test(t.path),source:"wysiwyg"};e.eventManager.emit("stateChange",n)})),t.addEventListener("willPaste",(function(t){t.fragment&&e.eventManager.emit("willPaste",{source:"wysiwyg",data:t})}))},t._togglePlaceholder=function(){var e=this.getEditor();e.modifyDocument((function(){var t=e.getRoot();t.textContent||t.childNodes.length>1?t.classList.remove(Wn):t.classList.add(Wn)}))},t._onKeyDown=function(e){var t=Rn.convert(e);e.keyCode&&(this.eventManager.emit("keyMap",{source:"wysiwyg",keyMap:t,data:e}),e.defaultPrevented||this.eventManager.emit("wysiwygKeyEvent",{keyMap:t,data:e}))},t._initDefaultKeyEventHandler=function(){var e=this;this.addKeyEventHandler("ENTER",(function(t,n){e._isInOrphanText(n)&&e.defer((function(){e._wrapDefaultBlockToOrphanTexts(),e.breakToNewDefaultBlock(n,"before")})),e.defer((function(){return e.scrollIntoCursor()}))})),this.addKeyEventHandler("TAB",(function(t){var n=e.getEditor(),r=n.getSelection(),i=r.collapsed&&e._isCursorNotInRestrictedAreaOfTabAction(n),o=!r.collapsed&&dt["a"].isTextNode(r.commonAncestorContainer);return t.preventDefault(),!i&&!o||(n.insertPlainText("    "),!1)})),this.addKeyEventHandler("BACK_SPACE",(function(t,n,r){return e._handleRemoveKeyEvent(t,n,r)})),this.addKeyEventHandler("DELETE",(function(t,n,r){return e._handleRemoveKeyEvent(t,n,r)}))},t._handleRemoveKeyEvent=function(e,t,n){var r=this.getEditor();if(this._isStartHeadingOrTableAndContainsThem(t)){var i="BACK_SPACE"===n?"backspace":"delete";return r.removeAllFormatting(),r._keyHandlers[i](r,e,r.getSelection()),r.removeLastUndoStack(),!1}return!0},t._isStartHeadingOrTableAndContainsThem=function(e){var t=e.startContainer,n=e.startOffset,r=e.commonAncestorContainer,i=e.collapsed,o=this.getEditor().getRoot(),a=!1;return i||r!==o||(t===o?a=qn.test(dt["a"].getChildNodeByOffset(t,n).nodeName):0===n&&(a=qn.test(dt["a"].getParentUntil(t,o).nodeName))),a},t._wrapDefaultBlockToOrphanTexts=function(){var e=Xe()(this.getBody().childNodes).filter((function(e){return dt["a"].isTextNode(e)}));dt["a"].getAllTextNode(this.getBody()),e.forEach((function(e){e.nextSibling&&"BR"===e.nextSibling.tagName&&dt["a"].remove(e.nextSibling),dt["a"].wrap(e,document.createElement("div"))}))},t._isInOrphanText=function(e){return e.startContainer.nodeType===Node.TEXT_NODE&&e.startContainer.parentNode===this.getBody()},t._wrapDefaultBlockTo=function(e){this.saveSelection(e),this._joinSplitedTextNodes(),this.restoreSavedSelection(),e=this.getRange();var t=e.startContainer,n=e.startOffset,r=this.getEditor().createDefaultBlock([e.startContainer]),i=dt["a"].getChildNodeByOffset(e.startContainer,e.startOffset);i?e.setStartBefore(i):e.selectNodeContents(e.startContainer),e.collapse(!0),e.insertNode(r),e.setStart(t,n),e.collapse(!0),this.setRange(e)},t._joinSplitedTextNodes=function(){var e,t,n=[],r=Xe()(this.getBody().childNodes).filter((function(e){return dt["a"].isTextNode(e)}));r.forEach((function(r){e===r.previousSibling?(t.nodeValue+=r.nodeValue,n.push(r)):t=r,e=r})),dt["a"].remove(n)},t.saveSelection=function(e){e||(e=this.getRange()),this.getEditor()._saveRangeToBookmark(e)},t.setSelectionByContainerAndOffset=function(e,t,n,r){var i=this.getEditor(),o=i.getSelection();return o.setStart(e,t),o.setEnd(n,r),i.setSelection(o),o},t.restoreSavedSelection=function(){this.setRange(this.getEditor()._getRangeAndRemoveBookmark())},t.reset=function(){this.setValue("")},t.changeBlockFormatTo=function(e){this.getEditor().changeBlockFormatTo(e),this.eventManager.emit("wysiwygRangeChangeAfter",this)},t.makeEmptyBlockCurrentSelection=function(){var e=this;this.getEditor().modifyBlocks((function(t){return t.textContent||(t=e.getEditor().createDefaultBlock()),t}))},t.focus=function(){var e=this.scrollTop();this.editor.focus(),e!==this.scrollTop()&&this.scrollTop(e)},t.blur=function(){this.editor.blur()},t.remove=function(){ut()(this.editorContainerEl,"scroll"),this.getEditor().destroy(),this.editor=null,this.body=null,this.eventManager=null},t.setHeight=function(e){this._height=e,h()(this.editorContainerEl,{overflow:"auto",height:"100%"}),h()(this.editorContainerEl.parentNode,{height:l()(e)?e+"px":e});var t=this.editorContainerEl.style,n=this.getBody().style,r=parseInt(t.paddingTop,10)-parseInt(t.paddingBottom,10),i=parseInt(n.marginTop,10)-parseInt(n.marginBottom,10);h()(this.getBody(),{minHeight:e-i-r+"px"})},t.setMinHeight=function(e){var t=this.getBody();h()(t,"minHeight",e+"px")},t.setPlaceholder=function(e){e&&this.getEditor().getRoot().setAttribute("data-placeholder",e)},t.getLinkAttribute=function(){return this._linkAttribute},t.setValue=function(e,t){void 0===t&&(t=!0),e=this.eventManager.emitReduce("wysiwygSetValueBefore",e),this.editor.setHTML(e),this.eventManager.emit("wysiwygSetValueAfter",this),this.eventManager.emit("contentChangedFromWysiwyg",this),t&&this.moveCursorToEnd(),this.getEditor().preserveLastLine(),this.getEditor().removeLastUndoStack(),this.getEditor().saveUndoState()},t.insertText=function(e){this.editor.insertPlainText(e)},t.getValue=function(){this._prepareGetHTML();var e=this.editor.getHTML();return e=e.replace(In,(function(e,t){var n;return n="li"===t?e:"td"===t||"th"===t?"<"+t+"></"+t+">":"<br />",n})),e=e.replace(Fn,"<span$1>&nbsp;"),e=e.replace(Un,"&nbsp;</span>"),e=e.replace(Pn,"</$1>"),e=e.replace(/<div[^>]*>/g,""),e=e.replace(/<\/div>/g,"<br />"),e=this.eventManager.emitReduce("wysiwygProcessHTMLText",e),e},t._prepareGetHTML=function(){var e=this;this.getEditor().modifyDocument((function(){e._joinSplitedTextNodes(),e.eventManager.emit("wysiwygGetValueBefore",e)}))},t.postProcessForChange=function(){var e=this;this.isEditorValid()&&this.getEditor().modifyDocument((function(){e.eventManager.emit("wysiwygRangeChangeAfter",e)}))},t.readySilentChange=function(){Vn&&!this.getEditor().isIgnoreChange()&&(this._silentChange=!0)},t.getEditor=function(){return this.editor},t.replaceSelection=function(e,t){this.getEditor().replaceSelection(e,t)},t.replaceRelativeOffset=function(e,t,n){this.getEditor().replaceRelativeOffset(e,t,n)},t.addWidget=function(e,t,n,r){var i=this.getEditor().getSelectionPosition(e,n,r),o=dt["a"].getOffset(this.editorContainerEl);this.editorContainerEl.appendChild(t),h()(t,{position:"absolute",top:i.top-o.top+this.scrollTop()+"px",left:i.left-o.left+"px"})},t.getBody=function(){return this.getEditor().getBody()},t.hasFormatWithRx=function(e){return this.getEditor().getPath().match(e)},t.breakToNewDefaultBlock=function(e,t){var n=this.editor.createDefaultBlock(),r=dt["a"].getChildNodeByOffset(e.startContainer,e.startOffset)||dt["a"].getChildNodeByOffset(e.startContainer,e.startOffset-1),i=dt["a"].getParentUntil(r,this.getBody());"before"===t?dt["a"].insertBefore(n,i):dt["a"].insertAfter(n,i),e.setStart(n,0),e.collapse(!0),this.setRange(e)},t.replaceContentText=function(e,t,n){var r=e.innerHTML;e.innerHTML=r.replace(t,n)},t.unwrapBlockTag=function(e){e||(e=function(e){return Hn.test(e)}),this.getEditor().changeBlockFormat(e),this.eventManager.emit("wysiwygRangeChangeAfter",this)},t.scrollIntoCursor=function(){var e=this.scrollTop(),t=this.getEditor().getCursorPosition(),n=t.top,r=t.height,i=this.editorContainerEl.getBoundingClientRect(),o=i.top,a=i.height,s=n-o,l=n+r-(o+a);s<0?this.scrollTop(e+s):l>0&&this.scrollTop(Math.ceil(e+l))},t.moveCursorToEnd=function(){this.getEditor().moveCursorToEnd(),this.scrollIntoCursor(),this._correctRangeAfterMoveCursor("end")},t.moveCursorToStart=function(){this.getEditor().moveCursorToStart(),this.scrollTop(0)},t.scrollTop=function(e){return et()(e)||(this.editorContainerEl.scrollTop=e),this.editorContainerEl.scrollTop},t._correctRangeAfterMoveCursor=function(e){var t=this.getRange(),n=this.getBody();if("start"===e)while(n.firstChild)n=n.firstChild;else while(n.lastChild)n=n.lastChild;"BR"===n.tagName?t.setStartBefore(n):t.setStartAfter(n),t.collapse(!0),this.setRange(t)},t.getRange=function(){return this.getEditor().getSelection().cloneRange()},t.getIMERange=function(){var e,t=getSelection();return t&&t.rangeCount&&(e=t.getRangeAt(0).cloneRange()),e},t.fixIMERange=function(){var e=this.getIMERange();if(e){var t=dt["a"].getParentUntil(e.commonAncestorContainer,this.editorContainerEl),n=!(!t||!t.parentNode);n&&this.setRange(e)}},t.setRange=function(e){this.getEditor().setSelection(e)},t.isInTable=function(e){var t=e.collapsed?e.startContainer:e.commonAncestorContainer;return!!dt["a"].closest(t,"[contenteditable=true] table")},t.getTextObject=function(e){return new kn(this,e)},t.defer=function(e,t){var n=this,r=t||0;setTimeout((function(){n.isEditorValid()&&e(n)}),r)},t.isEditorValid=function(){return this.getEditor()&&dt["a"].isContain(document.body,this.editorContainerEl)},t._isCursorNotInRestrictedAreaOfTabAction=function(e){return!e.hasFormat("li")&&!e.hasFormat("blockquote")&&!e.hasFormat("table")},t.getSanitizer=function(){return this._sanitizer},e.factory=function(t,n,r){var i=new e(t,n,r);return i.init(),i.componentManager.addManager(Mt),i.componentManager.addManager(Pt),i.componentManager.addManager($t),i.componentManager.addManager(zt),i.componentManager.addManager(Xt),i.componentManager.addManager(Qt),i.componentManager.addManager(nn),i.componentManager.addManager(fn),i},e}(),Kn=zn,Gn=['<div class="tui-editor">','<div class="te-md-container">','<div class="te-editor"></div>','<div class="te-md-splitter"></div>','<div class="te-preview"></div>',"</div>",'<div class="te-ww-container">','<div class="te-editor"></div>',"</div>","</div>"].join(""),$n=function(){function e(e,t){this.el=e.el,this.height=e.height,this.type=e.initialEditType,this.eventManager=t,this.init(),this._initEvent()}var t=e.prototype;return t.init=function(){this._renderLayout(),this._initMarkdownAndPreviewSection(),this._initWysiwygSection()},t._initEvent=function(){this.eventManager.listen("hide",this.hide.bind(this)),this.eventManager.listen("show",this.show.bind(this))},t._renderLayout=function(){h()(this.el,{boxSizing:"border-box"}),this.containerEl=dt["a"].createElementWith(Gn,this.el)},t.switchToWYSIWYG=function(){m()(this.containerEl,"te-md-mode"),p()(this.containerEl,"te-ww-mode")},t.switchToMarkdown=function(){m()(this.containerEl,"te-ww-mode"),p()(this.containerEl,"te-md-mode")},t._initMarkdownAndPreviewSection=function(){this.mdEditorContainerEl=this.containerEl.querySelector(".te-md-container .te-editor"),this.previewEl=this.containerEl.querySelector(".te-md-container .te-preview")},t._initWysiwygSection=function(){this.wwEditorContainerEl=this.containerEl.querySelector(".te-ww-container .te-editor")},t._verticalSplitStyle=function(){var e=this.containerEl.querySelector(".te-md-container");m()(e,"te-preview-style-tab"),p()(e,"te-preview-style-vertical")},t._tabStyle=function(){var e=this.containerEl.querySelector(".te-md-container");m()(e,"te-preview-style-vertical"),p()(e,"te-preview-style-tab")},t.changePreviewStyle=function(e){"tab"===e?this._tabStyle():"vertical"===e&&this._verticalSplitStyle()},t.hide=function(){p()(this.el.querySelector(".tui-editor"),"te-hide")},t.show=function(){m()(this.el.querySelector(".tui-editor"),"te-hide")},t.remove=function(){dt["a"].remove(this.el.querySelector(".tui-editor"))},t.getEditorEl=function(){return this.containerEl},t.getPreviewEl=function(){return this.previewEl},t.getMdEditorContainerEl=function(){return this.mdEditorContainerEl},t.getWwEditorContainerEl=function(){return this.wwEditorContainerEl},e}(),Yn=$n,Xn=n(36),Zn=n(1),Qn=n(14),Jn=n.n(Qn),er=/^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})(\/([^\s]*))?$/g,tr=function(){function e(e){this.eventManager=e,this._initEvent(),this._initDefaultImageImporter()}e.decodeURIGraceful=function(e){var t,n=e.split(" "),r=[];return Qe()(n,(function(e){try{t=decodeURIComponent(e),t=t.replace(/ /g,"%20")}catch(n){t=e}return r.push(t)})),r.join(" ")},e.encodeMarkdownCharacters=function(e){return e.replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\[/g,"%5B").replace(/\]/g,"%5D").replace(/</g,"%3C").replace(/>/g,"%3E")},e.escapeMarkdownCharacters=function(e){return e.replace(/\(/g,"\\(").replace(/\)/g,"\\)").replace(/\[/g,"\\[").replace(/\]/g,"\\]").replace(/</g,"\\<").replace(/>/g,"\\>")};var t=e.prototype;return t._initEvent=function(){var e=this;this.eventManager.listen("drop",(function(t){var n=t.data.dataTransfer&&t.data.dataTransfer.files;e._processBlobItems(n,t.data)})),this.eventManager.listen("willPaste",(function(t){var n=t.data.fragment,r=n.querySelectorAll("*");if(1===r.length&&"IMG"===r[0].tagName&&/^data:image/.test(r[0].src)){t.data.preventDefault();var i=nr(r[0].src);e._emitAddImageBlobHook(i,"paste")}})),this.eventManager.listen("paste",(function(t){e._processClipboard(t.data)})),this.eventManager.listen("pasteBefore",(function(t){e._decodeURL(t)}))},t._initDefaultImageImporter=function(){this.eventManager.listen("addImageBlobHook",(function(e,t){var n=new FileReader;n.onload=function(e){t(e.target.result)},n.readAsDataURL(e)}))},t._emitAddImageBlobHook=function(e,t){var n=this;this.eventManager.emit("addImageBlobHook",e,(function(t,r){n.eventManager.emit("command","AddImage",{imageUrl:t,altText:r||e.name||"image"})}),t)},t._decodeURL=function(t){var n=e.decodeURIGraceful,r=e.encodeMarkdownCharacters;if("markdown"===t.source&&t.data.text){var i=t.data.text,o=i[0];1===i.length&&o.match(er)&&(o=n(o),o=r(o),t.data.update(null,null,[o]))}else if("wysiwyg"===t.source){var a=t.clipboardContainer,s=a.childNodes,l=s[0],c=l.innerText;1===a.childNodes.length&&"A"===l.tagName&&c.match(er)&&(l.innerText=n(c),l.href=r(l.href))}},t._processClipboard=function(e){var t=e.clipboardData||window.clipboardData,n=t&&t.items,r=t.types;n&&r&&1===r.length&&-1!==Jn()("Files",[].slice.call(r))&&this._processBlobItems(n,e)},t._processBlobItems=function(e,t){var n=this;e&&Qe()(e,(function(e){if(-1!==e.type.indexOf("image")){t.preventDefault(),t.stopPropagation(),t.codemirrorIgnore=!0;var r=e.name?e:e.getAsFile();return n._emitAddImageBlobHook(r,t.type),!1}return!0}))},e}();function nr(e){for(var t=atob(e.split(",")[1]),n=new ArrayBuffer(t.length),r=new Uint8Array(n),i=0;i<t.length;i+=1)r[i]=t.charCodeAt(i);var o=e.split(","),a=o[0],s=new Blob([n],{type:a.split(":")[1].split(";")[0]});return s}var rr=tr,ir=n(37),or=n(46),ar=n(23),sr="en-US",lr=function(){function e(){this._code=sr,this._langs=new ar["a"]}var t=e.prototype;return t.setCode=function(e){this._code=e},t.setLanguage=function(e,t){var n=this;e=[].concat(e),e.forEach((function(e){if(n._langs.has(e)){var r=n._langs.get(e);n._langs.set(e,u()(r,t))}else n._langs.set(e,t)}))},t.get=function(e,t){t||(t=this._code);var n=this._langs.get(t);n||(n=this._langs.get(sr));var r=n[e];if(!r)throw new Error('There is no text key "'+e+'" in '+t);return r},e}(),cr=new lr,ur=n(57),dr=n(9),hr=n.n(dr),fr=n(54),pr=n.n(fr),gr=n(58),mr=n.n(gr);function vr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var br=["click","mousedown","mousemove","mouseup","mouseover","mouseout","scroll"],_r=-1;function yr(){return _r+=1,_r}var Cr=function(){function e(e){void 0===e&&(e={}),vr(this,"tagName",void 0),vr(this,"className",void 0),vr(this,"el",void 0),vr(this,"_id",void 0),e=u()({tagName:"div"},e),this.tagName=e.tagName,this.className=e.className,this._id=yr(),this.customEventManager=new mr.a,this._setRootElement(e.rootElement)}var t=e.prototype;return t.on=function(e,t){var n=this;pr()(e)?i()(e,(function(e,t){n._addEvent(t,e)})):this._addEvent(e,t)},t._bindDomEvent=function(e,t,n){t?dt["a"].findAll(this.el,t).forEach((function(t){lt()(t,e,n)})):lt()(this.el,e,n)},t._addEvent=function(e,t){var n=this._parseEventType(e),r=n.event,i=n.selector;Jn()(r,br)>-1?this._bindDomEvent(r,i,t):this.customEventManager.on(r,t)},t._unbindDomEvent=function(e,t,n){t?dt["a"].findAll(this.el,t).forEach((function(t){ut()(t,e,n)})):ut()(this.el,e,n)},t.off=function(e,t){var n=this._parseEventType(e),r=n.event,i=n.selector;Jn()(r,br)>-1?this._unbindDomEvent(r,i,t):this.customEventManager.off(r,t)},t._parseEventType=function(e){var t=e.split(" "),n=t.shift(),r=t.join(" ");return{event:n,selector:r}},t._setRootElement=function(e){if(!e){var t=this.tagName;e=document.createElement(t),e.className=this.className||"uic"+this._id}this.el=e},t.trigger=function(e,t){this.customEventManager.fire(e,t)},t.remove=function(){this.el&&dt["a"].remove(this.el)},t.destroy=function(){var e=this;this.remove(),i()(this,(function(t,n){e[n]=null}))},e}(),wr=Cr;function Er(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function Tr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Nr=function(e){function t(n){var r;return void 0===n&&(n={name:t.name}),r=e.call(this,u()({className:t.className},n))||this,r._name=n.name,r}Er(t,e);var n=t.prototype;return n.getName=function(){return this._name},t}(wr);Tr(Nr,"name","item"),Tr(Nr,"className","tui-toolbar-item");var kr=Nr,Sr='<div class="arrow"></div><span class="text"></span></span>',xr=7,Lr=function(){function e(){this.el=dt["a"].createElementWith('<div class="tui-tooltip">'+Sr+"</div>"),document.body.appendChild(this.el),this.hide()}var t=e.prototype;return t.show=function(e,t){var n=e.getBoundingClientRect(),r=n.left+window.pageXOffset,i=n.top+window.pageYOffset;h()(this.el,{top:i+e.clientHeight+xr+"px",left:r+3+"px"}),this.el.querySelector(".text").innerHTML=t,h()(this.el,{display:"block"})},t.hide=function(){h()(this.el,{display:"none"})},t.remove=function(){dt["a"].remove(this.el)},e}(),Mr=new Lr;function Ar(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Br(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function Or(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Dr=function(e){function t(n){var r;return void 0===n&&(n={tagName:"button",name:t.name}),r=e.call(this,{name:n.name,tagName:"button",className:n.className+" "+t.className,rootElement:n.el})||this,r._setOptions(n),r._render(),r.on("click",r._onClick.bind(Ar(r))),n.tooltip&&(r.on("mouseover",r._onOver.bind(Ar(r))),r.on("mouseout",r._onOut.bind(Ar(r)))),r}Br(t,e);var n=t.prototype;return n.setTooltip=function(e){this._tooltip=e},n._setOptions=function(e){this._command=e.command,this._event=e.event,this._text=e.text,this._tooltip=e.tooltip,this._style=e.style,this._state=e.state},n._render=function(){var e=document.createTextNode(this._text||"");this.el.appendChild(e),this.el.setAttribute("type","button"),this._style&&this.el.setAttribute("style",this._style)},n._onClick=function(){this.isEnabled()&&(this._command?this.trigger("command",this._command):this._event&&this.trigger("event",this._event),this.trigger("clicked"))},n._onOver=function(){this.isEnabled()&&Mr.show(this.el,this._tooltip)},n._onOut=function(){Mr.hide()},n.enable=function(){this.el.disabled=!1},n.disable=function(){this.el.disabled=!0},n.isEnabled=function(){return!this.el.disabled},t}(kr);Or(Dr,"name","button"),Or(Dr,"className","tui-toolbar-icons");var Rr=Dr;function Ir(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function Pr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Hr=function(e){function t(){return e.call(this,{name:t.name,tagName:"div",className:t.className})||this}return Ir(t,e),t}(kr);Pr(Hr,"name","divider"),Pr(Hr,"className","tui-toolbar-divider");var Fr=Hr,Ur=function(){function e(){}return e.create=function(e,t){var n;switch(e){case"heading":n=new Rr({name:"heading",className:"tui-heading",event:"openHeadingSelect",tooltip:cr.get("Headings"),state:"heading"});break;case"bold":n=new Rr({name:"bold",className:"tui-bold",command:"Bold",tooltip:cr.get("Bold"),state:"strong"});break;case"italic":n=new Rr({name:"italic",className:"tui-italic",command:"Italic",tooltip:cr.get("Italic"),state:"emph"});break;case"strike":n=new Rr({name:"strike",className:"tui-strike",command:"Strike",tooltip:cr.get("Strike"),state:"strike"});break;case"hr":n=new Rr({name:"hr",className:"tui-hrline",command:"HR",tooltip:cr.get("Line"),state:"thematicBreak"});break;case"quote":n=new Rr({name:"quote",className:"tui-quote",command:"Blockquote",tooltip:cr.get("Blockquote"),state:"blockQuote"});break;case"ul":n=new Rr({name:"ul",className:"tui-ul",command:"UL",tooltip:cr.get("Unordered list"),state:"list"});break;case"ol":n=new Rr({name:"ol",className:"tui-ol",command:"OL",tooltip:cr.get("Ordered list"),state:"orderedList"});break;case"task":n=new Rr({name:"task",className:"tui-task",command:"Task",tooltip:cr.get("Task"),state:"taskList"});break;case"table":n=new Rr({name:"table",className:"tui-table",event:"openPopupAddTable",tooltip:cr.get("Insert table"),state:"table"});break;case"image":n=new Rr({name:"image",className:"tui-image",event:"openPopupAddImage",tooltip:cr.get("Insert image"),state:""});break;case"link":n=new Rr({name:"link",className:"tui-link",event:"openPopupAddLink",tooltip:cr.get("Insert link")});break;case"code":n=new Rr({name:"code",className:"tui-code",command:"Code",tooltip:cr.get("Code"),state:"code"});break;case"codeblock":n=new Rr({name:"codeblock",className:"tui-codeblock",command:"CodeBlock",tooltip:cr.get("Insert CodeBlock"),state:"codeBlock"});break;case"indent":n=new Rr({name:"indent",className:"tui-indent",command:"Indent",tooltip:cr.get("Indent")});break;case"outdent":n=new Rr({name:"outdent",className:"tui-outdent",command:"Outdent",tooltip:cr.get("Outdent")});break;case"divider":n=new Fr;break;case"button":n=new Rr(t);break;case"item":default:n=new kr(t)}return n},e}(),qr=Ur;function jr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Wr(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function Vr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var zr=function(e){function t(t,n){var r;return void 0===n&&(n=[]),r=e.call(this,{tagName:"div",className:"tui-editor-defaultUI-toolbar"})||this,Vr(jr(r),"_items",[]),Vr(jr(r),"_eventManager",void 0),r._eventManager=t,r.setItems(n),r._initEvent(t),r}Wr(t,e);var n=t.prototype;return n._initEvent=function(e){var t=this;e.listen("stateChange",(function(e){t._items.forEach((function(t){t._state&&dt["a"].toggleClass(t.el,"active",!!e[t._state])}))})),e.listen("changePreviewTabPreview",(function(){return t.disableAllButton()})),e.listen("changePreviewTabWrite",(function(){return t.enableAllButton()})),e.listen("changeMode",(function(){return t.enableAllButton()}))},n.disableAllButton=function(){this._items.forEach((function(e){e instanceof Rr&&e.disable()}))},n.enableAllButton=function(){this._items.forEach((function(e){e instanceof Rr&&e.enable()}))},n.getItems=function(){return this._items.slice(0)},n.getItem=function(e){return this._items[e]},n.setItems=function(e){this.removeAllItems(),e.forEach(this.addItem.bind(this))},n.addItem=function(e){this.insertItem(this._items.length,e)},n.insertItem=function(e,t){var n=this;hr()(t)?t=qr.create(t):hr()(t.type)&&(t=qr.create(t.type,t.options));var r=this.el.children;e>=0&&e<r.length?(dt["a"].insertBefore(t.el,r[e]),this._items.splice(e,0,t)):(this.el.appendChild(t.el),this._items.push(t)),t.onCommandHandler=function(e){return n._eventManager.emit("command",e)},t.onEventHandler=function(e){return n._eventManager.emit(e)},t.on("command",t.onCommandHandler),t.on("event",t.onEventHandler)},n.indexOfItem=function(e){var t;if(e instanceof kr)t=this._items.indexOf(e);else if(hr()(e)){var n=e;t=this._items.map((function(e){return e.getName()})).indexOf(n)}return t},n.removeItem=function(e,t){var n,r;return void 0===t&&(t=!0),n=e instanceof kr?this.indexOfItem(e):e,n>=0&&(r=this._items.splice(n,1)[0]),r&&(t?r.destroy():(r.off("command",r.onCommandHandler),r.off("event",r.onEventHandler),dt["a"].remove(r.el))),r},n.removeAllItems=function(){while(this._items&&this._items.length>0)this.removeItem(0)},n.destroy=function(){this.removeAllItems(),e.prototype.destroy.call(this)},t}(wr),Kr=zr;function Gr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $r(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Yr="tui-popup-",Xr="fit-window",Zr='<div class="'+Yr+'header">\n        <span class="'+Yr+'title"></span>\n        <div class="'+Yr+'header-buttons">\n            <button type="button" class="'+Yr+'close-button"></button>\n        </div>\n    </div>\n    <div class="'+Yr+'body"></div>',Qr='<div class="'+Yr+'wrapper">\n        <div class="'+Yr+'header">\n            <span class="'+Yr+'title"></span>\n            <div class="'+Yr+'header-buttons">\n                <button type="button" class="'+Yr+'close-button"></button>\n            </div>\n        </div>\n        <div class="'+Yr+'body"></div>\n    </div>',Jr=function(e){function t(t){var n;return t=u()({header:!0,target:document.body,textContent:""},t),n=e.call(this,{tagName:"div",className:t.modal?Yr+"modal-background":Yr+"wrapper",rootElement:t.el})||this,n._clickEventMap={},n._onClickCloseButton=n.hide.bind(Gr(n)),n._initInstance(t),n._initDOM(t),n._initDOMEvent(t),n._initEditorEvent(t),n}$r(t,e);var n=t.prototype;return n._initInstance=function(e){this._target=e.target,e.el&&(this.el=e.el,this._isExternalHtmlUse=!0),e.content?this.content=e.content:this.content=e.textContent,this.options=e},n._initDOM=function(){this._initLayout(),this._isExternalHtmlUse||(a()(this.options.title)&&this.setTitle(this.options.title),this.setContent(this.content));var e=this.options.headerButtons;if(e){var t=dt["a"].findAll(this.el,"."+Yr+"close-button");t.forEach((function(e){dt["a"].remove(e)}));var n=this.el.querySelector("."+Yr+"header-buttons");dt["a"].empty(n),n.innerHTML=e}this.options.css&&h()(this.el,this.options.css)},n._initDOMEvent=function(){var e=this,t=this.options,n=t.openerCssQuery,r=t.closerCssQuery,i=document,o=i.body;n&&dt["a"].findAll(o,n).forEach((function(t){var n="click."+e._id;e._clickEventMap[n]=e.show.bind(e),lt()(t,"click",e._clickEventMap[n])})),r&&dt["a"].findAll(o,r).forEach((function(t){var n="click."+e._id;e._clickEventMap[n]=e.hide.bind(e),lt()(t,"click",e._clickEventMap[n])})),this.on("click ."+Yr+"close-button",this._onClickCloseButton)},n._initEditorEvent=function(){},n._initLayout=function(){var e=this.options;if(this._isExternalHtmlUse)this.hide(),this._target.appendChild(this.el);else{var t=e.modal?Qr:Zr;this.el.innerHTML=t,e.className&&p.a.apply(void 0,[this.el].concat(e.className.split(/\s+/g))),this.hide(),this._target.appendChild(this.el),this.body=this.el.querySelector("."+Yr+"body"),e.header||dt["a"].remove(this.el.querySelector("."+Yr+"header"))}},n.setContent=function(e){dt["a"].empty(this.body),hr()(e)?this.body.innerHTML=e:this.body.appendChild(e)},n.setTitle=function(e){var t=this.el.querySelector("."+Yr+"title");dt["a"].empty(t),t.innerHTML=e},n.getTitleElement=function(){return this.el.querySelector("."+Yr+"title")},n.hide=function(){h()(this.el,{display:"none"}),this._isShow=!1,this.trigger("hidden",this)},n.show=function(){h()(this.el,{display:"block"}),this._isShow=!0,this.trigger("shown",this)},n.isShow=function(){return this._isShow},n.remove=function(){var e=this,t=this.options,n=t.openerCssQuery,r=t.closerCssQuery,i=document,o=i.body;this.trigger("remove",this),this.off("click ."+Yr+"close-button",this._onClickCloseButton),n&&dt["a"].findAll(o,n).forEach((function(t){ut()(t,"click",e._clickEventMap["click."+e._id]),delete e._clickEventMap["click."+e._id]})),r&&dt["a"].findAll(o,r).forEach((function(t){ut()(t,"click",e._clickEventMap["click."+e._id]),delete e._clickEventMap["click."+e._id]})),dt["a"].remove(this.el),this.el=null},n.setFitToWindow=function(e){dt["a"].toggleClass(this.el,Xr,e)},n.isFitToWindow=function(){return Bt()(this.el,Xr)},n.toggleFitToWindow=function(){var e=!this.isFitToWindow();return this.setFitToWindow(e),e},t}(wr),ei=Jr;function ti(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function ni(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ri=function(e){function t(t){return t=u()({header:!1,className:"te-dropdown-toolbar"},t),e.call(this,t)||this}ti(t,e);var n=t.prototype;return n.getToolbar=function(){return this._toolbar},n.getItems=function(){return this.getToolbar().getItems()},n.getItem=function(e){return this.getToolbar().getItem(e)},n.setItems=function(e){this.getToolbar().setItems(e)},n.addItem=function(e){this.getToolbar().addItem(e)},n.insertItem=function(e,t){this.getToolbar().insertItem(e,t)},n.indexOfItem=function(e){return this.getToolbar().indexOfItem(e)},n.removeItem=function(e,t){return this.getToolbar().removeItem(e,t)},n.removeAllItems=function(){this.getToolbar().removeAllItems()},n._initInstance=function(t){e.prototype._initInstance.call(this,t);var n=t.button,r=t.eventManager;this._button=n,this._eventManager=r,this._toolbar=new Kr(r)},n._initDOM=function(){e.prototype._initDOM.call(this),this.setContent(this._toolbar.el)},n._initEditorEvent=function(){var n=this;e.prototype._initEditorEvent.call(this),this._eventManager.listen("focus",(function(){return n.hide()})),this._eventManager.listen("closeAllPopup",(function(){return n.hide()})),this._eventManager.listen(t.OPEN_EVENT,(function(){var e=n.isShow();n._eventManager.emit("closeAllPopup"),e||n.show(),h()(n.el,{left:"-1000px"});var t=n._button,r=dt["a"].getOuterHeight(t,!0),i=(r-dt["a"].getOuterHeight(t))/2,o=t.offsetTop+r-i,a=t.offsetLeft+dt["a"].getOuterWidth(t,!0)-dt["a"].getOuterWidth(n.el,!0);h()(n.el,{top:o+"px",left:a+"px"})}))},t}(ei);ni(ri,"OPEN_EVENT","openDropdownToolbar");var ii=ri;function oi(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ai(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function si(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var li="more",ci=function(e){function t(t,n){var r;return r=e.call(this,t,n)||this,si(oi(r),"_moreButton",void 0),si(oi(r),"_popupDropdownToolbar",void 0),si(oi(r),"_observer",void 0),r._init(t),r._bindWidthChangedEvent(),r}ai(t,e);var n=t.prototype;return n.insertItem=function(t,n){e.prototype.insertItem.call(this,t,n),this._arrangeMoreButton()},n._init=function(e){var t=qr.create("button",{name:li,className:"tui-more",tooltip:cr.get("More"),event:ii.OPEN_EVENT});this._moreButton=t,this._popupDropdownToolbar=new ii({eventManager:e,target:this.el,button:t.el}),this.addItem(t)},n._bindWidthChangedEvent=function(){var e=this;this._observer=new ur["a"]((function(){e._popupDropdownToolbar.hide(),e._balanceButtons()})),this._observer.observe(this.el)},n._balanceButtons=function(){var t=this,n=this._popupDropdownToolbar.getItems();n.forEach((function(n){t._popupDropdownToolbar.removeItem(n,!1);var r=t.getItems().length;e.prototype.insertItem.call(t,r,n)})),this.removeItem(this._moreButton,!1),e.prototype.insertItem.call(this,0,this._moreButton);var r=this.getItems(),i=r.filter((function(e){return e.el.offsetTop>t.el.clientHeight}));i.forEach((function(e){t.removeItem(e,!1),t._popupDropdownToolbar.addItem(e)})),this._arrangeMoreButton()},n._arrangeMoreButton=function(){if(this._popupDropdownToolbar){this.removeItem(this._moreButton,!1);var t=this._popupDropdownToolbar.getItems().length>0,n=this.getItems().length;t&&e.prototype.insertItem.call(this,n,this._moreButton)}},n.destroy=function(){this._observer&&(this._observer.disconnect(),this._observer=null)},t}(Kr),ui=ci;function di(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var hi="te-tab-active",fi=function(e){function t(t){var n;return void 0===t&&(t={}),n=e.call(this,{tagName:"div",className:"te-tab"})||this,n.sections=t.sections,n._activeButton=null,n._render(t),n._initEvent(t),n}di(t,e);var n=t.prototype;return n._initEvent=function(e){var t=e.onItemClick;t&&this.on("itemClick",t),this.on("click button",this._onTabButton.bind(this))},n._render=function(e){for(var t=e.items,n=e.initName,r=[],i=0,o=t.length;i<o;i+=1)r.push('<button type="button" data-index="'+i+'">'+t[i]+"</button>");this.el.innerHTML=r.join(""),this.activate(n)},n._findButtonContained=function(e,t,n){return dt["a"].findAll(e,t).filter((function(e){return new RegExp(n).test(e.textContent)}))},n.activate=function(e){var t=this._findButtonContained(this.el,"button",e),n=t[0];this._activateTabByButton(n)},n._onTabButton=function(e){var t=e.target;this._activateTabByButton(t),this.trigger("itemClick",t.textContent)},n._activateTabByButton=function(e){this._isActivatedButton(e)||this._updateClassByButton(e)},n._updateClassByButton=function(e){if(this._activeButton){var t=this._activeButton.getAttribute("data-index");m()(this._activeButton,hi),this.sections&&m()(this.sections[t],hi)}p()(e,hi),this._activeButton=e;var n=e.getAttribute("data-index");this.sections&&p()(this.sections[n],hi)},n._isActivatedButton=function(e){return this._activeButton&&this._activeButton.textContent===e.textContent},t}(wr),pi=fi;function gi(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function mi(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function vi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var bi="markdown",_i="wysiwyg",yi=function(e){function t(t,n,r){var i;return i=e.call(this,{tagName:"div",className:"te-mode-switch"})||this,vi(gi(i),"_buttons",{}),vi(gi(i),"_type",void 0),vi(gi(i),"_rootElement",void 0),i._eventManager=r,i._render(t),i._switchType(a()(n)?n:bi),i._initEvent(),i}mi(t,e);var n=t.prototype;return n.isShown=function(){return"block"===this._rootElement.style.display},n.show=function(){h()(this._rootElement,{display:"block"})},n.hide=function(){h()(this._rootElement,{display:"none"})},n._render=function(e){this._buttons.markdown=dt["a"].createElementWith('<button class="te-switch-button markdown" type="button">'+cr.get("Markdown")+"</button>"),this._buttons.wysiwyg=dt["a"].createElementWith('<button class="te-switch-button wysiwyg" type="button">'+cr.get("WYSIWYG")+"</button>"),this.el.appendChild(this._buttons.markdown),this.el.appendChild(this._buttons.wysiwyg),e&&(e.appendChild(this.el),this._rootElement=e),this.on("click .markdown",this._changeMarkdown.bind(this)),this.on("click .wysiwyg",this._changeWysiwyg.bind(this)),this.show()},n._changeMarkdown=function(){this._switchType(bi)},n._changeWysiwyg=function(){this._switchType(_i)},n._setActiveButton=function(e){m()(this._buttons.markdown,"active"),m()(this._buttons.wysiwyg,"active"),p()(this._buttons[""+e],"active")},n._switchType=function(e){this._type!==e&&(this._type=e,this._setActiveButton(e),this.trigger("modeSwitched",this._type))},n._initEvent=function(){var e=this;this._eventManager.listen("changeMode",(function(t){e._type!==t&&(e._type=t,e._setActiveButton(t))}))},t}(wr);vi(yi,"TYPE",{MARKDOWN:bi,WYSIWYG:_i});var Ci=yi;function wi(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Ei=/^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})(\/([^\s]*))?$/,Ti=function(e){function t(t){var n='\n            <label for="url">'+cr.get("URL")+'</label>\n            <input type="text" class="te-url-input" />\n            <label for="linkText">'+cr.get("Link text")+'</label>\n            <input type="text" class="te-link-text-input" />\n            <div class="te-button-section">\n                <button type="button" class="te-ok-button">'+cr.get("OK")+'</button>\n                <button type="button" class="te-close-button">'+cr.get("Cancel")+"</button>\n            </div>\n        ";return t=u()({header:!0,title:cr.get("Insert link"),className:"te-popup-add-link tui-editor-popup",content:n},t),e.call(this,t)||this}wi(t,e);var n=t.prototype;return n._initInstance=function(t){e.prototype._initInstance.call(this,t),this._editor=t.editor,this._eventManager=t.editor.eventManager},n._initDOM=function(){e.prototype._initDOM.call(this);var t=this.el;this._inputText=t.querySelector(".te-link-text-input"),this._inputURL=t.querySelector(".te-url-input")},n._initDOMEvent=function(){var t=this;e.prototype._initDOMEvent.call(this),this.on("click .te-close-button",(function(){return t.hide()})),this.on("click .te-ok-button",(function(){return t._addLink()})),this.on("shown",(function(){var e=t._inputText,n=t._inputURL,r=t._editor.getSelectedText().trim();e.value=r,Ei.exec(r)&&(n.value=r),n.focus()})),this.on("hidden",(function(){t._resetInputs()}))},n._initEditorEvent=function(){var t=this;e.prototype._initEditorEvent.call(this);var n=this._eventManager;n.listen("focus",(function(){return t.hide()})),n.listen("closeAllPopup",(function(){return t.hide()})),n.listen("openPopupAddLink",(function(){n.emit("closeAllPopup"),t.show()}))},n._addLink=function(){var e=this._getValue(),t=e.url,n=e.linkText;this._clearValidationStyle(),n.length<1?p()(this._inputText,"wrong"):t.length<1?p()(this._inputURL,"wrong"):(this._eventManager.emit("command","AddLink",{linkText:n,url:t}),this.hide())},n._getValue=function(){var e=this._inputURL.value,t=this._inputText.value;return{url:e,linkText:t}},n._clearValidationStyle=function(){m()(this._inputURL,"wrong"),m()(this._inputText,"wrong")},n._resetInputs=function(){this._inputText.value="",this._inputURL.value="",this._clearValidationStyle()},t}(ei),Ni=Ti;function ki(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Si="te-image-url-input",xi="te-image-file-input",Li="te-alt-text-input",Mi="te-ok-button",Ai="te-close-button",Bi="te-file-type",Oi="te-url-type",Di="te-tab-section",Ri="ui",Ii=function(e){function t(t){var n='\n            <div class="'+Di+'"></div>\n            <div class="'+Oi+'">\n                <label for="">'+cr.get("Image URL")+'</label>\n                <input type="text" class="'+Si+'" />\n            </div>\n            <div class="'+Bi+'">\n                <label for="">'+cr.get("Select image file")+'</label>\n                <input type="file" class="'+xi+'" accept="image/*" />\n            </div>\n            <label for="url">'+cr.get("Description")+'</label>\n            <input type="text" class="'+Li+'" />\n            <div class="te-button-section">\n                <button type="button" class="'+Mi+'">'+cr.get("OK")+'</button>\n                <button type="button" class="'+Ai+'">'+cr.get("Cancel")+"</button>\n            </div>\n        ";return t=u()({header:!0,title:cr.get("Insert image"),className:"te-popup-add-image tui-editor-popup",content:n},t),e.call(this,t)||this}ki(t,e);var n=t.prototype;return n._initInstance=function(t){e.prototype._initInstance.call(this,t),this.eventManager=t.eventManager},n._initDOM=function(){e.prototype._initDOM.call(this);var t=this.el;this._imageUrlInput=t.querySelector("."+Si),this._imageFileInput=t.querySelector("."+xi),this._altTextInput=t.querySelector("."+Li);var n=t.querySelector("."+Bi),r=t.querySelector("."+Oi),i=this.body.querySelector("."+Di);this.tab=new pi({initName:cr.get("File"),items:[cr.get("File"),cr.get("URL")],sections:[n,r]}),i.appendChild(this.tab.el)},n._initDOMEvent=function(){var t=this;e.prototype._initDOMEvent.call(this),this.on("shown",(function(){return t._imageUrlInput.focus()})),this.on("hidden",(function(){return t._resetInputs()})),this.on("change ."+xi,(function(){var e=t._imageFileInput.value.split("\\").pop();t._altTextInput.value=e})),this.on("click ."+Ai,(function(){return t.hide()})),this.on("click ."+Mi,(function(){var e=t._imageUrlInput.value,n=t._altTextInput.value;if(e)t._applyImage(e,n);else{var r=t._imageFileInput.files;if(r.length){var i=r.item(0),o=function(e,r){return t._applyImage(e,r||n)};t.eventManager.emit("addImageBlobHook",i,o,Ri)}}t.hide()})),this.tab.on("itemClick",(function(){return t._resetInputs()}))},n._initEditorEvent=function(){var t=this;e.prototype._initEditorEvent.call(this),this.eventManager.listen("focus",(function(){return t.hide()})),this.eventManager.listen("closeAllPopup",(function(){return t.hide()})),this.eventManager.listen("openPopupAddImage",(function(){t.eventManager.emit("closeAllPopup"),t.show()}))},n._applyImage=function(e,t){this.eventManager.emit("command","AddImage",{imageUrl:e,altText:t||"image"}),this.hide()},n._resetInputs=function(){dt["a"].findAll(this.el,"input").forEach((function(e){e.value=""}))},n.remove=function(){this.tab.remove(),e.prototype.remove.call(this)},t}(ei),Pi=Ii;function Hi(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Fi="te-table-remove-row",Ui="te-context-menu-disabled",qi=function(e){function t(t){var n='\n      <button type="button" class="te-table-add-row">'+cr.get("Add row")+'</button>\n      <button type="button" class="te-table-add-col">'+cr.get("Add col")+'</button>\n      <button type="button" class="te-table-remove-row">'+cr.get("Remove row")+'</button>\n      <button type="button" class="te-table-remove-col">'+cr.get("Remove col")+'</button>\n      <hr/>\n      <button type="button" class="te-table-col-align-left">'+cr.get("Align left")+'</button>\n      <button type="button" class="te-table-col-align-center">'+cr.get("Align center")+'</button>\n      <button type="button" class="te-table-col-align-right">'+cr.get("Align right")+'</button>\n      <hr/>\n      <button type="button" class="te-table-remove">'+cr.get("Remove table")+"</button>\n    ";return t=u()({header:!1,className:"te-popup-table-utils",content:n},t),e.call(this,t)||this}Hi(t,e);var n=t.prototype;return n._initInstance=function(t){e.prototype._initInstance.call(this,t),this.eventManager=t.eventManager},n._initDOMEvent=function(){var t=this;e.prototype._initDOMEvent.call(this),this.on("click .te-table-add-row",(function(){return t.eventManager.emit("command","AddRow")})),this.on("click .te-table-add-col",(function(){return t.eventManager.emit("command","AddCol")})),this.on("click .te-table-col-align-left",(function(){return t.eventManager.emit("command","AlignCol","left")})),this.on("click .te-table-col-align-center",(function(){return t.eventManager.emit("command","AlignCol","center")})),this.on("click .te-table-col-align-right",(function(){return t.eventManager.emit("command","AlignCol","right")})),this.on("click .te-table-remove-col",(function(){return t.eventManager.emit("command","RemoveCol")})),this.on("click .te-table-remove",(function(){return t.eventManager.emit("command","RemoveTable")})),this._bindClickEventOnRemoveRowMenu()},n._initEditorEvent=function(){var t=this;e.prototype._initEditorEvent.call(this),this.eventManager.listen("focus",(function(){return t.hide()})),this.eventManager.listen("mousedown",(function(){return t.hide()})),this.eventManager.listen("closeAllPopup",(function(){return t.hide()})),this.eventManager.listen("openPopupTableUtils",(function(e){var n=t.el.parentNode.getBoundingClientRect(),r=n.left,i=n.top;t._disableRemoveRowMenu(e.target),h()(t.el,{position:"absolute",top:e.clientY-i+5+"px",left:e.clientX-r+10+"px"}),t.eventManager.emit("closeAllPopup"),t.show()}))},n._bindClickEventOnRemoveRowMenu=function(){var e=this;this.on("click ."+Fi,(function(t){var n=t.target;Bt()(n,Ui)?t.preventDefault():e.eventManager.emit("command","RemoveRow")}))},n._disableRemoveRowMenu=function(e){var t=this.el.querySelector("."+Fi);dt["a"].toggleClass(t,Ui,"TH"===e.nodeName)},t}(ei),ji=qi;function Wi(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Vi="te-table-selection",zi="te-table-header",Ki="te-table-body",Gi="te-selection-area",$i="te-description",Yi='\n    <div class="'+Vi+'">\n        <div class="'+zi+'"></div>\n        <div class="'+Ki+'"></div>\n        <div class="'+Gi+'"></div>\n    </div>\n    <p class="'+$i+'"></p>\n',Xi=25,Zi=17,Qi=7,Ji=14,eo=5,to=9,no=1,ro=1,io=1,oo=1,ao=function(e){function t(t){return t=u()({header:!1,className:"te-popup-add-table",content:Yi},t),e.call(this,t)||this}Wi(t,e);var n=t.prototype;return n._initInstance=function(t){e.prototype._initInstance.call(this,t),this._selectedBound={},this._tableBound={},this._eventManager=t.eventManager,this._button=t.button,this._eventHandlers={onMousedown:this._selectTableRange.bind(this),onClick:this._fireCommandEvent.bind(this)}},n._initDOM=function(){e.prototype._initDOM.call(this),this._cacheElements(),this._setTableSizeByBound(eo,Qi)},n._initDOMEvent=function(t){e.prototype._initDOMEvent.call(this,t),this.on("mousemove ."+Vi,this._eventHandlers.onMousedown),this.on("click ."+Vi,this._eventHandlers.onClick)},n._selectTableRange=function(e){var t=e.pageX-this._selectionOffset.left,n=e.pageY-this._selectionOffset.top,r=this._getSelectionBoundByOffset(t,n);this._resizeTableBySelectionIfNeed(r.col,r.row),this._setSelectionAreaByBound(r.col,r.row),this._setDisplayText(r.col,r.row),this._setSelectedBound(r.col,r.row)},n._fireCommandEvent=function(){var e=this._getSelectedTableSize();this._eventManager.emit("command","Table",e.col,e.row)},n._initEditorEvent=function(){var t=this;e.prototype._initEditorEvent.call(this),this._eventManager.listen("focus",(function(){return t.hide()})),this._eventManager.listen("closeAllPopup",(function(){return t.hide()})),this._eventManager.listen("openPopupAddTable",(function(){var e=t._button,n=e.offsetTop,r=e.offsetLeft;h()(t.el,{top:n+dt["a"].getOuterHeight(e)+"px",left:r+"px"}),t._eventManager.emit("closeAllPopup"),t.show();var i=t.el.querySelector("."+Vi).getBoundingClientRect(),o=i.left,a=i.top;t._selectionOffset={left:o+window.pageXOffset,top:a+window.pageYOffset}}))},n._cacheElements=function(){this.header=this.el.querySelector("."+zi),this.body=this.el.querySelector("."+Ki),this.selection=this.el.querySelector("."+Gi),this.desc=this.el.querySelector("."+$i)},n._resizeTableBySelectionIfNeed=function(e,t){var n=this._getResizedTableBound(e,t);n&&this._setTableSizeByBound(n.col,n.row)},n._getResizedTableBound=function(e,t){var n,r,i;return e>=eo&&e<to?n=e+1:e<eo&&(n=eo),t>=Qi&&t<Ji?r=t+1:t<Qi&&(r=Qi),this._isNeedResizeTable(n,r)&&(i={row:r||this._tableBound.row,col:n||this._tableBound.col}),i},n._isNeedResizeTable=function(e,t){return e&&e!==this._tableBound.col||t&&t!==this._tableBound.row},n._getBoundByOffset=function(e,t){var n=parseInt(t/Zi,10),r=parseInt(e/Xi,10);return{row:n,col:r}},n._getOffsetByBound=function(e,t){var n=e*Xi+Xi,r=t*Zi+Zi;return{x:n,y:r}},n._setTableSizeByBound=function(e,t){var n=this._getOffsetByBound(e,t-io);this._setTableSize(n.x,n.y),this._tableBound.row=t,this._tableBound.col=e},n._getSelectionBoundByOffset=function(e,t){var n=this._getBoundByOffset(e,t);return n.row<no?n.row=no:n.row>this._tableBound.row&&(n.row=this._tableBound.row),n.col<ro?n.col=ro:n.col>this._tableBound.col&&(n.col=this._tableBound.col),n},n._setSelectionAreaByBound=function(e,t){var n=this._getOffsetByBound(e,t);this._setSelectionArea(n.x,n.y)},n._setSelectedBound=function(e,t){this._selectedBound.col=e,this._selectedBound.row=t},n._getSelectedTableSize=function(){return{row:this._selectedBound.row+1,col:this._selectedBound.col+1}},n._setDisplayText=function(e,t){this.desc.innerHTML=e+1+" x "+(t+1)},n._setTableSize=function(e,t){e+=oo,t+=oo,h()(this.header,{height:Zi+"px",width:e+"px"}),h()(this.body,{height:t+"px",width:e+"px"}),h()(this.el,{width:e+30+"px"})},n._setSelectionArea=function(e,t){e+=oo,t+=oo,h()(this.selection,{height:t+"px",width:e+"px"})},n.remove=function(){this.off("mousemove ."+Vi,this._eventHandlers.onMousedown),this.off("click ."+Vi,this._eventHandlers.onClick),e.prototype.remove.call(this)},t}(ei);ao.CELL_WIDTH=Xi,ao.CELL_HEIGHT=Zi,ao.MIN_ROW_SELECTION_INDEX=no,ao.MIN_COL_SELECTION_INDEX=ro;var so=ao;function lo(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var co=function(e){function t(t){var n='\n      <ul>\n        <li data-value="1" data-type="Heading"><h1>'+cr.get("Heading")+' 1</h1></li>\n        <li data-value="2" data-type="Heading"><h2>'+cr.get("Heading")+' 2</h2></li>\n        <li data-value="3" data-type="Heading"><h3>'+cr.get("Heading")+' 3</h3></li>\n        <li data-value="4" data-type="Heading"><h4>'+cr.get("Heading")+' 4</h4></li>\n        <li data-value="5" data-type="Heading"><h5>'+cr.get("Heading")+' 5</h5></li>\n        <li data-value="6" data-type="Heading"><h6>'+cr.get("Heading")+' 6</h6></li>\n        <li data-type="Paragraph"><div>'+cr.get("Paragraph")+"</div></li>\n      </ul>\n    ";return t=u()({header:!1,className:"te-heading-add",content:n},t),e.call(this,t)||this}lo(t,e);var n=t.prototype;return n._initInstance=function(t){e.prototype._initInstance.call(this,t),this._eventManager=t.eventManager,this._button=t.button},n._initDOMEvent=function(){var t=this;e.prototype._initDOMEvent.call(this),this.on("click li",(function(e){var n=dt["a"].closest(e.target,"li");t._eventManager.emit("command",n.getAttribute("data-type"),n.getAttribute("data-value"))}))},n._initEditorEvent=function(){var t=this;e.prototype._initEditorEvent.call(this),this._eventManager.listen("focus",this.hide.bind(this)),this._eventManager.listen("closeAllPopup",this.hide.bind(this)),this._eventManager.listen("openHeadingSelect",(function(){var e=t._button,n=e.offsetTop,r=e.offsetLeft;h()(t.el,{top:n+dt["a"].getOuterHeight(e)+"px",left:r+"px"}),t._eventManager.emit("closeAllPopup"),t.show()}))},t}(ei),uo=co;function ho(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var fo="te-popup-code-block-lang-";function po(e){return e.map((function(e){return'<button type="button" class="'+fo+e+'" data-lang="'+e+'">'+e+"</button>"})).join("")}var go=function(e){function t(t){var n=t,r=n.languages;return t=u()({header:!1,className:"te-popup-code-block-languages",content:po(r)},t),e.call(this,t)||this}ho(t,e);var n=t.prototype;return n._initInstance=function(t){var n=this;e.prototype._initInstance.call(this,t),this.eventManager=t.eventManager,this._onSelectedLanguage=null,this._onDismissed=null,this._currentButton=null,this._buttons=null,this._languages=t.languages,this._btnMousedownHandler=function(e){var t=e.target.getAttribute("data-lang");n._onSelectedLanguage&&n._onSelectedLanguage(t),n.hide()}},n._initDOM=function(t){e.prototype._initDOM.call(this,t),h()(this.el,"zIndex",1e4),this._buttons=dt["a"].findAll(this.el,"button"),this._activateButtonByIndex(0)},n._initDOMEvent=function(){e.prototype._initDOMEvent.call(this),this._addBtnMouseDownHandler()},n._initEditorEvent=function(){var t=this;e.prototype._initEditorEvent.call(this),this.eventManager.listen("openPopupCodeBlockLanguages",(function(e){return t.show(e.callback),h()(t.el,{top:e.offset.top+"px"}),h()(t.el,{left:e.offset.left+"px"}),t.setCurrentLanguage(e.language),t})),this.eventManager.listen("focus",(function(){return t.hide()})),this.eventManager.listen("mousedown",(function(){return t.hide()})),this.eventManager.listen("closeAllPopup",(function(){return t.hide()})),this.eventManager.listen("closePopupCodeBlockLanguages",(function(){return t.hide()})),this.eventManager.listen("scroll",(function(){return t.hide()})),this.eventManager.listen("setCodeBlockLanguages",(function(e){return t._changeLanguageButtons(e)}))},n._activateButtonByIndex=function(e){this._currentButton&&m()(this._currentButton,"active"),this._buttons.length&&(this._currentButton=this._buttons[e],p()(this._currentButton,"active"),this._currentButton.scrollIntoView())},n.prev=function(){var e=Jn()(this._currentButton,this._buttons)-1;e<0&&(e=this._buttons.length-1),this._activateButtonByIndex(e)},n.next=function(){var e=Jn()(this._currentButton,this._buttons)+1;e>=this._buttons.length&&(e=0),this._activateButtonByIndex(e)},n.getCurrentLanguage=function(){var e=this._currentButton.getAttribute("data-lang");return e},n.setCurrentLanguage=function(e){var t=this._buttons.filter((function(t){return ft()(t,"."+fo+e)}));if(t.length>0){var n=Jn()(t[0],this._buttons);this._activateButtonByIndex(n)}},n.show=function(t){this._onSelectedLanguage=t.selected,this._onDismissed=t.dismissed,e.prototype.show.call(this)},n.hide=function(){this._onDismissed&&this._onDismissed(),this._onSelectedLanguage=null,this._onDismissed=null,e.prototype.hide.call(this)},n._addBtnMouseDownHandler=function(){var e=this;this._languages.forEach((function(t){e.off("mousedown ."+fo+t,e._btnMousedownHandler),e.on("mousedown ."+fo+t,e._btnMousedownHandler)}))},n._changeLanguageButtons=function(e){this._languages=e,e&&e.length&&(this.content=po(e),this.setContent(this.content),this._addBtnMouseDownHandler(),this._buttons=dt["a"].findAll(this.el,"button"),this._activateButtonByIndex(0))},t}(ei),mo=go,vo="tui-split-scroll",bo="single-content",_o="scroll-sync",yo="tui-split-scroll-wrapper",Co="tui-split-scroll-content",wo="tui-splitter",Eo="requireScrollIntoView",To="tui-split-content-left",No="tui-split-content-right",ko={left:To,right:No},So=function(){function e(e,t,n,r){void 0===r&&(r={}),r=u()({showScrollSyncButton:!1,scrollSync:!0,splitView:!0},r),this._baseElement=e,this._eventManager=r.eventManager,this._contentElements=[],this._initDom(t,n,r),this._initDomEvent()}var t=e.prototype;return t._initDom=function(e,t,n){var r=document.createElement("div");r.className=vo,this._el=r;var i=document.createElement("div");i.className=yo,this._scrollWrapper=i,this._setScrollSync(n.scrollSync),this.setSplitView(n.splitView);var o=document.createElement("div");o.className=Co,this._contentWrapper=o;var a=document.createElement("div");a.className=wo,this._baseElement.appendChild(r),r.appendChild(i),i.appendChild(o),i.appendChild(a),this._setLeft(e),this._setRight(t)},t._initDomEvent=function(){this._contentWrapper.addEventListener("scroll",this.sync.bind(this))},t._requireScrollIntoView=function(e){var t,n=e.getBoundingClientRect(),r=n.top,i=n.bottom;if(this.isScrollSynced())t=this._contentWrapper;else if(dt["a"].parents(e,this._contentElements.left).length)t=this._contentElements.left;else{if(!dt["a"].parents(e,this._contentElements.right).length)return;t=this._contentElements.right}var o=t.getBoundingClientRect(),a=o.top,s=o.bottom;r<a?t.scrollTop=t.scrollTop+r-a:i>s&&(t.scrollTop=t.scrollTop+i-s),this.sync()},t._setContentElement=function(e,t){var n=this,r=this._contentElements[t];r&&(this._eventManager.removeEventHandler(Eo),this._contentWrapper.removeChild(r)),p()(e,ko[t]),this._contentWrapper.appendChild(e),this._eventManager.listen(Eo,(function(e){return n._requireScrollIntoView(e)})),this._eventManager.listen("requireScrollSync",(function(){return n.sync()})),this._contentElements[t]=e,this.sync()},t._setLeft=function(e){this._setContentElement(e,"left")},t._setRight=function(e){this._setContentElement(e,"right")},t._setScrollSync=function(e){dt["a"].toggleClass(this._el,_o,e)},t.toggleScrollSync=function(){dt["a"].toggleClass(this._el,_o)},t.setSplitView=function(e){e?m()(this._el,bo):p()(this._el,bo)},t.toggleSplitView=function(){dt["a"].toggleClass(this._el,bo)},t.isScrollSynced=function(){return Bt()(this._el,_o)},t.isSplitView=function(){return!Bt()(this._el,bo)},t.sync=function(){if(this._contentElements.left&&this._contentElements.right){var e=this._contentWrapper.clientHeight,t=this._contentWrapper.scrollTop,n=this._contentElements.left,r=this._contentElements.right,i=n.offsetHeight-e>0?n:r,o=i===n?r:n,a=i.offsetHeight,s=Math.max(a-e,0),l=Math.max(o.offsetHeight,e),c=a-l;h()(i,{top:0}),h()(o,{top:t/s*c+"px"})}},t.scrollTop=function(e){this._contentWrapper.scrollTop=e},e}(),xo=So;function Lo(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Mo=function(e){function t(t,n){var r;return r=e.call(this,t,{singleCursorHeightPerLine:!1,theme:"none"})||this,r._language="",r._eventManager=n,r._initEvent(),r}Lo(t,e);var n=t.prototype;return n._initEvent=function(){var e=this;this.on("cursorActivity",this._onRequireScrollIntoView.bind(this)),this.on("beforeChange",(function(t,n){"paste"===n.origin&&e._eventManager.emit("pasteBefore",{source:"codeblock",data:n})}))},n._onRequireScrollIntoView=function(){var e=this,t=this.getCursor(),n=this.getWrapperElement();setTimeout((function(){var r=n.querySelector("pre:nth-child("+(t.line+1)+")");r&&e._eventManager.emit("requireScrollIntoView",r)}),0)},n.load=function(e){var t=e.cloneNode(!0);this.setLanguage(t.getAttribute("data-language")||""),this.setEditorCodeText(t.textContent)},n.save=function(e){e.innerHTML="",e.textContent=this.getEditorCodeText(),e.setAttribute("data-language",this._language),this._eventManager.emit("changeLanguage")},n.clear=function(){this.setLanguage(""),this.setEditorCodeText("")},n.getLanguage=function(){return this._language},n.setLanguage=function(e){void 0===e&&(e=""),this._language=e},n.getEditorCodeText=function(){return this.getValue()},n.setEditorCodeText=function(e){void 0===e&&(e=""),this.setValue(e)},n.refresh=function(){this.cm.refresh()},t}(V),Ao=Mo,Bo=n(38);function Oo(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Do(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Ro=function(e){function t(t,n,r,i){var o;return o=e.call(this,t,n,r,!0)||this,o._codeBlockEditor=i,o._initEvent(),o.lazyRunner.registerLazyRunFunction("refresh",o.refresh,o.delayCodeBlockTime,Oo(o)),o}Do(t,e);var n=t.prototype;return n._initEvent=function(){var e=this;this._codeBlockEditor.on("update",(function(){return e.lazyRunner.run("refresh")}))},n.refresh=function(){var t=this._codeBlockEditor.getLanguage(),n=this._codeBlockEditor.getEditorCodeText();e.prototype.refresh.call(this,"```"+t+"\n"+n+"\n```"),this.eventManager.emit("requireScrollSync")},n.clear=function(){e.prototype.render.call(this,"")},t}(Bo["a"]),Io=Ro,Po=function(){function e(e,t){this._eventManager=e,this._languages=t,this._initDOM(),this._initDOMEvent(),this._initEvent()}var t=e.prototype;return t._initDOM=function(){this._inputLanguage=dt["a"].createElementWith('<input type="text" maxlength="20" placeholder="'+cr.get("Choose language")+'" />'),this._wrapper=dt["a"].createElementWith('<span class="te-input-language"></span>'),this._wrapper.appendChild(this._inputLanguage),this._hide()},t._initDOMEvent=function(){var e=this;this._inputLanguage.addEventListener("keydown",(function(t){return e._onKeyEvent(t)})),this._inputLanguage.addEventListener("focus",(function(){return e._showPopupCodeBlockLanguages()})),this._inputLanguage.addEventListener("focusout",(function(){return e._onFocusOut()})),this._wrapper.addEventListener("mousedown",(function(t){t.target===e._wrapper&&(t.preventDefault(),e._toggleFocus())}))},t._initEvent=function(){var e=this;this._eventManager.listen("setCodeBlockLanguages",(function(t){e._languages=t,t&&t.length?e._show():e._hide()}))},t._showPopupCodeBlockLanguages=function(){var e=this,t=this._inputLanguage.getBoundingClientRect();p()(this._wrapper,"active"),this.active=!0,this._popupCodeBlockLanguages=this._eventManager.emitReduce("openPopupCodeBlockLanguages",{language:this._prevStoredLanguage,offset:{left:t.left,top:t.bottom},callback:{selected:function(t){return e._onLanguageSelectedFromList(t)},dismissed:function(){e._popupCodeBlockLanguages=null}}})},t._toggleFocus=function(){var e=this._inputLanguage;Bt()(this._wrapper,"active")?e.blur():e.focus()},t._onFocusOut=function(){m()(this._wrapper,"active"),this._inputLanguage.value=this._prevStoredLanguage,this._hidePopupCodeBlockLanguages()},t._onKeyEvent=function(e){if(this._popupCodeBlockLanguages)switch(e.which){case G.keyCode("UP"):this._popupCodeBlockLanguages.prev(),e.preventDefault();break;case G.keyCode("DOWN"):this._popupCodeBlockLanguages.next(),e.preventDefault();break;case G.keyCode("ENTER"):case G.keyCode("TAB"):var t=this._popupCodeBlockLanguages.getCurrentLanguage();this._inputLanguage.value=t,this._storeInputLanguage(),e.preventDefault();break;default:this._popupCodeBlockLanguages.hide()}else e.which!==G.keyCode("ENTER")&&e.which!==G.keyCode("TAB")||(this._storeInputLanguage(),e.preventDefault())},t._onLanguageSelectedFromList=function(e){this._inputLanguage.value=e,this._storeInputLanguage()},t.setOnLanguageSelected=function(e){this._onLanguageSelected=e},t._hidePopupCodeBlockLanguages=function(){this._eventManager.emit("closePopupCodeBlockLanguages")},t.setLanguage=function(e){this._prevStoredLanguage=e,this._inputLanguage.value=e},t._storeInputLanguage=function(){var e=this._inputLanguage.value;this.setLanguage(e),this._onLanguageSelected&&this._onLanguageSelected(e),this._hidePopupCodeBlockLanguages()},t.getElement=function(){return this._wrapper},t._show=function(){h()(this._wrapper,{display:"inline-block"})},t._hide=function(){h()(this._wrapper,{display:"none"})},e}(),Ho=Po;function Fo(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var Uo="popup-editor-",qo="te-ok-button",jo="te-close-button",Wo="tui-popup-close-button",Vo='\n    <button type="button" class="'+Uo+'toggle-scroll"></button>\n    <button type="button" class="'+Uo+'toggle-preview"></button>\n    <button type="button" class="'+Uo+'toggle-fit"></button>\n    <button type="button" class="'+Wo+'"></button>\n',zo=function(e){function t(t){var n='\n            <div class="'+Uo+'body"></div>\n            <div class="te-button-section">\n                <button type="button" class="'+qo+'">'+cr.get("OK")+'</button>\n                <button type="button" class="'+jo+'">'+cr.get("Cancel")+"</button>\n            </div>\n        ";return t=u()({header:!0,title:"CodeBlock Editor",content:n,className:"tui-popup-code-block-editor",headerButtons:Vo,modal:!0},t),e.call(this,t)||this}Fo(t,e);var n=t.prototype;return n._initInstance=function(t){e.prototype._initInstance.call(this,t),this.eventManager=t.eventManager,this.convertor=t.convertor,this.languages=t.languages},n._initDOM=function(t){e.prototype._initDOM.call(this,t);var n=this.el,r=this.eventManager;this._body=n.querySelector("."+Uo+"body"),this._toggleFitButton=n.querySelector("."+Uo+"toggle-fit"),this._togglePreviewButton=n.querySelector("."+Uo+"toggle-preview"),this._toggleScrollButton=n.querySelector("."+Uo+"toggle-scroll"),this._okButton=n.querySelector("."+qo),this._closeButton=n.querySelector("."+jo),this._codeMirrorWrapper=this._createCodeBlockEditor(),this._previewWrapper=this._createPreview(),this._scrollSyncSplit=new xo(this._body,this._codeMirrorWrapper,this._previewWrapper,{eventManager:r}),this._updateFitWindowButton(),this._updatePreviewButton(),this._updateScrollButton(),this._codeBlockLanguagesCombo=this._createCodeBlockLanguagesCombo()},n._initDOMEvent=function(){var t=this;e.prototype._initDOMEvent.call(this),this.on("scroll",(function(e){return e.preventDefault()})),this.on("click ."+Uo+"toggle-fit",(function(){return t._toggleFitToWindow()})),this.on("click ."+Uo+"toggle-preview",(function(){return t._togglePreview()})),this.on("click ."+Uo+"toggle-scroll",(function(){return t._toggleScroll()})),this.on("click ."+qo,(function(){return t._save()})),this.on("click ."+jo,(function(){return t.hide()})),this.on("click ."+Uo+"close",(function(){return t.hide()})),this.on("click ."+Uo+"editor-wrapper",(function(e){e.target===t._codeMirrorWrapper&&t._focusEditor(!0)}))},n._initEditorEvent=function(){var t=this;e.prototype._initEditorEvent.call(this),this.eventManager.listen("openPopupCodeBlockEditor",(function(e){return t.eventManager.emit("closeAllPopup"),t.show(e),t})),this.eventManager.listen("closeAllPopup",this.hide.bind(this)),this.eventManager.listen("closePopupCodeBlockEditor",this.hide.bind(this))},n._createCodeBlockEditor=function(){var e=document.createElement("div");return e.className=Uo+"editor-wrapper",this._codeBlockEditor=new Ao(e,this.eventManager),e},n._createPreview=function(){var e=document.createElement("div");return this._codeBlockPreview=new Io(e,this.eventManager,this.convertor,this._codeBlockEditor),e},n._createCodeBlockLanguagesCombo=function(){var e=this,t=this.getTitleElement(),n=new Ho(this.eventManager,this.languages);return n.setOnLanguageSelected((function(t){e._codeBlockEditor.setLanguage(t),e._codeBlockEditor.refresh(),e._focusEditor()})),t.innerHTML="CodeBlock Editor",t.appendChild(n.getElement()),n},n._updateFitWindowButton=function(){dt["a"].toggleClass(this._toggleFitButton,"active",this.isFitToWindow())},n._updatePreviewButton=function(){dt["a"].toggleClass(this._togglePreviewButton,"active",this._scrollSyncSplit.isSplitView())},n._updateScrollButton=function(){this._scrollSyncSplit.isSplitView()?h()(this._toggleScrollButton,{display:"inline-block"}):h()(this._toggleScrollButton,{display:"none"}),dt["a"].toggleClass(this._toggleScrollButton,"active",this._scrollSyncSplit.isScrollSynced())},n._focusEditor=function(e){this._codeBlockEditor.focus(),e?this._codeBlockEditor.moveCursorToEnd():this._codeBlockEditor.moveCursorToStart()},n._togglePreview=function(){this._scrollSyncSplit.toggleSplitView(),this._updatePreviewButton(),this._updateScrollButton(),this._codeBlockEditor.refresh()},n._toggleFitToWindow=function(){this.toggleFitToWindow(),this._updateFitWindowButton(),this._codeBlockEditor.refresh()},n._toggleScroll=function(){this._scrollSyncSplit.toggleScrollSync(),this._updateScrollButton()},n._save=function(){this._codeBlockEditor.save(this._codeBlockElement),this.hide()},n._load=function(e){this._codeBlockElement=e,this._codeBlockEditor.load(e),this._codeBlockLanguagesCombo.setLanguage(this._codeBlockEditor.getLanguage()),this._focusEditor(),this._codeBlockPreview.refresh()},n.show=function(t){if(e.prototype.show.call(this),!t)throw new Error("should be called with codeBlockElement");this._load(t)},n.hide=function(){this.setFitToWindow(!1),this._codeBlockEditor&&this._codeBlockEditor.clear(),this._codeBlockPreview&&this._codeBlockPreview.clear(),this._codeBlockElement=null,e.prototype.hide.call(this)},t}(ei),Ko=zo;function Go(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var $o="te-toolbar-section",Yo="te-markdown-tab-section",Xo="te-editor-section",Zo="te-mode-switch-section",Qo=['<div class="tui-editor-defaultUI">','<div class="'+$o+'"><div class="'+Yo+'"></div></div>','<div class="'+Xo+'"></div>','<div class="'+Zo+'"></div>',"</div>"].join(""),Jo=function(){function e(e){Go(this,"name","default"),Go(this,"el",void 0),Go(this,"_toolbar",void 0),Go(this,"_container",void 0),Go(this,"_editorSection",void 0),Go(this,"_initialEditType",void 0),Go(this,"_editor",void 0),Go(this,"_markdownTabSection",void 0),Go(this,"_markdownTab",void 0),Go(this,"_modeSwitch",void 0),Go(this,"_popups",[]),this._editor=e,this._initialEditType=e.options.initialEditType,this._init(e.options),this._initEvent()}var t=e.prototype;return t._init=function(e){var t=e.el,n=e.toolbarItems,r=e.hideModeSwitch;this.el=dt["a"].createElementWith(Qo,t),this._container=t,this._editorSection=this.el.querySelector("."+Xo),this._editorSection.appendChild(this._editor.layout.getEditorEl()),this._initToolbar(this._editor.eventManager,n),this._initModeSwitch(this._editor.eventManager,r),this._initPopupAddLink(),this._initPopupAddImage(),this._initPopupAddTable(),this._initPopupAddHeading(),this._initPopupTableUtils(),this._initPopupCodeBlockLanguages(),this._initPopupCodeBlockEditor(),this._initMarkdownTab()},t._initEvent=function(){this._editor.eventManager.listen("hide",this.hide.bind(this)),this._editor.eventManager.listen("show",this.show.bind(this)),this._editor.eventManager.listen("changeMode",this._markdownTabControl.bind(this)),this._editor.eventManager.listen("changePreviewStyle",this._markdownTabControl.bind(this))},t._initToolbar=function(e,t){var n=new ui(e,t);this._toolbar=n,this.el.querySelector("."+$o).appendChild(n.el)},t._initModeSwitch=function(e,t){var n=this,r=this.el.querySelector("."+Zo),i="markdown"===this._initialEditType?Ci.TYPE.MARKDOWN:Ci.TYPE.WYSIWYG,o=new Ci(r,i,e);this._modeSwitch=o,t&&o.hide(),o.on("modeSwitched",(function(e){return n._editor.changeMode(e)}))},t._initMarkdownTab=function(){var e=this._editor;this._markdownTab=new pi({initName:cr.get("Write"),items:[cr.get("Write"),cr.get("Preview")],sections:[e.layout.getMdEditorContainerEl(),e.layout.getPreviewEl()]}),this._markdownTabSection=this.el.querySelector("."+Yo),this._markdownTabSection.appendChild(this._markdownTab.el),this._markdownTab.on("itemClick",(function(t){t===cr.get("Preview")?(e.eventManager.emit("previewNeedsRefresh"),e.eventManager.emit("changePreviewTabPreview"),e.eventManager.emit("closeAllPopup")):(e.getCodeMirror().focus(),e.eventManager.emit("changePreviewTabWrite"))}))},t._markdownTabControl=function(){this._editor.isMarkdownMode()&&"tab"===this._editor.getCurrentPreviewStyle()?(h()(this._markdownTabSection,{display:"block"}),this._markdownTab.activate(cr.get("Write"))):h()(this._markdownTabSection,{display:"none"})},t._initPopupAddLink=function(){this._popups.push(new Ni({target:this.el,editor:this._editor}))},t._initPopupAddImage=function(){this._popups.push(new Pi({target:this.el,eventManager:this._editor.eventManager}))},t._initPopupAddTable=function(){this._popups.push(new so({target:this._toolbar.el,eventManager:this._editor.eventManager,button:this.el.querySelector("button.tui-table"),css:{position:"absolute"}}))},t._initPopupAddHeading=function(){this._popups.push(new uo({target:this._toolbar.el,eventManager:this._editor.eventManager,button:this.el.querySelector("button.tui-heading"),css:{position:"absolute"}}))},t._initPopupTableUtils=function(){var e=this;this._editor.eventManager.listen("contextmenu",(function(t){dt["a"].parents(t.data.target,"[contenteditable=true] table").length>0&&(t.data.preventDefault(),e._editor.eventManager.emit("openPopupTableUtils",t.data))})),this._popups.push(new ji({target:this.el,eventManager:this._editor.eventManager}))},t._initPopupCodeBlockLanguages=function(){var e=this._editor;this._popups.push(new mo({target:this.el,eventManager:e.eventManager,languages:e.codeBlockLanguages}))},t._initPopupCodeBlockEditor=function(){this._popups.push(new Ko({target:this.el,eventManager:this._editor.eventManager,convertor:this._editor.convertor,languages:this._editor.codeBlockLanguages}))},t.getToolbar=function(){return this._toolbar},t.setToolbar=function(e){this._toolbar.destroy(),this._toolbar=e},t.getModeSwitch=function(){return this._modeSwitch},t.getEditorSectionHeight=function(){var e=this._editorSection.getBoundingClientRect();return e.bottom-e.top},t.getEditorHeight=function(){var e=this._container.getBoundingClientRect();return e.bottom-e.top},t.getPopupTableUtils=function(){var e;return this._popups.forEach((function(t){t instanceof ji&&(e=t)})),e},t.hide=function(){p()(this.el,"te-hide")},t.show=function(){m()(this.el,"te-hide")},t.remove=function(){dt["a"].remove(this.el),this._markdownTab.remove(),this._modeSwitch.remove(),this._toolbar.destroy(),this._popups.forEach((function(e){return e.remove()})),this._popups=[],Mr.hide()},t.createPopup=function(e){return new ei(e)},e}(),ea=Jo,ta=n(30),na=n(32),ra=n.n(na);function ia(e){var t=dt["a"].isElemNode,n=dt["a"].isTextNode,r=n(e.previousSibling)&&t(e.firstChild),i=n(e.nextSibling)&&t(e.lastChild);return!r&&!i}function oa(e,t,n){var r,i=/^(\s*)((?:.|\n)*\S)(\s*)$/m,o=t.match(i),a=o[1],s=o[2],l=o[3];if(ia(e))r=""+n+s+n;else{var c=e.nodeName.toLowerCase();r="<"+c+">"+s+"</"+c+">"}return""+a+r+l}var aa=ra.a.Renderer.factory(ra.a.gfmRenderer,{"EM, I":function(e,t){return this.isEmptyText(t)?"":oa(e,t,"*")},"STRONG, B":function(e,t){return this.isEmptyText(t)?"":oa(e,t,"**")},"DEL, S":function(e,t){return this.isEmptyText(t)?"":oa(e,t,"~~")}}),sa=n(31),la=function(e,t){var n,r=e.start,i=e.end;if(r.ch>=t){var o={line:r.line,ch:r.ch-t},a={line:i.line,ch:i.ch+t};n={from:o,to:a}}return n},ca=function(e,t){var n=t.length;return e.substr(n,e.length-2*n)},ua=function(e,t){return""+t+e+t},da=function(e,t,n,r,i){var o=la(t,n),a=!1;if(o){var s=o.from,l=o.to,c=e.getRange(s,l);r(c)&&(e.setSelection(s,l),e.replaceSelection(i(c),"around"),a=!0)}return a},ha=function(e,t,n,r){var i=!1;return n(t)&&(e.replaceSelection(r(t),"around"),i=!0),i},fa=function(e,t,n,r,i){var o=e.getCursor(),a=o.line,s=o.ch,l=e.getSelection(),c=n.length,u=function(e){return r.test(e)};if(!da(e,t,c,u,(function(e){return ca(e,n)}))&&!ha(e,l,u,(function(e){return ca(e,n)}))){var d=l.replace(i,"$1");e.replaceSelection(ua(d,n),"around")}var h=e.getSelection(),f=s;l||(u(h)?f+=c:f-=c,e.setCursor(a,f))},pa=/^(\*{2}|_{2}).*\1$/,ga=/[*_]{2,}([^*_]*)[*_]{2,}/g,ma="**",va=Zn["a"].command("markdown",{name:"Bold",keyMap:["CTRL+B","META+B"],exec:function(e){var t=e.getEditor(),n=t.getDoc(),r=e.getRange();fa(n,r,ma,pa,ga),t.focus()}}),ba=va,_a=/^(\*{3}|_{3}).*\1$/,ya=/^(\*{2}|_{2}).*\1$/,Ca=/^(\*|_).*\1$/,wa=/([^*_])[*_]([^*_]+)[*_]([^*_])/g,Ea=function(e){return _a.test(e)},Ta=function(e){return ya.test(e)},Na=function(e){return Ca.test(e)},ka="*",Sa="**",xa="***",La=ka.length,Ma=Sa.length,Aa=xa.length,Ba=function(e){return e?e.replace(wa,"$1$2$3"):""},Oa=function(e,t,n){var r=da.bind(this,e,n);return r(Aa,Ea,(function(e){return ca(e,ka)}))||r(Ma,Ta,(function(e){return ua(Ba(e),ka)}))||r(La,Na,(function(e){return ca(e,ka)}))||ha(e,t,Ea,(function(e){return ca(e,ka)}))||ha(e,t,Ta,(function(e){return ua(Ba(e),ka)}))||ha(e,t,Na,(function(e){return ca(e,ka)}))},Da=function(e,t){return da(e,t,Aa,Ea,(function(e){return ca(e,ka)}))||da(e,t,Ma,Ta,(function(e){return ua(e,ka)}))||da(e,t,La,Na,(function(){return""}))||e.replaceSelection(""+ka+ka,"around")},Ra=Zn["a"].command("markdown",{name:"Italic",keyMap:["CTRL+I","META+I"],exec:function(e){var t=e.getEditor(),n=t.getDoc(),r=n.getCursor(),i=r.line,o=r.ch,a=e.getRange(),s=n.getSelection();if(s)Oa(n,s,a)||n.replaceSelection(ua(Ba(s),ka),"around");else{Da(n,a);var l=n.getSelection(),c=o;Ea(l)||Na(l)&&!Ta(l)?c+=La:c-=La,n.setCursor(i,c)}t.focus()}}),Ia=Ra,Pa=/^~~.*~~$/,Ha=/~~([^~]*)~~/g,Fa="~~",Ua=Zn["a"].command("markdown",{name:"Strike",keyMap:["CTRL+S","META+S"],exec:function(e){var t=e.getEditor(),n=t.getDoc(),r=e.getRange();fa(n,r,Fa,Pa,Ha),t.focus()}}),qa=Ua,ja=/^> ?/,Wa=Zn["a"].command("markdown",{name:"Blockquote",keyMap:["ALT+Q","ALT+Q"],exec:function(e){var t,n=e.getEditor(),r=n.getDoc(),i=e.getCurrentRange(),o={line:i.from.line,ch:0},a={line:i.to.line,ch:r.getLineHandle(i.to.line).text.length},s=r.getRange(o,a),l=s.split("\n"),c=this._haveBlockquote(l);if(t=c?this._removeBlockquote(l):this._addBlockquote(l),r.replaceRange(t.join("\n"),o,a),c){var u=l.length;this._isBlockquoteWithSpace(l[u-1])?i.to.ch-=2:i.to.ch-=1}else i.to.ch+=2;r.setCursor(i.to),n.focus()},_haveBlockquote:function(e){for(var t=0;t<e.length;t+=1)if(!ja.test(e[t]))return!1;return!0},_addBlockquote:function(e){return e.map((function(e){return"> "+e}))},_removeBlockquote:function(e){return e.map((function(e){return e.replace(ja,"")}))},_isBlockquoteWithSpace:function(e){return/^> /.test(e)}}),Va=Wa,za=/^#+\s/g,Ka=Zn["a"].command("markdown",{name:"Heading",exec:function(e,t){var n=e.getEditor(),r=n.getDoc(),i=e.getCurrentRange(),o={line:i.from.line,ch:0},a={line:i.to.line,ch:r.getLineHandle(i.to.line).text.length},s=r.getLine(a.line).length,l=r.getRange(o,a),c=l.split("\n");Xe()(c).forEach((function(e,n){c[n]=Ga(e,t)})),r.replaceRange(c.join("\n"),o,a),i.to.ch+=r.getLine(a.line).length-s,r.setSelection(o,i.to),n.focus()}});function Ga(e,t){var n=e.match(za),r="";do{r+="#",t-=1}while(t>0);if(n){var i=e.split(n[0]);e=i[1]}return r+" "+e}var $a=Ka,Ya=Zn["a"].command("markdown",{name:"Paragraph",exec:function(e){var t=e.getEditor(),n=t.getDoc(),r=e.getCurrentRange(),i={line:r.from.line,ch:0},o={line:r.to.line,ch:n.getLineHandle(r.to.line).text.length},a=n.getLine(o.line).length,s=n.getRange(i,o),l=s.split("\n");Xe()(l).forEach((function(e,t){l[t]=Xa(e)})),n.replaceRange(l.join("\n"),i,o),r.to.ch+=n.getLine(o.line).length-a,n.setSelection(i,o),t.focus()}});function Xa(e){var t=/^(#{1,6}| *((?:\*|-|\d\.)(?: \[[ xX]])?)) /;return e.replace(t,"")}var Za=Ya,Qa=Zn["a"].command("markdown",{name:"HR",keyMap:["CTRL+L","META+L"],exec:function(e){var t=e.getEditor(),n=t.getDoc(),r="",i=e.getCurrentRange(),o={line:i.from.line,ch:i.from.ch},a={line:i.to.line,ch:i.to.ch};i.collapsed&&(r=n.getLine(o.line),o.ch=0,a.ch=n.getLineHandle(i.to.line).text.length),n.getLine(o.line).length?r+="\n\n* * *\n\n":r+="\n* * *\n",n.replaceRange(r,o,a),t.focus()}}),Ja=Qa,es=rr.decodeURIGraceful,ts=rr.encodeMarkdownCharacters,ns=rr.escapeMarkdownCharacters,rs=Zn["a"].command("markdown",{name:"AddLink",exec:function(e,t){var n=e.getEditor(),r=n.getDoc(),i=e.getCurrentRange(),o={line:i.from.line,ch:i.from.ch},a={line:i.to.line,ch:i.to.ch},s=t.linkText,l=t.url;s=es(s),s=ns(s),l=ts(l);var c="["+s+"]("+l+")";r.replaceRange(c,o,a),n.focus()}}),is=rs,os=rr.decodeURIGraceful,as=rr.encodeMarkdownCharacters,ss=rr.escapeMarkdownCharacters,ls=Zn["a"].command("markdown",{name:"AddImage",exec:function(e,t){var n=e.getEditor(),r=n.getDoc(),i=e.getCurrentRange(),o={line:i.from.line,ch:i.from.ch},a={line:i.to.line,ch:i.to.ch},s=t.altText,l=t.imageUrl;s=os(s),s=ss(s),l=as(l);var c="!["+s+"]("+l+")";r.replaceRange(c,o,a,"+addImage"),n.focus()}}),cs=ls,us=Zn["a"].command("markdown",{name:"UL",keyMap:["CTRL+U","META+U"],exec:function(e){var t=e.getCurrentRange(),n=e.componentManager.getManager("list");n.changeSyntax(t,"ul")}}),ds=us,hs=Zn["a"].command("markdown",{name:"OL",keyMap:["CTRL+O","META+O"],exec:function(e){var t=e.getCurrentRange(),n=e.componentManager.getManager("list");n.changeSyntax(t,"ol")}}),fs=hs,ps=Zn["a"].command("markdown",{name:"Indent",exec:function(e){var t=e.getEditor();t.execCommand("indentOrderedList")}}),gs=ps,ms=Zn["a"].command("markdown",{name:"Outdent",exec:function(e){var t=e.getEditor();t.execCommand("indentLessOrderedList")}}),vs=ms,bs=Zn["a"].command("markdown",{name:"Table",exec:function(e,t,n,r){var i=e.getEditor(),o=i.getDoc(),a="\n";i.getCursor().ch>0&&(a+="\n"),a+=_s(t,r),a+=ys(t,n-1,r),o.replaceSelection(a),r||i.setCursor(i.getCursor().line-n,2),e.focus()}});function _s(e,t){var n="|",r="|",i=0;while(e)t?(n+=" "+t[i]+" |",i+=1):n+="  |",r+=" --- |",e-=1;return n+"\n"+r+"\n"}function ys(e,t,n){for(var r="",i=e,o=0;o<t;o+=1){r+="|";for(var a=0;a<e;a+=1)n?(r+=" "+n[i]+" |",i+=1):r+="  |";r+="\n"}return r=r.replace(/\n$/g,""),r}var Cs=bs,ws=Zn["a"].command("markdown",{name:"Task",keyMap:["ALT+T","ALT+T"],exec:function(e){var t=e.getCurrentRange(),n=e.componentManager.getManager("list");n.changeSyntax(t,"task")}}),Es=ws,Ts=/^`([^`]+)`$/,Ns=/`([^`]+)`/g,ks=Zn["a"].command("markdown",{name:"Code",keyMap:["SHIFT+CTRL+C","SHIFT+META+C"],exec:function(e){var t,n=e.getEditor(),r=n.getDoc(),i=r.getSelection(),o=n.getCursor(),a=this.hasStrikeSyntax(i);a?(t=this.remove(i),t=this._removeCodeSyntax(t)):(t=this._removeCodeSyntax(i),t=this.append(t)),r.replaceSelection(t,"around"),i||a||this.setCursorToCenter(r,o,a),n.focus()},setCursorToCenter:function(e,t,n){var r=n?-1:1;e.setCursor(t.line,t.ch+r)},hasStrikeSyntax:function(e){return Ts.test(e)},append:function(e){return"`"+e+"`"},remove:function(e){return e.substr(1,e.length-2)},_removeCodeSyntax:function(e){return e?e.replace(Ns,"$1"):""}}),Ss=ks,xs=Zn["a"].command("markdown",{name:"CodeBlock",keyMap:["SHIFT+CTRL+P","SHIFT+META+P"],exec:function(e){var t=e.getEditor(),n=t.getDoc(),r=e.getCurrentRange(),i=["```",n.getSelection(),"```"],o=1;0!==r.from.ch&&(i.unshift(""),o+=1),r.to.ch!==n.getLine(r.to.line).length&&i.push(""),n.replaceSelection(i.join("\n")),t.setCursor(r.from.line+o,0),t.focus()}}),Ls=xs,Ms=Zn["a"].command("wysiwyg",{name:"Bold",keyMap:["CTRL+B","META+B"],exec:function(e){var t=e.getEditor(),n=e.componentManager.getManager("tableSelection");if(e.focus(),t.hasFormat("table")&&n.getSelectedCells().length){n.styleToSelectedCells(As);var r=t.getSelection();r.collapse(!0),t.setSelection(r)}else As(t),dt["a"].optimizeRange(t.getSelection(),"B")}});function As(e){e.hasFormat("b")||e.hasFormat("strong")?e.changeFormat(null,{tag:"b"}):e.hasFormat("PRE")||(e.hasFormat("code")&&e.changeFormat(null,{tag:"code"}),e.bold())}var Bs=Ms,Os=Zn["a"].command("wysiwyg",{name:"Italic",keyMap:["CTRL+I","META+I"],exec:function(e){var t=e.getEditor(),n=e.componentManager.getManager("tableSelection");if(e.focus(),t.hasFormat("table")&&n.getSelectedCells().length){n.styleToSelectedCells(Ds);var r=t.getSelection();r.collapse(!0),t.setSelection(r)}else Ds(t),dt["a"].optimizeRange(t.getSelection(),"I")}});function Ds(e){e.hasFormat("i")||e.hasFormat("em")?e.changeFormat(null,{tag:"i"}):e.hasFormat("PRE")||(e.hasFormat("code")&&e.changeFormat(null,{tag:"code"}),e.italic())}var Rs=Os,Is=Zn["a"].command("wysiwyg",{name:"Strike",keyMap:["CTRL+S","META+S"],exec:function(e){var t=e.getEditor(),n=e.componentManager.getManager("tableSelection");if(e.focus(),t.hasFormat("table")&&n.getSelectedCells().length){n.styleToSelectedCells(Ps);var r=t.getSelection();r.collapse(!0),t.setSelection(r)}else Ps(t),dt["a"].optimizeRange(t.getSelection(),"S")}});function Ps(e){e.hasFormat("S")?e.changeFormat(null,{tag:"S"}):e.hasFormat("PRE")||(e.hasFormat("code")&&e.changeFormat(null,{tag:"code"}),e.strikethrough())}var Hs=Is,Fs=Zn["a"].command("wysiwyg",{name:"Blockquote",keyMap:["ALT+Q","ALT+Q"],exec:function(e){var t=e.getEditor();e.focus(),t.hasFormat("TABLE")||t.hasFormat("PRE")||(t.hasFormat("BLOCKQUOTE")?t.decreaseQuoteLevel():t.increaseQuoteLevel())}}),Us=Fs,qs=rr.decodeURIGraceful,js=rr.encodeMarkdownCharacters,Ws=Zn["a"].command("wysiwyg",{name:"AddImage",exec:function(e,t){var n=e.getEditor(),r=t.altText,i=t.imageUrl;r=qs(r),i=js(i),e.focus(),n.hasFormat("PRE")||n.insertImage(i,{alt:r})}}),Vs=Ws,zs=rr.decodeURIGraceful,Ks=rr.encodeMarkdownCharacters,Gs=Zn["a"].command("wysiwyg",{name:"AddLink",exec:function(e,t){var n=e.getEditor(),r=e.getLinkAttribute(),i=t.url,o=t.linkText;if(o=zs(o),i=Ks(i),e.focus(),!n.hasFormat("PRE"))if(n.removeAllFormatting(),n.getSelectedText())n.makeLink(i,r);else{var a=n.createElement("A",u()({href:i},r));a.textContent=o,n.insertElement(a)}}}),$s=Gs,Ys=Zn["a"].command("wysiwyg",{name:"HR",keyMap:["CTRL+L","META+L"],exec:function(e){var t=e.getEditor(),n=t.getSelection();if(n.collapsed&&!t.hasFormat("TABLE")&&!t.hasFormat("PRE")){var r=document.createElement("hr"),i=dt["a"].getChildNodeByOffset(n.startContainer,n.startOffset),o=dt["a"].getTopNextNodeUnder(i,e.getBody());if(o&&!dt["a"].isTextNode(o))while(o&&"false"===o.getAttribute("contenteditable"))o=o.nextSibling;o||(o=dt["a"].createEmptyLine(),dt["a"].append(e.getBody(),o)),t.modifyBlocks((function(e){return e.appendChild(r),e}));var a=r.previousSibling;a&&dt["a"].isTextNode(a)&&0===dt["a"].getTextLength(a)&&r.parentNode.removeChild(a),r.parentNode.replaceChild(dt["a"].createHorizontalRule(),r),n.selectNodeContents(o),n.collapse(!0),t.setSelection(n),t.saveUndoState(n)}e.focus()}}),Xs=Ys,Zs=Zn["a"].command("wysiwyg",{name:"Heading",exec:function(e,t){var n=e.getEditor(),r="h1, h2, h3, h4, h5, h6, div";e.focus(),n.hasFormat("TABLE")||n.hasFormat("PRE")||n.modifyBlocks((function(e){var n=dt["a"].children(e,r);return Xe()(n).forEach((function(e){var n="h"+t;if("DIV"===dt["a"].getNodeName(e))dt["a"].wrap(e,n);else{var r=document.createElement(n);dt["a"].insertBefore(r,e),r.innerHTML=e.innerHTML,dt["a"].remove(e)}})),e}))}}),Qs=Zs,Js=Zn["a"].command("wysiwyg",{name:"Paragraph",exec:function(e){var t=e.getEditor();e.focus(),t.hasFormat("TABLE")||t.hasFormat("PRE")||t.modifyBlocks((function(e){var t=document.createDocumentFragment();return Xe()(e.childNodes).forEach((function(e){e.nodeName.match(/h\d/i)?el(t,e.children):e.nodeName.match(/ul|ol/i)?dt["a"].findAll(e,"li").forEach((function(e){el(t,e.children)})):t.appendChild(e)})),t}))}});function el(e,t){Xe()(t).forEach((function(t){e.appendChild(t.cloneNode(!0))}))}var tl=Js,nl=Zn["a"].command("wysiwyg",{name:"UL",keyMap:["CTRL+U","META+U"],exec:function(e){var t=e.getEditor(),n=t.getSelection(),r=e.componentManager.getManager("list"),i=n.startContainer,o=n.endContainer,a=n.startOffset,s=n.endOffset,l=[];if(e.focus(),t.saveUndoState(n),r.isAvailableMakeListInTable())l=r.createListInTable(n,"UL");else for(var c=r.getLinesOfSelection(i,o),u=0;u<c.length;u+=1){var d=this._changeFormatToUnorderedListIfNeed(e,c[u]);d&&l.push(d)}l.length&&r.adjustRange(i,o,a,s,l)},_changeFormatToUnorderedListIfNeed:function(e,t){var n,r=e.getEditor(),i=r.getSelection(),o=e.componentManager.getManager("task");return r.hasFormat("PRE")||(i.setStart(t,0),i.collapse(!0),r.setSelection(i),r.hasFormat("LI")?(e.saveSelection(i),o.unformatTask(i.startContainer),r.replaceParent(i.startContainer,"ol","ul"),e.restoreSavedSelection()):(e.unwrapBlockTag(),r.makeUnorderedList()),n=r.getSelection().startContainer),n}}),rl=nl,il=Zn["a"].command("wysiwyg",{name:"OL",keyMap:["CTRL+O","META+O"],exec:function(e){var t=e.getEditor(),n=t.getSelection(),r=e.componentManager.getManager("list"),i=n.startContainer,o=n.startOffset,a=n.endContainer,s=n.endOffset,l=[];if(e.focus(),t.saveUndoState(n),r.isAvailableMakeListInTable())l=r.createListInTable(n,"OL");else for(var c=r.getLinesOfSelection(i,a),u=0;u<c.length;u+=1){var d=this._changeFormatToOrderedListIfNeed(e,c[u]);d&&l.push(d)}l.length&&r.adjustRange(i,a,o,s,l)},_changeFormatToOrderedListIfNeed:function(e,t){var n,r=e.getEditor(),i=r.getSelection(),o=e.componentManager.getManager("task");return r.hasFormat("PRE")||(i.setStart(t,0),i.collapse(!0),r.setSelection(i),r.hasFormat("LI")?(e.saveSelection(i),o.unformatTask(i.startContainer),r.replaceParent(i.startContainer,"ul","ol"),e.restoreSavedSelection()):(e.unwrapBlockTag(),r.makeOrderedList()),n=r.getSelection().startContainer),n}}),ol=il,al=Zn["a"].command("wysiwyg",{name:"Table",exec:function(e,t,n,r){var i,o=e.getEditor(),a=e.componentManager.getManager("table").getTableIDClassName();!o.getSelection().collapsed||o.hasFormat("TABLE")||o.hasFormat("PRE")?e.focus():(i='<table class="'+a+'">',i+=ll(t,r),i+=cl(t,n-1,r),i+="</table>",o.insertHTML(i),e.focus(),r||sl(o,e.getBody().querySelector("."+a)))}});function sl(e,t){var n=e.getSelection();n.selectNodeContents(t.querySelector("th")),n.collapse(!0),e.setSelection(n)}function ll(e,t){var n="<thead><tr>",r=0;while(e)n+="<th>",t&&(n+=t[r],r+=1),n+="</th>",e-=1;return n+="</tr></thead>",n}function cl(e,t,n){for(var r="<tbody>",i=e,o=0;o<t;o+=1){r+="<tr>";for(var a=0;a<e;a+=1)r+="<td>",n&&(r+=n[i],i+=1),r+="</td>";r+="</tr>"}return r+="</tbody>",r}var ul=al,dl=Zn["a"].command("wysiwyg",{name:"AddRow",exec:function(e){var t,n,r=e.getEditor(),i=r.getSelection().cloneRange(),o=hl(e);if(e.focus(),r.hasFormat("TD")){r.saveUndoState(i),t=dt["a"].closest(i.startContainer,"tr");for(var a=0;a<o;a+=1)n=fl(t),dt["a"].insertAfter(n,t);pl(r,n)}else if(r.hasFormat("TH")){r.saveUndoState(i),t=dt["a"].closest(i.startContainer,"tr");var s=dt["a"].parents(t,"thead"),l=s[0],c=l.nextSibling;if(ft()(c,"tbody")){var u=dt["a"].children(c,"tr");t=u[0]}for(var d=0;d<o;d+=1)n=fl(t),dt["a"].insertBefore(n,t);pl(r,n)}}});function hl(e){var t=e.componentManager.getManager("tableSelection"),n=t.getSelectedCells(),r=1;if(n.length>1){var i=n[0],o=n[n.length-1],a=t.getSelectionRangeFromTable(i,o);r=a.to.row-a.from.row+1}return r}function fl(e){var t=e.cloneNode(!0),n=it.a.msie?"":"<br />";return dt["a"].findAll(t,"td").forEach((function(e){e.innerHTML=n})),t}function pl(e,t){var n=e.getSelection();n.selectNodeContents(t.querySelector("td")),n.collapse(!0),e.setSelection(n)}var gl=dl,ml=Zn["a"].command("wysiwyg",{name:"AddCol",exec:function(e){var t,n=e.getEditor(),r=n.getSelection().cloneRange(),i=vl(e);e.focus(),n.hasFormat("TR")&&(n.saveUndoState(r),t=bl(r),_l(t,i),yl(n,t))}});function vl(e){var t=e.componentManager.getManager("tableSelection"),n=t.getSelectedCells(),r=1;if(n.length>0){var i=n[0].parentNode.querySelectorAll("td, th").length;r=Math.min(i,n.length)}return r}function bl(e){var t=e.startContainer;return"TD"!==dt["a"].getNodeName(t)&&"TH"!==dt["a"].getNodeName(t)&&(t=dt["a"].parentsUntil(t,"tr")),t}function _l(e,t){void 0===t&&(t=1);var n=dt["a"].parents(e,"table"),r=n[0];if(r){var i,o=Jn()(e,Xe()(e.parentNode.childNodes));dt["a"].findAll(r,"tr").forEach((function(e){for(var n="TBODY"===dt["a"].getNodeName(e.parentNode),r=it.a.msie,a=e.children[o],s=0;s<t;s+=1)i=n?document.createElement("td"):document.createElement("th"),r||i.appendChild(document.createElement("br")),dt["a"].insertAfter(i,a)}))}}function yl(e,t){var n=e.getSelection();n.selectNodeContents(t.nextSibling),n.collapse(!0),e.setSelection(n)}var Cl=ml,wl=Zn["a"].command("wysiwyg",{name:"RemoveRow",exec:function(e){var t=e.getEditor(),n=t.getSelection().cloneRange(),r=dt["a"].parents(n.startContainer,"table"),i=r[0],o=e.componentManager.getManager("tableSelection"),a=e.componentManager.getManager("table"),s=Nl(n,o,i),l=i.querySelectorAll("tbody tr").length;if(e.focus(),(t.hasFormat("TD")||t.hasFormat("TABLE"))&&l>1){t.saveUndoState(n);var c=s[0],u=s[s.length-1],d=u&&u.nextSibling?u.nextSibling:c&&c.previousSibling;d&&El(t,n,d,a),s.forEach((function(e){return dt["a"].remove(e)}))}o.removeClassAttrbuteFromAllCellsIfNeed()}});function El(e,t,n,r){var i=n.querySelector("td");t.setStart(i,0),t.collapse(!0),r.setLastCellNode(i),e.setSelection(t)}function Tl(e,t,n){var r=n.querySelectorAll("tbody tr").length,i=dt["a"].parents(e,"thead").length,o=t.from.row,a=t.to.row;i&&(o+=1);var s=(1===o||i)&&a===r;return s&&(a-=1),dt["a"].findAll(n,"tr").slice(o,a+1)}function Nl(e,t,n){var r,i,o=t.getSelectedCells();if(o.length)r=t.getSelectionRangeFromTable(o[0],o[o.length-1]),i=Tl(o[0],r,n);else{var a=dt["a"].closest(e.startContainer,"td,th");r=t.getSelectionRangeFromTable(a,a),i=Tl(a,r,n)}return i}var kl=wl,Sl=Zn["a"].command("wysiwyg",{name:"RemoveCol",exec:function(e){var t=e.getEditor(),n=t.getSelection().cloneRange(),r=dt["a"].parents(n.startContainer,"table"),i=r[0],o=e.componentManager.getManager("table"),a=e.componentManager.getManager("tableSelection"),s=dt["a"].closest(n.startContainer,"table").querySelectorAll("thead tr th").length>1;if(e.focus(),n.collapse(!0),t.setSelection(n),t.hasFormat("TR",null,n)&&s){var l=i.querySelectorAll("tbody tr"),c=l.length?l[0].querySelectorAll("td").length:0,u=a.getSelectedCells();if(u.length<c){var d;if(t.saveUndoState(n),u.length>1){var h=u[u.length-1],f=u[0];d=h.nextSibling?h.nextSibling:f.previousSibling,Ll(u)}else{var p=xl(n);d=p.nextSibling?p.nextSibling:p.previousSibling,Ml(p)}Al(t,d,o)}}}});function xl(e){var t=e.startContainer;return"TD"!==dt["a"].getNodeName(t)&&"TH"===!dt["a"].getNodeName(t)&&(t=dt["a"].parentsUntil(t,"tr")),t}function Ll(e){for(var t=e.length,n=0;n<t;n+=1){var r=e[n];r&&Ml(e[n])}}function Ml(e){var t=dt["a"].parents(e,"table"),n=t[0];if(n){var r=Jn()(e,Xe()(e.parentNode.childNodes));dt["a"].findAll(n,"tr").forEach((function(e){var t=e.children[r];dt["a"].remove(t)}))}}function Al(e,t,n){var r=t;if(t&&dt["a"].isContain(document.body,t)){var i=e.getSelection();i.selectNodeContents(t),i.collapse(!0),e.setSelection(i),n.setLastCellNode(r)}}var Bl=Sl,Ol=Zn["a"].command("wysiwyg",{name:"AlignCol",exec:function(e,t){var n=e.getEditor(),r=n.getSelection().cloneRange(),i=e.componentManager.getManager("tableSelection"),o=Il(r,i);if(e.focus(),n.hasFormat("TR")){n.saveUndoState(r);var a=dt["a"].parents(r.startContainer,"table"),s=a[0],l=Rl(s,o);Dl(s,t,l)}i.removeClassAttrbuteFromAllCellsIfNeed()}});function Dl(e,t,n){var r=n.isDivided||!1,i=n.startColumnIndex,o=n.endColumnIndex,a=dt["a"].findAll(e,"tr"),s=a.length?a[0].querySelectorAll("td,th").length:0;a.forEach((function(e){var n=Xe()(dt["a"].children(e,"td,th"));n.forEach((function(e,n){(r&&(i<=n&&n<=s||n<=o)||i<=n&&n<=o)&&e.setAttribute("align",t)}))}))}function Rl(e,t){var n,r,i,o=e.querySelectorAll("tr"),a=o.length?o[0].querySelectorAll("td,th").length:0,s=t.from,l=t.to;return s.row===l.row?(n=s.cell,r=l.cell):s.row<l.row&&(s.cell<=l.cell?(n=0,r=a-1):(n=s.cell,r=l.cell,i=!0)),{startColumnIndex:n,endColumnIndex:r,isDivided:i}}function Il(e,t){var n,r,i=t.getSelectedCells();if(i.length)n=t.getSelectionRangeFromTable(i[0],i[i.length-1]);else{var o=e.startContainer;r=dt["a"].isTextNode(o)?dt["a"].parent(o,"td,th"):o,n=t.getSelectionRangeFromTable(r,r)}return n}var Pl=Ol,Hl=Zn["a"].command("wysiwyg",{name:"RemoveTable",exec:function(e){var t=e.getEditor(),n=t.getSelection().cloneRange();t.hasFormat("TABLE")&&(t.saveUndoState(n),dt["a"].remove(dt["a"].closest(n.startContainer,"table"))),e.focus()}}),Fl=Hl,Ul=Zn["a"].command("wysiwyg",{name:"Indent",exec:function(e){var t,n,r,i=e.componentManager.getManager("list"),o=e.getEditor().getSelection(),a=dt["a"].closest(o.startContainer,"li"),s=a&&a.previousSibling;if(s){var l=a.querySelector("li");if(e.getEditor().saveUndoState(),n=a.className,t=s.className,a.className="",s.className="",l){r=l.className;var c=Xe()(l.children).filter((function(e){return ft()(e,"div")}));c.length||(l.className="")}e.getEditor().increaseListLevel(),i.mergeList(a),a.className=n,s.className=t,l&&(l.className=r)}}}),ql=Ul,jl=Zn["a"].command("wysiwyg",{name:"Outdent",exec:function(e){var t=Vl(e);if(t&&Wl(t)){e.getEditor().saveUndoState();var n=t.className;e.getEditor().decreaseListLevel(),t=Vl(e),t&&n&&(t.className=n)}}});function Wl(e){var t=dt["a"].getNodeName(e.nextSibling);return"OL"!==t&&"UL"!==t}function Vl(e){var t=e.getEditor().getSelection();return dt["a"].closest(t.startContainer,"li")}var zl=jl,Kl=Zn["a"].command("wysiwyg",{name:"Task",keyMap:["ALT+T","ALT+T"],exec:function(e){var t=e.getEditor(),n=t.getSelection(),r=e.componentManager.getManager("list"),i=n.startContainer,o=n.endContainer,a=n.startOffset,s=n.endOffset,l=[];if(e.focus(),t.saveUndoState(n),r.isAvailableMakeListInTable())l=r.createListInTable(n,"TASK");else for(var c=r.getLinesOfSelection(i,o),u=0;u<c.length;u+=1){var d=this._changeFormatToTaskIfNeed(e,c[u]);d&&l.push(d)}l.length&&r.adjustRange(i,o,a,s,l)},_changeFormatToTaskIfNeed:function(e,t){var n,r=e.getEditor(),i=r.getSelection(),o=e.componentManager.getManager("task");return r.hasFormat("PRE")||(i.setStart(t,0),i.collapse(!0),r.setSelection(i),r.hasFormat("li")||(r.makeUnorderedList(),t=r.getSelection().startContainer),Bt()(t,"task-list-item")?o.unformatTask(t):o.formatTask(t),n=r.getSelection().startContainer),n}}),Gl=Kl,$l=Zn["a"].command("wysiwyg",{name:"Code",keyMap:["SHIFT+CTRL+C","SHIFT+META+C"],exec:function(e){var t=e.getEditor(),n=e.componentManager.getManager("tableSelection"),r=Xl.bind(null,e.getEditor());if(e.focus(),t.hasFormat("table")&&n.getSelectedCells().length){n.styleToSelectedCells(r);var i=t.getSelection();i.collapse(!0),t.setSelection(i)}else r(t)}});function Yl(e){"CODE"===dt["a"].getNodeName(e.startContainer.nextSibling)&&0===dt["a"].getTextLength(e.startContainer.nextSibling)&&dt["a"].remove(e.startContainer.nextSibling)}function Xl(e,t){if(!t.hasFormat("PRE")&&t.hasFormat("code"))t.changeFormat(null,{tag:"code"}),Yl(e.getSelection().cloneRange());else if(!t.hasFormat("a")&&!t.hasFormat("PRE")){t.hasFormat("b")?t.removeBold():t.hasFormat("i")&&t.removeItalic(),t.changeFormat({tag:"code"});var n=t.getSelection().cloneRange();n.setStart(n.endContainer,n.endOffset),n.collapse(!0),t.setSelection(n)}}var Zl=$l,Ql="te-content-codeblock-temp",Jl="data-te-codeblock",ec=Zn["a"].command("wysiwyg",{name:"CodeBlock",keyMap:["SHIFT+CTRL+P","SHIFT+META+P"],exec:function(e,t){var n=e.getEditor(),r=n.getSelection().cloneRange();if(!n.hasFormat("PRE")&&!n.hasFormat("TABLE")){var i=Jl+' class = "'+Ql+'"';t&&(i+=' data-language="'+t+'"');var o=nc(r,e);n.insertHTML("<pre "+i+">"+o+"</pre>"),tc(e.getBody().querySelector("."+Ql),e)}e.focus()}});function tc(e,t){var n=t.getEditor().getSelection().cloneRange();m()(e,Ql),n.setStartBefore(e.firstChild),n.collapse(!0),t.getEditor().setSelection(n)}function nc(e,t){var n,r=t.componentManager.getManager("codeblock");if(e.collapsed)n="<br>";else{var i=e.extractContents(),o=Xe()(i.childNodes),a=document.createElement("div");a.appendChild(r.prepareToPasteOnCodeblock(o)),n=a.innerHTML}return n}var rc=ec,ic=200,oc=15,ac=null,sc=null;function lc(e,t){var n=t.syncScrollTop,r=t.releaseEventBlock;clearTimeout(sc),n(e),sc=setTimeout((function(){r()}),oc)}function cc(e,t,n){var r=t-e,i=Date.now(),o=function o(){var a,s=Date.now(),l=(s-i)/ic;ac&&clearTimeout(ac),l<1?(a=e+r*Math.cos((1-l)*Math.PI/2),lc(Math.ceil(a),n),ac=setTimeout(o,1)):(lc(t,n),ac=null)};o()}var uc=n(18),dc=n(24),hc=.5,fc=!1,pc=null;function gc(e,t){var n=Object(dc["a"])(t),r=n||e.offsetHeight;return n||Object(dc["d"])(t,r),r}function mc(e,t,n,r,i){var o=Object(he["f"])(n),a=e.lineInfo(t).handle.height,s=gc(i,0),l=r.getBoundingClientRect().top-i.getBoundingClientRect().top,c=-s*hc;if(Object(he["j"])(n)){var u=(t-o+1)*a;c=u,l+=u}return{top:l,additionalScrollTop:c}}function vc(e,t,n){var r=t._previewContent,i=t.el,o=e.cm,a=e.toastMark,s=o.getScrollInfo(),l=s.left,c=s.top,u=s.height,d=s.clientHeight,h=u-c<=d,f=i.scrollTop,p=h?i.scrollHeight:0;if(c&&!h){var g=n?o.coordsChar({left:l,top:c},"local"):o.getCursor("from"),m=g.line,v=a.findFirstNodeAtLine(m+1);if(!v||Object(he["g"])(v))return;var b=Object(uc["f"])(v),_=b.node,y=b.mdNode,C=Object(he["f"])(y),w=gc(i,0);if(p=Object(uc["g"])(_,r)||_.offsetTop,n){if(Object(uc["h"])(y)){var E=gc(_,y.id),T=o.heightAtLine(C-1,"local"),N=Object(uc["c"])(y,o);p+=Object(uc["b"])(c,T,N,E);var k={latestScrollTop:pc,scrollTop:c,targetScrollTop:p,sourceScrollTop:f};if(p=Object(uc["d"])(k),pc=c,p===f)return}}else{var S=mc(o,m,y,_,i),x=S.top,L=S.additionalScrollTop;if(x>0&&x<w)return;p+=L,pc=null}}fc=!0;var M={syncScrollTop:function(e){return i.scrollTop=e},releaseEventBlock:function(){return fc=!1}};cc(f,p,M)}function bc(){return fc}var _c=!1,yc=null;function Cc(e,t,n){var r=Object(dc["a"])(t),i=Object(dc["b"])(t),o=r||e.offsetHeight,a=i||Object(uc["g"])(e,n)||e.offsetTop;return r||Object(dc["d"])(t,o),i||Object(dc["e"])(t,a),{offsetHeight:o,offsetTop:a}}function wc(e,t){while(!e.getAttribute("data-nodeid")&&e.parentElement!==t)e=e.parentElement;return e}function Ec(e,t,n){var r=e.toastMark,i=e.cm,o=t.el,a=o.scrollTop,s=o.clientHeight,l=o.scrollHeight,c=t._previewContent,u=l-a<=s,d=i.getScrollInfo(),h=d.left,f=d.top,p=d.height,g=u?p:0;if(a&&n&&!u){if(n=wc(n,c),!n.getAttribute("data-nodeid"))return;var m=i.coordsChar({left:h,top:f},"local"),v=m.line,b=Number(n.getAttribute("data-nodeid")),_=Object(uc["f"])(r.findNodeById(b)),y=_.mdNode,C=_.node,w=Object(he["f"])(y);if(g=i.heightAtLine(w-1,"local"),Object(he["i"])(y)&&(g+=Object(uc["e"])(i,w,v)),Object(uc["h"])(y)){var E=Object(uc["c"])(y,i),T=Cc(C,b,c),N=T.offsetHeight,k=T.offsetTop;g+=Object(uc["b"])(a,k,N,E);var S={latestScrollTop:yc,scrollTop:a,targetScrollTop:g,sourceScrollTop:f};if(g=Object(uc["d"])(S),yc=a,g===f)return}}_c=!0;var x={syncScrollTop:function(e){return i.scrollTo(0,e)},releaseEventBlock:function(){return _c=!1}};cc(f,g,x)}function Tc(){return _c}var Nc="tui-scrollsync",kc="tui-scrollsync active",Sc=!0;function xc(){return Sc}function Lc(e){if(!e.isViewer()&&"default"===e.getUI().name){var t=e.i18n,n={ACTIVE:t.get("Auto scroll enabled"),INACTIVE:t.get("Auto scroll disabled")},r=e.getUI().getToolbar(),i=document.createElement("button");i.className=kc,r.addItem("divider"),r.addItem({type:"button",options:{command:"scrollSyncToggle",tooltip:n.ACTIVE,el:i}});var o=r.getItems(),a=o[o.length-2].el,s=o[o.length-1];Bc(e,s,a),Mc(e,s,a),Ac(e,s,n)}}function Mc(e,t,n){e.on("changeMode",(function(){return Bc(e,t,n)})),e.on("changePreviewStyle",(function(){return Bc(e,t,n)}))}function Ac(e,t,n){e.addCommand("markdown",{name:"scrollSyncToggle",exec:function(){Sc=!Sc,t._onOut(),Sc?(t.el.className=kc,t.setTooltip(n.ACTIVE)):(t.el.className=Nc,t.setTooltip(n.INACTIVE)),t._onOver()}})}function Bc(e,t,n){"vertical"===e.mdPreviewStyle&&"markdown"===e.currentMode?(h()(t.el,{display:"inline-block"}),h()(n,{display:"inline-block"})):(h()(t.el,{display:"none"}),h()(n,{display:"none"}))}var Oc=!0;function Dc(e){var t=e.mdEditor,n=e.preview;Lc(e),Ic(t,n),Rc(t,n),t.cm.on("change",(function(){return Oc=!1}))}function Rc(e,t){e.eventManager.listen("previewRenderAfter",(function(){setTimeout((function(){xc()&&vc(e,t),Oc=!0}),200)}))}function Ic(e,t){var n=e.eventManager;n.listen("scroll",(function(n){var r=n.source,i=n.data;xc()&&Oc&&t.isVisible()&&("markdown"!==r||Tc()?"preview"!==r||bc()||Ec(e,t,i):vc(e,t,!0))}))}function Pc(){return Pc=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pc.apply(this,arguments)}var Hc=[],Fc=function(){function e(e){var t=this;this.initialHtml=e.el.innerHTML,e.el.innerHTML="",this.options=u()({previewStyle:"tab",previewHighlight:!0,initialEditType:"markdown",height:"300px",minHeight:"200px",language:"en-US",useDefaultHTMLSanitizer:!0,useCommandShortcut:!0,usageStatistics:!0,toolbarItems:["heading","bold","italic","strike","divider","hr","quote","divider","ul","ol","task","indent","outdent","divider","table","image","link","divider","code","codeblock"],hideModeSwitch:!1,linkAttribute:null,extendedAutolinks:!1,customConvertor:null,customHTMLRenderer:null,referenceDefinition:!1,customHTMLSanitizer:null},e),this.codeBlockLanguages=[],this.eventManager=new Xn["a"],this.importManager=new rr(this.eventManager),this.commandManager=new Zn["a"](this,{useCommandShortcut:this.options.useCommandShortcut});var n=Object(y["c"])(this.options.linkAttribute),r=Object(sa["a"])(this.options.plugins),o=r.renderer,a=r.parser,s=r.plugins,l=this.options,c=l.customHTMLRenderer,d=l.customHTMLSanitizer,h=l.extendedAutolinks,f=l.referenceDefinition,p=l.useDefaultHTMLSanitizer,g={linkAttribute:n,customHTMLRenderer:Pc({},o,{},c),extendedAutolinks:h,referenceDefinition:f,customParser:a};this.options.customConvertor?this.convertor=new this.options.customConvertor(this.eventManager,g):this.convertor=new ir["a"](this.eventManager,g);var m=d||(p?pt["a"]:null);m&&this.convertor.initHtmlSanitizer(m),this.options.hooks&&i()(this.options.hooks,(function(e,n){return t.addHook(n,e)})),this.options.events&&i()(this.options.events,(function(e,n){return t.on(n,e)})),this.layout=new Yn(e,this.eventManager),this.i18n=cr,this.i18n.setCode(this.options.language),this.setUI(this.options.UI||new ea(this)),this.toastMark=new v["ToastMark"]("",{disallowedHtmlBlockTags:["br"],extendedAutolinks:h,referenceDefinition:f,disallowDeepHeading:!0,customParser:a}),this.mdEditor=Ge.factory(this.layout.getMdEditorContainerEl(),this.eventManager,this.toastMark,this.options),this.preview=new $e["a"](this.layout.getPreviewEl(),this.eventManager,this.convertor,Pc({},g,{isViewer:!1,highlight:this.options.previewHighlight})),this.wwEditor=Kn.factory(this.layout.getWwEditorContainerEl(),this.eventManager,{sanitizer:m,linkAttribute:n}),this.toMarkOptions={gfm:!0,renderer:aa},s&&Object(sa["b"])(s,this),this.changePreviewStyle(this.options.previewStyle),this.changeMode(this.options.initialEditType,!0),this.minHeight(this.options.minHeight),this.height(this.options.height),this.setMarkdown(this.options.initialValue,!1),this.options.placeholder&&this.setPlaceholder(this.options.placeholder),this.options.initialValue||this.setHtml(this.initialHtml,!1),this.eventManager.emit("load",this),Hc.push(this),this._addDefaultCommands(),this.options.usageStatistics&&Object(y["d"])(),Dc(this)}var t=e.prototype;return t.changePreviewStyle=function(e){this.layout.changePreviewStyle(e),this.mdPreviewStyle=e,this.eventManager.emit("changePreviewStyle",e),this.eventManager.emit("previewNeedsRefresh",this.getMarkdown())},t.exec=function(){var e;(e=this.commandManager).exec.apply(e,arguments)},t._addDefaultCommands=function(){this.addCommand(ba),this.addCommand(Ia),this.addCommand(Va),this.addCommand($a),this.addCommand(Za),this.addCommand(Ja),this.addCommand(is),this.addCommand(cs),this.addCommand(ds),this.addCommand(fs),this.addCommand(gs),this.addCommand(vs),this.addCommand(Cs),this.addCommand(Es),this.addCommand(Ss),this.addCommand(Ls),this.addCommand(qa),this.addCommand(Bs),this.addCommand(Rs),this.addCommand(Us),this.addCommand(rl),this.addCommand(ol),this.addCommand(Vs),this.addCommand($s),this.addCommand(Xs),this.addCommand(Qs),this.addCommand(tl),this.addCommand(ql),this.addCommand(zl),this.addCommand(Gl),this.addCommand(ul),this.addCommand(gl),this.addCommand(Cl),this.addCommand(kl),this.addCommand(Bl),this.addCommand(Pl),this.addCommand(Fl),this.addCommand(Zl),this.addCommand(rc),this.addCommand(Hs)},t.addCommand=function(e,t){t?this.commandManager.addCommand(Zn["a"].command(e,t)):this.commandManager.addCommand(e)},t.afterAddedCommand=function(){this.eventManager.emit("afterAddedCommand",this)},t.on=function(e,t){this.eventManager.listen(e,t)},t.off=function(e){this.eventManager.removeEventHandler(e)},t.addHook=function(e,t){this.eventManager.removeEventHandler(e),this.eventManager.listen(e,t)},t.removeHook=function(e){this.eventManager.removeEventHandler(e)},t.getCodeMirror=function(){return this.mdEditor.getEditor()},t.getSquire=function(){return this.wwEditor.getEditor()},t.focus=function(){this.getCurrentModeEditor().focus()},t.blur=function(){this.getCurrentModeEditor().blur()},t.moveCursorToEnd=function(){this.getCurrentModeEditor().moveCursorToEnd()},t.moveCursorToStart=function(){this.getCurrentModeEditor().moveCursorToStart()},t.setMarkdown=function(e,t){void 0===t&&(t=!0),e=e||"",this.isMarkdownMode()?this.mdEditor.setValue(e,t):this.wwEditor.setValue(this.convertor.toHTML(e),t),this.eventManager.emit("setMarkdownAfter",e)},t.setHtml=function(e,t){if(void 0===t&&(t=!0),e=e||"",this.wwEditor.setValue(e,t),this.isMarkdownMode()){var n=this.convertor.toMarkdown(this.wwEditor.getValue(),this.toMarkOptions);this.mdEditor.setValue(n,t),this.eventManager.emit("setMarkdownAfter",n)}},t.getMarkdown=function(){var e;return e=this.isMarkdownMode()?this.mdEditor.getValue():this.convertor.toMarkdown(this.wwEditor.getValue(),this.toMarkOptions),e},t.getHtml=function(){return this.isWysiwygMode()&&this.mdEditor.setValue(this.convertor.toMarkdown(this.wwEditor.getValue(),this.toMarkOptions)),this.convertor.toHTML(this.mdEditor.getValue())},t.insertText=function(e){this.isMarkdownMode()?this.mdEditor.replaceSelection(e):this.wwEditor.insertText(e)},t.addWidget=function(e,t,n,r){this.getCurrentModeEditor().addWidget(e,t,n,r)},t.height=function(e){if(a()(e)){var t=this.options.el;"auto"===e?(p()(t,"auto-height"),this.minHeight(this.minHeight())):(m()(t,"auto-height"),this.minHeight(e)),l()(e)&&(e+="px"),h()(this.options.el,{height:e}),this._height=e}return this._height},t.minHeight=function(e){if(a()(e)){var t=this._ui.getEditorHeight(),n=this._ui.getEditorSectionHeight(),r=t-n;this._minHeight=e,e=parseInt(e,10),e=Math.max(e-r,0),this.wwEditor.setMinHeight(e),this.mdEditor.setMinHeight(e),this.preview.setMinHeight(e)}return this._minHeight},t.getCurrentModeEditor=function(){var e;return e=this.isMarkdownMode()?this.mdEditor:this.wwEditor,e},t.isMarkdownMode=function(){return"markdown"===this.currentMode},t.isWysiwygMode=function(){return"wysiwyg"===this.currentMode},t.isViewer=function(){return!1},t.getCurrentPreviewStyle=function(){return this.mdPreviewStyle},t.changeMode=function(e,t){this.currentMode!==e&&(this.eventManager.emit("changeModeBefore",this.currentMode),this.currentMode=e,this.isWysiwygMode()?(this.layout.switchToWYSIWYG(),this.wwEditor.setValue(this.convertor.toHTML(this.mdEditor.getValue()),!t),this.eventManager.emit("changeModeToWysiwyg")):(this.layout.switchToMarkdown(),this.mdEditor.resetState(),this.mdEditor.setValue(this.convertor.toMarkdown(this.wwEditor.getValue(),this.toMarkOptions),!t),this.getCodeMirror().refresh(),this.eventManager.emit("changeModeToMarkdown")),this.eventManager.emit("changeMode",e),t||this.focus())},t.remove=function(){var e=this,t=Hc.length-1;for(this.wwEditor.remove(),this.mdEditor.remove(),this.layout.remove(),this.preview.remove(),this.getUI()&&this.getUI().remove(),this.eventManager.emit("removeEditor"),this.eventManager.events.forEach((function(t,n){e.off(n)})),this.eventManager=null;t>=0;t-=1)Hc[t]===this&&Hc.splice(t,1)},t.hide=function(){this.eventManager.emit("hide",this)},t.show=function(){this.eventManager.emit("show",this),this.getCodeMirror().refresh()},t.scrollTop=function(e){return this.getCurrentModeEditor().scrollTop(e)},t.setUI=function(e){this._ui=e},t.getUI=function(){return this._ui},t.reset=function(){this.wwEditor.reset(),this.mdEditor.reset()},t.getRange=function(){return this.getCurrentModeEditor().getRange()},t.getTextObject=function(e){return this.getCurrentModeEditor().getTextObject(e)},t.getSelectedText=function(){var e=this.getRange(),t=this.getTextObject(e);return t.getTextContent()||""},t.setPlaceholder=function(e){this.mdEditor.setPlaceholder(e),this.wwEditor.setPlaceholder(e)},t.setCodeBlockLanguages=function(e){var t=this;void 0===e&&(e=[]),e.forEach((function(e){t.codeBlockLanguages.indexOf(e)<0&&t.codeBlockLanguages.push(e)})),this.eventManager.emit("setCodeBlockLanguages",this.codeBlockLanguages)},e.getInstances=function(){return Hc},e.factory=function(t){var n;return n=t.viewer?new or["a"](t):new e(t),n},e.setLanguage=function(e,t){cr.setLanguage(e,t)},e}();Fc._createMarkdownToHTML=_,Fc.isViewer=!1,Fc.codeBlockManager=ta["a"],Fc.WwCodeBlockManager=fn,Fc.WwTableManager=zt,Fc.WwTableSelectionManager=$t,Fc.CommandManager=Zn["a"];var Uc=Fc;n(50),n(51),n(41),n(52);Uc.setLanguage(["en","en-US"],{Markdown:"Markdown",WYSIWYG:"WYSIWYG",Write:"Write",Preview:"Preview",Headings:"Headings",Paragraph:"Paragraph",Bold:"Bold",Italic:"Italic",Strike:"Strike",Code:"Inline code",Line:"Line",Blockquote:"Blockquote","Unordered list":"Unordered list","Ordered list":"Ordered list",Task:"Task",Indent:"Indent",Outdent:"Outdent","Insert link":"Insert link","Insert CodeBlock":"Insert codeBlock","Insert table":"Insert table","Insert image":"Insert image",Heading:"Heading","Image URL":"Image URL","Select image file":"Select image file",Description:"Description",OK:"OK",More:"More",Cancel:"Cancel",File:"File",URL:"URL","Link text":"Link text","Add row":"Add row","Add col":"Add col","Remove row":"Remove row","Remove col":"Remove col","Align left":"Align left","Align center":"Align center","Align right":"Align right","Remove table":"Remove table","Would you like to paste as table?":"Would you like to paste as table?","Text color":"Text color","Auto scroll enabled":"Auto scroll enabled","Auto scroll disabled":"Auto scroll disabled","Choose language":"Choose language"});t["default"]=Uc}])["default"]}))}}]);