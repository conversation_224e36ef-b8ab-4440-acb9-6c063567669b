(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["app~c2c46666"],{"0ed5":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("a-modal",{attrs:{title:"cron表达式",width:e.modalWidth,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleSubmit,cancel:e.close}},[n("div",{staticClass:"card-container"},[n("a-tabs",{attrs:{type:"card"}},[n("a-tab-pane",{key:"1",attrs:{type:"card"}},[n("span",{attrs:{slot:"tab"},slot:"tab"},[n("a-icon",{attrs:{type:"schedule"}}),e._v(" 秒")],1),n("a-radio-group",{model:{value:e.result.second.cronEvery,callback:function(t){e.$set(e.result.second,"cronEvery",t)},expression:"result.second.cronEvery"}},[n("a-row",[n("a-radio",{attrs:{value:"1"}},[e._v("每一秒钟")])],1),n("a-row",[n("a-radio",{attrs:{value:"2"}},[e._v("每隔\n              "),n("a-input-number",{attrs:{size:"small",min:1,max:59},model:{value:e.result.second.incrementIncrement,callback:function(t){e.$set(e.result.second,"incrementIncrement",t)},expression:"result.second.incrementIncrement"}}),e._v("\n              秒执行 从\n              "),n("a-input-number",{attrs:{size:"small",min:0,max:59},model:{value:e.result.second.incrementStart,callback:function(t){e.$set(e.result.second,"incrementStart",t)},expression:"result.second.incrementStart"}}),e._v("\n              秒开始\n            ")],1)],1),n("a-row",[n("a-radio",{attrs:{value:"3"}},[e._v("具体秒数(可多选)")]),n("a-select",{staticStyle:{width:"354px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.second.specificSpecific,callback:function(t){e.$set(e.result.second,"specificSpecific",t)},expression:"result.second.specificSpecific"}},e._l(60,(function(t,r){return n("a-select-option",{key:r,attrs:{value:r}},[e._v(e._s(r))])})),1)],1),n("a-row",[n("a-radio",{attrs:{value:"4"}},[e._v("周期从\n              "),n("a-input-number",{attrs:{size:"small",min:1,max:59},model:{value:e.result.second.rangeStart,callback:function(t){e.$set(e.result.second,"rangeStart",t)},expression:"result.second.rangeStart"}}),e._v("\n              到\n              "),n("a-input-number",{attrs:{size:"small",min:0,max:59},model:{value:e.result.second.rangeEnd,callback:function(t){e.$set(e.result.second,"rangeEnd",t)},expression:"result.second.rangeEnd"}}),e._v("\n              秒\n            ")],1)],1)],1)],1),n("a-tab-pane",{key:"2"},[n("span",{attrs:{slot:"tab"},slot:"tab"},[n("a-icon",{attrs:{type:"schedule"}}),e._v("分")],1),n("div",{staticClass:"tabBody"},[n("a-radio-group",{model:{value:e.result.minute.cronEvery,callback:function(t){e.$set(e.result.minute,"cronEvery",t)},expression:"result.minute.cronEvery"}},[n("a-row",[n("a-radio",{attrs:{value:"1"}},[e._v("每一分钟")])],1),n("a-row",[n("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:60},model:{value:e.result.minute.incrementIncrement,callback:function(t){e.$set(e.result.minute,"incrementIncrement",t)},expression:"result.minute.incrementIncrement"}}),e._v("\n                分执行 从\n                "),n("a-input-number",{attrs:{size:"small",min:0,max:59},model:{value:e.result.minute.incrementStart,callback:function(t){e.$set(e.result.minute,"incrementStart",t)},expression:"result.minute.incrementStart"}}),e._v("\n                分开始\n              ")],1)],1),n("a-row",[n("a-radio",{attrs:{value:"3"}},[e._v("具体分钟数(可多选)")]),n("a-select",{staticStyle:{width:"340px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.minute.specificSpecific,callback:function(t){e.$set(e.result.minute,"specificSpecific",t)},expression:"result.minute.specificSpecific"}},e._l(Array(60),(function(t,r){return n("a-select-option",{key:r,attrs:{value:r}},[e._v(" "+e._s(r))])})),1)],1),n("a-row",[n("a-radio",{attrs:{value:"4"}},[e._v("周期从\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:60},model:{value:e.result.minute.rangeStart,callback:function(t){e.$set(e.result.minute,"rangeStart",t)},expression:"result.minute.rangeStart"}}),e._v("\n                到\n                "),n("a-input-number",{attrs:{size:"small",min:0,max:59},model:{value:e.result.minute.rangeEnd,callback:function(t){e.$set(e.result.minute,"rangeEnd",t)},expression:"result.minute.rangeEnd"}}),e._v("\n                分\n              ")],1)],1)],1)],1)]),n("a-tab-pane",{key:"3"},[n("span",{attrs:{slot:"tab"},slot:"tab"},[n("a-icon",{attrs:{type:"schedule"}}),e._v(" 时")],1),n("div",{staticClass:"tabBody"},[n("a-radio-group",{model:{value:e.result.hour.cronEvery,callback:function(t){e.$set(e.result.hour,"cronEvery",t)},expression:"result.hour.cronEvery"}},[n("a-row",[n("a-radio",{attrs:{value:"1"}},[e._v("每一小时")])],1),n("a-row",[n("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),n("a-input-number",{attrs:{size:"small",min:0,max:23},model:{value:e.result.hour.incrementIncrement,callback:function(t){e.$set(e.result.hour,"incrementIncrement",t)},expression:"result.hour.incrementIncrement"}}),e._v("\n                小时执行 从\n                "),n("a-input-number",{attrs:{size:"small",min:0,max:23},model:{value:e.result.hour.incrementStart,callback:function(t){e.$set(e.result.hour,"incrementStart",t)},expression:"result.hour.incrementStart"}}),e._v("\n                小时开始\n              ")],1)],1),n("a-row",[n("a-radio",{staticClass:"long",attrs:{value:"3"}},[e._v("具体小时数(可多选)")]),n("a-select",{staticStyle:{width:"340px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.hour.specificSpecific,callback:function(t){e.$set(e.result.hour,"specificSpecific",t)},expression:"result.hour.specificSpecific"}},e._l(Array(24),(function(t,r){return n("a-select-option",{key:r},[e._v(e._s(r))])})),1)],1),n("a-row",[n("a-radio",{attrs:{value:"4"}},[e._v("周期从\n                "),n("a-input-number",{attrs:{size:"small",min:0,max:23},model:{value:e.result.hour.rangeStart,callback:function(t){e.$set(e.result.hour,"rangeStart",t)},expression:"result.hour.rangeStart"}}),e._v("\n                到\n                "),n("a-input-number",{attrs:{size:"small",min:0,max:23},model:{value:e.result.hour.rangeEnd,callback:function(t){e.$set(e.result.hour,"rangeEnd",t)},expression:"result.hour.rangeEnd"}}),e._v("\n                小时\n              ")],1)],1)],1)],1)]),n("a-tab-pane",{key:"4"},[n("span",{attrs:{slot:"tab"},slot:"tab"},[n("a-icon",{attrs:{type:"schedule"}}),e._v("  天")],1),n("div",{staticClass:"tabBody"},[n("a-radio-group",{model:{value:e.result.day.cronEvery,callback:function(t){e.$set(e.result.day,"cronEvery",t)},expression:"result.day.cronEvery"}},[n("a-row",[n("a-radio",{attrs:{value:"1"}},[e._v("每一天")])],1),n("a-row",[n("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:7},model:{value:e.result.week.incrementIncrement,callback:function(t){e.$set(e.result.week,"incrementIncrement",t)},expression:"result.week.incrementIncrement"}}),e._v("\n                周执行 从\n                "),n("a-select",{attrs:{size:"small"},model:{value:e.result.week.incrementStart,callback:function(t){e.$set(e.result.week,"incrementStart",t)},expression:"result.week.incrementStart"}},e._l(Array(7),(function(t,r){return n("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(e.weekDays[r]))])})),1),e._v("\n                开始\n              ")],1)],1),n("a-row",[n("a-radio",{attrs:{value:"3"}},[e._v("每隔\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:31},model:{value:e.result.day.incrementIncrement,callback:function(t){e.$set(e.result.day,"incrementIncrement",t)},expression:"result.day.incrementIncrement"}}),e._v("\n                天执行 从\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:31},model:{value:e.result.day.incrementStart,callback:function(t){e.$set(e.result.day,"incrementStart",t)},expression:"result.day.incrementStart"}}),e._v("\n                天开始\n              ")],1)],1),n("a-row",[n("a-radio",{staticClass:"long",attrs:{value:"4"}},[e._v("具体星期几(可多选)")]),n("a-select",{staticStyle:{width:"340px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.week.specificSpecific,callback:function(t){e.$set(e.result.week,"specificSpecific",t)},expression:"result.week.specificSpecific"}},e._l(Array(7),(function(t,r){return n("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(e.weekDays[r]))])})),1)],1),n("a-row",[n("a-radio",{staticClass:"long",attrs:{value:"5"}},[e._v("具体天数(可多选)")]),n("a-select",{staticStyle:{width:"354px"},attrs:{size:"small",mode:"multiple"},model:{value:e.result.day.specificSpecific,callback:function(t){e.$set(e.result.day,"specificSpecific",t)},expression:"result.day.specificSpecific"}},e._l(Array(31),(function(t,r){return n("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(r+1))])})),1)],1),n("a-row",[n("a-radio",{attrs:{value:"6"}},[e._v("在这个月的最后一天")])],1),n("a-row",[n("a-radio",{attrs:{value:"7"}},[e._v("在这个月的最后一个工作日")])],1),n("a-row",[n("a-radio",{attrs:{value:"8"}},[e._v("在这个月的最后一个\n                "),n("a-select",{attrs:{size:"small"},model:{value:e.result.day.cronLastSpecificDomDay,callback:function(t){e.$set(e.result.day,"cronLastSpecificDomDay",t)},expression:"result.day.cronLastSpecificDomDay"}},e._l(Array(7),(function(t,r){return n("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(e.weekDays[r]))])})),1)],1)],1),n("a-row",[n("a-radio",{attrs:{value:"9"}},[e._v("\n                在本月底前\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:31},model:{value:e.result.day.cronDaysBeforeEomMinus,callback:function(t){e.$set(e.result.day,"cronDaysBeforeEomMinus",t)},expression:"result.day.cronDaysBeforeEomMinus"}}),e._v("\n                天\n              ")],1)],1),n("a-row",[n("a-radio",{attrs:{value:"10"}},[e._v("最近的工作日（周一至周五）至本月\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:31},model:{value:e.result.day.cronDaysNearestWeekday,callback:function(t){e.$set(e.result.day,"cronDaysNearestWeekday",t)},expression:"result.day.cronDaysNearestWeekday"}}),e._v("\n                日\n              ")],1)],1),n("a-row",[n("a-radio",{attrs:{value:"11"}},[e._v("在这个月的第\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:5},model:{value:e.result.week.cronNthDayNth,callback:function(t){e.$set(e.result.week,"cronNthDayNth",t)},expression:"result.week.cronNthDayNth"}}),e._v("\n                个\n                "),n("a-select",{attrs:{size:"small"},model:{value:e.result.week.cronNthDayDay,callback:function(t){e.$set(e.result.week,"cronNthDayDay",t)},expression:"result.week.cronNthDayDay"}},e._l(Array(7),(function(t,r){return n("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(e.weekDays[r]))])})),1)],1)],1)],1)],1)]),n("a-tab-pane",{key:"5"},[n("span",{attrs:{slot:"tab"},slot:"tab"},[n("a-icon",{attrs:{type:"schedule"}}),e._v(" 月")],1),n("div",{staticClass:"tabBody"},[n("a-radio-group",{model:{value:e.result.month.cronEvery,callback:function(t){e.$set(e.result.month,"cronEvery",t)},expression:"result.month.cronEvery"}},[n("a-row",[n("a-radio",{attrs:{value:"1"}},[e._v("每一月")])],1),n("a-row",[n("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),n("a-input-number",{attrs:{size:"small",min:0,max:12},model:{value:e.result.month.incrementIncrement,callback:function(t){e.$set(e.result.month,"incrementIncrement",t)},expression:"result.month.incrementIncrement"}}),e._v("\n                月执行 从\n                "),n("a-input-number",{attrs:{size:"small",min:0,max:12},model:{value:e.result.month.incrementStart,callback:function(t){e.$set(e.result.month,"incrementStart",t)},expression:"result.month.incrementStart"}}),e._v("\n                月开始\n              ")],1)],1),n("a-row",[n("a-radio",{staticClass:"long",attrs:{value:"3"}},[e._v("具体月数(可多选)")]),n("a-select",{staticStyle:{width:"354px"},attrs:{size:"small",filterable:"",mode:"multiple"},model:{value:e.result.month.specificSpecific,callback:function(t){e.$set(e.result.month,"specificSpecific",t)},expression:"result.month.specificSpecific"}},e._l(Array(12),(function(t,r){return n("a-select-option",{key:r,attrs:{value:r+1}},[e._v(e._s(r+1))])})),1)],1),n("a-row",[n("a-radio",{attrs:{value:"4"}},[e._v("从\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:12},model:{value:e.result.month.rangeStart,callback:function(t){e.$set(e.result.month,"rangeStart",t)},expression:"result.month.rangeStart"}}),e._v("\n                到\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:12},model:{value:e.result.month.rangeEnd,callback:function(t){e.$set(e.result.month,"rangeEnd",t)},expression:"result.month.rangeEnd"}}),e._v("\n                月之间的每个月\n              ")],1)],1)],1)],1)]),n("a-tab-pane",{key:"6"},[n("span",{attrs:{slot:"tab"},slot:"tab"},[n("a-icon",{attrs:{type:"schedule"}}),e._v(" 年")],1),n("div",{staticClass:"tabBody"},[n("a-radio-group",{model:{value:e.result.year.cronEvery,callback:function(t){e.$set(e.result.year,"cronEvery",t)},expression:"result.year.cronEvery"}},[n("a-row",[n("a-radio",{attrs:{value:"1"}},[e._v("每一年")])],1),n("a-row",[n("a-radio",{attrs:{value:"2"}},[e._v("每隔\n                "),n("a-input-number",{attrs:{size:"small",min:1,max:99},model:{value:e.result.year.incrementIncrement,callback:function(t){e.$set(e.result.year,"incrementIncrement",t)},expression:"result.year.incrementIncrement"}}),e._v("\n                年执行 从\n                "),n("a-input-number",{attrs:{size:"small",min:2019,max:2119},model:{value:e.result.year.incrementStart,callback:function(t){e.$set(e.result.year,"incrementStart",t)},expression:"result.year.incrementStart"}}),e._v("\n                年开始\n              ")],1)],1),n("a-row",[n("a-radio",{staticClass:"long",attrs:{value:"3"}},[e._v("具体年份(可多选)")]),n("a-select",{staticStyle:{width:"354px"},attrs:{size:"small",filterable:"",mode:"multiple"},model:{value:e.result.year.specificSpecific,callback:function(t){e.$set(e.result.year,"specificSpecific",t)},expression:"result.year.specificSpecific"}},e._l(Array(100),(function(t,r){return n("a-select-option",{key:r,attrs:{value:2019+r}},[e._v(e._s(2019+r))])})),1)],1),n("a-row",[n("a-radio",{attrs:{value:"4"}},[e._v("从\n                "),n("a-input-number",{attrs:{size:"small",min:2019,max:2119},model:{value:e.result.year.rangeStart,callback:function(t){e.$set(e.result.year,"rangeStart",t)},expression:"result.year.rangeStart"}}),e._v("\n                到\n                "),n("a-input-number",{attrs:{size:"small",min:2019,max:2119},model:{value:e.result.year.rangeEnd,callback:function(t){e.$set(e.result.year,"rangeEnd",t)},expression:"result.year.rangeEnd"}}),e._v("\n                年之间的每一年\n              ")],1)],1)],1)],1)])],1),n("div",{staticClass:"bottom"},[n("span",{staticClass:"value"},[e._v(e._s(this.cron))])])],1)])},a=[],i={name:"VueCron",props:["data"],data:function(){return{visible:!1,confirmLoading:!1,size:"large",weekDays:["天","一","二","三","四","五","六"].map((function(e){return"星期"+e})),result:{second:{},minute:{},hour:{},day:{},week:{},month:{},year:{}},defaultValue:{second:{cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:0,specificSpecific:[]},minute:{cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:"0",specificSpecific:[]},hour:{cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:"0",rangeEnd:"0",specificSpecific:[]},day:{cronEvery:"",incrementStart:1,incrementIncrement:"1",rangeStart:"",rangeEnd:"",specificSpecific:[],cronLastSpecificDomDay:1,cronDaysBeforeEomMinus:1,cronDaysNearestWeekday:1},week:{cronEvery:"",incrementStart:1,incrementIncrement:1,specificSpecific:[],cronNthDayDay:1,cronNthDayNth:1},month:{cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:1,specificSpecific:[]},year:{cronEvery:"",incrementStart:2017,incrementIncrement:1,rangeStart:2019,rangeEnd:2019,specificSpecific:[]},label:""}}},computed:{modalWidth:function(){return 608},secondsText:function(){var e="",t=this.result.second.cronEvery||"";switch(t.toString()){case"1":e="*";break;case"2":e=this.result.second.incrementStart+"/"+this.result.second.incrementIncrement;break;case"3":this.result.second.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.second.rangeStart+"-"+this.result.second.rangeEnd;break}return e},minutesText:function(){var e="",t=this.result.minute.cronEvery||"";switch(t.toString()){case"1":e="*";break;case"2":e=this.result.minute.incrementStart+"/"+this.result.minute.incrementIncrement;break;case"3":this.result.minute.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.minute.rangeStart+"-"+this.result.minute.rangeEnd;break}return e},hoursText:function(){var e="",t=this.result.hour.cronEvery||"";switch(t.toString()){case"1":e="*";break;case"2":e=this.result.hour.incrementStart+"/"+this.result.hour.incrementIncrement;break;case"3":this.result.hour.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.hour.rangeStart+"-"+this.result.hour.rangeEnd;break}return e},daysText:function(){var e="",t=this.result.day.cronEvery||"";switch(t.toString()){case"1":break;case"2":case"4":case"11":e="?";break;case"3":e=this.result.day.incrementStart+"/"+this.result.day.incrementIncrement;break;case"5":this.result.day.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"6":e="L";break;case"7":e="LW";break;case"8":e=this.result.day.cronLastSpecificDomDay+"L";break;case"9":e="L-"+this.result.day.cronDaysBeforeEomMinus;break;case"10":e=this.result.day.cronDaysNearestWeekday+"W";break}return e},weeksText:function(){var e="",t=this.result.day.cronEvery||"";switch(t.toString()){case"1":case"3":case"5":e="?";break;case"2":e=this.result.week.incrementStart+"/"+this.result.week.incrementIncrement;break;case"4":this.result.week.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"6":case"7":case"8":case"9":case"10":e="?";break;case"11":e=this.result.week.cronNthDayDay+"#"+this.result.week.cronNthDayNth;break}return e},monthsText:function(){var e="",t=this.result.month.cronEvery||"";switch(t.toString()){case"1":e="*";break;case"2":e=this.result.month.incrementStart+"/"+this.result.month.incrementIncrement;break;case"3":this.result.month.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.month.rangeStart+"-"+this.result.month.rangeEnd;break}return e},yearsText:function(){var e="",t=this.result.year.cronEvery||"";switch(t.toString()){case"1":e="*";break;case"2":e=this.result.year.incrementStart+"/"+this.result.year.incrementIncrement;break;case"3":this.result.year.specificSpecific.map((function(t){e+=t+","})),e=e.slice(0,-1);break;case"4":e=this.result.year.rangeStart+"-"+this.result.year.rangeEnd;break}return e},cron:function(){return"".concat(this.secondsText||"*"," ").concat(this.minutesText||"*"," ").concat(this.hoursText||"*"," ").concat(this.daysText||"*"," ").concat(this.monthsText||"*"," ").concat(this.weeksText||"?"," ").concat(this.yearsText||"*")}},watch:{visible:{handler:function(){var e=this.data;e?(this.secondsReverseExp(e),this.minutesReverseExp(e),this.hoursReverseExp(e),this.daysReverseExp(e),this.daysReverseExp(e),this.monthsReverseExp(e),this.yearReverseExp(e),JSON.parse(JSON.stringify(e))):this.result=JSON.parse(JSON.stringify(this.defaultValue))}}},methods:{show:function(){this.visible=!0},handleSubmit:function(){this.$emit("ok",this.cron),this.close(),this.visible=!1},close:function(){this.visible=!1},secondsReverseExp:function(e){var t=e.split(" ")[0],n={cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:0,specificSpecific:[]};switch(!0){case t.includes("*"):n.cronEvery="1";break;case t.includes("/"):n.cronEvery="2",n.incrementStart=t.split("/")[0],n.incrementIncrement=t.split("/")[1];break;case t.includes(","):n.cronEvery="3",n.specificSpecific=t.split(",").map(Number).sort();break;case t.includes("-"):n.cronEvery="4",n.rangeStart=t.split("-")[0],n.rangeEnd=t.split("-")[1];break;default:n.cronEvery="1"}this.result.second=n},minutesReverseExp:function(e){var t=e.split(" ")[1],n={cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:0,specificSpecific:[]};switch(!0){case t.includes("*"):n.cronEvery="1";break;case t.includes("/"):n.cronEvery="2",n.incrementStart=t.split("/")[0],n.incrementIncrement=t.split("/")[1];break;case t.includes(","):n.cronEvery="3",n.specificSpecific=t.split(",").map(Number).sort();break;case t.includes("-"):n.cronEvery="4",n.rangeStart=t.split("-")[0],n.rangeEnd=t.split("-")[1];break;default:n.cronEvery="1"}this.result.minute=n},hoursReverseExp:function(e){var t=e.split(" ")[2],n={cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:"0",specificSpecific:[]};switch(!0){case t.includes("*"):n.cronEvery="1";break;case t.includes("/"):n.cronEvery="2",n.incrementStart=t.split("/")[0],n.incrementIncrement=t.split("/")[1];break;case t.includes(","):n.cronEvery="3",n.specificSpecific=t.split(",").map(Number).sort();break;case t.includes("-"):n.cronEvery="4",n.rangeStart=t.split("-")[0],n.rangeEnd=t.split("-")[1];break;default:n.cronEvery="1"}this.result.hour=n},daysReverseExp:function(e){var t=e.split(" ")[3],n=e.split(" ")[5],r={cronEvery:"",incrementStart:1,incrementIncrement:1,rangeStart:1,rangeEnd:1,specificSpecific:[],cronLastSpecificDomDay:1,cronDaysBeforeEomMinus:1,cronDaysNearestWeekday:1},a={cronEvery:"",incrementStart:1,incrementIncrement:1,specificSpecific:[],cronNthDayDay:1,cronNthDayNth:"1"};if(t.includes("?"))switch(!0){case n.includes("/"):r.cronEvery="2",a.incrementStart=n.split("/")[0],a.incrementIncrement=n.split("/")[1];break;case n.includes(","):r.cronEvery="4",a.specificSpecific=n.split(",").map(Number).sort();break;case"#":r.cronEvery="11",a.cronNthDayDay=n.split("#")[0],a.cronNthDayNth=n.split("#")[1];break;default:r.cronEvery="1",a.cronEvery="1"}else switch(!0){case t.includes("*"):r.cronEvery="1";break;case t.includes("?"):break;case t.includes("/"):r.cronEvery="3",r.incrementStart=t.split("/")[0],r.incrementIncrement=t.split("/")[1];break;case t.includes(","):r.cronEvery="5",r.specificSpecific=t.split(",").map(Number).sort();break;case t.includes("LW"):r.cronEvery="7";break;case t.includes("L-"):r.cronEvery="9",r.cronDaysBeforeEomMinus=t.split("L-")[1];break;case t.includes("L"):1==t.len?(r.cronEvery="6",r.cronLastSpecificDomDay="1"):(r.cronEvery="8",r.cronLastSpecificDomDay=Number(t.split("L")[0]));break;case t.includes("W"):r.cronEvery="10",r.cronDaysNearestWeekday=t.split("W")[0];break;default:r.cronEvery="1"}this.result.day=r,this.result.week=a},monthsReverseExp:function(e){var t=e.split(" ")[4],n={cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:1,rangeEnd:1,specificSpecific:[]};switch(!0){case t.includes("*"):n.cronEvery="1";break;case t.includes("/"):n.cronEvery="2",n.incrementStart=t.split("/")[0],n.incrementIncrement=t.split("/")[1];break;case t.includes(","):n.cronEvery="3",n.specificSpecific=t.split(",").map(Number).sort();break;case t.includes("-"):n.cronEvery="4",n.rangeStart=t.split("-")[0],n.rangeEnd=t.split("-")[1];break;default:n.cronEvery="1"}this.result.month=n},yearReverseExp:function(e){var t=e.split(" ")[6],n={cronEvery:"",incrementStart:3,incrementIncrement:5,rangeStart:2019,rangeEnd:2019,specificSpecific:[]};switch(!0){case t.includes("*"):n.cronEvery="1";break;case t.includes("/"):n.cronEvery="2",n.incrementStart=t.split("/")[0],n.incrementIncrement=t.split("/")[1];break;case t.includes(","):n.cronEvery="3",n.specificSpecific=t.split(",").map(Number).sort();break;case t.includes("-"):n.cronEvery="4",n.rangeStart=t.split("-")[0],n.rangeEnd=t.split("-")[1];break;default:n.cronEvery="1"}this.result.year=n}}},s=i,o=(n("9791"),n("5934"),n("2877")),c=Object(o["a"])(s,r,a,!1,null,"8de57b8c",null);t["default"]=c.exports},"0f9d":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("j-select-biz-component",e._g(e._b({attrs:{value:e.value,ellipsisLength:25,listUrl:e.url.list,columns:e.columns}},"j-select-biz-component",e.attrs,!1),e.$listeners))},a=[],i=n("2638"),s=n.n(i),o=n("2dab"),c=n("b296"),l={name:"JSelectMultiUser",components:{JDate:o["default"],JSelectBizComponent:c["default"]},props:{value:null,queryConfig:{type:Array,default:function(){return[]}}},data:function(){var e=this,t=this.$createElement;return{url:{list:"/sys/user/list"},columns:[{title:"姓名",align:"center",width:"25%",widthRight:"70%",dataIndex:"realname"},{title:"账号",align:"center",width:"25%",dataIndex:"username"},{title:"电话",align:"center",width:"20%",dataIndex:"phone"},{title:"出生日期",align:"center",width:"20%",dataIndex:"birthday"}],default:{name:"用户",width:1200,displayKey:"realname",returnKeys:["id","username"],queryParamText:"账号"},queryConfigDefault:[{key:"sex",label:"性别",dictCode:"sex"},{key:"birthday",label:"生日",placeholder:"请选择出生日期",customRender:function(n){var r=n.key,a=n.queryParam,i=n.options;return t("j-date",s()([{},i,{style:"width:180px;",model:{value:a[r],callback:function(t){e.$set(a,r,t)}}}]))}}]}},computed:{attrs:function(){return Object.assign(this.default,this.$attrs,{queryConfig:this.queryConfigDefault.concat(this.queryConfig)})}}},u=l,d=n("2877"),p=Object(d["a"])(u,r,a,!1,null,"3c54ee81",null);t["default"]=p.exports},"1e8c":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("j-select-biz-component",e._g(e._b({attrs:{value:e.value,name:"角色",displayKey:"roleName",returnKeys:e.returnKeys,listUrl:e.url.list,columns:e.columns,queryParamText:"角色编码"}},"j-select-biz-component",e.$attrs,!1),e.$listeners))},a=[],i=n("b296"),s={name:"JSelectRole",components:{JSelectBizComponent:i["default"]},props:["value"],data:function(){return{returnKeys:["id","roleCode"],url:{list:"/sys/role/list"},columns:[{title:"角色名称",dataIndex:"roleName",align:"center",width:120},{title:"角色编码",dataIndex:"roleCode",align:"center",width:120}]}}},o=s,c=n("2877"),l=Object(c["a"])(o,r,a,!1,null,"1c32062d",null);t["default"]=l.exports},"202a":function(e,t,n){},"242f":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("a-modal",{attrs:{title:"image"===e.fileType?"图片上传":"文件上传",width:e.width,visible:e.visible,cancelText:"取消"},on:{ok:e.ok,cancel:e.close}},[n("j-upload",{attrs:{"file-type":e.fileType,value:e.filePath,disabled:e.disabled,number:e.number},on:{change:e.handleChange}})],1)],1)},a=[],i=n("0fea"),s=function(e){if(e.lastIndexOf("\\")>=0){var t=new RegExp("\\\\","g");e=e.replace(t,"/")}return e.substring(e.lastIndexOf("/")+1)},o={name:"JFilePop",components:{},props:{title:{type:String,default:"",required:!1},position:{type:String,default:"right",required:!1},height:{type:Number,default:200,required:!1},width:{type:Number,default:520,required:!1},popContainer:{type:String,default:"",required:!1},disabled:{type:Boolean,default:!1,required:!1},number:{type:Number,required:!1,default:0}},data:function(){return{visible:!1,filePath:"",id:"",fileType:"file"}},methods:{handleChange:function(e){this.filePath=e},show:function(e,t,n){this.id=e,this.filePath=t,this.visible=!0,this.fileType="img"===n?"image":"file"},ok:function(){if(!this.filePath)return this.$message.error("未上传任何文件"),!1;var e=this.filePath.split(","),t={name:s(e[0]),url:Object(i["d"])(e[0]),path:this.filePath,status:"done",id:this.id};this.$emit("ok",t),this.visible=!1},close:function(){this.visible=!1}}},c=o,l=n("2877"),u=Object(l["a"])(c,r,a,!1,null,"640cdafc",null);t["default"]=u.exports},"267b":function(e,t,n){"use strict";var r=n("98da"),a=n("c681"),i=n("9ffd"),s=n("6f9a"),o=n("a876"),c=n("a061"),l=n("7550"),u=n("9e8f"),d=n("8c6e"),p=n("cf74"),h=n("b098"),f=n("49a8"),m=n("e610"),y=n("7c93"),v=n("4165"),b=n("a726"),g=n("1e29"),S=n("2dab"),w=n("d579"),k=n("4349"),x=n("a916"),E=n("242f"),O=n("5f64"),_=n("13d2"),I=n("ae14"),C=n("f92c"),j=n("5dd5"),D=n("6b87"),K=n("9fa5"),R=n("fe54"),$=n("0f9d"),q=n("61fc"),P=n("1e8c"),T=n("c14a"),z=n("56cd"),J=n("ed3b"),N=n("f64c"),V=n("2ef0"),A=n.n(V),B=n("b047"),M=n.n(B),U=n("88bc"),L=n.n(U),W=n("5176"),F=n.n(W);t["a"]={install:function(e){e.use(r["a"]),e.component("JMarkdownEditor",s["default"]),e.component("JPopupOnlReport",x["default"]),e.component("JFilePop",E["default"]),e.component("JInputPop",O["default"]),e.component("JAreaLinkage",u["default"]),e.component("JCategorySelect",f["default"]),e.component("JCheckbox",b["default"]),e.component("JCodeEditor",o["default"]),e.component("JCron",g["default"]),e.component("JDate",S["default"]),e.component("JEditableTable",l["default"]),e.component("JEditor",c["default"]),e.component("JEllipsis",w["default"]),e.component("JFormContainer",a["default"]),e.component("JImageUpload",m["default"]),e.component("JImportModal",y["default"]),e.component("JInput",k["default"]),e.component("JPopup",i["default"]),e.component("JSelectMultiple",_["default"]),e.component("JSlider",I["default"]),e.component("JSuperQuery",d["default"]),e.component("JSwitch",C["default"]),e.component("JTime",j["default"]),e.component("JTreeDict",v["default"]),e.component("JTreeSelect",h["default"]),e.component("JTreeTable",D["default"]),e.component("JUpload",p["default"]),e.component("JSelectDepart",R["default"]),e.component("JSelectMultiUser",$["default"]),e.component("JSelectPosition",q["default"]),e.component("JSelectRole",P["default"]),e.component("JSelectUserByDep",T["default"]),e.component(K["a"].name,K["a"]),e.prototype.$Jnotification=z["a"],e.prototype.$Jmodal=J["a"],e.prototype.$Jmessage=N["a"],e.prototype.$Jlodash=A.a,e.prototype.$Jdebounce=M.a,e.prototype.$Jpick=L.a,e.prototype.$Jpcaa=F.a}}},"35f1":function(e,t,n){"use strict";var r=n("64c9"),a=n.n(r);a.a},"3ad3":function(e,t,n){},4186:function(e,t,n){},"4d3d":function(e,t,n){},5934:function(e,t,n){"use strict";var r=n("202a"),a=n.n(r);a.a},"5f64":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("a-popover",{attrs:{trigger:"contextmenu",placement:e.position,overlayClassName:"j-input-pop"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[n("div",{attrs:{slot:"title"},slot:"title"},[n("span",[e._v(e._s(e.title))]),n("span",{staticStyle:{float:"right"},attrs:{title:"关闭"}},[n("a-icon",{attrs:{type:"close"},on:{click:function(t){e.visible=!1}}})],1)]),n("a-input",{attrs:{value:e.inputContent,disabled:e.disabled},on:{change:e.handleInputChange}},[n("a-icon",{attrs:{slot:"suffix",type:"fullscreen"},on:{click:function(t){return t.stopPropagation(),e.pop(t)}},slot:"suffix"})],1),n("div",{attrs:{slot:"content"},slot:"content"},[n("a-textarea",{ref:"textarea",style:{height:e.height+"px",width:e.width+"px"},attrs:{value:e.inputContent,disabled:e.disabled},on:{input:e.handleInputChange}})],1)],1)},a=[],i={name:"JInputPop",props:{title:{type:String,default:"",required:!1},position:{type:String,default:"right",required:!1},height:{type:Number,default:200,required:!1},width:{type:Number,default:150,required:!1},value:{type:String,required:!1},popContainer:{type:String,default:"",required:!1},disabled:{type:Boolean,default:!1}},data:function(){return{visible:!1,inputContent:""}},watch:{value:{immediate:!0,handler:function(){this.value&&this.value.length>0&&(this.inputContent=this.value)}}},model:{prop:"value",event:"change"},methods:{handleInputChange:function(e){this.inputContent=e.target.value,this.$emit("change",this.inputContent)},pop:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.textarea.focus()}))},getPopupContainer:function(e){return this.popContainer?document.getElementById(this.popContainer):e.parentNode}}},s=i,o=n("2877"),c=Object(o["a"])(s,r,a,!1,null,"c0948618",null);t["default"]=c.exports},"61fc":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("j-select-biz-component",e._g(e._b({attrs:{width:1e3}},"j-select-biz-component",e.configs,!1),e.$listeners))},a=[],i=n("b296"),s={name:"JSelectPosition",components:{JSelectBizComponent:i["default"]},props:["value"],data:function(){return{settings:{name:"职务",displayKey:"name",returnKeys:["id","code"],listUrl:"/sys/position/list",queryParamCode:"name",queryParamText:"职务名称",columns:[{title:"职务名称",dataIndex:"name",align:"center",width:"30%",widthRight:"70%"},{title:"职务编码",dataIndex:"code",align:"center",width:"35%"},{title:"职级",dataIndex:"rank_dictText",align:"center",width:"25%"}]}}},computed:{configs:function(){return Object.assign({value:this.value},this.settings,this.$attrs)}}},o=s,c=n("2877"),l=Object(c["a"])(o,r,a,!1,null,"09d7ae7b",null);t["default"]=l.exports},"64c9":function(e,t,n){},"67da":function(e,t,n){"use strict";var r=n("4d3d"),a=n.n(r);a.a},"73ea":function(e,t,n){},"921e":function(e,t,n){},9791:function(e,t,n){"use strict";var r=n("73ea"),a=n.n(r);a.a},"9f14":function(e,t,n){"use strict";var r=n("4186"),a=n.n(r);a.a},a505:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("j-modal",{staticStyle:{top:"50px"},attrs:{width:e.modalWidth,visible:e.visible,title:e.title,switchFullscreen:"",wrapClassName:"j-user-select-modal",cancelText:"关闭"},on:{ok:e.handleSubmit,cancel:e.close}},[n("a-row",{staticStyle:{"background-color":"#ececec",padding:"10px",margin:"-10px"},attrs:{gutter:10}},[n("a-col",{attrs:{md:6,sm:24}},[n("a-card",{attrs:{bordered:!1}},[n("a-directory-tree",{attrs:{selectable:"",selectedKeys:e.selectedDepIds,checkStrictly:!0,dropdownStyle:{maxHeight:"200px",overflow:"auto"},treeData:e.departTree,expandAction:!1,expandedKeys:e.expandedKeys},on:{"update:expandedKeys":function(t){e.expandedKeys=t},"update:expanded-keys":function(t){e.expandedKeys=t},select:e.onDepSelect}})],1)],1),n("a-col",{attrs:{md:18,sm:24}},[n("a-card",{attrs:{bordered:!1}},[e._v("\n        用户账号:\n        "),n("a-input-search",{style:{width:"150px",marginBottom:"15px"},attrs:{placeholder:"请输入账号"},on:{search:e.onSearch},model:{value:e.queryParam.username,callback:function(t){e.$set(e.queryParam,"username",t)},expression:"queryParam.username"}}),n("a-button",{staticStyle:{"margin-left":"20px"},attrs:{icon:"redo"},on:{click:function(t){return e.searchReset(1)}}},[e._v("重置")]),n("a-table",{ref:"table",attrs:{scroll:e.scrollTrigger,size:"middle",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange,type:e.getType},loading:e.loading},on:{change:e.handleTableChange}})],1)],1)],1)],1)},a=[],i=n("a34a"),s=n.n(i),o=n("ca00"),c=n("4ec3"),l=n("0fea");function u(e,t,n,r,a,i,s){try{var o=e[i](s),c=o.value}catch(l){return void n(l)}o.done?t(c):Promise.resolve(c).then(r,a)}function d(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){u(i,r,a,s,o,"next",e)}function o(e){u(i,r,a,s,o,"throw",e)}s(void 0)}))}}function p(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=h(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,o=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return s=e.done,e},e:function(e){o=!0,i=e},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw i}}}}function h(e,t){if(e){if("string"===typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function m(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var y={name:"JSelectUserByDepModal",components:{},props:["modalWidth","multi","userIds","store","text"],data:function(){return{queryParam:{username:""},columns:[{title:"用户账号",align:"center",dataIndex:"username"},{title:"用户姓名",align:"center",dataIndex:"realname"},{title:"性别",align:"center",dataIndex:"sex",customRender:function(e){return 1===e?"男":2===e?"女":e}},{title:"手机",align:"center",dataIndex:"phone"},{title:"部门",align:"center",dataIndex:"orgCodeTxt"}],scrollTrigger:{},dataSource:[],selectionRows:[],selectedRowKeys:[],selectUserRows:[],selectUserIds:[],title:"根据部门选择用户",ipagination:{current:1,pageSize:10,pageSizeOptions:["10","20","30"],showTotal:function(e,t){return t[0]+"-"+t[1]+" 共"+e+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0},isorter:{column:"createTime",order:"desc"},selectedDepIds:[],departTree:[],visible:!1,form:this.$form.createForm(this),loading:!1,expandedKeys:[]}},computed:{getType:function(){return 1==this.multi?"checkbox":"radio"}},watch:{userIds:{immediate:!0,handler:function(){this.initUserNames()}}},created:function(){this.resetScreenSize(),this.loadData()},methods:{initUserNames:function(){var e=this;if(this.userIds){var t=this.userIds.split(",")+",",n=m({},this.store,t);Object(l["c"])("/sys/user/getMultiUser",n).then((function(t){e.selectionRows=[];var n=[],r=[];if(t&&t.length>0){var a,i=p(t);try{for(i.s();!(a=i.n()).done;){var s=a.value;r.push(s[e.text]),n.push(s["id"]),e.selectionRows.push(s)}}catch(o){i.e(o)}finally{i.f()}}e.selectedRowKeys=n,e.$emit("initComp",r.join(","))}))}else this.$emit("initComp",""),this.selectedRowKeys=[]},loadData:function(){var e=d(s.a.mark((function e(t){var n,r=this;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:1===t&&(this.ipagination.current=1),n=this.getQueryParams(),this.loading=!0,Object(l["c"])("/sys/user/queryUserComponentData",n).then((function(e){e.success&&(r.dataSource=e.result.records,r.ipagination.total=e.result.total)})).finally((function(){r.loading=!1}));case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),resetScreenSize:function(){var e=document.body.clientWidth;this.scrollTrigger=e<500?{x:800}:{}},showModal:function(){this.visible=!0,this.queryDepartTree(),this.initUserNames(),this.loadData(),this.form.resetFields()},getQueryParams:function(){var e=Object.assign({},this.queryParam,this.isorter);return e.field=this.getQueryField(),e.pageNo=this.ipagination.current,e.pageSize=this.ipagination.pageSize,e.departId=this.selectedDepIds.join(","),Object(o["d"])(e)},getQueryField:function(){for(var e="id,",t=0;t<this.columns.length;t++)e+=","+this.columns[t].dataIndex;return e},searchReset:function(e){var t=this;t.selectedRowKeys=[],t.selectUserIds=[],t.selectedDepIds=[],0!==e&&(t.queryParam={},t.loadData(1))},close:function(){this.searchReset(0),this.visible=!1},handleTableChange:function(e,t,n){Object.keys(n).length>0&&(this.isorter.column=n.field,this.isorter.order="ascend"===n.order?"asc":"desc"),this.ipagination=e,this.loadData()},handleSubmit:function(){var e=this;this.getSelectUserRows(),e.$emit("ok",e.selectUserRows),e.searchReset(0),e.close()},getSelectUserRows:function(){this.selectUserRows=[];var e,t=p(this.selectionRows);try{for(t.s();!(e=t.n()).done;){var n=e.value;this.selectedRowKeys.includes(n.id)&&this.selectUserRows.push(n)}}catch(r){t.e(r)}finally{t.f()}this.selectUserIds=this.selectUserRows.map((function(e){return e.username})).join(",")},onDepSelect:function(e){null!=e[0]&&(this.selectedDepIds[0]!==e[0]&&(this.selectedDepIds=[e[0]]),this.loadData(1))},onSelectChange:function(e,t){var n=this;this.selectedRowKeys=e,t.forEach((function(e){return Object(o["j"])(n.selectionRows,e,"id")}))},onSearch:function(){this.loadData(1)},initQueryUserByDepId:function(e){var t=this;return this.loading=!0,Object(c["O"])({id:e.toString()}).then((function(e){e.success&&(t.dataSource=e.result,t.ipagination.total=e.result.length)})).finally((function(){t.loading=!1}))},queryDepartTree:function(){var e=this;Object(c["D"])().then((function(t){t.success&&(e.departTree=t.result,e.expandedKeys=e.departTree.map((function(e){return e.id})))}))},modalFormOk:function(){this.loadData()}}},v=y,b=(n("9f14"),n("2877")),g=Object(b["a"])(v,r,a,!1,null,"9e8d090e",null);t["default"]=g.exports},a916:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("j-modal",{attrs:{title:e.title,width:e.modalWidth,visible:e.visible,confirmLoading:e.confirmLoading,switchFullscreen:"",wrapClassName:"j-popup-modal",cancelText:"关闭"},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[n("div",{staticClass:"table-page-search-wrapper"},[n("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchByquery(t)}}},[e.showSearchFlag?n("a-row",{attrs:{gutter:24}},[e._l(e.queryInfo,(function(t,r){return["1"===t.hidden?[n("a-col",{directives:[{name:"show",rawName:"v-show",value:e.toggleSearchStatus,expression:"toggleSearchStatus"}],key:"query"+r,attrs:{md:8,sm:24}},[n("online-query-form-item",{attrs:{queryParam:e.queryParam,item:t,dictOptions:e.dictOptions}})],1)]:[n("a-col",{key:"query"+r,attrs:{md:8,sm:24}},[n("online-query-form-item",{attrs:{queryParam:e.queryParam,item:t,dictOptions:e.dictOptions}})],1)]]})),n("a-col",{attrs:{md:8,sm:8}},[n("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[n("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchByquery}},[e._v("查询")]),n("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),n("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),n("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2):e._e()],1)],1),n("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[n("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v("\n    已选择 "),n("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.table.selectedRowKeys.length))]),e._v("项  \n    "),n("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")]),e.showSearchFlag?e._e():n("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onlyReload}},[e._v("刷新")])]),n("a-table",{ref:"table",staticStyle:{"min-height":"300px"},attrs:{size:"middle",bordered:"",rowKey:e.combineRowKey,columns:e.table.columns,dataSource:e.table.dataSource,pagination:e.table.pagination,loading:e.table.loading,rowSelection:{fixed:!0,selectedRowKeys:e.table.selectedRowKeys,onChange:e.handleChangeInTableSelect},scroll:e.tableScroll,customRow:e.clickThenCheck},on:{change:e.handleChangeInTable}})],1)},a=[],i=n("0fea"),s=n("ca00"),o=n("89f2"),c=n("a5d1");function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){d(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=m(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,o=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return s=e.done,e},e:function(e){o=!0,i=e},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw i}}}}function h(e){return v(e)||y(e)||m(e)||f()}function f(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return b(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?b(e,t):void 0}}function y(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function v(e){if(Array.isArray(e))return b(e)}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var g=1200,S={name:"JPopupOnlReport",props:["multi","code","sorter","groupId","param"],components:{},data:function(){return{visible:!1,title:"",confirmLoading:!1,queryInfo:[],toggleSearchStatus:!1,queryParam:{},dictOptions:{},url:{getColumns:"/online/cgreport/api/getRpColumns/",getData:"/online/cgreport/api/getData/",getQueryInfo:"/online/cgreport/api/getQueryInfo/"},table:{loading:!0,columns:[],dataSource:[],selectedRowKeys:[],selectionRows:[],pagination:{current:1,pageSize:10,pageSizeOptions:["10","20","30"],showTotal:function(e,t){return t[0]+"-"+t[1]+" 共"+e+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0}},cgRpConfigId:"",modalWidth:g,tableScroll:{x:!0},dynamicParam:{},iSorter:null}},mounted:function(){},watch:{code:function(){this.loadColumnsInfo()},param:{deep:!0,handler:function(){this.visible&&(this.dynamicParamHandler(),this.loadData())}},sorter:{immediate:!0,handler:function(){var e=this;if(this.sorter){var t=this.sorter.split("=");2===t.length&&["asc","desc"].includes(t[1].toLowerCase())&&(this.iSorter={column:t[0],order:t[1].toLowerCase()},this.table.columns.forEach((function(t){t.dataIndex===e.iSorter.column?e.$set(t,"sortOrder","asc"===e.iSorter.order?"ascend":"descend"):e.$set(t,"sortOrder",!1)})))}}}},computed:{showSearchFlag:function(){return this.queryInfo&&this.queryInfo.length>0}},methods:{loadColumnsInfo:function(){var e,t=this,n="".concat(this.url.getColumns).concat(this.code);this.groupId&&(e=this.groupId+n),Object(c["a"])((function(){return Object(i["c"])(n)}),e).then((function(e){if(e.success){t.initDictOptionData(e.result.dictOptions),t.cgRpConfigId=e.result.cgRpConfigId,t.title=e.result.cgRpConfigName;for(var n=e.result.columns,r=0;r<n.length;r++)n[r].customRender&&function(){var e=n[r].customRender;n[r].customRender=function(n){return Object(o["c"])(t.dictOptions[e],n+"")}}(),t.iSorter&&n[r].dataIndex===t.iSorter.column&&(n[r].sortOrder="asc"===t.iSorter.order?"ascend":"descend");t.table.columns=h(n),t.initQueryInfo()}}))},initQueryInfo:function(){var e,t=this,n="".concat(this.url.getQueryInfo).concat(this.cgRpConfigId);this.groupId&&(e=this.groupId+n),Object(c["a"])((function(){return Object(i["c"])(n)}),e).then((function(e){e.success?(t.dynamicParamHandler(e.result),t.queryInfo=e.result,t.loadData(1)):t.$message.warning(e.message)}))},dynamicParamHandler:function(e){var t=this;if(e&&e.length>0){var n,r={},a=p(e);try{for(a.s();!(n=a.n()).done;){var i=n.value;"single"===i.mode&&(r[i.field]="")}}catch(o){a.e(o)}finally{a.f()}this.queryParam=u({},r)}var s={};this.param&&Object.keys(this.param).map((function(e){var n=t.param[e];e in t.queryParam&&(n&&n.startsWith("'")&&n.endsWith("'")&&(n=n.substring(1,n.length-1)),t.queryParam[e]=n),s[e]=t.param[e]})),this.dynamicParam=u({},s)},loadData:function(e){var t=this;1==e&&(this.table.pagination.current=1);var n=this.getQueryParams();this.table.loading=!0;var r,a="".concat(this.url.getData).concat(this.cgRpConfigId);this.groupId&&(r=this.groupId+a+JSON.stringify(n)),Object(c["a"])((function(){return Object(i["c"])(a,n)}),r).then((function(e){t.table.loading=!1;var n=e.result;n?(t.table.pagination.total=Number(n.total),t.table.dataSource=n.records):(t.table.pagination.total=0,t.table.dataSource=[])}))},getQueryParams:function(){var e=this,t={};this.dynamicParam&&Object.keys(this.dynamicParam).map((function(n){t["self_"+n]=e.dynamicParam[n]}));var n=Object.assign(t,this.queryParam,this.iSorter);return n.pageNo=this.table.pagination.current,n.pageSize=this.table.pagination.pageSize,Object(s["d"])(n)},handleChangeInTableSelect:function(e,t){var n=this;if(e&&0!=e.length)if(e.length==t.length)this.table.selectionRows=t;else{for(var r=this.table.selectedRowKeys,a=this.table.selectionRows,i=0;i<t.length;i++){var s=this.combineRowKey(t[i]);r.indexOf(s)<0&&a.push(t[i])}this.table.selectionRows=a.filter((function(t){var r=n.combineRowKey(t);return e.indexOf(r)>=0}))}else this.table.selectionRows=[];this.table.selectedRowKeys=e},handleChangeInTable:function(e,t,n){var r=this;Object.keys(n).length>0&&(this.iSorter={column:n.field,order:"ascend"===n.order?"asc":"desc"},this.table.columns.forEach((function(e){e.dataIndex===n.field?r.$set(e,"sortOrder",n.order):r.$set(e,"sortOrder",!1)}))),this.table.pagination=e,this.loadData()},handleCancel:function(){this.close()},handleSubmit:function(){return!this.multi&&this.table.selectionRows&&this.table.selectionRows.length>1?(this.$message.warning("请选择一条记录"),!1):this.table.selectionRows&&0!=this.table.selectionRows.length?(this.$emit("ok",this.table.selectionRows),void this.close()):(this.$message.warning("请选择一条记录"),!1)},close:function(){this.$emit("close"),this.visible=!1,this.onClearSelected()},show:function(){this.visible=!0,this.loadColumnsInfo()},handleToggleSearch:function(){this.toggleSearchStatus=!this.toggleSearchStatus},searchByquery:function(){this.loadData(1)},onlyReload:function(){this.loadData()},searchReset:function(){var e=this;Object.keys(this.queryParam).forEach((function(t){e.queryParam[t]=""})),this.loadData(1)},onClearSelected:function(){this.table.selectedRowKeys=[],this.table.selectionRows=[]},combineRowKey:function(e){var t="";return Object.keys(e).forEach((function(n){"id"==n?t=e[n]+t:t+=e[n]})),t.length>50&&(t=t.substring(0,50)),t},clickThenCheck:function(e){var t=this;return{on:{click:function(){var n=t.combineRowKey(e);if(t.table.selectedRowKeys&&0!=t.table.selectedRowKeys.length)if(t.table.selectedRowKeys.indexOf(n)<0)t.table.selectedRowKeys.push(n),t.table.selectionRows.push(e);else{var r=t.table.selectedRowKeys.indexOf(n);t.table.selectedRowKeys.splice(r,1),t.table.selectionRows.splice(r,1)}else{var a=[],i=[];a.push(e),i.push(n),t.table.selectedRowKeys=i,t.table.selectionRows=a}}}}},initDictOptionData:function(e){var t={};Object.keys(e).map((function(n){t[n]=e[n].filter((function(e){return null!=e}))})),this.dictOptions=t}}},w=S,k=n("2877"),x=Object(k["a"])(w,r,a,!1,null,"b3ef99c8",null);t["default"]=x.exports},b0cd:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("j-modal",{attrs:{title:"选择部门",width:e.modalWidth,visible:e.visible,confirmLoading:e.confirmLoading,wrapClassName:"j-depart-select-modal",switchFullscreen:"",cancelText:"关闭"},on:{ok:e.handleSubmit,cancel:e.handleCancel,"update:fullscreen":e.isFullscreen}},[n("a-spin",{attrs:{tip:"Loading...",spinning:!1}},[n("a-input-search",{staticStyle:{"margin-bottom":"1px"},attrs:{placeholder:"请输入部门名称按回车进行搜索"},on:{search:e.onSearch}}),n("a-tree",{class:e.treeScreenClass,attrs:{checkable:"",treeData:e.treeData,checkStrictly:e.checkStrictly,autoExpandParent:e.autoExpandParent,expandedKeys:e.expandedKeys,checkedKeys:e.checkedKeys},on:{check:e.onCheck,select:e.onSelect,expand:e.onExpand},scopedSlots:e._u([{key:"title",fn:function(t){var r=t.title;return[r.indexOf(e.searchValue)>-1?n("span",[e._v("\n          "+e._s(r.substr(0,r.indexOf(e.searchValue)))+"\n          "),n("span",{staticStyle:{color:"#f50"}},[e._v(e._s(e.searchValue))]),e._v("\n          "+e._s(r.substr(r.indexOf(e.searchValue)+e.searchValue.length))+"\n        ")]):n("span",[e._v(e._s(r))])]}}])})],1),e.treeOpera&&e.multi?n("template",{slot:"footer"},[n("div",{staticClass:"drawer-bootom-button"},[n("a-dropdown",{staticStyle:{float:"left"},attrs:{trigger:["click"],placement:"topCenter"}},[n("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[n("a-menu-item",{key:"1",on:{click:function(t){return e.switchCheckStrictly(1)}}},[e._v("父子关联")]),n("a-menu-item",{key:"2",on:{click:function(t){return e.switchCheckStrictly(2)}}},[e._v("取消关联")])],1),n("a-button",[e._v("\n          树操作 "),n("a-icon",{attrs:{type:"up"}})],1)],1),n("a-button",{staticStyle:{"margin-right":"0.8rem"},attrs:{type:"primary"},on:{click:e.handleCancel}},[e._v("关闭")]),n("a-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确认")])],1)]):e._e()],2)},a=[],i=n("4ec3");function s(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=l(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,o=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return s=e.done,e},e:function(e){o=!0,i=e},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw i}}}}function o(e){return d(e)||u(e)||l(e)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,t){if(e){if("string"===typeof e)return p(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(e,t):void 0}}function u(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function d(e){if(Array.isArray(e))return p(e)}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var h={name:"JSelectDepartModal",props:["modalWidth","multi","rootOpened","departId","store","text","treeOpera"],data:function(){return{visible:!1,confirmLoading:!1,treeData:[],autoExpandParent:!0,expandedKeys:[],dataList:[],checkedKeys:[],checkedRows:[],searchValue:"",checkStrictly:!0,fullscreen:!1}},created:function(){this.loadDepart()},watch:{departId:function(){this.initDepartComponent()},visible:{handler:function(){this.initDepartComponent(!0)}}},computed:{treeScreenClass:function(){return{"my-dept-select-tree":!0,fullscreen:this.fullscreen}}},methods:{show:function(){this.visible=!0,this.checkedRows=[],this.checkedKeys=[]},loadDepart:function(){var e=this;Object(i["D"])().then((function(t){if(t.success){var n=o(t.result);e.reWriterWithSlot(n),e.treeData=n,e.initDepartComponent(),e.rootOpened&&e.initExpandedKeys(t.result)}}))},initDepartComponent:function(e){var t=[],n=1==e?"key":this.text;if(this.departId){var r,a=this.departId.split(","),i=s(this.dataList);try{for(i.s();!(r=i.n()).done;){var o=r.value;a.indexOf(o[this.store])>=0&&t.push(o[n])}}catch(c){i.e(c)}finally{i.f()}}1==e?this.checkedKeys=[].concat(t):this.$emit("initComp",t.join(","))},reWriterWithSlot:function(e){var t,n=s(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;if(r.children&&r.children.length>0){this.reWriterWithSlot(r.children);var a=Object.assign({},r);a.children={},this.dataList.push(a)}else this.dataList.push(r),r.scopedSlots={title:"title"}}}catch(i){n.e(i)}finally{n.f()}},initExpandedKeys:function(e){if(e&&e.length>0){var t,n=[],r=s(e);try{for(r.s();!(t=r.n()).done;){var a=t.value;a.children&&a.children.length>0&&n.push(a.id)}}catch(i){r.e(i)}finally{r.f()}this.expandedKeys=[].concat(n)}else this.expandedKeys=[]},onCheck:function(e,t){var n=this;if(this.multi)this.checkStrictly?this.checkedKeys=e.checked:this.checkedKeys=e,this.checkedRows=this.getCheckedRows(this.checkedKeys);else{var r=e.checked.filter((function(e){return n.checkedKeys.indexOf(e)<0}));this.checkedKeys=o(r),this.checkedRows=0===this.checkedKeys.length?[]:[t.node.dataRef]}},onSelect:function(e,t){if(this.checkStrictly){var n=[];if(n.push(e[0]),this.checkedKeys&&0!==this.checkedKeys.length&&this.multi){var r,a=t.node.dataRef.key;if(this.checkedKeys.indexOf(a)>=0)this.checkedKeys=this.checkedKeys.filter((function(e){return e!==a}));else(r=this.checkedKeys).push.apply(r,n)}else this.checkedKeys=[].concat(n),this.checkedRows=[t.node.dataRef];this.checkedRows=this.getCheckedRows(this.checkedKeys)}},onExpand:function(e){this.expandedKeys=e,this.autoExpandParent=!1},handleSubmit:function(){if(this.checkedKeys&&0!=this.checkedKeys.length){var e=this.getCheckedRows(this.checkedKeys),t=this.checkedKeys.join(",");this.$emit("ok",e,t)}else this.$emit("ok","");this.handleClear()},handleCancel:function(){this.handleClear()},handleClear:function(){this.visible=!1,this.checkedKeys=[]},getParentKey:function(e,t){for(var n,r=0;r<t.length;r++){var a=t[r];a.children&&(a.children.some((function(t){return t.key===e}))?n=a.key:this.getParentKey(e,a.children)&&(n=this.getParentKey(e,a.children)))}return n},onSearch:function(e){var t=this,n=this.dataList.map((function(n){return n.title.indexOf(e)>-1?t.getParentKey(n.key,t.treeData):null})).filter((function(e,t,n){return e&&n.indexOf(e)===t}));Object.assign(this,{expandedKeys:n,searchValue:e,autoExpandParent:!0})},getCheckedRows:function(e){var t,n=function e(t,n){var r,a=s(t);try{for(a.s();!(r=a.n()).done;){var i=r.value;if(i.id===n)return i;if(i.children instanceof Array){var o=e(i.children,n);if(null!=o)return o}}}catch(c){a.e(c)}finally{a.f()}return null},r=[],a=s(e);try{for(a.s();!(t=a.n()).done;){var i=t.value,o=n(this.treeData,i);null!=o&&r.push(o)}}catch(c){a.e(c)}finally{a.f()}return r},switchCheckStrictly:function(e){1==e?this.checkStrictly=!1:2==e&&(this.checkStrictly=!0)},isFullscreen:function(e){this.fullscreen=e}}},f=h,m=(n("ccd3"),n("2877")),y=Object(m["a"])(f,r,a,!1,null,"5adb723c",null);t["default"]=y.exports},b16a:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("j-modal",{attrs:{centered:"",title:e.name+"选择",width:e.width,visible:e.visible,switchFullscreen:"",cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.close}},[n("a-row",{attrs:{gutter:18}},[n("a-col",{attrs:{span:16}},[n("a-form",{staticClass:"j-inline-form",attrs:{layout:"inline"}},[n("a-form-item",{attrs:{label:e.queryParamText||e.name}},[n("a-input",{attrs:{placeholder:"请输入"+(e.queryParamText||e.name)},on:{pressEnter:e.searchQuery},model:{value:e.queryParam[e.queryParamCode||e.valueKey],callback:function(t){e.$set(e.queryParam,e.queryParamCode||e.valueKey,t)},expression:"queryParam[queryParamCode||valueKey]"}})],1),e.queryConfig.length>0?n("j-select-biz-query-item",{directives:[{name:"show",rawName:"v-show",value:e.showMoreQueryItems,expression:"showMoreQueryItems"}],attrs:{queryParam:e.queryParam,queryConfig:e.queryConfig},on:{pressEnter:e.searchQuery}}):e._e(),n("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),n("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),e.queryConfig.length>0?n("a",{staticStyle:{"margin-left":"8px"},on:{click:function(t){e.showMoreQueryItems=!e.showMoreQueryItems}}},[e._v("\n          "+e._s(e.showMoreQueryItems?"收起":"展开")+"\n          "),n("a-icon",{attrs:{type:e.showMoreQueryItems?"up":"down"}})],1):e._e()],1),n("a-table",{attrs:{size:"middle",bordered:"",rowKey:e.rowKey,columns:e.innerColumns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,scroll:{y:240},rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange,type:e.multiple?"checkbox":"radio"},customRow:e.customRowFn},on:{change:e.handleTableChange}})],1),n("a-col",{attrs:{span:8}},[n("a-card",{attrs:{title:"已选"+e.name,bordered:!1,"head-style":{padding:0},"body-style":{padding:0}}},[n("a-table",e._b({attrs:{size:"middle",rowKey:e.rowKey,bordered:""},scopedSlots:e._u([{key:"action",fn:function(t,r,a){return n("span",{},[n("a",{on:{click:function(t){return e.handleDeleteSelected(r,a)}}},[e._v("删除")])])}}])},"a-table",e.selectedTable,!1))],1)],1)],1)],1)},a=[],i=n("0fea"),s=n("c4db"),o=n("b65a"),c=n("ca00"),l=n("2638"),u=n.n(l),d={name:"JSelectBizQueryItem",props:{queryParam:Object,queryConfig:Array},data:function(){return{}},methods:{renderQueryItem:function(){var e=this,t=this.$createElement;return this.queryConfig.map((function(n){var r,a=n.key,i=n.label,s=n.placeholder,o=n.dictCode,c=n.props,l=n.customRender,d={props:{},on:{pressEnter:function(){return e.$emit("pressEnter")}}};return null!=c&&Object.assign(d.props,c),d.props["placeholder"]=void 0===s?o?"请选择".concat(i):"请输入".concat(i):s,r="function"===typeof l?l.call(e,{key:a,options:d,queryParam:e.queryParam}):o?t("j-dict-select-tag",u()([{},d,{attrs:{dictCode:o},style:"width:180px;",model:{value:e.queryParam[a],callback:function(t){e.$set(e.queryParam,a,t)}}}])):t("a-input",u()([{},d,{model:{value:e.queryParam[a],callback:function(t){e.$set(e.queryParam,a,t)}}}])),t("a-form-item",{key:a,attrs:{label:i}},[r])}))}},render:function(){var e=arguments[0];return e("span",[this.renderQueryItem()])}};function p(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=h(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,o=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return s=e.done,e},e:function(e){o=!0,i=e},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw i}}}}function h(e,t){if(e){if("string"===typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){v(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b={name:"JSelectBizComponentModal",mixins:[o["a"]],components:{Ellipsis:s["a"],JSelectBizQueryItem:d},props:{value:{type:Array,default:function(){return[]}},visible:{type:Boolean,default:!1},valueKey:{type:String,required:!0},multiple:{type:Boolean,default:!0},width:{type:Number,default:900},name:{type:String,default:""},listUrl:{type:String,required:!0,default:""},valueUrl:{type:String,default:""},displayKey:{type:String,default:null},columns:{type:Array,required:!0,default:function(){return[]}},queryParamCode:{type:String,default:null},queryParamText:{type:String,default:null},queryConfig:{type:Array,default:function(){return[]}},rowKey:{type:String,default:"id"},ellipsisLength:{type:Number,default:12}},data:function(){var e=this,t=this.$createElement;return{innerValue:[],selectedTable:{pagination:!1,scroll:{y:240},columns:[y(y({},this.columns[0]),{},{width:this.columns[0].widthRight||this.columns[0].width}),{title:"操作",dataIndex:"action",align:"center",width:60,scopedSlots:{customRender:"action"}}],dataSource:[]},renderEllipsis:function(n){return t("ellipsis",{attrs:{length:e.ellipsisLength}},[n])},url:{list:this.listUrl},ipagination:{current:1,pageSize:5,pageSizeOptions:["5","10","20","30"],showTotal:function(e,t){return t[0]+"-"+t[1]+" 共"+e+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0},options:[],dataSourceMap:{},showMoreQueryItems:!1}},computed:{innerColumns:function(){var e=this,t=Object(c["b"])(this.columns);return t.forEach((function(t){-1!==e.ellipsisLength&&(t.customRender=function(t){return e.renderEllipsis(t)})})),t}},watch:{value:{deep:!0,immediate:!0,handler:function(e){this.innerValue=Object(c["b"])(e),this.selectedRowKeys=[],this.valueWatchHandler(e),this.queryOptionsByValue(e)}},dataSource:{deep:!0,handler:function(e){this.emitOptions(e),this.valueWatchHandler(this.innerValue)}},selectedRowKeys:{immediate:!0,deep:!0,handler:function(e){var t=this;this.innerValue&&(this.innerValue.length=0),this.selectedTable.dataSource=e.map((function(e){var n,r=p(t.dataSource);try{for(r.s();!(n=r.n()).done;){var a=n.value;if(a[t.rowKey]===e)return Object(c["j"])(t.innerValue,a[t.valueKey]),a}}catch(l){r.e(l)}finally{r.f()}var i,s=p(t.selectedTable.dataSource);try{for(s.s();!(i=s.n()).done;){var o=i.value;if(o[t.rowKey]===e)return Object(c["j"])(t.innerValue,o[t.valueKey]),o}}catch(l){s.e(l)}finally{s.f()}return{}}))}}},methods:{close:function(){this.$emit("update:visible",!1)},valueWatchHandler:function(e){var t=this;e.forEach((function(e){t.dataSource.concat(t.selectedTable.dataSource).forEach((function(n){n[t.valueKey]===e&&Object(c["j"])(t.selectedRowKeys,n[t.rowKey])}))}))},queryOptionsByValue:function(e){var t,n=this;if(e&&0!==e.length){var r,a=!1,s=p(e);try{for(s.s();!(r=s.n()).done;){var o,l=r.value,u=!1,d=p(this.options);try{for(d.s();!(o=d.n()).done;){var h=o.value;if(l===h.value){u=!0;break}}}catch(f){d.e(f)}finally{d.f()}if(!u){a=!0;break}}}catch(f){s.e(f)}finally{s.f()}a&&Object(i["c"])(this.valueUrl||this.listUrl,(t={},v(t,this.valueKey,e.join(",")+","),v(t,"pageNo",1),v(t,"pageSize",e.length),t)).then((function(e){if(e.success){var t=e.result;t instanceof Array||(t=e.result.records),n.emitOptions(t,(function(e){Object(c["j"])(n.innerValue,e[n.valueKey]),Object(c["j"])(n.selectedRowKeys,e[n.rowKey]),Object(c["j"])(n.selectedTable.dataSource,e,n.rowKey)}))}}))}},emitOptions:function(e,t){var n=this;e.forEach((function(e){var r=e[n.valueKey];n.dataSourceMap[r]=e,Object(c["j"])(n.options,{label:e[n.displayKey||n.valueKey],value:r},"value"),"function"===typeof t&&t(e)})),this.$emit("options",this.options,this.dataSourceMap)},handleOk:function(){var e=this,t=this.selectedTable.dataSource.map((function(t){return t[e.valueKey]}));this.$emit("input",t),this.close()},handleDeleteSelected:function(e,t){this.selectedRowKeys.splice(this.selectedRowKeys.indexOf(e[this.rowKey]),1),this.selectedTable.dataSource.splice(this.selectedTable.dataSource.indexOf(e),1),this.innerValue.splice(this.innerValue.indexOf(e[this.valueKey]),1)},customRowFn:function(e){var t=this;return{on:{click:function(){var n=e[t.rowKey];if(t.multiple){var r=t.selectedRowKeys.indexOf(n);-1===r?(t.selectedRowKeys.push(n),t.selectedTable.dataSource.push(e)):t.handleDeleteSelected(e,r)}else t.selectedRowKeys=[n],t.selectedTable.dataSource=[e]}}}}}},g=b,S=(n("35f1"),n("2877")),w=Object(S["a"])(g,r,a,!1,null,"53863efc",null);t["default"]=w.exports},b296:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("a-row",{staticClass:"j-select-biz-component-box",attrs:{type:"flex",gutter:8}},[n("a-col",{staticClass:"left",class:{full:!e.buttons}},[e._t("left",[n("a-select",{staticStyle:{width:"100%"},attrs:{mode:"multiple",placeholder:e.placeholder,options:e.selectOptions,allowClear:"",disabled:e.disabled,open:e.selectOpen},on:{dropdownVisibleChange:e.handleDropdownVisibleChange},nativeOn:{click:function(t){e.visible=!e.buttons&&!e.disabled||e.visible}},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}})])],2),e.buttons?n("a-col",{staticClass:"right"},[n("a-button",{attrs:{type:"primary",icon:"search",disabled:e.disabled},on:{click:function(t){e.visible=!0}}},[e._v(e._s(e.selectButtonText))])],1):e._e(),n("j-select-biz-component-modal",e._b({attrs:{visible:e.visible},on:{"update:visible":function(t){e.visible=t},options:e.handleOptions},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}},"j-select-biz-component-modal",e.modalProps,!1))],1)},a=[],i=n("b16a"),s={name:"JSelectBizComponent",components:{JSelectBizComponentModal:i["default"]},props:{value:{type:String,default:""},returnId:{type:Boolean,default:!1},placeholder:{type:String,default:"请选择"},disabled:{type:Boolean,default:!1},multiple:{type:Boolean,default:!0},buttons:{type:Boolean,default:!0},displayKey:{type:String,default:null},returnKeys:{type:Array,default:function(){return["id","id"]}},selectButtonText:{type:String,default:"选择"}},data:function(){return{selectValue:[],selectOptions:[],dataSourceMap:{},visible:!1,selectOpen:!1}},computed:{valueKey:function(){return this.returnId?this.returnKeys[0]:this.returnKeys[1]},modalProps:function(){return Object.assign({valueKey:this.valueKey,multiple:this.multiple,returnKeys:this.returnKeys,displayKey:this.displayKey||this.valueKey},this.$attrs)}},watch:{value:{immediate:!0,handler:function(e){this.selectValue=e?e.split(","):[]}},selectValue:{deep:!0,handler:function(e){var t=this,n=e.map((function(e){return t.dataSourceMap[e]})),r=e.join(",");r!==this.value&&(this.$emit("select",n),this.$emit("input",r),this.$emit("change",r))}}},methods:{handleOptions:function(e,t){this.selectOptions=e,this.dataSourceMap=t},handleDropdownVisibleChange:function(){var e=this;this.selectOpen=!0,this.$nextTick((function(){e.selectOpen=!1}))}}},o=s,c=(n("e01a"),n("2877")),l=Object(c["a"])(o,r,a,!1,null,"7c43cf42",null);t["default"]=l.exports},c14a:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("a-input-search",{attrs:{placeholder:"请先选择用户",readOnly:"",unselectable:"on"},on:{search:e.onSearchDepUser},model:{value:e.textVals,callback:function(t){e.textVals=t},expression:"textVals"}},[n("a-button",{attrs:{slot:"enterButton",disabled:e.disabled},slot:"enterButton"},[e._v("选择用户")])],1),n("j-select-user-by-dep-modal",{ref:"selectModal",attrs:{"modal-width":e.modalWidth,multi:e.multi,"user-ids":e.value,store:e.storeField,text:e.textField},on:{ok:e.selectOK,initComp:e.initComp}})],1)},a=[],i=n("a505"),s=n("a9c5");function o(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=c(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,o=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return s=e.done,e},e:function(e){o=!0,i=e},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw i}}}}function c(e,t){if(e){if("string"===typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u={name:"JSelectUserByDep",components:{JSelectUserByDepModal:i["default"]},props:{modalWidth:{type:Number,default:1250,required:!1},value:{type:String,required:!1},disabled:{type:Boolean,required:!1,default:!1},multi:{type:Boolean,default:!0,required:!1},backUser:{type:Boolean,default:!1,required:!1},store:{type:String,default:"username",required:!1},text:{type:String,default:"realname",required:!1}},data:function(){return{storeVals:"",textVals:""}},computed:{storeField:function(){var e=this.customReturnField;return e||(e=this.store),Object(s["c"])(e)},textField:function(){return Object(s["c"])(this.text)}},mounted:function(){this.storeVals=this.value},watch:{value:function(e){this.storeVals=e}},model:{prop:"value",event:"change"},methods:{initComp:function(e){this.textVals=e},backDeparInfo:function(){if(!0===this.backUser&&this.storeVals&&this.storeVals.length>0){for(var e=this.storeVals.split(","),t=this.textVals.split(","),n=[],r=0;r<e.length;r++)n.push({value:e[r],text:t[r]});this.$emit("back",n)}},onSearchDepUser:function(){this.$refs.selectModal.showModal()},selectOK:function(e){if(e){var t,n=[],r=[],a=o(e);try{for(a.s();!(t=a.n()).done;){var i=t.value;n.push(i[this.storeField]),r.push(i[this.textField])}}catch(s){a.e(s)}finally{a.f()}this.storeVals=n.join(","),this.textVals=r.join(",")}else this.storeVals="",this.textVals="";this.$emit("change",this.storeVals)}}},d=u,p=n("2877"),h=Object(p["a"])(d,r,a,!1,null,"1cdc4e39",null);t["default"]=h.exports},ccd3:function(e,t,n){"use strict";var r=n("921e"),a=n.n(r);a.a},e01a:function(e,t,n){"use strict";var r=n("3ad3"),a=n.n(r);a.a},fe54:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"components-input-demo-presuffix"},[n("a-input",{attrs:{placeholder:"请点击选择部门",readOnly:"",disabled:e.disabled},on:{click:e.openModal},model:{value:e.textVals,callback:function(t){e.textVals=t},expression:"textVals"}},[n("a-icon",{attrs:{slot:"prefix",type:"cluster",title:"部门选择控件"},slot:"prefix"}),e.storeVals?n("a-icon",{attrs:{slot:"suffix",type:"close-circle",title:"清空"},on:{click:e.handleEmpty},slot:"suffix"}):e._e()],1),n("j-select-depart-modal",{ref:"innerDepartSelectModal",attrs:{"modal-width":e.modalWidth,multi:e.multi,rootOpened:e.rootOpened,"depart-id":e.value,store:e.storeField,text:e.textField,treeOpera:e.treeOpera},on:{ok:e.handleOK,initComp:e.initComp}})],1)},a=[],i=n("b0cd"),s=n("a9c5");function o(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=c(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,o=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return s=e.done,e},e:function(e){o=!0,i=e},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw i}}}}function c(e,t){if(e){if("string"===typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u={name:"JSelectDepart",components:{JSelectDepartModal:i["default"]},props:{modalWidth:{type:Number,default:500,required:!1},multi:{type:Boolean,default:!1,required:!1},rootOpened:{type:Boolean,default:!0,required:!1},value:{type:String,required:!1},disabled:{type:Boolean,required:!1,default:!1},customReturnField:{type:String,default:""},backDepart:{type:Boolean,default:!1,required:!1},store:{type:String,default:"id",required:!1},text:{type:String,default:"departName",required:!1},treeOpera:{type:Boolean,default:!1,required:!1}},data:function(){return{visible:!1,confirmLoading:!1,storeVals:"",textVals:""}},computed:{storeField:function(){var e=this.customReturnField;return e||(e=this.store),Object(s["c"])(e)},textField:function(){return Object(s["c"])(this.text)}},mounted:function(){this.storeVals=this.value},watch:{value:function(e){this.storeVals=e}},methods:{initComp:function(e){this.textVals=e},backDeparInfo:function(){if(!0===this.backDepart&&this.departIds&&this.departIds.length>0){for(var e=this.storeVals.split(","),t=this.textVals.split(","),n=[],r=0;r<e.length;r++)n.push({value:e[r],text:t[r]});this.$emit("back",n)}},openModal:function(){this.$refs.innerDepartSelectModal.show()},handleOK:function(e){if(!e&&e.length<=0)this.textVals="",this.storeVals="";else{var t,n=[],r=[],a=o(e);try{for(a.s();!(t=a.n()).done;){var i=t.value;n.push(i[this.storeField]),r.push(i[this.textField])}}catch(s){a.e(s)}finally{a.f()}this.storeVals=n.join(","),this.textVals=r.join(",")}this.$emit("change",this.storeVals),this.backDeparInfo()},getDepartNames:function(){return this.departNames},handleEmpty:function(){this.handleOK("")}},model:{prop:"value",event:"change"}},d=u,p=(n("67da"),n("2877")),h=Object(p["a"])(d,r,a,!1,null,"c6a612e0",null);t["default"]=h.exports}}]);