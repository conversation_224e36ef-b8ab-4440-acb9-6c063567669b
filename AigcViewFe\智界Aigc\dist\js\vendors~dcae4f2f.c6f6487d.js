(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~dcae4f2f"],{"56b3":function(e,t,n){(function(t,n){e.exports=n()})(0,(function(){"use strict";var e=navigator.userAgent,t=navigator.platform,n=/gecko\/\d/i.test(e),r=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),l=r||i||o,s=l&&(r?document.documentMode||6:+(o||i)[1]),a=!o&&/WebKit\//.test(e),u=a&&/Qt\/\d+\.\d+/.test(e),c=!o&&/Chrome\//.test(e),h=/Opera\//.test(e),f=/Apple Computer/.test(navigator.vendor),d=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),p=/PhantomJS/.test(e),g=!o&&/AppleWebKit/.test(e)&&/Mobile\/\w+/.test(e),v=/Android/.test(e),m=g||v||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),y=g||/Mac/.test(t),b=/\bCrOS\b/.test(e),w=/win/i.test(t),x=h&&e.match(/Version\/(\d*\.\d*)/);x&&(x=Number(x[1])),x&&x>=15&&(h=!1,a=!0);var C=y&&(u||h&&(null==x||x<12.11)),S=n||l&&s>=9;function k(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var L,T=function(e,t){var n=e.className,r=k(t).exec(n);if(r){var i=n.slice(r.index+r[0].length);e.className=n.slice(0,r.index)+(i?r[1]+i:"")}};function M(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function N(e,t){return M(e).appendChild(t)}function A(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function O(e,t,n,r){var i=A(e,t,n,r);return i.setAttribute("role","presentation"),i}function D(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function W(){var e;try{e=document.activeElement}catch(t){e=document.body||null}while(e&&e.shadowRoot&&e.shadowRoot.activeElement)e=e.shadowRoot.activeElement;return e}function P(e,t){var n=e.className;k(t).test(n)||(e.className+=(n?" ":"")+t)}function H(e,t){for(var n=e.split(" "),r=0;r<n.length;r++)n[r]&&!k(n[r]).test(t)&&(t+=" "+n[r]);return t}L=document.createRange?function(e,t,n,r){var i=document.createRange();return i.setEnd(r||e,n),i.setStart(e,t),i}:function(e,t,n){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch(i){return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r};var F=function(e){e.select()};function E(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function I(e,t,n){for(var r in t||(t={}),e)!e.hasOwnProperty(r)||!1===n&&t.hasOwnProperty(r)||(t[r]=e[r]);return t}function R(e,t,n,r,i){null==t&&(t=e.search(/[^\s\u00a0]/),-1==t&&(t=e.length));for(var o=r||0,l=i||0;;){var s=e.indexOf("\t",o);if(s<0||s>=t)return l+(t-o);l+=s-o,l+=n-l%n,o=s+1}}g?F=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:l&&(F=function(e){try{e.select()}catch(t){}});var z=function(){this.id=null,this.f=null,this.time=0,this.handler=E(this.onTimeout,this)};function B(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}z.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},z.prototype.set=function(e,t){this.f=t;var n=+new Date+e;(!this.id||n<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=n)};var G=50,U={toString:function(){return"CodeMirror.Pass"}},V={scroll:!1},K={origin:"*mouse"},j={origin:"+move"};function X(e,t,n){for(var r=0,i=0;;){var o=e.indexOf("\t",r);-1==o&&(o=e.length);var l=o-r;if(o==e.length||i+l>=t)return r+Math.min(l,t-i);if(i+=o-r,i+=n-i%n,r=o+1,i>=t)return r}}var Y=[""];function _(e){while(Y.length<=e)Y.push($(Y)+" ");return Y[e]}function $(e){return e[e.length-1]}function q(e,t){for(var n=[],r=0;r<e.length;r++)n[r]=t(e[r],r);return n}function Z(e,t,n){var r=0,i=n(t);while(r<e.length&&n(e[r])<=i)r++;e.splice(r,0,t)}function J(){}function Q(e,t){var n;return Object.create?n=Object.create(e):(J.prototype=e,n=new J),t&&I(t,n),n}var ee=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function te(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||ee.test(e))}function ne(e,t){return t?!!(t.source.indexOf("\\w")>-1&&te(e))||t.test(e):te(e)}function re(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ie=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function oe(e){return e.charCodeAt(0)>=768&&ie.test(e)}function le(e,t,n){while((n<0?t>0:t<e.length)&&oe(e.charAt(t)))t+=n;return t}function se(e,t,n){for(var r=t>n?-1:1;;){if(t==n)return t;var i=(t+n)/2,o=r<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:n;e(o)?n=o:t=o+r}}function ae(e,t,n,r){if(!e)return r(t,n,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<n&&l.to>t||t==n&&l.to==t)&&(r(Math.max(l.from,t),Math.min(l.to,n),1==l.level?"rtl":"ltr",o),i=!0)}i||r(t,n,"ltr")}var ue=null;function ce(e,t,n){var r;ue=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==n?r=i:ue=i),o.from==t&&(o.from!=o.to&&"before"!=n?r=i:ue=i)}return null!=r?r:ue}var he=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function n(n){return n<=247?e.charAt(n):1424<=n&&n<=1524?"R":1536<=n&&n<=1785?t.charAt(n-1536):1774<=n&&n<=2220?"r":8192<=n&&n<=8203?"w":8204==n?"b":"L"}var r=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,l=/[Lb1n]/,s=/[1n]/;function a(e,t,n){this.level=e,this.from=t,this.to=n}return function(e,t){var u="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!r.test(e))return!1;for(var c=e.length,h=[],f=0;f<c;++f)h.push(n(e.charCodeAt(f)));for(var d=0,p=u;d<c;++d){var g=h[d];"m"==g?h[d]=p:p=g}for(var v=0,m=u;v<c;++v){var y=h[v];"1"==y&&"r"==m?h[v]="n":o.test(y)&&(m=y,"r"==y&&(h[v]="R"))}for(var b=1,w=h[0];b<c-1;++b){var x=h[b];"+"==x&&"1"==w&&"1"==h[b+1]?h[b]="1":","!=x||w!=h[b+1]||"1"!=w&&"n"!=w||(h[b]=w),w=x}for(var C=0;C<c;++C){var S=h[C];if(","==S)h[C]="N";else if("%"==S){var k=void 0;for(k=C+1;k<c&&"%"==h[k];++k);for(var L=C&&"!"==h[C-1]||k<c&&"1"==h[k]?"1":"N",T=C;T<k;++T)h[T]=L;C=k-1}}for(var M=0,N=u;M<c;++M){var A=h[M];"L"==N&&"1"==A?h[M]="L":o.test(A)&&(N=A)}for(var O=0;O<c;++O)if(i.test(h[O])){var D=void 0;for(D=O+1;D<c&&i.test(h[D]);++D);for(var W="L"==(O?h[O-1]:u),P="L"==(D<c?h[D]:u),H=W==P?W?"L":"R":u,F=O;F<D;++F)h[F]=H;O=D-1}for(var E,I=[],R=0;R<c;)if(l.test(h[R])){var z=R;for(++R;R<c&&l.test(h[R]);++R);I.push(new a(0,z,R))}else{var B=R,G=I.length,U="rtl"==t?1:0;for(++R;R<c&&"L"!=h[R];++R);for(var V=B;V<R;)if(s.test(h[V])){B<V&&(I.splice(G,0,new a(1,B,V)),G+=U);var K=V;for(++V;V<R&&s.test(h[V]);++V);I.splice(G,0,new a(2,K,V)),G+=U,B=V}else++V;B<R&&I.splice(G,0,new a(1,B,R))}return"ltr"==t&&(1==I[0].level&&(E=e.match(/^\s+/))&&(I[0].from=E[0].length,I.unshift(new a(0,0,E[0].length))),1==$(I).level&&(E=e.match(/\s+$/))&&($(I).to-=E[0].length,I.push(new a(0,c-E[0].length,c)))),"rtl"==t?I.reverse():I}}();function fe(e,t){var n=e.order;return null==n&&(n=e.order=he(e.text,t)),n}var de=[],pe=function(e,t,n){if(e.addEventListener)e.addEventListener(t,n,!1);else if(e.attachEvent)e.attachEvent("on"+t,n);else{var r=e._handlers||(e._handlers={});r[t]=(r[t]||de).concat(n)}};function ge(e,t){return e._handlers&&e._handlers[t]||de}function ve(e,t,n){if(e.removeEventListener)e.removeEventListener(t,n,!1);else if(e.detachEvent)e.detachEvent("on"+t,n);else{var r=e._handlers,i=r&&r[t];if(i){var o=B(i,n);o>-1&&(r[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function me(e,t){var n=ge(e,t);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function ye(e,t,n){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),me(e,n||t.type,e,t),ke(t)||t.codemirrorIgnore}function be(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)-1==B(n,t[r])&&n.push(t[r])}function we(e,t){return ge(e,t).length>0}function xe(e){e.prototype.on=function(e,t){pe(this,e,t)},e.prototype.off=function(e,t){ve(this,e,t)}}function Ce(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Se(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function ke(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Le(e){Ce(e),Se(e)}function Te(e){return e.target||e.srcElement}function Me(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),y&&e.ctrlKey&&1==t&&(t=3),t}var Ne,Ae,Oe=function(){if(l&&s<9)return!1;var e=A("div");return"draggable"in e||"dragDrop"in e}();function De(e){if(null==Ne){var t=A("span","​");N(e,A("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Ne=t.offsetWidth<=1&&t.offsetHeight>2&&!(l&&s<8))}var n=Ne?A("span","​"):A("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}function We(e){if(null!=Ae)return Ae;var t=N(e,document.createTextNode("AخA")),n=L(t,0,1).getBoundingClientRect(),r=L(t,1,2).getBoundingClientRect();return M(e),!(!n||n.left==n.right)&&(Ae=r.right-n.right<3)}var Pe=3!="\n\nb".split(/\n/).length?function(e){var t=0,n=[],r=e.length;while(t<=r){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(n.push(o.slice(0,l)),t+=l+1):(n.push(o),t=i+1)}return n}:function(e){return e.split(/\r\n?|\n/)},He=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(t){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(n){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Fe=function(){var e=A("div");return"oncopy"in e||(e.setAttribute("oncopy","return;"),"function"==typeof e.oncopy)}(),Ee=null;function Ie(e){if(null!=Ee)return Ee;var t=N(e,A("span","x")),n=t.getBoundingClientRect(),r=L(t,0,1).getBoundingClientRect();return Ee=Math.abs(n.left-r.left)>1}var Re={},ze={};function Be(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Re[e]=t}function Ge(e,t){ze[e]=t}function Ue(e){if("string"==typeof e&&ze.hasOwnProperty(e))e=ze[e];else if(e&&"string"==typeof e.name&&ze.hasOwnProperty(e.name)){var t=ze[e.name];"string"==typeof t&&(t={name:t}),e=Q(t,e),e.name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Ue("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Ue("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Ve(e,t){t=Ue(t);var n=Re[t.name];if(!n)return Ve(e,"text/plain");var r=n(e,t);if(Ke.hasOwnProperty(t.name)){var i=Ke[t.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)r[l]=t.modeProps[l];return r}var Ke={};function je(e,t){var n=Ke.hasOwnProperty(e)?Ke[e]:Ke[e]={};I(t,n)}function Xe(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var n={};for(var r in t){var i=t[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function Ye(e,t){var n;while(e.innerMode){if(n=e.innerMode(t),!n||n.mode==e)break;t=n.state,e=n.mode}return n||{mode:e,state:t}}function _e(e,t,n){return!e.startState||e.startState(t,n)}var $e=function(e,t,n){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};function qe(e,t){if(t-=e.first,t<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");var n=e;while(!n.lines)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(t<o){n=i;break}t-=o}return n.lines[t]}function Ze(e,t,n){var r=[],i=t.line;return e.iter(t.line,n.line+1,(function(e){var o=e.text;i==n.line&&(o=o.slice(0,n.ch)),i==t.line&&(o=o.slice(t.ch)),r.push(o),++i})),r}function Je(e,t,n){var r=[];return e.iter(t,n,(function(e){r.push(e.text)})),r}function Qe(e,t){var n=t-e.height;if(n)for(var r=e;r;r=r.parent)r.height+=n}function et(e){if(null==e.parent)return null;for(var t=e.parent,n=B(t.lines,e),r=t.parent;r;t=r,r=r.parent)for(var i=0;;++i){if(r.children[i]==t)break;n+=r.children[i].chunkSize()}return n+t.first}function tt(e,t){var n=e.first;e:do{for(var r=0;r<e.children.length;++r){var i=e.children[r],o=i.height;if(t<o){e=i;continue e}t-=o,n+=i.chunkSize()}return n}while(!e.lines);for(var l=0;l<e.lines.length;++l){var s=e.lines[l],a=s.height;if(t<a)break;t-=a}return n+l}function nt(e,t){return t>=e.first&&t<e.first+e.size}function rt(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function it(e,t,n){if(void 0===n&&(n=null),!(this instanceof it))return new it(e,t,n);this.line=e,this.ch=t,this.sticky=n}function ot(e,t){return e.line-t.line||e.ch-t.ch}function lt(e,t){return e.sticky==t.sticky&&0==ot(e,t)}function st(e){return it(e.line,e.ch)}function at(e,t){return ot(e,t)<0?t:e}function ut(e,t){return ot(e,t)<0?e:t}function ct(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function ht(e,t){if(t.line<e.first)return it(e.first,0);var n=e.first+e.size-1;return t.line>n?it(n,qe(e,n).text.length):ft(t,qe(e,t.line).text.length)}function ft(e,t){var n=e.ch;return null==n||n>t?it(e.line,t):n<0?it(e.line,0):e}function dt(e,t){for(var n=[],r=0;r<t.length;r++)n[r]=ht(e,t[r]);return n}$e.prototype.eol=function(){return this.pos>=this.string.length},$e.prototype.sol=function(){return this.pos==this.lineStart},$e.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},$e.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},$e.prototype.eat=function(e){var t,n=this.string.charAt(this.pos);if(t="string"==typeof e?n==e:n&&(e.test?e.test(n):e(n)),t)return++this.pos,n},$e.prototype.eatWhile=function(e){var t=this.pos;while(this.eat(e));return this.pos>t},$e.prototype.eatSpace=function(){var e=this.pos;while(/[\s\u00a0]/.test(this.string.charAt(this.pos)))++this.pos;return this.pos>e},$e.prototype.skipToEnd=function(){this.pos=this.string.length},$e.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},$e.prototype.backUp=function(e){this.pos-=e},$e.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=R(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},$e.prototype.indentation=function(){return R(this.string,null,this.tabSize)-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},$e.prototype.match=function(e,t,n){if("string"!=typeof e){var r=this.string.slice(this.pos).match(e);return r&&r.index>0?null:(r&&!1!==t&&(this.pos+=r[0].length),r)}var i=function(e){return n?e.toLowerCase():e},o=this.string.substr(this.pos,e.length);if(i(o)==i(e))return!1!==t&&(this.pos+=e.length),!0},$e.prototype.current=function(){return this.string.slice(this.start,this.pos)},$e.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},$e.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},$e.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var pt=function(e,t){this.state=e,this.lookAhead=t},gt=function(e,t,n,r){this.state=t,this.doc=e,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};function vt(e,t,n,r){var i=[e.state.modeGen],o={};Lt(e,t.text,e.doc.mode,n,(function(e,t){return i.push(e,t)}),o,r);for(var l=n.state,s=function(r){n.baseTokens=i;var s=e.state.overlays[r],a=1,u=0;n.state=!0,Lt(e,t.text,s.mode,n,(function(e,t){var n=a;while(u<e){var r=i[a];r>e&&i.splice(a,1,e,i[a+1],r),a+=2,u=Math.min(e,r)}if(t)if(s.opaque)i.splice(n,a-n,e,"overlay "+t),a=n+2;else for(;n<a;n+=2){var o=i[n+1];i[n+1]=(o?o+" ":"")+"overlay "+t}}),o),n.state=l,n.baseTokens=null,n.baseTokenPos=1},a=0;a<e.state.overlays.length;++a)s(a);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function mt(e,t,n){if(!t.styles||t.styles[0]!=e.state.modeGen){var r=yt(e,et(t)),i=t.text.length>e.options.maxHighlightLength&&Xe(e.doc.mode,r.state),o=vt(e,t,r);i&&(r.state=i),t.stateAfter=r.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function yt(e,t,n){var r=e.doc,i=e.display;if(!r.mode.startState)return new gt(r,!0,t);var o=Tt(e,t,n),l=o>r.first&&qe(r,o-1).stateAfter,s=l?gt.fromSaved(r,l,o):new gt(r,_e(r.mode),o);return r.iter(o,t,(function(n){bt(e,n.text,s);var r=s.line;n.stateAfter=r==t-1||r%5==0||r>=i.viewFrom&&r<i.viewTo?s.save():null,s.nextLine()})),n&&(r.modeFrontier=s.line),s}function bt(e,t,n,r){var i=e.doc.mode,o=new $e(t,e.options.tabSize,n);o.start=o.pos=r||0,""==t&&wt(i,n.state);while(!o.eol())xt(i,o,n.state),o.start=o.pos}function wt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var n=Ye(e,t);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function xt(e,t,n,r){for(var i=0;i<10;i++){r&&(r[0]=Ye(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}gt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},gt.prototype.baseToken=function(e){if(!this.baseTokens)return null;while(this.baseTokens[this.baseTokenPos]<=e)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},gt.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},gt.fromSaved=function(e,t,n){return t instanceof pt?new gt(e,Xe(e.mode,t.state),n,t.lookAhead):new gt(e,Xe(e.mode,t),n)},gt.prototype.save=function(e){var t=!1!==e?Xe(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new pt(t,this.maxLookAhead):t};var Ct=function(e,t,n){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=n};function St(e,t,n,r){var i,o=e.doc,l=o.mode;t=ht(o,t);var s,a=qe(o,t.line),u=yt(e,t.line,n),c=new $e(a.text,e.options.tabSize,u);r&&(s=[]);while((r||c.pos<t.ch)&&!c.eol())c.start=c.pos,i=xt(l,c,u.state),r&&s.push(new Ct(c,i,Xe(o.mode,u.state)));return r?s:new Ct(c,i,u.state)}function kt(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";null==t[r]?t[r]=n[2]:new RegExp("(?:^|\\s)"+n[2]+"(?:$|\\s)").test(t[r])||(t[r]+=" "+n[2])}return e}function Lt(e,t,n,r,i,o,l){var s=n.flattenSpans;null==s&&(s=e.options.flattenSpans);var a,u=0,c=null,h=new $e(t,e.options.tabSize,r),f=e.options.addModeClass&&[null];""==t&&kt(wt(n,r.state),o);while(!h.eol()){if(h.pos>e.options.maxHighlightLength?(s=!1,l&&bt(e,t,r,h.pos),h.pos=t.length,a=null):a=kt(xt(n,h,r.state,f),o),f){var d=f[0].name;d&&(a="m-"+(a?d+" "+a:d))}if(!s||c!=a){while(u<h.start)u=Math.min(h.start,u+5e3),i(u,c);c=a}h.start=h.pos}while(u<h.pos){var p=Math.min(h.pos,u+5e3);i(p,c),u=p}}function Tt(e,t,n){for(var r,i,o=e.doc,l=n?-1:t-(e.doc.mode.innerMode?1e3:100),s=t;s>l;--s){if(s<=o.first)return o.first;var a=qe(o,s-1),u=a.stateAfter;if(u&&(!n||s+(u instanceof pt?u.lookAhead:0)<=o.modeFrontier))return s;var c=R(a.text,null,e.options.tabSize);(null==i||r>c)&&(i=s-1,r=c)}return i}function Mt(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var n=e.first,r=t-1;r>n;r--){var i=qe(e,r).stateAfter;if(i&&(!(i instanceof pt)||r+i.lookAhead<t)){n=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,n)}}var Nt=!1,At=!1;function Ot(){Nt=!0}function Dt(){At=!0}function Wt(e,t,n){this.marker=e,this.from=t,this.to=n}function Pt(e,t){if(e)for(var n=0;n<e.length;++n){var r=e[n];if(r.marker==t)return r}}function Ht(e,t){for(var n,r=0;r<e.length;++r)e[r]!=t&&(n||(n=[])).push(e[r]);return n}function Ft(e,t){e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],t.marker.attachLine(e)}function Et(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,s=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);if(s||o.from==t&&"bookmark"==l.type&&(!n||!o.marker.insertLeft)){var a=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);(r||(r=[])).push(new Wt(l,o.from,a?null:o.to))}}return r}function It(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,s=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);if(s||o.from==t&&"bookmark"==l.type&&(!n||o.marker.insertLeft)){var a=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);(r||(r=[])).push(new Wt(l,a?null:o.from-t,null==o.to?null:o.to-t))}}return r}function Rt(e,t){if(t.full)return null;var n=nt(e,t.from.line)&&qe(e,t.from.line).markedSpans,r=nt(e,t.to.line)&&qe(e,t.to.line).markedSpans;if(!n&&!r)return null;var i=t.from.ch,o=t.to.ch,l=0==ot(t.from,t.to),s=Et(n,i,l),a=It(r,o,l),u=1==t.text.length,c=$(t.text).length+(u?i:0);if(s)for(var h=0;h<s.length;++h){var f=s[h];if(null==f.to){var d=Pt(a,f.marker);d?u&&(f.to=null==d.to?null:d.to+c):f.to=i}}if(a)for(var p=0;p<a.length;++p){var g=a[p];if(null!=g.to&&(g.to+=c),null==g.from){var v=Pt(s,g.marker);v||(g.from=c,u&&(s||(s=[])).push(g))}else g.from+=c,u&&(s||(s=[])).push(g)}s&&(s=zt(s)),a&&a!=s&&(a=zt(a));var m=[s];if(!u){var y,b=t.text.length-2;if(b>0&&s)for(var w=0;w<s.length;++w)null==s[w].to&&(y||(y=[])).push(new Wt(s[w].marker,null,null));for(var x=0;x<b;++x)m.push(y);m.push(a)}return m}function zt(e){for(var t=0;t<e.length;++t){var n=e[t];null!=n.from&&n.from==n.to&&!1!==n.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Bt(e,t,n){var r=null;if(e.iter(t.line,n.line+1,(function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var n=e.markedSpans[t].marker;!n.readOnly||r&&-1!=B(r,n)||(r||(r=[])).push(n)}})),!r)return null;for(var i=[{from:t,to:n}],o=0;o<r.length;++o)for(var l=r[o],s=l.find(0),a=0;a<i.length;++a){var u=i[a];if(!(ot(u.to,s.from)<0||ot(u.from,s.to)>0)){var c=[a,1],h=ot(u.from,s.from),f=ot(u.to,s.to);(h<0||!l.inclusiveLeft&&!h)&&c.push({from:u.from,to:s.from}),(f>0||!l.inclusiveRight&&!f)&&c.push({from:s.to,to:u.to}),i.splice.apply(i,c),a+=c.length-3}}return i}function Gt(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function Ut(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function Vt(e){return e.inclusiveLeft?-1:0}function Kt(e){return e.inclusiveRight?1:0}function jt(e,t){var n=e.lines.length-t.lines.length;if(0!=n)return n;var r=e.find(),i=t.find(),o=ot(r.from,i.from)||Vt(e)-Vt(t);if(o)return-o;var l=ot(r.to,i.to)||Kt(e)-Kt(t);return l||t.id-e.id}function Xt(e,t){var n,r=At&&e.markedSpans;if(r)for(var i=void 0,o=0;o<r.length;++o)i=r[o],i.marker.collapsed&&null==(t?i.from:i.to)&&(!n||jt(n,i.marker)<0)&&(n=i.marker);return n}function Yt(e){return Xt(e,!0)}function _t(e){return Xt(e,!1)}function $t(e,t){var n,r=At&&e.markedSpans;if(r)for(var i=0;i<r.length;++i){var o=r[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!n||jt(n,o.marker)<0)&&(n=o.marker)}return n}function qt(e,t,n,r,i){var o=qe(e,t),l=At&&o.markedSpans;if(l)for(var s=0;s<l.length;++s){var a=l[s];if(a.marker.collapsed){var u=a.marker.find(0),c=ot(u.from,n)||Vt(a.marker)-Vt(i),h=ot(u.to,r)||Kt(a.marker)-Kt(i);if(!(c>=0&&h<=0||c<=0&&h>=0)&&(c<=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?ot(u.to,n)>=0:ot(u.to,n)>0)||c>=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?ot(u.from,r)<=0:ot(u.from,r)<0)))return!0}}}function Zt(e){var t;while(t=Yt(e))e=t.find(-1,!0).line;return e}function Jt(e){var t;while(t=_t(e))e=t.find(1,!0).line;return e}function Qt(e){var t,n;while(t=_t(e))e=t.find(1,!0).line,(n||(n=[])).push(e);return n}function en(e,t){var n=qe(e,t),r=Zt(n);return n==r?t:et(r)}function tn(e,t){if(t>e.lastLine())return t;var n,r=qe(e,t);if(!nn(e,r))return t;while(n=_t(r))r=n.find(1,!0).line;return et(r)+1}function nn(e,t){var n=At&&t.markedSpans;if(n)for(var r=void 0,i=0;i<n.length;++i)if(r=n[i],r.marker.collapsed){if(null==r.from)return!0;if(!r.marker.widgetNode&&0==r.from&&r.marker.inclusiveLeft&&rn(e,t,r))return!0}}function rn(e,t,n){if(null==n.to){var r=n.marker.find(1,!0);return rn(e,r.line,Pt(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if(i=t.markedSpans[o],i.marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(null==i.to||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&rn(e,t,i))return!0}function on(e){e=Zt(e);for(var t=0,n=e.parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==e)break;t+=i.height}for(var o=n.parent;o;n=o,o=n.parent)for(var l=0;l<o.children.length;++l){var s=o.children[l];if(s==n)break;t+=s.height}return t}function ln(e){if(0==e.height)return 0;var t,n=e.text.length,r=e;while(t=Yt(r)){var i=t.find(0,!0);r=i.from.line,n+=i.from.ch-i.to.ch}r=e;while(t=_t(r)){var o=t.find(0,!0);n-=r.text.length-o.from.ch,r=o.to.line,n+=r.text.length-o.to.ch}return n}function sn(e){var t=e.display,n=e.doc;t.maxLine=qe(n,n.first),t.maxLineLength=ln(t.maxLine),t.maxLineChanged=!0,n.iter((function(e){var n=ln(e);n>t.maxLineLength&&(t.maxLineLength=n,t.maxLine=e)}))}var an=function(e,t,n){this.text=e,Ut(this,t),this.height=n?n(this):1};function un(e,t,n,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Gt(e),Ut(e,n);var i=r?r(e):1;i!=e.height&&Qe(e,i)}function cn(e){e.parent=null,Gt(e)}an.prototype.lineNo=function(){return et(this)},xe(an);var hn={},fn={};function dn(e,t){if(!e||/^\s*$/.test(e))return null;var n=t.addModeClass?fn:hn;return n[e]||(n[e]=e.replace(/\S+/g,"cm-$&"))}function pn(e,t){var n=O("span",null,null,a?"padding-right: .1px":null),r={pre:O("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,l=void 0;r.pos=0,r.addToken=vn,We(e.display.measure)&&(l=fe(o,e.doc.direction))&&(r.addToken=yn(r.addToken,l)),r.map=[];var s=t!=e.display.externalMeasured&&et(o);wn(o,r,mt(e,o,s)),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=H(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=H(o.styleClasses.textClass,r.textClass||""))),0==r.map.length&&r.map.push(0,0,r.content.appendChild(De(e.display.measure))),0==i?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(a){var u=r.content.lastChild;(/\bcm-tab\b/.test(u.className)||u.querySelector&&u.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return me(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=H(r.pre.className,r.textClass||"")),r}function gn(e){var t=A("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function vn(e,t,n,r,i,o,a){if(t){var u,c=e.splitSpaces?mn(t,e.trailingSpace):t,h=e.cm.state.specialChars,f=!1;if(h.test(t)){u=document.createDocumentFragment();var d=0;while(1){h.lastIndex=d;var p=h.exec(t),g=p?p.index-d:t.length-d;if(g){var v=document.createTextNode(c.slice(d,d+g));l&&s<9?u.appendChild(A("span",[v])):u.appendChild(v),e.map.push(e.pos,e.pos+g,v),e.col+=g,e.pos+=g}if(!p)break;d+=g+1;var m=void 0;if("\t"==p[0]){var y=e.cm.options.tabSize,b=y-e.col%y;m=u.appendChild(A("span",_(b),"cm-tab")),m.setAttribute("role","presentation"),m.setAttribute("cm-text","\t"),e.col+=b}else"\r"==p[0]||"\n"==p[0]?(m=u.appendChild(A("span","\r"==p[0]?"␍":"␤","cm-invalidchar")),m.setAttribute("cm-text",p[0]),e.col+=1):(m=e.cm.options.specialCharPlaceholder(p[0]),m.setAttribute("cm-text",p[0]),l&&s<9?u.appendChild(A("span",[m])):u.appendChild(m),e.col+=1);e.map.push(e.pos,e.pos+1,m),e.pos++}}else e.col+=t.length,u=document.createTextNode(c),e.map.push(e.pos,e.pos+t.length,u),l&&s<9&&(f=!0),e.pos+=t.length;if(e.trailingSpace=32==c.charCodeAt(t.length-1),n||r||i||f||o){var w=n||"";r&&(w+=r),i&&(w+=i);var x=A("span",[u],w,o);if(a)for(var C in a)a.hasOwnProperty(C)&&"style"!=C&&"class"!=C&&x.setAttribute(C,a[C]);return e.content.appendChild(x)}e.content.appendChild(u)}}function mn(e,t){if(e.length>1&&!/  /.test(e))return e;for(var n=t,r="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!n||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),r+=o,n=" "==o}return r}function yn(e,t){return function(n,r,i,o,l,s,a){i=i?i+" cm-force-border":"cm-force-border";for(var u=n.pos,c=u+r.length;;){for(var h=void 0,f=0;f<t.length;f++)if(h=t[f],h.to>u&&h.from<=u)break;if(h.to>=c)return e(n,r,i,o,l,s,a);e(n,r.slice(0,h.to-u),i,o,null,s,a),o=null,r=r.slice(h.to-u),u=h.to}}}function bn(e,t,n,r){var i=!r&&n.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!r&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",n.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function wn(e,t,n){var r=e.markedSpans,i=e.text,o=0;if(r)for(var l,s,a,u,c,h,f,d=i.length,p=0,g=1,v="",m=0;;){if(m==p){a=u=c=s="",f=null,h=null,m=1/0;for(var y=[],b=void 0,w=0;w<r.length;++w){var x=r[w],C=x.marker;if("bookmark"==C.type&&x.from==p&&C.widgetNode)y.push(C);else if(x.from<=p&&(null==x.to||x.to>p||C.collapsed&&x.to==p&&x.from==p)){if(null!=x.to&&x.to!=p&&m>x.to&&(m=x.to,u=""),C.className&&(a+=" "+C.className),C.css&&(s=(s?s+";":"")+C.css),C.startStyle&&x.from==p&&(c+=" "+C.startStyle),C.endStyle&&x.to==m&&(b||(b=[])).push(C.endStyle,x.to),C.title&&((f||(f={})).title=C.title),C.attributes)for(var S in C.attributes)(f||(f={}))[S]=C.attributes[S];C.collapsed&&(!h||jt(h.marker,C)<0)&&(h=x)}else x.from>p&&m>x.from&&(m=x.from)}if(b)for(var k=0;k<b.length;k+=2)b[k+1]==m&&(u+=" "+b[k]);if(!h||h.from==p)for(var L=0;L<y.length;++L)bn(t,0,y[L]);if(h&&(h.from||0)==p){if(bn(t,(null==h.to?d+1:h.to)-p,h.marker,null==h.from),null==h.to)return;h.to==p&&(h=!1)}}if(p>=d)break;var T=Math.min(d,m);while(1){if(v){var M=p+v.length;if(!h){var N=M>T?v.slice(0,T-p):v;t.addToken(t,N,l?l+a:a,c,p+N.length==m?u:"",s,f)}if(M>=T){v=v.slice(T-p),p=T;break}p=M,c=""}v=i.slice(o,o=n[g++]),l=dn(n[g++],t.cm.options)}}else for(var A=1;A<n.length;A+=2)t.addToken(t,i.slice(o,o=n[A]),dn(n[A+1],t.cm.options))}function xn(e,t,n){this.line=t,this.rest=Qt(t),this.size=this.rest?et($(this.rest))-n+1:1,this.node=this.text=null,this.hidden=nn(e,t)}function Cn(e,t,n){for(var r,i=[],o=t;o<n;o=r){var l=new xn(e.doc,qe(e.doc,o),o);r=o+l.size,i.push(l)}return i}var Sn=null;function kn(e){Sn?Sn.ops.push(e):e.ownsGroup=Sn={ops:[e],delayedCallbacks:[]}}function Ln(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var r=0;r<e.ops.length;r++){var i=e.ops[r];if(i.cursorActivityHandlers)while(i.cursorActivityCalled<i.cursorActivityHandlers.length)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<t.length)}function Tn(e,t){var n=e.ownsGroup;if(n)try{Ln(n)}finally{Sn=null,t(n)}}var Mn=null;function Nn(e,t){var n=ge(e,t);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);Sn?r=Sn.delayedCallbacks:Mn?r=Mn:(r=Mn=[],setTimeout(An,0));for(var o=function(e){r.push((function(){return n[e].apply(null,i)}))},l=0;l<n.length;++l)o(l)}}function An(){var e=Mn;Mn=null;for(var t=0;t<e.length;++t)e[t]()}function On(e,t,n,r){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?Hn(e,t):"gutter"==o?En(e,t,n,r):"class"==o?Fn(e,t):"widget"==o&&In(e,t,r)}t.changes=null}function Dn(e){return e.node==e.text&&(e.node=A("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),l&&s<8&&(e.node.style.zIndex=2)),e.node}function Wn(e,t){var n=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),t.background)n?t.background.className=n:(t.background.parentNode.removeChild(t.background),t.background=null);else if(n){var r=Dn(t);t.background=r.insertBefore(A("div",null,n),r.firstChild),e.display.input.setUneditable(t.background)}}function Pn(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):pn(e,t)}function Hn(e,t){var n=t.text.className,r=Pn(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,Fn(e,t)):n&&(t.text.className=n)}function Fn(e,t){Wn(e,t),t.line.wrapClass?Dn(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var n=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=n||""}function En(e,t,n,r){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=Dn(t);t.gutterBackground=A("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=Dn(t),s=t.gutter=A("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(e.display.input.setUneditable(s),l.insertBefore(s,t.text),t.line.gutterClass&&(s.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=s.appendChild(A("div",rt(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var a=0;a<e.display.gutterSpecs.length;++a){var u=e.display.gutterSpecs[a].className,c=o.hasOwnProperty(u)&&o[u];c&&s.appendChild(A("div",[c],"CodeMirror-gutter-elt","left: "+r.gutterLeft[u]+"px; width: "+r.gutterWidth[u]+"px"))}}}function In(e,t,n){t.alignable&&(t.alignable=null);for(var r=k("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,r.test(i.className)&&t.node.removeChild(i);zn(e,t,n)}function Rn(e,t,n,r){var i=Pn(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),Fn(e,t),En(e,t,n,r),zn(e,t,r),t.node}function zn(e,t,n){if(Bn(e,t.line,t,n,!0),t.rest)for(var r=0;r<t.rest.length;r++)Bn(e,t.rest[r],t,n,!1)}function Bn(e,t,n,r,i){if(t.widgets)for(var o=Dn(n),l=0,s=t.widgets;l<s.length;++l){var a=s[l],u=A("div",[a.node],"CodeMirror-linewidget"+(a.className?" "+a.className:""));a.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),Gn(a,u,n,r),e.display.input.setUneditable(u),i&&a.above?o.insertBefore(u,n.gutter||n.text):o.appendChild(u),Nn(a,"redraw")}}function Gn(e,t,n,r){if(e.noHScroll){(n.alignable||(n.alignable=[])).push(t);var i=r.wrapperWidth;t.style.left=r.fixedPos+"px",e.coverGutter||(i-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function Un(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!D(document.body,e.node)){var n="position: relative;";e.coverGutter&&(n+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(n+="width: "+t.display.wrapper.clientWidth+"px;"),N(t.display.measure,A("div",[e.node],null,n))}return e.height=e.node.parentNode.offsetHeight}function Vn(e,t){for(var n=Te(t);n!=e.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==e.sizer&&n!=e.mover)return!0}function Kn(e){return e.lineSpace.offsetTop}function jn(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Xn(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=N(e.measure,A("pre","x","CodeMirror-line-like")),n=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,r={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return isNaN(r.left)||isNaN(r.right)||(e.cachedPaddingH=r),r}function Yn(e){return G-e.display.nativeBarWidth}function _n(e){return e.display.scroller.clientWidth-Yn(e)-e.display.barWidth}function $n(e){return e.display.scroller.clientHeight-Yn(e)-e.display.barHeight}function qn(e,t,n){var r=e.options.lineWrapping,i=r&&_n(e);if(!t.measure.heights||r&&t.measure.width!=i){var o=t.measure.heights=[];if(r){t.measure.width=i;for(var l=t.text.firstChild.getClientRects(),s=0;s<l.length-1;s++){var a=l[s],u=l[s+1];Math.abs(a.bottom-u.bottom)>2&&o.push((a.bottom+u.top)/2-n.top)}}o.push(n.bottom-n.top)}}function Zn(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var i=0;i<e.rest.length;i++)if(et(e.rest[i])>n)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function Jn(e,t){t=Zt(t);var n=et(t),r=e.display.externalMeasured=new xn(e.doc,t,n);r.lineN=n;var i=r.built=pn(e,r);return r.text=i.pre,N(e.display.lineMeasure,i.pre),r}function Qn(e,t,n,r){return nr(e,tr(e,t),n,r)}function er(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[Fr(e,t)];var n=e.display.externalMeasured;return n&&t>=n.lineN&&t<n.lineN+n.size?n:void 0}function tr(e,t){var n=et(t),r=er(e,n);r&&!r.text?r=null:r&&r.changes&&(On(e,r,n,Or(e)),e.curOp.forceUpdate=!0),r||(r=Jn(e,t));var i=Zn(r,t,n);return{line:t,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function nr(e,t,n,r,i){t.before&&(n=-1);var o,l=n+(r||"");return t.cache.hasOwnProperty(l)?o=t.cache[l]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(qn(e,t.view,t.rect),t.hasHeights=!0),o=sr(e,t,n,r),o.bogus||(t.cache[l]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var rr,ir={left:0,right:0,top:0,bottom:0};function or(e,t,n){for(var r,i,o,l,s,a,u=0;u<e.length;u+=3)if(s=e[u],a=e[u+1],t<s?(i=0,o=1,l="left"):t<a?(i=t-s,o=i+1):(u==e.length-3||t==a&&e[u+3]>t)&&(o=a-s,i=o-1,t>=a&&(l="right")),null!=i){if(r=e[u+2],s==a&&n==(r.insertLeft?"left":"right")&&(l=n),"left"==n&&0==i)while(u&&e[u-2]==e[u-3]&&e[u-1].insertLeft)r=e[2+(u-=3)],l="left";if("right"==n&&i==a-s)while(u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft)r=e[(u+=3)+2],l="right";break}return{node:r,start:i,end:o,collapse:l,coverStart:s,coverEnd:a}}function lr(e,t){var n=ir;if("left"==t){for(var r=0;r<e.length;r++)if((n=e[r]).left!=n.right)break}else for(var i=e.length-1;i>=0;i--)if((n=e[i]).left!=n.right)break;return n}function sr(e,t,n,r){var i,o=or(t.map,n,r),a=o.node,u=o.start,c=o.end,h=o.collapse;if(3==a.nodeType){for(var f=0;f<4;f++){while(u&&oe(t.line.text.charAt(o.coverStart+u)))--u;while(o.coverStart+c<o.coverEnd&&oe(t.line.text.charAt(o.coverStart+c)))++c;if(i=l&&s<9&&0==u&&c==o.coverEnd-o.coverStart?a.parentNode.getBoundingClientRect():lr(L(a,u,c).getClientRects(),r),i.left||i.right||0==u)break;c=u,u-=1,h="right"}l&&s<11&&(i=ar(e.display.measure,i))}else{var d;u>0&&(h=r="right"),i=e.options.lineWrapping&&(d=a.getClientRects()).length>1?d["right"==r?d.length-1:0]:a.getBoundingClientRect()}if(l&&s<9&&!u&&(!i||!i.left&&!i.right)){var p=a.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+Ar(e.display),top:p.top,bottom:p.bottom}:ir}for(var g=i.top-t.rect.top,v=i.bottom-t.rect.top,m=(g+v)/2,y=t.view.measure.heights,b=0;b<y.length-1;b++)if(m<y[b])break;var w=b?y[b-1]:0,x=y[b],C={left:("right"==h?i.right:i.left)-t.rect.left,right:("left"==h?i.left:i.right)-t.rect.left,top:w,bottom:x};return i.left||i.right||(C.bogus=!0),e.options.singleCursorHeightPerLine||(C.rtop=g,C.rbottom=v),C}function ar(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!Ie(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*r,bottom:t.bottom*r}}function ur(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function cr(e){e.display.externalMeasure=null,M(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)ur(e.display.view[t])}function hr(e){cr(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function fr(){return c&&v?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function dr(){return c&&v?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function pr(e){var t=0;if(e.widgets)for(var n=0;n<e.widgets.length;++n)e.widgets[n].above&&(t+=Un(e.widgets[n]));return t}function gr(e,t,n,r,i){if(!i){var o=pr(t);n.top+=o,n.bottom+=o}if("line"==r)return n;r||(r="local");var l=on(t);if("local"==r?l+=Kn(e.display):l-=e.display.viewOffset,"page"==r||"window"==r){var s=e.display.lineSpace.getBoundingClientRect();l+=s.top+("window"==r?0:dr());var a=s.left+("window"==r?0:fr());n.left+=a,n.right+=a}return n.top+=l,n.bottom+=l,n}function vr(e,t,n){if("div"==n)return t;var r=t.left,i=t.top;if("page"==n)r-=fr(),i-=dr();else if("local"==n||!n){var o=e.display.sizer.getBoundingClientRect();r+=o.left,i+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:r-l.left,top:i-l.top}}function mr(e,t,n,r,i){return r||(r=qe(e.doc,t.line)),gr(e,r,Qn(e,r,t.ch,i),n)}function yr(e,t,n,r,i,o){function l(t,l){var s=nr(e,i,t,l?"right":"left",o);return l?s.left=s.right:s.right=s.left,gr(e,r,s,n)}r=r||qe(e.doc,t.line),i||(i=tr(e,r));var s=fe(r,e.doc.direction),a=t.ch,u=t.sticky;if(a>=r.text.length?(a=r.text.length,u="before"):a<=0&&(a=0,u="after"),!s)return l("before"==u?a-1:a,"before"==u);function c(e,t,n){var r=s[t],i=1==r.level;return l(n?e-1:e,i!=n)}var h=ce(s,a,u),f=ue,d=c(a,h,"before"==u);return null!=f&&(d.other=c(a,f,"before"!=u)),d}function br(e,t){var n=0;t=ht(e.doc,t),e.options.lineWrapping||(n=Ar(e.display)*t.ch);var r=qe(e.doc,t.line),i=on(r)+Kn(e.display);return{left:n,right:n,top:i,bottom:i+r.height}}function wr(e,t,n,r,i){var o=it(e,t,n);return o.xRel=i,r&&(o.outside=r),o}function xr(e,t,n){var r=e.doc;if(n+=e.display.viewOffset,n<0)return wr(r.first,0,null,-1,-1);var i=tt(r,n),o=r.first+r.size-1;if(i>o)return wr(r.first+r.size-1,qe(r,o).text.length,null,1,1);t<0&&(t=0);for(var l=qe(r,i);;){var s=Lr(e,l,i,t,n),a=$t(l,s.ch+(s.xRel>0||s.outside>0?1:0));if(!a)return s;var u=a.find(1);if(u.line==i)return u;l=qe(r,i=u.line)}}function Cr(e,t,n,r){r-=pr(t);var i=t.text.length,o=se((function(t){return nr(e,n,t-1).bottom<=r}),i,0);return i=se((function(t){return nr(e,n,t).top>r}),o,i),{begin:o,end:i}}function Sr(e,t,n,r){n||(n=tr(e,t));var i=gr(e,t,nr(e,n,r),"line").top;return Cr(e,t,n,i)}function kr(e,t,n,r){return!(e.bottom<=n)&&(e.top>n||(r?e.left:e.right)>t)}function Lr(e,t,n,r,i){i-=on(t);var o=tr(e,t),l=pr(t),s=0,a=t.text.length,u=!0,c=fe(t,e.doc.direction);if(c){var h=(e.options.lineWrapping?Mr:Tr)(e,t,n,o,c,r,i);u=1!=h.level,s=u?h.from:h.to-1,a=u?h.to:h.from-1}var f,d,p=null,g=null,v=se((function(t){var n=nr(e,o,t);return n.top+=l,n.bottom+=l,!!kr(n,r,i,!1)&&(n.top<=i&&n.left<=r&&(p=t,g=n),!0)}),s,a),m=!1;if(g){var y=r-g.left<g.right-r,b=y==u;v=p+(b?0:1),d=b?"after":"before",f=y?g.left:g.right}else{u||v!=a&&v!=s||v++,d=0==v?"after":v==t.text.length?"before":nr(e,o,v-(u?1:0)).bottom+l<=i==u?"after":"before";var w=yr(e,it(n,v,d),"line",t,o);f=w.left,m=i<w.top?-1:i>=w.bottom?1:0}return v=le(t.text,v,1),wr(n,v,d,m,r-f)}function Tr(e,t,n,r,i,o,l){var s=se((function(s){var a=i[s],u=1!=a.level;return kr(yr(e,it(n,u?a.to:a.from,u?"before":"after"),"line",t,r),o,l,!0)}),0,i.length-1),a=i[s];if(s>0){var u=1!=a.level,c=yr(e,it(n,u?a.from:a.to,u?"after":"before"),"line",t,r);kr(c,o,l,!0)&&c.top>l&&(a=i[s-1])}return a}function Mr(e,t,n,r,i,o,l){var s=Cr(e,t,r,l),a=s.begin,u=s.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var c=null,h=null,f=0;f<i.length;f++){var d=i[f];if(!(d.from>=u||d.to<=a)){var p=1!=d.level,g=nr(e,r,p?Math.min(u,d.to)-1:Math.max(a,d.from)).right,v=g<o?o-g+1e9:g-o;(!c||h>v)&&(c=d,h=v)}}return c||(c=i[i.length-1]),c.from<a&&(c={from:a,to:c.to,level:c.level}),c.to>u&&(c={from:c.from,to:u,level:c.level}),c}function Nr(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==rr){rr=A("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)rr.appendChild(document.createTextNode("x")),rr.appendChild(A("br"));rr.appendChild(document.createTextNode("x"))}N(e.measure,rr);var n=rr.offsetHeight/50;return n>3&&(e.cachedTextHeight=n),M(e.measure),n||1}function Ar(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=A("span","xxxxxxxxxx"),n=A("pre",[t],"CodeMirror-line-like");N(e.measure,n);var r=t.getBoundingClientRect(),i=(r.right-r.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function Or(e){for(var t=e.display,n={},r={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l){var s=e.display.gutterSpecs[l].className;n[s]=o.offsetLeft+o.clientLeft+i,r[s]=o.clientWidth}return{fixedPos:Dr(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function Dr(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function Wr(e){var t=Nr(e.display),n=e.options.lineWrapping,r=n&&Math.max(5,e.display.scroller.clientWidth/Ar(e.display)-3);return function(i){if(nn(e.doc,i))return 0;var o=0;if(i.widgets)for(var l=0;l<i.widgets.length;l++)i.widgets[l].height&&(o+=i.widgets[l].height);return n?o+(Math.ceil(i.text.length/r)||1)*t:o+t}}function Pr(e){var t=e.doc,n=Wr(e);t.iter((function(e){var t=n(e);t!=e.height&&Qe(e,t)}))}function Hr(e,t,n,r){var i=e.display;if(!n&&"true"==Te(t).getAttribute("cm-not-content"))return null;var o,l,s=i.lineSpace.getBoundingClientRect();try{o=t.clientX-s.left,l=t.clientY-s.top}catch(h){return null}var a,u=xr(e,o,l);if(r&&u.xRel>0&&(a=qe(e.doc,u.line).text).length==u.ch){var c=R(a,a.length,e.options.tabSize)-a.length;u=it(u.line,Math.max(0,Math.round((o-Xn(e.display).left)/Ar(e.display))-c))}return u}function Fr(e,t){if(t>=e.display.viewTo)return null;if(t-=e.display.viewFrom,t<0)return null;for(var n=e.display.view,r=0;r<n.length;r++)if(t-=n[r].size,t<0)return r}function Er(e,t,n,r){null==t&&(t=e.doc.first),null==n&&(n=e.doc.first+e.doc.size),r||(r=0);var i=e.display;if(r&&n<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)At&&en(e.doc,t)<i.viewTo&&Rr(e);else if(n<=i.viewFrom)At&&tn(e.doc,n+r)>i.viewFrom?Rr(e):(i.viewFrom+=r,i.viewTo+=r);else if(t<=i.viewFrom&&n>=i.viewTo)Rr(e);else if(t<=i.viewFrom){var o=zr(e,n,n+r,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=r):Rr(e)}else if(n>=i.viewTo){var l=zr(e,t,t,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):Rr(e)}else{var s=zr(e,t,t,-1),a=zr(e,n,n+r,1);s&&a?(i.view=i.view.slice(0,s.index).concat(Cn(e,s.lineN,a.lineN)).concat(i.view.slice(a.index)),i.viewTo+=r):Rr(e)}var u=i.externalMeasured;u&&(n<u.lineN?u.lineN+=r:t<u.lineN+u.size&&(i.externalMeasured=null))}function Ir(e,t,n){e.curOp.viewChanged=!0;var r=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(r.externalMeasured=null),!(t<r.viewFrom||t>=r.viewTo)){var o=r.view[Fr(e,t)];if(null!=o.node){var l=o.changes||(o.changes=[]);-1==B(l,n)&&l.push(n)}}}function Rr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function zr(e,t,n,r){var i,o=Fr(e,t),l=e.display.view;if(!At||n==e.doc.first+e.doc.size)return{index:o,lineN:n};for(var s=e.display.viewFrom,a=0;a<o;a++)s+=l[a].size;if(s!=t){if(r>0){if(o==l.length-1)return null;i=s+l[o].size-t,o++}else i=s-t;t+=i,n+=i}while(en(e.doc,n)!=n){if(o==(r<0?0:l.length-1))return null;n+=r*l[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function Br(e,t,n){var r=e.display,i=r.view;0==i.length||t>=r.viewTo||n<=r.viewFrom?(r.view=Cn(e,t,n),r.viewFrom=t):(r.viewFrom>t?r.view=Cn(e,t,r.viewFrom).concat(r.view):r.viewFrom<t&&(r.view=r.view.slice(Fr(e,t))),r.viewFrom=t,r.viewTo<n?r.view=r.view.concat(Cn(e,r.viewTo,n)):r.viewTo>n&&(r.view=r.view.slice(0,Fr(e,n)))),r.viewTo=n}function Gr(e){for(var t=e.display.view,n=0,r=0;r<t.length;r++){var i=t[r];i.hidden||i.node&&!i.changes||++n}return n}function Ur(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Vr(e,t){void 0===t&&(t=!0);for(var n=e.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),l=0;l<n.sel.ranges.length;l++)if(t||l!=n.sel.primIndex){var s=n.sel.ranges[l];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var a=s.empty();(a||e.options.showCursorWhenSelecting)&&Kr(e,s.head,i),a||Xr(e,s,o)}}return r}function Kr(e,t,n){var r=yr(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=n.appendChild(A("div"," ","CodeMirror-cursor"));if(i.style.left=r.left+"px",i.style.top=r.top+"px",i.style.height=Math.max(0,r.bottom-r.top)*e.options.cursorHeight+"px",r.other){var o=n.appendChild(A("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=r.other.left+"px",o.style.top=r.other.top+"px",o.style.height=.85*(r.other.bottom-r.other.top)+"px"}}function jr(e,t){return e.top-t.top||e.left-t.left}function Xr(e,t,n){var r=e.display,i=e.doc,o=document.createDocumentFragment(),l=Xn(e.display),s=l.left,a=Math.max(r.sizerWidth,_n(e)-r.sizer.offsetLeft)-l.right,u="ltr"==i.direction;function c(e,t,n,r){t<0&&(t=0),t=Math.round(t),r=Math.round(r),o.appendChild(A("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==n?a-e:n)+"px;\n                             height: "+(r-t)+"px"))}function h(t,n,r){var o,l,h=qe(i,t),f=h.text.length;function d(n,r){return mr(e,it(t,n),"div",h,r)}function p(t,n,r){var i=Sr(e,h,null,t),o="ltr"==n==("after"==r)?"left":"right",l="after"==r?i.begin:i.end-(/\s/.test(h.text.charAt(i.end-1))?2:1);return d(l,o)[o]}var g=fe(h,i.direction);return ae(g,n||0,null==r?f:r,(function(e,t,i,h){var v="ltr"==i,m=d(e,v?"left":"right"),y=d(t-1,v?"right":"left"),b=null==n&&0==e,w=null==r&&t==f,x=0==h,C=!g||h==g.length-1;if(y.top-m.top<=3){var S=(u?b:w)&&x,k=(u?w:b)&&C,L=S?s:(v?m:y).left,T=k?a:(v?y:m).right;c(L,m.top,T-L,m.bottom)}else{var M,N,A,O;v?(M=u&&b&&x?s:m.left,N=u?a:p(e,i,"before"),A=u?s:p(t,i,"after"),O=u&&w&&C?a:y.right):(M=u?p(e,i,"before"):s,N=!u&&b&&x?a:m.right,A=!u&&w&&C?s:y.left,O=u?p(t,i,"after"):a),c(M,m.top,N-M,m.bottom),m.bottom<y.top&&c(s,m.bottom,null,y.top),c(A,y.top,O-A,y.bottom)}(!o||jr(m,o)<0)&&(o=m),jr(y,o)<0&&(o=y),(!l||jr(m,l)<0)&&(l=m),jr(y,l)<0&&(l=y)})),{start:o,end:l}}var f=t.from(),d=t.to();if(f.line==d.line)h(f.line,f.ch,d.ch);else{var p=qe(i,f.line),g=qe(i,d.line),v=Zt(p)==Zt(g),m=h(f.line,f.ch,v?p.text.length+1:null).end,y=h(d.line,v?0:null,d.ch).start;v&&(m.top<y.top-2?(c(m.right,m.top,null,m.bottom),c(s,y.top,y.left,y.bottom)):c(m.right,m.top,y.left-m.right,m.bottom)),m.bottom<y.top&&c(s,m.bottom,null,y.top)}n.appendChild(o)}function Yr(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var n=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval((function(){return t.cursorDiv.style.visibility=(n=!n)?"":"hidden"}),e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function _r(e){e.state.focused||(e.display.input.focus(),qr(e))}function $r(e){e.state.delayingBlurEvent=!0,setTimeout((function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,Zr(e))}),100)}function qr(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(me(e,"focus",e,t),e.state.focused=!0,P(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),a&&setTimeout((function(){return e.display.input.reset(!0)}),20)),e.display.input.receivedFocus()),Yr(e))}function Zr(e,t){e.state.delayingBlurEvent||(e.state.focused&&(me(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout((function(){e.state.focused||(e.display.shift=!1)}),150))}function Jr(e){for(var t=e.display,n=t.lineDiv.offsetTop,r=0;r<t.view.length;r++){var i=t.view[r],o=e.options.lineWrapping,a=void 0,u=0;if(!i.hidden){if(l&&s<8){var c=i.node.offsetTop+i.node.offsetHeight;a=c-n,n=c}else{var h=i.node.getBoundingClientRect();a=h.bottom-h.top,!o&&i.text.firstChild&&(u=i.text.firstChild.getBoundingClientRect().right-h.left-1)}var f=i.line.height-a;if((f>.005||f<-.005)&&(Qe(i.line,a),Qr(i.line),i.rest))for(var d=0;d<i.rest.length;d++)Qr(i.rest[d]);if(u>e.display.sizerWidth){var p=Math.ceil(u/Ar(e.display));p>e.display.maxLineLength&&(e.display.maxLineLength=p,e.display.maxLine=i.line,e.display.maxLineChanged=!0)}}}}function Qr(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var n=e.widgets[t],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function ei(e,t,n){var r=n&&null!=n.top?Math.max(0,n.top):e.scroller.scrollTop;r=Math.floor(r-Kn(e));var i=n&&null!=n.bottom?n.bottom:r+e.wrapper.clientHeight,o=tt(t,r),l=tt(t,i);if(n&&n.ensure){var s=n.ensure.from.line,a=n.ensure.to.line;s<o?(o=s,l=tt(t,on(qe(t,s))+e.wrapper.clientHeight)):Math.min(a,t.lastLine())>=l&&(o=tt(t,on(qe(t,a))-e.wrapper.clientHeight),l=a)}return{from:o,to:Math.max(l,o+1)}}function ti(e,t){if(!ye(e,"scrollCursorIntoView")){var n=e.display,r=n.sizer.getBoundingClientRect(),i=null;if(t.top+r.top<0?i=!0:t.bottom+r.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!p){var o=A("div","​",null,"position: absolute;\n                         top: "+(t.top-n.viewOffset-Kn(e.display))+"px;\n                         height: "+(t.bottom-t.top+Yn(e)+n.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}function ni(e,t,n,r){var i;null==r&&(r=0),e.options.lineWrapping||t!=n||(t=t.ch?it(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t,n="before"==t.sticky?it(t.line,t.ch+1,"before"):t);for(var o=0;o<5;o++){var l=!1,s=yr(e,t),a=n&&n!=t?yr(e,n):s;i={left:Math.min(s.left,a.left),top:Math.min(s.top,a.top)-r,right:Math.max(s.left,a.left),bottom:Math.max(s.bottom,a.bottom)+r};var u=ii(e,i),c=e.doc.scrollTop,h=e.doc.scrollLeft;if(null!=u.scrollTop&&(hi(e,u.scrollTop),Math.abs(e.doc.scrollTop-c)>1&&(l=!0)),null!=u.scrollLeft&&(di(e,u.scrollLeft),Math.abs(e.doc.scrollLeft-h)>1&&(l=!0)),!l)break}return i}function ri(e,t){var n=ii(e,t);null!=n.scrollTop&&hi(e,n.scrollTop),null!=n.scrollLeft&&di(e,n.scrollLeft)}function ii(e,t){var n=e.display,r=Nr(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:n.scroller.scrollTop,o=$n(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var s=e.doc.height+jn(n),a=t.top<r,u=t.bottom>s-r;if(t.top<i)l.scrollTop=a?0:t.top;else if(t.bottom>i+o){var c=Math.min(t.top,(u?s:t.bottom)-o);c!=i&&(l.scrollTop=c)}var h=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:n.scroller.scrollLeft,f=_n(e)-(e.options.fixedGutter?n.gutters.offsetWidth:0),d=t.right-t.left>f;return d&&(t.right=t.left+f),t.left<10?l.scrollLeft=0:t.left<h?l.scrollLeft=Math.max(0,t.left-(d?0:10)):t.right>f+h-3&&(l.scrollLeft=t.right+(d?0:10)-f),l}function oi(e,t){null!=t&&(ui(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function li(e){ui(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function si(e,t,n){null==t&&null==n||ui(e),null!=t&&(e.curOp.scrollLeft=t),null!=n&&(e.curOp.scrollTop=n)}function ai(e,t){ui(e),e.curOp.scrollToPos=t}function ui(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var n=br(e,t.from),r=br(e,t.to);ci(e,n,r,t.margin)}}function ci(e,t,n,r){var i=ii(e,{left:Math.min(t.left,n.left),top:Math.min(t.top,n.top)-r,right:Math.max(t.right,n.right),bottom:Math.max(t.bottom,n.bottom)+r});si(e,i.scrollLeft,i.scrollTop)}function hi(e,t){Math.abs(e.doc.scrollTop-t)<2||(n||Ui(e,{top:t}),fi(e,t,!0),n&&Ui(e),Hi(e,100))}function fi(e,t,n){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),(e.display.scroller.scrollTop!=t||n)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function di(e,t,n,r){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r||(e.doc.scrollLeft=t,Xi(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function pi(e){var t=e.display,n=t.gutters.offsetWidth,r=Math.round(e.doc.height+jn(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+Yn(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}var gi=function(e,t,n){this.cm=n;var r=this.vert=A("div",[A("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=A("div",[A("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,e(r),e(i),pe(r,"scroll",(function(){r.clientHeight&&t(r.scrollTop,"vertical")})),pe(i,"scroll",(function(){i.clientWidth&&t(i.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,l&&s<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};gi.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,n=e.scrollHeight>e.clientHeight+1,r=e.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=t?r+"px":"0";var i=e.viewHeight-(t?r:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=n?r+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(n?r:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==r&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?r:0,bottom:t?r:0}},gi.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},gi.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},gi.prototype.zeroWidthHack=function(){var e=y&&!d?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new z,this.disableVert=new z},gi.prototype.enableZeroWidthBar=function(e,t,n){function r(){var i=e.getBoundingClientRect(),o="vert"==n?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1);o!=e?e.style.pointerEvents="none":t.set(1e3,r)}e.style.pointerEvents="auto",t.set(1e3,r)},gi.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var vi=function(){};function mi(e,t){t||(t=pi(e));var n=e.display.barWidth,r=e.display.barHeight;yi(e,t);for(var i=0;i<4&&n!=e.display.barWidth||r!=e.display.barHeight;i++)n!=e.display.barWidth&&e.options.lineWrapping&&Jr(e),yi(e,pi(e)),n=e.display.barWidth,r=e.display.barHeight}function yi(e,t){var n=e.display,r=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}vi.prototype.update=function(){return{bottom:0,right:0}},vi.prototype.setScrollLeft=function(){},vi.prototype.setScrollTop=function(){},vi.prototype.clear=function(){};var bi={native:gi,null:vi};function wi(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&T(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new bi[e.options.scrollbarStyle]((function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),pe(t,"mousedown",(function(){e.state.focused&&setTimeout((function(){return e.display.input.focus()}),0)})),t.setAttribute("cm-not-content","true")}),(function(t,n){"horizontal"==n?di(e,t):hi(e,t)}),e),e.display.scrollbars.addClass&&P(e.display.wrapper,e.display.scrollbars.addClass)}var xi=0;function Ci(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++xi},kn(e.curOp)}function Si(e){var t=e.curOp;t&&Tn(t,(function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;ki(e)}))}function ki(e){for(var t=e.ops,n=0;n<t.length;n++)Li(t[n]);for(var r=0;r<t.length;r++)Ti(t[r]);for(var i=0;i<t.length;i++)Mi(t[i]);for(var o=0;o<t.length;o++)Ni(t[o]);for(var l=0;l<t.length;l++)Ai(t[l])}function Li(e){var t=e.cm,n=t.display;Ii(t),e.updateMaxLine&&sn(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<n.viewFrom||e.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new Ei(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function Ti(e){e.updatedDisplay=e.mustUpdate&&Bi(e.cm,e.update)}function Mi(e){var t=e.cm,n=t.display;e.updatedDisplay&&Jr(t),e.barMeasure=pi(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Qn(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+Yn(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-_n(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection())}function Ni(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&di(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==W();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n),(e.updatedDisplay||e.startHeight!=t.doc.height)&&mi(t,e.barMeasure),e.updatedDisplay&&ji(t,e.barMeasure),e.selectionChanged&&Yr(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),n&&_r(e.cm)}function Ai(e){var t=e.cm,n=t.display,r=t.doc;if(e.updatedDisplay&&Gi(t,e.update),null==n.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(n.wheelStartX=n.wheelStartY=null),null!=e.scrollTop&&fi(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&di(t,e.scrollLeft,!0,!0),e.scrollToPos){var i=ni(t,ht(r,e.scrollToPos.from),ht(r,e.scrollToPos.to),e.scrollToPos.margin);ti(t,i)}var o=e.maybeHiddenMarkers,l=e.maybeUnhiddenMarkers;if(o)for(var s=0;s<o.length;++s)o[s].lines.length||me(o[s],"hide");if(l)for(var a=0;a<l.length;++a)l[a].lines.length&&me(l[a],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&me(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Oi(e,t){if(e.curOp)return t();Ci(e);try{return t()}finally{Si(e)}}function Di(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Ci(e);try{return t.apply(e,arguments)}finally{Si(e)}}}function Wi(e){return function(){if(this.curOp)return e.apply(this,arguments);Ci(this);try{return e.apply(this,arguments)}finally{Si(this)}}}function Pi(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Ci(t);try{return e.apply(this,arguments)}finally{Si(t)}}}function Hi(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,E(Fi,e))}function Fi(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var n=+new Date+e.options.workTime,r=yt(e,t.highlightFrontier),i=[];t.iter(r.line,Math.min(t.first+t.size,e.display.viewTo+500),(function(o){if(r.line>=e.display.viewFrom){var l=o.styles,s=o.text.length>e.options.maxHighlightLength?Xe(t.mode,r.state):null,a=vt(e,o,r,!0);s&&(r.state=s),o.styles=a.styles;var u=o.styleClasses,c=a.classes;c?o.styleClasses=c:u&&(o.styleClasses=null);for(var h=!l||l.length!=o.styles.length||u!=c&&(!u||!c||u.bgClass!=c.bgClass||u.textClass!=c.textClass),f=0;!h&&f<l.length;++f)h=l[f]!=o.styles[f];h&&i.push(r.line),o.stateAfter=r.save(),r.nextLine()}else o.text.length<=e.options.maxHighlightLength&&bt(e,o.text,r),o.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>n)return Hi(e,e.options.workDelay),!0})),t.highlightFrontier=r.line,t.modeFrontier=Math.max(t.modeFrontier,r.line),i.length&&Oi(e,(function(){for(var t=0;t<i.length;t++)Ir(e,i[t],"text")}))}}var Ei=function(e,t,n){var r=e.display;this.viewport=t,this.visible=ei(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=_n(e),this.force=n,this.dims=Or(e),this.events=[]};function Ii(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Yn(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Yn(e)+"px",t.scrollbarsClipped=!0)}function Ri(e){if(e.hasFocus())return null;var t=W();if(!t||!D(e.display.lineDiv,t))return null;var n={activeElt:t};if(window.getSelection){var r=window.getSelection();r.anchorNode&&r.extend&&D(e.display.lineDiv,r.anchorNode)&&(n.anchorNode=r.anchorNode,n.anchorOffset=r.anchorOffset,n.focusNode=r.focusNode,n.focusOffset=r.focusOffset)}return n}function zi(e){if(e&&e.activeElt&&e.activeElt!=W()&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&D(document.body,e.anchorNode)&&D(document.body,e.focusNode))){var t=window.getSelection(),n=document.createRange();n.setEnd(e.anchorNode,e.anchorOffset),n.collapse(!1),t.removeAllRanges(),t.addRange(n),t.extend(e.focusNode,e.focusOffset)}}function Bi(e,t){var n=e.display,r=e.doc;if(t.editorIsHidden)return Rr(e),!1;if(!t.force&&t.visible.from>=n.viewFrom&&t.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==Gr(e))return!1;Yi(e)&&(Rr(e),t.dims=Or(e));var i=r.first+r.size,o=Math.max(t.visible.from-e.options.viewportMargin,r.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>l&&n.viewTo-l<20&&(l=Math.min(i,n.viewTo)),At&&(o=en(e.doc,o),l=tn(e.doc,l));var s=o!=n.viewFrom||l!=n.viewTo||n.lastWrapHeight!=t.wrapperHeight||n.lastWrapWidth!=t.wrapperWidth;Br(e,o,l),n.viewOffset=on(qe(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";var a=Gr(e);if(!s&&0==a&&!t.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var u=Ri(e);return a>4&&(n.lineDiv.style.display="none"),Vi(e,n.updateLineNumbers,t.dims),a>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,zi(u),M(n.cursorDiv),M(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,s&&(n.lastWrapHeight=t.wrapperHeight,n.lastWrapWidth=t.wrapperWidth,Hi(e,400)),n.updateLineNumbers=null,!0}function Gi(e,t){for(var n=t.viewport,r=!0;;r=!1){if(r&&e.options.lineWrapping&&t.oldDisplayWidth!=_n(e))r&&(t.visible=ei(e.display,e.doc,n));else if(n&&null!=n.top&&(n={top:Math.min(e.doc.height+jn(e.display)-$n(e),n.top)}),t.visible=ei(e.display,e.doc,n),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!Bi(e,t))break;Jr(e);var i=pi(e);Ur(e),mi(e,i),ji(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function Ui(e,t){var n=new Ei(e,t);if(Bi(e,n)){Jr(e),Gi(e,n);var r=pi(e);Ur(e),mi(e,r),ji(e,r),n.finish()}}function Vi(e,t,n){var r=e.display,i=e.options.lineNumbers,o=r.lineDiv,l=o.firstChild;function s(t){var n=t.nextSibling;return a&&y&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),n}for(var u=r.view,c=r.viewFrom,h=0;h<u.length;h++){var f=u[h];if(f.hidden);else if(f.node&&f.node.parentNode==o){while(l!=f.node)l=s(l);var d=i&&null!=t&&t<=c&&f.lineNumber;f.changes&&(B(f.changes,"gutter")>-1&&(d=!1),On(e,f,c,n)),d&&(M(f.lineNumber),f.lineNumber.appendChild(document.createTextNode(rt(e.options,c)))),l=f.node.nextSibling}else{var p=Rn(e,f,c,n);o.insertBefore(p,l)}c+=f.size}while(l)l=s(l)}function Ki(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px"}function ji(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Yn(e)+"px"}function Xi(e){var t=e.display,n=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var r=Dr(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=r+"px",l=0;l<n.length;l++)if(!n[l].hidden){e.options.fixedGutter&&(n[l].gutter&&(n[l].gutter.style.left=o),n[l].gutterBackground&&(n[l].gutterBackground.style.left=o));var s=n[l].alignable;if(s)for(var a=0;a<s.length;a++)s[a].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+i+"px")}}function Yi(e){if(!e.options.lineNumbers)return!1;var t=e.doc,n=rt(e.options,t.first+t.size-1),r=e.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(A("div",[A("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-l)+1,r.lineNumWidth=r.lineNumInnerWidth+l,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",Ki(e.display),!0}return!1}function _i(e,t){for(var n=[],r=!1,i=0;i<e.length;i++){var o=e[i],l=null;if("string"!=typeof o&&(l=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;r=!0}n.push({className:o,style:l})}return t&&!r&&n.push({className:"CodeMirror-linenumbers",style:null}),n}function $i(e){var t=e.gutters,n=e.gutterSpecs;M(t),e.lineGutter=null;for(var r=0;r<n.length;++r){var i=n[r],o=i.className,l=i.style,s=t.appendChild(A("div",null,"CodeMirror-gutter "+o));l&&(s.style.cssText=l),"CodeMirror-linenumbers"==o&&(e.lineGutter=s,s.style.width=(e.lineNumWidth||1)+"px")}t.style.display=n.length?"":"none",Ki(e)}function qi(e){$i(e.display),Er(e),Xi(e)}function Zi(e,t,r,i){var o=this;this.input=r,o.scrollbarFiller=A("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=A("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=O("div",null,"CodeMirror-code"),o.selectionDiv=A("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=A("div",null,"CodeMirror-cursors"),o.measure=A("div",null,"CodeMirror-measure"),o.lineMeasure=A("div",null,"CodeMirror-measure"),o.lineSpace=O("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var u=O("div",[o.lineSpace],"CodeMirror-lines");o.mover=A("div",[u],null,"position: relative"),o.sizer=A("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=A("div",null,null,"position: absolute; height: "+G+"px; width: 1px;"),o.gutters=A("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=A("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=A("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),l&&s<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),a||n&&m||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=_i(i.gutters,i.lineNumbers),$i(o),r.init(o)}Ei.prototype.signal=function(e,t){we(e,t)&&this.events.push(arguments)},Ei.prototype.finish=function(){for(var e=0;e<this.events.length;e++)me.apply(null,this.events[e])};var Ji=0,Qi=null;function eo(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==n&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:null==n&&(n=e.wheelDelta),{x:t,y:n}}function to(e){var t=eo(e);return t.x*=Qi,t.y*=Qi,t}function no(e,t){var r=eo(t),i=r.x,o=r.y,l=e.display,s=l.scroller,u=s.scrollWidth>s.clientWidth,c=s.scrollHeight>s.clientHeight;if(i&&u||o&&c){if(o&&y&&a)e:for(var f=t.target,d=l.view;f!=s;f=f.parentNode)for(var p=0;p<d.length;p++)if(d[p].node==f){e.display.currentWheelTarget=f;break e}if(i&&!n&&!h&&null!=Qi)return o&&c&&hi(e,Math.max(0,s.scrollTop+o*Qi)),di(e,Math.max(0,s.scrollLeft+i*Qi)),(!o||o&&c)&&Ce(t),void(l.wheelStartX=null);if(o&&null!=Qi){var g=o*Qi,v=e.doc.scrollTop,m=v+l.wrapper.clientHeight;g<0?v=Math.max(0,v+g-50):m=Math.min(e.doc.height,m+g+50),Ui(e,{top:v,bottom:m})}Ji<20&&(null==l.wheelStartX?(l.wheelStartX=s.scrollLeft,l.wheelStartY=s.scrollTop,l.wheelDX=i,l.wheelDY=o,setTimeout((function(){if(null!=l.wheelStartX){var e=s.scrollLeft-l.wheelStartX,t=s.scrollTop-l.wheelStartY,n=t&&l.wheelDY&&t/l.wheelDY||e&&l.wheelDX&&e/l.wheelDX;l.wheelStartX=l.wheelStartY=null,n&&(Qi=(Qi*Ji+n)/(Ji+1),++Ji)}}),200)):(l.wheelDX+=i,l.wheelDY+=o))}}l?Qi=-.53:n?Qi=15:c?Qi=-.7:f&&(Qi=-1/3);var ro=function(e,t){this.ranges=e,this.primIndex=t};ro.prototype.primary=function(){return this.ranges[this.primIndex]},ro.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var n=this.ranges[t],r=e.ranges[t];if(!lt(n.anchor,r.anchor)||!lt(n.head,r.head))return!1}return!0},ro.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new io(st(this.ranges[t].anchor),st(this.ranges[t].head));return new ro(e,this.primIndex)},ro.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},ro.prototype.contains=function(e,t){t||(t=e);for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(ot(t,r.from())>=0&&ot(e,r.to())<=0)return n}return-1};var io=function(e,t){this.anchor=e,this.head=t};function oo(e,t,n){var r=e&&e.options.selectionsMayTouch,i=t[n];t.sort((function(e,t){return ot(e.from(),t.from())})),n=B(t,i);for(var o=1;o<t.length;o++){var l=t[o],s=t[o-1],a=ot(s.to(),l.from());if(r&&!l.empty()?a>0:a>=0){var u=ut(s.from(),l.from()),c=at(s.to(),l.to()),h=s.empty()?l.from()==l.head:s.from()==s.head;o<=n&&--n,t.splice(--o,2,new io(h?c:u,h?u:c))}}return new ro(t,n)}function lo(e,t){return new ro([new io(e,t||e)],0)}function so(e){return e.text?it(e.from.line+e.text.length-1,$(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function ao(e,t){if(ot(e,t.from)<0)return e;if(ot(e,t.to)<=0)return so(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=so(t).ch-t.to.ch),it(n,r)}function uo(e,t){for(var n=[],r=0;r<e.sel.ranges.length;r++){var i=e.sel.ranges[r];n.push(new io(ao(i.anchor,t),ao(i.head,t)))}return oo(e.cm,n,e.sel.primIndex)}function co(e,t,n){return e.line==t.line?it(n.line,e.ch-t.ch+n.ch):it(n.line+(e.line-t.line),e.ch)}function ho(e,t,n){for(var r=[],i=it(e.first,0),o=i,l=0;l<t.length;l++){var s=t[l],a=co(s.from,i,o),u=co(so(s),i,o);if(i=s.to,o=u,"around"==n){var c=e.sel.ranges[l],h=ot(c.head,c.anchor)<0;r[l]=new io(h?u:a,h?a:u)}else r[l]=new io(a,a)}return new ro(r,e.sel.primIndex)}function fo(e){e.doc.mode=Ve(e.options,e.doc.modeOption),po(e)}function po(e){e.doc.iter((function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)})),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Hi(e,100),e.state.modeGen++,e.curOp&&Er(e)}function go(e,t){return 0==t.from.ch&&0==t.to.ch&&""==$(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function vo(e,t,n,r){function i(e){return n?n[e]:null}function o(e,n,i){un(e,n,i,r),Nn(e,"change",e,t)}function l(e,t){for(var n=[],o=e;o<t;++o)n.push(new an(u[o],i(o),r));return n}var s=t.from,a=t.to,u=t.text,c=qe(e,s.line),h=qe(e,a.line),f=$(u),d=i(u.length-1),p=a.line-s.line;if(t.full)e.insert(0,l(0,u.length)),e.remove(u.length,e.size-u.length);else if(go(e,t)){var g=l(0,u.length-1);o(h,h.text,d),p&&e.remove(s.line,p),g.length&&e.insert(s.line,g)}else if(c==h)if(1==u.length)o(c,c.text.slice(0,s.ch)+f+c.text.slice(a.ch),d);else{var v=l(1,u.length-1);v.push(new an(f+c.text.slice(a.ch),d,r)),o(c,c.text.slice(0,s.ch)+u[0],i(0)),e.insert(s.line+1,v)}else if(1==u.length)o(c,c.text.slice(0,s.ch)+u[0]+h.text.slice(a.ch),i(0)),e.remove(s.line+1,p);else{o(c,c.text.slice(0,s.ch)+u[0],i(0)),o(h,f+h.text.slice(a.ch),d);var m=l(1,u.length-1);p>1&&e.remove(s.line+1,p-1),e.insert(s.line+1,m)}Nn(e,"change",e,t)}function mo(e,t,n){function r(e,i,o){if(e.linked)for(var l=0;l<e.linked.length;++l){var s=e.linked[l];if(s.doc!=i){var a=o&&s.sharedHist;n&&!a||(t(s.doc,a),r(s.doc,e,a))}}}r(e,null,!0)}function yo(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,Pr(e),fo(e),bo(e),e.options.lineWrapping||sn(e),e.options.mode=t.modeOption,Er(e)}function bo(e){("rtl"==e.doc.direction?P:T)(e.display.lineDiv,"CodeMirror-rtl")}function wo(e){Oi(e,(function(){bo(e),Er(e)}))}function xo(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Co(e,t){var n={from:st(t.from),to:so(t),text:Ze(e,t.from,t.to)};return Ao(e,n,t.from.line,t.to.line+1),mo(e,(function(e){return Ao(e,n,t.from.line,t.to.line+1)}),!0),n}function So(e){while(e.length){var t=$(e);if(!t.ranges)break;e.pop()}}function ko(e,t){return t?(So(e.done),$(e.done)):e.done.length&&!$(e.done).ranges?$(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),$(e.done)):void 0}function Lo(e,t,n,r){var i=e.history;i.undone.length=0;var o,l,s=+new Date;if((i.lastOp==r||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>s-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=ko(i,i.lastOp==r)))l=$(o.changes),0==ot(t.from,t.to)&&0==ot(t.from,l.to)?l.to=so(t):o.changes.push(Co(e,t));else{var a=$(i.done);a&&a.ranges||No(e.sel,i.done),o={changes:[Co(e,t)],generation:i.generation},i.done.push(o);while(i.done.length>i.undoDepth)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=s,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=t.origin,l||me(e,"historyAdded")}function To(e,t,n,r){var i=t.charAt(0);return"*"==i||"+"==i&&n.ranges.length==r.ranges.length&&n.somethingSelected()==r.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function Mo(e,t,n,r){var i=e.history,o=r&&r.origin;n==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||To(e,o,$(i.done),t))?i.done[i.done.length-1]=t:No(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=n,r&&!1!==r.clearRedo&&So(i.undone)}function No(e,t){var n=$(t);n&&n.ranges&&n.equals(e)||t.push(e)}function Ao(e,t,n,r){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,n),Math.min(e.first+e.size,r),(function(n){n.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=n.markedSpans),++o}))}function Oo(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t||(t=e.slice(0,n)):t&&t.push(e[n]);return t?t.length?t:null:e}function Do(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var r=[],i=0;i<t.text.length;++i)r.push(Oo(n[i]));return r}function Wo(e,t){var n=Do(e,t),r=Rt(e,t);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],l=r[i];if(o&&l)e:for(var s=0;s<l.length;++s){for(var a=l[s],u=0;u<o.length;++u)if(o[u].marker==a.marker)continue e;o.push(a)}else l&&(n[i]=l)}return n}function Po(e,t,n){for(var r=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)r.push(n?ro.prototype.deepCopy.call(o):o);else{var l=o.changes,s=[];r.push({changes:s});for(var a=0;a<l.length;++a){var u=l[a],c=void 0;if(s.push({from:u.from,to:u.to,text:u.text}),t)for(var h in u)(c=h.match(/^spans_(\d+)$/))&&B(t,Number(c[1]))>-1&&($(s)[h]=u[h],delete u[h])}}}return r}function Ho(e,t,n,r){if(r){var i=e.anchor;if(n){var o=ot(t,i)<0;o!=ot(n,i)<0?(i=t,t=n):o!=ot(t,n)<0&&(t=n)}return new io(i,t)}return new io(n||t,t)}function Fo(e,t,n,r,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),Go(e,new ro([Ho(e.sel.primary(),t,n,i)],0),r)}function Eo(e,t,n){for(var r=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)r[o]=Ho(e.sel.ranges[o],t[o],null,i);var l=oo(e.cm,r,e.sel.primIndex);Go(e,l,n)}function Io(e,t,n,r){var i=e.sel.ranges.slice(0);i[t]=n,Go(e,oo(e.cm,i,e.sel.primIndex),r)}function Ro(e,t,n,r){Go(e,lo(t,n),r)}function zo(e,t,n){var r={ranges:t.ranges,update:function(t){this.ranges=[];for(var n=0;n<t.length;n++)this.ranges[n]=new io(ht(e,t[n].anchor),ht(e,t[n].head))},origin:n&&n.origin};return me(e,"beforeSelectionChange",e,r),e.cm&&me(e.cm,"beforeSelectionChange",e.cm,r),r.ranges!=t.ranges?oo(e.cm,r.ranges,r.ranges.length-1):t}function Bo(e,t,n){var r=e.history.done,i=$(r);i&&i.ranges?(r[r.length-1]=t,Uo(e,t,n)):Go(e,t,n)}function Go(e,t,n){Uo(e,t,n),Mo(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function Uo(e,t,n){(we(e,"beforeSelectionChange")||e.cm&&we(e.cm,"beforeSelectionChange"))&&(t=zo(e,t,n));var r=n&&n.bias||(ot(t.primary().head,e.sel.primary().head)<0?-1:1);Vo(e,jo(e,t,r,!0)),n&&!1===n.scroll||!e.cm||li(e.cm)}function Vo(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,be(e.cm)),Nn(e,"cursorActivity",e))}function Ko(e){Vo(e,jo(e,e.sel,null,!1))}function jo(e,t,n,r){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],s=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],a=Yo(e,l.anchor,s&&s.anchor,n,r),u=Yo(e,l.head,s&&s.head,n,r);(i||a!=l.anchor||u!=l.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new io(a,u))}return i?oo(e.cm,i,t.primIndex):t}function Xo(e,t,n,r,i){var o=qe(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var s=o.markedSpans[l],a=s.marker,u="selectLeft"in a?!a.selectLeft:a.inclusiveLeft,c="selectRight"in a?!a.selectRight:a.inclusiveRight;if((null==s.from||(u?s.from<=t.ch:s.from<t.ch))&&(null==s.to||(c?s.to>=t.ch:s.to>t.ch))){if(i&&(me(a,"beforeCursorEnter"),a.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!a.atomic)continue;if(n){var h=a.find(r<0?1:-1),f=void 0;if((r<0?c:u)&&(h=_o(e,h,-r,h&&h.line==t.line?o:null)),h&&h.line==t.line&&(f=ot(h,n))&&(r<0?f<0:f>0))return Xo(e,h,t,r,i)}var d=a.find(r<0?-1:1);return(r<0?u:c)&&(d=_o(e,d,r,d.line==t.line?o:null)),d?Xo(e,d,t,r,i):null}}return t}function Yo(e,t,n,r,i){var o=r||1,l=Xo(e,t,n,o,i)||!i&&Xo(e,t,n,o,!0)||Xo(e,t,n,-o,i)||!i&&Xo(e,t,n,-o,!0);return l||(e.cantEdit=!0,it(e.first,0))}function _o(e,t,n,r){return n<0&&0==t.ch?t.line>e.first?ht(e,it(t.line-1)):null:n>0&&t.ch==(r||qe(e,t.line)).text.length?t.line<e.first+e.size-1?it(t.line+1,0):null:new it(t.line,t.ch+n)}function $o(e){e.setSelection(it(e.firstLine(),0),it(e.lastLine()),V)}function qo(e,t,n){var r={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return r.canceled=!0}};return n&&(r.update=function(t,n,i,o){t&&(r.from=ht(e,t)),n&&(r.to=ht(e,n)),i&&(r.text=i),void 0!==o&&(r.origin=o)}),me(e,"beforeChange",e,r),e.cm&&me(e.cm,"beforeChange",e.cm,r),r.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:r.from,to:r.to,text:r.text,origin:r.origin}}function Zo(e,t,n){if(e.cm){if(!e.cm.curOp)return Di(e.cm,Zo)(e,t,n);if(e.cm.state.suppressEdits)return}if(!(we(e,"beforeChange")||e.cm&&we(e.cm,"beforeChange"))||(t=qo(e,t,!0),t)){var r=Nt&&!n&&Bt(e,t.from,t.to);if(r)for(var i=r.length-1;i>=0;--i)Jo(e,{from:r[i].from,to:r[i].to,text:i?[""]:t.text,origin:t.origin});else Jo(e,t)}}function Jo(e,t){if(1!=t.text.length||""!=t.text[0]||0!=ot(t.from,t.to)){var n=uo(e,t);Lo(e,t,n,e.cm?e.cm.curOp.id:NaN),tl(e,t,n,Rt(e,t));var r=[];mo(e,(function(e,n){n||-1!=B(r,e.history)||(ll(e.history,t),r.push(e.history)),tl(e,t,null,Rt(e,t))}))}}function Qo(e,t,n){var r=e.cm&&e.cm.state.suppressEdits;if(!r||n){for(var i,o=e.history,l=e.sel,s="undo"==t?o.done:o.undone,a="undo"==t?o.undone:o.done,u=0;u<s.length;u++)if(i=s[u],n?i.ranges&&!i.equals(e.sel):!i.ranges)break;if(u!=s.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(i=s.pop(),!i.ranges){if(r)return void s.push(i);break}if(No(i,a),n&&!i.equals(e.sel))return void Go(e,i,{clearRedo:!1});l=i}var c=[];No(l,a),a.push({changes:c,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var h=we(e,"beforeChange")||e.cm&&we(e.cm,"beforeChange"),f=function(n){var r=i.changes[n];if(r.origin=t,h&&!qo(e,r,!1))return s.length=0,{};c.push(Co(e,r));var o=n?uo(e,r):$(s);tl(e,r,o,Wo(e,r)),!n&&e.cm&&e.cm.scrollIntoView({from:r.from,to:so(r)});var l=[];mo(e,(function(e,t){t||-1!=B(l,e.history)||(ll(e.history,r),l.push(e.history)),tl(e,r,null,Wo(e,r))}))},d=i.changes.length-1;d>=0;--d){var p=f(d);if(p)return p.v}}}}function el(e,t){if(0!=t&&(e.first+=t,e.sel=new ro(q(e.sel.ranges,(function(e){return new io(it(e.anchor.line+t,e.anchor.ch),it(e.head.line+t,e.head.ch))})),e.sel.primIndex),e.cm)){Er(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,r=n.viewFrom;r<n.viewTo;r++)Ir(e.cm,r,"gutter")}}function tl(e,t,n,r){if(e.cm&&!e.cm.curOp)return Di(e.cm,tl)(e,t,n,r);if(t.to.line<e.first)el(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);el(e,i),t={from:it(e.first,0),to:it(t.to.line+i,t.to.ch),text:[$(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:it(o,qe(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=Ze(e,t.from,t.to),n||(n=uo(e,t)),e.cm?nl(e.cm,t,r):vo(e,t,r),Uo(e,n,V),e.cantEdit&&Yo(e,it(e.firstLine(),0))&&(e.cantEdit=!1)}}function nl(e,t,n){var r=e.doc,i=e.display,o=t.from,l=t.to,s=!1,a=o.line;e.options.lineWrapping||(a=et(Zt(qe(r,o.line))),r.iter(a,l.line+1,(function(e){if(e==i.maxLine)return s=!0,!0}))),r.sel.contains(t.from,t.to)>-1&&be(e),vo(r,t,n,Wr(e)),e.options.lineWrapping||(r.iter(a,o.line+t.text.length,(function(e){var t=ln(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,s=!1)})),s&&(e.curOp.updateMaxLine=!0)),Mt(r,o.line),Hi(e,400);var u=t.text.length-(l.line-o.line)-1;t.full?Er(e):o.line!=l.line||1!=t.text.length||go(e.doc,t)?Er(e,o.line,l.line+1,u):Ir(e,o.line,"text");var c=we(e,"changes"),h=we(e,"change");if(h||c){var f={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin};h&&Nn(e,"change",e,f),c&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(f)}e.display.selForContextMenu=null}function rl(e,t,n,r,i){var o;r||(r=n),ot(r,n)<0&&(o=[r,n],n=o[0],r=o[1]),"string"==typeof t&&(t=e.splitLines(t)),Zo(e,{from:n,to:r,text:t,origin:i})}function il(e,t,n,r){n<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function ol(e,t,n,r){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||(o=e[i]=o.deepCopy(),o.copied=!0);for(var s=0;s<o.ranges.length;s++)il(o.ranges[s].anchor,t,n,r),il(o.ranges[s].head,t,n,r)}else{for(var a=0;a<o.changes.length;++a){var u=o.changes[a];if(n<u.from.line)u.from=it(u.from.line+r,u.from.ch),u.to=it(u.to.line+r,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}}function ll(e,t){var n=t.from.line,r=t.to.line,i=t.text.length-(r-n)-1;ol(e.done,n,r,i),ol(e.undone,n,r,i)}function sl(e,t,n,r){var i=t,o=t;return"number"==typeof t?o=qe(e,ct(e,t)):i=et(t),null==i?null:(r(o,i)&&e.cm&&Ir(e.cm,i,n),o)}function al(e){this.lines=e,this.parent=null;for(var t=0,n=0;n<e.length;++n)e[n].parent=this,t+=e[n].height;this.height=t}function ul(e){this.children=e;for(var t=0,n=0,r=0;r<e.length;++r){var i=e[r];t+=i.chunkSize(),n+=i.height,i.parent=this}this.size=t,this.height=n,this.parent=null}io.prototype.from=function(){return ut(this.anchor,this.head)},io.prototype.to=function(){return at(this.anchor,this.head)},io.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},al.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n=e,r=e+t;n<r;++n){var i=this.lines[n];this.height-=i.height,cn(i),Nn(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,n){for(var r=e+t;e<r;++e)if(n(this.lines[e]))return!0}},ul.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var n=0;n<this.children.length;++n){var r=this.children[n],i=r.chunkSize();if(e<i){var o=Math.min(t,i-e),l=r.height;if(r.removeInner(e,o),this.height-=l-r.height,i==o&&(this.children.splice(n--,1),r.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof al))){var s=[];this.collapse(s),this.children=[new al(s)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,n){this.size+=t.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,n),i.lines&&i.lines.length>50){for(var l=i.lines.length%25+25,s=l;s<i.lines.length;){var a=new al(i.lines.slice(s,s+=25));i.height-=a.height,this.children.splice(++r,0,a),a.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=e.children.splice(e.children.length-5,5),n=new ul(t);if(e.parent){e.size-=n.size,e.height-=n.height;var r=B(e.parent.children,e);e.parent.children.splice(r+1,0,n)}else{var i=new ul(e.children);i.parent=e,e.children=[i,n],e=i}n.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e);if(i.iterN(e,l,n))return!0;if(0==(t-=l))break;e=0}else e-=o}}};var cl=function(e,t,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=e,this.node=t};function hl(e,t,n){on(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&oi(e,n)}function fl(e,t,n,r){var i=new cl(e,n,r),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),sl(e,t,"widget",(function(t){var n=t.widgets||(t.widgets=[]);if(null==i.insertAt?n.push(i):n.splice(Math.min(n.length-1,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!nn(e,t)){var r=on(t)<e.scrollTop;Qe(t,t.height+Un(i)),r&&oi(o,i.height),o.curOp.forceUpdate=!0}return!0})),o&&Nn(o,"lineWidgetAdded",o,i,"number"==typeof t?t:et(t)),i}cl.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,n=this.line,r=et(n);if(null!=r&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(n.widgets=null);var o=Un(this);Qe(n,Math.max(0,n.height-o)),e&&(Oi(e,(function(){hl(e,n,-o),Ir(e,r,"widget")})),Nn(e,"lineWidgetCleared",e,this,r))}},cl.prototype.changed=function(){var e=this,t=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=Un(this)-t;i&&(nn(this.doc,r)||Qe(r,r.height+i),n&&Oi(n,(function(){n.curOp.forceUpdate=!0,hl(n,r,i),Nn(n,"lineWidgetChanged",n,e,et(r))})))},xe(cl);var dl=0,pl=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++dl};function gl(e,t,n,r,i){if(r&&r.shared)return ml(e,t,n,r,i);if(e.cm&&!e.cm.curOp)return Di(e.cm,gl)(e,t,n,r,i);var o=new pl(e,i),l=ot(t,n);if(r&&I(r,o,!1),l>0||0==l&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=O("span",[o.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(qt(e,t.line,t,n,o)||t.line!=n.line&&qt(e,n.line,t,n,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Dt()}o.addToHistory&&Lo(e,{from:t,to:n,origin:"markText"},e.sel,NaN);var s,a=t.line,u=e.cm;if(e.iter(a,n.line+1,(function(e){u&&o.collapsed&&!u.options.lineWrapping&&Zt(e)==u.display.maxLine&&(s=!0),o.collapsed&&a!=t.line&&Qe(e,0),Ft(e,new Wt(o,a==t.line?t.ch:null,a==n.line?n.ch:null)),++a})),o.collapsed&&e.iter(t.line,n.line+1,(function(t){nn(e,t)&&Qe(t,0)})),o.clearOnEnter&&pe(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(Ot(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++dl,o.atomic=!0),u){if(s&&(u.curOp.updateMaxLine=!0),o.collapsed)Er(u,t.line,n.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var c=t.line;c<=n.line;c++)Ir(u,c,"text");o.atomic&&Ko(u.doc),Nn(u,"markerAdded",u,o)}return o}pl.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Ci(e),we(this,"clear")){var n=this.find();n&&Nn(this,"clear",n.from,n.to)}for(var r=null,i=null,o=0;o<this.lines.length;++o){var l=this.lines[o],s=Pt(l.markedSpans,this);e&&!this.collapsed?Ir(e,et(l),"text"):e&&(null!=s.to&&(i=et(l)),null!=s.from&&(r=et(l))),l.markedSpans=Ht(l.markedSpans,s),null==s.from&&this.collapsed&&!nn(this.doc,l)&&e&&Qe(l,Nr(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var a=0;a<this.lines.length;++a){var u=Zt(this.lines[a]),c=ln(u);c>e.display.maxLineLength&&(e.display.maxLine=u,e.display.maxLineLength=c,e.display.maxLineChanged=!0)}null!=r&&e&&this.collapsed&&Er(e,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&Ko(e.doc)),e&&Nn(e,"markerCleared",e,this,r,i),t&&Si(e),this.parent&&this.parent.clear()}},pl.prototype.find=function(e,t){var n,r;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],l=Pt(o.markedSpans,this);if(null!=l.from&&(n=it(t?o:et(o),l.from),-1==e))return n;if(null!=l.to&&(r=it(t?o:et(o),l.to),1==e))return r}return n&&{from:n,to:r}},pl.prototype.changed=function(){var e=this,t=this.find(-1,!0),n=this,r=this.doc.cm;t&&r&&Oi(r,(function(){var i=t.line,o=et(t.line),l=er(r,o);if(l&&(ur(l),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!nn(n.doc,i)&&null!=n.height){var s=n.height;n.height=null;var a=Un(n)-s;a&&Qe(i,i.height+a)}Nn(r,"markerChanged",r,e)}))},pl.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=B(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},pl.prototype.detachLine=function(e){if(this.lines.splice(B(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},xe(pl);var vl=function(e,t){this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=this};function ml(e,t,n,r,i){r=I(r),r.shared=!1;var o=[gl(e,t,n,r,i)],l=o[0],s=r.widgetNode;return mo(e,(function(e){s&&(r.widgetNode=s.cloneNode(!0)),o.push(gl(e,ht(e,t),ht(e,n),r,i));for(var a=0;a<e.linked.length;++a)if(e.linked[a].isParent)return;l=$(o)})),new vl(o,l)}function yl(e){return e.findMarks(it(e.first,0),e.clipPos(it(e.lastLine())),(function(e){return e.parent}))}function bl(e,t){for(var n=0;n<t.length;n++){var r=t[n],i=r.find(),o=e.clipPos(i.from),l=e.clipPos(i.to);if(ot(o,l)){var s=gl(e,o,l,r.primary,r.primary.type);r.markers.push(s),s.parent=r}}}function wl(e){for(var t=function(t){var n=e[t],r=[n.primary.doc];mo(n.primary.doc,(function(e){return r.push(e)}));for(var i=0;i<n.markers.length;i++){var o=n.markers[i];-1==B(r,o.doc)&&(o.parent=null,n.markers.splice(i--,1))}},n=0;n<e.length;n++)t(n)}vl.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();Nn(this,"clear")}},vl.prototype.find=function(e,t){return this.primary.find(e,t)},xe(vl);var xl=0,Cl=function(e,t,n,r,i){if(!(this instanceof Cl))return new Cl(e,t,n,r,i);null==n&&(n=0),ul.call(this,[new al([new an("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=it(n,0);this.sel=lo(o),this.history=new xo(null),this.id=++xl,this.modeOption=t,this.lineSep=r,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),vo(this,{from:o,to:o,text:e}),Go(this,lo(o),V)};Cl.prototype=Q(ul.prototype,{constructor:Cl,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,r=0;r<t.length;++r)n+=t[r].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=Je(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:Pi((function(e){var t=it(this.first,0),n=this.first+this.size-1;Zo(this,{from:t,to:it(n,qe(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&si(this.cm,0,0),Go(this,lo(t),V)})),replaceRange:function(e,t,n,r){t=ht(this,t),n=n?ht(this,n):t,rl(this,e,t,n,r)},getRange:function(e,t,n){var r=Ze(this,ht(this,e),ht(this,t));return!1===n?r:r.join(n||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(nt(this,e))return qe(this,e)},getLineNumber:function(e){return et(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=qe(this,e)),Zt(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return ht(this,e)},getCursor:function(e){var t,n=this.sel.primary();return t=null==e||"head"==e?n.head:"anchor"==e?n.anchor:"end"==e||"to"==e||!1===e?n.to():n.from(),t},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Pi((function(e,t,n){Ro(this,ht(this,"number"==typeof e?it(e,t||0):e),null,n)})),setSelection:Pi((function(e,t,n){Ro(this,ht(this,e),ht(this,t||e),n)})),extendSelection:Pi((function(e,t,n){Fo(this,ht(this,e),t&&ht(this,t),n)})),extendSelections:Pi((function(e,t){Eo(this,dt(this,e),t)})),extendSelectionsBy:Pi((function(e,t){var n=q(this.sel.ranges,e);Eo(this,dt(this,n),t)})),setSelections:Pi((function(e,t,n){if(e.length){for(var r=[],i=0;i<e.length;i++)r[i]=new io(ht(this,e[i].anchor),ht(this,e[i].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Go(this,oo(this.cm,r,t),n)}})),addSelection:Pi((function(e,t,n){var r=this.sel.ranges.slice(0);r.push(new io(ht(this,e),ht(this,t||e))),Go(this,oo(this.cm,r,r.length-1),n)})),getSelection:function(e){for(var t,n=this.sel.ranges,r=0;r<n.length;r++){var i=Ze(this,n[r].from(),n[r].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=Ze(this,n[r].from(),n[r].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[r]=i}return t},replaceSelection:function(e,t,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=e;this.replaceSelections(r,t,n||"+input")},replaceSelections:Pi((function(e,t,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];r[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:n}}for(var s=t&&"end"!=t&&ho(this,r,t),a=r.length-1;a>=0;a--)Zo(this,r[a]);s?Bo(this,s):this.cm&&li(this.cm)})),undo:Pi((function(){Qo(this,"undo")})),redo:Pi((function(){Qo(this,"redo")})),undoSelection:Pi((function(){Qo(this,"undo",!0)})),redoSelection:Pi((function(){Qo(this,"redo",!0)})),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){var e=this;this.history=new xo(this.history.maxGeneration),mo(this,(function(t){return t.history=e.history}),!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Po(this.history.done),undone:Po(this.history.undone)}},setHistory:function(e){var t=this.history=new xo(this.history.maxGeneration);t.done=Po(e.done.slice(0),null,!0),t.undone=Po(e.undone.slice(0),null,!0)},setGutterMarker:Pi((function(e,t,n){return sl(this,e,"gutter",(function(e){var r=e.gutterMarkers||(e.gutterMarkers={});return r[t]=n,!n&&re(r)&&(e.gutterMarkers=null),!0}))})),clearGutter:Pi((function(e){var t=this;this.iter((function(n){n.gutterMarkers&&n.gutterMarkers[e]&&sl(t,n,"gutter",(function(){return n.gutterMarkers[e]=null,re(n.gutterMarkers)&&(n.gutterMarkers=null),!0}))}))})),lineInfo:function(e){var t;if("number"==typeof e){if(!nt(this,e))return null;if(t=e,e=qe(this,e),!e)return null}else if(t=et(e),null==t)return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Pi((function(e,t,n){return sl(this,e,"gutter"==t?"gutter":"class",(function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[r]){if(k(n).test(e[r]))return!1;e[r]+=" "+n}else e[r]=n;return!0}))})),removeLineClass:Pi((function(e,t,n){return sl(this,e,"gutter"==t?"gutter":"class",(function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[r];if(!i)return!1;if(null==n)e[r]=null;else{var o=i.match(k(n));if(!o)return!1;var l=o.index+o[0].length;e[r]=i.slice(0,o.index)+(o.index&&l!=i.length?" ":"")+i.slice(l)||null}return!0}))})),addLineWidget:Pi((function(e,t,n){return fl(this,e,t,n)})),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return gl(this,ht(this,e),ht(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){var n={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return e=ht(this,e),gl(this,e,e,n,"bookmark")},findMarksAt:function(e){e=ht(this,e);var t=[],n=qe(this,e.line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,n){e=ht(this,e),t=ht(this,t);var r=[],i=e.line;return this.iter(e.line,t.line+1,(function(o){var l=o.markedSpans;if(l)for(var s=0;s<l.length;s++){var a=l[s];null!=a.to&&i==e.line&&e.ch>=a.to||null==a.from&&i!=e.line||null!=a.from&&i==t.line&&a.from>=t.ch||n&&!n(a.marker)||r.push(a.marker.parent||a.marker)}++i})),r},getAllMarks:function(){var e=[];return this.iter((function(t){var n=t.markedSpans;if(n)for(var r=0;r<n.length;++r)null!=n[r].from&&e.push(n[r].marker)})),e},posFromIndex:function(e){var t,n=this.first,r=this.lineSeparator().length;return this.iter((function(i){var o=i.text.length+r;if(o>e)return t=e,!0;e-=o,++n})),ht(this,it(n,t))},indexFromPos:function(e){e=ht(this,e);var t=e.ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,(function(e){t+=e.text.length+n})),t},copy:function(e){var t=new Cl(Je(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,n=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<n&&(n=e.to);var r=new Cl(Je(this,t,n),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],bl(r,yl(this)),r},unlinkDoc:function(e){if(e instanceof Es&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){var n=this.linked[t];if(n.doc==e){this.linked.splice(t,1),e.unlinkDoc(this),wl(yl(this));break}}if(e.history==this.history){var r=[e.id];mo(e,(function(e){return r.push(e.id)}),!0),e.history=new xo(null),e.history.done=Po(this.history.done,r),e.history.undone=Po(this.history.undone,r)}},iterLinkedDocs:function(e){mo(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Pe(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:Pi((function(e){"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter((function(e){return e.order=null})),this.cm&&wo(this.cm))}))}),Cl.prototype.eachLine=Cl.prototype.iter;var Sl=0;function kl(e){var t=this;if(Ml(t),!ye(t,e)&&!Vn(t.display,e)){Ce(e),l&&(Sl=+new Date);var n=Hr(t,e,!0),r=e.dataTransfer.files;if(n&&!t.isReadOnly())if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),s=0,a=function(){++s==i&&Di(t,(function(){n=ht(t.doc,n);var e={from:n,to:n,text:t.doc.splitLines(o.filter((function(e){return null!=e})).join(t.doc.lineSeparator())),origin:"paste"};Zo(t.doc,e),Bo(t.doc,lo(ht(t.doc,n),ht(t.doc,so(e))))}))()},u=function(e,n){if(t.options.allowDropFileTypes&&-1==B(t.options.allowDropFileTypes,e.type))a();else{var r=new FileReader;r.onerror=function(){return a()},r.onload=function(){var e=r.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[n]=e),a()},r.readAsText(e)}},c=0;c<r.length;c++)u(r[c],c);else{if(t.state.draggingText&&t.doc.sel.contains(n)>-1)return t.state.draggingText(e),void setTimeout((function(){return t.display.input.focus()}),20);try{var h=e.dataTransfer.getData("Text");if(h){var f;if(t.state.draggingText&&!t.state.draggingText.copy&&(f=t.listSelections()),Uo(t.doc,lo(n,n)),f)for(var d=0;d<f.length;++d)rl(t.doc,"",f[d].anchor,f[d].head,"drag");t.replaceSelection(h,"around","paste"),t.display.input.focus()}}catch(p){}}}}function Ll(e,t){if(l&&(!e.state.draggingText||+new Date-Sl<100))Le(t);else if(!ye(e,t)&&!Vn(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!f)){var n=A("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",h&&(n.width=n.height=1,e.display.wrapper.appendChild(n),n._top=n.offsetTop),t.dataTransfer.setDragImage(n,0,0),h&&n.parentNode.removeChild(n)}}function Tl(e,t){var n=Hr(e,t);if(n){var r=document.createDocumentFragment();Kr(e,n,r),e.display.dragCursor||(e.display.dragCursor=A("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),N(e.display.dragCursor,r)}}function Ml(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Nl(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),n=[],r=0;r<t.length;r++){var i=t[r].CodeMirror;i&&n.push(i)}n.length&&n[0].operation((function(){for(var t=0;t<n.length;t++)e(n[t])}))}}var Al=!1;function Ol(){Al||(Dl(),Al=!0)}function Dl(){var e;pe(window,"resize",(function(){null==e&&(e=setTimeout((function(){e=null,Nl(Wl)}),100))})),pe(window,"blur",(function(){return Nl(Zr)}))}function Wl(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Pl={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Hl=0;Hl<10;Hl++)Pl[Hl+48]=Pl[Hl+96]=String(Hl);for(var Fl=65;Fl<=90;Fl++)Pl[Fl]=String.fromCharCode(Fl);for(var El=1;El<=12;El++)Pl[El+111]=Pl[El+63235]="F"+El;var Il={};function Rl(e){var t,n,r,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var l=0;l<o.length-1;l++){var s=o[l];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else{if(!/^s(hift)?$/i.test(s))throw new Error("Unrecognized modifier name: "+s);r=!0}}return t&&(e="Alt-"+e),n&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),r&&(e="Shift-"+e),e}function zl(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==r){delete e[n];continue}for(var i=q(n.split(" "),Rl),o=0;o<i.length;o++){var l=void 0,s=void 0;o==i.length-1?(s=i.join(" "),l=r):(s=i.slice(0,o+1).join(" "),l="...");var a=t[s];if(a){if(a!=l)throw new Error("Inconsistent bindings for "+s)}else t[s]=l}delete e[n]}for(var u in t)e[u]=t[u];return e}function Bl(e,t,n,r){t=Kl(t);var i=t.call?t.call(e,r):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&n(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Bl(e,t.fallthrough,n,r);for(var o=0;o<t.fallthrough.length;o++){var l=Bl(e,t.fallthrough[o],n,r);if(l)return l}}}function Gl(e){var t="string"==typeof e?e:Pl[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Ul(e,t,n){var r=e;return t.altKey&&"Alt"!=r&&(e="Alt-"+e),(C?t.metaKey:t.ctrlKey)&&"Ctrl"!=r&&(e="Ctrl-"+e),(C?t.ctrlKey:t.metaKey)&&"Cmd"!=r&&(e="Cmd-"+e),!n&&t.shiftKey&&"Shift"!=r&&(e="Shift-"+e),e}function Vl(e,t){if(h&&34==e.keyCode&&e["char"])return!1;var n=Pl[e.keyCode];return null!=n&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(n=e.code),Ul(n,e,t))}function Kl(e){return"string"==typeof e?Il[e]:e}function jl(e,t){for(var n=e.doc.sel.ranges,r=[],i=0;i<n.length;i++){var o=t(n[i]);while(r.length&&ot(o.from,$(r).to)<=0){var l=r.pop();if(ot(l.from,o.from)<0){o.from=l.from;break}}r.push(o)}Oi(e,(function(){for(var t=r.length-1;t>=0;t--)rl(e.doc,"",r[t].from,r[t].to,"+delete");li(e)}))}function Xl(e,t,n){var r=le(e.text,t+n,n);return r<0||r>e.text.length?null:r}function Yl(e,t,n){var r=Xl(e,t.ch,n);return null==r?null:new it(t.line,r,n<0?"after":"before")}function _l(e,t,n,r,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=fe(n,t.doc.direction);if(o){var l,s=i<0?$(o):o[0],a=i<0==(1==s.level),u=a?"after":"before";if(s.level>0||"rtl"==t.doc.direction){var c=tr(t,n);l=i<0?n.text.length-1:0;var h=nr(t,c,l).top;l=se((function(e){return nr(t,c,e).top==h}),i<0==(1==s.level)?s.from:s.to-1,l),"before"==u&&(l=Xl(n,l,1))}else l=i<0?s.to:s.from;return new it(r,l,u)}}return new it(r,i<0?n.text.length:0,i<0?"before":"after")}function $l(e,t,n,r){var i=fe(t,e.doc.direction);if(!i)return Yl(t,n,r);n.ch>=t.text.length?(n.ch=t.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var o=ce(i,n.ch,n.sticky),l=i[o];if("ltr"==e.doc.direction&&l.level%2==0&&(r>0?l.to>n.ch:l.from<n.ch))return Yl(t,n,r);var s,a=function(e,n){return Xl(t,e instanceof it?e.ch:e,n)},u=function(n){return e.options.lineWrapping?(s=s||tr(e,t),Sr(e,t,s,n)):{begin:0,end:t.text.length}},c=u("before"==n.sticky?a(n,-1):n.ch);if("rtl"==e.doc.direction||1==l.level){var h=1==l.level==r<0,f=a(n,h?1:-1);if(null!=f&&(h?f<=l.to&&f<=c.end:f>=l.from&&f>=c.begin)){var d=h?"before":"after";return new it(n.line,f,d)}}var p=function(e,t,r){for(var o=function(e,t){return t?new it(n.line,a(e,1),"before"):new it(n.line,e,"after")};e>=0&&e<i.length;e+=t){var l=i[e],s=t>0==(1!=l.level),u=s?r.begin:a(r.end,-1);if(l.from<=u&&u<l.to)return o(u,s);if(u=s?l.from:a(l.to,-1),r.begin<=u&&u<r.end)return o(u,s)}},g=p(o+r,r,c);if(g)return g;var v=r>0?c.end:a(c.begin,-1);return null==v||r>0&&v==t.text.length||(g=p(r>0?0:i.length-1,r,u(v)),!g)?null:g}Il.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Il.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Il.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Il.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Il["default"]=y?Il.macDefault:Il.pcDefault;var ql={selectAll:$o,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),V)},killLine:function(e){return jl(e,(function(t){if(t.empty()){var n=qe(e.doc,t.head.line).text.length;return t.head.ch==n&&t.head.line<e.lastLine()?{from:t.head,to:it(t.head.line+1,0)}:{from:t.head,to:it(t.head.line,n)}}return{from:t.from(),to:t.to()}}))},deleteLine:function(e){return jl(e,(function(t){return{from:it(t.from().line,0),to:ht(e.doc,it(t.to().line+1,0))}}))},delLineLeft:function(e){return jl(e,(function(e){return{from:it(e.from().line,0),to:e.from()}}))},delWrappedLineLeft:function(e){return jl(e,(function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return{from:r,to:t.from()}}))},delWrappedLineRight:function(e){return jl(e,(function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div");return{from:t.from(),to:r}}))},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(it(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(it(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy((function(t){return Zl(e,t.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy((function(t){return Ql(e,t.head)}),{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy((function(t){return Jl(e,t.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy((function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div")}),j)},goLineLeft:function(e){return e.extendSelectionsBy((function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:n},"div")}),j)},goLineLeftSmart:function(e){return e.extendSelectionsBy((function(t){var n=e.cursorCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return r.ch<e.getLine(r.line).search(/\S/)?Ql(e,t.head):r}),j)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),r=e.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),l=R(e.getLine(o.line),o.ch,r);t.push(_(r-l%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Oi(e,(function(){for(var t=e.listSelections(),n=[],r=0;r<t.length;r++)if(t[r].empty()){var i=t[r].head,o=qe(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new it(i.line,i.ch-1)),i.ch>0)i=new it(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),it(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var l=qe(e.doc,i.line-1).text;l&&(i=new it(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+l.charAt(l.length-1),it(i.line-1,l.length-1),i,"+transpose"))}n.push(new io(i,i))}e.setSelections(n)}))},newlineAndIndent:function(e){return Oi(e,(function(){for(var t=e.listSelections(),n=t.length-1;n>=0;n--)e.replaceRange(e.doc.lineSeparator(),t[n].anchor,t[n].head,"+input");t=e.listSelections();for(var r=0;r<t.length;r++)e.indentLine(t[r].from().line,null,!0);li(e)}))},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Zl(e,t){var n=qe(e.doc,t),r=Zt(n);return r!=n&&(t=et(r)),_l(!0,e,r,t,1)}function Jl(e,t){var n=qe(e.doc,t),r=Jt(n);return r!=n&&(t=et(r)),_l(!0,e,n,t,-1)}function Ql(e,t){var n=Zl(e,t.line),r=qe(e.doc,n.line),i=fe(r,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(n.ch,r.text.search(/\S/)),l=t.line==n.line&&t.ch<=o&&t.ch;return it(n.line,l?0:o,n.sticky)}return n}function es(e,t,n){if("string"==typeof t&&(t=ql[t],!t))return!1;e.display.input.ensurePolled();var r=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),i=t(e)!=U}finally{e.display.shift=r,e.state.suppressEdits=!1}return i}function ts(e,t,n){for(var r=0;r<e.state.keyMaps.length;r++){var i=Bl(t,e.state.keyMaps[r],n,e);if(i)return i}return e.options.extraKeys&&Bl(t,e.options.extraKeys,n,e)||Bl(t,e.options.keyMap,n,e)}var ns=new z;function rs(e,t,n,r){var i=e.state.keySeq;if(i){if(Gl(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:ns.set(50,(function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())})),is(e,i+" "+t,n,r))return!0}return is(e,t,n,r)}function is(e,t,n,r){var i=ts(e,t,r);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&Nn(e,"keyHandled",e,t,n),"handled"!=i&&"multi"!=i||(Ce(n),Yr(e)),!!i}function os(e,t){var n=Vl(t,!0);return!!n&&(t.shiftKey&&!e.state.keySeq?rs(e,"Shift-"+n,t,(function(t){return es(e,t,!0)}))||rs(e,n,t,(function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return es(e,t)})):rs(e,n,t,(function(t){return es(e,t)})))}function ls(e,t,n){return rs(e,"'"+n+"'",t,(function(t){return es(e,t,!0)}))}var ss=null;function as(e){var t=this;if((!e.target||e.target==t.display.input.getField())&&(t.curOp.focus=W(),!ye(t,e))){l&&s<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var i=os(t,e);h&&(ss=i?r:null,i||88!=r||Fe||!(y?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),n&&!y&&!i&&46==r&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||us(t)}}function us(e){var t=e.display.lineDiv;function n(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),ve(document,"keyup",n),ve(document,"mouseover",n))}P(t,"CodeMirror-crosshair"),pe(document,"keyup",n),pe(document,"mouseover",n)}function cs(e){16==e.keyCode&&(this.doc.sel.shift=!1),ye(this,e)}function hs(e){var t=this;if((!e.target||e.target==t.display.input.getField())&&!(Vn(t.display,e)||ye(t,e)||e.ctrlKey&&!e.altKey||y&&e.metaKey)){var n=e.keyCode,r=e.charCode;if(h&&n==ss)return ss=null,void Ce(e);if(!h||e.which&&!(e.which<10)||!os(t,e)){var i=String.fromCharCode(null==r?n:r);"\b"!=i&&(ls(t,e,i)||t.display.input.onKeyPress(e))}}}var fs,ds,ps=400,gs=function(e,t,n){this.time=e,this.pos=t,this.button=n};function vs(e,t){var n=+new Date;return ds&&ds.compare(n,e,t)?(fs=ds=null,"triple"):fs&&fs.compare(n,e,t)?(ds=new gs(n,e,t),fs=null,"double"):(fs=new gs(n,e,t),ds=null,"single")}function ms(e){var t=this,n=t.display;if(!(ye(t,e)||n.activeTouch&&n.input.supportsTouch()))if(n.input.ensurePolled(),n.shift=e.shiftKey,Vn(n,e))a||(n.scroller.draggable=!1,setTimeout((function(){return n.scroller.draggable=!0}),100));else if(!Ts(t,e)){var r=Hr(t,e),i=Me(e),o=r?vs(r,i):"single";window.focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),r&&ys(t,i,r,o,e)||(1==i?r?ws(t,r,o,e):Te(e)==n.scroller&&Ce(e):2==i?(r&&Fo(t.doc,r),setTimeout((function(){return n.input.focus()}),20)):3==i&&(S?t.display.input.onContextMenu(e):$r(t)))}}function ys(e,t,n,r,i){var o="Click";return"double"==r?o="Double"+o:"triple"==r&&(o="Triple"+o),o=(1==t?"Left":2==t?"Middle":"Right")+o,rs(e,Ul(o,i),i,(function(t){if("string"==typeof t&&(t=ql[t]),!t)return!1;var r=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r=t(e,n)!=U}finally{e.state.suppressEdits=!1}return r}))}function bs(e,t,n){var r=e.getOption("configureMouse"),i=r?r(e,t,n):{};if(null==i.unit){var o=b?n.shiftKey&&n.metaKey:n.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||n.shiftKey),null==i.addNew&&(i.addNew=y?n.metaKey:n.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(y?n.altKey:n.ctrlKey)),i}function ws(e,t,n,r){l?setTimeout(E(_r,e),0):e.curOp.focus=W();var i,o=bs(e,n,r),s=e.doc.sel;e.options.dragDrop&&Oe&&!e.isReadOnly()&&"single"==n&&(i=s.contains(t))>-1&&(ot((i=s.ranges[i]).from(),t)<0||t.xRel>0)&&(ot(i.to(),t)>0||t.xRel<0)?xs(e,r,t,o):Ss(e,r,t,o)}function xs(e,t,n,r){var i=e.display,o=!1,u=Di(e,(function(t){a&&(i.scroller.draggable=!1),e.state.draggingText=!1,ve(i.wrapper.ownerDocument,"mouseup",u),ve(i.wrapper.ownerDocument,"mousemove",c),ve(i.scroller,"dragstart",h),ve(i.scroller,"drop",u),o||(Ce(t),r.addNew||Fo(e.doc,n,null,null,r.extend),a&&!f||l&&9==s?setTimeout((function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()}),20):i.input.focus())})),c=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},h=function(){return o=!0};a&&(i.scroller.draggable=!0),e.state.draggingText=u,u.copy=!r.moveOnDrag,i.scroller.dragDrop&&i.scroller.dragDrop(),pe(i.wrapper.ownerDocument,"mouseup",u),pe(i.wrapper.ownerDocument,"mousemove",c),pe(i.scroller,"dragstart",h),pe(i.scroller,"drop",u),$r(e),setTimeout((function(){return i.input.focus()}),20)}function Cs(e,t,n){if("char"==n)return new io(t,t);if("word"==n)return e.findWordAt(t);if("line"==n)return new io(it(t.line,0),ht(e.doc,it(t.line+1,0)));var r=n(e,t);return new io(r.from,r.to)}function Ss(e,t,n,r){var i=e.display,o=e.doc;Ce(t);var l,s,a=o.sel,u=a.ranges;if(r.addNew&&!r.extend?(s=o.sel.contains(n),l=s>-1?u[s]:new io(n,n)):(l=o.sel.primary(),s=o.sel.primIndex),"rectangle"==r.unit)r.addNew||(l=new io(n,n)),n=Hr(e,t,!0,!0),s=-1;else{var c=Cs(e,n,r.unit);l=r.extend?Ho(l,c.anchor,c.head,r.extend):c}r.addNew?-1==s?(s=u.length,Go(o,oo(e,u.concat([l]),s),{scroll:!1,origin:"*mouse"})):u.length>1&&u[s].empty()&&"char"==r.unit&&!r.extend?(Go(o,oo(e,u.slice(0,s).concat(u.slice(s+1)),0),{scroll:!1,origin:"*mouse"}),a=o.sel):Io(o,s,l,K):(s=0,Go(o,new ro([l],0),K),a=o.sel);var h=n;function f(t){if(0!=ot(h,t))if(h=t,"rectangle"==r.unit){for(var i=[],u=e.options.tabSize,c=R(qe(o,n.line).text,n.ch,u),f=R(qe(o,t.line).text,t.ch,u),d=Math.min(c,f),p=Math.max(c,f),g=Math.min(n.line,t.line),v=Math.min(e.lastLine(),Math.max(n.line,t.line));g<=v;g++){var m=qe(o,g).text,y=X(m,d,u);d==p?i.push(new io(it(g,y),it(g,y))):m.length>y&&i.push(new io(it(g,y),it(g,X(m,p,u))))}i.length||i.push(new io(n,n)),Go(o,oo(e,a.ranges.slice(0,s).concat(i),s),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,w=l,x=Cs(e,t,r.unit),C=w.anchor;ot(x.anchor,C)>0?(b=x.head,C=ut(w.from(),x.anchor)):(b=x.anchor,C=at(w.to(),x.head));var S=a.ranges.slice(0);S[s]=ks(e,new io(ht(o,C),b)),Go(o,oo(e,S,s),K)}}var d=i.wrapper.getBoundingClientRect(),p=0;function g(t){var n=++p,l=Hr(e,t,!0,"rectangle"==r.unit);if(l)if(0!=ot(l,h)){e.curOp.focus=W(),f(l);var s=ei(i,o);(l.line>=s.to||l.line<s.from)&&setTimeout(Di(e,(function(){p==n&&g(t)})),150)}else{var a=t.clientY<d.top?-20:t.clientY>d.bottom?20:0;a&&setTimeout(Di(e,(function(){p==n&&(i.scroller.scrollTop+=a,g(t))})),50)}}function v(t){e.state.selectingText=!1,p=1/0,t&&(Ce(t),i.input.focus()),ve(i.wrapper.ownerDocument,"mousemove",m),ve(i.wrapper.ownerDocument,"mouseup",y),o.history.lastSelOrigin=null}var m=Di(e,(function(e){0!==e.buttons&&Me(e)?g(e):v(e)})),y=Di(e,v);e.state.selectingText=y,pe(i.wrapper.ownerDocument,"mousemove",m),pe(i.wrapper.ownerDocument,"mouseup",y)}function ks(e,t){var n=t.anchor,r=t.head,i=qe(e.doc,n.line);if(0==ot(n,r)&&n.sticky==r.sticky)return t;var o=fe(i);if(!o)return t;var l=ce(o,n.ch,n.sticky),s=o[l];if(s.from!=n.ch&&s.to!=n.ch)return t;var a,u=l+(s.from==n.ch==(1!=s.level)?0:1);if(0==u||u==o.length)return t;if(r.line!=n.line)a=(r.line-n.line)*("ltr"==e.doc.direction?1:-1)>0;else{var c=ce(o,r.ch,r.sticky),h=c-l||(r.ch-n.ch)*(1==s.level?-1:1);a=c==u-1||c==u?h<0:h>0}var f=o[u+(a?-1:0)],d=a==(1==f.level),p=d?f.from:f.to,g=d?"after":"before";return n.ch==p&&n.sticky==g?t:new io(new it(n.line,p,g),r)}function Ls(e,t,n,r){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(f){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&Ce(t);var l=e.display,s=l.lineDiv.getBoundingClientRect();if(o>s.bottom||!we(e,n))return ke(t);o-=s.top-l.viewOffset;for(var a=0;a<e.display.gutterSpecs.length;++a){var u=l.gutters.childNodes[a];if(u&&u.getBoundingClientRect().right>=i){var c=tt(e.doc,o),h=e.display.gutterSpecs[a];return me(e,n,e,c,h.className,t),ke(t)}}}function Ts(e,t){return Ls(e,t,"gutterClick",!0)}function Ms(e,t){Vn(e.display,t)||Ns(e,t)||ye(e,t,"contextmenu")||S||e.display.input.onContextMenu(t)}function Ns(e,t){return!!we(e,"gutterContextMenu")&&Ls(e,t,"gutterContextMenu",!1)}function As(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),hr(e)}gs.prototype.compare=function(e,t,n){return this.time+ps>e&&0==ot(t,this.pos)&&n==this.button};var Os={toString:function(){return"CodeMirror.Init"}},Ds={},Ws={};function Ps(e){var t=e.optionHandlers;function n(n,r,i,o){e.defaults[n]=r,i&&(t[n]=o?function(e,t,n){n!=Os&&i(e,t,n)}:i)}e.defineOption=n,e.Init=Os,n("value","",(function(e,t){return e.setValue(t)}),!0),n("mode",null,(function(e,t){e.doc.modeOption=t,fo(e)}),!0),n("indentUnit",2,fo,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,(function(e){po(e),hr(e),Er(e)}),!0),n("lineSeparator",null,(function(e,t){if(e.doc.lineSep=t,t){var n=[],r=e.doc.first;e.doc.iter((function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,n.push(it(r,o))}r++}));for(var i=n.length-1;i>=0;i--)rl(e.doc,t,n[i],it(n[i].line,n[i].ch+t.length))}})),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200c\u200e\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,(function(e,t,n){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),n!=Os&&e.refresh()})),n("specialCharPlaceholder",gn,(function(e){return e.refresh()}),!0),n("electricChars",!0),n("inputStyle",m?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),n("spellcheck",!1,(function(e,t){return e.getInputField().spellcheck=t}),!0),n("autocorrect",!1,(function(e,t){return e.getInputField().autocorrect=t}),!0),n("autocapitalize",!1,(function(e,t){return e.getInputField().autocapitalize=t}),!0),n("rtlMoveVisually",!w),n("wholeLineUpdateBefore",!0),n("theme","default",(function(e){As(e),qi(e)}),!0),n("keyMap","default",(function(e,t,n){var r=Kl(t),i=n!=Os&&Kl(n);i&&i.detach&&i.detach(e,r),r.attach&&r.attach(e,i||null)})),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,Fs,!0),n("gutters",[],(function(e,t){e.display.gutterSpecs=_i(t,e.options.lineNumbers),qi(e)}),!0),n("fixedGutter",!0,(function(e,t){e.display.gutters.style.left=t?Dr(e.display)+"px":"0",e.refresh()}),!0),n("coverGutterNextToScrollbar",!1,(function(e){return mi(e)}),!0),n("scrollbarStyle","native",(function(e){wi(e),mi(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)}),!0),n("lineNumbers",!1,(function(e,t){e.display.gutterSpecs=_i(e.options.gutters,t),qi(e)}),!0),n("firstLineNumber",1,qi,!0),n("lineNumberFormatter",(function(e){return e}),qi,!0),n("showCursorWhenSelecting",!1,Ur,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("selectionsMayTouch",!1),n("readOnly",!1,(function(e,t){"nocursor"==t&&(Zr(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)})),n("screenReaderLabel",null,(function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)})),n("disableInput",!1,(function(e,t){t||e.display.input.reset()}),!0),n("dragDrop",!0,Hs),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,Ur,!0),n("singleCursorHeightPerLine",!0,Ur,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,po,!0),n("addModeClass",!1,po,!0),n("pollInterval",100),n("undoDepth",200,(function(e,t){return e.doc.history.undoDepth=t})),n("historyEventDelay",1250),n("viewportMargin",10,(function(e){return e.refresh()}),!0),n("maxHighlightLength",1e4,po,!0),n("moveInputWithCursor",!0,(function(e,t){t||e.display.input.resetPosition()})),n("tabindex",null,(function(e,t){return e.display.input.getField().tabIndex=t||""})),n("autofocus",null),n("direction","ltr",(function(e,t){return e.doc.setDirection(t)}),!0),n("phrases",null)}function Hs(e,t,n){var r=n&&n!=Os;if(!t!=!r){var i=e.display.dragFunctions,o=t?pe:ve;o(e.display.scroller,"dragstart",i.start),o(e.display.scroller,"dragenter",i.enter),o(e.display.scroller,"dragover",i.over),o(e.display.scroller,"dragleave",i.leave),o(e.display.scroller,"drop",i.drop)}}function Fs(e){e.options.lineWrapping?(P(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),sn(e)),Pr(e),Er(e),hr(e),setTimeout((function(){return mi(e)}),100)}function Es(e,t){var n=this;if(!(this instanceof Es))return new Es(e,t);this.options=t=t?I(t):{},I(Ds,t,!1);var r=t.value;"string"==typeof r?r=new Cl(r,t.mode,null,t.lineSeparator,t.direction):t.mode&&(r.modeOption=t.mode),this.doc=r;var i=new Es.inputStyles[t.inputStyle](this),o=this.display=new Zi(e,r,i,t);for(var u in o.wrapper.CodeMirror=this,As(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),wi(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new z,keySeq:null,specialChars:null},t.autofocus&&!m&&o.input.focus(),l&&s<11&&setTimeout((function(){return n.display.input.reset(!0)}),20),Is(this),Ol(),Ci(this),this.curOp.forceUpdate=!0,yo(this,r),t.autofocus&&!m||this.hasFocus()?setTimeout(E(qr,this),20):Zr(this),Ws)Ws.hasOwnProperty(u)&&Ws[u](this,t[u],Os);Yi(this),t.finishInit&&t.finishInit(this);for(var c=0;c<Rs.length;++c)Rs[c](this);Si(this),a&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function Is(e){var t=e.display;pe(t.scroller,"mousedown",Di(e,ms)),pe(t.scroller,"dblclick",l&&s<11?Di(e,(function(t){if(!ye(e,t)){var n=Hr(e,t);if(n&&!Ts(e,t)&&!Vn(e.display,t)){Ce(t);var r=e.findWordAt(n);Fo(e.doc,r.anchor,r.head)}}})):function(t){return ye(e,t)||Ce(t)}),pe(t.scroller,"contextmenu",(function(t){return Ms(e,t)})),pe(t.input.getField(),"contextmenu",(function(n){t.scroller.contains(n.target)||Ms(e,n)}));var n,r={end:0};function i(){t.activeTouch&&(n=setTimeout((function(){return t.activeTouch=null}),1e3),r=t.activeTouch,r.end=+new Date)}function o(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function a(e,t){if(null==t.left)return!0;var n=t.left-e.left,r=t.top-e.top;return n*n+r*r>400}pe(t.scroller,"touchstart",(function(i){if(!ye(e,i)&&!o(i)&&!Ts(e,i)){t.input.ensurePolled(),clearTimeout(n);var l=+new Date;t.activeTouch={start:l,moved:!1,prev:l-r.end<=300?r:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}})),pe(t.scroller,"touchmove",(function(){t.activeTouch&&(t.activeTouch.moved=!0)})),pe(t.scroller,"touchend",(function(n){var r=t.activeTouch;if(r&&!Vn(t,n)&&null!=r.left&&!r.moved&&new Date-r.start<300){var o,l=e.coordsChar(t.activeTouch,"page");o=!r.prev||a(r,r.prev)?new io(l,l):!r.prev.prev||a(r,r.prev.prev)?e.findWordAt(l):new io(it(l.line,0),ht(e.doc,it(l.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),Ce(n)}i()})),pe(t.scroller,"touchcancel",i),pe(t.scroller,"scroll",(function(){t.scroller.clientHeight&&(hi(e,t.scroller.scrollTop),di(e,t.scroller.scrollLeft,!0),me(e,"scroll",e))})),pe(t.scroller,"mousewheel",(function(t){return no(e,t)})),pe(t.scroller,"DOMMouseScroll",(function(t){return no(e,t)})),pe(t.wrapper,"scroll",(function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0})),t.dragFunctions={enter:function(t){ye(e,t)||Le(t)},over:function(t){ye(e,t)||(Tl(e,t),Le(t))},start:function(t){return Ll(e,t)},drop:Di(e,kl),leave:function(t){ye(e,t)||Ml(e)}};var u=t.input.getField();pe(u,"keyup",(function(t){return cs.call(e,t)})),pe(u,"keydown",Di(e,as)),pe(u,"keypress",Di(e,hs)),pe(u,"focus",(function(t){return qr(e,t)})),pe(u,"blur",(function(t){return Zr(e,t)}))}Es.defaults=Ds,Es.optionHandlers=Ws;var Rs=[];function zs(e,t,n,r){var i,o=e.doc;null==n&&(n="add"),"smart"==n&&(o.mode.indent?i=yt(e,t).state:n="prev");var l=e.options.tabSize,s=qe(o,t),a=R(s.text,null,l);s.stateAfter&&(s.stateAfter=null);var u,c=s.text.match(/^\s*/)[0];if(r||/\S/.test(s.text)){if("smart"==n&&(u=o.mode.indent(i,s.text.slice(c.length),s.text),u==U||u>150)){if(!r)return;n="prev"}}else u=0,n="not";"prev"==n?u=t>o.first?R(qe(o,t-1).text,null,l):0:"add"==n?u=a+e.options.indentUnit:"subtract"==n?u=a-e.options.indentUnit:"number"==typeof n&&(u=a+n),u=Math.max(0,u);var h="",f=0;if(e.options.indentWithTabs)for(var d=Math.floor(u/l);d;--d)f+=l,h+="\t";if(f<u&&(h+=_(u-f)),h!=c)return rl(o,h,it(t,0),it(t,c.length),"+input"),s.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<c.length){var v=it(t,c.length);Io(o,p,new io(v,v));break}}}Es.defineInitHook=function(e){return Rs.push(e)};var Bs=null;function Gs(e){Bs=e}function Us(e,t,n,r,i){var o=e.doc;e.display.shift=!1,r||(r=o.sel);var l=+new Date-200,s="paste"==i||e.state.pasteIncoming>l,a=Pe(t),u=null;if(s&&r.ranges.length>1)if(Bs&&Bs.text.join("\n")==t){if(r.ranges.length%Bs.text.length==0){u=[];for(var c=0;c<Bs.text.length;c++)u.push(o.splitLines(Bs.text[c]))}}else a.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(u=q(a,(function(e){return[e]})));for(var h=e.curOp.updateInput,f=r.ranges.length-1;f>=0;f--){var d=r.ranges[f],p=d.from(),g=d.to();d.empty()&&(n&&n>0?p=it(p.line,p.ch-n):e.state.overwrite&&!s?g=it(g.line,Math.min(qe(o,g.line).text.length,g.ch+$(a).length)):s&&Bs&&Bs.lineWise&&Bs.text.join("\n")==t&&(p=g=it(p.line,0)));var v={from:p,to:g,text:u?u[f%u.length]:a,origin:i||(s?"paste":e.state.cutIncoming>l?"cut":"+input")};Zo(e.doc,v),Nn(e,"inputRead",e,v)}t&&!s&&Ks(e,t),li(e),e.curOp.updateInput<2&&(e.curOp.updateInput=h),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Vs(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");if(n)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||Oi(t,(function(){return Us(t,n,0,null,"paste")})),!0}function Ks(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var n=e.doc.sel,r=n.ranges.length-1;r>=0;r--){var i=n.ranges[r];if(!(i.head.ch>100||r&&n.ranges[r-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var s=0;s<o.electricChars.length;s++)if(t.indexOf(o.electricChars.charAt(s))>-1){l=zs(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(qe(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=zs(e,i.head.line,"smart"));l&&Nn(e,"electricInput",e,i.head.line)}}}function js(e){for(var t=[],n=[],r=0;r<e.doc.sel.ranges.length;r++){var i=e.doc.sel.ranges[r].head.line,o={anchor:it(i,0),head:it(i+1,0)};n.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:n}}function Xs(e,t,n,r){e.setAttribute("autocorrect",n?"":"off"),e.setAttribute("autocapitalize",r?"":"off"),e.setAttribute("spellcheck",!!t)}function Ys(){var e=A("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=A("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return a?e.style.width="1000px":e.setAttribute("wrap","off"),g&&(e.style.border="1px solid black"),Xs(e),t}function _s(e){var t=e.optionHandlers,n=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,n){var r=this.options,i=r[e];r[e]==n&&"mode"!=e||(r[e]=n,t.hasOwnProperty(e)&&Di(this,t[e])(this,n,i),me(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Kl(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,n=0;n<t.length;++n)if(t[n]==e||t[n].name==e)return t.splice(n,1),!0},addOverlay:Wi((function(t,n){var r=t.token?t:e.getMode(this.options,t);if(r.startState)throw new Error("Overlays may not be stateful.");Z(this.state.overlays,{mode:r,modeSpec:t,opaque:n&&n.opaque,priority:n&&n.priority||0},(function(e){return e.priority})),this.state.modeGen++,Er(this)})),removeOverlay:Wi((function(e){for(var t=this.state.overlays,n=0;n<t.length;++n){var r=t[n].modeSpec;if(r==e||"string"==typeof e&&r.name==e)return t.splice(n,1),this.state.modeGen++,void Er(this)}})),indentLine:Wi((function(e,t,n){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),nt(this.doc,e)&&zs(this,e,t,n)})),indentSelection:Wi((function(e){for(var t=this.doc.sel.ranges,n=-1,r=0;r<t.length;r++){var i=t[r];if(i.empty())i.head.line>n&&(zs(this,i.head.line,e,!0),n=i.head.line,r==this.doc.sel.primIndex&&li(this));else{var o=i.from(),l=i.to(),s=Math.max(n,o.line);n=Math.min(this.lastLine(),l.line-(l.ch?0:1))+1;for(var a=s;a<n;++a)zs(this,a,e);var u=this.doc.sel.ranges;0==o.ch&&t.length==u.length&&u[r].from().ch>0&&Io(this.doc,r,new io(o,u[r].to()),V)}}})),getTokenAt:function(e,t){return St(this,e,t)},getLineTokens:function(e,t){return St(this,it(e),t,!0)},getTokenTypeAt:function(e){e=ht(this.doc,e);var t,n=mt(this,qe(this.doc,e.line)),r=0,i=(n.length-1)/2,o=e.ch;if(0==o)t=n[2];else for(;;){var l=r+i>>1;if((l?n[2*l-1]:0)>=o)i=l;else{if(!(n[2*l+1]<o)){t=n[2*l+2];break}r=l+1}}var s=t?t.indexOf("overlay "):-1;return s<0?t:0==s?null:t.slice(0,s-1)},getModeAt:function(t){var n=this.doc.mode;return n.innerMode?e.innerMode(n,this.getTokenAt(t).state).mode:n},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var r=[];if(!n.hasOwnProperty(t))return r;var i=n[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&r.push(i[o[t]]);else if(o[t])for(var l=0;l<o[t].length;l++){var s=i[o[t][l]];s&&r.push(s)}else o.helperType&&i[o.helperType]?r.push(i[o.helperType]):i[o.name]&&r.push(i[o.name]);for(var a=0;a<i._global.length;a++){var u=i._global[a];u.pred(o,this)&&-1==B(r,u.val)&&r.push(u.val)}return r},getStateAfter:function(e,t){var n=this.doc;return e=ct(n,null==e?n.first+n.size-1:e),yt(this,e+1,t).state},cursorCoords:function(e,t){var n,r=this.doc.sel.primary();return n=null==e?r.head:"object"==typeof e?ht(this.doc,e):e?r.from():r.to(),yr(this,n,t||"page")},charCoords:function(e,t){return mr(this,ht(this.doc,e),t||"page")},coordsChar:function(e,t){return e=vr(this,e,t||"page"),xr(this,e.left,e.top)},lineAtHeight:function(e,t){return e=vr(this,{top:e,left:0},t||"page").top,tt(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,n){var r,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),r=qe(this.doc,e)}else r=e;return gr(this,r,{top:0,left:0},t||"page",n||i).top+(i?this.doc.height-on(r):0)},defaultTextHeight:function(){return Nr(this.display)},defaultCharWidth:function(){return Ar(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,n,r,i){var o=this.display;e=yr(this,ht(this.doc,e));var l=e.bottom,s=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==r)l=e.top;else if("above"==r||"near"==r){var a=Math.max(o.wrapper.clientHeight,this.doc.height),u=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==r||e.bottom+t.offsetHeight>a)&&e.top>t.offsetHeight?l=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=a&&(l=e.bottom),s+t.offsetWidth>u&&(s=u-t.offsetWidth)}t.style.top=l+"px",t.style.left=t.style.right="","right"==i?(s=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?s=0:"middle"==i&&(s=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=s+"px"),n&&ri(this,{left:s,top:l,right:s+t.offsetWidth,bottom:l+t.offsetHeight})},triggerOnKeyDown:Wi(as),triggerOnKeyPress:Wi(hs),triggerOnKeyUp:cs,triggerOnMouseDown:Wi(ms),execCommand:function(e){if(ql.hasOwnProperty(e))return ql[e].call(null,this)},triggerElectric:Wi((function(e){Ks(this,e)})),findPosH:function(e,t,n,r){var i=1;t<0&&(i=-1,t=-t);for(var o=ht(this.doc,e),l=0;l<t;++l)if(o=$s(this.doc,o,i,n,r),o.hitSide)break;return o},moveH:Wi((function(e,t){var n=this;this.extendSelectionsBy((function(r){return n.display.shift||n.doc.extend||r.empty()?$s(n.doc,r.head,e,t,n.options.rtlMoveVisually):e<0?r.from():r.to()}),j)})),deleteH:Wi((function(e,t){var n=this.doc.sel,r=this.doc;n.somethingSelected()?r.replaceSelection("",null,"+delete"):jl(this,(function(n){var i=$s(r,n.head,e,t,!1);return e<0?{from:i,to:n.head}:{from:n.head,to:i}}))})),findPosV:function(e,t,n,r){var i=1,o=r;t<0&&(i=-1,t=-t);for(var l=ht(this.doc,e),s=0;s<t;++s){var a=yr(this,l,"div");if(null==o?o=a.left:a.left=o,l=qs(this,a,i,n),l.hitSide)break}return l},moveV:Wi((function(e,t){var n=this,r=this.doc,i=[],o=!this.display.shift&&!r.extend&&r.sel.somethingSelected();if(r.extendSelectionsBy((function(l){if(o)return e<0?l.from():l.to();var s=yr(n,l.head,"div");null!=l.goalColumn&&(s.left=l.goalColumn),i.push(s.left);var a=qs(n,s,e,t);return"page"==t&&l==r.sel.primary()&&oi(n,mr(n,a,"div").top-s.top),a}),j),i.length)for(var l=0;l<r.sel.ranges.length;l++)r.sel.ranges[l].goalColumn=i[l]})),findWordAt:function(e){var t=this.doc,n=qe(t,e.line).text,r=e.ch,i=e.ch;if(n){var o=this.getHelper(e,"wordChars");"before"!=e.sticky&&i!=n.length||!r?++i:--r;var l=n.charAt(r),s=ne(l,o)?function(e){return ne(e,o)}:/\s/.test(l)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!ne(e)};while(r>0&&s(n.charAt(r-1)))--r;while(i<n.length&&s(n.charAt(i)))++i}return new io(it(e.line,r),it(e.line,i))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?P(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),me(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==W()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Wi((function(e,t){si(this,e,t)})),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Yn(this)-this.display.barHeight,width:e.scrollWidth-Yn(this)-this.display.barWidth,clientHeight:$n(this),clientWidth:_n(this)}},scrollIntoView:Wi((function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:it(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?ai(this,e):ci(this,e.from,e.to,e.margin)})),setSize:Wi((function(e,t){var n=this,r=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=r(e)),null!=t&&(this.display.wrapper.style.height=r(t)),this.options.lineWrapping&&cr(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,(function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){Ir(n,i,"widget");break}++i})),this.curOp.forceUpdate=!0,me(this,"refresh",this)})),operation:function(e){return Oi(this,e)},startOperation:function(){return Ci(this)},endOperation:function(){return Si(this)},refresh:Wi((function(){var e=this.display.cachedTextHeight;Er(this),this.curOp.forceUpdate=!0,hr(this),si(this,this.doc.scrollLeft,this.doc.scrollTop),Ki(this.display),(null==e||Math.abs(e-Nr(this.display))>.5||this.options.lineWrapping)&&Pr(this),me(this,"refresh",this)})),swapDoc:Wi((function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),yo(this,e),hr(this),this.display.input.reset(),si(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,Nn(this,"swapDoc",this,t),t})),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},xe(e),e.registerHelper=function(t,r,i){n.hasOwnProperty(t)||(n[t]=e[t]={_global:[]}),n[t][r]=i},e.registerGlobalHelper=function(t,r,i,o){e.registerHelper(t,r,o),n[t]._global.push({pred:i,val:o})}}function $s(e,t,n,r,i){var o=t,l=n,s=qe(e,t.line),a=i&&"rtl"==e.direction?-n:n;function u(){var n=t.line+a;return!(n<e.first||n>=e.first+e.size)&&(t=new it(n,t.ch,t.sticky),s=qe(e,n))}function c(r){var o;if(o=i?$l(e.cm,s,t,n):Yl(s,t,n),null==o){if(r||!u())return!1;t=_l(i,e.cm,s,t.line,a)}else t=o;return!0}if("char"==r)c();else if("column"==r)c(!0);else if("word"==r||"group"==r)for(var h=null,f="group"==r,d=e.cm&&e.cm.getHelper(t,"wordChars"),p=!0;;p=!1){if(n<0&&!c(!p))break;var g=s.text.charAt(t.ch)||"\n",v=ne(g,d)?"w":f&&"\n"==g?"n":!f||/\s/.test(g)?null:"p";if(!f||p||v||(v="s"),h&&h!=v){n<0&&(n=1,c(),t.sticky="after");break}if(v&&(h=v),n>0&&!c(!p))break}var m=Yo(e,t,o,l,!0);return lt(o,m)&&(m.hitSide=!0),m}function qs(e,t,n,r){var i,o,l=e.doc,s=t.left;if("page"==r){var a=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),u=Math.max(a-.5*Nr(e.display),3);i=(n>0?t.bottom:t.top)+n*u}else"line"==r&&(i=n>0?t.bottom+3:t.top-3);for(;;){if(o=xr(e,s,i),!o.outside)break;if(n<0?i<=0:i>=l.height){o.hitSide=!0;break}i+=5*n}return o}var Zs=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new z,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function Js(e,t){var n=er(e,t.line);if(!n||n.hidden)return null;var r=qe(e.doc,t.line),i=Zn(n,r,t.line),o=fe(r,e.doc.direction),l="left";if(o){var s=ce(o,t.ch);l=s%2?"right":"left"}var a=or(i.map,t.ch,l);return a.offset="right"==a.collapse?a.end:a.start,a}function Qs(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function ea(e,t){return t&&(e.bad=!0),e}function ta(e,t,n,r,i){var o="",l=!1,s=e.doc.lineSeparator(),a=!1;function u(e){return function(t){return t.id==e}}function c(){l&&(o+=s,a&&(o+=s),l=a=!1)}function h(e){e&&(c(),o+=e)}function f(t){if(1==t.nodeType){var n=t.getAttribute("cm-text");if(n)return void h(n);var o,d=t.getAttribute("cm-marker");if(d){var p=e.findMarks(it(r,0),it(i+1,0),u(+d));return void(p.length&&(o=p[0].find(0))&&h(Ze(e.doc,o.from,o.to).join(s)))}if("false"==t.getAttribute("contenteditable"))return;var g=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;g&&c();for(var v=0;v<t.childNodes.length;v++)f(t.childNodes[v]);/^(pre|p)$/i.test(t.nodeName)&&(a=!0),g&&(l=!0)}else 3==t.nodeType&&h(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;;){if(f(t),t==n)break;t=t.nextSibling,a=!1}return o}function na(e,t,n){var r;if(t==e.display.lineDiv){if(r=e.display.lineDiv.childNodes[n],!r)return ea(e.clipPos(it(e.display.viewTo-1)),!0);t=null,n=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==r)return ra(o,t,n)}}function ra(e,t,n){var r=e.text.firstChild,i=!1;if(!t||!D(r,t))return ea(it(et(e.line),0),!0);if(t==r&&(i=!0,t=r.childNodes[n],n=0,!t)){var o=e.rest?$(e.rest):e.line;return ea(it(et(o),o.text.length),i)}var l=3==t.nodeType?t:null,s=t;l||1!=t.childNodes.length||3!=t.firstChild.nodeType||(l=t.firstChild,n&&(n=l.nodeValue.length));while(s.parentNode!=r)s=s.parentNode;var a=e.measure,u=a.maps;function c(t,n,r){for(var i=-1;i<(u?u.length:0);i++)for(var o=i<0?a.map:u[i],l=0;l<o.length;l+=3){var s=o[l+2];if(s==t||s==n){var c=et(i<0?e.line:e.rest[i]),h=o[l]+r;return(r<0||s!=t)&&(h=o[l+(r?1:0)]),it(c,h)}}}var h=c(l,s,n);if(h)return ea(h,i);for(var f=s.nextSibling,d=l?l.nodeValue.length-n:0;f;f=f.nextSibling){if(h=c(f,f.firstChild,0),h)return ea(it(h.line,h.ch-d),i);d+=f.textContent.length}for(var p=s.previousSibling,g=n;p;p=p.previousSibling){if(h=c(p,p.firstChild,-1),h)return ea(it(h.line,h.ch+g),i);g+=p.textContent.length}}Zs.prototype.init=function(e){var t=this,n=this,r=n.cm,i=n.div=e.lineDiv;function o(e){for(var t=e.target;t;t=t.parentNode){if(t==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}return!1}function l(e){if(o(e)&&!ye(r,e)){if(r.somethingSelected())Gs({lineWise:!1,text:r.getSelections()}),"cut"==e.type&&r.replaceSelection("",null,"cut");else{if(!r.options.lineWiseCopyCut)return;var t=js(r);Gs({lineWise:!0,text:t.text}),"cut"==e.type&&r.operation((function(){r.setSelections(t.ranges,0,V),r.replaceSelection("",null,"cut")}))}if(e.clipboardData){e.clipboardData.clearData();var l=Bs.text.join("\n");if(e.clipboardData.setData("Text",l),e.clipboardData.getData("Text")==l)return void e.preventDefault()}var s=Ys(),a=s.firstChild;r.display.lineSpace.insertBefore(s,r.display.lineSpace.firstChild),a.value=Bs.text.join("\n");var u=document.activeElement;F(a),setTimeout((function(){r.display.lineSpace.removeChild(s),u.focus(),u==i&&n.showPrimarySelection()}),50)}}Xs(i,r.options.spellcheck,r.options.autocorrect,r.options.autocapitalize),pe(i,"paste",(function(e){!o(e)||ye(r,e)||Vs(e,r)||s<=11&&setTimeout(Di(r,(function(){return t.updateFromDOM()})),20)})),pe(i,"compositionstart",(function(e){t.composing={data:e.data,done:!1}})),pe(i,"compositionupdate",(function(e){t.composing||(t.composing={data:e.data,done:!1})})),pe(i,"compositionend",(function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)})),pe(i,"touchstart",(function(){return n.forceCompositionEnd()})),pe(i,"input",(function(){t.composing||t.readFromDOMSoon()})),pe(i,"copy",l),pe(i,"cut",l)},Zs.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},Zs.prototype.prepareSelection=function(){var e=Vr(this.cm,!1);return e.focus=document.activeElement==this.div,e},Zs.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Zs.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Zs.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,r=t.doc.sel.primary(),i=r.from(),o=r.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var l=na(t,e.anchorNode,e.anchorOffset),s=na(t,e.focusNode,e.focusOffset);if(!l||l.bad||!s||s.bad||0!=ot(ut(l,s),i)||0!=ot(at(l,s),o)){var a=t.display.view,u=i.line>=t.display.viewFrom&&Js(t,i)||{node:a[0].measure.map[2],offset:0},c=o.line<t.display.viewTo&&Js(t,o);if(!c){var h=a[a.length-1].measure,f=h.maps?h.maps[h.maps.length-1]:h.map;c={node:f[f.length-1],offset:f[f.length-2]-f[f.length-3]}}if(u&&c){var d,p=e.rangeCount&&e.getRangeAt(0);try{d=L(u.node,u.offset,c.offset,c.node)}catch(g){}d&&(!n&&t.state.focused?(e.collapse(u.node,u.offset),d.collapsed||(e.removeAllRanges(),e.addRange(d))):(e.removeAllRanges(),e.addRange(d)),p&&null==e.anchorNode?e.addRange(p):n&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Zs.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation((function(){return e.cm.curOp.selectionChanged=!0}))}),20)},Zs.prototype.showMultipleSelections=function(e){N(this.cm.display.cursorDiv,e.cursors),N(this.cm.display.selectionDiv,e.selection)},Zs.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Zs.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return D(this.div,t)},Zs.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&document.activeElement==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Zs.prototype.blur=function(){this.div.blur()},Zs.prototype.getField=function(){return this.div},Zs.prototype.supportsTouch=function(){return!0},Zs.prototype.receivedFocus=function(){var e=this;function t(){e.cm.state.focused&&(e.pollSelection(),e.polling.set(e.cm.options.pollInterval,t))}this.selectionInEditor()?this.pollSelection():Oi(this.cm,(function(){return e.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,t)},Zs.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Zs.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(v&&c&&this.cm.display.gutterSpecs.length&&Qs(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var n=na(t,e.anchorNode,e.anchorOffset),r=na(t,e.focusNode,e.focusOffset);n&&r&&Oi(t,(function(){Go(t.doc,lo(n,r),V),(n.bad||r.bad)&&(t.curOp.selectionChanged=!0)}))}}},Zs.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,n,r=this.cm,i=r.display,o=r.doc.sel.primary(),l=o.from(),s=o.to();if(0==l.ch&&l.line>r.firstLine()&&(l=it(l.line-1,qe(r.doc,l.line-1).length)),s.ch==qe(r.doc,s.line).text.length&&s.line<r.lastLine()&&(s=it(s.line+1,0)),l.line<i.viewFrom||s.line>i.viewTo-1)return!1;l.line==i.viewFrom||0==(e=Fr(r,l.line))?(t=et(i.view[0].line),n=i.view[0].node):(t=et(i.view[e].line),n=i.view[e-1].node.nextSibling);var a,u,c=Fr(r,s.line);if(c==i.view.length-1?(a=i.viewTo-1,u=i.lineDiv.lastChild):(a=et(i.view[c+1].line)-1,u=i.view[c+1].node.previousSibling),!n)return!1;var h=r.doc.splitLines(ta(r,n,u,t,a)),f=Ze(r.doc,it(t,0),it(a,qe(r.doc,a).text.length));while(h.length>1&&f.length>1)if($(h)==$(f))h.pop(),f.pop(),a--;else{if(h[0]!=f[0])break;h.shift(),f.shift(),t++}var d=0,p=0,g=h[0],v=f[0],m=Math.min(g.length,v.length);while(d<m&&g.charCodeAt(d)==v.charCodeAt(d))++d;var y=$(h),b=$(f),w=Math.min(y.length-(1==h.length?d:0),b.length-(1==f.length?d:0));while(p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1))++p;if(1==h.length&&1==f.length&&t==l.line)while(d&&d>l.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1))d--,p++;h[h.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),h[0]=h[0].slice(d).replace(/\u200b+$/,"");var x=it(t,d),C=it(a,f.length?$(f).length-p:0);return h.length>1||h[0]||ot(x,C)?(rl(r.doc,h,x,C,"+input"),!0):void 0},Zs.prototype.ensurePolled=function(){this.forceCompositionEnd()},Zs.prototype.reset=function(){this.forceCompositionEnd()},Zs.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Zs.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()}),80))},Zs.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Oi(this.cm,(function(){return Er(e.cm)}))},Zs.prototype.setUneditable=function(e){e.contentEditable="false"},Zs.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Di(this.cm,Us)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Zs.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Zs.prototype.onContextMenu=function(){},Zs.prototype.resetPosition=function(){},Zs.prototype.needsContentAttribute=!0;var ia=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new z,this.hasSelection=!1,this.composing=null};function oa(e,t){if(t=t?I(t):{},t.value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var n=W();t.autofocus=n==e||null!=e.getAttribute("autofocus")&&n==document.body}function r(){e.value=s.getValue()}var i;if(e.form&&(pe(e.form,"submit",r),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var l=o.submit=function(){r(),o.submit=i,o.submit(),o.submit=l}}catch(a){}}t.finishInit=function(n){n.save=r,n.getTextArea=function(){return e},n.toTextArea=function(){n.toTextArea=isNaN,r(),e.parentNode.removeChild(n.getWrapperElement()),e.style.display="",e.form&&(ve(e.form,"submit",r),t.leaveSubmitMethodAlone||"function"!=typeof e.form.submit||(e.form.submit=i))}},e.style.display="none";var s=Es((function(t){return e.parentNode.insertBefore(t,e.nextSibling)}),t);return s}function la(e){e.off=ve,e.on=pe,e.wheelEventPixels=to,e.Doc=Cl,e.splitLines=Pe,e.countColumn=R,e.findColumn=X,e.isWordChar=te,e.Pass=U,e.signal=me,e.Line=an,e.changeEnd=so,e.scrollbarModel=bi,e.Pos=it,e.cmpPos=ot,e.modes=Re,e.mimeModes=ze,e.resolveMode=Ue,e.getMode=Ve,e.modeExtensions=Ke,e.extendMode=je,e.copyState=Xe,e.startState=_e,e.innerMode=Ye,e.commands=ql,e.keyMap=Il,e.keyName=Vl,e.isModifierKey=Gl,e.lookupKey=Bl,e.normalizeKeyMap=zl,e.StringStream=$e,e.SharedTextMarker=vl,e.TextMarker=pl,e.LineWidget=cl,e.e_preventDefault=Ce,e.e_stopPropagation=Se,e.e_stop=Le,e.addClass=P,e.contains=D,e.rmClass=T,e.keyNames=Pl}ia.prototype.init=function(e){var t=this,n=this,r=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!ye(r,e)){if(r.somethingSelected())Gs({lineWise:!1,text:r.getSelections()});else{if(!r.options.lineWiseCopyCut)return;var t=js(r);Gs({lineWise:!0,text:t.text}),"cut"==e.type?r.setSelections(t.ranges,null,V):(n.prevInput="",i.value=t.text.join("\n"),F(i))}"cut"==e.type&&(r.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),g&&(i.style.width="0px"),pe(i,"input",(function(){l&&s>=9&&t.hasSelection&&(t.hasSelection=null),n.poll()})),pe(i,"paste",(function(e){ye(r,e)||Vs(e,r)||(r.state.pasteIncoming=+new Date,n.fastPoll())})),pe(i,"cut",o),pe(i,"copy",o),pe(e.scroller,"paste",(function(t){if(!Vn(e,t)&&!ye(r,t)){if(!i.dispatchEvent)return r.state.pasteIncoming=+new Date,void n.focus();var o=new Event("paste");o.clipboardData=t.clipboardData,i.dispatchEvent(o)}})),pe(e.lineSpace,"selectstart",(function(t){Vn(e,t)||Ce(t)})),pe(i,"compositionstart",(function(){var e=r.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:e,range:r.markText(e,r.getCursor("to"),{className:"CodeMirror-composing"})}})),pe(i,"compositionend",(function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)}))},ia.prototype.createField=function(e){this.wrapper=Ys(),this.textarea=this.wrapper.firstChild},ia.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},ia.prototype.prepareSelection=function(){var e=this.cm,t=e.display,n=e.doc,r=Vr(e);if(e.options.moveInputWithCursor){var i=yr(e,n.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+l.top-o.top)),r.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+l.left-o.left))}return r},ia.prototype.showSelection=function(e){var t=this.cm,n=t.display;N(n.cursorDiv,e.cursors),N(n.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},ia.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var n=t.getSelection();this.textarea.value=n,t.state.focused&&F(this.textarea),l&&s>=9&&(this.hasSelection=n)}else e||(this.prevInput=this.textarea.value="",l&&s>=9&&(this.hasSelection=null))}},ia.prototype.getField=function(){return this.textarea},ia.prototype.supportsTouch=function(){return!1},ia.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!m||W()!=this.textarea))try{this.textarea.focus()}catch(e){}},ia.prototype.blur=function(){this.textarea.blur()},ia.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},ia.prototype.receivedFocus=function(){this.slowPoll()},ia.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){e.poll(),e.cm.state.focused&&e.slowPoll()}))},ia.prototype.fastPoll=function(){var e=!1,t=this;function n(){var r=t.poll();r||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,n))}t.pollingFast=!0,t.polling.set(20,n)},ia.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||!t.state.focused||He(n)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=n.value;if(i==r&&!t.somethingSelected())return!1;if(l&&s>=9&&this.hasSelection===i||y&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||r||(r="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}var a=0,u=Math.min(r.length,i.length);while(a<u&&r.charCodeAt(a)==i.charCodeAt(a))++a;return Oi(t,(function(){Us(t,i.slice(a),r.length-a,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?n.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},ia.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},ia.prototype.onKeyPress=function(){l&&s>=9&&(this.hasSelection=null),this.fastPoll()},ia.prototype.onContextMenu=function(e){var t=this,n=t.cm,r=n.display,i=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=Hr(n,e),u=r.scroller.scrollTop;if(o&&!h){var c=n.options.resetSelectionOnContextMenu;c&&-1==n.doc.sel.contains(o)&&Di(n,Go)(n.doc,lo(o),V);var f,d=i.style.cssText,p=t.wrapper.style.cssText,g=t.wrapper.offsetParent.getBoundingClientRect();if(t.wrapper.style.cssText="position: static",i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-g.top-5)+"px; left: "+(e.clientX-g.left-5)+"px;\n      z-index: 1000; background: "+(l?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",a&&(f=window.scrollY),r.input.focus(),a&&window.scrollTo(null,f),r.input.reset(),n.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=y,r.selForContextMenu=n.doc.sel,clearTimeout(r.detectingSelectAll),l&&s>=9&&m(),S){Le(e);var v=function(){ve(window,"mouseup",v),setTimeout(y,20)};pe(window,"mouseup",v)}else setTimeout(y,50)}function m(){if(null!=i.selectionStart){var e=n.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,r.selForContextMenu=n.doc.sel}}function y(){if(t.contextMenuPending==y&&(t.contextMenuPending=!1,t.wrapper.style.cssText=p,i.style.cssText=d,l&&s<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=u),null!=i.selectionStart)){(!l||l&&s<9)&&m();var e=0,o=function(){r.selForContextMenu==n.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?Di(n,$o)(n):e++<10?r.detectingSelectAll=setTimeout(o,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(o,200)}}},ia.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e},ia.prototype.setUneditable=function(){},ia.prototype.needsContentAttribute=!1,Ps(Es),_s(Es);var sa="iter insert remove copy getEditor constructor".split(" ");for(var aa in Cl.prototype)Cl.prototype.hasOwnProperty(aa)&&B(sa,aa)<0&&(Es.prototype[aa]=function(e){return function(){return e.apply(this.doc,arguments)}}(Cl.prototype[aa]));return xe(Cl),Es.inputStyles={textarea:ia,contenteditable:Zs},Es.defineMode=function(e){Es.defaults.mode||"null"==e||(Es.defaults.mode=e),Be.apply(this,arguments)},Es.defineMIME=Ge,Es.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),Es.defineMIME("text/plain","null"),Es.defineExtension=function(e,t){Es.prototype[e]=t},Es.defineDocExtension=function(e,t){Cl.prototype[e]=t},Es.fromTextArea=oa,la(Es),Es.version="5.55.0",Es}))},"76ae":function(e,t,n){(function(e){e(n("56b3"))})((function(e){"use strict";function t(e,t){if(!e.hasOwnProperty(t))throw new Error("Undefined state "+t+" in simple mode")}function n(e,t){if(!e)return/(?:)/;var n="";return e instanceof RegExp?(e.ignoreCase&&(n="i"),e=e.source):e=String(e),new RegExp((!1===t?"":"^")+"(?:"+e+")",n)}function r(e){if(!e)return null;if(e.apply)return e;if("string"==typeof e)return e.replace(/\./g," ");for(var t=[],n=0;n<e.length;n++)t.push(e[n]&&e[n].replace(/\./g," "));return t}function i(e,i){(e.next||e.push)&&t(i,e.next||e.push),this.regex=n(e.regex),this.token=r(e.token),this.data=e}function o(e,t){return function(n,r){if(r.pending){var i=r.pending.shift();return 0==r.pending.length&&(r.pending=null),n.pos+=i.text.length,i.token}if(r.local){if(r.local.end&&n.match(r.local.end)){var o=r.local.endToken||null;return r.local=r.localState=null,o}var l;o=r.local.mode.token(n,r.localState);return r.local.endScan&&(l=r.local.endScan.exec(n.current()))&&(n.pos=n.start+l.index),o}for(var a=e[r.state],u=0;u<a.length;u++){var c=a[u],h=(!c.data.sol||n.sol())&&n.match(c.regex);if(h){c.data.next?r.state=c.data.next:c.data.push?((r.stack||(r.stack=[])).push(r.state),r.state=c.data.push):c.data.pop&&r.stack&&r.stack.length&&(r.state=r.stack.pop()),c.data.mode&&s(t,r,c.data.mode,c.token),c.data.indent&&r.indent.push(n.indentation()+t.indentUnit),c.data.dedent&&r.indent.pop();var f=c.token;if(f&&f.apply&&(f=f(h)),h.length>2&&c.token&&"string"!=typeof c.token){r.pending=[];for(var d=2;d<h.length;d++)h[d]&&r.pending.push({text:h[d],token:c.token[d-1]});return n.backUp(h[0].length-(h[1]?h[1].length:0)),f[0]}return f&&f.join?f[0]:f}}return n.next(),null}}function l(e,t){if(e===t)return!0;if(!e||"object"!=typeof e||!t||"object"!=typeof t)return!1;var n=0;for(var r in e)if(e.hasOwnProperty(r)){if(!t.hasOwnProperty(r)||!l(e[r],t[r]))return!1;n++}for(var r in t)t.hasOwnProperty(r)&&n--;return 0==n}function s(t,r,i,o){var s;if(i.persistent)for(var a=r.persistentStates;a&&!s;a=a.next)(i.spec?l(i.spec,a.spec):i.mode==a.mode)&&(s=a);var u=s?s.mode:i.mode||e.getMode(t,i.spec),c=s?s.state:e.startState(u);i.persistent&&!s&&(r.persistentStates={mode:u,spec:i.spec,state:c,next:r.persistentStates}),r.localState=c,r.local={mode:u,end:i.end&&n(i.end),endScan:i.end&&!1!==i.forceEnd&&n(i.end,!1),endToken:o&&o.join?o[o.length-1]:o}}function a(e,t){for(var n=0;n<t.length;n++)if(t[n]===e)return!0}function u(t,n){return function(r,i,o){if(r.local&&r.local.mode.indent)return r.local.mode.indent(r.localState,i,o);if(null==r.indent||r.local||n.dontIndentStates&&a(r.state,n.dontIndentStates)>-1)return e.Pass;var l=r.indent.length-1,s=t[r.state];e:for(;;){for(var u=0;u<s.length;u++){var c=s[u];if(c.data.dedent&&!1!==c.data.dedentIfLineStart){var h=c.regex.exec(i);if(h&&h[0]){l--,(c.next||c.push)&&(s=t[c.next||c.push]),i=i.slice(h[0].length);continue e}}}break}return l<0?0:r.indent[l]}}e.defineSimpleMode=function(t,n){e.defineMode(t,(function(t){return e.simpleMode(t,n)}))},e.simpleMode=function(n,r){t(r,"start");var l={},s=r.meta||{},a=!1;for(var c in r)if(c!=s&&r.hasOwnProperty(c))for(var h=l[c]=[],f=r[c],d=0;d<f.length;d++){var p=f[d];h.push(new i(p,r)),(p.indent||p.dedent)&&(a=!0)}var g={startState:function(){return{state:"start",pending:null,local:null,localState:null,indent:a?[]:null}},copyState:function(t){var n={state:t.state,pending:t.pending,local:t.local,localState:null,indent:t.indent&&t.indent.slice(0)};t.localState&&(n.localState=e.copyState(t.local.mode,t.localState)),t.stack&&(n.stack=t.stack.slice(0));for(var r=t.persistentStates;r;r=r.next)n.persistentStates={mode:r.mode,spec:r.spec,state:r.state==t.localState?n.localState:e.copyState(r.mode,r.state),next:n.persistentStates};return n},token:o(l,n),innerMode:function(e){return e.local&&{mode:e.local.mode,state:e.localState}},indent:u(l,s)};if(s)for(var v in s)s.hasOwnProperty(v)&&(g[v]=s[v]);return g}}))},"9eb9":function(e,t,n){(function(e){e(n("56b3"))})((function(e){"use strict";e.overlayMode=function(t,n,r){return{startState:function(){return{base:e.startState(t),overlay:e.startState(n),basePos:0,baseCur:null,overlayPos:0,overlayCur:null,streamSeen:null}},copyState:function(r){return{base:e.copyState(t,r.base),overlay:e.copyState(n,r.overlay),basePos:r.basePos,baseCur:null,overlayPos:r.overlayPos,overlayCur:null}},token:function(e,i){return(e!=i.streamSeen||Math.min(i.basePos,i.overlayPos)<e.start)&&(i.streamSeen=e,i.basePos=i.overlayPos=e.start),e.start==i.basePos&&(i.baseCur=t.token(e,i.base),i.basePos=e.pos),e.start==i.overlayPos&&(e.pos=e.start,i.overlayCur=n.token(e,i.overlay),i.overlayPos=e.pos),e.pos=Math.min(i.basePos,i.overlayPos),null==i.overlayCur?i.baseCur:null!=i.baseCur&&i.overlay.combineTokens||r&&null==i.overlay.combineTokens?i.baseCur+" "+i.overlayCur:i.overlayCur},indent:t.indent&&function(e,n,r){return t.indent(e.base,n,r)},electricChars:t.electricChars,innerMode:function(e){return{state:e.base,mode:t}},blankLine:function(e){var i,o;return t.blankLine&&(i=t.blankLine(e.base)),n.blankLine&&(o=n.blankLine(e.overlay)),null==o?i:r&&null!=i?i+" "+o:o}}}}))},a7be:function(e,t,n){},eb0c:function(e,t,n){(function(e){e(n("56b3"))})((function(e){"use strict";e.multiplexingMode=function(t){var n=Array.prototype.slice.call(arguments,1);function r(e,t,n,r){if("string"==typeof t){var i=e.indexOf(t,n);return r&&i>-1?i+t.length:i}var o=t.exec(n?e.slice(n):e);return o?o.index+n+(r?o[0].length:0):-1}return{startState:function(){return{outer:e.startState(t),innerActive:null,inner:null}},copyState:function(n){return{outer:e.copyState(t,n.outer),innerActive:n.innerActive,inner:n.innerActive&&e.copyState(n.innerActive.mode,n.inner)}},token:function(i,o){if(o.innerActive){var l=o.innerActive;u=i.string;if(!l.close&&i.sol())return o.innerActive=o.inner=null,this.token(i,o);f=l.close?r(u,l.close,i.pos,l.parseDelimiters):-1;if(f==i.pos&&!l.parseDelimiters)return i.match(l.close),o.innerActive=o.inner=null,l.delimStyle&&l.delimStyle+" "+l.delimStyle+"-close";f>-1&&(i.string=u.slice(0,f));var s=l.mode.token(i,o.inner);return f>-1&&(i.string=u),f==i.pos&&l.parseDelimiters&&(o.innerActive=o.inner=null),l.innerStyle&&(s=s?s+" "+l.innerStyle:l.innerStyle),s}for(var a=1/0,u=i.string,c=0;c<n.length;++c){var h=n[c],f=r(u,h.open,i.pos);if(f==i.pos){h.parseDelimiters||i.match(h.open),o.innerActive=h;var d=0;if(t.indent){var p=t.indent(o.outer,"","");p!==e.Pass&&(d=p)}return o.inner=e.startState(h.mode,d),h.delimStyle&&h.delimStyle+" "+h.delimStyle+"-open"}-1!=f&&f<a&&(a=f)}a!=1/0&&(i.string=u.slice(0,a));var g=t.token(i,o.outer);return a!=1/0&&(i.string=u),g},indent:function(n,r,i){var o=n.innerActive?n.innerActive.mode:t;return o.indent?o.indent(n.innerActive?n.inner:n.outer,r,i):e.Pass},blankLine:function(r){var i=r.innerActive?r.innerActive.mode:t;if(i.blankLine&&i.blankLine(r.innerActive?r.inner:r.outer),r.innerActive)"\n"===r.innerActive.close&&(r.innerActive=r.inner=null);else for(var o=0;o<n.length;++o){var l=n[o];"\n"===l.open&&(r.innerActive=l,r.inner=e.startState(l.mode,i.indent?i.indent(r.outer,"",""):0))}},electricChars:t.electricChars,innerMode:function(e){return e.inner?{state:e.inner,mode:e.innerActive.mode}:{state:e.outer,mode:t}}}}}))},f6b6:function(e,t,n){}}]);