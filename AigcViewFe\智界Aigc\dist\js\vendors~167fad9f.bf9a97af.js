(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~167fad9f"],{2638:function(e,n,t){"use strict";function r(){return r=Object.assign||function(e){for(var n,t=1;t<arguments.length;t++)for(var r in n=arguments[t],n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},r.apply(this,arguments)}var o=["attrs","props","domProps"],i=["class","style","directives"],a=["on","nativeOn"],l=function(e){return e.reduce((function(e,n){for(var t in n)if(e[t])if(-1!==o.indexOf(t))e[t]=r({},e[t],n[t]);else if(-1!==i.indexOf(t)){var l=e[t]instanceof Array?e[t]:[e[t]],c=n[t]instanceof Array?n[t]:[n[t]];e[t]=l.concat(c)}else if(-1!==a.indexOf(t))for(var f in n[t])if(e[t][f]){var s=e[t][f]instanceof Array?e[t][f]:[e[t][f]],d=n[t][f]instanceof Array?n[t][f]:[n[t][f]];e[t][f]=s.concat(d)}else e[t][f]=n[t][f];else if("hook"==t)for(var p in n[t])e[t][p]=e[t][p]?u(e[t][p],n[t][p]):n[t][p];else e[t]=n[t];else e[t]=n[t];return e}),{})},u=function(e,n){return function(){e&&e.apply(this,arguments),n&&n.apply(this,arguments)}};e.exports=l},f4c3:function(e,n,t){
/*!
 * TOAST UI Editor : i18n
 * @version 2.2.0
 * <AUTHOR> FE Development Lab <<EMAIL>>
 * @license MIT
 */
(function(n,r){e.exports=r(t("a0ae"))})(window,(function(e){return function(e){var n={};function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)t.d(r,o,function(n){return e[n]}.bind(null,o));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=18)}({0:function(n,t){n.exports=e},18:function(e,n,t){"use strict";t.r(n);var r=t(0),o=t.n(r);o.a.setLanguage("zh-CN",{Markdown:"Markdown",WYSIWYG:"所见即所得",Write:"编辑",Preview:"预览",Headings:"标题",Paragraph:"文本",Bold:"加粗",Italic:"斜体字",Strike:"删除线",Code:"内嵌代码",Line:"水平线",Blockquote:"引用块","Unordered list":"无序列表","Ordered list":"有序列表",Task:"任务",Indent:"缩进",Outdent:"减少缩进","Insert link":"插入链接","Insert CodeBlock":"插入代码块","Insert table":"插入表格","Insert image":"插入图片",Heading:"标题","Image URL":"图片网址","Select image file":"选择图片文件",Description:"说明",OK:"确认",More:"更多",Cancel:"取消",File:"文件",URL:"URL","Link text":"链接文本","Add row":"添加行","Add col":"添加列","Remove row":"删除行","Remove col":"删除列","Align left":"左对齐","Align center":"居中对齐","Align right":"右对齐","Remove table":"删除表格","Would you like to paste as table?":"需要粘贴为表格吗?","Text color":"文字颜色","Auto scroll enabled":"自动滚动已启用","Auto scroll disabled":"自动滚动已禁用","Choose language":"选择语言"})}})}))},f513:function(e,n,t){}}]);