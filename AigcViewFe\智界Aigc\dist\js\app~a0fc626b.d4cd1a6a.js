(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["app~a0fc626b"],{"0f63":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("global-layout",{on:{dynamicRouterShow:t.dynamicRouterShow}},[i("contextmenu",{staticStyle:{"z-index":"9999"},attrs:{itemList:t.menuItemList,visible:t.menuVisible},on:{"update:visible":function(e){t.menuVisible=e},select:t.onMenuSelect}}),t.multipage?i("a-tabs",{staticClass:"tab-layout-tabs",staticStyle:{height:"52px"},attrs:{"active-key":t.activePage,"hide-add":!0,type:"editable-card"},on:{change:t.changePage,tabClick:t.tab<PERSON>all<PERSON>ack,edit:t.editPage},nativeOn:{contextmenu:function(e){return function(e){return t.onContextmenu(e)}(e)}}},t._l(t.pageList,(function(e){return i("a-tab-pane",{key:e.fullPath,attrs:{id:e.fullPath,closable:!("首页"==e.meta.title)}},[i("span",{attrs:{slot:"tab",pagekey:e.fullPath},slot:"tab"},[t._v(t._s(e.meta.title))])])})),1):t._e(),i("div",{staticStyle:{margin:"12px 12px 0"}},[t.multipage?i("keep-alive",[t.reloadFlag?i("router-view"):t._e()],1):[t.reloadFlag?i("router-view"):t._e()]],2)],1)},a=[],s=i("8415"),r=i("91ad"),o=i("ac0d"),l=i("ca00"),u=i("2b0e"),c=i("9fb0");function h(t){return m(t)||p(t)||d(t)||f()}function f(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"===typeof t)return g(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?g(t,e):void 0}}function p(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function m(t){if(Array.isArray(t))return g(t)}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var v="/dashboard/analysis",y={name:"TabLayout",components:{GlobalLayout:s["default"],Contextmenu:r["default"]},mixins:[o["a"],o["b"]],data:function(){return{pageList:[],linkList:[],activePage:"",menuVisible:!1,menuItemList:[{key:"4",icon:"reload",text:"刷 新"},{key:"1",icon:"arrow-left",text:"关闭左侧"},{key:"2",icon:"arrow-right",text:"关闭右侧"},{key:"3",icon:"close",text:"关闭其它"}],reloadFlag:!0}},provide:function(){return{closeCurrent:this.closeCurrent}},computed:{multipage:function(){return!this.isMobile()&&this.$store.state.app.multipage}},created:function(){this.$route.path!=v&&this.addIndexToFirst();var t=Object.assign({},this.$route);t.meta=Object.assign({},t.meta),this.pageList.push(t),this.linkList.push(t.fullPath),this.activePage=t.fullPath},mounted:function(){},watch:{$route:function(t){if(this.activePage=t.fullPath,this.multipage){if(v==t.fullPath)!1===t.meta.keepAlive&&this.routeReload();else if(this.linkList.indexOf(t.fullPath)<0)this.linkList.push(t.fullPath),this.pageList.push(Object.assign({},t));else if(this.linkList.indexOf(t.fullPath)>=0){var e=this.linkList.indexOf(t.fullPath),i=this.pageList[e];this.pageList.splice(e,1,Object.assign({},t,{meta:i.meta}))}}else this.linkList=[t.fullPath],this.pageList=[Object.assign({},t)]},activePage:function(t){var e=this.linkList.lastIndexOf(t),i=this.pageList[e];i.fullPath!==this.$route.fullPath&&this.$router.push(Object.assign({},i)),this.changeTitle(i.meta.title)},multipage:function(t){this.reloadFlag&&(t||(this.linkList=[this.$route.fullPath],this.pageList=[this.$route]))},device:function(){this.multipage&&-1===this.linkList.indexOf(v)&&this.addIndexToFirst()}},methods:{addIndexToFirst:function(){this.pageList.splice(0,0,{name:"dashboard-analysis",path:v,fullPath:v,meta:{icon:"dashboard",title:"首页"}}),this.linkList.splice(0,0,v)},changeTitle:function(t){var e="智界AIGC";this.$route.path===v?document.title=e:document.title=t+" · "+e},changePage:function(t){this.activePage=t},tabCallBack:function(){this.$nextTick((function(){setTimeout((function(){Object(l["p"])()}),20)}))},editPage:function(t,e){this[e](t)},remove:function(t){if(t!=v)if(1!==this.pageList.length){var e=this.pageList.filter((function(e){return e.fullPath==t}));this.pageList=this.pageList.filter((function(e){return e.fullPath!==t}));var i=this.linkList.indexOf(t);this.linkList=this.linkList.filter((function(e){return e!==t})),i=i>=this.linkList.length?this.linkList.length-1:i,this.activePage=this.linkList[i];var n=u["default"].ls.get(c["b"])||[];if(e&&e[0]){var a=e[0].meta.componentName;n.includes(a)&&(n.splice(n.findIndex((function(t){return t===a})),1),u["default"].ls.set(c["b"],n))}}else this.$message.warning("这是最后一页，不能再关闭了啦");else this.$message.warning("首页不能关闭!")},onContextmenu:function(t){var e=this.getPageKey(t.target);null!==e&&(t.preventDefault(),this.menuVisible=!0)},getPageKey:function(t,e){if(e=e||0,e>2)return null;var i=t.getAttribute("pagekey");return i=i||(t.previousElementSibling?t.previousElementSibling.getAttribute("pagekey"):null),i||(t.firstElementChild?this.getPageKey(t.firstElementChild,++e):null)},onMenuSelect:function(t,e){var i=this.getPageKey(e);switch(t){case"1":this.closeLeft(i);break;case"2":this.closeRight(i);break;case"3":this.closeOthers(i);break;case"4":this.routeReload();break;default:break}},closeCurrent:function(){this.remove(this.activePage)},closeOthers:function(t){var e=this.linkList.indexOf(t);if(t==v||t.indexOf("?ticke=")>=0)this.linkList=this.linkList.slice(e,e+1),this.pageList=this.pageList.slice(e,e+1),this.activePage=this.linkList[0];else{var i=this.pageList.slice(0,1)[0];this.linkList=this.linkList.slice(e,e+1),this.pageList=this.pageList.slice(e,e+1),this.linkList.unshift(i.fullPath),this.pageList.unshift(i),this.activePage=this.linkList[1]}},closeLeft:function(t){if(t!=v){var e=h(this.pageList),i=e.slice(0,1)[0],n=this.linkList.indexOf(t);this.linkList=this.linkList.slice(n),this.pageList=this.pageList.slice(n),this.linkList.unshift(i.fullPath),this.pageList.unshift(i),this.linkList.indexOf(this.activePage)<0&&(this.activePage=this.linkList[0])}},closeRight:function(t){var e=this.linkList.indexOf(t);this.linkList=this.linkList.slice(0,e+1),this.pageList=this.pageList.slice(0,e+1),this.linkList.indexOf(this.activePage<0)&&(this.activePage=this.linkList[this.linkList.length-1])},dynamicRouterShow:function(t,e){var i=this.linkList.indexOf(t);if(i>=0){var n=this.pageList[i],a=Object.assign({},n.meta,{title:e});this.pageList.splice(i,1,Object.assign({},n,{meta:a})),t===this.activePage&&this.changeTitle(e)}},routeReload:function(){var t=this;this.reloadFlag=!1;var e="ToggleMultipage";this.$store.dispatch(e,!1),this.$nextTick((function(){t.$store.dispatch(e,!0),t.reloadFlag=!0}))},excuteCallback:function(t){t()}}},b=y,k=(i("47b1"),i("2877")),L=Object(k["a"])(b,n,a,!1,null,null,null);e["default"]=L.exports},"22fb":function(t,e,i){},"2a7a":function(t,e,i){"use strict";var n=i("7efd");i.d(e,"b",(function(){return n["default"]}));var a=i("e017");i.d(e,"a",(function(){return a["default"]}));i("5f32"),i("501f"),i("345a"),i("0f63")},"2c11":function(t,e,i){},"2e5e":function(t,e,i){"use strict";var n=i("22fb"),a=i.n(n);a.a},"345a":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("page-layout",{attrs:{desc:t.description,title:t.getTitle,"link-list":t.linkList,search:t.search,tabs:t.tabs}},[i("div",{staticClass:"extra-img",attrs:{slot:"extra"},slot:"extra"},[i("img",{attrs:{src:t.extraImage}})]),i("route-view",{ref:"content"})],1)},a=[],s=i("b445"),r=i("501f"),o={name:"PageContent",components:{RouteView:r["default"],PageLayout:s["default"]},data:function(){return{title:"",description:"",linkList:[],extraImage:"",search:!1,tabs:{}}},mounted:function(){this.getPageHeaderInfo()},updated:function(){this.getPageHeaderInfo()},computed:{getTitle:function(){return this.$route.meta.title}},methods:{getPageHeaderInfo:function(){this.title=this.$route.meta.title;var t=this.$refs.content&&this.$refs.content.$children[0];t&&(this.description=t.description,this.linkList=t.linkList,this.extraImage=t.extraImage,this.search=1==t.search,this.tabs=t.tabs)}}},l=o,u=(i("2e5e"),i("2877")),c=Object(u["a"])(l,n,a,!1,null,"4e528f33",null);e["default"]=c.exports},"3c98":function(t,e,i){},"47b1":function(t,e,i){"use strict";var n=i("6faa"),a=i.n(n);a.a},"501f":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"main"},[i("keep-alive",{attrs:{include:t.includedComponents}},[t.keepAlive?i("router-view"):t._e()],1),t.keepAlive?t._e():i("router-view")],1)},a=[],s=i("2b0e"),r=i("9fb0"),o={name:"RouteView",computed:{includedComponents:function(){var t=s["default"].ls.get(r["b"]);if(this.$route.meta.keepAlive&&this.$route.meta.componentName){var e=s["default"].ls.get(r["b"])||[];if(!e.includes(this.$route.meta.componentName))return e.push(this.$route.meta.componentName),s["default"].ls.set(r["b"],e),e}return t},keepAlive:function(){return this.$route.meta.keepAlive}}},l=o,u=i("2877"),c=Object(u["a"])(l,n,a,!1,null,null,null);e["default"]=c.exports},5661:function(t,e,i){"use strict";var n=i("b2d9"),a=i.n(n);a.a},"5f32":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("global-layout",[i("transition",{attrs:{name:"page-transition"}},[t.keepAlive?i("keep-alive",[i("router-view")],1):i("router-view")],1)],1)},a=[],s=i("8415"),r={name:"BasicLayout",components:{GlobalLayout:s["default"]},data:function(){return{}},computed:{keepAlive:function(){return this.$route.meta.keepAlive}},methods:{}},o=r,l=(i("dac0"),i("2877")),u=Object(l["a"])(o,n,a,!1,null,null,null);e["default"]=u.exports},"6faa":function(t,e,i){},"7efd":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{class:["user-layout-wrapper",t.device],attrs:{id:"userLayout"}},[i("div",{staticClass:"container"},[i("div",{staticClass:"main"},[i("route-view")],1),i("div",{staticClass:"footer"},[i("div",{staticClass:"copyright"},[t._v("Copyright © "+t._s((new Date).getFullYear()))])])])])},a=[],s=i("501f"),r=i("ac0d"),o={name:"UserLayout",components:{RouteView:s["default"]},mixins:[r["b"]],data:function(){return{}},mounted:function(){document.body.classList.add("userLayout")},beforeDestroy:function(){document.body.classList.remove("userLayout")}},l=o,u=(i("5661"),i("f1c2"),i("2877")),c=Object(u["a"])(l,n,a,!1,null,"79891e04",null);e["default"]=c.exports},"8c91":function(t,e,i){},"91ad":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("a-menu",{directives:[{name:"show",rawName:"v-show",value:t.visible,expression:"visible"}],staticClass:"contextmenu",style:t.style,attrs:{selectedKeys:t.selectedKeys},on:{click:t.handleClick}},t._l(t.itemList,(function(e){return i("a-menu-item",{key:e.key},[e.icon?i("a-icon",{attrs:{role:"menuitemicon",type:e.icon}}):t._e(),t._v(t._s(e.text)+"\n  ")],1)})),1)},a=[],s={name:"Contextmenu",props:{visible:{type:Boolean,required:!1,default:!1},itemList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{left:0,top:0,target:null,selectedKeys:[]}},computed:{style:function(){return{left:this.left+"px",top:this.top+"px"}}},created:function(){var t=this;window.addEventListener("mousedown",(function(e){return t.closeMenu(e)})),window.addEventListener("contextmenu",(function(e){return t.setPosition(e)}))},methods:{closeMenu:function(t){!0===this.visible&&["menuitemicon","menuitem"].indexOf(t.target.getAttribute("role"))<0&&this.$emit("update:visible",!1)},setPosition:function(t){this.left=t.clientX,this.top=t.clientY,this.target=t.target},handleClick:function(t){var e=t.key;this.$emit("select",e,this.target),this.$emit("update:visible",!1)}}},r=s,o=(i("bd59"),i("2877")),l=Object(o["a"])(r,n,a,!1,null,"99248454",null);e["default"]=l.exports},"955f":function(t,e,i){"use strict";var n=i("2638"),a=i.n(n),s=i("55f1"),r=i("0c63");function o(t){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function l(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function u(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?l(Object(i),!0).forEach((function(e){c(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function c(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function h(t,e){var i;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(i=f(t))||e&&t&&"number"===typeof t.length){i&&(t=i);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,r=!0,o=!1;return{s:function(){i=t[Symbol.iterator]()},n:function(){var t=i.next();return r=t.done,t},e:function(t){o=!0,s=t},f:function(){try{r||null==i.return||i.return()}finally{if(o)throw s}}}}function f(t,e){if(t){if("string"===typeof t)return d(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var p=s["a"].Item,m=s["a"].SubMenu;e["a"]={name:"SMenu",props:{menu:{type:Array,required:!0},theme:{type:String,required:!1,default:"dark"},mode:{type:String,required:!1,default:"inline"},collapsed:{type:Boolean,required:!1,default:!1}},data:function(){return{openKeys:[],selectedKeys:[],cachedOpenKeys:[]}},computed:{rootSubmenuKeys:function(t){var e=[];return t.menu.forEach((function(t){return e.push(t.path)})),e}},mounted:function(){this.updateMenu()},watch:{collapsed:function(t){t?(this.cachedOpenKeys=this.openKeys.concat(),this.openKeys=[]):this.openKeys=this.cachedOpenKeys},$route:function(){this.updateMenu()}},methods:{onOpenChange:function(t){var e=this;if("horizontal"!==this.mode){var i=t.find((function(t){return!e.openKeys.includes(t)}));this.rootSubmenuKeys.includes(i)?this.openKeys=i?[i]:[]:this.openKeys=t}else this.openKeys=t},updateMenu:function(){var t=this.$route.matched.concat(),e=this.$route.meta.hidden;t.length>=3&&e?(t.pop(),this.selectedKeys=[t[t.length-1].path]):this.selectedKeys=[t.pop().path];var i=[];if("inline"===this.mode&&t.forEach((function(t){i.push(t.path)})),this.selectedKeys[0].includes(":")){var n=this.$route.fullPath;this.selectedKeys=[n];var a=[];this.fullOpenKeys(this.menu,n,a),a.length>0&&(i=a.reverse())}(!this.selectedKeys||this.selectedKeys[0].indexOf(":")<0)&&(this.collapsed?this.cachedOpenKeys=i:this.openKeys=i)},fullOpenKeys:function(t,e,i){var n,a=h(t);try{for(a.s();!(n=a.n()).done;){var s=n.value;if(s.path===e)return i.push(s.path),this.$emit("updateMenuTitle",s),!0;if(Array.isArray(s.children)&&this.fullOpenKeys(s.children,e,i))return i.push(s.path),!0}}catch(r){a.e(r)}finally{a.f()}},renderItem:function(t){return t.hidden?null:t.children&&!t.alwaysShow?this.renderSubMenu(t):this.renderMenuItem(t)},renderMenuItem:function(t){var e=this.$createElement,i=t.meta.target||null,n=i?"a":"router-link",s={to:{name:t.name}};t.route&&"0"===t.route&&(s={to:{path:t.path}});var r={href:t.path,target:t.meta.target};return t.children&&t.alwaysShow&&t.children.forEach((function(t){t.meta=Object.assign(t.meta,{hidden:!0})})),e(p,a()([{},{key:t.path}]),[e(n,{props:u({},s),attrs:u({},r)},[this.renderIcon(t.meta.icon),e("span",[t.meta.title])])])},renderSubMenu:function(t){var e=this,i=this.$createElement,n=[];return t.alwaysShow||t.children.forEach((function(t){return n.push(e.renderItem(t))})),i(m,a()([{},{key:t.path}]),[i("span",{slot:"title"},[this.renderIcon(t.meta.icon),i("span",[t.meta.title])]),n])},renderIcon:function(t){var e=this.$createElement;if("none"===t||void 0===t)return null;var i={};return"object"===o(t)?i.component=t:i.type=t,e(r["a"],{props:u({},i)})}},render:function(){var t=this,e=arguments[0],i=this.mode,n=this.theme,a=this.menu,r={mode:i,theme:n,openKeys:this.openKeys},o={select:function(e){t.selectedKeys=e.selectedKeys,t.$emit("select",e)},openChange:this.onOpenChange},l=a.map((function(e){return e.hidden?null:t.renderItem(e)}));return e(s["a"],{props:u({},r),on:u({},o),model:{value:t.selectedKeys,callback:function(e){t.selectedKeys=e}}},[l])}}},"9e65":function(t,e,i){},b2d9:function(t,e,i){},bd59:function(t,e,i){"use strict";var n=i("8c91"),a=i.n(n);a.a},c56c:function(t,e,i){},dac0:function(t,e,i){"use strict";var n=i("2c11"),a=i.n(n);a.a},e017:function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("router-view")],1)},a=[],s={name:"BlankLayout"},r=s,o=i("2877"),l=Object(o["a"])(r,n,a,!1,null,"9f5db0b4",null);e["default"]=l.exports},e23e:function(t,e,i){"use strict";var n=i("3c98"),a=i.n(n);a.a},e5f9:function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("a-layout-sider",{class:["sider",t.isDesktop()?null:"shadow",t.theme,t.fixSiderbar?"ant-fixed-sidemenu":null],attrs:{width:"208px",collapsible:t.collapsible,trigger:null},model:{value:t.collapsed,callback:function(e){t.collapsed=e},expression:"collapsed"}},[i("logo"),i("s-menu",{style:t.smenuStyle,attrs:{collapsed:t.collapsed,menu:t.menus,theme:t.theme,mode:t.mode},on:{select:t.onSelect,updateMenuTitle:t.onUpdateMenuTitle}})],1)},a=[],s=i("1fca"),r=i("a250"),o=i("955f"),l=i("ac0d"),u={name:"SideMenu",components:{ALayoutSider:s["a"],Logo:r["default"],SMenu:o["a"]},mixins:[l["a"],l["b"]],props:{mode:{type:String,required:!1,default:"inline"},theme:{type:String,required:!1,default:"dark"},collapsible:{type:Boolean,required:!1,default:!1},collapsed:{type:Boolean,required:!1,default:!1},menus:{type:Array,required:!0}},computed:{smenuStyle:function(){var t={padding:"0"};return this.fixSiderbar&&(t["height"]="calc(100% - 59px)",t["overflow"]="auto",t["overflow-x"]="hidden"),t}},methods:{onSelect:function(t){this.$emit("menuSelect",t)},onUpdateMenuTitle:function(t){this.$emit("updateMenuTitle",t)}}},c=u,h=(i("e23e"),i("f5ad"),i("2877")),f=Object(h["a"])(c,n,a,!1,null,"a67b5a52",null);e["default"]=f.exports},f1c2:function(t,e,i){"use strict";var n=i("c56c"),a=i.n(n);a.a},f5ad:function(t,e,i){"use strict";var n=i("9e65"),a=i.n(n);a.a}}]);