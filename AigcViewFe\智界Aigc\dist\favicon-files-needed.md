# Favicon文件说明

## 📁 需要放置的文件

请将以下文件放置到 `public/` 目录下：

### 必需文件
- `favicon.ico` - 主要的浏览器图标 (16x16px)
- `logo.png` - 主Logo文件 (364x364px) - **已提供**

### 推荐文件（可选）
- `favicon-16x16.png` - 16x16像素PNG图标
- `favicon-32x32.png` - 32x32像素PNG图标  
- `apple-touch-icon.png` - 苹果设备图标 (180x180px)

## 🔧 如何生成其他尺寸

如果只有 `favicon.ico` 和 `logo.png`，可以通过以下方式生成其他尺寸：

### 方法1：在线工具
- 访问 https://favicon.io/favicon-converter/
- 上传 logo.png 文件
- 下载生成的所有尺寸文件

### 方法2：使用图片编辑软件
- 使用Photoshop、GIMP等软件
- 将logo.png调整为对应尺寸
- 保存为PNG格式

## 📱 文件用途说明

| 文件名 | 尺寸 | 用途 |
|--------|------|------|
| favicon.ico | 16x16px | 浏览器标签页图标 |
| favicon-16x16.png | 16x16px | 现代浏览器PNG图标 |
| favicon-32x32.png | 32x32px | 高分辨率浏览器图标 |
| apple-touch-icon.png | 180x180px | iOS设备主屏幕图标 |
| logo.png | 364x364px | 主Logo，用于各种场景 |

## ✅ 当前状态

- ✅ `logo.png` - 已提供
- ❓ `favicon.ico` - 需要用户提供
- ❓ 其他尺寸文件 - 可选，建议生成

## 🚀 部署后效果

配置完成后，网站将支持：
- 浏览器标签页图标显示
- 收藏夹图标显示
- iOS设备添加到主屏幕时的图标
- PWA应用图标支持
