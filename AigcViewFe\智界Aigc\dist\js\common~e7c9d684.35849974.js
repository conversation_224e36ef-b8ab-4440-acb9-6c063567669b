(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~e7c9d684"],{"042c":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",[a("draggable",{staticStyle:{display:"inline-block"},attrs:{options:{animation:300}},on:{end:t.end},model:{value:t.dataSource,callback:function(e){t.dataSource=e},expression:"dataSource"}},[t._l(t.dataSource,(function(t,e){return[a("div",{key:e,staticStyle:{float:"left",width:"150px",height:"150px","margin-right":"10px",margin:"0 8px 8px 0"}},[a("div",{staticStyle:{width:"100%",height:"100%",position:"relative",padding:"8px",border:"1px solid #d9d9d9","border-radius":"4px"}},[a("img",{staticStyle:{width:"100%"},attrs:{src:t.filePath,preview:"index"}})])])]})),a("a-button",{staticStyle:{"margin-top":"115px"},attrs:{type:"primary"},on:{click:t.sureChange}},[t._v("确定")])],2),a("br"),a("a-row",[a("a-col",{attrs:{span:12}},[a("p",[t._v("拖拽前json数据：")]),a("textarea",{staticStyle:{width:"780px"},attrs:{rows:"25"}},[t._v(t._s(t.oldDateSource))])]),a("a-col",{attrs:{span:12}},[a("p",[t._v("拖拽后json数据：")]),a("textarea",{staticStyle:{width:"780px"},attrs:{rows:"25"}},[t._v(t._s(t.newDateSource))])])],1)],1)},i=[],o=a("b76a"),l=a.n(o),r=a("290c"),s=a("da05"),c={name:"ImgDragSort",components:{ACol:s["b"],ARow:r["a"],draggable:l.a},data:function(){return{description:"图片拖拽排序",spinning:!1,dataSource:[{id:"000",sort:0,filePath:"https://static.jeecg.com/upload/test/1_1588149743473.jpg"},{id:"111",sort:1,filePath:"https://static.jeecg.com/upload/test/u27356337152749454924fm27gp0_1588149731821.jpg"},{id:"222",sort:2,filePath:"https://static.jeecg.com/upload/test/u24454681402491956848fm27gp0_1588149712663.jpg"},{id:"333",sort:3,filePath:"https://static.jeecg.com/temp/国炬软件logo_1606575029126.png"},{id:"444",sort:4,filePath:"https://static.jeecg.com/upload/test/u8891206113801177793fm27gp0_1588149704459.jpg"}],oldDateSource:[],newDateSource:[]}},created:function(){this.oldDateSource=this.dataSource},methods:{end:function(t){},sureChange:function(){for(var t=0;t<this.dataSource.length;t++)this.dataSource[t].sort=t;this.newDateSource=this.dataSource}}},d=c,p=a("2877"),u=Object(p["a"])(d,n,i,!1,null,"05a15932",null);e["default"]=u.exports},"048b":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("j-vxe-table",{ref:"vTable",staticStyle:{"margin-top":"8px"},attrs:{toolbar:"","row-number":"","row-selection":"","drag-sort":"","keep-source":"",height:580,loading:t.loading,dataSource:t.dataSource,columns:t.columns},on:{valueChange:t.handleValueChange},scopedSlots:t._u([{key:"toolbarSuffix",fn:function(){return[a("a-button",{on:{click:t.handleTableCheck}},[t._v("表单验证")]),a("a-tooltip",{attrs:{placement:"top",title:"获取值，忽略表单验证",autoAdjustOverflow:!0}},[a("a-button",{on:{click:t.handleTableGet}},[t._v("获取值")])],1),a("a-tooltip",{attrs:{placement:"top",title:"模拟加载1000条数据",autoAdjustOverflow:!0}},[a("a-button",{on:{click:t.handleTableSet}},[t._v("设置值")])],1)]},proxy:!0},{key:"action",fn:function(e){return[a("a",{on:{click:function(a){return t.handleCK(e)}}},[t._v("查看")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-popconfirm",{attrs:{title:"确定删除吗？"},on:{confirm:function(a){return t.handleDL(e)}}},[a("a",[t._v("删除")])])]}}])})},i=[],o=a("c1df"),l=a.n(o),r=a("ca00"),s=a("2475"),c={name:"JVxeDemo1",data:function(){return{loading:!1,columns:[{title:"不可编辑",key:"normal",type:s["b"].normal,width:"180px",fixed:"left",defaultValue:"normal-new"},{title:"单行文本",key:"input",type:s["b"].input,width:"180px",defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"请输入${title}"},{pattern:/^[a-z|A-Z][a-z|A-Z\d_-]*$/,message:"${title}必须以字母开头，可包含数字、下划线、横杠"},{unique:!0,message:"${title}不能重复"},{handler:function(t,e,a){var n=t.cellValue;t.row,t.column;"abc"===n?e(!1,"${title}不能是abc"):e(!0)},message:"${title}默认提示"}]},{title:"多行文本",key:"textarea",type:s["b"].textarea,width:"200px"},{title:"数字",key:"number",type:s["b"].inputNumber,width:"80px",defaultValue:32,statistics:["sum","average"]},{title:"下拉框",key:"select",type:s["b"].select,width:"180px",options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}],allowInput:!0,placeholder:"请选择"},{title:"下拉框_字典",key:"select_dict",type:s["b"].select,width:"180px",options:[],dictCode:"sex",placeholder:"请选择"},{title:"下拉框_多选",key:"select_multiple",type:s["b"].selectMultiple,width:"205px",options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}],defaultValue:["int","boolean"],placeholder:"多选"},{title:"下拉框_搜索",key:"select_search",type:s["b"].selectSearch,width:"180px",options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}]},{title:"日期时间",key:"datetime",type:s["b"].datetime,width:"200px",defaultValue:"2019-4-30 14:52:22",placeholder:"请选择"},{title:"复选框",key:"checkbox",type:s["b"].checkbox,width:"100px",customValue:["Y","N"],defaultChecked:!1},{title:"操作",key:"action",type:s["b"].slot,fixed:"right",minWidth:"100px",align:"center",slotName:"action"}],dataSource:[]}},created:function(){this.randomPage(0,20,!0)},methods:{handleCK:function(t){this.$message.success("请在控制台查看输出")},handleDL:function(t){t.target.removeRows(t.row)},handleValueChange:function(t){},handleTableCheck:function(){var t=this;this.$refs.vTable.validateTable().then((function(e){e?t.$message.error("验证未通过，请在控制台查看详细"):t.$message.success("验证通过")}))},handleTableGet:function(){this.$refs.vTable.getTableData();this.$message.success("获取值成功，请看控制台输出")},handleTableSet:function(){this.randomPage(1,1e3,!0)},randomPage:function(t,e){var a=this,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];n&&(this.loading=!0);for(var i=function(){var t=Object(r["k"])(1e3,9999999999999);return l()(new Date(t)).format("YYYY-MM-DD HH:mm:ss")},o=(t-1)*e,s=["string","int","double","boolean"],c=Date.now(),d=[],p=0;p<e;p++)d.push({id:Object(r["m"])(),normal:"normal-".concat(o+p+1),input:"text-".concat(o+p+1),textarea:"textarea-".concat(o+p+1),number:Object(r["k"])(0,233),select:s[Object(r["k"])(0,3)],select_dict:Object(r["k"])(1,2).toString(),select_multiple:function(){for(var t=Object(r["k"])(1,4),e=[],a=0;a<t;a++)Object(r["j"])(e,s[Object(r["k"])(0,3)]);return e}(),select_search:s[Object(r["k"])(0,3)],datetime:i(),checkbox:["Y","N"][Object(r["k"])(0,1)]});this.dataSource=d;var u=Date.now(),h=u-c;n&&h<e&&setTimeout((function(){a.loading=!1}),e-h)}}},d=c,p=a("2877"),u=Object(p["a"])(d,n,i,!1,null,"30b1e721",null);e["default"]=u.exports},"0658":function(t,e,a){},"0663":function(t,e,a){"use strict";a.r(e);var n,i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"wrapper"},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.model,expression:"model"}],staticClass:"model"},[a("div",{staticClass:"model-show",on:{click:function(e){t.model=!1}}},[a("img",{attrs:{src:t.modelSrc,alt:""},on:{click:function(e){t.model=!1}}})])]),a("div",{staticClass:"content"},[a("div",{staticClass:"show-info"},[a("div",{staticClass:"test test1"},[a("vueCropper",{ref:"cropper",attrs:{img:t.option.img,outputSize:t.option.size,outputType:t.option.outputType,info:!0,full:t.option.full,canMove:t.option.canMove,canMoveBox:t.option.canMoveBox,fixedBox:t.option.fixedBox,original:t.option.original,autoCrop:t.option.autoCrop,autoCropWidth:t.option.autoCropWidth,autoCropHeight:t.option.autoCropHeight,centerBox:t.option.centerBox,high:t.option.high,infoTrue:t.option.infoTrue,maxImgSize:t.option.maxImgSize,enlarge:t.option.enlarge,mode:t.option.mode,limitMinSize:t.option.limitMinSize},on:{realTime:t.realTime,imgLoad:t.imgLoad,cropMoving:t.cropMoving}})],1),a("div",{staticClass:"test-button"},[a("button",{staticClass:"btn",on:{click:t.changeImg}},[t._v("changeImg")]),a("label",{staticClass:"btn",attrs:{for:"uploads"}},[t._v("upload")]),a("input",{ref:"uploadImg",staticStyle:{position:"absolute",clip:"rect(0 0 0 0)"},attrs:{type:"file",id:"uploads",accept:"image/png, image/jpeg, image/gif, image/jpg"},on:{change:function(e){return t.uploadImg(e,1)}}}),t.crap?a("button",{staticClass:"btn",on:{click:t.stopCrop}},[t._v("stop")]):a("button",{staticClass:"btn",on:{click:t.startCrop}},[t._v("start")]),a("button",{staticClass:"btn",on:{click:t.clearCrop}},[t._v("clear")]),a("button",{staticClass:"btn",on:{click:t.refreshCrop}},[t._v("refresh")]),a("button",{staticClass:"btn",on:{click:function(e){return t.changeScale(1)}}},[t._v("+")]),a("button",{staticClass:"btn",on:{click:function(e){return t.changeScale(-1)}}},[t._v("-")]),a("button",{staticClass:"btn",on:{click:t.rotateLeft}},[t._v("rotateLeft")]),a("button",{staticClass:"btn",on:{click:t.rotateRight}},[t._v("rotateRight")]),a("button",{staticClass:"btn",on:{click:function(e){return t.finish("base64")}}},[t._v("preview(base64)")]),a("button",{staticClass:"btn",on:{click:function(e){return t.finish("blob")}}},[t._v("preview(blob)")]),a("button",{staticClass:"btn",on:{click:function(){return t.option.img=""}}},[t._v("清除图片")]),a("a",{staticClass:"btn",on:{click:function(e){return t.down("base64")}}},[t._v("download(base64)")]),a("a",{staticClass:"btn",on:{click:function(e){return t.down("blob")}}},[t._v("download(blob)")]),a("a",{ref:"downloadDom",attrs:{href:t.downImg,download:"demo.png"}})]),a("div",{staticClass:"pre"},[a("section",{staticClass:"pre-item"},[a("p",[t._v("截图框大小")]),a("div",{staticClass:"show-preview",style:{width:t.previews.w+"px",height:t.previews.h+"px",overflow:"hidden",margin:"5px"}},[a("div",{style:t.previews.div},[a("img",{style:t.previews.img,attrs:{src:t.previews.url}})])])]),a("section",{staticClass:"pre-item"},[a("p",[t._v("中等大小")]),a("div",{style:t.previewStyle1},[a("div",{style:t.previews.div},[a("img",{style:t.previews.img,attrs:{src:t.previews.url}})])])]),a("section",{staticClass:"pre-item"},[a("p",[t._v("迷你大小")]),a("div",{style:t.previewStyle2},[a("div",{style:t.previews.div},[a("img",{style:t.previews.img,attrs:{src:t.previews.url}})])])]),a("section",{staticClass:"pre-item",attrs:{title:"zoom: (100 / previews.w)"}},[a("p",[t._v("固定为100宽度")]),a("div",{style:t.previewStyle3},[a("div",{style:t.previews.div},[a("img",{style:t.previews.img,attrs:{src:t.previews.url}})])])]),a("section",{staticClass:"pre-item",attrs:{title:"zoom: (100 / previews.h)"}},[a("p",[t._v("固定为100高度")]),a("div",{style:t.previewStyle4},[a("div",{style:t.previews.div},[a("img",{style:t.previews.img,attrs:{src:t.previews.url}})])])])]),a("div",{staticStyle:{display:"block",width:"100%"}},[a("label",{staticClass:"c-item"},[a("span",[t._v("图片默认渲染方式")]),a("select",{directives:[{name:"model",rawName:"v-model",value:t.option.mode,expression:"option.mode"}],on:{change:function(e){var a=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.option,"mode",e.target.multiple?a:a[0])}}},[a("option",{attrs:{value:"contain"}},[t._v("contain")]),a("option",{attrs:{value:"cover"}},[t._v("cover")]),a("option",{attrs:{value:"400px auto"}},[t._v("400px auto")]),a("option",{attrs:{value:"auto 400px"}},[t._v("auto 400px")]),a("option",{attrs:{value:"50%"}},[t._v("50%")]),a("option",{attrs:{value:"auto 50%"}},[t._v("auto 50%")])]),a("section",[t._v("\n              类似css background属性设置  设置不符合规范不生效， 参照文档说明\n            ")])]),a("label",{staticClass:"c-item"},[a("span",[t._v("上传时图片最大大小(默认会压缩尺寸到这个大小)")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.maxImgSize,expression:"option.maxImgSize"}],attrs:{type:"nubmer"},domProps:{value:t.option.maxImgSize},on:{input:function(e){e.target.composing||t.$set(t.option,"maxImgSize",e.target.value)}}})]),a("label",{staticClass:"c-item"},[a("span",[t._v("上传图片是否显示原始宽高 (针对大图 可以铺满)")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.original,expression:"option.original"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.option.original)?t._i(t.option.original,null)>-1:t.option.original},on:{change:function(e){var a=t.option.original,n=e.target,i=!!n.checked;if(Array.isArray(a)){var o=null,l=t._i(a,o);n.checked?l<0&&t.$set(t.option,"original",a.concat([o])):l>-1&&t.$set(t.option,"original",a.slice(0,l).concat(a.slice(l+1)))}else t.$set(t.option,"original",i)}}}),a("span",[t._v("original: "+t._s(t.option.original))])]),a("label",{staticClass:"c-item"},[a("span",[t._v("是否根据dpr生成适合屏幕的高清图片")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.high,expression:"option.high"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.option.high)?t._i(t.option.high,null)>-1:t.option.high},on:{change:function(e){var a=t.option.high,n=e.target,i=!!n.checked;if(Array.isArray(a)){var o=null,l=t._i(a,o);n.checked?l<0&&t.$set(t.option,"high",a.concat([o])):l>-1&&t.$set(t.option,"high",a.slice(0,l).concat(a.slice(l+1)))}else t.$set(t.option,"high",i)}}}),a("span",[t._v("high: "+t._s(t.option.high))])]),a("label",{staticClass:"c-item"},[a("span",[t._v("是否输出原图比例的截图")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.full,expression:"option.full"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.option.full)?t._i(t.option.full,null)>-1:t.option.full},on:{change:function(e){var a=t.option.full,n=e.target,i=!!n.checked;if(Array.isArray(a)){var o=null,l=t._i(a,o);n.checked?l<0&&t.$set(t.option,"full",a.concat([o])):l>-1&&t.$set(t.option,"full",a.slice(0,l).concat(a.slice(l+1)))}else t.$set(t.option,"full",i)}}}),a("span",[t._v("full: "+t._s(t.option.full))])]),a("label",{staticClass:"c-item"},[a("span",[t._v("截图信息展示是否是真实的输出宽高")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.infoTrue,expression:"option.infoTrue"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.option.infoTrue)?t._i(t.option.infoTrue,null)>-1:t.option.infoTrue},on:{change:function(e){var a=t.option.infoTrue,n=e.target,i=!!n.checked;if(Array.isArray(a)){var o=null,l=t._i(a,o);n.checked?l<0&&t.$set(t.option,"infoTrue",a.concat([o])):l>-1&&t.$set(t.option,"infoTrue",a.slice(0,l).concat(a.slice(l+1)))}else t.$set(t.option,"infoTrue",i)}}}),a("span",[t._v("infoTrue: "+t._s(t.option.infoTrue))])]),a("label",{staticClass:"c-item"},[a("span",[t._v("能否拖动图片")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.canMove,expression:"option.canMove"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.option.canMove)?t._i(t.option.canMove,null)>-1:t.option.canMove},on:{change:function(e){var a=t.option.canMove,n=e.target,i=!!n.checked;if(Array.isArray(a)){var o=null,l=t._i(a,o);n.checked?l<0&&t.$set(t.option,"canMove",a.concat([o])):l>-1&&t.$set(t.option,"canMove",a.slice(0,l).concat(a.slice(l+1)))}else t.$set(t.option,"canMove",i)}}}),a("span",[t._v("canMove: "+t._s(t.option.canMove))])]),a("label",{staticClass:"c-item"},[a("span",[t._v("能否拖动截图框")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.canMoveBox,expression:"option.canMoveBox"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.option.canMoveBox)?t._i(t.option.canMoveBox,null)>-1:t.option.canMoveBox},on:{change:function(e){var a=t.option.canMoveBox,n=e.target,i=!!n.checked;if(Array.isArray(a)){var o=null,l=t._i(a,o);n.checked?l<0&&t.$set(t.option,"canMoveBox",a.concat([o])):l>-1&&t.$set(t.option,"canMoveBox",a.slice(0,l).concat(a.slice(l+1)))}else t.$set(t.option,"canMoveBox",i)}}}),a("span",[t._v("canMoveBox: "+t._s(t.option.canMoveBox))])]),a("label",{staticClass:"c-item"},[a("span",[t._v("截图框固定大小")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.fixedBox,expression:"option.fixedBox"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.option.fixedBox)?t._i(t.option.fixedBox,null)>-1:t.option.fixedBox},on:{change:function(e){var a=t.option.fixedBox,n=e.target,i=!!n.checked;if(Array.isArray(a)){var o=null,l=t._i(a,o);n.checked?l<0&&t.$set(t.option,"fixedBox",a.concat([o])):l>-1&&t.$set(t.option,"fixedBox",a.slice(0,l).concat(a.slice(l+1)))}else t.$set(t.option,"fixedBox",i)}}}),a("span",[t._v("fixedBox: "+t._s(t.option.fixedBox))])]),a("label",{staticClass:"c-item"},[a("span",[t._v("是否自动生成截图框")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.autoCrop,expression:"option.autoCrop"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.option.autoCrop)?t._i(t.option.autoCrop,null)>-1:t.option.autoCrop},on:{change:function(e){var a=t.option.autoCrop,n=e.target,i=!!n.checked;if(Array.isArray(a)){var o=null,l=t._i(a,o);n.checked?l<0&&t.$set(t.option,"autoCrop",a.concat([o])):l>-1&&t.$set(t.option,"autoCrop",a.slice(0,l).concat(a.slice(l+1)))}else t.$set(t.option,"autoCrop",i)}}}),a("span",[t._v("autoCrop: "+t._s(t.option.autoCrop))])]),a("label",{staticClass:"c-item"},[a("span",[t._v("自动生成截图框的宽高")]),a("span",[t._v("宽度:  ")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.autoCropWidth,expression:"option.autoCropWidth"}],attrs:{type:"number"},domProps:{value:t.option.autoCropWidth},on:{input:function(e){e.target.composing||t.$set(t.option,"autoCropWidth",e.target.value)}}}),a("span",[t._v("高度:  ")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.autoCropHeight,expression:"option.autoCropHeight"}],attrs:{type:"number"},domProps:{value:t.option.autoCropHeight},on:{input:function(e){e.target.composing||t.$set(t.option,"autoCropHeight",e.target.value)}}})]),a("label",{staticClass:"c-item"},[a("span",[t._v("截图框是否限制在图片里(只有在自动生成截图框时才能生效)")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.centerBox,expression:"option.centerBox"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.option.centerBox)?t._i(t.option.centerBox,null)>-1:t.option.centerBox},on:{change:function(e){var a=t.option.centerBox,n=e.target,i=!!n.checked;if(Array.isArray(a)){var o=null,l=t._i(a,o);n.checked?l<0&&t.$set(t.option,"centerBox",a.concat([o])):l>-1&&t.$set(t.option,"centerBox",a.slice(0,l).concat(a.slice(l+1)))}else t.$set(t.option,"centerBox",i)}}}),a("span",[t._v("centerBox: "+t._s(t.option.centerBox))])]),a("label",{staticClass:"c-item"},[a("span",[t._v("是否按照截图框比例输出 默认为1 ")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.enlarge,expression:"option.enlarge"}],attrs:{type:"number"},domProps:{value:t.option.enlarge},on:{input:function(e){e.target.composing||t.$set(t.option,"enlarge",e.target.value)}}})]),a("p",[t._v("输出图片格式")]),a("label",{staticClass:"c-item"},[a("label",[t._v("jpg  "),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.outputType,expression:"option.outputType"}],attrs:{type:"radio",name:"type",value:"jpeg"},domProps:{checked:t._q(t.option.outputType,"jpeg")},on:{change:function(e){return t.$set(t.option,"outputType","jpeg")}}})]),a("label",[t._v("png  "),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.outputType,expression:"option.outputType"}],attrs:{type:"radio",name:"type",value:"png"},domProps:{checked:t._q(t.option.outputType,"png")},on:{change:function(e){return t.$set(t.option,"outputType","png")}}})]),a("label",[t._v("webp "),a("input",{directives:[{name:"model",rawName:"v-model",value:t.option.outputType,expression:"option.outputType"}],attrs:{type:"radio",name:"type",value:"webp"},domProps:{checked:t._q(t.option.outputType,"webp")},on:{change:function(e){return t.$set(t.option,"outputType","webp")}}})])])])])])])},o=[],l=a("7e79");function r(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function s(t){return s="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}var c=(n={name:"ImagCropper",components:{VueCropper:l["VueCropper"]},data:function(){return{model:!1,modelSrc:"",crap:!1,previews:{},lists:[{img:"https://avatars2.githubusercontent.com/u/15681693?s=460&v=4"},{img:"http://cdn.xyxiao.cn/Landscape_1.jpg"},{img:"http://cdn.xyxiao.cn/Landscape_2.jpg"},{img:"http://cdn.xyxiao.cn/Landscape_3.jpg"},{img:"http://cdn.xyxiao.cn/Landscape_4.jpg"},{img:"http://cdn.xyxiao.cn/Portrait_1.jpg"},{img:"http://cdn.xyxiao.cn/Portrait_2.jpg"}],option:{img:"",size:1,full:!1,outputType:"png",canMove:!0,fixedBox:!1,original:!1,canMoveBox:!0,autoCrop:!0,autoCropWidth:200,autoCropHeight:150,centerBox:!1,high:!1,cropData:{},enlarge:1,mode:"contain",maxImgSize:3e3,limitMinSize:[100,120]},example2:{img:"http://cdn.xyxiao.cn/Landscape_2.jpg",info:!0,size:1,outputType:"jpeg",canScale:!0,autoCrop:!0,autoCropWidth:300,autoCropHeight:250,fixed:!0,infoTrue:!0,fixedNumber:[4,3]},example3:{img:"http://cdn.xyxiao.cn/Landscape_1.jpg",autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0},downImg:"#",previewStyle1:{},previewStyle2:{},previewStyle3:{},previewStyle4:{},code0:"",code1:"",code2:"",code3:"",preview3:""}},methods:{changeImg:function(){this.option.img=this.lists[~~(Math.random()*this.lists.length)].img},startCrop:function(){this.crap=!0,this.$refs.cropper.startCrop()},stopCrop:function(){this.crap=!1,this.$refs.cropper.stopCrop()},clearCrop:function(){this.$refs.cropper.clearCrop()},refreshCrop:function(){this.$refs.cropper.refresh()},changeScale:function(t){t=t||1,this.$refs.cropper.changeScale(t)},rotateLeft:function(){this.$refs.cropper.rotateLeft()},rotateRight:function(){this.$refs.cropper.rotateRight()},finish:function(t){var e=this;"blob"===t?this.$refs.cropper.getCropBlob((function(t){var a=window.URL.createObjectURL(t);e.model=!0,e.modelSrc=a})):this.$refs.cropper.getCropData((function(t){e.model=!0,e.modelSrc=t}))},realTime:function(t){var e=t,a=.5,n=.2;this.previewStyle1={width:e.w+"px",height:e.h+"px",overflow:"hidden",margin:"0",zoom:a},this.previewStyle2={width:e.w+"px",height:e.h+"px",overflow:"hidden",margin:"0",zoom:n},this.previewStyle3={width:e.w+"px",height:e.h+"px",overflow:"hidden",margin:"0",zoom:100/e.w},this.previewStyle4={width:e.w+"px",height:e.h+"px",overflow:"hidden",margin:"0",zoom:100/e.h},this.previews=t},finish2:function(t){var e=this;this.$refs.cropper2.getCropData((function(t){e.model=!0,e.modelSrc=t}))},finish3:function(t){var e=this;this.$refs.cropper3.getCropData((function(t){e.model=!0,e.modelSrc=t}))},down:function(t){var e=this;"blob"===t?this.$refs.cropper.getCropBlob((function(t){if(e.downImg=window.URL.createObjectURL(t),window.navigator.msSaveBlob){var a=new Blob([t]);window.navigator.msSaveBlob(a,"demo.png")}else e.$nextTick((function(){e.$refs.downloadDom.click()}))})):this.$refs.cropper.getCropData((function(t){if(e.downImg=t,window.navigator.msSaveBlob){var a=new Blob([t]);window.navigator.msSaveBlob(a,"demo.png")}else e.$nextTick((function(){e.$refs.downloadDom.click()}))}))},uploadImg:function(t,e){var a=this,n=t.target.files[0];if(!/\.(gif|jpg|jpeg|png|bmp|GIF|JPG|PNG)$/.test(t.target.value))return alert("图片类型必须是.gif,jpeg,jpg,png,bmp中的一种"),!1;var i=new FileReader;i.onload=function(t){var n;n="object"===s(t.target.result)?window.URL.createObjectURL(new Blob([t.target.result])):t.target.result,1===e?a.option.img=n:2===e&&(a.example2.img=n),a.$refs.uploadImg.value=""},i.readAsArrayBuffer(n)},imgLoad:function(t){},cropMoving:function(t){this.option.cropData=t}}},r(n,"components",{VueCropper:l["VueCropper"]}),r(n,"mounted",(function(){this.changeImg();var t=[].slice.call(document.querySelectorAll("pre code"));t.forEach((function(t,e){hljs.highlightBlock(t)}))})),n),d=c,p=(a("19a3"),a("2877")),u=Object(p["a"])(d,i,o,!1,null,"2095ef4a",null);e["default"]=u.exports},1073:function(t,e,a){},"13c7":function(t,e,a){"use strict";var n=a("2dc6"),i=a.n(n);i.a},"15dc":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchQuery(e)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户名"}},[a("j-input",{attrs:{placeholder:"请输入名称模糊查询"},model:{value:t.queryParam.name,callback:function(e){t.$set(t.queryParam,"name",e)},expression:"queryParam.name"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"年龄"}},[a("a-input",{staticStyle:{width:"calc(50% - 15px)"},attrs:{placeholder:"最小年龄",type:"ge"},model:{value:t.queryParam.age_begin,callback:function(e){t.$set(t.queryParam,"age_begin",e)},expression:"queryParam.age_begin"}}),a("span",{staticClass:"group-query-strig"},[t._v("~")]),a("a-input",{staticStyle:{width:"calc(50% - 15px)"},attrs:{placeholder:"最大年龄",type:"le"},model:{value:t.queryParam.age_end,callback:function(e){t.$set(t.queryParam,"age_end",e)},expression:"queryParam.age_end"}})],1)],1),t.toggleSearchStatus?[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"生日"}},[a("a-range-picker",{attrs:{format:"YYYY-MM-DD",placeholder:["开始时间","结束时间"]},on:{change:t.onBirthdayChange},model:{value:t.queryParam.birthdayRange,callback:function(e){t.$set(t.queryParam,"birthdayRange",e)},expression:"queryParam.birthdayRange"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"性别"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择性别",dictCode:"sex"},model:{value:t.queryParam.sex,callback:function(e){t.$set(t.queryParam,"sex",e)},expression:"queryParam.sex"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"选择用户"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择用户",dictCode:"demo,name,id"},model:{value:t.queryParam.id,callback:function(e){t.$set(t.queryParam,"id",e)},expression:"queryParam.id"}})],1)],1)]:t._e(),a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:t.searchQuery}},[t._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:t.searchReset}},[t._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:t.handleToggleSearch}},[t._v("\n              "+t._s(t.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:t.toggleSearchStatus?"up":"down"}})],1)],1)],1)],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:t.handleAdd}},[t._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:t.jump}},[t._v("创建单据")]),a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:t.onetomany}},[t._v("一对多")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(e){return t.handleExportXls("单表示例")}}},[t._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:t.tokenHeader,action:t.importExcelUrl},on:{change:t.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[t._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:t.fieldList},on:{handleSuperQuery:t.handleSuperQuery}}),t.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:t.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),t._v("\n          删除\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[t._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):t._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),t._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[t._v(t._s(t.selectedRowKeys.length))]),t._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:t.onClearSelected}},[t._v("清空")]),a("span",{staticStyle:{float:"right"}},[a("a",{on:{click:function(e){return t.loadData()}}},[a("a-icon",{attrs:{type:"sync"}}),t._v("刷新")],1),a("a-divider",{attrs:{type:"vertical"}}),a("a-popover",{attrs:{title:"自定义列",trigger:"click",placement:"leftBottom"}},[a("template",{slot:"content"},[a("a-checkbox-group",{attrs:{defaultValue:t.settingColumns},on:{change:t.onColSettingsChange},model:{value:t.settingColumns,callback:function(e){t.settingColumns=e},expression:"settingColumns"}},[a("a-row",{staticStyle:{width:"400px"}},[t._l(t.defColumns,(function(e,n){return["rowIndex"!=e.key&&"action"!=e.dataIndex?[a("a-col",{attrs:{span:12}},[a("a-checkbox",{attrs:{value:e.dataIndex}},[a("j-ellipsis",{attrs:{value:e.title,length:10}})],1)],1)]:t._e()]}))],2)],1)],1),a("a",[a("a-icon",{attrs:{type:"setting"}}),t._v("设置")],1)],2)],1)]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:t.columns,dataSource:t.dataSource,pagination:t.ipagination,loading:t.loading,rowSelection:{selectedRowKeys:t.selectedRowKeys,onChange:t.onSelectChange}},on:{change:t.handleTableChange},scopedSlots:t._u([{key:"action",fn:function(e,n){return a("span",{},[a("a",{on:{click:function(e){return t.handleEdit(n)}}},[t._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[t._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return t.handleDelete(n.id)}}},[a("a",[t._v("删除")])])],1)],1)],1)],1)}}])},[a("div",{attrs:{slot:"filterDropdown"},slot:"filterDropdown"},[a("a-card",[a("a-checkbox-group",{attrs:{defaultValue:t.settingColumns},on:{change:t.onColSettingsChange},model:{value:t.settingColumns,callback:function(e){t.settingColumns=e},expression:"settingColumns"}},[a("a-row",{staticStyle:{width:"400px"}},[t._l(t.defColumns,(function(e,n){return["rowIndex"!=e.key&&"action"!=e.dataIndex?[a("a-col",{attrs:{span:12}},[a("a-checkbox",{attrs:{value:e.dataIndex}},[a("j-ellipsis",{attrs:{value:e.title,length:10}})],1)],1)]:t._e()]}))],2)],1)],1)],1),a("a-icon",{style:{fontSize:"16px",color:"#108ee9"},attrs:{slot:"filterIcon",type:"setting"},slot:"filterIcon"})],1)],1),a("jeecgDemo-modal",{ref:"modalForm",on:{ok:t.modalFormOk}}),a("JeecgDemoTabsModal",{ref:"jeecgDemoTabsModal",on:{ok:t.modalFormOk}})],1)},i=[],o=a("c943"),l=a("8c6e"),r=a("4349"),s=a("3335"),c=a("89f2"),d=a("b65a"),p=a("2b0e"),u=a("ca00"),h=[{type:"string",value:"name",text:"用户名"},{type:"int",value:"age",text:"年龄"},{type:"date",value:"birthday",text:"生日"}],g={name:"JeecgDemoList",mixins:[d["a"]],components:{JeecgDemoModal:o["default"],JSuperQuery:l["default"],JeecgDemoTabsModal:s["default"],JInput:r["default"]},data:function(){return{description:"单表示例列表",sexDictOptions:[],importExcelUrl:"".concat(window._CONFIG["domianURL"],"/test/jeecgDemo/importExcel"),columns:[],settingColumns:[],defColumns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(t,e,a){return parseInt(a)+1}},{title:"姓名",align:"center",dataIndex:"name"},{title:"关键词",align:"center",dataIndex:"keyWord"},{title:"打卡时间",align:"center",dataIndex:"punchTime"},{title:"性别",align:"center",dataIndex:"sex",customRender:function(t){return Object(c["b"])("sex",t)}},{title:"年龄",align:"center",dataIndex:"age"},{title:"生日",align:"center",dataIndex:"birthday"},{title:"邮箱",align:"center",dataIndex:"email"},{title:"个人简介",align:"center",dataIndex:"content"},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{filterDropdown:"filterDropdown",filterIcon:"filterIcon",customRender:"action"}}],url:{list:"/test/jeecgDemo/list",delete:"/test/jeecgDemo/delete",deleteBatch:"/test/jeecgDemo/deleteBatch",exportXlsUrl:"/test/jeecgDemo/exportXls"},fieldList:h}},methods:{getQueryParams:function(){var t={};this.superQueryParams&&(t["superQueryParams"]=encodeURI(this.superQueryParams),t["superQueryMatchType"]=this.superQueryMatchType);var e=Object.assign(t,this.queryParam,this.isorter,this.filters);return e.field=this.getQueryField(),e.pageNo=this.ipagination.current,e.pageSize=this.ipagination.pageSize,delete e.birthdayRange,Object(u["d"])(e)},initDictConfig:function(){var t=this;Object(c["d"])("sex").then((function(e){e.success&&(t.sexDictOptions=e.result)}))},onetomany:function(){this.$refs.jeecgDemoTabsModal.add(),this.$refs.jeecgDemoTabsModal.title="编辑"},jump:function(){this.$router.push({path:"/jeecg/helloworld"})},onBirthdayChange:function(t,e){this.queryParam.birthday_begin=e[0],this.queryParam.birthday_end=e[1]},onColSettingsChange:function(t){var e=this,a=this.$route.name+":colsettings";p["default"].ls.set(a,t,6048e5),this.settingColumns=t;var n=this.defColumns.filter((function(t){return"rowIndex"==t.key||"action"==t.dataIndex||!!e.settingColumns.includes(t.dataIndex)}));this.columns=n},initColumns:function(){var t=this.$route.name+":colsettings",e=p["default"].ls.get(t);if(null==e||void 0==e){var a=[];this.defColumns.forEach((function(t,e,n){a.push(t.dataIndex)})),this.settingColumns=a,this.columns=this.defColumns}else{this.settingColumns=e;var n=this.defColumns.filter((function(t){return"rowIndex"==t.key||"action"==t.dataIndex||!!e.includes(t.dataIndex)}));this.columns=n}}},created:function(){this.initColumns()}},m=g,b=(a("13c7"),a("2877")),f=Object(b["a"])(m,n,i,!1,null,"01c81c68",null);e["default"]=f.exports},"19a3":function(t,e,a){"use strict";var n=a("7a291"),i=a.n(n);i.a},"19c0":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("a-card",{attrs:{bordered:!1}},[n("a-col",{staticClass:"clName",attrs:{span:4}},[n("a-tree",{staticStyle:{height:"500px","overflow-y":"auto"},attrs:{treeData:t.treeData,defaultExpandAll:t.defaultExpandAll},on:{select:this.onSelect}})],1),n("a-col",{attrs:{span:2}}),n("a-col",{attrs:{span:18}},[n("a-spin",{attrs:{tip:"Loading...",spinning:t.spinning}},t._l(t.dataSource,(function(e,i){return n("div",{key:i},[n("a-row",[n("a-col",{attrs:{span:24}},[n("p",[n("a-divider",{attrs:{orientation:"left"}},[t._v(t._s(e.fileName))])],1)]),n("a-col",{attrs:{span:24}},[e.filePdfPath?[n("div",{staticStyle:{float:"left",width:"104px",height:"104px","margin-right":"10px",margin:"0 8px 8px 0"}},[n("div",{staticStyle:{width:"100%",height:"100%",position:"relative",padding:"8px"},on:{click:function(a){return t.pdfPreview(e.title)}}},[n("img",{staticStyle:{width:"100%"},attrs:{src:a("c6cf8")}})])])]:[t._v('\n              (暂无材料，点击"选择文件"或"扫描上传"上传文件)\n            ')]],2)],1)],1)})),0)],1),n("pdf-preview-modal",{ref:"pdfmodal"})],1)},i=[],o=a("0fea"),l=a("9fb0"),r=a("2b0e"),s=a("588f"),c=[{id:"1",key:"1",title:"实例.pdf",fileCode:"shili",fileName:"实例",filePdfPath:"实例"}],d={name:"JeecgPdfView",components:{PdfPreviewModal:s["default"]},data:function(){return{description:"PDF预览页面",treeData:[{title:"所有PDF电子档",key:"0-0",children:c}],dataSource:c,allData:c,defaultExpandAll:!0,spinning:!1,url:{pdfList:"/mock/api/pdfList"}}},created:function(){},methods:{loadData:function(){var t=this;this.spinning=!1,Object(o["c"])(this.url.pdfList).then((function(e){e.length>0&&(t.allData=e,t.dataSource=e,t.treeData[0].children=e),t.spinning=!1}))},pdfPreview:function(t){var e=r["default"].ls.get(l["a"]);this.headers={"X-Access-Token":e},this.$refs.pdfmodal.previewFiles(t,e)},onSelect:function(t,e){this.dataSource=[],void 0===t[0]||"0-0"===t[0]?this.dataSource=this.allData:this.dataSource.push(e.node._props.dataRef)},modalFormOk:function(){this.loadData()}}},p=d,u=(a("341b"),a("2877")),h=Object(u["a"])(p,n,i,!1,null,"0c5d87b0",null);e["default"]=h.exports},"1b51":function(t,e,a){"use strict";var n=a("1073"),i=a.n(n);i.a},"1b7f":function(t,e,a){},"1bc32":function(t,e,a){"use strict";var n=a("1b7f"),i=a.n(n);i.a},"2dc6":function(t,e,a){},"327c":function(t,e,a){"use strict";var n=a("0658"),i=a.n(n);i.a},"341b":function(t,e,a){"use strict";var n=a("a9cc"),i=a.n(n);i.a},3826:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-table",{ref:"table",attrs:{size:"default",bordered:"",rowKey:"id",columns:t.columns,pagination:!1,dataSource:t.dataSource}})],1)},i=[],o={name:"RowspanTable",components:{},data:function(){var t=this;return{description:"存放位置设置表管理页面",levelNum:{},gridNum:0,boxNum:0,cabinetNo:"",columns:[{title:"分组一",align:"center",dataIndex:"cabinetNo",customRender:function(e,a,n){var i={children:e,attrs:{}};return i.attrs.rowSpan=0===n?t.dataSource.length:0,i}},{title:"分组二",align:"center",dataIndex:"levelNo",customRender:function(e,a,n){for(var i={children:e,attrs:{}},o=parseInt(t.levelNum),l=parseInt(t.gridNum)*parseInt(t.boxNum),r=0;r<=o;r++){if(n===r*l){i.attrs.rowSpan=l;break}i.attrs.rowSpan=0}return i}},{title:"分组三",align:"center",dataIndex:"gridNo",customRender:function(e,a,n){for(var i={children:e,attrs:{}},o=parseInt(t.levelNum)*parseInt(t.gridNum),l=parseInt(t.boxNum),r=0;r<=o;r++){if(n===r*l){i.attrs.rowSpan=l;break}i.attrs.rowSpan=0}return i}},{title:"字段一",align:"center",dataIndex:"boxNo"},{title:"字段二",align:"center",dataIndex:"storedNum"},{title:"字段三",align:"center",dataIndex:"maxNum"}],dataSource:[{id:"cb1dfd12cbeca3f8ba121439ee7e2411",attributeId:"e62831f314e1390edbd884e9d9e6aca6",cabinetNo:"1",levelNo:"1",gridNo:"1",boxNo:"1",storedNum:2,maxNum:2,unitNum:2,assignStatus:"1",storageStatus:"1",remark:null,createBy:"admin",createTime:"2019-04-02",updateBy:"admin",updateTime:"2019-04-02"},{id:"f903d50d02904b14175dccf2a7948777",attributeId:"e62831f314e1390edbd884e9d9e6aca6",cabinetNo:"1",levelNo:"1",gridNo:"1",boxNo:"2",storedNum:2,maxNum:2,unitNum:2,assignStatus:"1",storageStatus:"1",remark:null,createBy:"admin",createTime:"2019-04-02",updateBy:"admin",updateTime:"2019-04-02"},{id:"4f04c0ca4202535d678871b07e706cf6",attributeId:"e62831f314e1390edbd884e9d9e6aca6",cabinetNo:"1",levelNo:"1",gridNo:"2",boxNo:"1",storedNum:2,maxNum:2,unitNum:2,assignStatus:"1",storageStatus:"1",remark:null,createBy:"admin",createTime:"2019-04-02",updateBy:"admin",updateTime:"2019-04-02"},{id:"d0c91dabedfc03efad0126e50ea72e80",attributeId:"e62831f314e1390edbd884e9d9e6aca6",cabinetNo:"1",levelNo:"1",gridNo:"2",boxNo:"2",storedNum:2,maxNum:2,unitNum:2,assignStatus:"1",storageStatus:"1",remark:null,createBy:"admin",createTime:"2019-04-02",updateBy:"admin",updateTime:"2019-04-08"},{id:"1e8bfcbe4352afbab8878f9fd368e007",attributeId:"e62831f314e1390edbd884e9d9e6aca6",cabinetNo:"1",levelNo:"2",gridNo:"1",boxNo:"1",storedNum:1,maxNum:2,unitNum:1,assignStatus:"1",storageStatus:"0",remark:null,createBy:"admin",createTime:"2019-04-02",updateBy:"admin",updateTime:"2019-04-08"},{id:"d76087d8d3ebc7a59d43458588f26941",attributeId:"e62831f314e1390edbd884e9d9e6aca6",cabinetNo:"1",levelNo:"2",gridNo:"1",boxNo:"2",storedNum:0,maxNum:2,unitNum:0,assignStatus:"1",storageStatus:"0",remark:null,createBy:"admin",createTime:"2019-04-02",updateBy:"admin",updateTime:"2019-04-02"},{id:"7bf7754f12e1bf95edcd501cc6b85e62",attributeId:"e62831f314e1390edbd884e9d9e6aca6",cabinetNo:"1",levelNo:"2",gridNo:"2",boxNo:"1",storedNum:0,maxNum:2,unitNum:0,assignStatus:"1",storageStatus:"0",remark:null,createBy:"admin",createTime:"2019-04-02",updateBy:"admin",updateTime:"2019-04-02"},{id:"9cd08d733657d5b286bec870f12f6ecf",attributeId:"e62831f314e1390edbd884e9d9e6aca6",cabinetNo:"1",levelNo:"2",gridNo:"2",boxNo:"2",storedNum:0,maxNum:2,unitNum:0,assignStatus:"1",storageStatus:"0",remark:null,createBy:"admin",createTime:"2019-04-02",updateBy:"admin",updateTime:"2019-04-02"}],isorter:{column:"createTime",order:"desc"},url:{}}},created:function(){this.loadData()},methods:{loadData:function(){this.levelNum=4,this.gridNum=2,this.boxNum=2}}},l=o,r=(a("1b51"),a("2877")),s=Object(r["a"])(l,n,i,!1,null,"cb47b3be",null);e["default"]=s.exports},"42c7":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-row",{attrs:{gutter:8}},[a("a-col",{attrs:{span:12}},[a("j-vxe-table",{staticStyle:{"margin-bottom":"8px"},attrs:{toolbar:"","row-number":"","row-selection":"","click-select-row":"","highlight-current-row":"","radio-config":{highlight:!1},"checkbox-config":{highlight:!1},height:357,loading:t.table1.loading,columns:t.table1.columns,dataSource:t.table1.dataSource,pagination:t.table1.pagination},on:{pageChange:t.handleTable1PageChange,selectRowChange:t.handleTable1SelectRowChange}}),a("j-vxe-table",{attrs:{toolbar:"","row-number":"","row-selection":"","click-select-row":"",height:356,loading:t.table2.loading,columns:t.table2.columns,dataSource:t.table2.dataSource,pagination:t.table2.pagination},on:{pageChange:t.handleTable2PageChange}})],1),a("a-col",{attrs:{span:12}},[a("j-vxe-table",{staticStyle:{"margin-top":"40px"},attrs:{"row-number":"",height:800,columns:t.table1.columns,dataSource:t.table1.selectedRows}})],1)],1)],1)},i=[],o=a("0fea"),l=a("2475");function r(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var s={name:"Template3",components:{},data:function(){return{table1:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0},lastRow:null,selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:l["b"].input},{key:"call",title:"呼叫",width:"80px",type:l["b"].input},{key:"len",title:"长",width:"80px",type:l["b"].input},{key:"ton",title:"吨",width:"120px",type:l["b"].input},{key:"payer",title:"付款方",width:"120px",type:l["b"].input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",width:"180px",type:l["b"].input},{key:"trend",title:"动向",width:"120px",type:l["b"].input}]},table2:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:l["b"].input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:l["b"].input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:l["b"].input},{key:"type",title:"船舶分类",width:"120px",type:l["b"].input},{key:"port_area",title:"所属港区",width:"120px",type:l["b"].input}]},url:{getData:"/mock/vxe/getData"}}},watch:r({},"table1.lastRow",(function(t){this.loadTable2Data()})),created:function(){this.loadTable1Data()},methods:{loadTable1Data:function(){var t=this,e={pageNo:this.table1.pagination.current,pageSize:this.table1.pagination.pageSize};this.table1.loading=!0,Object(o["c"])(this.url.getData,e).then((function(e){e.success?(t.table1.pagination.total=e.result.total,t.table1.dataSource=e.result.records):t.$error({title:"主表查询失败",content:e.message})})).finally((function(){t.table1.loading=!1}))},loadTable2Data:function(){var t=this,e=this.table1.selectedRows;if(!e||0===e.length)return this.table2.pagination.total=0,void(this.table2.dataSource=[]);null==this.table1.lastRow&&(this.table1.lastRow=e[e.length-1]);var a={parentId:this.table1.lastRow.id,pageNo:this.table2.pagination.current,pageSize:this.table2.pagination.pageSize};this.table2.loading=!0,Object(o["c"])(this.url.getData,a).then((function(e){e.success?(t.table2.pagination.total=e.result.total,t.table2.dataSource=e.result.records):t.$error({title:"子表查询失败",content:e.message})})).finally((function(){t.table2.loading=!1}))},handleTable1PageChange:function(t){this.table1.pagination.current=t.current,this.table1.pagination.pageSize=t.pageSize,this.loadTable1Data(),this.table1.selectedRows=[],this.loadTable2Data()},handleTable2PageChange:function(t){this.table1.pagination.current=t.current,this.table1.pagination.pageSize=t.pageSize,this.loadTable2Data()},handleTable1SelectRowChange:function(t){this.handleTableSelectRowChange(this.table1,t)},handleTableSelectRowChange:function(t,e){var a=e.row,n=e.action,i=e.selectedRows,o=e.$table,l=i[i.length-1];"selected"===n?t.lastRow=a:"selected-all"===n?0===i.length?t.lastRow=null:t.lastRow||(t.lastRow=l):"unselected"===n&&a===t.lastRow&&(t.lastRow=l),o.setCurrentRow(t.lastRow),t.selectedRows=i}}},c=s,d=a("2877"),p=Object(d["a"])(c,n,i,!1,null,"6271126f",null);e["default"]=p.exports},"46a6":function(t,e,a){"use strict";a.r(e);var n,i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-row",{attrs:{gutter:8}},[a("a-col",{staticStyle:{"margin-bottom":"4px"},attrs:{span:24}},[a("j-vxe-table",{attrs:{toolbar:"","row-number":"","row-selection":"","click-select-row":"","highlight-current-row":"","radio-config":{highlight:!1},"checkbox-config":{highlight:!1},height:340,loading:t.table1.loading,columns:t.table1.columns,dataSource:t.table1.dataSource,pagination:t.table1.pagination},on:{pageChange:t.handleTable1PageChange,selectRowChange:t.handleTable1SelectRowChange}})],1),a("a-col",{attrs:{span:12}},[a("j-vxe-table",{attrs:{toolbar:"","row-number":"","row-selection":"","click-select-row":"","highlight-current-row":"","radio-config":{highlight:!1},"checkbox-config":{highlight:!1},height:340,loading:t.table2.loading,columns:t.table2.columns,dataSource:t.table2.dataSource,pagination:t.table2.pagination},on:{pageChange:t.handleTable2PageChange,selectRowChange:t.handleTable2SelectRowChange}})],1),a("a-col",{attrs:{span:12}},[a("j-vxe-table",{attrs:{toolbar:"","row-number":"","row-selection":"",height:340,loading:t.table3.loading,columns:t.table3.columns,dataSource:t.table3.dataSource,pagination:t.table3.pagination},on:{pageChange:t.handleTable3PageChange}})],1)],1)],1)},o=[],l=a("0fea"),r=a("2475");function s(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var c={name:"Template1",data:function(){return{table1:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0},lastRow:null,selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:r["b"].input,formatter:function(t){var e=t.cellValue,a=t.row,n=(t.column,"");return"佧伒侾佯有限公司"===a.company&&(n+="-233"),e+n}},{key:"call",title:"呼叫",width:"80px",type:r["b"].input},{key:"len",title:"长",width:"80px",type:r["b"].inputNumber},{key:"ton",title:"吨",width:"120px",type:r["b"].inputNumber},{key:"payer",title:"付款方",width:"120px",type:r["b"].input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:r["b"].input},{key:"trend",title:"动向",width:"120px",type:r["b"].input}]},table2:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},lastRow:null,selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:r["b"].input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:r["b"].input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:r["b"].input},{key:"type",title:"船舶分类",width:"120px",type:r["b"].input},{key:"port_area",title:"所属港区",width:"120px",type:r["b"].input}]},table3:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"120px",type:r["b"].input},{key:"power",title:"马力",width:"120px",type:r["b"].input},{key:"nature",title:"性质",width:"120px",type:r["b"].input},{key:"departure_time",title:"发船时间",width:"180px",type:r["b"].input}]},url:{getData:"/mock/vxe/getData"}}},watch:(n={},s(n,"table1.lastRow",(function(t){this.loadTable2Data()})),s(n,"table2.lastRow",(function(){this.loadTable3Data()})),n),created:function(){this.loadTable1Data()},methods:{loadTable1Data:function(){var t=this,e={pageNo:this.table1.pagination.current,pageSize:this.table1.pagination.pageSize};this.table1.loading=!0,Object(l["c"])(this.url.getData,e).then((function(e){e.success?(t.table1.pagination.total=e.result.total,t.table1.dataSource=e.result.records,t.table1.selectedRows=[]):t.$error({title:"主表查询失败",content:e.message})})).finally((function(){t.table1.loading=!1}))},handleTable1PageChange:function(t){this.table1.pagination.current=t.current,this.table1.pagination.pageSize=t.pageSize,this.loadTable1Data()},handleTable1SelectRowChange:function(t){this.handleTableSelectRowChange(this.table1,t)},loadTable2Data:function(){var t=this,e=this.table1.selectedRows;if(!e||0===e.length)return this.table2.pagination.total=0,this.table2.dataSource=[],void(this.table2.selectedRows=[]);null==this.table1.lastRow&&(this.table1.lastRow=e[e.length-1]);var a={parentId:this.table1.lastRow.id,pageNo:this.table2.pagination.current,pageSize:this.table2.pagination.pageSize};this.table2.loading=!0,Object(l["c"])(this.url.getData,a).then((function(e){e.success?(t.table2.pagination.total=e.result.total,t.table2.dataSource=e.result.records,t.table2.selectedRows=[]):t.$error({title:"子表查询失败",content:e.message})})).finally((function(){t.table2.loading=!1}))},handleTable2SelectRowChange:function(t){this.handleTableSelectRowChange(this.table2,t)},handleTable2PageChange:function(t){this.table2.pagination.current=t.current,this.table2.pagination.pageSize=t.pageSize,this.loadTable2Data()},loadTable3Data:function(){var t=this,e=this.table2.selectedRows;if(!e||0===e.length)return this.table3.pagination.total=0,this.table3.dataSource=[],void(this.table3.selectedRows=[]);null==this.table2.lastRow&&(this.table2.lastRow=e[e.length-1]);var a={parentId:this.table2.lastRow.id,pageNo:this.table3.pagination.current,pageSize:this.table3.pagination.pageSize};this.table3.loading=!0,Object(l["c"])(this.url.getData,a).then((function(e){e.success?(t.table3.pagination.total=e.result.total,t.table3.dataSource=e.result.records):t.$error({title:"子表查询失败",content:e.message})})).finally((function(){t.table3.loading=!1}))},handleTable3PageChange:function(t){this.table3.pagination.current=t.current,this.table3.pagination.pageSize=t.pageSize,this.loadTable3Data()},handleTableSelectRowChange:function(t,e){var a=e.row,n=e.action,i=e.selectedRows,o=e.$table,l=i[i.length-1];"selected"===n?t.lastRow=a:"selected-all"===n?0===i.length?t.lastRow=null:t.lastRow||(t.lastRow=l):"unselected"===n&&a===t.lastRow&&(t.lastRow=l),o.setCurrentRow(t.lastRow),t.selectedRows=i}}},d=c,p=a("2877"),u=Object(p["a"])(d,i,o,!1,null,null,null);e["default"]=u.exports},4874:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{staticStyle:{"min-width":"800px","overflow-x":"auto"},attrs:{title:"树形结构图片翻页查看"}},[a("a-row",[a("a-col",{attrs:{span:5}},[a("a-tree",{style:{height:"500px","border-right":"2px solid #c1c1c1","overflow-y":"auto"},attrs:{showLine:"",treeData:t.treeData,expandedKeys:[t.expandedKeys[0]],selectedKeys:t.selectedKeys},on:{expand:t.onExpand,select:this.onSelect}})],1),a("a-col",{attrs:{span:19}},[a("a-row",{staticStyle:{"margin-top":"10px"}},[a("a-col",{staticStyle:{"padding-left":"2%","margin-bottom":"10px"},attrs:{span:24}},[a("a-button",{attrs:{type:"primary"},on:{click:t.prev}},[a("a-icon",{attrs:{type:"left"}}),t._v("上一页")],1),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:t.next}},[t._v("下一页"),a("a-icon",{attrs:{type:"right"}})],1),a("span",{staticStyle:{"margin-left":"15%","font-weight":"bolder"}},[t._v(t._s(t.navName))])],1),a("a-col",{staticStyle:{"padding-left":"2%"},attrs:{span:24}},[a("img",{attrs:{src:t.imgUrl,preview:""}})])],1)],1)],1)],1)},i=[],o=a("b76a"),l=a.n(o),r={name:"ImgTurnPage",components:{draggable:l.a},data:function(){return{description:"图片翻页",treeData:[{title:"第一页",key:"0-0",children:[{title:"1页",key:"0-0-0",imgUrl:"https://static.jeecg.com/upload/test/1_1588149743473.jpg"},{title:"2页",key:"0-0-1",imgUrl:"https://static.jeecg.com/upload/test/u27356337152749454924fm27gp0_1588149731821.jpg"}]},{title:"第二页",key:"0-1",children:[{title:"1页",key:"0-1-0",imgUrl:"https://static.jeecg.com/upload/test/u24454681402491956848fm27gp0_1588149712663.jpg"},{title:"2页",key:"0-1-1",imgUrl:"https://static.jeecg.com/upload/test/u8891206113801177793fm27gp0_1588149704459.jpg"}]},{title:"第三页",key:"0-2",children:[{title:"1页",key:"0-2-0",imgUrl:"https://static.jeecg.com/upload/test/1374962_1587621329085.jpg"}]}],selectedKeys:[],expandedKeys:[],sort:0,imgUrl:"",navName:"",imgList:[]}},created:function(){this.getImgList()},methods:{getImgList:function(){for(var t=0,e=0;e<this.treeData.length;e++)for(var a=0;a<this.treeData[e].children.length;a++)this.imgList.push({key:this.treeData[e].children[a].key,pkey:this.treeData[e].key,sort:t++,imgUrl:this.treeData[e].children[a].imgUrl,navName:this.treeData[e].title+"/"+this.treeData[e].children[a].title});this.setValue(this.imgList[this.sort])},onSelect:function(t,e){for(var a=0;a<this.imgList.length;a++)if(this.imgList[a].key===t[0]){this.sort=this.imgList[a].sort,this.setValue(this.imgList[a]);break}},onExpand:function(t){this.expandedKeys=[],null!==t&&""!==t&&(this.expandedKeys[0]=t[1])},prev:function(){0===this.sort?this.sort=this.imgList.length-1:this.sort=this.sort-1,this.setValue(this.imgList[this.sort])},next:function(){this.sort===this.imgList.length-1?this.sort=0:this.sort=this.sort+1,this.setValue(this.imgList[this.sort])},setValue:function(t){this.selectedKeys=[],this.imgUrl=t.imgUrl,this.selectedKeys[0]=t.key,this.expandedKeys[0]=t.pkey,this.navName=t.navName}}},s=r,c=a("2877"),d=Object(c["a"])(s,n,i,!1,null,"bfa7c60c",null);e["default"]=d.exports},5443:function(t,e,a){"use strict";var n=a("f2a1"),i=a.n(n);i.a},"62cb":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("j-vxe-table",{ref:"vTable",attrs:{toolbar:"","row-number":"","row-selection":"","keep-source":"",height:484,dataSource:t.dataSource,columns:t.columns,"linkage-config":t.linkageConfig}})},i=[],o=a("a34a"),l=a.n(o),r=a("2475");function s(t,e,a,n,i,o,l){try{var r=t[o](l),s=r.value}catch(c){return void a(c)}r.done?e(s):Promise.resolve(s).then(n,i)}function c(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var o=t.apply(e,a);function l(t){s(o,n,i,l,r,"next",t)}function r(t){s(o,n,i,l,r,"throw",t)}l(void 0)}))}}var d={name:"JVxeDemo2",data:function(){return{linkageConfig:[{requestData:this.requestData,key:"s1"},{requestData:this.loadData,key:"level1"}],columns:[{title:"性别",key:"sex",type:r["b"].select,dictCode:"sex",width:"180px",placeholder:"请选择${title}"},{title:"省/直辖市/自治区",key:"s1",type:r["b"].select,width:"180px",placeholder:"请选择${title}",linkageKey:"s2"},{title:"市",key:"s2",type:r["b"].select,width:"180px",placeholder:"请选择${title}",linkageKey:"s3"},{title:"县/区",key:"s3",type:r["b"].select,width:"180px",options:[],placeholder:"请选择${title}"},{title:"一级",key:"level1",type:r["b"].select,width:"180px",placeholder:"请选择${title}",linkageKey:"level2"},{title:"二级",key:"level2",type:r["b"].select,width:"180px",placeholder:"请选择${title}",linkageKey:"level3"},{title:"三级",key:"level3",type:r["b"].select,width:"180px",placeholder:"请选择${title}"}],dataSource:[{sex:"1",s1:"110000",s2:"110100",s3:"110101",level1:"1",level2:"3",level3:"7"},{sex:"2",s1:"130000",s2:"130300",s3:"130303",level1:"2",level2:"6",level3:"14"}],mockData:[{text:"北京市",value:"110000",parent:""},{text:"天津市",value:"120000",parent:""},{text:"河北省",value:"130000",parent:""},{text:"上海市",value:"310000",parent:""},{text:"北京市",value:"110100",parent:"110000"},{text:"天津市市",value:"120100",parent:"120000"},{text:"石家庄市",value:"130100",parent:"130000"},{text:"唐山市",value:"130200",parent:"130000"},{text:"秦皇岛市",value:"130300",parent:"130000"},{text:"上海市",value:"310100",parent:"310000"},{text:"东城区",value:"110101",parent:"110100"},{text:"西城区",value:"110102",parent:"110100"},{text:"朝阳区",value:"110105",parent:"110100"},{text:"和平区",value:"120101",parent:"120100"},{text:"河东区",value:"120102",parent:"120100"},{text:"河西区",value:"120103",parent:"120100"},{text:"黄浦区",value:"310101",parent:"310100"},{text:"徐汇区",value:"310104",parent:"310100"},{text:"长宁区",value:"310105",parent:"310100"},{text:"长安区",value:"130102",parent:"130100"},{text:"桥西区",value:"130104",parent:"130100"},{text:"新华区",value:"130105",parent:"130100"},{text:"路南区",value:"130202",parent:"130200"},{text:"路北区",value:"130203",parent:"130200"},{text:"古冶区",value:"130204",parent:"130200"},{text:"海港区",value:"130302",parent:"130300"},{text:"山海关区",value:"130303",parent:"130300"},{text:"北戴河区",value:"130304",parent:"130300"}],mockData1:[{id:"1",name:"图书馆",parentId:"0"},{id:"2",name:"电影院",parentId:"0"},{id:"3",name:"一楼",parentId:"1"},{id:"4",name:"二楼",parentId:"1"},{id:"5",name:"中影星美",parentId:"2"},{id:"6",name:"万达国际",parentId:"2"},{id:"7",name:"技术图书",parentId:"3"},{id:"8",name:"财务图书",parentId:"3"},{id:"9",name:"儿童图书",parentId:"4"},{id:"10",name:"励志图书",parentId:"4"},{id:"11",name:"1号厅",parentId:"5"},{id:"12",name:"2号厅",parentId:"5"},{id:"13",name:"I-MAX厅",parentId:"6"},{id:"14",name:"3D厅",parentId:"6"}]}},methods:{requestData:function(t){var e=this;return new Promise((function(a,n){var i=e.mockData.filter((function(e){return e.parent===t}));setTimeout((function(){a(i)}),500)}))},loadData:function(){var t=c(l.a.mark((function t(e){var a=this;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,n){var i=""===e?"0":e,o=a.mockData1.filter((function(t){return t.parentId===i}));o=o.map((function(t){return{value:t.id,text:t.name}})),setTimeout((function(){t(o)}),500)})));case 1:case"end":return t.stop()}}),t)})));function e(e){return t.apply(this,arguments)}return e}()}},p=d,u=a("2877"),h=Object(u["a"])(p,n,i,!1,null,"4067447a",null);e["default"]=h.exports},"63dc":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单号"}},[a("a-input",{attrs:{placeholder:"请输入订单号"},model:{value:t.queryParam.orderCode,callback:function(e){t.$set(t.queryParam,"orderCode",e)},expression:"queryParam.orderCode"}})],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单类型"}},[a("a-select",{attrs:{placeholder:"请输入订单类型"},model:{value:t.queryParam.ctype,callback:function(e){t.$set(t.queryParam,"ctype",e)},expression:"queryParam.ctype"}},[a("a-select-option",{attrs:{value:"1"}},[t._v("国内订单")]),a("a-select-option",{attrs:{value:"2"}},[t._v("国际订单")])],1)],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:t.searchQuery}},[t._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:t.searchReset}},[t._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:t.handleAdd}},[t._v("新增")]),t.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:t.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),t._v("\n          删除\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[t._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):t._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),a("span",[t._v("已选择")]),a("a",{staticStyle:{"font-weight":"600"}},[t._v("\n        "+t._s(t.selectedRowKeys.length)+"\n      ")]),a("span",[t._v("项")]),a("a",{staticStyle:{"margin-left":"24px"},on:{click:t.onClearSelected}},[t._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:t.columns,dataSource:t.dataSource,pagination:t.ipagination,loading:t.loading,rowSelection:{selectedRowKeys:t.selectedRowKeys,onChange:t.onSelectChange}},on:{change:t.handleTableChange},scopedSlots:t._u([{key:"action",fn:function(e,n){return a("span",{},[a("a",{on:{click:function(e){return t.handleEdit(n)}}},[t._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[t._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return t.handleDelete(n.id)}}},[a("a",[t._v("删除")])])],1)],1)],1)],1)}}])})],1),a("jeecg-order-modal-for-j-vextable",{ref:"modalForm",on:{ok:t.modalFormOk}})],1)},i=[],o=a("b65a"),l=a("1fad"),r={name:"JeecgOrderMainListForJVxeTable",mixins:[o["a"]],components:{JeecgOrderModalForJVextable:l["default"]},data:function(){return{description:"订单管理页面",url:{list:"/test/jeecgOrderMain/list",delete:"/test/jeecgOrderMain/delete",deleteBatch:"/test/jeecgOrderMain/deleteBatch"},columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(t,e,a){return parseInt(a)+1}},{title:"订单号",align:"center",dataIndex:"orderCode"},{title:"订单类型",align:"center",dataIndex:"ctype",customRender:function(t){var e="";return"1"===t?e="国内订单":"2"===t&&(e="国际订单"),e}},{title:"订单日期",align:"center",dataIndex:"orderDate"},{title:"订单金额",align:"center",dataIndex:"orderMoney"},{title:"订单备注",align:"center",dataIndex:"content"},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{customRender:"action"}}]}},methods:{initDictConfig:function(){}}},s=r,c=(a("5443"),a("2877")),d=Object(c["a"])(s,n,i,!1,null,"a0d425d6",null);e["default"]=d.exports},"6c8f":function(t,e,a){"use strict";var n=a("eff1"),i=a.n(n);i.a},"6dde":function(t,e,a){},7602:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{title:"弹出子表示例",bordered:!1}},[a("j-vxe-table",{attrs:{toolbar:"","row-number":"","row-selection":"","highlight-current-row":"","click-row-show-sub-form":"","click-row-show-main-form":"",height:750,loading:t.loading,columns:t.columns,dataSource:t.dataSource},on:{detailsConfirm:t.handleDetailsConfirm},scopedSlots:t._u([{key:"mainForm",fn:function(e){var n=e.row;return[n?[a("a-form-model",{ref:"form2",attrs:{model:n,rules:t.rules,"label-col":t.labelCol,"wrapper-col":t.wrapperCol}},[a("a-row",{attrs:{gutter:8}},[a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"ID",prop:"id"}},[a("a-input",{attrs:{disabled:""},model:{value:n.id,callback:function(e){t.$set(n,"id",e)},expression:"row.id"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"序号",prop:"num"}},[a("a-input",{model:{value:n.num,callback:function(e){t.$set(n,"num",e)},expression:"row.num"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"船名",prop:"ship_name"}},[a("a-input",{model:{value:n.ship_name,callback:function(e){t.$set(n,"ship_name",e)},expression:"row.ship_name"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"呼叫",prop:"call"}},[a("a-input",{model:{value:n.call,callback:function(e){t.$set(n,"call",e)},expression:"row.call"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"长",prop:"len"}},[a("a-input",{model:{value:n.len,callback:function(e){t.$set(n,"len",e)},expression:"row.len"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"吨",prop:"ton"}},[a("a-input",{model:{value:n.ton,callback:function(e){t.$set(n,"ton",e)},expression:"row.ton"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"付款方",prop:"payer"}},[a("a-input",{model:{value:n.payer,callback:function(e){t.$set(n,"payer",e)},expression:"row.payer"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"数",prop:"count"}},[a("a-input",{model:{value:n.count,callback:function(e){t.$set(n,"count",e)},expression:"row.count"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"公司",prop:"company"}},[a("a-input",{model:{value:n.company,callback:function(e){t.$set(n,"company",e)},expression:"row.company"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"动向",prop:"trend"}},[a("a-input",{model:{value:n.trend,callback:function(e){t.$set(n,"trend",e)},expression:"row.trend"}})],1)],1)],1)],1)]:t._e()]}},{key:"subForm",fn:function(e){var n=e.row;return[t.loadSubData(n)?[a("j-vxe-table",{ref:"subFormTable",attrs:{height:"auto","max-height":350,loading:t.subTable.loading,columns:t.subTable.columns,dataSource:t.subTable.dataSource}})]:t._e()]}}])})],1)},i=[],o=a("0fea"),l=a("2475"),r={name:"PopupSubTable",data:function(){return{loading:!1,dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:l["b"].input},{key:"call",title:"呼叫",width:"80px"},{key:"len",title:"长",width:"80px"},{key:"ton",title:"吨",width:"120px"},{key:"payer",title:"付款方",width:"120px"},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",showDetails:!0},{key:"trend",title:"动向",width:"120px"}],subTable:{currentRowId:null,loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:l["b"].input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:l["b"].input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:l["b"].input},{key:"type",title:"船舶分类",width:"120px",type:l["b"].input},{key:"port_area",title:"所属港区",minWidth:"120px",type:l["b"].input}]},url:{getData:"/mock/vxe/getData"},mainForm:{id:"",num:"",ship_name:"",call:"",len:"",ton:"",payer:"",count:"",company:"",trend:""},labelCol:{span:4},wrapperCol:{span:20},rules:{num:[{required:!0,message:"必须输入序号"}]}}},created:function(){this.loadData()},methods:{log:console.log,loadData:function(){var t=this,e={pageNo:1,pageSize:30};this.loading=!0,Object(o["c"])(this.url.getData,e).then((function(e){e.success?(t.dataSource=e.result.records,t.selectedRows=[]):t.$error({title:"主表查询失败",content:e.message})})).finally((function(){t.loading=!1}))},loadSubData:function(t){var e=this;if(t){if(this.subTable.currentRowId===t.id)return!0;this.subTable.currentRowId=t.id;var a={pageNo:1,pageSize:30,parentId:t.id};return this.subTable.loading=!0,Object(o["c"])(this.url.getData,a).then((function(t){t.success?e.subTable.dataSource=t.result.records:e.$error({title:"主表查询失败",content:t.message})})).finally((function(){e.subTable.loading=!1})),!0}return!1},handleDetailsConfirm:function(t){var e=this,a=t.row,n=t.$table,i=t.callback;n.validate(a).then((function(t){t?(i(!1),e.$message.warn("校验失败")):(i(!0),e.loading=!0,setTimeout((function(){e.loading=!1,e.$message.success("保存成功")}),1e3))}))}}},s=r,c=a("2877"),d=Object(c["a"])(s,n,i,!1,null,"365fa5d4",null);e["default"]=d.exports},"7a291":function(t,e,a){},8475:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{title:"无痕刷新示例",bordered:!1}},[a("div",{staticStyle:{"margin-bottom":"8px"}},[a("span",[t._v("启用数据变动特效：")]),a("a-switch",{model:{value:t.reloadEffect,callback:function(e){t.reloadEffect=e},expression:"reloadEffect"}})],1),a("j-vxe-table",{ref:"table",attrs:{"row-number":"","row-selection":"","keep-source":"","socket-reload":"","socket-key":"demo-socket-reload","reload-effect":t.reloadEffect,height:340,loading:t.loading,columns:t.columns,dataSource:t.dataSource},on:{"edit-closed":t.handleEditClosed}})],1)},i=[],o=a("0fea"),l=a("2475"),r={name:"SocketReload",data:function(){return{loading:!1,dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:l["b"].input},{key:"call",title:"呼叫",width:"80px",type:l["b"].input},{key:"len",title:"长",width:"80px",type:l["b"].input},{key:"ton",title:"吨",width:"120px",type:l["b"].input},{key:"payer",title:"付款方",width:"120px",type:l["b"].input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:l["b"].input},{key:"trend",title:"动向",width:"120px",type:l["b"].input}],url:{getData:"/mock/vxe/getData"},reloadEffect:!1}},created:function(){this.loadData()},methods:{loadData:function(){var t=this,e={pageNo:1,pageSize:200};this.loading=!0,Object(o["c"])(this.url.getData,e).then((function(e){e.success?t.dataSource=e.result.records:t.$error({title:"主表查询失败",content:e.message})})).finally((function(){t.loading=!1}))},handleEditClosed:function(t){var e=this,a=t.$table,n=t.row,i=t.column,o=i.property;n[o];a.isUpdateByRow(n,o)&&a.validate(n).then((function(t){if(!t){var l=e.$message.loading('正在保存"'.concat(i.title,'"'),0);setTimeout((function(){l(),e.$message.success('"'.concat(i.title,'"保存成功！')),a.reloadRow(n,null,o),e.$refs.table.socketSendUpdateRow(n)}),555)}}))}}},s=r,c=a("2877"),d=Object(c["a"])(s,n,i,!1,null,"738330b2",null);e["default"]=d.exports},8629:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-col",{attrs:{span:18}},[a("a-spin",{attrs:{tip:"Loading...",spinning:t.spinning}},[a("div",[a("a-row",[a("a-col",{attrs:{span:18}},[a("p",[a("a-divider",{attrs:{orientation:"left"}},[t._v("组一")])],1)]),a("a-col",{attrs:{span:6}}),a("a-col",{attrs:{span:12}},[t._l(t.dataSource[0].fileDetails,(function(e,n){return a("div",{key:n},[a("div",{staticStyle:{float:"left",width:"104px",height:"104px","margin-right":"10px",margin:"0 8px 8px 0"}},[a("div",{staticStyle:{width:"100%",height:"100%",position:"relative",padding:"8px",border:"1px solid #d9d9d9","border-radius":"4px"}},[a("img",{staticStyle:{width:"100%"},attrs:{src:e.imgUrl,preview:t.dataSource[0].key}})])])])}))],2)],1)],1),a("div",[a("a-row",[a("a-col",{attrs:{span:18}},[a("p",[a("a-divider",{attrs:{orientation:"left"}},[t._v("组二")])],1)]),a("a-col",{attrs:{span:6}}),a("a-col",{attrs:{span:12}},[t._l(t.dataSource[1].fileDetails,(function(e,n){return a("div",{key:n},[a("div",{staticStyle:{float:"left",width:"104px",height:"104px","margin-right":"10px",margin:"0 8px 8px 0"}},[a("div",{staticStyle:{width:"100%",height:"100%",position:"relative",padding:"8px",border:"1px solid #d9d9d9","border-radius":"4px"}},[a("img",{staticStyle:{width:"100%"},attrs:{src:e.imgUrl,preview:t.dataSource[1].key}})])])])}))],2)],1)],1)]),a("p")],1)],1)},i=[],o=a("290c"),l={name:"ImagPreview",components:{ARow:o["a"]},data:function(){return{description:"图片预览页面",spinning:!1,dataSource:[{key:0,fileDetails:[{imgUrl:"https://static.jeecg.com/upload/test/3a4490d5d1cd495b826e528537a47cc1.jpg"},{imgUrl:"https://static.jeecg.com/temp/国炬软件logo_1606575029126.png"}]},{key:1,fileDetails:[{imgUrl:"https://static.jeecg.com/upload/test/u27356337152749454924fm27gp0_1588149731821.jpg"},{imgUrl:"https://static.jeecg.com/upload/test/1_1588149743473.jpg"}]}],url:{}}},created:function(){},methods:{}},r=l,s=(a("9836"),a("2877")),c=Object(s["a"])(r,n,i,!1,null,"7108324f",null);e["default"]=c.exports},"87a0":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("j-vxe-table",{ref:"vTable",staticStyle:{"margin-top":"8px"},attrs:{toolbar:"","row-number":"","row-selection":"","keep-source":"",height:484,loading:t.loading,dataSource:t.dataSource,columns:t.columns,pagination:t.pagination},on:{pageChange:t.handlePageChange},scopedSlots:t._u([{key:"toolbarSuffix",fn:function(){return[a("a-button",{on:{click:t.handleTableGet}},[t._v("获取值")])]},proxy:!0}])})},i=[],o=(a("c1df"),a("ca00")),l=a("2475"),r={name:"JVxeDemo2",data:function(){return{loading:!1,columns:[{title:"下拉框_字典表搜索",key:"select_dict_search",type:l["b"].selectDictSearch,width:"200px",dict:"sys_user,realname,username"},{title:"JPopup",key:"popup",type:l["b"].popup,width:"180px",popupCode:"demo",field:"name,sex,age",orgFields:"name,sex,age",destFields:"popup,popup_sex,popup_age"},{title:"JP-性别",key:"popup_sex",type:l["b"].select,dictCode:"sex",disabled:!0,width:"100px"},{title:"JP-年龄",key:"popup_age",type:l["b"].normal,width:"80px"},{title:"进度条",key:"progress",type:l["b"].progress,minWidth:"120px"},{title:"单选",key:"radio",type:l["b"].radio,width:"130px",options:[{text:"男",value:"1"},{text:"女",value:"2"}],allowClear:!0},{title:"上传",key:"upload",type:l["b"].upload,width:"180px",btnText:"点击上传",token:!0,responseName:"message",action:window._CONFIG["domianURL"]+"/sys/common/upload"},{title:"图片上传",key:"image",type:l["b"].image,width:"180px",token:!0},{title:"文件上传",key:"file",type:l["b"].file,width:"180px",token:!0}],dataSource:[],pagination:{current:1,pageSize:10,pageSizeOptions:["10","20","30","100","200"],total:1e3}}},created:function(){this.randomPage(this.pagination.current,this.pagination.pageSize,!0)},methods:{handlePageChange:function(t){this.pagination.current=t.current,this.pagination.pageSize=t.pageSize,this.randomPage(t.current,t.pageSize,!0)},handleTableGet:function(){this.$refs.vTable.getTableData();this.$message.success("获取值成功，请看控制台输出")},randomPage:function(t,e){var a=this,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];n&&(this.loading=!0);for(var i=Date.now(),l=[],r=0;r<e;r++){var s=Object(o["k"])(0,2);l.push({id:Object(o["m"])(),select_dict_search:["","admin","","jeecg",""][Object(o["k"])(0,4)],progress:Object(o["k"])(0,100),radio:s?s.toString():null})}this.dataSource=l;var c=Date.now(),d=c-i;n&&d<e&&setTimeout((function(){a.loading=!1}),e-d)}}},s=r,c=a("2877"),d=Object(c["a"])(s,n,i,!1,null,"59815a48",null);e["default"]=d.exports},"8f7d":function(t,e,a){"use strict";a.r(e);var n,i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-row",{attrs:{gutter:8}},[a("a-col",{attrs:{span:12}},[a("j-vxe-table",{staticStyle:{"margin-bottom":"8px"},attrs:{toolbar:"","row-number":"","row-selection":"","click-select-row":"","highlight-current-row":"","radio-config":{highlight:!1},"checkbox-config":{highlight:!1},height:340,loading:t.table1.loading,columns:t.table1.columns,dataSource:t.table1.dataSource,pagination:t.table1.pagination},on:{pageChange:t.handleTable1PageChange,selectRowChange:t.handleTable1SelectRowChange}}),a("j-vxe-table",{attrs:{toolbar:"","row-number":"","row-selection":"","click-select-row":"",height:350,loading:t.table2.loading,columns:t.table2.columns,dataSource:t.table2.dataSource,pagination:t.table2.pagination},on:{pageChange:t.handleTable2PageChange}})],1),a("a-col",{attrs:{span:12}},[a("j-vxe-table",{staticStyle:{margin:"40px 0 8px"},attrs:{"row-number":"","row-selection":"","click-select-row":"","highlight-current-row":"","radio-config":{highlight:!1},"checkbox-config":{highlight:!1},height:340,columns:t.table1.columns,dataSource:t.table1.selectedRows},on:{selectRowChange:t.handleTable3SelectRowChange}}),a("j-vxe-table",{staticStyle:{margin:"48px 0 0"},attrs:{toolbar:"","row-number":"","row-selection":"","click-select-row":"",height:350,loading:t.table4.loading,columns:t.table4.columns,dataSource:t.table4.dataSource,pagination:t.table4.pagination}})],1)],1)],1)},o=[],l=a("0fea"),r=a("2475");function s(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var c={name:"Template4",data:function(){return{table1:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0},lastRow:null,selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:r["b"].input},{key:"call",title:"呼叫",width:"80px",type:r["b"].input},{key:"len",title:"长",width:"80px",type:r["b"].input},{key:"ton",title:"吨",width:"120px",type:r["b"].input},{key:"payer",title:"付款方",width:"120px",type:r["b"].input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:r["b"].input},{key:"trend",title:"动向",width:"120px",type:r["b"].input}]},table2:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:r["b"].input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:r["b"].input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:r["b"].input},{key:"type",title:"船舶分类",width:"120px",type:r["b"].input},{key:"port_area",title:"所属港区",width:"120px",type:r["b"].input}]},table3:{lastRow:null,selectedRows:[]},table4:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:r["b"].input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:r["b"].input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:r["b"].input},{key:"type",title:"船舶分类",width:"120px",type:r["b"].input},{key:"port_area",title:"所属港区",width:"120px",type:r["b"].input}]},url:{getData:"/mock/vxe/getData"}}},watch:(n={},s(n,"table1.lastRow",(function(){this.loadTable2Data()})),s(n,"table3.lastRow",(function(){this.loadTable4Data()})),n),created:function(){this.loadTable1Data()},methods:{loadTable1Data:function(){var t=this,e={pageNo:this.table1.pagination.current,pageSize:this.table1.pagination.pageSize};this.table1.loading=!0,Object(l["c"])(this.url.getData,e).then((function(e){e.success?(t.table1.pagination.total=e.result.total,t.table1.dataSource=e.result.records,t.table1.selectedRows=[]):t.$error({title:"主表查询失败",content:e.message})})).finally((function(){t.table1.loading=!1}))},handleTable1PageChange:function(t){this.table1.pagination.current=t.current,this.table1.pagination.pageSize=t.pageSize,this.loadTable1Data()},handleTable1SelectRowChange:function(t){this.handleTableSelectRowChange(this.table1,t)},loadTable2Data:function(){var t=this,e=this.table1.selectedRows;if(!e||0===e.length)return this.table2.pagination.total=0,this.table2.dataSource=[],void(this.table2.selectedRows=[]);null==this.table1.lastRow&&(this.table1.lastRow=e[e.length-1]);var a={parentId:this.table1.lastRow.id,pageNo:this.table2.pagination.current,pageSize:this.table2.pagination.pageSize};this.table2.loading=!0,Object(l["c"])(this.url.getData,a).then((function(e){e.success?(t.table2.pagination.total=e.result.total,t.table2.dataSource=e.result.records,t.table2.selectedRows=[]):t.$error({title:"子表查询失败",content:e.message})})).finally((function(){t.table2.loading=!1}))},handleTable2PageChange:function(t){this.table2.pagination.current=t.current,this.table2.pagination.pageSize=t.pageSize,this.loadTable2Data()},handleTable3SelectRowChange:function(t){this.handleTableSelectRowChange(this.table3,t)},loadTable4Data:function(){var t=this,e=this.table3.selectedRows;if(!e||0===e.length)return this.table4.pagination.total=0,this.table4.dataSource=[],void(this.table4.selectedRows=[]);null==this.table3.lastRow&&(this.table3.lastRow=e[e.length-1]);var a={parentId:this.table3.lastRow.id,pageNo:this.table4.pagination.current,pageSize:this.table4.pagination.pageSize};this.table4.loading=!0,Object(l["c"])(this.url.getData,a).then((function(e){e.success?(t.table4.pagination.total=e.result.total,t.table4.dataSource=e.result.records,t.table4.selectedRows=[]):t.$error({title:"子表查询失败",content:e.message})})).finally((function(){t.table4.loading=!1}))},handleTable4PageChange:function(t){this.table4.pagination.current=t.current,this.table4.pagination.pageSize=t.pageSize,this.loadTable4Data()},handleTableSelectRowChange:function(t,e){var a=e.row,n=e.action,i=e.selectedRows,o=e.$table,l=i[i.length-1];"selected"===n?t.lastRow=a:"selected-all"===n?0===i.length?t.lastRow=null:t.lastRow||(t.lastRow=l):"unselected"===n&&a===t.lastRow&&(t.lastRow=l),o.setCurrentRow(t.lastRow),t.selectedRows=i}}},d=c,p=a("2877"),u=Object(p["a"])(d,i,o,!1,null,"a2d232f2",null);e["default"]=u.exports},"91b6":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{class:{abcdefg:!0},attrs:{bordered:!1}},[a("div",{staticClass:"no-print",staticStyle:{"text-align":"right"}},[a("a-button",{directives:[{name:"print",rawName:"v-print",value:"#printContent",expression:"'#printContent'"}],attrs:{ghost:"",type:"primary"}},[t._v("打印")])],1),a("section",{ref:"print",staticClass:"abcdefg",attrs:{id:"printContent"}},[a("div",{staticStyle:{"text-align":"center"}},[a("p",{staticStyle:{"font-size":"24px","font-weight":"800"}},[t._v("打印测试表单")])]),a("a-col",{attrs:{md:24,sm:24}},[a("div",{staticClass:"sign",staticStyle:{"text-align":"left",height:"inherit"}},[a("a-col",{attrs:{span:24}},[a("span",[t._v("\n          打印人员:\n        ")]),a("a-input",{staticStyle:{width:"30%"},model:{value:t.printer,callback:function(e){t.printer=e},expression:"printer"}}),a("span",{staticStyle:{"margin-left":"12.5%"}},[t._v("打印日期:")]),a("a-input",{staticStyle:{width:"30%"},model:{value:t.printTime,callback:function(e){t.printTime=e},expression:"printTime"}})],1),a("a-col",{attrs:{span:24}}),a("a-col",{staticStyle:{"margin-top":"20px"},attrs:{span:24}},[a("span",[t._v("打印内容:")]),a("a-input",{staticStyle:{width:"80%"},model:{value:t.printContent,callback:function(e){t.printContent=e},expression:"printContent"}})],1),a("a-col",{staticStyle:{"margin-top":"20px"},attrs:{span:24}},[a("span",[t._v("打印目的:")]),a("a-input",{staticStyle:{width:"80%"},model:{value:t.printReason,callback:function(e){t.printReason=e},expression:"printReason"}})],1),a("a-col",{staticStyle:{"margin-top":"20px"},attrs:{span:24}},[a("span",[t._v("打印图片:")]),a("br"),a("a-upload",{attrs:{action:"/jsonplaceholder.typicode.com/posts/",listType:"picture-card",fileList:t.fileList},on:{preview:t.handlePreview,change:t.handleChange}},[t.fileList.length<3?a("div",[a("a-icon",{attrs:{type:"plus"}}),a("div",{staticClass:"ant-upload-text"},[t._v("Upload")])],1):t._e()]),a("a-modal",{attrs:{visible:t.previewVisible,footer:null},on:{cancel:t.handleCancel}},[a("img",{staticStyle:{width:"100%"},attrs:{alt:"example",src:t.previewImage}})])],1)],1)])],1)])},i=[],o=a("da05"),l=a("290c"),r=a("261e"),s={components:{ATextarea:r["a"],ARow:l["a"],ACol:o["b"]},name:"Printgzsld",props:{reBizCode:{type:String,default:""}},data:function(){return{columns:[{}],labelCol:{xs:{span:24},sm:{span:2}},wrapperCol:{xs:{span:24},sm:{span:8}},printer:"张三",printTime:"2019-02-01 12:00:00",printContent:"打印内容就是,做一个打印测试",printReason:"做一个打印测试",previewVisible:!1,previewImage:"",fileList:[{uid:"-1",name:"xxx.png",status:"done",url:"https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"},{uid:"-2",name:"pic1.png",status:"done",url:"https://www.gizbot.com/img/2016/11/whatsapp-error-lead-image-08-1478607387.jpg"}],url:{loadApplicant:"/sps/register/loadApplicants",loadRegisterFiles:"/sps/register/getRegisterFilesConfig"}}},created:function(){this.getDate()},methods:{loadData:function(){},getDate:function(){},handleCancel:function(){this.previewVisible=!1},handlePreview:function(t){this.previewImage=t.url||t.thumbUrl,this.previewVisible=!0},handleChange:function(t){var e=t.fileList;this.fileList=e}}},c=s,d=(a("327c"),a("2877")),p=Object(d["a"])(c,n,i,!1,null,"3ee03aee",null);e["default"]=p.exports},"91fd":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-row",{attrs:{gutter:8}},[a("a-col",{attrs:{span:12}},[a("j-vxe-table",{attrs:{toolbar:"","row-number":"","row-selection":"","click-select-row":"","highlight-current-row":"","radio-config":{highlight:!1},"checkbox-config":{highlight:!1},height:790,loading:t.table1.loading,columns:t.table1.columns,dataSource:t.table1.dataSource,pagination:t.table1.pagination},on:{pageChange:t.handleTable1PageChange,selectRowChange:t.handleTable1SelectRowChange}})],1),a("a-col",{attrs:{span:12}},[a("j-vxe-table",{staticStyle:{margin:"40px 0 8px"},attrs:{"row-number":"",height:381,columns:t.table1.columns,dataSource:t.table1.selectedRows}}),a("j-vxe-table",{attrs:{toolbar:"","row-number":"","row-selection":"","click-select-row":"",height:361,loading:t.table2.loading,columns:t.table2.columns,dataSource:t.table2.dataSource,pagination:t.table2.pagination},on:{pageChange:t.handleTable2PageChange,selectRowChange:t.handleTable2SelectRowChange}})],1)],1)],1)},i=[],o=a("0fea"),l=a("2475");function r(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var s={name:"Template2",data:function(){return{table1:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0},lastRow:null,selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:l["b"].input},{key:"call",title:"呼叫",width:"80px",type:l["b"].input},{key:"len",title:"长",width:"80px",type:l["b"].input},{key:"ton",title:"吨",width:"120px",type:l["b"].input},{key:"payer",title:"付款方",width:"120px",type:l["b"].input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:l["b"].input},{key:"trend",title:"动向",width:"120px",type:l["b"].input}]},table2:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:l["b"].input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:l["b"].input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:l["b"].input},{key:"type",title:"船舶分类",width:"120px",type:l["b"].input},{key:"port_area",title:"所属港区",width:"120px",type:l["b"].input}]},url:{getData:"/mock/vxe/getData"}}},watch:r({},"table1.lastRow",(function(){this.loadTable2Data()})),created:function(){this.loadTable1Data()},methods:{loadTable1Data:function(){var t=this,e={pageNo:this.table1.pagination.current,pageSize:this.table1.pagination.pageSize};this.table1.loading=!0,Object(o["c"])(this.url.getData,e).then((function(e){e.success?(t.table1.pagination.total=e.result.total,t.table1.dataSource=e.result.records,t.table1.selectedRows=[]):t.$error({title:"主表查询失败",content:e.message})})).finally((function(){t.table1.loading=!1}))},loadTable2Data:function(){var t=this,e=this.table1.selectedRows;if(!e||0===e.length)return this.table2.pagination.total=0,this.table2.dataSource=[],void(this.table2.selectedRows=[]);null==this.table1.lastRow&&(this.table1.lastRow=e[e.length-1]);var a={parentId:this.table1.lastRow.id,pageNo:this.table2.pagination.current,pageSize:this.table2.pagination.pageSize};this.table2.loading=!0,Object(o["c"])(this.url.getData,a).then((function(e){e.success?(t.table2.pagination.total=e.result.total,t.table2.dataSource=e.result.records,t.table2.selectedRows=[]):t.$error({title:"子表查询失败",content:e.message})})).finally((function(){t.table2.loading=!1}))},handleTable1SelectRowChange:function(t){this.handleTableSelectRowChange(this.table1,t)},handleTable2SelectRowChange:function(t){this.table2.selectedRows=t.selectedRows},handleTable1PageChange:function(t){this.table1.pagination.current=t.current,this.table1.pagination.pageSize=t.pageSize,this.loadTable1Data()},handleTable2PageChange:function(t){this.table2.pagination.current=t.current,this.table2.pagination.pageSize=t.pageSize,this.loadTable2Data()},handleTableSelectRowChange:function(t,e){var a=e.row,n=e.action,i=e.selectedRows,o=e.$table,l=i[i.length-1];"selected"===n?t.lastRow=a:"selected-all"===n?0===i.length?t.lastRow=null:t.lastRow||(t.lastRow=l):"unselected"===n&&a===t.lastRow&&(t.lastRow=l),o.setCurrentRow(t.lastRow),t.selectedRows=i}}},c=s,d=a("2877"),p=Object(d["a"])(c,n,i,!1,null,"8a69257c",null);e["default"]=p.exports},"959a":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-tabs",[a("a-tab-pane",{key:"erp",attrs:{tab:"ERP布局模板"}},[a("erp-template")],1),a("a-tab-pane",{key:"1",attrs:{tab:"布局模板1"}},[a("template1")],1),a("a-tab-pane",{key:"2",attrs:{tab:"布局模板2"}},[a("template2")],1),a("a-tab-pane",{key:"3",attrs:{tab:"布局模板3"}},[a("template3")],1),a("a-tab-pane",{key:"4",attrs:{tab:"布局模板4"}},[a("template4")],1),a("a-tab-pane",{key:"5",attrs:{tab:"布局模板5"}},[a("template5")],1)],1)],1)},i=[],o=a("46a6"),l=a("91fd"),r=a("42c7"),s=a("8f7d"),c=a("d8de"),d=a("af80"),p={name:"LayoutDemo",components:{Template5:c["default"],Template4:s["default"],Template3:r["default"],Template2:l["default"],Template1:o["default"],ErpTemplate:d["default"]}},u=p,h=a("2877"),g=Object(h["a"])(u,n,i,!1,null,"eebbdda6",null);e["default"]=g.exports},9836:function(t,e,a){"use strict";var n=a("d142"),i=a.n(n);i.a},"9b15":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单号"}},[a("a-input",{attrs:{placeholder:"请输入订单号"},model:{value:t.queryParam.orderCode,callback:function(e){t.$set(t.queryParam,"orderCode",e)},expression:"queryParam.orderCode"}})],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单类型"}},[a("a-select",{attrs:{placeholder:"请输入订单类型"},model:{value:t.queryParam.ctype,callback:function(e){t.$set(t.queryParam,"ctype",e)},expression:"queryParam.ctype"}},[a("a-select-option",{attrs:{value:"1"}},[t._v("国内订单")]),a("a-select-option",{attrs:{value:"2"}},[t._v("国际订单")])],1)],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:t.searchQuery}},[t._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:t.searchReset}},[t._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:t.handleAdd}},[t._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(e){return t.handleExportXls("一对多示例")}}},[t._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:t.tokenHeader,action:t.importExcelUrl},on:{change:t.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[t._v("导入")])],1),t.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:t.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),t._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[t._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):t._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),t._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[t._v(t._s(t.selectedRowKeys.length))]),t._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:t.onClearSelected}},[t._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:t.columns,dataSource:t.dataSource,pagination:t.ipagination,loading:t.loading,rowSelection:{selectedRowKeys:t.selectedRowKeys,onChange:t.onSelectChange}},on:{change:t.handleTableChange},scopedSlots:t._u([{key:"action",fn:function(e,n){return a("span",{},[a("a",{on:{click:function(e){return t.handleEdit(n)}}},[t._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[t._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return t.handleDelete(n.id)}}},[a("a",[t._v("删除")])])],1)],1)],1)],1)}}])})],1),a("jeecgOrderMain-modal",{ref:"modalForm",on:{ok:t.modalFormOk}})],1)},i=[],o=a("8a45"),l=a("b65a"),r={name:"JeecgOrderMainList",mixins:[l["a"]],components:{JeecgOrderMainModal:o["default"]},data:function(){return{description:"订单管理页面",importExcelUrl:"".concat(window._CONFIG["domianURL"],"/test/jeecgOrderMain/importExcel"),columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(t,e,a){return parseInt(a)+1}},{title:"订单号",align:"center",dataIndex:"orderCode"},{title:"订单类型",align:"center",dataIndex:"ctype",customRender:function(t,e,a){var n="";return"1"===t?n="国内订单":"2"===t&&(n="国际订单"),n}},{title:"订单日期",align:"center",dataIndex:"orderDate"},{title:"订单金额",align:"center",dataIndex:"orderMoney"},{title:"订单备注",align:"center",dataIndex:"content"},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{customRender:"action"}}],url:{list:"/test/jeecgOrderMain/list",delete:"/test/jeecgOrderMain/delete",deleteBatch:"/test/jeecgOrderMain/deleteBatch",exportXlsUrl:"/test/jeecgOrderMain/exportXls"}}},methods:{}},s=r,c=(a("1bc32"),a("2877")),d=Object(c["a"])(s,n,i,!1,null,"1f16717b",null);e["default"]=d.exports},"9b15d":function(t,e,a){},a9cc:function(t,e,a){},af80:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("j-vxe-table",{staticStyle:{"margin-bottom":"8px"},attrs:{toolbar:"",toolbarConfig:t.toolbarConfig,"row-number":"","row-selection":"","row-selection-type":"radio","highlight-current-row":"","click-select-row":"",height:t.tableHeight,loading:t.table1.loading,columns:t.table1.columns,dataSource:t.table1.dataSource,pagination:t.table1.pagination,"expand-config":t.expandConfig},on:{pageChange:t.handleTable1PageChange,selectRowChange:t.handleTable1SelectRowChange}}),a("a-tabs",{directives:[{name:"show",rawName:"v-show",value:t.subTabs.show,expression:"subTabs.show"}],class:{"sub-tabs":!0,"un-expand":!t.subTabs.expand}},[a("a-tab-pane",{key:"1",attrs:{tab:"子表1"}},[a("j-vxe-table",{attrs:{toolbar:"","row-number":"","row-selection":"",height:"auto",maxHeight:350,loading:t.table2.loading,columns:t.table2.columns,dataSource:t.table2.dataSource,pagination:t.table2.pagination},on:{pageChange:t.handleTable2PageChange,selectRowChange:t.handleTable2SelectRowChange}})],1),a("a-tab-pane",{key:"2",attrs:{tab:"子表2"}},[a("h1",[t._v("这里是子表2")]),a("h1",[t._v("这里是子表2")]),a("h1",[t._v("这里是子表2")]),a("h1",[t._v("这里是子表2")]),a("h1",[t._v("这里是子表2")]),a("h1",[t._v("这里是子表2")])])],1)],1)},i=[],o=a("2475"),l=a("0fea"),r={name:"ErpTemplate",data:function(){var t=this,e=this.$createElement;return{toolbarConfig:{slot:["prefix","suffix"],btn:["add","remove","clearSelection"]},expandConfig:{accordion:!0},subTabs:{show:!1,expand:!0,autoExpand:!0},table1:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0,showTotal:function(a,n){var i=e("span",[n[0]+"-"+n[1]+" 共 "+a+" 条"]);if(t.subTabs.show){var o=e("span",[e("a-button",{attrs:{type:"link"},on:{click:t.handleToggleTabs}},[e("a-icon",{attrs:{type:t.subTabs.expand?"up":"down"}}),e("span",[t.subTabs.expand?"收起":"展开"])]),e("a-checkbox",{model:{value:t.subTabs.autoExpand,callback:function(e){t.$set(t.subTabs,"autoExpand",e)}}},["自动展开"])]);return[o,i]}return i}},selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:o["b"].input},{key:"call",title:"呼叫",width:"990px",type:o["b"].input},{key:"len",title:"长",width:"80px",type:o["b"].inputNumber},{key:"ton",title:"吨",width:"120px",type:o["b"].inputNumber},{key:"payer",title:"付款方",width:"120px",type:o["b"].input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:o["b"].input},{key:"trend",title:"动向",width:"120px",type:o["b"].input}]},table2:{currentRowId:null,loading:!1,pagination:{current:1,pageSize:10,pageSizeOptions:["5","10","20","30"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:o["b"].input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:o["b"].input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:o["b"].input},{key:"type",title:"船舶分类",width:"120px",type:o["b"].input},{key:"port_area",title:"所属港区",width:"120px",type:o["b"].input}]},currentSubRow:null,url:{getData:"/mock/vxe/getData"}}},computed:{tableHeight:function(){var t=this.subTabs,e=t.show,a=t.expand;return e&&a?350:482}},created:function(){this.loadTable1Data()},methods:{loadTable1Data:function(){var t=this,e={pageNo:this.table1.pagination.current,pageSize:this.table1.pagination.pageSize};this.table1.loading=!0,Object(l["c"])(this.url.getData,e).then((function(e){e.success?(t.table1.pagination.total=e.result.total,t.table1.dataSource=e.result.records,t.table1.selectedRows=[]):t.$error({title:"主表查询失败",content:e.message})})).finally((function(){t.table1.loading=!1}))},loadSubData:function(t){return!!t&&(this.table2.currentRowId===t.id||(this.table2.currentRowId=t.id,this.loadTable2Data()),!0)},loadTable2Data:function(){var t=this,e=this.table2,a={parentId:e.currentRowId,pageNo:this.table2.pagination.current,pageSize:this.table2.pagination.pageSize};e.loading=!0,Object(l["c"])(this.url.getData,a).then((function(a){a.success?(e.selectedRows=[],e.dataSource=a.result.records,e.pagination.total=a.result.total):t.$error({title:"子表查询失败",content:a.message})})).finally((function(){e.loading=!1}))},handleTable1SelectRowChange:function(t){this.table1.selectedRows=t.selectedRows,this.subTabs.show=!0,this.subTabs.autoExpand&&(this.subTabs.expand=!0),this.loadSubData(t.selectedRows[0])},handleTable2SelectRowChange:function(t){this.table2.selectedRows=t.selectedRows},handleTable1PageChange:function(t){this.table1.pagination.current=t.current,this.table1.pagination.pageSize=t.pageSize,this.loadTable1Data()},handleTable2PageChange:function(t){this.table2.pagination.current=t.current,this.table2.pagination.pageSize=t.pageSize,this.loadTable2Data()},handleToggleTabs:function(){this.subTabs.expand=!this.subTabs.expand}}},s=r,c=(a("6c8f"),a("2877")),d=Object(c["a"])(s,n,i,!1,null,"e08516b8",null);e["default"]=d.exports},b020:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-tabs",[a("a-tab-pane",{key:"1",attrs:{tab:"普通列表"}},[a("default-table")],1),a("a-tab-pane",{key:"2",attrs:{tab:"只读列表"}},[a("read-only-table")],1),a("a-tab-pane",{key:"3",attrs:{tab:"三级联动"}},[a("three-linkage")],1)],1)],1)},i=[],o=a("ff3c"),l=a("9ba5"),r=a("01fe"),s={name:"JeecgEditableTableExample",components:{DefaultTable:o["default"],ReadOnlyTable:l["default"],ThreeLinkage:r["default"]},data:function(){return{}},methods:{}},c=s,d=a("2877"),p=Object(d["a"])(c,n,i,!1,null,"7e88b5ec",null);e["default"]=p.exports},b2da:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单号"}},[a("a-input",{attrs:{placeholder:"请输入订单号"},model:{value:t.queryParam.orderCode,callback:function(e){t.$set(t.queryParam,"orderCode",e)},expression:"queryParam.orderCode"}})],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("a-form-item",{attrs:{label:"订单类型"}},[a("a-select",{attrs:{placeholder:"请输入订单类型"},model:{value:t.queryParam.ctype,callback:function(e){t.$set(t.queryParam,"ctype",e)},expression:"queryParam.ctype"}},[a("a-select-option",{attrs:{value:"1"}},[t._v("国内订单")]),a("a-select-option",{attrs:{value:"2"}},[t._v("国际订单")])],1)],1)],1),a("a-col",{attrs:{md:6,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:t.searchQuery}},[t._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:t.searchReset}},[t._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:t.handleAdd}},[t._v("新增")]),t.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:t.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),t._v("\n          删除\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[t._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):t._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),a("span",[t._v("已选择")]),a("a",{staticStyle:{"font-weight":"600"}},[t._v("\n        "+t._s(t.selectedRowKeys.length)+"\n      ")]),a("span",[t._v("项")]),a("a",{staticStyle:{"margin-left":"24px"},on:{click:t.onClearSelected}},[t._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:t.columns,dataSource:t.dataSource,pagination:t.ipagination,loading:t.loading,rowSelection:{selectedRowKeys:t.selectedRowKeys,onChange:t.onSelectChange}},on:{change:t.handleTableChange},scopedSlots:t._u([{key:"action",fn:function(e,n){return a("span",{},[a("a",{on:{click:function(e){return t.handleEdit(n)}}},[t._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[t._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return t.handleDelete(n.id)}}},[a("a",[t._v("删除")])])],1)],1)],1)],1)}}])})],1),a("jeecg-order-modal-for-j-editable-table",{ref:"modalForm",on:{ok:t.modalFormOk}})],1)},i=[],o=a("b65a"),l=a("b8ad3"),r={name:"JeecgOrderMainListForJEditableTable",mixins:[o["a"]],components:{JeecgOrderModalForJEditableTable:l["default"]},data:function(){return{description:"订单管理页面",url:{list:"/test/jeecgOrderMain/list",delete:"/test/jeecgOrderMain/delete",deleteBatch:"/test/jeecgOrderMain/deleteBatch"},columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(t,e,a){return parseInt(a)+1}},{title:"订单号",align:"center",dataIndex:"orderCode"},{title:"订单类型",align:"center",dataIndex:"ctype",customRender:function(t){var e="";return"1"===t?e="国内订单":"2"===t&&(e="国际订单"),e}},{title:"订单日期",align:"center",dataIndex:"orderDate"},{title:"订单金额",align:"center",dataIndex:"orderMoney"},{title:"订单备注",align:"center",dataIndex:"content"},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{customRender:"action"}}]}},methods:{initDictConfig:function(){}}},s=r,c=(a("e31a"),a("2877")),d=Object(c["a"])(s,n,i,!1,null,"1c6adbea",null);e["default"]=d.exports},c2d3:function(t,e,a){"use strict";var n=a("6dde"),i=a.n(n);i.a},d142:function(t,e,a){},d8de:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-row",{attrs:{gutter:8}},[a("a-col",{attrs:{span:6}},[a("a-tree",{staticClass:"template-5-tree",attrs:{"tree-data":t.treeData,"show-icon":"","show-line":"",expandedKeys:t.treeExpandedKeys,selectedKeys:[t.pagination.current]},on:{expand:t.handleTreeExpand,select:t.handleTreeSelect}},[a("a-icon",{staticStyle:{color:"#0c8fcf"},attrs:{slot:"myIcon",type:"unordered-list"},slot:"myIcon"})],1)],1),a("a-col",{attrs:{span:18}},[a("j-vxe-table",{attrs:{"row-number":"","row-selection":"",height:750,loading:t.loading,columns:t.columns,dataSource:t.dataSource,pagination:t.pagination},on:{pageChange:t.handleTablePageChange}})],1)],1)],1)},i=[],o=a("0fea"),l=a("2475"),r={name:"Template5",data:function(){return{loading:!1,pagination:{current:1,pageSize:50,pageSizeOptions:["50"],total:0},selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:l["b"].input},{key:"call",title:"呼叫",width:"80px",type:l["b"].input},{key:"len",title:"长",width:"80px",type:l["b"].input},{key:"ton",title:"吨",width:"120px",type:l["b"].input},{key:"payer",title:"付款方",width:"120px",type:l["b"].input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:l["b"].input},{key:"trend",title:"动向",width:"120px",type:l["b"].input}],treeData:[{title:"1-10页",key:"1-10",children:[{title:"第 1 页",key:1,slots:{icon:"myIcon"}},{title:"第 2 页",key:2,slots:{icon:"myIcon"}},{title:"第 3 页",key:3,slots:{icon:"myIcon"},children:[{title:"第 333 页",key:333,slots:{icon:"myIcon"}},{title:"第 444 页",key:444,slots:{icon:"myIcon"}},{title:"第 555 页",key:555,slots:{icon:"myIcon"}}]},{title:"第 4 页",key:4,slots:{icon:"myIcon"}},{title:"第 5 页",key:5,slots:{icon:"myIcon"}},{title:"第 6 页",key:6,slots:{icon:"myIcon"}},{title:"第 7 页",key:7,slots:{icon:"myIcon"}},{title:"第 8 页",key:8,slots:{icon:"myIcon"}},{title:"第 9 页",key:9,slots:{icon:"myIcon"}},{title:"第 10 页",key:10,slots:{icon:"myIcon"}}],slots:{icon:"myIcon"}},{title:"11-20页",key:"11-20",children:[{title:"第 11 页",key:11,slots:{icon:"myIcon"}},{title:"第 12 页",key:12,slots:{icon:"myIcon"}},{title:"第 13 页",key:13,slots:{icon:"myIcon"}},{title:"第 14 页",key:14,slots:{icon:"myIcon"}},{title:"第 15 页",key:15,slots:{icon:"myIcon"}},{title:"第 16 页",key:16,slots:{icon:"myIcon"}},{title:"第 17 页",key:17,slots:{icon:"myIcon"}},{title:"第 18 页",key:18,slots:{icon:"myIcon"}},{title:"第 19 页",key:19,slots:{icon:"myIcon"}},{title:"第 20 页",key:20,slots:{icon:"myIcon"}}],slots:{icon:"myIcon"}}],treeExpandedKeys:["1-10"],url:{getData:"/mock/vxe/getData"}}},created:function(){this.loadData()},methods:{loadData:function(){var t=this,e={pageNo:this.pagination.current,pageSize:this.pagination.pageSize};this.loading=!0,Object(o["c"])(this.url.getData,e).then((function(e){e.success?(t.pagination.total=e.result.total,t.dataSource=e.result.records,t.selectedRows=[]):t.$error({title:"主表查询失败",content:e.message})})).finally((function(){t.loading=!1}))},handleTablePageChange:function(t){this.pagination.current=t.current,this.pagination.pageSize=t.pageSize,this.loadData(),t.current<=10?this.treeExpandedKeys=["1-10"]:this.treeExpandedKeys=["11-20"]},handleTreeSelect:function(t){var e=t[0];"string"===typeof e?this.treeExpandedKeys=t:(this.pagination.current=e,this.loadData())},handleTreeExpand:function(t){this.treeExpandedKeys=t}}},s=r,c=(a("c2d3"),a("2877")),d=Object(c["a"])(s,n,i,!1,null,null,null);e["default"]=d.exports},da9b:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("j-tree-table",{attrs:{url:t.url,topValue:"0",queryKey:"id",columns:t.columns,tableProps:t.tableProps},scopedSlots:t._u([{key:"action",fn:function(e){return[a("a",{on:{click:function(){return t.handleEdit(e.record)}}},[t._v("编辑")])]}}])})],1)},i=[],o=a("6b87"),l={name:"JeecgTreeTable",components:{JTreeTable:o["default"]},data:function(){return{url:"/mock/api/asynTreeList",columns:[{title:"菜单名称",dataIndex:"name"},{title:"组件",dataIndex:"component"},{title:"排序",dataIndex:"orderNum"},{title:"操作",dataIndex:"action",scopedSlots:{customRender:"action"}}],selectedRowKeys:[]}},computed:{tableProps:function(){var t=this;return{rowSelection:{selectedRowKeys:t.selectedRowKeys,onChange:function(e){return t.selectedRowKeys=e}}}}},methods:{handleEdit:function(t){this.$info({width:600,title:"编辑",content:"编辑ID："+t.id+"；名称："+t.name,okText:"确定",maskClosable:!0})}}},r=l,s=a("2877"),c=Object(s["a"])(r,n,i,!1,null,"00c9c64c",null);e["default"]=c.exports},e313:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{title:"即时保存示例",bordered:!1}},[a("j-vxe-table",{attrs:{toolbar:"",toolbarConfig:t.toolbarConfig,"row-number":"","row-selection":"","keep-source":"","async-remove":"",height:340,loading:t.loading,columns:t.columns,dataSource:t.dataSource,pagination:t.pagination},on:{save:t.handleTableSave,remove:t.handleTableRemove,"edit-closed":t.handleEditClosed,pageChange:t.handlePageChange,selectRowChange:t.handleSelectRowChange}})],1)},i=[],o=a("0fea"),l=a("2475"),r={name:"JSBCDemo",data:function(){return{toolbarConfig:{btn:["add","save","remove","clearSelection"]},loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:l["b"].input},{key:"call",title:"呼叫",width:"80px",type:l["b"].input},{key:"len",title:"长",width:"80px",type:l["b"].input},{key:"ton",title:"吨",width:"120px",defaultValue:233,type:l["b"].input},{key:"payer",title:"付款方",width:"120px",defaultValue:"张三",type:l["b"].input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:l["b"].input},{key:"trend",title:"动向",width:"120px",type:l["b"].input}],url:{getData:"/mock/vxe/getData",saveRow:"/mock/vxe/immediateSaveRow",saveAll:"/mock/vxe/immediateSaveAll"}}},created:function(){this.loadData()},methods:{loadData:function(){var t=this,e={pageNo:this.pagination.current,pageSize:this.pagination.pageSize};this.loading=!0,Object(o["c"])(this.url.getData,e).then((function(e){e.success?(t.pagination.total=e.result.total,t.dataSource=e.result.records,t.selectedRows=[]):t.$error({title:"主表查询失败",content:e.message})})).finally((function(){t.loading=!1}))},handleTableSave:function(t){var e=this,a=t.$table,n=t.target;a.validate().then((function(t){if(!t){var a=n.getTableData();n.getNewData(),n.getDeleteData();e.loading=!0,Object(o["i"])(e.url.saveAll,a).then((function(t){t.success?e.$message.success("保存成功！"):e.$message.warn("保存失败："+t.message)})).finally((function(){e.loading=!1}))}}))},handleTableRemove:function(t){var e=this;t.deleteRows.map((function(t){return t.id}));this.loading=!0,window.setTimeout((function(){e.loading=!1,e.$message.success("删除成功"),t.confirmRemove()}),1e3)},handleEditClosed:function(t){var e=this,a=t.$table,n=t.row,i=t.column,l=i.property;n[l];a.isUpdateByRow(n,l)&&a.validate(n).then((function(t){if(!t){var r=e.$message.loading('正在保存"'.concat(i.title,'"'),0);Object(o["j"])(e.url.saveRow,n).then((function(t){t.success?(e.$message.success('"'.concat(i.title,'"保存成功！')),a.reloadRow(n,null,l)):e.$message.warn('"'.concat(i.title,'"保存失败：')+t.message)})).finally((function(){r()}))}}))},handlePageChange:function(t){this.pagination.current=t.current,this.pagination.pageSize=t.pageSize,this.loadData()},handleSelectRowChange:function(t){this.selectedRows=t.selectedRows}}},s=r,c=a("2877"),d=Object(c["a"])(s,n,i,!1,null,"1ee4436f",null);e["default"]=d.exports},e31a:function(t,e,a){"use strict";var n=a("9b15d"),i=a.n(n);i.a},eff1:function(t,e,a){},f2a1:function(t,e,a){},f6e4:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-tabs",[a("a-tab-pane",{key:"1",attrs:{tab:"基础示例",forceRender:""}},[a("j-vxe-demo1")],1),a("a-tab-pane",{key:"2",attrs:{tab:"高级示例",forceRender:""}},[a("j-vxe-demo2")],1),a("a-tab-pane",{key:"3",attrs:{tab:"联动示例",forceRender:""}},[a("j-vxe-demo3")],1)],1)],1)},i=[],o=a("048b"),l=a("87a0"),r=a("62cb"),s={name:"JVXETableDemo",components:{JVxeDemo2:l["default"],JVxeDemo1:o["default"],JVxeDemo3:r["default"]},data:function(){return{}},methods:{}},c=s,d=a("2877"),p=Object(d["a"])(c,n,i,!1,null,"65ac2de3",null);e["default"]=p.exports},ff2a:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-row",{staticStyle:{"margin-top":"20px"}},[a("a-col",{attrs:{md:2,sm:4}},[a("a-select",{staticStyle:{width:"90px"},attrs:{defaultValue:"POST",size:"large"},on:{change:t.handleChange}},[a("a-select-option",{attrs:{value:"POST"}},[t._v("POST")]),a("a-select-option",{attrs:{value:"GET"}},[t._v("GET")]),a("a-select-option",{attrs:{value:"PUT"}},[t._v("PUT")]),a("a-select-option",{attrs:{value:"DELETE"}},[t._v("DELETE")])],1)],1),a("a-col",{attrs:{md:22,sm:20}},[a("a-input-search",{attrs:{placeholder:"input send url",enterButton:"Send",size:"large"},on:{search:t.onSearch},model:{value:t.url,callback:function(e){t.url=e},expression:"url"}})],1)],1),a("a-tabs",{attrs:{defaultActiveKey:"2"}},[a("a-tab-pane",{key:"2",attrs:{tab:"params"}},[a("textarea",{staticStyle:{width:"100%","font-size":"16px","font-weight":"500"},attrs:{rows:13},on:{blur:t.changeVal}})])],1),a("a-tabs",{attrs:{defaultActiveKey:"1"}},[a("a-tab-pane",{key:"1",attrs:{tab:"response"}},[a("textarea",{staticStyle:{width:"100%","font-size":"16px","font-weight":"500"},attrs:{rows:10,readOnly:""},domProps:{innerHTML:t._s(t.resultJson)}})])],1)],1)},i=[],o=a("b775"),l=a("9fb0"),r=a("2b0e"),s={name:"FlowTest",data:function(){return{url:"",paramJson:"",resultJson:{},requestMethod:"POST"}},methods:{onSearch:function(t){var e=this,a=this;if(!t)return a.$message.error("请填写路径"),!1;this.resultJson={},Object(o["b"])({url:t,method:this.requestMethod,data:this.paramJson}).then((function(t){e.resultJson=t})).catch((function(t){a.$message.error("请求异常："+t)}))},changeVal:function(t){try{var e=t.target.value;e.indexOf(",}")>0&&(e=e.replace(",}","}")),this.paramJson=JSON.parse(e)}catch(t){this.$message.error("非法的JSON字符串")}},handleChange:function(t){this.requestMethod=t},created:function(){var t=r["default"].ls.get(l["a"]);this.headers={"X-Access-Token":t}}}},c=s,d=a("2877"),p=Object(d["a"])(c,n,i,!1,null,null,null);e["default"]=p.exports}}]);