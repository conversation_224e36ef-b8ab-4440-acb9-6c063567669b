(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~d9cf8aa0"],{"013f":function(e,t,a){},"1d6a":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"main"},[s("a-form",{staticStyle:{"max-width":"500px",margin:"40px auto 0"},attrs:{form:e.form},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.nextStep(t)}}},[s("a-form-item",[s("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["username",e.validatorRules.username],expression:"['username',validatorRules.username]"}],attrs:{size:"large",type:"text",autocomplete:"false",placeholder:"请输入用户账号或手机号"}},[s("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1),s("a-row",{attrs:{gutter:0}},[s("a-col",{attrs:{span:14}},[s("a-form-item",[s("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["inputCode",e.validatorRules.inputCode],expression:"['inputCode',validatorRules.inputCode]"}],attrs:{size:"large",type:"text",placeholder:"请输入验证码"},on:{change:e.inputCodeChange}},[e.inputCodeContent==e.verifiedCode?s("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"smile"},slot:"prefix"}):s("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"frown"},slot:"prefix"})],1)],1)],1),s("a-col",{staticStyle:{"text-align":"right"},attrs:{span:10}},[e.requestCodeSuccess?s("img",{staticStyle:{"margin-top":"2px"},attrs:{src:e.randCodeImage},on:{click:e.handleChangeCheckCode}}):s("img",{staticStyle:{"margin-top":"2px"},attrs:{src:a("d5ac")},on:{click:e.handleChangeCheckCode}})])],1),s("a-form-item",{attrs:{wrapperCol:{span:19,offset:5}}},[s("router-link",{staticStyle:{float:"left","line-height":"40px"},attrs:{to:{name:"login"}}},[e._v("使用已有账户登录")]),s("a-button",{attrs:{type:"primary"},on:{click:e.nextStep}},[e._v("下一步")])],1)],1)],1)},n=[],r=a("0fea"),o=a("4ec3"),i={name:"Step1",data:function(){return{form:this.$form.createForm(this),inputCodeContent:"",inputCodeNull:!0,verifiedCode:"",validatorRules:{username:{rules:[{required:!1},{validator:this.validateInputUsername}]},inputCode:{rules:[{required:!0,message:"请输入验证码!"}]}},randCodeImage:"",requestCodeSuccess:!0,currdatetime:""}},created:function(){this.handleChangeCheckCode()},methods:{handleChangeCheckCode:function(){var e=this;this.currdatetime=(new Date).getTime(),Object(r["c"])("/sys/randomImage/".concat(this.currdatetime)).then((function(t){t.success?(e.randCodeImage=t.result,e.requestCodeSuccess=!0):(e.$message.error(t.message),e.requestCodeSuccess=!1)})).catch((function(){e.requestCodeSuccess=!1}))},nextStep:function(){var e=this;this.form.validateFields((function(t,a){if(!t){var s=!1,n={},o=/^[1-9]\d*$|^0$/,i=a.username;1==o.test(i)?(n.phone=i,s=!0):n.username=i,e.validateInputCode().then((function(){Object(r["c"])("/sys/user/querySysUser",n).then((function(t){if(t.success){var a={username:t.result.username,phone:t.result.phone,isPhone:s};setTimeout((function(){e.$emit("nextStep",a)}))}}))}))}}))},validateInputCode:function(){var e=this;return new Promise((function(t,a){Object(r["i"])("/sys/checkCaptcha",{captcha:e.inputCodeContent,checkKey:e.currdatetime}).then((function(s){s.success?t():(e.$message.error(s.message),a())}))}))},inputCodeChange:function(e){this.inputCodeContent=e.target.value,e.target.value&&0!=e.target.value?(this.inputCodeContent=this.inputCodeContent.toLowerCase(),this.inputCodeNull=!1):this.inputCodeNull=!0},generateCode:function(e){this.verifiedCode=e.toLowerCase()},validateInputUsername:function(e,t,a){var s=/^[0-9]+.?[0-9]*/;if(t||a("请输入用户名和手机号！"),s.test(t)){var n={phone:t};Object(o["h"])(n).then((function(e){e.success?a("用户名不存在!"):a()}))}else{n={username:t};Object(o["h"])(n).then((function(e){e.success?a("用户名不存在!"):a()}))}}}},c=i,l=a("2877"),u=Object(l["a"])(c,s,n,!1,null,"5a355e54",null);t["default"]=u.exports},5193:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},n=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{attrs:{id:"loader-wrapper"}},[a("div",{attrs:{id:"loader"}}),a("div",{staticClass:"loader-section section-left"}),a("div",{staticClass:"loader-section section-right"}),a("div",{staticClass:"load_title"},[e._v("正在登录 智界AIGC，请耐心等待")])])])}],r=a("2f62"),o=a("ca00"),i=a("9fb0");function c(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,s)}return a}function l(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?c(Object(a),!0).forEach((function(t){u(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function u(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var d={name:"OAuth2Login",data:function(){return{env:{thirdApp:!1,wxWork:!1,dingtalk:!1}}},beforeCreate:function(){Object(o["h"])()||this.$router.replace({path:"/user/login"})},created:function(){this.checkEnv(),this.doOAuth2Login()},methods:l(l({},Object(r["b"])(["ThirdLogin"])),{},{checkEnv:function(){/wxwork/i.test(navigator.userAgent)&&(this.env.thirdApp=!0,this.env.wxWork=!0),/dingtalk/i.test(navigator.userAgent)&&(this.env.thirdApp=!0,this.env.dingtalk=!0)},doOAuth2Login:function(){if(this.env.thirdApp)if(this.$route.query.oauth2LoginToken){this.thirdType=this.$route.query.thirdType;var e=this.$route.query.oauth2LoginToken;this.doThirdLogin(e)}else this.env.wxWork?this.doWechatEnterpriseOAuth2Login():this.env.dingtalk&&this.doDingTalkOAuth2Login()},doThirdLogin:function(e){var t=this,a={};a.thirdType=this.thirdType,a.token=e,this.ThirdLogin(a).then((function(e){e.success?t.loginSuccess():t.requestFailed(e)}))},loginSuccess:function(){this.$router.replace({path:i["n"]}),this.$notification.success({message:"欢迎",description:"".concat(Object(o["o"])(),"，欢迎回来")})},requestFailed:function(e){this.$error({title:"登录失败",content:((e.response||{}).data||{}).message||e.message||"请求出现错误，请稍后再试",okText:"重新登陆",onOk:function(){window.location.reload()},onCancel:function(){window.location.reload()}})},doWechatEnterpriseOAuth2Login:function(){this.sysOAuth2Login("wechat_enterprise")},doDingTalkOAuth2Login:function(){this.sysOAuth2Login("dingtalk")},sysOAuth2Login:function(e){var t="".concat(window._CONFIG["domianURL"],"/sys/thirdLogin/oauth2/").concat(e,"/login");t+="?state=".concat(encodeURIComponent(window.location.origin)),window.location.href=t}})},p=d,m=a("2877"),f=Object(m["a"])(p,s,n,!1,null,"00fca87c",null);t["default"]=f.exports},"5cfa":function(e,t,a){},8859:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{staticStyle:{width:"130%","text-align":"center","margin-left":"-10%"},attrs:{bordered:!1}},[a("a-steps",{staticClass:"steps",attrs:{current:e.currentTab}},[a("a-step",{attrs:{title:"手机验证"}}),a("a-step",{attrs:{title:"更改密码"}}),a("a-step",{attrs:{title:"完成"}})],1),a("div",{staticClass:"content"},[0===e.currentTab?a("step2",{on:{nextStep:e.nextStep}}):e._e(),1===e.currentTab?a("step3",{attrs:{userList:e.userList},on:{nextStep:e.nextStep,prevStep:e.prevStep}}):e._e(),2===e.currentTab?a("step4",{attrs:{userList:e.userList},on:{prevStep:e.prevStep,finish:e.finish}}):e._e()],1)],1)},n=[],r=a("a73d"),o=a("977f"),i=a("b23d"),c={name:"Alteration",components:{Step2:r["default"],Step3:o["default"],Step4:i["default"]},data:function(){return{description:"将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。",currentTab:0,userList:{},form:null}},methods:{nextStep:function(e){this.userList=e,this.currentTab<4&&(this.currentTab+=1)},prevStep:function(e){this.userList=e,this.currentTab>0&&(this.currentTab-=1)},finish:function(){this.currentTab=0}}},l=c,u=(a("c4f8"),a("2877")),d=Object(u["a"])(l,s,n,!1,null,"8733cc9e",null);t["default"]=d.exports},"8ef0":function(e,t,a){"use strict";var s=a("013f"),n=a.n(s);n.a},"977f":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("a-form-model",{ref:"form",staticClass:"password-retrieval-form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",e._b({attrs:{label:"账号名"}},"a-form-model-item",e.layout,!1),[a("a-input",{attrs:{type:"text",value:e.accountName,disabled:""}})],1),a("a-form-model-item",e._b({staticClass:"stepFormText",attrs:{prop:"password",label:"新密码"}},"a-form-model-item",e.layout,!1),[a("a-input",{attrs:{type:"password",autocomplete:"false"},model:{value:e.model.password,callback:function(t){e.$set(e.model,"password",t)},expression:"model.password"}})],1),a("a-form-model-item",e._b({staticClass:"stepFormText",attrs:{prop:"confirmPassword",label:"确认密码"}},"a-form-model-item",e.layout,!1),[a("a-input",{attrs:{type:"password",autocomplete:"false"},model:{value:e.model.confirmPassword,callback:function(t){e.$set(e.model,"confirmPassword",t)},expression:"model.confirmPassword"}})],1),a("a-form-model-item",{attrs:{wrapperCol:{span:19,offset:5}}},[a("a-button",{staticStyle:{"margin-left":"8px"},on:{click:e.prevStep}},[e._v("上一步")]),a("a-button",{staticStyle:{"margin-left":"20px"},attrs:{loading:e.loading,type:"primary"},on:{click:e.nextStep}},[e._v("提交")])],1)],1)],1)},n=[],r=a("0fea"),o={name:"Step3",props:["userList"],data:function(){return{model:{},layout:{labelCol:{span:5},wrapperCol:{span:19}},loading:!1,accountName:this.userList.username,validatorRules:{password:[{required:!0,pattern:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{8,}$/,message:"密码由8位数字、大小写字母和特殊符号组成!!"}],confirmPassword:[{required:!0,message:"密码不能为空!"},{validator:this.handlePasswordCheck}]}}},methods:{nextStep:function(){var e=this;e.loading=!0,e.$refs["form"].validate((function(t){if(!0===t){var a={username:e.userList.username,password:e.model.password,smscode:e.userList.smscode,phone:e.userList.phone};Object(r["c"])("/sys/user/passwordChange",a).then((function(t){if(t.success){var a={username:e.userList.username};setTimeout((function(){e.$emit("nextStep",a)}),1500)}else e.passwordFailed(t.message),e.loading=!1}))}else e.loading=!1}))},prevStep:function(){this.$emit("prevStep",this.userList)},handlePasswordCheck:function(e,t,a){var s=this.model["password"];t&&s&&t.trim()!==s.trim()&&a(new Error("两次密码不一致")),a()},passwordFailed:function(e){this.$notification["error"]({message:"更改密码失败",description:e,duration:4})}}},i=o,c=(a("f55e"),a("2877")),l=Object(c["a"])(i,s,n,!1,null,"3c0160d7",null);t["default"]=l.exports},a2cc:function(e,t,a){},a73d:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("a-form-model",{ref:"form",staticClass:"password-retrieval-form",attrs:{model:e.model,rules:e.validatorRules},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.nextStep(t)}}},[a("a-form-model-item",{attrs:{label:"手机",required:"",prop:"phone",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:20}},[a("a-input",{attrs:{type:"text",autocomplete:"false",placeholder:"请输入手机号"},model:{value:e.model.phone,callback:function(t){e.$set(e.model,"phone",t)},expression:"model.phone"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"phone"},slot:"prefix"})],1)],1)],1)],1),e.show?a("a-form-model-item",{attrs:{required:"",prop:"captcha",label:"验证码",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:12}},[a("a-input",{attrs:{type:"text",placeholder:"手机短信验证码"},model:{value:e.model.captcha,callback:function(t){e.$set(e.model,"captcha",t)},expression:"model.captcha"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"code"},slot:"prefix"})],1)],1),a("a-col",{staticClass:"gutter-row",attrs:{span:8}},[a("a-button",{attrs:{tabindex:"-1",size:"default",disabled:e.state.smsSendBtn},domProps:{textContent:e._s(e.state.smsSendBtn?e.state.time+" s":"获取验证码")},on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.getCaptcha(t)}}})],1)],1)],1):e._e(),a("a-form-model-item",{attrs:{wrapperCol:{span:19,offset:5}}},[a("router-link",{staticStyle:{float:"left","line-height":"40px"},attrs:{to:{name:"login"}}},[e._v("使用已有账户登录")]),a("a-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.nextStep}},[e._v("下一步")])],1)],1)],1)},n=[],r=a("0fea"),o={name:"Step2",props:["userList"],data:function(){return{model:{},loading:!1,dropList:"0",captcha:"",show:!0,state:{time:60,smsSendBtn:!1},formLogin:{captcha:"",mobile:""},validatorRules:{phone:[{required:!0,message:"请输入手机号码!"},{validator:this.validatePhone}],captcha:[{required:!0,message:"请输入短信验证码!"}]}}},computed:{},methods:{nextStep:function(){var e=this,t=this;t.loading=!0,this.$refs["form"].validate((function(a){if(1==a){var s={phone:e.model.phone,smscode:e.model.captcha};Object(r["i"])("/sys/user/phoneVerification",s).then((function(a){if(a.success){var n={username:a.result.username,phone:s.phone,smscode:a.result.smscode};setTimeout((function(){t.$emit("nextStep",n)}),0)}else e.cmsFailed(a.message)}))}}))},getCaptcha:function(e){e.preventDefault();var t=this;t.$refs["form"].validateField("phone",(function(e){if(e)t.cmsFailed(e);else{t.state.smsSendBtn=!0;var a=window.setInterval((function(){t.state.time--<=0&&(t.state.time=60,t.state.smsSendBtn=!1,window.clearInterval(a))}),1e3),s=t.$message.loading("验证码发送中..",0),n={mobile:t.model.phone,smsmode:"2"};Object(r["i"])("/sys/sms",n).then((function(e){e.success||(setTimeout(s,1),t.cmsFailed(e.message)),setTimeout(s,500)}))}}))},cmsFailed:function(e){this.$notification["error"]({message:"验证错误",description:e,duration:4})},handleChangeSelect:function(e){var t=this;0==e?(t.dropList="0",t.show=!0):(t.dropList="1",t.show=!1)},validatePhone:function(e,t,a){if(t){var s=/^[1][3,4,5,7,8][0-9]{9}$/;s.test(t)?a():a("请输入正确的手机号")}else a()}}},i=o,c=(a("8ef0"),a("2877")),l=Object(c["a"])(i,s,n,!1,null,"14343278",null);t["default"]=l.exports},aa79:function(e,t,a){},b23d:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("a-form",{staticStyle:{margin:"40px auto 0"}},[a("result",{attrs:{title:"更改密码成功","is-success":!0}},[a("div",{staticClass:"toLogin"},[a("h3",[e._v("将在"),a("span",[e._v(e._s(e.time))]),e._v("秒后返回登录页面.")])])])],1)],1)},n=[],r=a("9a3d"),o={name:"Step4",props:["userList"],components:{Result:r["default"]},data:function(){return{loading:!1,time:0}},methods:{countDown:function(){var e=this;e.time--}},mounted:function(){var e=this;e.time=5,setInterval(e.countDown,1e3)},watch:{time:function(e,t){if(0==e){var a={username:this.userList.username};this.$router.push({name:"login",params:a})}}}},i=o,c=(a("d8b8"),a("2877")),l=Object(c["a"])(i,s,n,!1,null,"0ac9a29e",null);t["default"]=l.exports},c4f8:function(e,t,a){"use strict";var s=a("aa79"),n=a.n(s);n.a},d8b8:function(e,t,a){"use strict";var s=a("a2cc"),n=a.n(s);n.a},f55e:function(e,t,a){"use strict";var s=a("5cfa"),n=a.n(s);n.a}}]);