(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~35f50c21"],{"030f":function(t,n,e){(function(t){(function(){"use strict";var n=function(t){var n=typeof t;return null===t?"null":"object"===n&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"===n&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":n},e=function(t){return function(e){return n(e)===t}},o=function(t){return function(n){return typeof n===t}},r=function(t){return function(n){return t===n}},i=e("string"),u=e("object"),a=e("array"),c=r(null),s=o("boolean"),f=r(void 0),l=function(t){return null===t||void 0===t},d=function(t){return!l(t)},m=o("function"),g=o("number"),p=function(t,n){if(a(t)){for(var e=0,o=t.length;e<o;++e)if(!n(t[e]))return!1;return!0}return!1},h=function(){},v=function(t){return function(){return t()}},b=function(t,n){return function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];return t(n.apply(null,e))}},y=function(t,n){return function(e){return t(n(e))}},x=function(t){return function(){return t}},w=function(t){return t},S=function(t,n){return t===n};function k(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];return function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];var r=n.concat(e);return t.apply(null,r)}}var C=function(t){return function(n){return!t(n)}},O=function(t){return function(){throw new Error(t)}},_=x(!1),T=x(!0),E=tinymce.util.Tools.resolve("tinymce.ThemeManager"),D=function(){return D=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e],n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t},D.apply(this,arguments)};function A(t,n){var e={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&n.indexOf(o)<0&&(e[o]=t[o]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(t);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(e[o[r]]=t[o[r]])}return e}function B(t,n,e){if(e||2===arguments.length)for(var o,r=0,i=n.length;r<i;r++)!o&&r in n||(o||(o=Array.prototype.slice.call(n,0,r)),o[r]=n[r]);return t.concat(o||Array.prototype.slice.call(n))}var M=function(){return F},F=function(){var t=function(t){return t()},n=w,e={fold:function(t,n){return t()},isSome:_,isNone:T,getOr:n,getOrThunk:t,getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:x(null),getOrUndefined:x(void 0),or:n,orThunk:t,map:M,each:h,bind:M,exists:_,forall:T,filter:function(){return M()},toArray:function(){return[]},toString:x("none()")};return e}(),I=function(t){var n=x(t),e=function(){return r},o=function(n){return n(t)},r={fold:function(n,e){return e(t)},isSome:T,isNone:_,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return I(n(t))},each:function(n){n(t)},bind:o,exists:o,forall:o,filter:function(n){return n(t)?r:F},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return r},R=function(t){return null===t||void 0===t?F:I(t)},N={some:I,none:M,from:R},P=Array.prototype.slice,H=Array.prototype.indexOf,V=Array.prototype.push,L=function(t,n){return H.call(t,n)},z=function(t,n){var e=L(t,n);return-1===e?N.none():N.some(e)},U=function(t,n){return L(t,n)>-1},j=function(t,n){for(var e=0,o=t.length;e<o;e++){var r=t[e];if(n(r,e))return!0}return!1},W=function(t,n){for(var e=[],o=0;o<t;o++)e.push(n(o));return e},G=function(t,n){for(var e=[],o=0;o<t.length;o+=n){var r=P.call(t,o,o+n);e.push(r)}return e},X=function(t,n){for(var e=t.length,o=new Array(e),r=0;r<e;r++){var i=t[r];o[r]=n(i,r)}return o},Y=function(t,n){for(var e=0,o=t.length;e<o;e++){var r=t[e];n(r,e)}},q=function(t,n){for(var e=t.length-1;e>=0;e--){var o=t[e];n(o,e)}},K=function(t,n){for(var e=[],o=[],r=0,i=t.length;r<i;r++){var u=t[r],a=n(u,r)?e:o;a.push(u)}return{pass:e,fail:o}},J=function(t,n){for(var e=[],o=0,r=t.length;o<r;o++){var i=t[o];n(i,o)&&e.push(i)}return e},$=function(t,n,e){return q(t,(function(t,o){e=n(e,t,o)})),e},Q=function(t,n,e){return Y(t,(function(t,o){e=n(e,t,o)})),e},Z=function(t,n,e){for(var o=0,r=t.length;o<r;o++){var i=t[o];if(n(i,o))return N.some(i);if(e(i,o))break}return N.none()},tt=function(t,n){return Z(t,n,_)},nt=function(t,n){for(var e=0,o=t.length;e<o;e++){var r=t[e];if(n(r,e))return N.some(e)}return N.none()},et=function(t){for(var n=[],e=0,o=t.length;e<o;++e){if(!a(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);V.apply(n,t[e])}return n},ot=function(t,n){return et(X(t,n))},rt=function(t,n){for(var e=0,o=t.length;e<o;++e){var r=t[e];if(!0!==n(r,e))return!1}return!0},it=function(t){var n=P.call(t,0);return n.reverse(),n},ut=function(t,n){return J(t,(function(t){return!U(n,t)}))},at=function(t,n){for(var e={},o=0,r=t.length;o<r;o++){var i=t[o];e[String(i)]=n(i,o)}return e},ct=function(t){return[t]},st=function(t,n){var e=P.call(t,0);return e.sort(n),e},ft=function(t,n){return n>=0&&n<t.length?N.some(t[n]):N.none()},lt=function(t){return ft(t,0)},dt=function(t){return ft(t,t.length-1)},mt=m(Array.from)?Array.from:function(t){return P.call(t)},gt=function(t,n){for(var e=0;e<t.length;e++){var o=n(t[e],e);if(o.isSome())return o}return N.none()},pt=Object.keys,ht=Object.hasOwnProperty,vt=function(t,n){for(var e=pt(t),o=0,r=e.length;o<r;o++){var i=e[o],u=t[i];n(u,i)}},bt=function(t,n){return yt(t,(function(t,e){return{k:e,v:n(t,e)}}))},yt=function(t,n){var e={};return vt(t,(function(t,o){var r=n(t,o);e[r.k]=r.v})),e},xt=function(t){return function(n,e){t[e]=n}},wt=function(t,n,e,o){var r={};return vt(t,(function(t,r){(n(t,r)?e:o)(t,r)})),r},St=function(t,n){var e={};return wt(t,n,xt(e),h),e},kt=function(t,n){var e=[];return vt(t,(function(t,o){e.push(n(t,o))})),e},Ct=function(t,n){for(var e=pt(t),o=0,r=e.length;o<r;o++){var i=e[o],u=t[i];if(n(u,i,t))return N.some(u)}return N.none()},Ot=function(t){return kt(t,w)},_t=function(t,n){return Tt(t,n)?N.from(t[n]):N.none()},Tt=function(t,n){return ht.call(t,n)},Et=function(t,n){return Tt(t,n)&&void 0!==t[n]&&null!==t[n]},Dt=function(t,n,e){return void 0===e&&(e=S),t.exists((function(t){return e(t,n)}))},At=function(t,n,e){return void 0===e&&(e=S),Ft(t,n,e).getOr(t.isNone()&&n.isNone())},Bt=function(t){for(var n=[],e=function(t){n.push(t)},o=0;o<t.length;o++)t[o].each(e);return n},Mt=function(t){for(var n=[],e=0;e<t.length;e++){var o=t[e];if(!o.isSome())return N.none();n.push(o.getOrDie())}return N.some(n)},Ft=function(t,n,e){return t.isSome()&&n.isSome()?N.some(e(t.getOrDie(),n.getOrDie())):N.none()},It=function(t,n,e,o){return t.isSome()&&n.isSome()&&e.isSome()?N.some(o(t.getOrDie(),n.getOrDie(),e.getOrDie())):N.none()},Rt=function(t,n){return void 0!==t&&null!==t?N.some(n(t)):N.none()},Nt=function(t,n){return t?N.some(n):N.none()},Pt=function(t,n){return t+n},Ht=function(t,n){return t.substring(n)},Vt=function(t,n,e){return""===n||t.length>=n.length&&t.substr(e,e+n.length)===n},Lt=function(t,n){return jt(t,n)?Ht(t,n.length):t},zt=function(t,n){return Wt(t,n)?t:Pt(t,n)},Ut=function(t,n){return-1!==t.indexOf(n)},jt=function(t,n){return Vt(t,n,0)},Wt=function(t,n){return Vt(t,n,t.length-n.length)},Gt=function(t){return function(n){return n.replace(t,"")}},Xt=Gt(/^\s+|\s+$/g),Yt=function(t){return t.length>0},qt=function(t){return!Yt(t)},Kt=function(t){return void 0!==t.style&&m(t.style.getPropertyValue)},Jt=function(t,n){var e=n||document,o=e.createElement("div");if(o.innerHTML=t,!o.hasChildNodes()||o.childNodes.length>1)throw new Error("HTML must have a single root node");return Zt(o.childNodes[0])},$t=function(t,n){var e=n||document,o=e.createElement(t);return Zt(o)},Qt=function(t,n){var e=n||document,o=e.createTextNode(t);return Zt(o)},Zt=function(t){if(null===t||void 0===t)throw new Error("Node cannot be null or undefined");return{dom:t}},tn=function(t,n,e){return N.from(t.dom.elementFromPoint(n,e)).map(Zt)},nn={fromHtml:Jt,fromTag:$t,fromText:Qt,fromDom:Zt,fromPoint:tn};"undefined"!==typeof window?window:Function("return this;")();var en,on=9,rn=11,un=1,an=3,cn=function(t){var n=t.dom.nodeName;return n.toLowerCase()},sn=function(t){return t.dom.nodeType},fn=function(t){return function(n){return sn(n)===t}},ln=fn(un),dn=fn(an),mn=fn(on),gn=fn(rn),pn=function(t){var n,e=!1;return function(){for(var o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];return e||(e=!0,n=t.apply(null,o)),n}},hn=function(t,n,e,o){var r=t.isiOS()&&!0===/ipad/i.test(e),i=t.isiOS()&&!r,u=t.isiOS()||t.isAndroid(),a=u||o("(pointer:coarse)"),c=r||!i&&u&&o("(min-device-width:768px)"),s=i||u&&!c,f=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(e),l=!s&&!c&&!f;return{isiPad:x(r),isiPhone:x(i),isTablet:x(c),isPhone:x(s),isTouch:x(a),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:x(f),isDesktop:x(l)}},vn=function(t,n){for(var e=0;e<t.length;e++){var o=t[e];if(o.test(n))return o}},bn=function(t,n){var e=vn(t,n);if(!e)return{major:0,minor:0};var o=function(t){return Number(n.replace(e,"$"+t))};return wn(o(1),o(2))},yn=function(t,n){var e=String(n).toLowerCase();return 0===t.length?xn():bn(t,e)},xn=function(){return wn(0,0)},wn=function(t,n){return{major:t,minor:n}},Sn={nu:wn,detect:yn,unknown:xn},kn=function(t,n){return gt(n.brands,(function(n){var e=n.brand.toLowerCase();return tt(t,(function(t){var n;return e===(null===(n=t.brand)||void 0===n?void 0:n.toLowerCase())})).map((function(t){return{current:t.name,version:Sn.nu(parseInt(n.version,10),0)}}))}))},Cn=function(t,n){var e=String(n).toLowerCase();return tt(t,(function(t){return t.search(e)}))},On=function(t,n){return Cn(t,n).map((function(t){var e=Sn.detect(t.versionRegexes,n);return{current:t.name,version:e}}))},_n=function(t,n){return Cn(t,n).map((function(t){var e=Sn.detect(t.versionRegexes,n);return{current:t.name,version:e}}))},Tn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,En=function(t){return function(n){return Ut(n,t)}},Dn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return Ut(t,"edge/")&&Ut(t,"chrome")&&Ut(t,"safari")&&Ut(t,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Tn],search:function(t){return Ut(t,"chrome")&&!Ut(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return Ut(t,"msie")||Ut(t,"trident")}},{name:"Opera",versionRegexes:[Tn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:En("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:En("firefox")},{name:"Safari",versionRegexes:[Tn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(Ut(t,"safari")||Ut(t,"mobile/"))&&Ut(t,"applewebkit")}}],An=[{name:"Windows",search:En("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return Ut(t,"iphone")||Ut(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:En("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:En("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:En("linux"),versionRegexes:[]},{name:"Solaris",search:En("sunos"),versionRegexes:[]},{name:"FreeBSD",search:En("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:En("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Bn={browsers:x(Dn),oses:x(An)},Mn="Edge",Fn="Chrome",In="IE",Rn="Opera",Nn="Firefox",Pn="Safari",Hn=function(){return Vn({current:void 0,version:Sn.unknown()})},Vn=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isEdge:o(Mn),isChrome:o(Fn),isIE:o(In),isOpera:o(Rn),isFirefox:o(Nn),isSafari:o(Pn)}},Ln={unknown:Hn,nu:Vn,edge:x(Mn),chrome:x(Fn),ie:x(In),opera:x(Rn),firefox:x(Nn),safari:x(Pn)},zn="Windows",Un="iOS",jn="Android",Wn="Linux",Gn="OSX",Xn="Solaris",Yn="FreeBSD",qn="ChromeOS",Kn=function(){return Jn({current:void 0,version:Sn.unknown()})},Jn=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isWindows:o(zn),isiOS:o(Un),isAndroid:o(jn),isOSX:o(Gn),isLinux:o(Wn),isSolaris:o(Xn),isFreeBSD:o(Yn),isChromeOS:o(qn)}},$n={unknown:Kn,nu:Jn,windows:x(zn),ios:x(Un),android:x(jn),linux:x(Wn),osx:x(Gn),solaris:x(Xn),freebsd:x(Yn),chromeos:x(qn)},Qn=function(t,n,e){var o=Bn.browsers(),r=Bn.oses(),i=n.bind((function(t){return kn(o,t)})).orThunk((function(){return On(o,t)})).fold(Ln.unknown,Ln.nu),u=_n(r,t).fold($n.unknown,$n.nu),a=hn(u,i,t,e);return{browser:i,os:u,deviceType:a}},Zn={detect:Qn},te=function(t){return window.matchMedia(t).matches},ne=pn((function(){return Zn.detect(navigator.userAgent,N.from(navigator.userAgentData),te)})),ee=function(){return ne()},oe=function(t,n,e){return 0!==(t.compareDocumentPosition(n)&e)},re=function(t,n){return oe(t,n,Node.DOCUMENT_POSITION_CONTAINED_BY)},ie=function(t,n){var e=t.dom;if(e.nodeType!==un)return!1;var o=e;if(void 0!==o.matches)return o.matches(n);if(void 0!==o.msMatchesSelector)return o.msMatchesSelector(n);if(void 0!==o.webkitMatchesSelector)return o.webkitMatchesSelector(n);if(void 0!==o.mozMatchesSelector)return o.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},ue=function(t){return t.nodeType!==un&&t.nodeType!==on&&t.nodeType!==rn||0===t.childElementCount},ae=function(t,n){var e=void 0===n?document:n.dom;return ue(e)?[]:X(e.querySelectorAll(t),nn.fromDom)},ce=function(t,n){var e=void 0===n?document:n.dom;return ue(e)?N.none():N.from(e.querySelector(t)).map(nn.fromDom)},se=function(t,n){return t.dom===n.dom},fe=function(t,n){var e=t.dom,o=n.dom;return e!==o&&e.contains(o)},le=function(t,n){return re(t.dom,n.dom)},de=function(t,n){return ee().browser.isIE()?le(t,n):fe(t,n)},me=function(t){return nn.fromDom(t.dom.ownerDocument)},ge=function(t){return mn(t)?t:me(t)},pe=function(t){return nn.fromDom(ge(t).dom.documentElement)},he=function(t){return nn.fromDom(ge(t).dom.defaultView)},ve=function(t){return N.from(t.dom.parentNode).map(nn.fromDom)},be=function(t){return ve(t)},ye=function(t){return N.from(t.dom.offsetParent).map(nn.fromDom)},xe=function(t){return N.from(t.dom.nextSibling).map(nn.fromDom)},we=function(t){return X(t.dom.childNodes,nn.fromDom)},Se=function(t,n){var e=t.dom.childNodes;return N.from(e[n]).map(nn.fromDom)},ke=function(t){return Se(t,0)},Ce=function(t,n){return{element:t,offset:n}},Oe=function(t,n){var e=we(t);return e.length>0&&n<e.length?Ce(e[n],0):Ce(t,n)},_e=function(t){return gn(t)&&d(t.dom.host)},Te=m(Element.prototype.attachShadow)&&m(Node.prototype.getRootNode),Ee=x(Te),De=Te?function(t){return nn.fromDom(t.dom.getRootNode())}:ge,Ae=function(t){return _e(t)?t:nn.fromDom(ge(t).dom.body)},Be=function(t){return Me(t).isSome()},Me=function(t){var n=De(t);return _e(n)?N.some(n):N.none()},Fe=function(t){return nn.fromDom(t.dom.host)},Ie=function(t){if(Ee()&&d(t.target)){var n=nn.fromDom(t.target);if(ln(n)&&Re(n)&&t.composed&&t.composedPath){var e=t.composedPath();if(e)return lt(e)}}return N.from(t.target)},Re=function(t){return d(t.dom.shadowRoot)},Ne=function(t){var n=dn(t)?t.dom.parentNode:t.dom;if(void 0===n||null===n||null===n.ownerDocument)return!1;var e=n.ownerDocument;return Me(nn.fromDom(n)).fold((function(){return e.body.contains(n)}),y(Ne,Fe))},Pe=function(){return He(nn.fromDom(document))},He=function(t){var n=t.dom.body;if(null===n||void 0===n)throw new Error("Body is not available yet");return nn.fromDom(n)},Ve=function(t,n,e){if(!(i(e)||s(e)||g(e)))throw new Error("Attribute value was not simple");t.setAttribute(n,e+"")},Le=function(t,n,e){Ve(t.dom,n,e)},ze=function(t,n){var e=t.dom;vt(n,(function(t,n){Ve(e,n,t)}))},Ue=function(t,n){var e=t.dom.getAttribute(n);return null===e?void 0:e},je=function(t,n){return N.from(Ue(t,n))},We=function(t,n){var e=t.dom;return!(!e||!e.hasAttribute)&&e.hasAttribute(n)},Ge=function(t,n){t.dom.removeAttribute(n)},Xe=function(t,n,e){if(!i(e))throw new Error("CSS value must be a string: "+e);Kt(t)&&t.style.setProperty(n,e)},Ye=function(t,n){Kt(t)&&t.style.removeProperty(n)},qe=function(t,n,e){var o=t.dom;Xe(o,n,e)},Ke=function(t,n){var e=t.dom;vt(n,(function(t,n){Xe(e,n,t)}))},Je=function(t,n){var e=t.dom;vt(n,(function(t,n){t.fold((function(){Ye(e,n)}),(function(t){Xe(e,n,t)}))}))},$e=function(t,n){var e=t.dom,o=window.getComputedStyle(e),r=o.getPropertyValue(n);return""!==r||Ne(t)?r:Qe(e,n)},Qe=function(t,n){return Kt(t)?t.style.getPropertyValue(n):""},Ze=function(t,n){var e=t.dom,o=Qe(e,n);return N.from(o).filter((function(t){return t.length>0}))},to=function(t){var n={},e=t.dom;if(Kt(e))for(var o=0;o<e.style.length;o++){var r=e.style.item(o);n[r]=e.style[r]}return n},no=function(t,n,e){var o=nn.fromTag(t);qe(o,n,e);var r=Ze(o,n);return r.isSome()},eo=function(t,n){var e=t.dom;Ye(e,n),Dt(je(t,"style").map(Xt),"")&&Ge(t,"style")},oo=function(t){return t.dom.offsetWidth},ro=function(t,n){var e=function(n,e){if(!g(e)&&!e.match(/^[0-9]+$/))throw new Error(t+".set accepts only positive integer values. Value was "+e);var o=n.dom;Kt(o)&&(o.style[t]=e+"px")},o=function(e){var o=n(e);if(o<=0||null===o){var r=$e(e,t);return parseFloat(r)||0}return o},r=o,i=function(t,n){return Q(n,(function(n,e){var o=$e(t,e),r=void 0===o?0:parseInt(o,10);return isNaN(r)?n:n+r}),0)},u=function(t,n,e){var o=i(t,e),r=n>o?n-o:0;return r};return{set:e,get:o,getOuter:r,aggregate:i,max:u}},io=ro("height",(function(t){var n=t.dom;return Ne(t)?n.getBoundingClientRect().height:n.offsetHeight})),uo=function(t){return io.get(t)},ao=function(t){return io.getOuter(t)},co=function(t,n){var e=["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"],o=io.max(t,n,e);qe(t,"max-height",o+"px")},so=function(t,n){var e=function(e,o){return so(t+e,n+o)};return{left:t,top:n,translate:e}},fo=so,lo=function(t){var n=t.getBoundingClientRect();return fo(n.left,n.top)},mo=function(t,n){return void 0!==t?t:void 0!==n?n:0},go=function(t){var n=t.dom.ownerDocument,e=n.body,o=n.defaultView,r=n.documentElement;if(e===t.dom)return fo(e.offsetLeft,e.offsetTop);var i=mo(null===o||void 0===o?void 0:o.pageYOffset,r.scrollTop),u=mo(null===o||void 0===o?void 0:o.pageXOffset,r.scrollLeft),a=mo(r.clientTop,e.clientTop),c=mo(r.clientLeft,e.clientLeft);return po(t).translate(u-c,i-a)},po=function(t){var n=t.dom,e=n.ownerDocument,o=e.body;return o===n?fo(o.offsetLeft,o.offsetTop):Ne(t)?lo(n):fo(0,0)},ho=ro("width",(function(t){return t.dom.offsetWidth})),vo=function(t,n){return ho.set(t,n)},bo=function(t){return ho.get(t)},yo=function(t){return ho.getOuter(t)},xo=function(t,n){var e=["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"],o=ho.max(t,n,e);qe(t,"max-width",o+"px")},wo=function(t,n,e,o,r,i,u){return{target:t,x:n,y:e,stop:o,prevent:r,kill:i,raw:u}},So=function(t){var n=nn.fromDom(Ie(t).getOr(t.target)),e=function(){return t.stopPropagation()},o=function(){return t.preventDefault()},r=b(o,e);return wo(n,t.clientX,t.clientY,e,o,r,t)},ko=function(t,n){return function(e){t(e)&&n(So(e))}},Co=function(t,n,e,o,r){var i=ko(e,o);return t.dom.addEventListener(n,i,r),{unbind:k(To,t,n,i,r)}},Oo=function(t,n,e,o){return Co(t,n,e,o,!1)},_o=function(t,n,e,o){return Co(t,n,e,o,!0)},To=function(t,n,e,o){t.dom.removeEventListener(n,e,o)},Eo=function(t,n){var e=ve(t);e.each((function(e){e.dom.insertBefore(n.dom,t.dom)}))},Do=function(t,n){var e=xe(t);e.fold((function(){var e=ve(t);e.each((function(t){Bo(t,n)}))}),(function(t){Eo(t,n)}))},Ao=function(t,n){var e=ke(t);e.fold((function(){Bo(t,n)}),(function(e){t.dom.insertBefore(n.dom,e.dom)}))},Bo=function(t,n){t.dom.appendChild(n.dom)},Mo=function(t,n,e){Se(t,e).fold((function(){Bo(t,n)}),(function(t){Eo(t,n)}))},Fo=function(t,n){Y(n,(function(n){Eo(t,n)}))},Io=function(t,n){Y(n,(function(n){Bo(t,n)}))},Ro=function(t){t.dom.textContent="",Y(we(t),(function(t){No(t)}))},No=function(t){var n=t.dom;null!==n.parentNode&&n.parentNode.removeChild(n)},Po=function(t){var n=we(t);n.length>0&&Fo(t,n),No(t)},Ho=function(t){var n=void 0!==t?t.dom:document,e=n.body.scrollLeft||n.documentElement.scrollLeft,o=n.body.scrollTop||n.documentElement.scrollTop;return fo(e,o)},Vo=function(t,n,e){var o=void 0!==e?e.dom:document,r=o.defaultView;r&&r.scrollTo(t,n)},Lo=function(t){var n=void 0===t?window:t;return ee().browser.isFirefox()?N.none():N.from(n["visualViewport"])},zo=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},Uo=function(t){var n=void 0===t?window:t,e=n.document,o=Ho(nn.fromDom(e));return Lo(n).fold((function(){var t=n.document.documentElement,e=t.clientWidth,r=t.clientHeight;return zo(o.left,o.top,e,r)}),(function(t){return zo(Math.max(t.pageLeft,o.left),Math.max(t.pageTop,o.top),t.width,t.height)}))},jo=function(t,n){var e=t.view(n);return e.fold(x([]),(function(n){var e=t.owner(n),o=jo(t,e);return[n].concat(o)}))},Wo=function(t,n){var e=n.owner(t),o=jo(n,e);return N.some(o)},Go=function(t){var n,e=t.dom===document?N.none():N.from(null===(n=t.dom.defaultView)||void 0===n?void 0:n.frameElement);return e.map(nn.fromDom)},Xo=function(t){return me(t)},Yo=Object.freeze({__proto__:null,view:Go,owner:Xo}),qo=function(t){var n=nn.fromDom(document),e=Ho(n),o=Wo(t,Yo);return o.fold(k(go,t),(function(n){var o=po(t),r=$(n,(function(t,n){var e=po(n);return{left:t.left+e.left,top:t.top+e.top}}),{left:0,top:0});return fo(r.left+o.left+e.left,r.top+o.top+e.top)}))},Ko=function(t,n,e){return{point:t,width:n,height:e}},Jo=function(t,n,e,o){return{x:t,y:n,width:e,height:o}},$o=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},Qo=function(t){var n=go(t),e=yo(t),o=ao(t);return $o(n.left,n.top,e,o)},Zo=function(t){var n=qo(t),e=yo(t),o=ao(t);return $o(n.left,n.top,e,o)},tr=function(){return Uo(window)},nr=function(t){var n=function(n){return nr(t)},e=function(n){return nr(t)},o=function(n){return nr(n(t))},r=function(n){return nr(t)},i=function(n){n(t)},u=function(n){return n(t)},a=function(n,e){return e(t)},c=function(n){return n(t)},s=function(n){return n(t)},f=function(){return N.some(t)};return{isValue:T,isError:_,getOr:x(t),getOrThunk:x(t),getOrDie:x(t),or:n,orThunk:e,fold:a,map:o,mapError:r,each:i,bind:u,exists:c,forall:s,toOptional:f}},er=function(t){var n=function(t){return t()},e=function(){return O(String(t))()},o=w,r=function(t){return t()},i=function(n){return er(t)},u=function(n){return er(n(t))},a=function(n){return er(t)},c=function(n,e){return n(t)};return{isValue:_,isError:T,getOr:w,getOrThunk:n,getOrDie:e,or:o,orThunk:r,fold:c,map:i,mapError:u,each:h,bind:a,exists:_,forall:T,toOptional:N.none}},or=function(t,n){return t.fold((function(){return er(n)}),nr)},rr={value:nr,error:er,fromOption:or};(function(t){t[t["Error"]=0]="Error",t[t["Value"]=1]="Value"})(en||(en={}));var ir=function(t,n,e){return t.stype===en.Error?n(t.serror):e(t.svalue)},ur=function(t){var n=[],e=[];return Y(t,(function(t){ir(t,(function(t){return e.push(t)}),(function(t){return n.push(t)}))})),{values:n,errors:e}},ar=function(t,n){return t.stype===en.Error?{stype:en.Error,serror:n(t.serror)}:t},cr=function(t,n){return t.stype===en.Value?{stype:en.Value,svalue:n(t.svalue)}:t},sr=function(t,n){return t.stype===en.Value?n(t.svalue):t},fr=function(t,n){return t.stype===en.Error?n(t.serror):t},lr=function(t){return{stype:en.Value,svalue:t}},dr=function(t){return{stype:en.Error,serror:t}},mr=function(t){return ir(t,rr.error,rr.value)},gr=function(t){return t.fold(dr,lr)},pr={fromResult:gr,toResult:mr,svalue:lr,partition:ur,serror:dr,bind:sr,bindError:fr,map:cr,mapError:ar,fold:ir},hr=function(t,n,e,o){return{tag:"field",key:t,newKey:n,presence:e,prop:o}},vr=function(t,n){return{tag:"custom",newKey:t,instantiator:n}},br=function(t,n,e){switch(t.tag){case"field":return n(t.key,t.newKey,t.presence,t.prop);case"custom":return e(t.newKey,t.instantiator)}},yr=function(t,n){return n},xr=function(t,n){var e=u(t)&&u(n);return e?Sr(t,n):n},wr=function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var o={},r=0;r<n.length;r++){var i=n[r];for(var u in i)Tt(i,u)&&(o[u]=t(o[u],i[u]))}return o}},Sr=wr(xr),kr=wr(yr),Cr=function(){return{tag:"required",process:{}}},Or=function(t){return{tag:"defaultedThunk",process:t}},_r=function(t){return Or(x(t))},Tr=function(){return{tag:"option",process:{}}},Er=function(t){return{tag:"mergeWithThunk",process:t}},Dr=function(t){return Er(x(t))},Ar=function(t,n){return t.length>0?pr.svalue(Sr(n,kr.apply(void 0,t))):pr.svalue(n)},Br=function(t){return b(pr.serror,et)(t)},Mr=function(t,n){var e=pr.partition(t);return e.errors.length>0?Br(e.errors):Ar(e.values,n)},Fr=function(t){var n=pr.partition(t);return n.errors.length>0?Br(n.errors):pr.svalue(n.values)},Ir={consolidateObj:Mr,consolidateArr:Fr},Rr=function(t){return u(t)&&pt(t).length>100?" removed due to size":JSON.stringify(t,null,2)},Nr=function(t){var n=t.length>10?t.slice(0,10).concat([{path:[],getErrorInfo:x("... (only showing first ten failures)")}]):t;return X(n,(function(t){return"Failed path: ("+t.path.join(" > ")+")\n"+t.getErrorInfo()}))},Pr=function(t,n){return pr.serror([{path:t,getErrorInfo:n}])},Hr=function(t,n,e){return Pr(t,(function(){return'Could not find valid *required* value for "'+n+'" in '+Rr(e)}))},Vr=function(t,n){return Pr(t,(function(){return'Choice schema did not contain choice key: "'+n+'"'}))},Lr=function(t,n,e){return Pr(t,(function(){return'The chosen schema: "'+e+'" did not exist in branches: '+Rr(n)}))},zr=function(t,n){return Pr(t,(function(){return"There are unsupported fields: ["+n.join(", ")+"] specified"}))},Ur=function(t,n){return Pr(t,x(n))},jr=function(t){var n=function(n,e){return pr.bindError(t(e),(function(t){return Ur(n,t)}))},e=x("val");return{extract:n,toString:e}},Wr=jr(pr.svalue),Gr=function(t,n,e,o){return _t(n,e).fold((function(){return Hr(t,e,n)}),o)},Xr=function(t,n,e,o){var r=_t(t,n).getOrThunk((function(){return e(t)}));return o(r)},Yr=function(t,n,e){return e(_t(t,n))},qr=function(t,n,e,o){var r=_t(t,n).map((function(n){return!0===n?e(t):n}));return o(r)},Kr=function(t,n,e,o,r){var i=function(t){return r.extract(n.concat([o]),t)},u=function(t){return t.fold((function(){return pr.svalue(N.none())}),(function(t){var e=r.extract(n.concat([o]),t);return pr.map(e,N.some)}))};switch(t.tag){case"required":return Gr(n,e,o,i);case"defaultedThunk":return Xr(e,o,t.process,i);case"option":return Yr(e,o,u);case"defaultedOptionThunk":return qr(e,o,t.process,u);case"mergeWithThunk":return Xr(e,o,x({}),(function(n){var o=Sr(t.process(e),n);return i(o)}))}},Jr=function(t,n,e){for(var o={},r=[],i=0,u=e;i<u.length;i++){var a=u[i];br(a,(function(e,i,u,a){var c=Kr(u,t,n,e,a);pr.fold(c,(function(t){r.push.apply(r,t)}),(function(t){o[i]=t}))}),(function(t,e){o[t]=e(n)}))}return r.length>0?pr.serror(r):pr.svalue(o)},$r=function(t){var n=function(n,e){return t().extract(n,e)},e=function(){return t().toString()};return{extract:n,toString:e}},Qr=function(t){return pt(St(t,d))},Zr=function(t){var n=ti(t),e=$(t,(function(t,n){return br(n,(function(n){var e;return Sr(t,(e={},e[n]=!0,e))}),x(t))}),{}),o=function(t,o){var r=s(o)?[]:Qr(o),i=J(r,(function(t){return!Et(e,t)}));return 0===i.length?n.extract(t,o):zr(t,i)};return{extract:o,toString:n.toString}},ti=function(t){var n=function(n,e){return Jr(n,e,t)},e=function(){var n=X(t,(function(t){return br(t,(function(t,n,e,o){return t+" -> "+o.toString()}),(function(t,n){return"state("+t+")"}))}));return"obj{\n"+n.join("\n")+"}"};return{extract:n,toString:e}},ni=function(t){var n=function(n,e){var o=X(e,(function(e,o){return t.extract(n.concat(["["+o+"]"]),e)}));return Ir.consolidateArr(o)},e=function(){return"array("+t.toString()+")"};return{extract:n,toString:e}},ei=function(t){var n=function(n,e){for(var o=[],r=0,i=t;r<i.length;r++){var u=i[r],a=u.extract(n,e);if(a.stype===en.Value)return a;o.push(a)}return Ir.consolidateArr(o)},e=function(){return"oneOf("+X(t,(function(t){return t.toString()})).join(", ")+")"};return{extract:n,toString:e}},oi=function(t,n){var e=function(n,e){return ni(jr(t)).extract(n,e)},o=function(t,o){var r=pt(o),i=e(t,r);return pr.bind(i,(function(e){var r=X(e,(function(t){return hr(t,t,Cr(),n)}));return ti(r).extract(t,o)}))},r=function(){return"setOf("+n.toString()+")"};return{extract:o,toString:r}},ri=function(t,n){var e=pn(n),o=function(t,n){return e().extract(t,n)},r=function(){return e().toString()};return{extract:o,toString:r}},ii=b(ni,ti),ui=x(Wr),ai=function(t,n){return jr((function(e){var o=typeof e;return t(e)?pr.svalue(e):pr.serror("Expected type: "+n+" but got: "+o)}))},ci=ai(g,"number"),si=ai(i,"string"),fi=ai(s,"boolean"),li=ai(m,"function"),di=function(t){if(Object(t)!==t)return!0;switch({}.toString.call(t).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(t).every((function(n){return di(t[n])}));default:return!1}},mi=jr((function(t){return di(t)?pr.svalue(t):pr.serror("Expected value to be acceptable for sending via postMessage")})),gi=function(t,n,e,o){var r=_t(e,o);return r.fold((function(){return Lr(t,e,o)}),(function(e){return e.extract(t.concat(["branch: "+o]),n)}))},pi=function(t,n){var e=function(e,o){var r=_t(o,t);return r.fold((function(){return Vr(e,t)}),(function(t){return gi(e,o,n,t)}))},o=function(){return"chooseOn("+t+"). Possible values: "+pt(n)};return{extract:e,toString:o}},hi=function(){return ni(Wr)},vi=function(t){return jr((function(n){return t(n).fold(pr.serror,pr.svalue)}))},bi=function(t,n){return oi((function(n){return pr.fromResult(t(n))}),n)},yi=function(t,n,e){var o=n.extract([t],e);return pr.mapError(o,(function(t){return{input:e,errors:t}}))},xi=function(t,n,e){return pr.toResult(yi(t,n,e))},wi=function(t){return t.fold((function(t){throw new Error(ki(t))}),w)},Si=function(t,n,e){return wi(xi(t,n,e))},ki=function(t){return"Errors: \n"+Nr(t.errors).join("\n")+"\n\nInput object: "+Rr(t.input)},Ci=function(t,n){return pi(t,bt(n,ti))},Oi=function(t,n){return ri(t,n)},_i=hr,Ti=vr,Ei=function(t){return vi((function(n){return U(t,n)?rr.value(n):rr.error('Unsupported value: "'+n+'", choose one of "'+t.join(", ")+'".')}))},Di=function(t){return _i(t,t,Cr(),ui())},Ai=function(t,n){return _i(t,t,Cr(),n)},Bi=function(t){return Ai(t,ci)},Mi=function(t){return Ai(t,si)},Fi=function(t,n){return _i(t,t,Cr(),Ei(n))},Ii=function(t){return Ai(t,fi)},Ri=function(t){return Ai(t,li)},Ni=function(t,n){return _i(t,t,Tr(),jr((function(e){return pr.serror("The field: "+t+" is forbidden. "+n)})))},Pi=function(t,n){return _i(t,t,Cr(),ti(n))},Hi=function(t,n){return _i(t,t,Cr(),ii(n))},Vi=function(t,n){return _i(t,t,Cr(),ni(n))},Li=function(t){return _i(t,t,Tr(),ui())},zi=function(t,n){return _i(t,t,Tr(),n)},Ui=function(t){return zi(t,ci)},ji=function(t){return zi(t,si)},Wi=function(t){return zi(t,li)},Gi=function(t,n){return zi(t,ni(n))},Xi=function(t,n){return zi(t,ti(n))},Yi=function(t,n){return zi(t,Zr(n))},qi=function(t,n){return _i(t,t,_r(n),ui())},Ki=function(t,n,e){return _i(t,t,_r(n),e)},Ji=function(t,n){return Ki(t,n,ci)},$i=function(t,n){return Ki(t,n,si)},Qi=function(t,n,e){return Ki(t,n,Ei(e))},Zi=function(t,n){return Ki(t,n,fi)},tu=function(t,n){return Ki(t,n,li)},nu=function(t,n){return Ki(t,n,mi)},eu=function(t,n,e){return Ki(t,n,ni(e))},ou=function(t,n,e){return Ki(t,n,ti(e))},ru=function(t){var n=t,e=function(){return n},o=function(t){n=t};return{get:e,set:o}},iu=function(t){if(!a(t))throw new Error("cases must be an array");if(0===t.length)throw new Error("there must be at least one case");var n=[],e={};return Y(t,(function(o,r){var i=pt(o);if(1!==i.length)throw new Error("one and only one name per case");var u=i[0],c=o[u];if(void 0!==e[u])throw new Error("duplicate key detected:"+u);if("cata"===u)throw new Error("cannot have a case named cata (sorry)");if(!a(c))throw new Error("case arguments must be an array");n.push(u),e[u]=function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];var i=e.length;if(i!==c.length)throw new Error("Wrong number of arguments to case "+u+". Expected "+c.length+" ("+c+"), got "+i);var a=function(t){var o=pt(t);if(n.length!==o.length)throw new Error("Wrong number of arguments to match. Expected: "+n.join(",")+"\nActual: "+o.join(","));var r=rt(n,(function(t){return U(o,t)}));if(!r)throw new Error("Not all branches were specified when using match. Specified: "+o.join(", ")+"\nRequired: "+n.join(", "));return t[u].apply(null,e)};return{fold:function(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];if(n.length!==t.length)throw new Error("Wrong number of arguments to fold. Expected "+t.length+", got "+n.length);var i=n[r];return i.apply(null,e)},match:a,log:function(t){}}}})),e},uu={generate:iu};uu.generate([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);var au,cu=function(t){var n=[],e=[];return Y(t,(function(t){t.fold((function(t){n.push(t)}),(function(t){e.push(t)}))})),{errors:n,values:e}},su=function(t,n){var e={};return vt(t,(function(t,o){U(n,o)||(e[o]=t)})),e},fu=function(t,n){var e;return e={},e[t]=n,e},lu=function(t){var n={};return Y(t,(function(t){n[t.key]=t.value})),n},du=function(t,n){return su(t,n)},mu=function(t,n){return fu(t,n)},gu=function(t){return lu(t)},pu=function(t,n){return 0===t.length?rr.value(n):rr.value(Sr(n,kr.apply(void 0,t)))},hu=function(t){return rr.error(et(t))},vu=function(t,n){var e=cu(t);return e.errors.length>0?hu(e.errors):pu(e.values,n)},bu=function(t){return m(t)?t:_},yu=function(t,n,e){var o=t.dom,r=bu(e);while(o.parentNode){o=o.parentNode;var i=nn.fromDom(o),u=n(i);if(u.isSome())return u;if(r(i))break}return N.none()},xu=function(t,n,e){var o=n(t),r=bu(e);return o.orThunk((function(){return r(t)?N.none():yu(t,n,r)}))},wu=function(t,n){return se(t.element,n.event.target)},Su={can:T,abort:_,run:h},ku=function(t){if(!Et(t,"can")&&!Et(t,"abort")&&!Et(t,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(t,null,2)+" does not have can, abort, or run!");return D(D({},Su),t)},Cu=function(t,n){return function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];return Q(t,(function(t,o){return t&&n(o).apply(void 0,e)}),!0)}},Ou=function(t,n){return function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];return Q(t,(function(t,o){return t||n(o).apply(void 0,e)}),!1)}},_u=function(t){return m(t)?{can:T,abort:_,run:t}:t},Tu=function(t){var n=Cu(t,(function(t){return t.can})),e=Ou(t,(function(t){return t.abort})),o=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];Y(t,(function(t){t.run.apply(void 0,n)}))};return{can:n,abort:e,run:o}},Eu=x,Du=Eu("touchstart"),Au=Eu("touchmove"),Bu=Eu("touchend"),Mu=Eu("touchcancel"),Fu=Eu("mousedown"),Iu=Eu("mousemove"),Ru=Eu("mouseout"),Nu=Eu("mouseup"),Pu=Eu("mouseover"),Hu=Eu("focusin"),Vu=Eu("focusout"),Lu=Eu("keydown"),zu=Eu("keyup"),Uu=Eu("input"),ju=Eu("change"),Wu=Eu("click"),Gu=Eu("transitioncancel"),Xu=Eu("transitionend"),Yu=Eu("transitionstart"),qu=Eu("selectstart"),Ku=function(t){return x("alloy."+t)},Ju={tap:Ku("tap")},$u=Ku("focus"),Qu=Ku("blur.post"),Zu=Ku("paste.post"),ta=Ku("receive"),na=Ku("execute"),ea=Ku("focus.item"),oa=Ju.tap,ra=Ku("longpress"),ia=Ku("sandbox.close"),ua=Ku("typeahead.cancel"),aa=Ku("system.init"),ca=Ku("system.touchmove"),sa=Ku("system.touchend"),fa=Ku("system.scroll"),la=Ku("system.resize"),da=Ku("system.attached"),ma=Ku("system.detached"),ga=Ku("system.dismissRequested"),pa=Ku("system.repositionRequested"),ha=Ku("focusmanager.shifted"),va=Ku("slotcontainer.visibility"),ba=Ku("change.tab"),ya=Ku("dismiss.tab"),xa=Ku("highlight"),wa=Ku("dehighlight"),Sa=function(t,n){_a(t,t.element,n,{})},ka=function(t,n,e){_a(t,t.element,n,e)},Ca=function(t){Sa(t,na())},Oa=function(t,n,e){_a(t,n,e,{})},_a=function(t,n,e,o){var r=D({target:n},o);t.getSystem().triggerEvent(e,n,r)},Ta=function(t,n,e,o){t.getSystem().triggerEvent(e,n,o.event)},Ea=function(t){return gu(t)},Da=function(t,n){return{key:t,value:ku({abort:n})}},Aa=function(t,n){return{key:t,value:ku({can:n})}},Ba=function(t){return{key:t,value:ku({run:function(t,n){n.event.prevent()}})}},Ma=function(t,n){return{key:t,value:ku({run:n})}},Fa=function(t,n,e){return{key:t,value:ku({run:function(t,o){n.apply(void 0,[t,o].concat(e))}})}},Ia=function(t){return function(n){return Ma(t,n)}},Ra=function(t){return function(n){return{key:t,value:ku({run:function(t,e){wu(t,e)&&n(t,e)}})}}},Na=function(t,n){return Ma(t,(function(e,o){e.getSystem().getByUid(n).each((function(n){Ta(n,n.element,t,o)}))}))},Pa=function(t,n,e){var o=n.partUids[e];return Na(t,o)},Ha=function(t,n){return Ma(t,(function(t,e){var o=e.event,r=t.getSystem().getByDom(o.target).getOrThunk((function(){var n=xu(o.target,(function(n){return t.getSystem().getByDom(n).toOptional()}),_);return n.getOr(t)}));n(t,r,e)}))},Va=function(t){return Ma(t,(function(t,n){n.cut()}))},La=function(t){return Ma(t,(function(t,n){n.stop()}))},za=function(t,n){return Ra(t)(n)},Ua=Ra(da()),ja=Ra(ma()),Wa=Ra(aa()),Ga=Ia(na()),Xa=function(t,n){var e=n||document,o=e.createElement("div");return o.innerHTML=t,we(nn.fromDom(o))},Ya=function(t){return t.dom.innerHTML},qa=function(t,n){var e=me(t),o=e.dom,r=nn.fromDom(o.createDocumentFragment()),i=Xa(n,o);Io(r,i),Ro(t),Bo(t,r)},Ka=function(t){var n=nn.fromTag("div"),e=nn.fromDom(t.dom.cloneNode(!0));return Bo(n,e),Ya(n)},Ja=function(t,n){return nn.fromDom(t.dom.cloneNode(n))},$a=function(t){return Ja(t,!1)},Qa=function(t){if(_e(t))return"#shadow-root";var n=$a(t);return Ka(n)},Za=function(t){return Qa(t)},tc=function(t,n,e){return se(n,t.element)&&!se(n,e)},nc=Ea([Aa($u(),(function(t,n){var e=n.event,o=e.originator,r=e.target;return!tc(t,o,r)}))]),ec=Object.freeze({__proto__:null,events:nc}),oc=0,rc=function(t){var n=new Date,e=n.getTime(),o=Math.floor(1e9*Math.random());return oc++,t+"_"+o+oc+String(e)},ic=x("alloy-id-"),uc=x("data-alloy-id"),ac=ic(),cc=uc(),sc=function(t,n){var e=rc(ac+t);return fc(n,e),e},fc=function(t,n){Object.defineProperty(t.dom,cc,{value:n,writable:!0})},lc=function(t){var n=ln(t)?t.dom[cc]:null;return N.from(n)},dc=function(t){return rc(t)},mc=w,gc=function(t){var n=function(n){return"The component must be in a context to execute: "+n+(t?"\n"+Za(t().element)+" is not in context.":"")},e=function(t){return function(){throw new Error(n(t))}},o=function(t){return function(){}};return{debugInfo:x("fake"),triggerEvent:o("triggerEvent"),triggerFocus:o("triggerFocus"),triggerEscape:o("triggerEscape"),broadcast:o("broadcast"),broadcastOn:o("broadcastOn"),broadcastEvent:o("broadcastEvent"),build:e("build"),addToWorld:e("addToWorld"),removeFromWorld:e("removeFromWorld"),addToGui:e("addToGui"),removeFromGui:e("removeFromGui"),getByUid:e("getByUid"),getByDom:e("getByDom"),isConnected:_}},pc=gc(),hc=function(t,n,e){var o=e.toString(),r=o.indexOf(")")+1,i=o.indexOf("("),u=o.substring(i+1,r-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:n,parameters:vc(u.slice(0,1).concat(u.slice(3)))}},t},vc=function(t){return X(t,(function(t){return Wt(t,"/*")?t.substring(0,t.length-"/*".length):t}))},bc=function(t,n){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:n,parameters:vc(i)}},t},yc=function(t,n){var e=n.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:vc(i.slice(1))}},t},xc=rc("alloy-premade"),wc=function(t){return mu(xc,t)},Sc=function(t){return _t(t,xc)},kc=function(t){return yc((function(n){for(var e=[],o=1;o<arguments.length;o++)e[o-1]=arguments[o];return t.apply(void 0,B([n.getApis(),n],e,!1))}),t)},Cc={init:function(){return Oc({readState:x("No State required")})}},Oc=function(t){return t},_c=function(t,n){var e=X(n,(function(t){return Xi(t.name(),[Di("config"),qi("state",Cc)])})),o=xi("component.behaviours",ti(e),t.behaviours).fold((function(n){throw new Error(ki(n)+"\nComplete spec:\n"+JSON.stringify(t,null,2))}),w);return{list:n,data:bt(o,(function(t){var n=t.map((function(t){return{config:t.config,state:t.state.init(t.config)}}));return x(n)}))}},Tc=function(t){return t.list},Ec=function(t){return t.data},Dc=function(t,n){var e={};return vt(t,(function(t,o){vt(t,(function(t,r){var i=_t(e,r).getOr([]);e[r]=i.concat([n(o,t)])}))})),e},Ac=function(t){return{classes:f(t.classes)?[]:t.classes,attributes:f(t.attributes)?{}:t.attributes,styles:f(t.styles)?{}:t.styles}},Bc=function(t,n){return D(D({},t),{attributes:D(D({},t.attributes),n.attributes),styles:D(D({},t.styles),n.styles),classes:t.classes.concat(n.classes)})},Mc=function(t,n,e,o){var r=D({},n);Y(e,(function(n){r[n.name()]=n.exhibit(t,o)}));var i=Dc(r,(function(t,n){return{name:t,modification:n}})),u=function(t){return $(t,(function(t,n){return D(D({},n.modification),t)}),{})},a=$(i.classes,(function(t,n){return n.modification.concat(t)}),[]),c=u(i.attributes),s=u(i.styles);return Ac({classes:a,attributes:c,styles:s})},Fc=function(t,n,e,o){try{var r=st(e,(function(e,r){var i=e[n],u=r[n],a=o.indexOf(i),c=o.indexOf(u);if(-1===a)throw new Error("The ordering for "+t+" does not have an entry for "+i+".\nOrder specified: "+JSON.stringify(o,null,2));if(-1===c)throw new Error("The ordering for "+t+" does not have an entry for "+u+".\nOrder specified: "+JSON.stringify(o,null,2));return a<c?-1:c<a?1:0}));return rr.value(r)}catch(i){return rr.error([i])}},Ic=function(t,n){return{handler:t,purpose:n}},Rc=function(t,n){return{cHandler:t,purpose:n}},Nc=function(t,n){return Rc(k.apply(void 0,[t.handler].concat(n)),t.purpose)},Pc=function(t){return t.cHandler},Hc=function(t,n){return{name:t,handler:n}},Vc=function(t,n){var e={};return Y(t,(function(t){e[t.name()]=t.handlers(n)})),e},Lc=function(t,n,e){var o=D(D({},e),Vc(n,t));return Dc(o,Hc)},zc=function(t,n,e,o){var r=Lc(t,e,o);return Gc(r,n)},Uc=function(t){var n=_u(t);return function(t,e){for(var o=[],r=2;r<arguments.length;r++)o[r-2]=arguments[r];var i=[t,e].concat(o);n.abort.apply(void 0,i)?e.stop():n.can.apply(void 0,i)&&n.run.apply(void 0,i)}},jc=function(t,n){return rr.error(["The event ("+t+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(X(n,(function(t){return t.name})),null,2)])},Wc=function(t,n,e){var o=n[e];return o?Fc("Event: "+e,"name",t,o).map((function(t){var n=X(t,(function(t){return t.handler}));return Tu(n)})):jc(e,t)},Gc=function(t,n){var e=kt(t,(function(t,e){var o=1===t.length?rr.value(t[0].handler):Wc(t,n,e);return o.map((function(o){var r=Uc(o),i=t.length>1?J(n[e],(function(n){return j(t,(function(t){return t.name===n}))})).join(" > "):t[0].name;return mu(e,Ic(r,i))}))}));return vu(e,{})},Xc="alloy.base.behaviour",Yc=ti([_i("dom","dom",Cr(),ti([Di("tag"),qi("styles",{}),qi("classes",[]),qi("attributes",{}),Li("value"),Li("innerHtml")])),Di("components"),Di("uid"),qi("events",{}),qi("apis",{}),_i("eventOrder","eventOrder",Dr((au={},au[na()]=["disabling",Xc,"toggling","typeaheadevents"],au[$u()]=[Xc,"focusing","keying"],au[aa()]=[Xc,"disabling","toggling","representing"],au[Uu()]=[Xc,"representing","streaming","invalidating"],au[ma()]=[Xc,"representing","item-events","tooltipping"],au[Fu()]=["focusing",Xc,"item-type-events"],au[Du()]=["focusing",Xc,"item-type-events"],au[Pu()]=["item-type-events","tooltipping"],au[ta()]=["receiving","reflecting","tooltipping"],au)),ui()),Li("domModification")]),qc=function(t){return xi("custom.definition",Yc,t)},Kc=function(t){return D(D({},t.dom),{uid:t.uid,domChildren:X(t.components,(function(t){return t.element}))})},Jc=function(t){return t.domModification.fold((function(){return Ac({})}),Ac)},$c=function(t){return t.events},Qc=function(t,n){var e=Ue(t,n);return void 0===e||""===e?[]:e.split(" ")},Zc=function(t,n,e){var o=Qc(t,n),r=o.concat([e]);return Le(t,n,r.join(" ")),!0},ts=function(t,n,e){var o=J(Qc(t,n),(function(t){return t!==e}));return o.length>0?Le(t,n,o.join(" ")):Ge(t,n),!1},ns=function(t){return void 0!==t.dom.classList},es=function(t){return Qc(t,"class")},os=function(t,n){return Zc(t,"class",n)},rs=function(t,n){return ts(t,"class",n)},is=function(t,n){ns(t)?t.dom.classList.add(n):os(t,n)},us=function(t){var n=ns(t)?t.dom.classList:es(t);0===n.length&&Ge(t,"class")},as=function(t,n){if(ns(t)){var e=t.dom.classList;e.remove(n)}else rs(t,n);us(t)},cs=function(t,n){return ns(t)&&t.dom.classList.contains(n)},ss=function(t,n){Y(n,(function(n){is(t,n)}))},fs=function(t,n){Y(n,(function(n){as(t,n)}))},ls=function(t,n){return rt(n,(function(n){return cs(t,n)}))},ds=function(t){return t.dom.value},ms=function(t,n){if(void 0===n)throw new Error("Value.set was undefined");t.dom.value=n},gs=function(t){var n=nn.fromTag(t.tag);ze(n,t.attributes),ss(n,t.classes),Ke(n,t.styles),t.innerHtml.each((function(t){return qa(n,t)}));var e=t.domChildren;return Io(n,e),t.value.each((function(t){ms(n,t)})),t.uid,fc(n,t.uid),n},ps=function(t){var n=_t(t,"behaviours").getOr({});return ot(pt(n),(function(t){var e=n[t];return d(e)?[e.me]:[]}))},hs=function(t,n){return _c(t,n)},vs=function(t){var n=ps(t);return hs(t,n)},bs=function(t,n,e){var o=Kc(t),r=Jc(t),i={"alloy.base.modification":r},u=n.length>0?Mc(e,i,n,o):r;return Bc(o,u)},ys=function(t,n,e){var o={"alloy.base.behaviour":$c(t)};return zc(e,t.eventOrder,n,o).getOrDie()},xs=function(t){var n=function(){return y},e=ru(pc),o=wi(qc(t)),r=vs(t),i=Tc(r),u=Ec(r),a=bs(o,i,u),c=gs(a),s=ys(o,i,u),f=ru(o.components),l=function(t){e.set(t)},d=function(){e.set(gc(n))},g=function(){var t=we(c),n=ot(t,(function(t){return e.get().getByDom(t).fold((function(){return[]}),ct)}));f.set(n)},p=function(n){var e=u,o=m(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(t,null,2))};return o()},h=function(t){return m(u[t.name()])},v=function(){return o.apis},b=function(t){return u[t]().map((function(t){return t.state.readState()})).getOr("not enabled")},y={uid:t.uid,getSystem:e.get,config:p,hasConfigured:h,spec:t,readState:b,getApis:v,connect:l,disconnect:d,element:c,syncComponents:g,components:f.get,events:s};return y},ws=function(t){var n=_t(t,"components").getOr([]);return X(n,Ts)},Ss=function(t){var n=mc(t),e=n.events,o=A(n,["events"]),r=ws(o),i=D(D({},o),{events:D(D({},ec),e),components:r});return rr.value(xs(i))},ks=function(t){var n=nn.fromText(t);return Cs({element:n})},Cs=function(t){var n=Si("external.component",Zr([Di("element"),Li("uid")]),t),e=ru(gc()),o=function(t){e.set(t)},r=function(){e.set(gc((function(){return u})))},i=n.uid.getOrThunk((function(){return dc("external")}));fc(n.element,i);var u={uid:i,getSystem:e.get,config:N.none,hasConfigured:_,connect:o,disconnect:r,getApis:function(){return{}},element:n.element,spec:t,readState:x("No state"),syncComponents:h,components:x([]),events:{}};return wc(u)},Os=dc,_s=function(t){return Tt(t,"uid")},Ts=function(t){return Sc(t).getOrThunk((function(){var n=_s(t)?t:D({uid:Os("")},t);return Ss(n).getOrDie()}))},Es=wc;function Ds(t,n,e,o,r){return t(e,o)?N.some(e):m(r)&&r(e)?N.none():n(e,o,r)}var As,Bs=function(t,n,e){var o=t.dom,r=m(e)?e:_;while(o.parentNode){o=o.parentNode;var i=nn.fromDom(o);if(n(i))return N.some(i);if(r(i))break}return N.none()},Ms=function(t,n,e){var o=function(t,n){return n(t)};return Ds(o,Bs,t,n,e)},Fs=function(t,n){var e=function(t){return n(nn.fromDom(t))},o=tt(t.dom.childNodes,e);return o.map(nn.fromDom)},Is=function(t,n){var e=function(t){for(var o=0;o<t.childNodes.length;o++){var r=nn.fromDom(t.childNodes[o]);if(n(r))return N.some(r);var i=e(t.childNodes[o]);if(i.isSome())return i}return N.none()};return e(t.dom)},Rs=function(t,n,e){return Ms(t,n,e).isSome()},Ns=function(t,n,e){return Bs(t,(function(t){return ie(t,n)}),e)},Ps=function(t,n){return Fs(t,(function(t){return ie(t,n)}))},Hs=function(t,n){return ce(n,t)},Vs=function(t,n,e){var o=function(t,n){return ie(t,n)};return Ds(o,Ns,t,n,e)},Ls=function(t){var n=Ms(t,(function(t){if(!ln(t))return!1;var n=Ue(t,"id");return void 0!==n&&n.indexOf("aria-owns")>-1}));return n.bind((function(t){var n=Ue(t,"id"),e=De(t);return Hs(e,'[aria-owns="'+n+'"]')}))},zs=function(){var t=rc("aria-owns"),n=function(n){Le(n,"aria-owns",t)},e=function(t){Ge(t,"aria-owns")};return{id:t,link:n,unlink:e}},Us=function(t,n){return Ls(n).exists((function(n){return js(t,n)}))},js=function(t,n){return Rs(n,(function(n){return se(n,t.element)}),_)||Us(t,n)},Ws="unknown";(function(t){t[t["STOP"]=0]="STOP",t[t["NORMAL"]=1]="NORMAL",t[t["LOGGING"]=2]="LOGGING"})(As||(As={}));var Gs=ru({}),Xs=function(t,n){var e=[];(new Date).getTime();return{logEventCut:function(t,n,o){e.push({outcome:"cut",target:n,purpose:o})},logEventStopped:function(t,n,o){e.push({outcome:"stopped",target:n,purpose:o})},logNoParent:function(t,n,o){e.push({outcome:"no-parent",target:n,purpose:o})},logEventNoHandlers:function(t,n){e.push({outcome:"no-handlers-left",target:n})},logEventResponse:function(t,n,o){e.push({outcome:"response",purpose:o,target:n})},write:function(){(new Date).getTime();U(["mousemove","mouseover","mouseout",aa()],t)}}},Ys=function(t,n,e){var o=_t(Gs.get(),t).orThunk((function(){var n=pt(Gs.get());return gt(n,(function(n){return t.indexOf(n)>-1?N.some(Gs.get()[n]):N.none()}))})).getOr(As.NORMAL);switch(o){case As.NORMAL:return e(Qs());case As.LOGGING:var r=Xs(t,n),i=e(r);return r.write(),i;case As.STOP:return!0}},qs=["alloy/data/Fields","alloy/debugging/Debugging"],Ks=function(){var t=new Error;if(void 0!==t.stack){var n=t.stack.split("\n");return tt(n,(function(t){return t.indexOf("alloy")>0&&!j(qs,(function(n){return t.indexOf(n)>-1}))})).getOr(Ws)}return Ws},Js={logEventCut:h,logEventStopped:h,logNoParent:h,logEventNoHandlers:h,logEventResponse:h,write:h},$s=function(t,n,e){return Ys(t,n,e)},Qs=x(Js),Zs=x([Di("menu"),Di("selectedMenu")]),tf=x([Di("item"),Di("selectedItem")]);x(ti(tf().concat(Zs())));var nf=x(ti(tf())),ef=Pi("initSize",[Di("numColumns"),Di("numRows")]),of=function(){return Ai("markers",nf())},rf=function(){return Pi("markers",[Di("backgroundMenu")].concat(Zs()).concat(tf()))},uf=function(t){return Pi("markers",X(t,Di))},af=function(t,n,e){return Ks(),_i(n,n,e,vi((function(t){return rr.value((function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(void 0,n)}))})))},cf=function(t){return af("onHandler",t,_r(h))},sf=function(t){return af("onKeyboardHandler",t,_r(N.none))},ff=function(t){return af("onHandler",t,Cr())},lf=function(t){return af("onKeyboardHandler",t,Cr())},df=function(t,n){return Ti(t,x(n))},mf=function(t){return Ti(t,w)},gf=x(ef),pf=function(t,n,e,o,r,i,u,a){return void 0===a&&(a=!1),{x:t,y:n,bubble:e,direction:o,placement:r,restriction:i,label:u+"-"+r,alwaysFit:a}},hf=uu.generate([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),vf=function(t,n,e,o,r,i,u,a,c){return t.fold(n,e,o,r,i,u,a,c)},bf=function(t,n,e,o){return t.fold(n,n,o,o,n,o,e,e)},yf=function(t,n,e,o){return t.fold(n,o,n,o,e,e,n,o)},xf=hf.southeast,wf=hf.southwest,Sf=hf.northeast,kf=hf.northwest,Cf=hf.south,Of=hf.north,_f=hf.east,Tf=hf.west,Ef=function(t,n,e,o){var r=t+n;return r>o?e:r<e?o:r},Df=function(t,n,e){return Math.min(Math.max(t,n),e)},Af=function(t,n){switch(n){case 1:return t.x;case 0:return t.x+t.width;case 2:return t.y;case 3:return t.y+t.height}},Bf=function(t,n){return at(["left","right","top","bottom"],(function(e){return _t(n,e).map((function(n){return Af(t,n)}))}))},Mf=function(t,n,e){var o=function(o,r){return n[o].map((function(n){var i="top"===o||"bottom"===o,u=i?e.top:e.left,a="left"===o||"top"===o?Math.max:Math.min,c=a(n,r)+u;return i?Df(c,t.y,t.bottom):Df(c,t.x,t.right)})).getOr(r)},r=o("left",t.x),i=o("top",t.y),u=o("right",t.right),a=o("bottom",t.bottom);return $o(r,i,u-r,a-i)},Ff="layout",If=function(t){return t.x},Rf=function(t,n){return t.x+t.width/2-n.width/2},Nf=function(t,n){return t.x+t.width-n.width},Pf=function(t,n){return t.y-n.height},Hf=function(t){return t.y+t.height},Vf=function(t,n){return t.y+t.height/2-n.height/2},Lf=function(t){return t.x+t.width},zf=function(t,n){return t.x-n.width},Uf=function(t,n,e){return pf(If(t),Hf(t),e.southeast(),xf(),"southeast",Bf(t,{left:1,top:3}),Ff)},jf=function(t,n,e){return pf(Nf(t,n),Hf(t),e.southwest(),wf(),"southwest",Bf(t,{right:0,top:3}),Ff)},Wf=function(t,n,e){return pf(If(t),Pf(t,n),e.northeast(),Sf(),"northeast",Bf(t,{left:1,bottom:2}),Ff)},Gf=function(t,n,e){return pf(Nf(t,n),Pf(t,n),e.northwest(),kf(),"northwest",Bf(t,{right:0,bottom:2}),Ff)},Xf=function(t,n,e){return pf(Rf(t,n),Pf(t,n),e.north(),Of(),"north",Bf(t,{bottom:2}),Ff)},Yf=function(t,n,e){return pf(Rf(t,n),Hf(t),e.south(),Cf(),"south",Bf(t,{top:3}),Ff)},qf=function(t,n,e){return pf(Lf(t),Vf(t,n),e.east(),_f(),"east",Bf(t,{left:0}),Ff)},Kf=function(t,n,e){return pf(zf(t,n),Vf(t,n),e.west(),Tf(),"west",Bf(t,{right:1}),Ff)},Jf=function(){return[Uf,jf,Wf,Gf,Yf,Xf,qf,Kf]},$f=function(){return[jf,Uf,Gf,Wf,Yf,Xf,qf,Kf]},Qf=function(){return[Wf,Gf,Uf,jf,Xf,Yf]},Zf=function(){return[Gf,Wf,jf,Uf,Xf,Yf]},tl=function(){return[Uf,jf,Wf,Gf,Yf,Xf]},nl=function(){return[jf,Uf,Gf,Wf,Yf,Xf]},el=function(t,n){return n.universal?t:J(t,(function(t){return U(n.channels,t)}))},ol=function(t){return Ea([Ma(ta(),(function(n,e){var o=t.channels,r=pt(o),i=e,u=el(r,i);Y(u,(function(t){var e=o[t],r=e.schema,u=Si("channel["+t+"] data\nReceiver: "+Za(n.element),r,i.data);e.onReceive(n,u)}))}))])},rl=Object.freeze({__proto__:null,events:ol}),il=[Ai("channels",bi(rr.value,Zr([ff("onReceive"),qi("schema",ui())])))],ul=function(t,n,e){return Ga((function(o){e(o,t,n)}))},al=function(t,n,e){return Wa((function(o,r){e(o,t,n)}))},cl=function(t,n,e,o,r,i){var u=Zr(t),a=Xi(n,[Yi("config",t)]);return dl(u,a,n,e,o,r,i)},sl=function(t,n,e,o,r,i){var u=t,a=Xi(n,[zi("config",t)]);return dl(u,a,n,e,o,r,i)},fl=function(t,n,e){var o=function(o){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];var u=[o].concat(r);return o.config({name:x(t)}).fold((function(){throw new Error("We could not find any behaviour configuration for: "+t+". Using API: "+e)}),(function(t){var e=Array.prototype.slice.call(u,1);return n.apply(void 0,[o,t.config,t.state].concat(e))}))};return hc(o,e,n)},ll=function(t){return{key:t,value:void 0}},dl=function(t,n,e,o,r,i,u){var a=function(t){return Et(t,e)?t[e]():N.none()},c=bt(r,(function(t,n){return fl(e,t,n)})),s=bt(i,(function(t,n){return bc(t,n)})),f=D(D(D({},s),c),{revoke:k(ll,e),config:function(n){var o=Si(e+"-config",t,n);return{key:e,value:{config:o,me:f,configAsRaw:pn((function(){return Si(e+"-config",t,n)})),initialConfig:n,state:u}}},schema:x(n),exhibit:function(t,n){return Ft(a(t),_t(o,"exhibit"),(function(t,e){return e(n,t.config,t.state)})).getOrThunk((function(){return Ac({})}))},name:x(e),handlers:function(t){return a(t).map((function(t){var n=_t(o,"events").getOr((function(){return{}}));return n(t.config,t.state)})).getOr({})}});return f},ml=function(t){return gu(t)},gl=Zr([Di("fields"),Di("name"),qi("active",{}),qi("apis",{}),qi("state",Cc),qi("extra",{})]),pl=function(t){var n=Si("Creating behaviour: "+t.name,gl,t);return cl(n.fields,n.name,n.active,n.apis,n.extra,n.state)},hl=Zr([Di("branchKey"),Di("branches"),Di("name"),qi("active",{}),qi("apis",{}),qi("state",Cc),qi("extra",{})]),vl=function(t){var n=Si("Creating behaviour: "+t.name,hl,t);return sl(Ci(n.branchKey,n.branches),n.name,n.active,n.apis,n.extra,n.state)},bl=x(void 0),yl=pl({fields:il,name:"receiving",active:rl}),xl=function(t,n){return Ac({classes:[],styles:n.useFixed()?{}:{position:"relative"}})},wl=Object.freeze({__proto__:null,exhibit:xl}),Sl=function(){return nn.fromDom(document)},kl=function(t){return t.dom.focus()},Cl=function(t){return t.dom.blur()},Ol=function(t){var n=De(t).dom;return t.dom===n.activeElement},_l=function(t){return void 0===t&&(t=Sl()),N.from(t.dom.activeElement).map(nn.fromDom)},Tl=function(t){return _l(De(t)).filter((function(n){return t.dom.contains(n.dom)}))},El=function(t,n){var e=De(n),o=_l(e).bind((function(t){var e=function(n){return se(t,n)};return e(n)?N.some(n):Is(n,e)})),r=t(n);return o.each((function(t){_l(e).filter((function(n){return se(n,t)})).fold((function(){kl(t)}),h)})),r},Dl=function(t,n,e,o,r){var i=function(t){return t+"px"};return{position:t,left:n.map(i),top:e.map(i),right:o.map(i),bottom:r.map(i)}},Al=function(t){return D(D({},t),{position:N.some(t.position)})},Bl=function(t,n){Je(t,Al(n))},Ml=uu.generate([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Fl=function(t,n,e,o,r,i){var u=n.rect,a=u.x-e,c=u.y-o,s=u.width,f=u.height,l=r-(a+s),d=i-(c+f),m=N.some(a),g=N.some(c),p=N.some(l),h=N.some(d),v=N.none();return vf(n.direction,(function(){return Dl(t,m,g,v,v)}),(function(){return Dl(t,v,g,p,v)}),(function(){return Dl(t,m,v,v,h)}),(function(){return Dl(t,v,v,p,h)}),(function(){return Dl(t,m,g,v,v)}),(function(){return Dl(t,m,v,v,h)}),(function(){return Dl(t,m,g,v,v)}),(function(){return Dl(t,v,g,p,v)}))},Il=function(t,n){return t.fold((function(){var t=n.rect;return Dl("absolute",N.some(t.x),N.some(t.y),N.none(),N.none())}),(function(t,e,o,r){return Fl("absolute",n,t,e,o,r)}),(function(t,e,o,r){return Fl("fixed",n,t,e,o,r)}))},Rl=function(t,n){var e=k(qo,n),o=t.fold(e,e,(function(){var t=Ho();return qo(n).translate(-t.left,-t.top)})),r=yo(n),i=ao(n);return $o(o.left,o.top,r,i)},Nl=function(t,n){return n.fold((function(){return t.fold(tr,tr,$o)}),(function(n){return t.fold(n,n,(function(){var e=n(),o=Pl(t,e.x,e.y);return $o(o.left,o.top,e.width,e.height)}))}))},Pl=function(t,n,e){var o=fo(n,e),r=function(){var t=Ho();return o.translate(-t.left,-t.top)};return t.fold(x(o),x(o),r)},Hl=function(t,n,e,o){return t.fold(n,e,o)};Ml.none;var Vl,Ll=Ml.relative,zl=Ml.fixed,Ul=function(t,n){return{anchorBox:t,origin:n}},jl=function(t,n){return Ul(t,n)},Wl="data-alloy-placement",Gl=function(t,n){Le(t,Wl,n)},Xl=function(t){return je(t,Wl)},Yl=function(t){return Ge(t,Wl)},ql=uu.generate([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Kl=function(t,n){var e=n.x,o=n.y,r=n.right,i=n.bottom,u=t.x,a=t.y,c=t.right,s=t.bottom,f=t.width,l=t.height,d=u>=e&&u<=r,m=a>=o&&a<=i,g=d&&m,p=c<=r&&c>=e,h=s<=i&&s>=o,v=p&&h,b=Math.min(f,u>=e?r-u:c-e),y=Math.min(l,a>=o?i-a:s-o);return{originInBounds:g,sizeInBounds:v,visibleW:b,visibleH:y}},Jl=function(t,n){var e=n.x,o=n.y,r=n.right,i=n.bottom,u=t.x,a=t.y,c=t.width,s=t.height,f=Math.max(e,r-c),l=Math.max(o,i-s),d=Df(u,e,f),m=Df(a,o,l),g=Math.min(d+c,r)-d,p=Math.min(m+s,i)-m;return $o(d,m,g,p)},$l=function(t,n,e){var o=x(n.bottom-e.y),r=x(e.bottom-n.y),i=bf(t,r,r,o),u=x(n.right-e.x),a=x(e.right-n.x),c=yf(t,a,a,u);return{maxWidth:c,maxHeight:i}},Ql=function(t,n,e,o){var r=t.bubble,i=r.offset,u=Mf(o,t.restriction,i),a=t.x+i.left,c=t.y+i.top,s=$o(a,c,n,e),f=Kl(s,u),l=f.originInBounds,d=f.sizeInBounds,m=f.visibleW,g=f.visibleH,p=l&&d,h=p?s:Jl(s,u),v=h.width>0&&h.height>0,b=$l(t.direction,h,o),y=b.maxWidth,x=b.maxHeight,w={rect:h,maxHeight:x,maxWidth:y,direction:t.direction,placement:t.placement,classes:{on:r.classesOn,off:r.classesOff},layout:t.label,testY:c};return p||t.alwaysFit?ql.fit(w):ql.nofit(w,m,g,v)},Zl=function(t,n,e,o,r,i){var u=o.width,a=o.height,c=function(n,c,s,f,l){var d=n(e,o,r,t,i),m=Ql(d,u,a,i);return m.fold(x(m),(function(t,n,e,o){var r=l===o?e>f||n>s:!l&&o;return r?m:ql.nofit(c,s,f,l)}))},s=Q(n,(function(t,n){var e=k(c,n);return t.fold(x(t),e)}),ql.nofit({rect:e,maxHeight:o.height,maxWidth:o.width,direction:xf(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:e.y},-1,-1,!1));return s.fold(w,w)},td=function(t){var n=ru(N.none()),e=function(){return n.get().each(t)},o=function(){e(),n.set(N.none())},r=function(){return n.get().isSome()},i=function(){return n.get()},u=function(t){e(),n.set(N.some(t))};return{clear:o,isSet:r,get:i,set:u}},nd=function(){return td((function(t){return t.destroy()}))},ed=function(){return td((function(t){return t.unbind()}))},od=function(){var t=nd(),n=function(n){return t.get().each(n)};return D(D({},t),{run:n})},rd=function(){var t=td(h),n=function(n){return t.get().each(n)};return D(D({},t),{on:n})},id=T,ud=function(t,n,e){return Oo(t,n,id,e)},ad=function(t,n,e){return _o(t,n,id,e)},cd=So,sd=["top","bottom","right","left"],fd="data-alloy-transition-timer",ld=function(t,n){return ls(t,n.classes)},dd=function(t,n,e){return e.exists((function(e){var o=t.mode;return"all"===o||e[o]!==n[o]}))},md=function(t,n){var e=function(t){return parseFloat(t).toFixed(3)};return Ct(n,(function(n,o){var r=t[o].map(e),i=n.map(e);return!At(r,i)})).isSome()},gd=function(t){var n=function(n){var e=$e(t,n),o=i(e)?e.split(/\s*,\s*/):[];return J(o,Yt)},e=function(t){if(i(t)&&/^[\d.]+/.test(t)){var n=parseFloat(t);return Wt(t,"ms")?n:1e3*n}return 0},o=n("transition-delay"),r=n("transition-duration");return Q(r,(function(t,n,r){var i=e(o[r])+e(n);return Math.max(t,i)}),0)},pd=function(t,n){var e,o=ed(),r=ed(),i=function(n){var e,o=null!==(e=n.raw.pseudoElement)&&void 0!==e?e:"";return se(n.target,t)&&qt(o)&&U(sd,n.raw.propertyName)},u=function(u){if(l(u)||i(u)){o.clear(),r.clear();var a=null===u||void 0===u?void 0:u.raw.type;(l(a)||a===Xu())&&(clearTimeout(e),Ge(t,fd),fs(t,n.classes))}},a=function(){o.set(ud(t,Xu(),u)),r.set(ud(t,Gu(),u))};if("ontransitionstart"in t.dom)var c=ud(t,Yu(),(function(t){i(t)&&(c.unbind(),a())}));else a();var s=gd(t);requestAnimationFrame((function(){e=setTimeout(u,s+17),Le(t,fd,e)}))},hd=function(t,n){ss(t,n.classes),je(t,fd).each((function(n){clearTimeout(parseInt(n,10)),Ge(t,fd)})),pd(t,n)},vd=function(t,n,e,o,r,i){var u=dd(o,r,i);if(u||ld(t,o)){qe(t,"position",e.position);var a=Rl(n,t),c=Il(n,D(D({},r),{rect:a})),s=at(sd,(function(t){return c[t]}));md(e,s)&&(Je(t,s),u&&hd(t,o),oo(t))}else fs(t,o.classes)},bd=function(t){return{width:yo(t),height:ao(t)}},yd=function(t,n,e,o){eo(n,"max-height"),eo(n,"max-width");var r=bd(n);return Zl(n,o.preference,t,r,e,o.bounds)},xd=function(t,n){var e=n.classes;fs(t,e.off),ss(t,e.on)},wd=function(t,n,e){var o=e.maxHeightFunction;o(t,n.maxHeight)},Sd=function(t,n,e){var o=e.maxWidthFunction;o(t,n.maxWidth)},kd=function(t,n,e){var o=Il(e.origin,n);e.transition.each((function(r){vd(t,e.origin,o,r,n,e.lastPlacement)})),Bl(t,o)},Cd=function(t,n){Gl(t,n.placement)},Od=function(t,n){co(t,Math.floor(n))},_d=x((function(t,n){Od(t,n),Ke(t,{"overflow-x":"hidden","overflow-y":"auto"})})),Td=x((function(t,n){Od(t,n)})),Ed=function(t,n,e){return void 0===t[n]?e:t[n]},Dd=function(t,n,e,o,r,i,u,a){var c=Ed(u,"maxHeightFunction",_d()),s=Ed(u,"maxWidthFunction",h),f=t.anchorBox,l=t.origin,d={bounds:Nl(l,i),origin:l,preference:o,maxHeightFunction:c,maxWidthFunction:s,lastPlacement:r,transition:a};return Ad(f,n,e,d)},Ad=function(t,n,e,o){var r=yd(t,n,e,o);return kd(n,r,o),Cd(n,r),xd(n,r),wd(n,r,o),Sd(n,r,o),{layout:r.layout,placement:r.placement}},Bd=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],Md=function(t,n,e,o){void 0===o&&(o=1);var r=t*o,i=n*o,u=function(t){return _t(e,t).getOr([])},a=function(t,n,e){var o=ut(Bd,e);return{offset:fo(t,n),classesOn:ot(e,u),classesOff:ot(o,u)}};return{southeast:function(){return a(-t,n,["top","alignLeft"])},southwest:function(){return a(t,n,["top","alignRight"])},south:function(){return a(-t/2,n,["top","alignCentre"])},northeast:function(){return a(-t,-n,["bottom","alignLeft"])},northwest:function(){return a(t,-n,["bottom","alignRight"])},north:function(){return a(-t/2,-n,["bottom","alignCentre"])},east:function(){return a(t,-n/2,["valignCentre","left"])},west:function(){return a(-t,-n/2,["valignCentre","right"])},insetNortheast:function(){return a(r,i,["top","alignLeft","inset"])},insetNorthwest:function(){return a(-r,i,["top","alignRight","inset"])},insetNorth:function(){return a(-r/2,i,["top","alignCentre","inset"])},insetSoutheast:function(){return a(r,-i,["bottom","alignLeft","inset"])},insetSouthwest:function(){return a(-r,-i,["bottom","alignRight","inset"])},insetSouth:function(){return a(-r/2,-i,["bottom","alignCentre","inset"])},insetEast:function(){return a(-r,-i/2,["valignCentre","right","inset"])},insetWest:function(){return a(r,-i/2,["valignCentre","left","inset"])}}},Fd=function(){return Md(0,0,{})},Id=w,Rd=function(t,n){return function(e){return"rtl"===Nd(e)?n:t}},Nd=function(t){return"rtl"===$e(t,"direction")?"rtl":"ltr"};(function(t){t["TopToBottom"]="toptobottom",t["BottomToTop"]="bottomtotop"})(Vl||(Vl={}));var Pd="data-alloy-vertical-dir",Hd=function(t){return Rs(t,(function(t){return ln(t)&&Ue(t,"data-alloy-vertical-dir")===Vl.BottomToTop}))},Vd=function(){return Xi("layouts",[Di("onLtr"),Di("onRtl"),Li("onBottomLtr"),Li("onBottomRtl")])},Ld=function(t,n,e,o,r,i,u){var a=u.map(Hd).getOr(!1),c=n.layouts.map((function(n){return n.onLtr(t)})),s=n.layouts.map((function(n){return n.onRtl(t)})),f=a?n.layouts.bind((function(n){return n.onBottomLtr.map((function(n){return n(t)}))})).or(c).getOr(r):c.getOr(e),l=a?n.layouts.bind((function(n){return n.onBottomRtl.map((function(n){return n(t)}))})).or(s).getOr(i):s.getOr(o),d=Rd(f,l);return d(t)},zd=function(t,n,e){var o=n.hotspot,r=Rl(e,o.element),i=Ld(t.element,n,tl(),nl(),Qf(),Zf(),N.some(n.hotspot.element));return N.some(Id({anchorBox:r,bubble:n.bubble.getOr(Fd()),overrides:n.overrides,layouts:i,placer:N.none()}))},Ud=[Di("hotspot"),Li("bubble"),qi("overrides",{}),Vd(),df("placement",zd)],jd=function(t,n,e){var o=Pl(e,n.x,n.y),r=$o(o.left,o.top,n.width,n.height),i=Ld(t.element,n,Jf(),$f(),Jf(),$f(),N.none());return N.some(Id({anchorBox:r,bubble:n.bubble,overrides:n.overrides,layouts:i,placer:N.none()}))},Wd=[Di("x"),Di("y"),qi("height",0),qi("width",0),qi("bubble",Fd()),qi("overrides",{}),Vd(),df("placement",jd)],Gd=uu.generate([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),Xd=function(t){return t.fold(w,(function(t,n,e){return t.translate(-n,-e)}))},Yd=function(t){return t.fold(w,w)},qd=function(t){return Q(t,(function(t,n){return t.translate(n.left,n.top)}),fo(0,0))},Kd=function(t){var n=X(t,Xd);return qd(n)},Jd=function(t){var n=X(t,Yd);return qd(n)},$d=Gd.screen,Qd=Gd.absolute,Zd=function(t,n,e){var o=he(e.root).dom,r=function(n){var e=me(n),o=me(t.element);return se(e,o)};return N.from(o.frameElement).map(nn.fromDom).filter(r).map(go)},tm=function(t,n,e){var o=me(t.element),r=Ho(o),i=Zd(t,n,e).getOr(r);return Qd(i,r.left,r.top)},nm=function(t,n,e,o){var r=$d(fo(t,n));return N.some(Ko(r,e,o))},em=function(t,n,e,o,r){return t.map((function(t){var i=[n,t.point],u=Hl(o,(function(){return Jd(i)}),(function(){return Jd(i)}),(function(){return Kd(i)})),a=Jo(u.left,u.top,t.width,t.height),c=e.showAbove?Qf():tl(),s=e.showAbove?Zf():nl(),f=Ld(r,e,c,s,c,s,N.none());return Id({anchorBox:a,bubble:e.bubble.getOr(Fd()),overrides:e.overrides,layouts:f,placer:N.none()})}))},om=function(t,n,e){var o=tm(t,e,n);return n.node.filter(Ne).bind((function(r){var i=r.dom.getBoundingClientRect(),u=nm(i.left,i.top,i.width,i.height),a=n.node.getOr(t.element);return em(u,o,n,e,a)}))},rm=[Di("node"),Di("root"),Li("bubble"),Vd(),qi("overrides",{}),qi("showAbove",!1),df("placement",om)],im="\ufeff",um=" ",am=function(t,n,e,o){return{start:t,soffset:n,finish:e,foffset:o}},cm={create:am},sm=uu.generate([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),fm=function(t,n,e,o){return t.fold(n,e,o)},lm=function(t){return t.fold(w,w,w)},dm=sm.before,mm=sm.on,gm=sm.after,pm={before:dm,on:mm,after:gm,cata:fm,getStart:lm},hm=uu.generate([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),vm=function(t){return hm.exact(t.start,t.soffset,t.finish,t.foffset)},bm=function(t){return t.match({domRange:function(t){return nn.fromDom(t.startContainer)},relative:function(t,n){return pm.getStart(t)},exact:function(t,n,e,o){return t}})},ym=hm.domRange,xm=hm.relative,wm=hm.exact,Sm=function(t){var n=bm(t);return he(n)},km=cm.create,Cm={domRange:ym,relative:xm,exact:wm,exactFromRange:vm,getWin:Sm,range:km},Om=function(t,n){n.fold((function(n){t.setStartBefore(n.dom)}),(function(n,e){t.setStart(n.dom,e)}),(function(n){t.setStartAfter(n.dom)}))},_m=function(t,n){n.fold((function(n){t.setEndBefore(n.dom)}),(function(n,e){t.setEnd(n.dom,e)}),(function(n){t.setEndAfter(n.dom)}))},Tm=function(t,n,e){var o=t.document.createRange();return Om(o,n),_m(o,e),o},Em=function(t,n,e,o,r){var i=t.document.createRange();return i.setStart(n.dom,e),i.setEnd(o.dom,r),i},Dm=function(t){return{left:t.left,top:t.top,right:t.right,bottom:t.bottom,width:t.width,height:t.height}},Am=function(t){var n=t.getClientRects(),e=n.length>0?n[0]:t.getBoundingClientRect();return e.width>0||e.height>0?N.some(e).map(Dm):N.none()},Bm=function(t){var n=t.getBoundingClientRect();return n.width>0||n.height>0?N.some(n).map(Dm):N.none()},Mm=uu.generate([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Fm=function(t,n,e){return n(nn.fromDom(e.startContainer),e.startOffset,nn.fromDom(e.endContainer),e.endOffset)},Im=function(t,n){return n.match({domRange:function(t){return{ltr:x(t),rtl:N.none}},relative:function(n,e){return{ltr:pn((function(){return Tm(t,n,e)})),rtl:pn((function(){return N.some(Tm(t,e,n))}))}},exact:function(n,e,o,r){return{ltr:pn((function(){return Em(t,n,e,o,r)})),rtl:pn((function(){return N.some(Em(t,o,r,n,e))}))}}})},Rm=function(t,n){var e=n.ltr();if(e.collapsed){var o=n.rtl().filter((function(t){return!1===t.collapsed}));return o.map((function(t){return Mm.rtl(nn.fromDom(t.endContainer),t.endOffset,nn.fromDom(t.startContainer),t.startOffset)})).getOrThunk((function(){return Fm(t,Mm.ltr,e)}))}return Fm(t,Mm.ltr,e)},Nm=function(t,n){var e=Im(t,n);return Rm(t,e)},Pm=function(t,n){var e=Nm(t,n);return e.match({ltr:function(n,e,o,r){var i=t.document.createRange();return i.setStart(n.dom,e),i.setEnd(o.dom,r),i},rtl:function(n,e,o,r){var i=t.document.createRange();return i.setStart(o.dom,r),i.setEnd(n.dom,e),i}})};Mm.ltr,Mm.rtl;var Hm,Vm=function(t,n){var e=function(e){if(!t(e))throw new Error("Can only get "+n+" value of a "+n+" node");return o(e).getOr("")},o=function(n){return t(n)?N.from(n.dom.nodeValue):N.none()},r=function(e,o){if(!t(e))throw new Error("Can only set raw "+n+" value of a "+n+" node");e.dom.nodeValue=o};return{get:e,getOption:o,set:r}},Lm=Vm(dn,"text"),zm=function(t){return Lm.get(t)},Um=function(t){return Lm.getOption(t)},jm=function(t){return"img"===cn(t)?1:Um(t).fold((function(){return we(t).length}),(function(t){return t.length}))},Wm=function(t){return Um(t).filter((function(t){return 0!==t.trim().length||t.indexOf(um)>-1})).isSome()},Gm=["img","br"],Xm=function(t){var n=Wm(t);return n||U(Gm,cn(t))},Ym=function(t){return qm(t,Xm)},qm=function(t,n){var e=function(t){for(var o=we(t),r=o.length-1;r>=0;r--){var i=o[r];if(n(i))return N.some(i);var u=e(i);if(u.isSome())return u}return N.none()};return e(t)},Km=function(t,n){return ae(n,t)},Jm=function(t,n,e,o){var r=me(t),i=r.dom.createRange();return i.setStart(t.dom,n),i.setEnd(e.dom,o),i},$m=function(t,n,e,o){var r=Jm(t,n,e,o),i=se(t,e)&&n===o;return r.collapsed&&!i},Qm=function(t){return N.from(t.getSelection())},Zm=function(t){if(t.rangeCount>0){var n=t.getRangeAt(0),e=t.getRangeAt(t.rangeCount-1);return N.some(cm.create(nn.fromDom(n.startContainer),n.startOffset,nn.fromDom(e.endContainer),e.endOffset))}return N.none()},tg=function(t){if(null===t.anchorNode||null===t.focusNode)return Zm(t);var n=nn.fromDom(t.anchorNode),e=nn.fromDom(t.focusNode);return $m(n,t.anchorOffset,e,t.focusOffset)?N.some(cm.create(n,t.anchorOffset,e,t.focusOffset)):Zm(t)},ng=function(t){return Qm(t).filter((function(t){return t.rangeCount>0})).bind(tg)},eg=function(t,n){var e=Pm(t,n);return Am(e)},og=function(t,n){var e=Pm(t,n);return Bm(e)},rg=function(t,n){return{element:t,offset:n}},ig=function(t,n){var e=we(t);if(0===e.length)return rg(t,n);if(n<e.length)return rg(e[n],0);var o=e[e.length-1],r=dn(o)?zm(o).length:we(o).length;return rg(o,r)},ug=function(t,n){return dn(t)?rg(t,n):ig(t,n)},ag=function(t,n){var e=n.getSelection.getOrThunk((function(){return function(){return ng(t)}}));return e().map((function(t){var n=ug(t.start,t.soffset),e=ug(t.finish,t.foffset);return Cm.range(n.element,n.offset,e.element,e.offset)}))},cg=function(t,n,e){var o=he(n.root).dom,r=tm(t,e,n),i=ag(o,n).bind((function(t){var n=og(o,Cm.exactFromRange(t)).orThunk((function(){var n=nn.fromText(im);Eo(t.start,n);var e=eg(o,Cm.exact(n,0,n,1));return No(n),e}));return n.bind((function(t){return nm(t.left,t.top,t.width,t.height)}))})),u=ag(o,n).bind((function(t){return ln(t.start)?N.some(t.start):be(t.start)})),a=u.getOr(t.element);return em(i,r,n,e,a)},sg=[Li("getSelection"),Di("root"),Li("bubble"),Vd(),qi("overrides",{}),qi("showAbove",!1),df("placement",cg)],fg="link-layout",lg=function(t){return t.x+t.width},dg=function(t,n){return t.x-n.width},mg=function(t,n){return t.y-n.height+t.height},gg=function(t){return t.y},pg=function(t,n,e){return pf(lg(t),gg(t),e.southeast(),xf(),"southeast",Bf(t,{left:0,top:2}),fg)},hg=function(t,n,e){return pf(dg(t,n),gg(t),e.southwest(),wf(),"southwest",Bf(t,{right:1,top:2}),fg)},vg=function(t,n,e){return pf(lg(t),mg(t,n),e.northeast(),Sf(),"northeast",Bf(t,{left:0,bottom:3}),fg)},bg=function(t,n,e){return pf(dg(t,n),mg(t,n),e.northwest(),kf(),"northwest",Bf(t,{right:1,bottom:3}),fg)},yg=function(){return[pg,hg,vg,bg]},xg=function(){return[hg,pg,bg,vg]},wg=function(t,n,e){var o=Rl(e,n.item.element),r=Ld(t.element,n,yg(),xg(),yg(),xg(),N.none());return N.some(Id({anchorBox:o,bubble:Fd(),overrides:n.overrides,layouts:r,placer:N.none()}))},Sg=[Di("item"),Vd(),qi("overrides",{}),df("placement",wg)],kg=Ci("type",{selection:sg,node:rm,hotspot:Ud,submenu:Sg,makeshift:Wd}),Cg=[Vi("classes",si),Qi("mode","all",["all","layout","placement"])],Og=[qi("useFixed",_),Li("getBounds")],_g=[Ai("anchor",kg),Xi("transition",Cg)],Tg=function(){var t=document.documentElement;return zl(0,0,t.clientWidth,t.clientHeight)},Eg=function(t){var n=go(t.element),e=t.element.dom.getBoundingClientRect();return Ll(n.left,n.top,e.width,e.height)},Dg=function(t,n,e,o,r,i,u){var a=jl(e.anchorBox,n);return Dd(a,r.element,e.bubble,e.layouts,i,o,e.overrides,u)},Ag=function(t,n,e,o,r){Bg(t,n,e,o,r,N.none())},Bg=function(t,n,e,o,r,i){var u=i.map(Qo);return Mg(t,n,e,o,r,u)},Mg=function(t,n,e,o,r,i){var u=Si("placement.info",ti(_g),r),a=u.anchor,c=o.element,s=e.get(o.uid);El((function(){qe(c,"position","fixed");var r=Ze(c,"visibility");qe(c,"visibility","hidden");var f=n.useFixed()?Tg():Eg(t),l=a.placement,d=i.map(x).or(n.getBounds);l(t,a,f).each((function(n){var r=n.placer.getOr(Dg),i=r(t,f,n,d,o,s,u.transition);e.set(o.uid,i)})),r.fold((function(){eo(c,"visibility")}),(function(t){qe(c,"visibility",t)})),Ze(c,"left").isNone()&&Ze(c,"top").isNone()&&Ze(c,"right").isNone()&&Ze(c,"bottom").isNone()&&Dt(Ze(c,"position"),"fixed")&&eo(c,"position")}),c)},Fg=function(t,n,e){return n.useFixed()?"fixed":"absolute"},Ig=function(t,n,e,o){var r=o.element;Y(["position","left","right","top","bottom"],(function(t){return eo(r,t)})),Yl(r),e.clear(o.uid)},Rg=Object.freeze({__proto__:null,position:Ag,positionWithin:Bg,positionWithinBounds:Mg,getMode:Fg,reset:Ig}),Ng=function(){var t={},n=function(n,e){t[n]=e},e=function(n){return _t(t,n)},o=function(n){d(n)?delete t[n]:t={}};return Oc({readState:function(){return t},clear:o,set:n,get:e})},Pg=Object.freeze({__proto__:null,init:Ng}),Hg=pl({fields:Og,name:"positioning",active:wl,apis:Rg,state:Pg}),Vg=function(t){Sa(t,ma());var n=t.components();Y(n,Vg)},Lg=function(t){var n=t.components();Y(n,Lg),Sa(t,da())},zg=function(t,n){Bo(t.element,n.element)},Ug=function(t){Y(t.components(),(function(t){return No(t.element)})),Ro(t.element),t.syncComponents()},jg=function(t,n){var e=t.components();Ug(t);var o=ut(e,n);Y(o,(function(n){Vg(n),t.getSystem().removeFromWorld(n)})),Y(n,(function(n){n.getSystem().isConnected()?zg(t,n):(t.getSystem().addToWorld(n),zg(t,n),Ne(t.element)&&Lg(n)),t.syncComponents()}))},Wg=function(t,n){Gg(t,n,Bo)},Gg=function(t,n,e){t.getSystem().addToWorld(n),e(t.element,n.element),Ne(t.element)&&Lg(n),t.syncComponents()},Xg=function(t){Vg(t),No(t.element),t.getSystem().removeFromWorld(t)},Yg=function(t){var n=ve(t.element).bind((function(n){return t.getSystem().getByDom(n).toOptional()}));Xg(t),n.each((function(t){t.syncComponents()}))},qg=function(t){var n=t.components();Y(n,Xg),Ro(t.element),t.syncComponents()},Kg=function(t,n){$g(t,n,Bo)},Jg=function(t,n){$g(t,n,Do)},$g=function(t,n,e){e(t,n.element);var o=we(n.element);Y(o,(function(t){n.getByDom(t).each(Lg)}))},Qg=function(t){var n=we(t.element);Y(n,(function(n){t.getByDom(n).each(Vg)})),No(t.element)},Zg=function(t,n,e,o){e.get().each((function(n){qg(t)}));var r=n.getAttachPoint(t);Wg(r,t);var i=t.getSystem().build(o);return Wg(t,i),e.set(i),i},tp=function(t,n,e,o){var r=Zg(t,n,e,o);return n.onOpen(t,r),r},np=function(t,n,e,o){return e.get().map((function(){return Zg(t,n,e,o)}))},ep=function(t,n,e,o,r){sp(t,n),tp(t,n,e,o),r(),lp(t,n)},op=function(t,n,e){e.get().each((function(o){qg(t),Yg(t),n.onClose(t,o),e.clear()}))},rp=function(t,n,e){return e.isOpen()},ip=function(t,n,e,o){return rp(t,n,e)&&e.get().exists((function(e){return n.isPartOf(t,e,o)}))},up=function(t,n,e){return e.get()},ap=function(t,n,e,o){Ze(t.element,n).fold((function(){Ge(t.element,e)}),(function(n){Le(t.element,e,n)})),qe(t.element,n,o)},cp=function(t,n,e){je(t.element,e).fold((function(){return eo(t.element,n)}),(function(e){return qe(t.element,n,e)}))},sp=function(t,n,e){var o=n.getAttachPoint(t);qe(t.element,"position",Hg.getMode(o)),ap(t,"visibility",n.cloakVisibilityAttr,"hidden")},fp=function(t){return j(["top","left","right","bottom"],(function(n){return Ze(t,n).isSome()}))},lp=function(t,n,e){fp(t.element)||eo(t.element,"position"),cp(t,"visibility",n.cloakVisibilityAttr)},dp=Object.freeze({__proto__:null,cloak:sp,decloak:lp,open:tp,openWhileCloaked:ep,close:op,isOpen:rp,isPartOf:ip,getState:up,setContent:np}),mp=function(t,n){return Ea([Ma(ia(),(function(e,o){op(e,t,n)}))])},gp=Object.freeze({__proto__:null,events:mp}),pp=[cf("onOpen"),cf("onClose"),Di("isPartOf"),Di("getAttachPoint"),qi("cloakVisibilityAttr","data-precloak-visibility")],hp=function(){var t=rd(),n=x("not-implemented");return Oc({readState:n,isOpen:t.isSet,clear:t.clear,set:t.set,get:t.get})},vp=Object.freeze({__proto__:null,init:hp}),bp=pl({fields:pp,name:"sandboxing",active:gp,apis:dp,state:vp}),yp=x("dismiss.popups"),xp=x("reposition.popups"),wp=x("mouse.released"),Sp=Zr([qi("isExtraPart",_),Xi("fireEventInstead",[qi("event",ga())])]),kp=function(t){var n,e=Si("Dismissal",Sp,t);return n={},n[yp()]={schema:Zr([Di("target")]),onReceive:function(t,n){if(bp.isOpen(t)){var o=bp.isPartOf(t,n.target)||e.isExtraPart(t,n.target);o||e.fireEventInstead.fold((function(){return bp.close(t)}),(function(n){return Sa(t,n.event)}))}}},n},Cp=Zr([Xi("fireEventInstead",[qi("event",pa())]),Ri("doReposition")]),Op=function(t){var n,e=Si("Reposition",Cp,t);return n={},n[xp()]={onReceive:function(t){bp.isOpen(t)&&e.fireEventInstead.fold((function(){return e.doReposition(t)}),(function(n){return Sa(t,n.event)}))}},n},_p=function(t,n,e){n.store.manager.onLoad(t,n,e)},Tp=function(t,n,e){n.store.manager.onUnload(t,n,e)},Ep=function(t,n,e,o){n.store.manager.setValue(t,n,e,o)},Dp=function(t,n,e){return n.store.manager.getValue(t,n,e)},Ap=function(t,n,e){return e},Bp=Object.freeze({__proto__:null,onLoad:_p,onUnload:Tp,setValue:Ep,getValue:Dp,getState:Ap}),Mp=function(t,n){var e=t.resetOnDom?[Ua((function(e,o){_p(e,t,n)})),ja((function(e,o){Tp(e,t,n)}))]:[al(t,n,_p)];return Ea(e)},Fp=Object.freeze({__proto__:null,events:Mp}),Ip=function(){var t=ru(null),n=function(){return{mode:"memory",value:t.get()}},e=function(){return null===t.get()},o=function(){t.set(null)};return Oc({set:t.set,get:t.get,isNotSet:e,clear:o,readState:n})},Rp=function(){var t=h;return Oc({readState:t})},Np=function(){var t=ru({}),n=ru({}),e=function(){return{mode:"dataset",dataByValue:t.get(),dataByText:n.get()}},o=function(){t.set({}),n.set({})},r=function(e){return _t(t.get(),e).orThunk((function(){return _t(n.get(),e)}))},i=function(e){var o=t.get(),r=n.get(),i={},u={};Y(e,(function(t){i[t.value]=t,_t(t,"meta").each((function(n){_t(n,"text").each((function(n){u[n]=t}))}))})),t.set(D(D({},o),i)),n.set(D(D({},r),u))};return Oc({readState:e,lookup:r,update:i,clear:o})},Pp=function(t){return t.store.manager.state(t)},Hp=Object.freeze({__proto__:null,memory:Ip,dataset:Np,manual:Rp,init:Pp}),Vp=function(t,n,e,o){var r=n.store;e.update([o]),r.setValue(t,o),n.onSetValue(t,o)},Lp=function(t,n,e){var o=n.store,r=o.getDataKey(t);return e.lookup(r).getOrThunk((function(){return o.getFallbackEntry(r)}))},zp=function(t,n,e){var o=n.store;o.initialValue.each((function(o){Vp(t,n,e,o)}))},Up=function(t,n,e){e.clear()},jp=[Li("initialValue"),Di("getFallbackEntry"),Di("getDataKey"),Di("setValue"),df("manager",{setValue:Vp,getValue:Lp,onLoad:zp,onUnload:Up,state:Np})],Wp=function(t,n,e){return n.store.getValue(t)},Gp=function(t,n,e,o){n.store.setValue(t,o),n.onSetValue(t,o)},Xp=function(t,n,e){n.store.initialValue.each((function(e){n.store.setValue(t,e)}))},Yp=[Di("getValue"),qi("setValue",h),Li("initialValue"),df("manager",{setValue:Gp,getValue:Wp,onLoad:Xp,onUnload:h,state:Cc.init})],qp=function(t,n,e,o){e.set(o),n.onSetValue(t,o)},Kp=function(t,n,e){return e.get()},Jp=function(t,n,e){n.store.initialValue.each((function(t){e.isNotSet()&&e.set(t)}))},$p=function(t,n,e){e.clear()},Qp=[Li("initialValue"),df("manager",{setValue:qp,getValue:Kp,onLoad:Jp,onUnload:$p,state:Ip})],Zp=[Ki("store",{mode:"memory"},Ci("mode",{memory:Qp,manual:Yp,dataset:jp})),cf("onSetValue"),qi("resetOnDom",!1)],th=pl({fields:Zp,name:"representing",active:Fp,apis:Bp,extra:{setValueFrom:function(t,n){var e=th.getValue(n);th.setValue(t,e)}},state:Hp}),nh=function(t,n){return ou(t,{},X(n,(function(n){return Ni(n.name(),"Cannot configure "+n.name()+" for "+t)})).concat([Ti("dump",w)]))},eh=function(t){return t.dump},oh=function(t,n){return D(D({},ml(n)),t.dump)},rh={field:nh,augment:oh,get:eh},ih="placeholder",uh=uu.generate([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),ah=function(t){return Tt(t,"uiType")},ch=function(t,n,e,o){return t.exists((function(t){return t!==e.owner}))?uh.single(!0,x(e)):_t(o,e.name).fold((function(){throw new Error("Unknown placeholder component: "+e.name+"\nKnown: ["+pt(o)+"]\nNamespace: "+t.getOr("none")+"\nSpec: "+JSON.stringify(e,null,2))}),(function(t){return t.replace()}))},sh=function(t,n,e,o){return ah(e)&&e.uiType===ih?ch(t,n,e,o):uh.single(!1,x(e))},fh=function(t,n,e,o){var r=sh(t,n,e,o);return r.fold((function(r,i){var u=ah(e)?i(n,e.config,e.validated):i(n),a=_t(u,"components").getOr([]),c=ot(a,(function(e){return fh(t,n,e,o)}));return[D(D({},u),{components:c})]}),(function(t,o){if(ah(e)){var r=o(n,e.config,e.validated),i=e.validated.preprocess.getOr(w);return i(r)}return o(n)}))},lh=function(t,n,e,o){return ot(e,(function(e){return fh(t,n,e,o)}))},dh=function(t,n){var e=!1,o=function(){return e},r=function(){if(e)throw new Error("Trying to use the same placeholder more than once: "+t);return e=!0,n},i=function(){return n.fold((function(t,n){return t}),(function(t,n){return t}))};return{name:x(t),required:i,used:o,replace:r}},mh=function(t,n,e,o){var r=bt(o,(function(t,n){return dh(n,t)})),i=lh(t,n,e,r);return vt(r,(function(e){if(!1===e.used()&&e.required())throw new Error("Placeholder: "+e.name()+" was not found in components list\nNamespace: "+t.getOr("none")+"\nComponents: "+JSON.stringify(n.components,null,2))})),i},gh=uh.single,ph=uh.multiple,hh=x(ih),vh=uu.generate([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),bh=qi("factory",{sketch:w}),yh=qi("schema",[]),xh=Di("name"),wh=_i("pname","pname",Or((function(t){return"<alloy."+rc(t.name)+">"})),ui()),Sh=Ti("schema",(function(){return[Li("preprocess")]})),kh=qi("defaults",x({})),Ch=qi("overrides",x({})),Oh=ti([bh,yh,xh,wh,kh,Ch]),_h=ti([bh,yh,xh,kh,Ch]),Th=ti([bh,yh,xh,wh,kh,Ch]),Eh=ti([bh,Sh,xh,Di("unit"),wh,kh,Ch]),Dh=function(t){return t.fold(N.some,N.none,N.some,N.some)},Ah=function(t){var n=function(t){return t.name};return t.fold(n,n,n,n)},Bh=function(t){return t.fold(w,w,w,w)},Mh=function(t,n){return function(e){var o=Si("Converting part type",n,e);return t(o)}},Fh=Mh(vh.required,Oh),Ih=Mh(vh.external,_h),Rh=Mh(vh.optional,Th),Nh=Mh(vh.group,Eh),Ph=x("entirety"),Hh=Object.freeze({__proto__:null,required:Fh,external:Ih,optional:Rh,group:Nh,asNamedPart:Dh,name:Ah,asCommon:Bh,original:Ph}),Vh=function(t,n,e,o){return Sr(n.defaults(t,e,o),e,{uid:t.partUids[n.name]},n.overrides(t,e,o))},Lh=function(t,n,e){var o={},r={};return Y(e,(function(t){t.fold((function(t){o[t.pname]=gh(!0,(function(n,e,o){return t.factory.sketch(Vh(n,t,e,o))}))}),(function(t){var e=n.parts[t.name];r[t.name]=x(t.factory.sketch(Vh(n,t,e[Ph()]),e))}),(function(t){o[t.pname]=gh(!1,(function(n,e,o){return t.factory.sketch(Vh(n,t,e,o))}))}),(function(t){o[t.pname]=ph(!0,(function(n,e,o){var r=n[t.name];return X(r,(function(e){return t.factory.sketch(Sr(t.defaults(n,e,o),e,t.overrides(n,e)))}))}))}))})),{internals:x(o),externals:x(r)}},zh=function(t,n){var e={};return Y(n,(function(n){Dh(n).each((function(n){var o=Uh(t,n.pname);e[n.name]=function(e){var r=Si("Part: "+n.name+" in "+t,ti(n.schema),e);return D(D({},o),{config:e,validated:r})}}))})),e},Uh=function(t,n){return{uiType:hh(),owner:t,name:n}},jh=function(t,n,e){return{uiType:hh(),owner:t,name:n,config:e,validated:{}}},Wh=function(t){return ot(t,(function(t){return t.fold(N.none,N.some,N.none,N.none).map((function(t){return Pi(t.name,t.schema.concat([mf(Ph())]))})).toArray()}))},Gh=function(t){return X(t,Ah)},Xh=function(t,n,e){return Lh(t,n,e)},Yh=function(t,n,e){return mh(N.some(t),n,n.components,e)},qh=function(t,n,e){var o=n.partUids[e];return t.getSystem().getByUid(o).toOptional()},Kh=function(t,n,e){return qh(t,n,e).getOrDie("Could not find part: "+e)},Jh=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return Y(e,(function(t){o[t]=x(i.getByUid(r[t]))})),o},$h=function(t,n){var e=t.getSystem();return bt(n.partUids,(function(t,n){return x(e.getByUid(t))}))},Qh=function(t){return pt(t.partUids)},Zh=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return Y(e,(function(t){o[t]=x(i.getByUid(r[t]).getOrDie())})),o},tv=function(t,n){var e=Gh(n);return gu(X(e,(function(n){return{key:n,value:t+"-"+n}})))},nv=function(t){return _i("partUids","partUids",Er((function(n){return tv(n.uid,t)})),ui())},ev=Object.freeze({__proto__:null,generate:zh,generateOne:jh,schemas:Wh,names:Gh,substitutes:Xh,components:Yh,defaultUids:tv,defaultUidsSchema:nv,getAllParts:$h,getAllPartNames:Qh,getPart:qh,getPartOrDie:Kh,getParts:Jh,getPartsOrDie:Zh}),ov=function(t,n){var e=t.length>0?[Pi("parts",t)]:[];return e.concat([Di("uid"),qi("dom",{}),qi("components",[]),mf("originalSpec"),qi("debug.sketcher",{})]).concat(n)},rv=function(t,n,e,o,r){var i=ov(o,r);return Si(t+" [SpecSchema]",Zr(i.concat(n)),e)},iv=function(t,n,e,o){var r=cv(o),i=rv(t,n,r,[],[]);return e(i,r)},uv=function(t,n,e,o,r){var i=cv(r),u=Wh(e),a=nv(e),c=rv(t,n,i,u,[a]),s=Xh(t,c,e),f=Yh(t,c,s.internals());return o(c,f,i,s.externals())},av=function(t){return Tt(t,"uid")},cv=function(t){return av(t)?t:D(D({},t),{uid:dc("uid")})},sv=function(t){return void 0!==t.uid},fv=Zr([Di("name"),Di("factory"),Di("configFields"),qi("apis",{}),qi("extraApis",{})]),lv=Zr([Di("name"),Di("factory"),Di("configFields"),Di("partFields"),qi("apis",{}),qi("extraApis",{})]),dv=function(t){var n=Si("Sketcher for "+t.name,fv,t),e=function(t){return iv(n.name,n.configFields,n.factory,t)},o=bt(n.apis,kc),r=bt(n.extraApis,(function(t,n){return bc(t,n)}));return D(D({name:n.name,configFields:n.configFields,sketch:e},o),r)},mv=function(t){var n=Si("Sketcher for "+t.name,lv,t),e=function(t){return uv(n.name,n.configFields,n.partFields,n.factory,t)},o=zh(n.name,n.partFields),r=bt(n.apis,kc),i=bt(n.extraApis,(function(t,n){return bc(t,n)}));return D(D({name:n.name,partFields:n.partFields,configFields:n.configFields,sketch:e,parts:o},r),i)},gv=function(t){return"input"===cn(t)&&"radio"!==Ue(t,"type")||"textarea"===cn(t)},pv=function(t,n,e){return n.find(t)},hv=Object.freeze({__proto__:null,getCurrent:pv}),vv=[Di("find")],bv=pl({fields:vv,name:"composing",apis:hv}),yv=["input","button","textarea","select"],xv=function(t,n,e){var o=n.disabled()?Ev:Dv;o(t,n)},wv=function(t,n){return!0===n.useNative&&U(yv,cn(t.element))},Sv=function(t){return We(t.element,"disabled")},kv=function(t){Le(t.element,"disabled","disabled")},Cv=function(t){Ge(t.element,"disabled")},Ov=function(t){return"true"===Ue(t.element,"aria-disabled")},_v=function(t){Le(t.element,"aria-disabled","true")},Tv=function(t){Le(t.element,"aria-disabled","false")},Ev=function(t,n,e){n.disableClass.each((function(n){is(t.element,n)}));var o=wv(t,n)?kv:_v;o(t),n.onDisabled(t)},Dv=function(t,n,e){n.disableClass.each((function(n){as(t.element,n)}));var o=wv(t,n)?Cv:Tv;o(t),n.onEnabled(t)},Av=function(t,n){return wv(t,n)?Sv(t):Ov(t)},Bv=function(t,n,e,o){var r=o?Ev:Dv;r(t,n)},Mv=Object.freeze({__proto__:null,enable:Dv,disable:Ev,isDisabled:Av,onLoad:xv,set:Bv}),Fv=function(t,n){return Ac({classes:n.disabled()?n.disableClass.toArray():[]})},Iv=function(t,n){return Ea([Da(na(),(function(n,e){return Av(n,t)})),al(t,n,xv)])},Rv=Object.freeze({__proto__:null,exhibit:Fv,events:Iv}),Nv=[tu("disabled",_),qi("useNative",!0),Li("disableClass"),cf("onDisabled"),cf("onEnabled")],Pv=pl({fields:Nv,name:"disabling",active:Rv,apis:Mv}),Hv=function(t,n,e,o){var r=Km(t.element,"."+n.highlightClass);Y(r,(function(e){j(o,(function(t){return t.element===e}))||(as(e,n.highlightClass),t.getSystem().getByDom(e).each((function(e){n.onDehighlight(t,e),Sa(e,wa())})))}))},Vv=function(t,n,e){return Hv(t,n,e,[])},Lv=function(t,n,e,o){Xv(t,n,e,o)&&(as(o.element,n.highlightClass),n.onDehighlight(t,o),Sa(o,wa()))},zv=function(t,n,e,o){Hv(t,n,e,[o]),Xv(t,n,e,o)||(is(o.element,n.highlightClass),n.onHighlight(t,o),Sa(o,xa()))},Uv=function(t,n,e){Kv(t,n).each((function(o){zv(t,n,e,o)}))},jv=function(t,n,e){Jv(t,n).each((function(o){zv(t,n,e,o)}))},Wv=function(t,n,e,o){qv(t,n,e,o).fold((function(t){throw t}),(function(o){zv(t,n,e,o)}))},Gv=function(t,n,e,o){var r=tb(t,n),i=tt(r,o);i.each((function(o){zv(t,n,e,o)}))},Xv=function(t,n,e,o){return cs(o.element,n.highlightClass)},Yv=function(t,n,e){return Hs(t.element,"."+n.highlightClass).bind((function(n){return t.getSystem().getByDom(n).toOptional()}))},qv=function(t,n,e,o){var r=Km(t.element,"."+n.itemClass);return N.from(r[o]).fold((function(){return rr.error(new Error("No element found with index "+o))}),t.getSystem().getByDom)},Kv=function(t,n,e){return Hs(t.element,"."+n.itemClass).bind((function(n){return t.getSystem().getByDom(n).toOptional()}))},Jv=function(t,n,e){var o=Km(t.element,"."+n.itemClass),r=o.length>0?N.some(o[o.length-1]):N.none();return r.bind((function(n){return t.getSystem().getByDom(n).toOptional()}))},$v=function(t,n,e,o){var r=Km(t.element,"."+n.itemClass),i=nt(r,(function(t){return cs(t,n.highlightClass)}));return i.bind((function(n){var e=Ef(n,o,0,r.length-1);return t.getSystem().getByDom(r[e]).toOptional()}))},Qv=function(t,n,e){return $v(t,n,e,-1)},Zv=function(t,n,e){return $v(t,n,e,1)},tb=function(t,n,e){var o=Km(t.element,"."+n.itemClass);return Bt(X(o,(function(n){return t.getSystem().getByDom(n).toOptional()})))},nb=Object.freeze({__proto__:null,dehighlightAll:Vv,dehighlight:Lv,highlight:zv,highlightFirst:Uv,highlightLast:jv,highlightAt:Wv,highlightBy:Gv,isHighlighted:Xv,getHighlighted:Yv,getFirst:Kv,getLast:Jv,getPrevious:Qv,getNext:Zv,getCandidates:tb}),eb=[Di("highlightClass"),Di("itemClass"),cf("onHighlight"),cf("onDehighlight")],ob=pl({fields:eb,name:"highlighting",apis:nb}),rb=[8],ib=[9],ub=[13],ab=[27],cb=[32],sb=[37],fb=[38],lb=[39],db=[40],mb=function(t,n,e){var o=it(t.slice(0,n)),r=it(t.slice(n+1));return tt(o.concat(r),e)},gb=function(t,n,e){var o=it(t.slice(0,n));return tt(o,e)},pb=function(t,n,e){var o=t.slice(0,n),r=t.slice(n+1);return tt(r.concat(o),e)},hb=function(t,n,e){var o=t.slice(n+1);return tt(o,e)},vb=function(t){return function(n){var e=n.raw;return U(t,e.which)}},bb=function(t){return function(n){return rt(t,(function(t){return t(n)}))}},yb=function(t){var n=t.raw;return!0===n.shiftKey},xb=function(t){var n=t.raw;return!0===n.ctrlKey},wb=C(yb),Sb=function(t,n){return{matches:t,classification:n}},kb=function(t,n){var e=tt(t,(function(t){return t.matches(n)}));return e.map((function(t){return t.classification}))},Cb=function(t,n,e){var o=n.exists((function(t){return e.exists((function(n){return se(n,t)}))}));o||ka(t,ha(),{prevFocus:n,newFocus:e})},Ob=function(){var t=function(t){return Tl(t.element)},n=function(n,e){var o=t(n);n.getSystem().triggerFocus(e,n.element);var r=t(n);Cb(n,o,r)};return{get:t,set:n}},_b=function(){var t=function(t){return ob.getHighlighted(t).map((function(t){return t.element}))},n=function(n,e){var o=t(n);n.getSystem().getByDom(e).fold(h,(function(t){ob.highlight(n,t)}));var r=t(n);Cb(n,o,r)};return{get:t,set:n}};(function(t){t["OnFocusMode"]="onFocus",t["OnEnterOrSpaceMode"]="onEnterOrSpace",t["OnApiMode"]="onApi"})(Hm||(Hm={}));var Tb=function(t,n,e,o,r){var i=function(){return t.concat([qi("focusManager",Ob()),Ki("focusInside","onFocus",vi((function(t){return U(["onFocus","onEnterOrSpace","onApi"],t)?rr.value(t):rr.error("Invalid value for focusInside")}))),df("handler",c),df("state",n),df("sendFocusIn",r)])},u=function(t,n,e,o,r){var i=e(t,n,o,r);return kb(i,n.event).bind((function(e){return e(t,n,o,r)}))},a=function(t,n){var i=t.focusInside!==Hm.OnFocusMode?N.none():r(t).map((function(e){return Ma($u(),(function(o,r){e(o,t,n),r.stop()}))})),a=function(e,o){var i=vb(cb.concat(ub))(o.event);t.focusInside===Hm.OnEnterOrSpaceMode&&i&&wu(e,o)&&r(t).each((function(r){r(e,t,n),o.stop()}))},c=[Ma(Lu(),(function(o,r){u(o,r,e,t,n).fold((function(){a(o,r)}),(function(t){r.stop()}))})),Ma(zu(),(function(e,r){u(e,r,o,t,n).each((function(t){r.stop()}))}))];return Ea(i.toArray().concat(c))},c={schema:i,processKey:u,toEvents:a};return c},Eb=function(t){var n=[Li("onEscape"),Li("onEnter"),qi("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),qi("firstTabstop",0),qi("useTabstopAt",T),Li("visibilitySelector")].concat([t]),e=function(t,n){var e=t.visibilitySelector.bind((function(t){return Vs(n,t)})).getOr(n);return uo(e)>0},o=function(t,n){var o=Km(t.element,n.selector),r=J(o,(function(t){return e(n,t)}));return N.from(r[n.firstTabstop])},r=function(t,n){return n.focusManager.get(t).bind((function(t){return Vs(t,n.selector)}))},i=function(t,n){return e(t,n)&&t.useTabstopAt(n)},u=function(t,n,e){o(t,n).each((function(e){n.focusManager.set(t,e)}))},a=function(t,n,e,o,r){return r(n,e,(function(t){return i(o,t)})).fold((function(){return o.cyclic?N.some(!0):N.none()}),(function(n){return o.focusManager.set(t,n),N.some(!0)}))},c=function(t,n,e,o){var i=Km(t.element,e.selector);return r(t,e).bind((function(n){var r=nt(i,k(se,n));return r.bind((function(n){return a(t,i,n,e,o)}))}))},s=function(t,n,e){var o=e.cyclic?mb:gb;return c(t,n,e,o)},f=function(t,n,e){var o=e.cyclic?pb:hb;return c(t,n,e,o)},l=function(t,n,e){return e.onEnter.bind((function(e){return e(t,n)}))},d=function(t,n,e){return e.onEscape.bind((function(e){return e(t,n)}))},m=x([Sb(bb([yb,vb(ib)]),s),Sb(vb(ib),f),Sb(vb(ab),d),Sb(bb([wb,vb(ub)]),l)]),g=x([]);return Tb(n,Cc.init,m,g,(function(){return N.some(u)}))},Db=Eb(Ti("cyclic",_)),Ab=Eb(Ti("cyclic",T)),Bb=function(t,n,e){return Oa(t,e,na()),N.some(!0)},Mb=function(t,n,e){var o=gv(e)&&vb(cb)(n.event);return o?N.none():Bb(t,n,e)},Fb=function(t,n){return N.some(!0)},Ib=[qi("execute",Mb),qi("useSpace",!1),qi("useEnter",!0),qi("useControlEnter",!1),qi("useDown",!1)],Rb=function(t,n,e){return e.execute(t,n,t.element)},Nb=function(t,n,e,o){var r=e.useSpace&&!gv(t.element)?cb:[],i=e.useEnter?ub:[],u=e.useDown?db:[],a=r.concat(i).concat(u);return[Sb(vb(a),Rb)].concat(e.useControlEnter?[Sb(bb([xb,vb(ub)]),Rb)]:[])},Pb=function(t,n,e,o){return e.useSpace&&!gv(t.element)?[Sb(vb(cb),Fb)]:[]},Hb=Tb(Ib,Cc.init,Nb,Pb,(function(){return N.none()})),Vb=function(){var t=rd(),n=function(n,e){t.set({numRows:n,numColumns:e})},e=function(){return t.get().map((function(t){return t.numRows}))},o=function(){return t.get().map((function(t){return t.numColumns}))};return Oc({readState:function(){return t.get().map((function(t){return{numRows:String(t.numRows),numColumns:String(t.numColumns)}})).getOr({numRows:"?",numColumns:"?"})},setGridSize:n,getNumRows:e,getNumColumns:o})},Lb=function(t){return t.state(t)},zb=Object.freeze({__proto__:null,flatgrid:Vb,init:Lb}),Ub=function(t){return function(n,e,o,r){var i=t(n.element);return Xb(i,n,e,o,r)}},jb=function(t,n){var e=Rd(t,n);return Ub(e)},Wb=function(t,n){var e=Rd(n,t);return Ub(e)},Gb=function(t){return function(n,e,o,r){return Xb(t,n,e,o,r)}},Xb=function(t,n,e,o,r){var i=o.focusManager.get(n).bind((function(e){return t(n.element,e,o,r)}));return i.map((function(t){return o.focusManager.set(n,t),!0}))},Yb=Gb,qb=Gb,Kb=Gb,Jb=function(t){return t.offsetWidth<=0&&t.offsetHeight<=0},$b=function(t){return!Jb(t.dom)},Qb=function(t,n){return nt(t,n).map((function(n){return{index:n,candidates:t}}))},Zb=function(t,n,e){var o=function(t){return se(t,n)},r=Km(t,e),i=J(r,$b);return Qb(i,o)},ty=function(t,n){return nt(t,(function(t){return se(n,t)}))},ny=function(t,n,e,o){var r=Math.floor(n/e),i=n%e;return o(r,i).bind((function(n){var o=n.row*e+n.column;return o>=0&&o<t.length?N.some(t[o]):N.none()}))},ey=function(t,n,e,o,r){return ny(t,n,o,(function(n,i){var u=n===e-1,a=u?t.length-n*o:o,c=Ef(i,r,0,a-1);return N.some({row:n,column:c})}))},oy=function(t,n,e,o,r){return ny(t,n,o,(function(n,i){var u=Ef(n,r,0,e-1),a=u===e-1,c=a?t.length-u*o:o,s=Df(i,0,c-1);return N.some({row:u,column:s})}))},ry=function(t,n,e,o){return ey(t,n,e,o,1)},iy=function(t,n,e,o){return ey(t,n,e,o,-1)},uy=function(t,n,e,o){return oy(t,n,e,o,-1)},ay=function(t,n,e,o){return oy(t,n,e,o,1)},cy=[Di("selector"),qi("execute",Mb),sf("onEscape"),qi("captureTab",!1),gf()],sy=function(t,n,e){Hs(t.element,n.selector).each((function(e){n.focusManager.set(t,e)}))},fy=function(t,n){return n.focusManager.get(t).bind((function(t){return Vs(t,n.selector)}))},ly=function(t,n,e,o){return fy(t,e).bind((function(o){return e.execute(t,n,o)}))},dy=function(t){return function(n,e,o,r){return Zb(n,e,o.selector).bind((function(n){return t(n.candidates,n.index,r.getNumRows().getOr(o.initSize.numRows),r.getNumColumns().getOr(o.initSize.numColumns))}))}},my=function(t,n,e){return e.captureTab?N.some(!0):N.none()},gy=function(t,n,e){return e.onEscape(t,n)},py=dy(iy),hy=dy(ry),vy=dy(uy),by=dy(ay),yy=x([Sb(vb(sb),jb(py,hy)),Sb(vb(lb),Wb(py,hy)),Sb(vb(fb),Yb(vy)),Sb(vb(db),qb(by)),Sb(bb([yb,vb(ib)]),my),Sb(bb([wb,vb(ib)]),my),Sb(vb(ab),gy),Sb(vb(cb.concat(ub)),ly)]),xy=x([Sb(vb(cb),Fb)]),wy=Tb(cy,Vb,yy,xy,(function(){return N.some(sy)})),Sy=function(t,n,e,o){var r=function(t){return"button"===cn(t)&&"disabled"===Ue(t,"disabled")},i=function(t,n,e){var u=Ef(n,o,0,e.length-1);return u===t?N.none():r(e[u])?i(t,u,e):N.from(e[u])};return Zb(t,e,n).bind((function(t){var n=t.index,e=t.candidates;return i(n,n,e)}))},ky=[Di("selector"),qi("getInitial",N.none),qi("execute",Mb),sf("onEscape"),qi("executeOnMove",!1),qi("allowVertical",!0)],Cy=function(t,n){return n.focusManager.get(t).bind((function(t){return Vs(t,n.selector)}))},Oy=function(t,n,e){return Cy(t,e).bind((function(o){return e.execute(t,n,o)}))},_y=function(t,n,e){n.getInitial(t).orThunk((function(){return Hs(t.element,n.selector)})).each((function(e){n.focusManager.set(t,e)}))},Ty=function(t,n,e){return Sy(t,e.selector,n,-1)},Ey=function(t,n,e){return Sy(t,e.selector,n,1)},Dy=function(t){return function(n,e,o,r){return t(n,e,o,r).bind((function(){return o.executeOnMove?Oy(n,e,o):N.some(!0)}))}},Ay=function(t,n,e){return e.onEscape(t,n)},By=function(t,n,e,o){var r=sb.concat(e.allowVertical?fb:[]),i=lb.concat(e.allowVertical?db:[]);return[Sb(vb(r),Dy(jb(Ty,Ey))),Sb(vb(i),Dy(Wb(Ty,Ey))),Sb(vb(ub),Oy),Sb(vb(cb),Oy),Sb(vb(ab),Ay)]},My=x([Sb(vb(cb),Fb)]),Fy=Tb(ky,Cc.init,By,My,(function(){return N.some(_y)})),Iy=function(t,n,e){return N.from(t[n]).bind((function(t){return N.from(t[e]).map((function(t){return{rowIndex:n,columnIndex:e,cell:t}}))}))},Ry=function(t,n,e,o){var r=t[n],i=r.length,u=Ef(e,o,0,i-1);return Iy(t,n,u)},Ny=function(t,n,e,o){var r=Ef(e,o,0,t.length-1),i=t[r].length,u=Df(n,0,i-1);return Iy(t,r,u)},Py=function(t,n,e,o){var r=t[n],i=r.length,u=Df(e+o,0,i-1);return Iy(t,n,u)},Hy=function(t,n,e,o){var r=Df(e+o,0,t.length-1),i=t[r].length,u=Df(n,0,i-1);return Iy(t,r,u)},Vy=function(t,n,e){return Ry(t,n,e,1)},Ly=function(t,n,e){return Ry(t,n,e,-1)},zy=function(t,n,e){return Ny(t,e,n,-1)},Uy=function(t,n,e){return Ny(t,e,n,1)},jy=function(t,n,e){return Py(t,n,e,-1)},Wy=function(t,n,e){return Py(t,n,e,1)},Gy=function(t,n,e){return Hy(t,e,n,-1)},Xy=function(t,n,e){return Hy(t,e,n,1)},Yy=[Pi("selectors",[Di("row"),Di("cell")]),qi("cycles",!0),qi("previousSelector",N.none),qi("execute",Mb)],qy=function(t,n,e){var o=n.previousSelector(t).orThunk((function(){var e=n.selectors;return Hs(t.element,e.cell)}));o.each((function(e){n.focusManager.set(t,e)}))},Ky=function(t,n,e){return Tl(t.element).bind((function(o){return e.execute(t,n,o)}))},Jy=function(t,n){return X(t,(function(t){return Km(t,n.selectors.cell)}))},$y=function(t,n){return function(e,o,r){var i=r.cycles?t:n;return Vs(o,r.selectors.row).bind((function(t){var n=Km(t,r.selectors.cell);return ty(n,o).bind((function(n){var o=Km(e,r.selectors.row);return ty(o,t).bind((function(t){var e=Jy(o,r);return i(e,t,n).map((function(t){return t.cell}))}))}))}))}},Qy=$y(Ly,jy),Zy=$y(Vy,Wy),tx=$y(zy,Gy),nx=$y(Uy,Xy),ex=x([Sb(vb(sb),jb(Qy,Zy)),Sb(vb(lb),Wb(Qy,Zy)),Sb(vb(fb),Yb(tx)),Sb(vb(db),qb(nx)),Sb(vb(cb.concat(ub)),Ky)]),ox=x([Sb(vb(cb),Fb)]),rx=Tb(Yy,Cc.init,ex,ox,(function(){return N.some(qy)})),ix=[Di("selector"),qi("execute",Mb),qi("moveOnTab",!1)],ux=function(t,n,e){return e.focusManager.get(t).bind((function(o){return e.execute(t,n,o)}))},ax=function(t,n,e){Hs(t.element,n.selector).each((function(e){n.focusManager.set(t,e)}))},cx=function(t,n,e){return Sy(t,e.selector,n,-1)},sx=function(t,n,e){return Sy(t,e.selector,n,1)},fx=function(t,n,e,o){return e.moveOnTab?Kb(cx)(t,n,e,o):N.none()},lx=function(t,n,e,o){return e.moveOnTab?Kb(sx)(t,n,e,o):N.none()},dx=x([Sb(vb(fb),Kb(cx)),Sb(vb(db),Kb(sx)),Sb(bb([yb,vb(ib)]),fx),Sb(bb([wb,vb(ib)]),lx),Sb(vb(ub),ux),Sb(vb(cb),ux)]),mx=x([Sb(vb(cb),Fb)]),gx=Tb(ix,Cc.init,dx,mx,(function(){return N.some(ax)})),px=[sf("onSpace"),sf("onEnter"),sf("onShiftEnter"),sf("onLeft"),sf("onRight"),sf("onTab"),sf("onShiftTab"),sf("onUp"),sf("onDown"),sf("onEscape"),qi("stopSpaceKeyup",!1),Li("focusIn")],hx=function(t,n,e){return[Sb(vb(cb),e.onSpace),Sb(bb([wb,vb(ub)]),e.onEnter),Sb(bb([yb,vb(ub)]),e.onShiftEnter),Sb(bb([yb,vb(ib)]),e.onShiftTab),Sb(bb([wb,vb(ib)]),e.onTab),Sb(vb(fb),e.onUp),Sb(vb(db),e.onDown),Sb(vb(sb),e.onLeft),Sb(vb(lb),e.onRight),Sb(vb(cb),e.onSpace),Sb(vb(ab),e.onEscape)]},vx=function(t,n,e){return e.stopSpaceKeyup?[Sb(vb(cb),Fb)]:[]},bx=Tb(px,Cc.init,hx,vx,(function(t){return t.focusIn})),yx=Db.schema(),xx=Ab.schema(),wx=Fy.schema(),Sx=wy.schema(),kx=rx.schema(),Cx=Hb.schema(),Ox=gx.schema(),_x=bx.schema(),Tx=Object.freeze({__proto__:null,acyclic:yx,cyclic:xx,flow:wx,flatgrid:Sx,matrix:kx,execution:Cx,menu:Ox,special:_x}),Ex=function(t){return Et(t,"setGridSize")},Dx=vl({branchKey:"mode",branches:Tx,name:"keying",active:{events:function(t,n){var e=t.handler;return e.toEvents(t,n)}},apis:{focusIn:function(t,n,e){n.sendFocusIn(n).fold((function(){t.getSystem().triggerFocus(t.element,t.element)}),(function(o){o(t,n,e)}))},setGridSize:function(t,n,e,o,r){Ex(e)&&e.setGridSize(o,r)}},state:zb}),Ax=function(t,n,e,o){El((function(){var n=X(o,t.getSystem().build);jg(t,n)}),t.element)},Bx=function(t,n,e,o){var r=t.getSystem().build(o);Gg(t,r,e)},Mx=function(t,n,e,o){Bx(t,n,Bo,o)},Fx=function(t,n,e,o){Bx(t,n,Ao,o)},Ix=function(t,n,e,o){var r=Rx(t),i=tt(r,(function(t){return se(o.element,t.element)}));i.each(Yg)},Rx=function(t,n){return t.components()},Nx=function(t,n,e,o,r){var i=Rx(t);return N.from(i[o]).map((function(i){return Ix(t,n,e,i),r.each((function(e){Bx(t,n,(function(t,n){Mo(t,n,o)}),e)})),i}))},Px=function(t,n,e,o,r){var i=Rx(t);return nt(i,o).bind((function(o){return Nx(t,n,e,o,r)}))},Hx=Object.freeze({__proto__:null,append:Mx,prepend:Fx,remove:Ix,replaceAt:Nx,replaceBy:Px,set:Ax,contents:Rx}),Vx=pl({fields:[],name:"replacing",apis:Hx}),Lx=function(t,n){var e=Ea(n);return pl({fields:[Di("enabled")],name:t,active:{events:x(e)}})},zx=function(t,n){var e=Lx(t,n);return{key:t,value:{config:{},me:e,configAsRaw:x({}),initialConfig:{},state:Cc}}},Ux=function(t,n){n.ignore||(kl(t.element),n.onFocus(t))},jx=function(t,n){n.ignore||Cl(t.element)},Wx=function(t){return Ol(t.element)},Gx=Object.freeze({__proto__:null,focus:Ux,blur:jx,isFocused:Wx}),Xx=function(t,n){var e=n.ignore?{}:{attributes:{tabindex:"-1"}};return Ac(e)},Yx=function(t){return Ea([Ma($u(),(function(n,e){Ux(n,t),e.stop()}))].concat(t.stopMousedown?[Ma(Fu(),(function(t,n){n.event.prevent()}))]:[]))},qx=Object.freeze({__proto__:null,exhibit:Xx,events:Yx}),Kx=[cf("onFocus"),qi("stopMousedown",!1),qi("ignore",!1)],Jx=pl({fields:Kx,name:"focusing",active:qx,apis:Gx}),$x=function(t){var n=function(){var n=ru(t),e=function(){return n.get()},o=function(t){return n.set(t)},r=function(){return n.set(t)},i=function(){return n.get()};return{get:e,set:o,clear:r,readState:i}};return{init:n}},Qx=function(t,n,e){var o=n.aria;o.update(t,o,e.get())},Zx=function(t,n,e){n.toggleClass.each((function(n){e.get()?is(t.element,n):as(t.element,n)}))},tw=function(t,n,e){ow(t,n,e,!e.get())},nw=function(t,n,e){e.set(!0),Zx(t,n,e),Qx(t,n,e)},ew=function(t,n,e){e.set(!1),Zx(t,n,e),Qx(t,n,e)},ow=function(t,n,e,o){var r=o?nw:ew;r(t,n,e)},rw=function(t,n,e){return e.get()},iw=function(t,n,e){ow(t,n,e,n.selected)},uw=Object.freeze({__proto__:null,onLoad:iw,toggle:tw,isOn:rw,on:nw,off:ew,set:ow}),aw=function(){return Ac({})},cw=function(t,n){var e=ul(t,n,tw),o=al(t,n,iw);return Ea(et([t.toggleOnExecute?[e]:[],[o]]))},sw=Object.freeze({__proto__:null,exhibit:aw,events:cw}),fw=function(t,n,e){Le(t.element,"aria-pressed",e),n.syncWithExpanded&&mw(t,n,e)},lw=function(t,n,e){Le(t.element,"aria-selected",e)},dw=function(t,n,e){Le(t.element,"aria-checked",e)},mw=function(t,n,e){Le(t.element,"aria-expanded",e)},gw=[qi("selected",!1),Li("toggleClass"),qi("toggleOnExecute",!0),Ki("aria",{mode:"none"},Ci("mode",{pressed:[qi("syncWithExpanded",!1),df("update",fw)],checked:[df("update",dw)],expanded:[df("update",mw)],selected:[df("update",lw)],none:[df("update",h)]}))],pw=pl({fields:gw,name:"toggling",active:sw,apis:uw,state:$x(!1)}),hw=function(){var t=function(t,n){n.stop(),Ca(t)};return[Ma(Wu(),t),Ma(oa(),t),Va(Du()),Va(Fu())]},vw=function(t){var n=function(t){return Ga((function(n,e){t(n),e.stop()}))};return Ea(et([t.map(n).toArray(),hw()]))},bw="alloy.item-hover",yw="alloy.item-focus",xw=function(t){(Tl(t.element).isNone()||Jx.isFocused(t))&&(Jx.isFocused(t)||Jx.focus(t),ka(t,bw,{item:t}))},ww=function(t){ka(t,yw,{item:t})},Sw=x(bw),kw=x(yw),Cw=function(t){return{dom:t.dom,domModification:D(D({},t.domModification),{attributes:D(D(D({role:t.toggling.isSome()?"menuitemcheckbox":"menuitem"},t.domModification.attributes),{"aria-haspopup":t.hasSubmenu}),t.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:rh.augment(t.itemBehaviours,[t.toggling.fold(pw.revoke,(function(t){return pw.config(D({aria:{mode:"checked"}},t))})),Jx.config({ignore:t.ignoreFocus,stopMousedown:t.ignoreFocus,onFocus:function(t){ww(t)}}),Dx.config({mode:"execution"}),th.config({store:{mode:"memory",initialValue:t.data}}),zx("item-type-events",B(B([],hw(),!0),[Ma(Pu(),xw),Ma(ea(),Jx.focus)],!1))]),components:t.components,eventOrder:t.eventOrder}},Ow=[Di("data"),Di("components"),Di("dom"),qi("hasSubmenu",!1),Li("toggling"),rh.field("itemBehaviours",[pw,Jx,Dx,th]),qi("ignoreFocus",!1),qi("domModification",{}),df("builder",Cw),qi("eventOrder",{})],_w=function(t){return{dom:t.dom,components:t.components,events:Ea([La(ea())])}},Tw=[Di("dom"),Di("components"),df("builder",_w)],Ew=x("item-widget"),Dw=x([Fh({name:"widget",overrides:function(t){return{behaviours:ml([th.config({store:{mode:"manual",getValue:function(n){return t.data},setValue:h}})])}}})]),Aw=function(t){var n=Xh(Ew(),t,Dw()),e=Yh(Ew(),t,n.internals()),o=function(n){return qh(n,t,"widget").map((function(t){return Dx.focusIn(t),t}))},r=function(n,e){return gv(e.event.target)?N.none():function(){return t.autofocus?(e.setSource(n.element),N.none()):N.none()}()};return{dom:t.dom,components:e,domModification:t.domModification,events:Ea([Ga((function(t,n){o(t).each((function(t){n.stop()}))})),Ma(Pu(),xw),Ma(ea(),(function(n,e){t.autofocus?o(n):Jx.focus(n)}))]),behaviours:rh.augment(t.widgetBehaviours,[th.config({store:{mode:"memory",initialValue:t.data}}),Jx.config({ignore:t.ignoreFocus,onFocus:function(t){ww(t)}}),Dx.config({mode:"special",focusIn:t.autofocus?function(t){o(t)}:bl(),onLeft:r,onRight:r,onEscape:function(n,e){return Jx.isFocused(n)||t.autofocus?t.autofocus?(e.setSource(n.element),N.none()):N.none():(Jx.focus(n),N.some(!0))}})])}},Bw=[Di("uid"),Di("data"),Di("components"),Di("dom"),qi("autofocus",!1),qi("ignoreFocus",!1),rh.field("widgetBehaviours",[th,Jx,Dx]),qi("domModification",{}),nv(Dw()),df("builder",Aw)],Mw=Ci("type",{widget:Bw,item:Ow,separator:Tw}),Fw=function(t,n){return{mode:"flatgrid",selector:"."+t.markers.item,initSize:{numColumns:n.initSize.numColumns,numRows:n.initSize.numRows},focusManager:t.focusManager}},Iw=function(t,n){return{mode:"matrix",selectors:{row:n.rowSelector,cell:"."+t.markers.item},focusManager:t.focusManager}},Rw=function(t,n){return{mode:"menu",selector:"."+t.markers.item,moveOnTab:n.moveOnTab,focusManager:t.focusManager}},Nw=x([Nh({factory:{sketch:function(t){var n=Si("menu.spec item",Mw,t);return n.builder(n)}},name:"items",unit:"item",defaults:function(t,n){return Tt(n,"uid")?n:D(D({},n),{uid:dc("item")})},overrides:function(t,n){return{type:n.type,ignoreFocus:t.fakeFocus,domModification:{classes:[t.markers.item]}}}})]),Pw=x([Di("value"),Di("items"),Di("dom"),Di("components"),qi("eventOrder",{}),nh("menuBehaviours",[ob,th,bv,Dx]),Ki("movement",{mode:"menu",moveOnTab:!0},Ci("mode",{grid:[gf(),df("config",Fw)],matrix:[df("config",Iw),Di("rowSelector")],menu:[qi("moveOnTab",!0),df("config",Rw)]})),of(),qi("fakeFocus",!1),qi("focusManager",Ob()),cf("onHighlight")]),Hw=x("alloy.menu-focus"),Vw=function(t,n,e,o){return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:oh(t.menuBehaviours,[ob.config({highlightClass:t.markers.selectedItem,itemClass:t.markers.item,onHighlight:t.onHighlight}),th.config({store:{mode:"memory",initialValue:t.value}}),bv.config({find:N.some}),Dx.config(t.movement.config(t,t.movement))]),events:Ea([Ma(kw(),(function(t,n){var e=n.event;t.getSystem().getByDom(e.target).each((function(e){ob.highlight(t,e),n.stop(),ka(t,Hw(),{menu:t,item:e})}))})),Ma(Sw(),(function(t,n){var e=n.event.item;ob.highlight(t,e)}))]),components:n,eventOrder:t.eventOrder,domModification:{attributes:{role:"menu"}}}},Lw=mv({name:"Menu",configFields:Pw(),partFields:Nw(),factory:Vw}),zw=function(t){return yt(t,(function(t,n){return{k:t,v:n}}))},Uw=function(t,n,e,o){return _t(e,o).bind((function(o){return _t(t,o).bind((function(o){var r=Uw(t,n,e,o);return N.some([o].concat(r))}))})).getOr([])},jw=function(t,n){var e={};vt(t,(function(t,n){Y(t,(function(t){e[t]=n}))}));var o=n,r=zw(n),i=bt(r,(function(t,n){return[n].concat(Uw(e,o,r,n))}));return bt(e,(function(t){return _t(i,t).getOr([t])}))},Ww=function(){var t=ru({}),n=ru({}),e=ru({}),o=rd(),r=ru({}),i=function(){t.set({}),n.set({}),e.set({}),o.clear()},u=function(){return o.get().isNone()},a=function(t,e){var o;n.set(D(D({},n.get()),(o={},o[t]={type:"prepared",menu:e},o)))},c=function(i,u,a,c){o.set(i),t.set(a),n.set(u),r.set(c);var s=jw(c,a);e.set(s)},s=function(n){return Ct(t.get(),(function(t,e){return t===n}))},f=function(t,n,e){return p(t).bind((function(o){return s(t).bind((function(t){return n(t).map((function(t){return{triggeredMenu:o,triggeringItem:t,triggeringPath:e}}))}))}))},l=function(t,n){var r=J(v(t).toArray(),(function(t){return p(t).isSome()}));return _t(e.get(),t).bind((function(t){var e=it(r.concat(t)),i=ot(e,(function(t,r){return f(t,n,e.slice(0,r+1)).fold((function(){return Dt(o.get(),t)?[]:[N.none()]}),(function(t){return[N.some(t)]}))}));return Mt(i)}))},d=function(n){return _t(t.get(),n).map((function(t){var o=_t(e.get(),n).getOr([]);return[t].concat(o)}))},m=function(t){return _t(e.get(),t).bind((function(t){return t.length>1?N.some(t.slice(1)):N.none()}))},g=function(t){return _t(e.get(),t)},p=function(t){return h(t).bind(Gw)},h=function(t){return _t(n.get(),t)},v=function(n){return _t(t.get(),n)},b=function(t){var n=r.get();return ut(pt(n),t)},y=function(){return o.get().bind(p)},x=function(){return n.get()};return{setMenuBuilt:a,setContents:c,expand:d,refresh:g,collapse:m,lookupMenu:h,lookupItem:v,otherMenus:b,getPrimary:y,getMenus:x,clear:i,isClear:u,getTriggeringPath:l}},Gw=function(t){return"prepared"===t.type?N.some(t.menu):N.none()},Xw={init:Ww,extractPreparedMenu:Gw},Yw=function(t,n){var e,o=rd(),r=function(n,e,o){return bt(o,(function(o,r){var i=function(){return Lw.sketch(D(D({},o),{value:r,markers:t.markers,fakeFocus:t.fakeFocus,onHighlight:t.onHighlight,focusManager:t.fakeFocus?_b():Ob()}))};return r===e?{type:"prepared",menu:n.getSystem().build(i())}:{type:"notbuilt",nbMenu:i}}))},i=Xw.init(),u=function(n){var e=r(n,t.data.primary,t.data.menus),o=s();return i.setContents(t.data.primary,e,t.data.expansions,o),i.getPrimary()},a=function(t){return th.getValue(t).value},c=function(t,n,e){return gt(n,(function(t){if(!t.getSystem().isConnected())return N.none();var n=ob.getCandidates(t);return tt(n,(function(t){return a(t)===e}))}))},s=function(n){return bt(t.data.menus,(function(t,n){return ot(t.items,(function(t){return"separator"===t.type?[]:[t.data.value]}))}))},f=function(t,n){ob.highlight(t,n),ob.getHighlighted(n).orThunk((function(){return ob.getFirst(n)})).each((function(n){Oa(t,n.element,ea())}))},l=function(t,n){return Bt(X(n,(function(n){return t.lookupMenu(n).bind((function(t){return"prepared"===t.type?N.some(t.menu):N.none()}))})))},d=function(n,e,o){var r=l(e,e.otherMenus(o));Y(r,(function(e){fs(e.element,[t.markers.backgroundMenu]),t.stayInDom||Vx.remove(n,e)}))},m=function(n){return o.get().getOrThunk((function(){var e={},r=Km(n.element,"."+t.markers.item),i=J(r,(function(t){return"true"===Ue(t,"aria-haspopup")}));return Y(i,(function(t){n.getSystem().getByDom(t).each((function(t){var n=a(t);e[n]=t}))})),o.set(e),e}))},g=function(t,n){var e=m(t);vt(e,(function(t,e){var o=U(n,e);Le(t.element,"aria-expanded",o)}))},p=function(n,e,o){return N.from(o[0]).bind((function(r){return e.lookupMenu(r).bind((function(r){if("notbuilt"===r.type)return N.none();var i=r.menu,u=l(e,o.slice(1));return Y(u,(function(n){is(n.element,t.markers.backgroundMenu)})),Ne(i.element)||Vx.append(n,Es(i)),fs(i.element,[t.markers.backgroundMenu]),f(n,i),d(n,e,o),N.some(i)}))}))};(function(t){t[t["HighlightSubmenu"]=0]="HighlightSubmenu",t[t["HighlightParent"]=1]="HighlightParent"})(e||(e={}));var v=function(t,n,e){if("notbuilt"===e.type){var o=t.getSystem().build(e.nbMenu());return i.setMenuBuilt(n,o),o}return e.menu},b=function(n,o,r){if(void 0===r&&(r=e.HighlightSubmenu),o.hasConfigured(Pv)&&Pv.isDisabled(o))return N.some(o);var u=a(o);return i.expand(u).bind((function(u){return g(n,u),N.from(u[0]).bind((function(a){return i.lookupMenu(a).bind((function(c){var s=v(n,a,c);return Ne(s.element)||Vx.append(n,Es(s)),t.onOpenSubmenu(n,o,s,it(u)),r===e.HighlightSubmenu?(ob.highlightFirst(s),p(n,i,u)):(ob.dehighlightAll(s),N.some(o))}))}))}))},y=function(n,e){var o=a(e);return i.collapse(o).bind((function(o){return g(n,o),p(n,i,o).map((function(o){return t.onCollapseMenu(n,e,o),o}))}))},x=function(t,n){var e=a(n);return i.refresh(e).bind((function(n){return g(t,n),p(t,i,n)}))},w=function(t,n){return gv(n.element)?N.none():b(t,n,e.HighlightSubmenu)},S=function(t,n){return gv(n.element)?N.none():y(t,n)},k=function(n,e){return y(n,e).orThunk((function(){return t.onEscape(n,e).map((function(){return n}))}))},C=function(n){return function(e,o){return Vs(o.getSource(),"."+t.markers.item).bind((function(t){return e.getSystem().getByDom(t).toOptional().bind((function(t){return n(e,t).map(T)}))}))}},O=Ea([Ma(Hw(),(function(t,n){var e=n.event.item;i.lookupItem(a(e)).each((function(){var e=n.event.menu;ob.highlight(t,e);var o=a(n.event.item);i.refresh(o).each((function(n){return d(t,i,n)}))}))})),Ga((function(n,o){var r=o.event.target;n.getSystem().getByDom(r).each((function(o){var r=a(o);0===r.indexOf("collapse-item")&&y(n,o),b(n,o,e.HighlightSubmenu).fold((function(){t.onExecute(n,o)}),h)}))})),Ua((function(n,e){u(n).each((function(e){Vx.append(n,Es(e)),t.onOpenMenu(n,e),t.highlightImmediately&&f(n,e)}))}))].concat(t.navigateOnHover?[Ma(Sw(),(function(n,o){var r=o.event.item;x(n,r),b(n,r,e.HighlightParent),t.onHover(n,r)}))]:[])),_=function(t){return ob.getHighlighted(t).bind(ob.getHighlighted)},E=function(t){_(t).each((function(n){y(t,n)}))},A=function(t){i.getPrimary().each((function(n){f(t,n)}))},B=function(t){return N.from(t.components()[0]).filter((function(t){return"menu"===Ue(t.element,"role")}))},M=function(n){var e=i.getPrimary().bind((function(t){return _(n).bind((function(t){var e=a(t),o=Ot(i.getMenus()),r=Bt(X(o,Xw.extractPreparedMenu));return i.getTriggeringPath(e,(function(t){return c(n,r,t)}))})).map((function(n){return{primary:t,triggeringPath:n}}))}));e.fold((function(){B(n).each((function(e){t.onRepositionMenu(n,e,[])}))}),(function(e){var o=e.primary,r=e.triggeringPath;t.onRepositionMenu(n,o,r)}))},F={collapseMenu:E,highlightPrimary:A,repositionMenus:M};return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:oh(t.tmenuBehaviours,[Dx.config({mode:"special",onRight:C(w),onLeft:C(S),onEscape:C(k),focusIn:function(t,n){i.getPrimary().each((function(n){Oa(t,n.element,ea())}))}}),ob.config({highlightClass:t.markers.selectedMenu,itemClass:t.markers.menu}),bv.config({find:function(t){return ob.getHighlighted(t)}}),Vx.config({})]),eventOrder:t.eventOrder,apis:F,events:O}},qw=x("collapse-item"),Kw=function(t,n,e){return{primary:t,menus:n,expansions:e}},Jw=function(t,n){return{primary:t,menus:mu(t,n),expansions:{}}},$w=function(t){return{value:rc(qw()),meta:{text:t}}},Qw=dv({name:"TieredMenu",configFields:[lf("onExecute"),lf("onEscape"),ff("onOpenMenu"),ff("onOpenSubmenu"),cf("onRepositionMenu"),cf("onCollapseMenu"),qi("highlightImmediately",!0),Pi("data",[Di("primary"),Di("menus"),Di("expansions")]),qi("fakeFocus",!1),cf("onHighlight"),cf("onHover"),rf(),Di("dom"),qi("navigateOnHover",!0),qi("stayInDom",!1),nh("tmenuBehaviours",[Dx,ob,bv,Vx]),qi("eventOrder",{})],apis:{collapseMenu:function(t,n){t.collapseMenu(n)},highlightPrimary:function(t,n){t.highlightPrimary(n)},repositionMenus:function(t,n){t.repositionMenus(n)}},factory:Yw,extraApis:{tieredData:Kw,singleData:Jw,collapseItem:$w}}),Zw=function(t,n,e,o,r){var i=function(){return t.lazySink(n)},u="horizontal"===o.type?{layouts:{onLtr:function(){return tl()},onRtl:function(){return nl()}}}:{},a=function(t){return 2===t.length},c=function(t){return a(t)?u:{}};return Qw.sketch({dom:{tag:"div"},data:o.data,markers:o.menu.markers,highlightImmediately:o.menu.highlightImmediately,onEscape:function(){return bp.close(n),t.onEscape.map((function(t){return t(n)})),N.some(!0)},onExecute:function(){return N.some(!0)},onOpenMenu:function(t,n){Hg.positionWithinBounds(i().getOrDie(),n,e,r())},onOpenSubmenu:function(t,n,e,o){var r=i().getOrDie();Hg.position(r,e,{anchor:D({type:"submenu",item:n},c(o))})},onRepositionMenu:function(t,n,o){var u=i().getOrDie();Hg.positionWithinBounds(u,n,e,r()),Y(o,(function(t){var n=c(t.triggeringPath);Hg.position(u,t.triggeredMenu,{anchor:D({type:"submenu",item:t.triggeringItem},n)})}))}})},tS=function(t,n){var e=function(n,e){var o=t.getRelated(n);return o.exists((function(t){return js(t,e)}))},o=function(t,n){bp.setContent(t,n)},r=function(t,n,e){i(t,n,e,N.none())},i=function(t,n,e,o){u(t,n,e,(function(){return o.map((function(t){return Qo(t)}))}))},u=function(n,e,o,r){var i=t.lazySink(n).getOrDie();bp.openWhileCloaked(n,e,(function(){return Hg.positionWithinBounds(i,n,o,r())})),th.setValue(n,N.some({mode:"position",config:o,getBounds:r}))},a=function(t,n,e){c(t,n,e,N.none)},c=function(n,e,o,r){var i=Zw(t,n,e,o,r);bp.open(n,i),th.setValue(n,N.some({mode:"menu",menu:i}))},s=function(t){bp.isOpen(t)&&(th.setValue(t,N.none()),bp.close(t))},f=function(t){return bp.getState(t)},l=function(n){bp.isOpen(n)&&th.getValue(n).each((function(e){switch(e.mode){case"menu":bp.getState(n).each(Qw.repositionMenus);break;case"position":var o=t.lazySink(n).getOrDie();Hg.positionWithinBounds(o,n,e.config,e.getBounds());break}}))},d={setContent:o,showAt:r,showWithin:i,showWithinBounds:u,showMenuAt:a,showMenuWithinBounds:c,hide:s,getContent:f,reposition:l,isOpen:bp.isOpen};return{uid:t.uid,dom:t.dom,behaviours:oh(t.inlineBehaviours,[bp.config({isPartOf:function(t,n,o){return js(n,o)||e(t,o)},getAttachPoint:function(n){return t.lazySink(n).getOrDie()},onOpen:function(n){t.onShow(n)},onClose:function(n){t.onHide(n)}}),th.config({store:{mode:"memory",initialValue:N.none()}}),yl.config({channels:D(D({},kp(D({isExtraPart:n.isExtraPart},t.fireDismissalEventInstead.map((function(t){return{fireEventInstead:{event:t.event}}})).getOr({})))),Op(D(D({},t.fireRepositionEventInstead.map((function(t){return{fireEventInstead:{event:t.event}}})).getOr({})),{doReposition:l})))})]),eventOrder:t.eventOrder,apis:d}},nS=dv({name:"InlineView",configFields:[Di("lazySink"),cf("onShow"),cf("onHide"),Wi("onEscape"),nh("inlineBehaviours",[bp,th,yl]),Xi("fireDismissalEventInstead",[qi("event",ga())]),Xi("fireRepositionEventInstead",[qi("event",pa())]),qi("getRelated",N.none),qi("isExtraPart",_),qi("eventOrder",N.none)],factory:tS,apis:{showAt:function(t,n,e,o){t.showAt(n,e,o)},showWithin:function(t,n,e,o,r){t.showWithin(n,e,o,r)},showWithinBounds:function(t,n,e,o,r){t.showWithinBounds(n,e,o,r)},showMenuAt:function(t,n,e,o){t.showMenuAt(n,e,o)},showMenuWithinBounds:function(t,n,e,o,r){t.showMenuWithinBounds(n,e,o,r)},hide:function(t,n){t.hide(n)},isOpen:function(t,n){return t.isOpen(n)},getContent:function(t,n){return t.getContent(n)},setContent:function(t,n,e){t.setContent(n,e)},reposition:function(t,n){t.reposition(n)}}}),eS="layout-inset",oS=function(t){return t.x},rS=function(t,n){return t.x+t.width/2-n.width/2},iS=function(t,n){return t.x+t.width-n.width},uS=function(t){return t.y},aS=function(t,n){return t.y+t.height-n.height},cS=function(t,n){return t.y+t.height/2-n.height/2},sS=function(t,n,e){return pf(iS(t,n),aS(t,n),e.insetSouthwest(),kf(),"southwest",Bf(t,{right:0,bottom:3}),eS)},fS=function(t,n,e){return pf(oS(t),aS(t,n),e.insetSoutheast(),Sf(),"southeast",Bf(t,{left:1,bottom:3}),eS)},lS=function(t,n,e){return pf(iS(t,n),uS(t),e.insetNorthwest(),wf(),"northwest",Bf(t,{right:0,top:2}),eS)},dS=function(t,n,e){return pf(oS(t),uS(t),e.insetNortheast(),xf(),"northeast",Bf(t,{left:1,top:2}),eS)},mS=function(t,n,e){return pf(rS(t,n),uS(t),e.insetNorth(),Cf(),"north",Bf(t,{top:2}),eS)},gS=function(t,n,e){return pf(rS(t,n),aS(t,n),e.insetSouth(),Of(),"south",Bf(t,{bottom:3}),eS)},pS=function(t,n,e){return pf(iS(t,n),cS(t,n),e.insetEast(),Tf(),"east",Bf(t,{right:0}),eS)},hS=function(t,n,e){return pf(oS(t),cS(t,n),e.insetWest(),_f(),"west",Bf(t,{left:1}),eS)},vS=function(t){switch(t){case"north":return mS;case"northeast":return dS;case"northwest":return lS;case"south":return gS;case"southeast":return fS;case"southwest":return sS;case"east":return pS;case"west":return hS}},bS=function(t,n,e,o,r){var i=Xl(o).map(vS).getOr(mS);return i(t,n,e,o,r)},yS=function(t){switch(t){case"north":return gS;case"northeast":return fS;case"northwest":return sS;case"south":return mS;case"southeast":return dS;case"southwest":return lS;case"east":return hS;case"west":return pS}},xS=function(t,n,e,o,r){var i=Xl(o).map(yS).getOr(mS);return i(t,n,e,o,r)},wS=tinymce.util.Tools.resolve("tinymce.util.Delay"),SS=function(t){var n=vw(t.action),e=t.dom.tag,o=function(n){return _t(t.dom,"attributes").bind((function(t){return _t(t,n)}))},r=function(){if("button"===e){var t=o("type").getOr("button"),n=o("role").map((function(t){return{role:t}})).getOr({});return D({type:t},n)}var r=o("role").getOr("button");return{role:r}};return{uid:t.uid,dom:t.dom,components:t.components,events:n,behaviours:rh.augment(t.buttonBehaviours,[Jx.config({}),Dx.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:r()},eventOrder:t.eventOrder}},kS=dv({name:"Button",factory:SS,configFields:[qi("uid",void 0),Di("dom"),qi("components",[]),rh.field("buttonBehaviours",[Jx,Dx]),Li("action"),Li("role"),qi("eventOrder",{})]}),CS=function(t){var n=void 0!==t.dom.attributes?t.dom.attributes:[];return Q(n,(function(t,n){var e;return"class"===n.name?t:D(D({},t),(e={},e[n.name]=n.value,e))}),{})},OS=function(t){return Array.prototype.slice.call(t.dom.classList,0)},_S=function(t){var n=nn.fromHtml(t),e=we(n),o=CS(n),r=OS(n),i=0===e.length?{}:{innerHtml:Ya(n)};return D({tag:cn(n),classes:r,attributes:o},i)},TS=function(t){var n=sv(t)&&Et(t,"uid")?t.uid:dc("memento"),e=function(t){return t.getSystem().getByUid(n).getOrDie()},o=function(t){return t.getSystem().getByUid(n).toOptional()},r=function(){return D(D({},t),{uid:n})};return{get:e,getOpt:o,asSpec:r}};function ES(t){return ES="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ES(t)}function DS(t,n){return DS=Object.setPrototypeOf||function(t,n){return t.__proto__=n,t},DS(t,n)}function AS(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function BS(t,n,e){return BS=AS()?Reflect.construct:function(t,n,e){var o=[null];o.push.apply(o,n);var r=Function.bind.apply(t,o),i=new r;return e&&DS(i,e.prototype),i},BS.apply(null,arguments)}function MS(t){return FS(t)||IS(t)||RS(t)||PS()}function FS(t){if(Array.isArray(t))return NS(t)}function IS(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function RS(t,n){if(t){if("string"===typeof t)return NS(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?NS(t,n):void 0}}function NS(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,o=new Array(n);e<n;e++)o[e]=t[e];return o}function PS(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var HS=Object.hasOwnProperty,VS=Object.setPrototypeOf,LS=Object.isFrozen,zS=Object.getPrototypeOf,US=Object.getOwnPropertyDescriptor,jS=Object.freeze,WS=Object.seal,GS=Object.create,XS="undefined"!==typeof Reflect&&Reflect,YS=XS.apply,qS=XS.construct;YS||(YS=function(t,n,e){return t.apply(n,e)}),jS||(jS=function(t){return t}),WS||(WS=function(t){return t}),qS||(qS=function(t,n){return BS(t,MS(n))});var KS=ik(Array.prototype.forEach),JS=ik(Array.prototype.pop),$S=ik(Array.prototype.push),QS=ik(String.prototype.toLowerCase),ZS=ik(String.prototype.match),tk=ik(String.prototype.replace),nk=ik(String.prototype.indexOf),ek=ik(String.prototype.trim),ok=ik(RegExp.prototype.test),rk=uk(TypeError);function ik(t){return function(n){for(var e=arguments.length,o=new Array(e>1?e-1:0),r=1;r<e;r++)o[r-1]=arguments[r];return YS(t,n,o)}}function uk(t){return function(){for(var n=arguments.length,e=new Array(n),o=0;o<n;o++)e[o]=arguments[o];return qS(t,e)}}function ak(t,n){VS&&VS(t,null);var e=n.length;while(e--){var o=n[e];if("string"===typeof o){var r=QS(o);r!==o&&(LS(n)||(n[e]=r),o=r)}t[o]=!0}return t}function ck(t){var n,e=GS(null);for(n in t)YS(HS,t,[n])&&(e[n]=t[n]);return e}function sk(t,n){while(null!==t){var e=US(t,n);if(e){if(e.get)return ik(e.get);if("function"===typeof e.value)return ik(e.value)}t=zS(t)}function o(t){return null}return o}var fk=jS(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),lk=jS(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),dk=jS(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),mk=jS(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),gk=jS(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),pk=jS(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),hk=jS(["#text"]),vk=jS(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),bk=jS(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),yk=jS(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),xk=jS(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),wk=WS(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Sk=WS(/<%[\w\W]*|[\w\W]*%>/gm),kk=WS(/^data-[\-\w.\u00B7-\uFFFF]/),Ck=WS(/^aria-[\-\w]+$/),Ok=WS(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),_k=WS(/^(?:\w+script|data):/i),Tk=WS(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Ek=WS(/^html$/i),Dk=function(){return"undefined"===typeof window?null:window},Ak=function(t,n){if("object"!==ES(t)||"function"!==typeof t.createPolicy)return null;var e=null,o="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(o)&&(e=n.currentScript.getAttribute(o));var r="dompurify"+(e?"#"+e:"");try{return t.createPolicy(r,{createHTML:function(t){return t}})}catch(i){return null}};function Bk(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Dk(),n=function(t){return Bk(t)};if(n.version="2.3.8",n.removed=[],!t||!t.document||9!==t.document.nodeType)return n.isSupported=!1,n;var e=t.document,o=t.document,r=t.DocumentFragment,i=t.HTMLTemplateElement,u=t.Node,a=t.Element,c=t.NodeFilter,s=t.NamedNodeMap,f=void 0===s?t.NamedNodeMap||t.MozNamedAttrMap:s,l=t.HTMLFormElement,d=t.DOMParser,m=t.trustedTypes,g=a.prototype,p=sk(g,"cloneNode"),h=sk(g,"nextSibling"),v=sk(g,"childNodes"),b=sk(g,"parentNode");if("function"===typeof i){var y=o.createElement("template");y.content&&y.content.ownerDocument&&(o=y.content.ownerDocument)}var x=Ak(m,e),w=x?x.createHTML(""):"",S=o,k=S.implementation,C=S.createNodeIterator,O=S.createDocumentFragment,_=S.getElementsByTagName,T=e.importNode,E={};try{E=ck(o).documentMode?o.documentMode:{}}catch(zt){}var D={};n.isSupported="function"===typeof b&&k&&"undefined"!==typeof k.createHTMLDocument&&9!==E;var A,B,M=wk,F=Sk,I=kk,R=Ck,N=_k,P=Tk,H=Ok,V=null,L=ak({},[].concat(MS(fk),MS(lk),MS(dk),MS(gk),MS(hk))),z=null,U=ak({},[].concat(MS(vk),MS(bk),MS(yk),MS(xk))),j=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),W=null,G=null,X=!0,Y=!0,q=!1,K=!1,J=!1,$=!1,Q=!1,Z=!1,tt=!1,nt=!1,et=!0,ot=!0,rt=!1,it={},ut=null,at=ak({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),ct=null,st=ak({},["audio","video","img","source","image","track"]),ft=null,lt=ak({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),dt="http://www.w3.org/1998/Math/MathML",mt="http://www.w3.org/2000/svg",gt="http://www.w3.org/1999/xhtml",pt=gt,ht=!1,vt=["application/xhtml+xml","text/html"],bt="text/html",yt=null,xt=o.createElement("form"),wt=function(t){return t instanceof RegExp||t instanceof Function},St=function(t){yt&&yt===t||(t&&"object"===ES(t)||(t={}),t=ck(t),V="ALLOWED_TAGS"in t?ak({},t.ALLOWED_TAGS):L,z="ALLOWED_ATTR"in t?ak({},t.ALLOWED_ATTR):U,ft="ADD_URI_SAFE_ATTR"in t?ak(ck(lt),t.ADD_URI_SAFE_ATTR):lt,ct="ADD_DATA_URI_TAGS"in t?ak(ck(st),t.ADD_DATA_URI_TAGS):st,ut="FORBID_CONTENTS"in t?ak({},t.FORBID_CONTENTS):at,W="FORBID_TAGS"in t?ak({},t.FORBID_TAGS):{},G="FORBID_ATTR"in t?ak({},t.FORBID_ATTR):{},it="USE_PROFILES"in t&&t.USE_PROFILES,X=!1!==t.ALLOW_ARIA_ATTR,Y=!1!==t.ALLOW_DATA_ATTR,q=t.ALLOW_UNKNOWN_PROTOCOLS||!1,K=t.SAFE_FOR_TEMPLATES||!1,J=t.WHOLE_DOCUMENT||!1,Z=t.RETURN_DOM||!1,tt=t.RETURN_DOM_FRAGMENT||!1,nt=t.RETURN_TRUSTED_TYPE||!1,Q=t.FORCE_BODY||!1,et=!1!==t.SANITIZE_DOM,ot=!1!==t.KEEP_CONTENT,rt=t.IN_PLACE||!1,H=t.ALLOWED_URI_REGEXP||H,pt=t.NAMESPACE||gt,t.CUSTOM_ELEMENT_HANDLING&&wt(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(j.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&wt(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(j.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"===typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(j.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),A=A=-1===vt.indexOf(t.PARSER_MEDIA_TYPE)?bt:t.PARSER_MEDIA_TYPE,B="application/xhtml+xml"===A?function(t){return t}:QS,K&&(Y=!1),tt&&(Z=!0),it&&(V=ak({},MS(hk)),z=[],!0===it.html&&(ak(V,fk),ak(z,vk)),!0===it.svg&&(ak(V,lk),ak(z,bk),ak(z,xk)),!0===it.svgFilters&&(ak(V,dk),ak(z,bk),ak(z,xk)),!0===it.mathMl&&(ak(V,gk),ak(z,yk),ak(z,xk))),t.ADD_TAGS&&(V===L&&(V=ck(V)),ak(V,t.ADD_TAGS)),t.ADD_ATTR&&(z===U&&(z=ck(z)),ak(z,t.ADD_ATTR)),t.ADD_URI_SAFE_ATTR&&ak(ft,t.ADD_URI_SAFE_ATTR),t.FORBID_CONTENTS&&(ut===at&&(ut=ck(ut)),ak(ut,t.FORBID_CONTENTS)),ot&&(V["#text"]=!0),J&&ak(V,["html","head","body"]),V.table&&(ak(V,["tbody"]),delete W.tbody),jS&&jS(t),yt=t)},kt=ak({},["mi","mo","mn","ms","mtext"]),Ct=ak({},["foreignobject","desc","title","annotation-xml"]),Ot=ak({},["title","style","font","a","script"]),_t=ak({},lk);ak(_t,dk),ak(_t,mk);var Tt=ak({},gk);ak(Tt,pk);var Et=function(t){var n=b(t);n&&n.tagName||(n={namespaceURI:gt,tagName:"template"});var e=QS(t.tagName),o=QS(n.tagName);return t.namespaceURI===mt?n.namespaceURI===gt?"svg"===e:n.namespaceURI===dt?"svg"===e&&("annotation-xml"===o||kt[o]):Boolean(_t[e]):t.namespaceURI===dt?n.namespaceURI===gt?"math"===e:n.namespaceURI===mt?"math"===e&&Ct[o]:Boolean(Tt[e]):t.namespaceURI===gt&&(!(n.namespaceURI===mt&&!Ct[o])&&(!(n.namespaceURI===dt&&!kt[o])&&(!Tt[e]&&(Ot[e]||!_t[e]))))},Dt=function(t){$S(n.removed,{element:t});try{t.parentNode.removeChild(t)}catch(zt){try{t.outerHTML=w}catch(zt){t.remove()}}},At=function(t,e){try{$S(n.removed,{attribute:e.getAttributeNode(t),from:e})}catch(zt){$S(n.removed,{attribute:null,from:e})}if(e.removeAttribute(t),"is"===t&&!z[t])if(Z||tt)try{Dt(e)}catch(zt){}else try{e.setAttribute(t,"")}catch(zt){}},Bt=function(t){var n,e;if(Q)t="<remove></remove>"+t;else{var r=ZS(t,/^[\r\n\t ]+/);e=r&&r[0]}"application/xhtml+xml"===A&&(t='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+t+"</body></html>");var i=x?x.createHTML(t):t;if(pt===gt)try{n=(new d).parseFromString(i,A)}catch(zt){}if(!n||!n.documentElement){n=k.createDocument(pt,"template",null);try{n.documentElement.innerHTML=ht?"":i}catch(zt){}}var u=n.body||n.documentElement;return t&&e&&u.insertBefore(o.createTextNode(e),u.childNodes[0]||null),pt===gt?_.call(n,J?"html":"body")[0]:J?n.documentElement:u},Mt=function(t){return C.call(t.ownerDocument||t,t,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT,null,!1)},Ft=function(t){return t instanceof l&&("string"!==typeof t.nodeName||"string"!==typeof t.textContent||"function"!==typeof t.removeChild||!(t.attributes instanceof f)||"function"!==typeof t.removeAttribute||"function"!==typeof t.setAttribute||"string"!==typeof t.namespaceURI||"function"!==typeof t.insertBefore)},It=function(t){return"object"===ES(u)?t instanceof u:t&&"object"===ES(t)&&"number"===typeof t.nodeType&&"string"===typeof t.nodeName},Rt=function(t,e,o){D[t]&&KS(D[t],(function(t){t.call(n,e,o,yt)}))},Nt=function(t){var e;if(Rt("beforeSanitizeElements",t,null),Ft(t))return Dt(t),!0;if(ok(/[\u0080-\uFFFF]/,t.nodeName))return Dt(t),!0;var o=B(t.nodeName);if(Rt("uponSanitizeElement",t,{tagName:o,allowedTags:V}),t.hasChildNodes()&&!It(t.firstElementChild)&&(!It(t.content)||!It(t.content.firstElementChild))&&ok(/<[/\w]/g,t.innerHTML)&&ok(/<[/\w]/g,t.textContent))return Dt(t),!0;if("select"===o&&ok(/<template/i,t.innerHTML))return Dt(t),!0;if(!V[o]||W[o]){if(!W[o]&&Ht(o)){if(j.tagNameCheck instanceof RegExp&&ok(j.tagNameCheck,o))return!1;if(j.tagNameCheck instanceof Function&&j.tagNameCheck(o))return!1}if(ot&&!ut[o]){var r=b(t)||t.parentNode,i=v(t)||t.childNodes;if(i&&r)for(var u=i.length,c=u-1;c>=0;--c)r.insertBefore(p(i[c],!0),h(t))}return Dt(t),!0}return t instanceof a&&!Et(t)?(Dt(t),!0):"noscript"!==o&&"noembed"!==o||!ok(/<\/no(script|embed)/i,t.innerHTML)?(K&&3===t.nodeType&&(e=t.textContent,e=tk(e,M," "),e=tk(e,F," "),t.textContent!==e&&($S(n.removed,{element:t.cloneNode()}),t.textContent=e)),Rt("afterSanitizeElements",t,null),!1):(Dt(t),!0)},Pt=function(t,n,e){if(et&&("id"===n||"name"===n)&&(e in o||e in xt))return!1;if(Y&&!G[n]&&ok(I,n));else if(X&&ok(R,n));else if(!z[n]||G[n]){if(!(Ht(t)&&(j.tagNameCheck instanceof RegExp&&ok(j.tagNameCheck,t)||j.tagNameCheck instanceof Function&&j.tagNameCheck(t))&&(j.attributeNameCheck instanceof RegExp&&ok(j.attributeNameCheck,n)||j.attributeNameCheck instanceof Function&&j.attributeNameCheck(n))||"is"===n&&j.allowCustomizedBuiltInElements&&(j.tagNameCheck instanceof RegExp&&ok(j.tagNameCheck,e)||j.tagNameCheck instanceof Function&&j.tagNameCheck(e))))return!1}else if(ft[n]);else if(ok(H,tk(e,P,"")));else if("src"!==n&&"xlink:href"!==n&&"href"!==n||"script"===t||0!==nk(e,"data:")||!ct[t]){if(q&&!ok(N,tk(e,P,"")));else if(e)return!1}else;return!0},Ht=function(t){return t.indexOf("-")>0},Vt=function(t){var e,o,r,i;Rt("beforeSanitizeAttributes",t,null);var u=t.attributes;if(u){var a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:z};i=u.length;while(i--){e=u[i];var c=e,s=c.name,f=c.namespaceURI;if(o="value"===s?e.value:ek(e.value),r=B(s),a.attrName=r,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Rt("uponSanitizeAttribute",t,a),o=a.attrValue,!a.forceKeepAttr&&(At(s,t),a.keepAttr))if(ok(/\/>/i,o))At(s,t);else{K&&(o=tk(o,M," "),o=tk(o,F," "));var l=B(t.nodeName);if(Pt(l,r,o))try{f?t.setAttributeNS(f,s,o):t.setAttribute(s,o),JS(n.removed)}catch(zt){}}}Rt("afterSanitizeAttributes",t,null)}},Lt=function t(n){var e,o=Mt(n);Rt("beforeSanitizeShadowDOM",n,null);while(e=o.nextNode())Rt("uponSanitizeShadowNode",e,null),Nt(e)||(e.content instanceof r&&t(e.content),Vt(e));Rt("afterSanitizeShadowDOM",n,null)};return n.sanitize=function(o,i){var a,c,s,f,l;if(ht=!o,ht&&(o="\x3c!--\x3e"),"string"!==typeof o&&!It(o)){if("function"!==typeof o.toString)throw rk("toString is not a function");if(o=o.toString(),"string"!==typeof o)throw rk("dirty is not a string, aborting")}if(!n.isSupported){if("object"===ES(t.toStaticHTML)||"function"===typeof t.toStaticHTML){if("string"===typeof o)return t.toStaticHTML(o);if(It(o))return t.toStaticHTML(o.outerHTML)}return o}if($||St(i),n.removed=[],"string"===typeof o&&(rt=!1),rt){if(o.nodeName){var d=B(o.nodeName);if(!V[d]||W[d])throw rk("root node is forbidden and cannot be sanitized in-place")}}else if(o instanceof u)a=Bt("\x3c!----\x3e"),c=a.ownerDocument.importNode(o,!0),1===c.nodeType&&"BODY"===c.nodeName||"HTML"===c.nodeName?a=c:a.appendChild(c);else{if(!Z&&!K&&!J&&-1===o.indexOf("<"))return x&&nt?x.createHTML(o):o;if(a=Bt(o),!a)return Z?null:nt?w:""}a&&Q&&Dt(a.firstChild);var m=Mt(rt?o:a);while(s=m.nextNode())3===s.nodeType&&s===f||Nt(s)||(s.content instanceof r&&Lt(s.content),Vt(s),f=s);if(f=null,rt)return o;if(Z){if(tt){l=O.call(a.ownerDocument);while(a.firstChild)l.appendChild(a.firstChild)}else l=a;return z.shadowroot&&(l=T.call(e,l,!0)),l}var g=J?a.outerHTML:a.innerHTML;return J&&V["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&ok(Ek,a.ownerDocument.doctype.name)&&(g="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+g),K&&(g=tk(g,M," "),g=tk(g,F," ")),x&&nt?x.createHTML(g):g},n.setConfig=function(t){St(t),$=!0},n.clearConfig=function(){yt=null,$=!1},n.isValidAttribute=function(t,n,e){yt||St({});var o=B(t),r=B(n);return Pt(o,r,e)},n.addHook=function(t,n){"function"===typeof n&&(D[t]=D[t]||[],$S(D[t],n))},n.removeHook=function(t){if(D[t])return JS(D[t])},n.removeHooks=function(t){D[t]&&(D[t]=[])},n.removeAllHooks=function(){D={}},n}var Mk=Bk(),Fk=function(t){return Mk().sanitize(t)},Ik=tinymce.util.Tools.resolve("tinymce.util.I18n"),Rk={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Nk="temporary-placeholder",Pk=function(t){return function(){return _t(t,Nk).getOr("!not found!")}},Hk=function(t,n){var e=t.toLowerCase();if(Ik.isRtl()){var o=zt(e,"-rtl");return Tt(n,o)?o:e}return e},Vk=function(t,n){return _t(n,Hk(t,n))},Lk=function(t,n){var e=n();return Vk(t,e).getOrThunk(Pk(e))},zk=function(t,n,e){var o=n();return Vk(t,o).or(e).getOrThunk(Pk(o))},Uk=function(t){return!!Ik.isRtl()&&Tt(Rk,t)},jk=function(){return zx("add-focusable",[Ua((function(t){Ps(t.element,"svg").each((function(t){return Le(t,"focusable","false")}))}))])},Wk=function(t,n,e,o){var r,i,u=Uk(n)?["tox-icon--flip"]:[],a=_t(e,Hk(n,e)).or(o).getOrThunk(Pk(e));return{dom:{tag:t.tag,attributes:null!==(r=t.attributes)&&void 0!==r?r:{},classes:t.classes.concat(u),innerHtml:a},behaviours:ml(B(B([],null!==(i=t.behaviours)&&void 0!==i?i:[],!0),[jk()],!1))}},Gk=function(t,n,e,o){return void 0===o&&(o=N.none()),Wk(n,t,e(),o)},Xk=function(t,n,e){var o=e(),r=tt(t,(function(t){return Tt(o,Hk(t,o))}));return Wk(n,r.getOr(Nk),o,N.none())},Yk={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},qk=function(t){var n=TS({dom:_S("<p>"+Fk(t.translationProvider(t.text))+"</p>"),behaviours:ml([Vx.config({})])}),e=function(t){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+t+"%"}}}},o=function(t){return{dom:{tag:"div",classes:["tox-text"],innerHtml:t+"%"}}},r=TS({dom:{tag:"div",classes:t.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(0)]},o(0)],behaviours:ml([Vx.config({})])}),i=function(t,n){t.getSystem().isConnected()&&r.getOpt(t).each((function(t){Vx.set(t,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(n)]},o(n)])}))},u=function(t,e){if(t.getSystem().isConnected()){var o=n.get(t);Vx.set(o,[ks(e)])}},a={updateProgress:i,updateText:u},c=et([t.icon.toArray(),t.level.toArray(),t.level.bind((function(t){return N.from(Yk[t])})).toArray()]),s=TS(kS.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[Gk("close",{tag:"div",classes:["tox-icon"],attributes:{"aria-label":t.translationProvider("Close")}},t.iconProvider)],action:function(n){t.onAction(n)}})),f=Xk(c,{tag:"div",classes:["tox-notification__icon"]},t.iconProvider),l={dom:{tag:"div",classes:["tox-notification__body"]},components:[n.asSpec()],behaviours:ml([Vx.config({})])},d=[f,l];return{uid:t.uid,dom:{tag:"div",attributes:{role:"alert"},classes:t.level.map((function(t){return["tox-notification","tox-notification--in","tox-notification--"+t]})).getOr(["tox-notification","tox-notification--in"])},behaviours:ml([Jx.config({}),zx("notification-events",[Ma(Hu(),(function(t){s.getOpt(t).each(Jx.focus)}))])]),components:d.concat(t.progress?[r.asSpec()]:[]).concat(t.closeButton?[s.asSpec()]:[]),apis:a}},Kk=dv({name:"Notification",factory:qk,configFields:[Li("level"),Di("progress"),Di("icon"),Di("onAction"),Di("text"),Di("iconProvider"),Di("translationProvider"),Zi("closeButton",!0)],apis:{updateProgress:function(t,n,e){t.updateProgress(n,e)},updateText:function(t,n,e){t.updateText(n,e)}}});function Jk(t,n,e){var o=n.backstage.shared,r=function(t){switch(t){case"bc-bc":return gS;case"tc-tc":return mS;case"tc-bc":return Xf;case"bc-tc":default:return Yf}},i=function(t){t.length>0&&Y(t,(function(n,e){0===e?n.moveRel(null,"banner"):n.moveRel(t[e-1].getEl(),"bc-tc")}))},u=function(n,i){var u=!n.closeButton&&n.timeout&&(n.timeout>0||n.timeout<0),a=function(){i(),nS.hide(s)},c=Ts(Kk.sketch({text:n.text,level:U(["success","error","warning","warn","info"],n.type)?n.type:void 0,progress:!0===n.progressBar,icon:N.from(n.icon),closeButton:!u,onAction:a,iconProvider:o.providers.icons,translationProvider:o.providers.translate})),s=Ts(nS.sketch(D({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:o.getSink,fireDismissalEventInstead:{}},o.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}})));e.add(s),n.timeout>0&&wS.setTimeout((function(){a()}),n.timeout);var f=function(){var n=Qo(nn.fromDom(t.getContentAreaContainer())),e=tr(),o=Df(e.x,n.x,n.right),r=Df(e.y,n.y,n.bottom),i=Math.max(n.right,e.right),u=Math.max(n.bottom,e.bottom);return N.some($o(o,r,i-o,u-r))};return{close:a,moveTo:function(t,n){nS.showAt(s,Es(c),{anchor:{type:"makeshift",x:t,y:n}})},moveRel:function(t,n){var e=Es(c),i={maxHeightFunction:Td()};if("banner"!==n&&d(t)){var u=r(n),a={type:"node",root:Pe(),node:N.some(nn.fromDom(t)),overrides:i,layouts:{onRtl:function(){return[u]},onLtr:function(){return[u]}}};nS.showWithinBounds(s,e,{anchor:a},f)}else{var l=D(D({},o.anchors.banner()),{overrides:i});nS.showWithinBounds(s,e,{anchor:l},f)}},text:function(t){Kk.updateText(c,t)},settings:n,getEl:function(){return c.element.dom},progressBar:{value:function(t){Kk.updateProgress(c,t)}}}},a=function(t){t.close()},c=function(t){return t.settings};return{open:u,close:a,reposition:i,getArgs:c}}var $k,Qk=function(t,n){var e=null,o=function(){c(e)||(clearTimeout(e),e=null)},r=function(){for(var o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];c(e)&&(e=setTimeout((function(){e=null,t.apply(null,o)}),n))};return{cancel:o,throttle:r}},Zk=function(t,n){var e=null,o=function(){c(e)||(clearTimeout(e),e=null)},r=function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];o(),e=setTimeout((function(){e=null,t.apply(null,r)}),n)};return{cancel:o,throttle:r}},tC=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),nC=function(t,n){return t.isBlock(n)||U(["BR","IMG","HR","INPUT"],n.nodeName)||"false"===t.getContentEditable(n)},eC=function(t,n,e,o,r){var i=tC(t,(function(n){return nC(t,n)}));return N.from(i.backwards(n,e,o,r))},oC="[data-mce-autocompleter]",rC=function(t,n){return iC(nn.fromDom(t.selection.getNode())).getOrThunk((function(){var e=nn.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',t.getDoc());return Bo(e,nn.fromDom(n.extractContents())),n.insertNode(e.dom),ve(e).each((function(t){return t.dom.normalize()})),Ym(e).map((function(n){t.selection.setCursorLocation(n.dom,jm(n))})),e}))},iC=function(t){return Vs(t,oC)},uC=function(t){return t.collapsed&&3===t.startContainer.nodeType},aC=function(t){return t.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")},cC=function(t){return""!==t&&-1!=="  \f\n\r\t\v".indexOf(t)},sC=function(t,n){return t.substring(n.length)},fC=function(t,n,e){var o;for(o=n-1;o>=0;o--){var r=t.charAt(o);if(cC(r))return N.none();if(r===e)break}return N.some(o)},lC=function(t,n,e,o){if(void 0===o&&(o=0),!uC(n))return N.none();var r=function(t,n,o){return fC(o,n,e).getOr(n)},i=t.getParent(n.startContainer,t.isBlock)||t.getRoot();return eC(t,n.startContainer,n.startOffset,r,i).bind((function(t){var r=n.cloneRange();if(r.setStart(t.container,t.offset),r.setEnd(n.endContainer,n.endOffset),r.collapsed)return N.none();var i=aC(r),u=i.lastIndexOf(e);return 0!==u||sC(i,e).length<o?N.none():N.some({text:sC(i,e),range:r,triggerChar:e})}))},dC=function(t,n,e,o){return void 0===o&&(o=0),iC(nn.fromDom(n.startContainer)).fold((function(){return lC(t,n,e,o)}),(function(n){var o=t.createRng();o.selectNode(n.dom);var r=aC(o);return N.some({range:o,text:sC(r,e),triggerChar:e})}))},mC=function(t,n){n.on("keypress compositionend",t.onKeypress.throttle),n.on("remove",t.onKeypress.cancel);var e=function(t,n){ka(t,Lu(),{raw:n})};n.on("keydown",(function(n){var o=function(){return t.getView().bind(ob.getHighlighted)};8===n.which&&t.onKeypress.throttle(n),t.isActive()&&(27===n.which&&t.cancelIfNecessary(),t.isMenuOpen()?13===n.which?(o().each(Ca),n.preventDefault()):40===n.which?(o().fold((function(){t.getView().each(ob.highlightFirst)}),(function(t){e(t,n)})),n.preventDefault(),n.stopImmediatePropagation()):37!==n.which&&38!==n.which&&39!==n.which||o().each((function(t){e(t,n),n.preventDefault(),n.stopImmediatePropagation()})):13!==n.which&&38!==n.which&&40!==n.which||t.cancelIfNecessary())})),n.on("NodeChange",(function(n){t.isActive()&&!t.isProcessingAction()&&iC(nn.fromDom(n.element)).isNone()&&t.cancelIfNecessary()}))},gC={setup:mC},pC=tinymce.util.Tools.resolve("tinymce.util.Promise"),hC=function(t,n){return{container:t,offset:n}},vC=function(t){return t.nodeType===an},bC=function(t){return t.nodeType===un},yC=function(t){if(vC(t))return hC(t,t.data.length);var n=t.childNodes;return n.length>0?yC(n[n.length-1]):hC(t,n.length)},xC=function(t,n){var e=t.childNodes;return e.length>0&&n<e.length?xC(e[n],0):e.length>0&&bC(t)&&e.length===n?yC(e[e.length-1]):hC(t,n)},wC=function(t,n){return eC(t,n.container,n.offset,(function(t,n){return 0===n?-1:n}),t.getRoot()).filter((function(t){var n=t.container.data.charAt(t.offset-1);return!cC(n)})).isSome()},SC=function(t){return function(n){var e=xC(n.startContainer,n.startOffset);return!wC(t,e)}},kC=function(t,n,e){return gt(e.triggerChars,(function(e){return dC(t,n,e)}))},CC=function(t,n){var e=n(),o=t.selection.getRng();return kC(t.dom,o,e).bind((function(e){return OC(t,n,e)}))},OC=function(t,n,e,o){void 0===o&&(o={});var r=n(),i=t.selection.getRng(),u=i.startContainer.nodeValue,a=J(r.lookupByChar(e.triggerChar),(function(n){return e.text.length>=n.minChars&&n.matches.getOrThunk((function(){return SC(t.dom)}))(e.range,u,e.text)}));if(0===a.length)return N.none();var c=pC.all(X(a,(function(t){var n=t.fetch(e.text,t.maxResults,o);return n.then((function(n){return{matchText:e.text,items:n,columns:t.columns,onAction:t.onAction,highlightOn:t.highlightOn}}))})));return N.some({lookupData:c,context:e})},_C=ti([Mi("type"),ji("text")]),TC=function(t){return xi("separatormenuitem",_C,t)},EC=ti([qi("type","autocompleteitem"),qi("active",!1),qi("disabled",!1),qi("meta",{}),Mi("value"),ji("text"),ji("icon")]),DC=ti([Mi("type"),Mi("ch"),Ji("minChars",1),qi("columns",1),Ji("maxResults",10),Wi("matches"),Ri("fetch"),Ri("onAction"),eu("highlightOn",[],si)]),AC=function(t){return xi("Autocompleter.Separator",_C,t)},BC=function(t){return xi("Autocompleter.Item",EC,t)},MC=function(t){return xi("Autocompleter",DC,t)},FC=[Zi("disabled",!1),ji("tooltip"),ji("icon"),ji("text"),tu("onSetup",(function(){return h}))],IC=ti([Mi("type"),Ri("onAction")].concat(FC)),RC=function(t){return xi("toolbarbutton",IC,t)},NC=[Zi("active",!1)].concat(FC),PC=ti(NC.concat([Mi("type"),Ri("onAction")])),HC=function(t){return xi("ToggleButton",PC,t)},VC=[tu("predicate",_),Qi("scope","node",["node","editor"]),Qi("position","selection",["node","selection","line"])],LC=FC.concat([qi("type","contextformbutton"),qi("primary",!1),Ri("onAction"),Ti("original",w)]),zC=NC.concat([qi("type","contextformbutton"),qi("primary",!1),Ri("onAction"),Ti("original",w)]),UC=FC.concat([qi("type","contextformbutton")]),jC=NC.concat([qi("type","contextformtogglebutton")]),WC=Ci("type",{contextformbutton:LC,contextformtogglebutton:zC}),GC=ti([qi("type","contextform"),tu("initValue",x("")),ji("label"),Vi("commands",WC),zi("launch",Ci("type",{contextformbutton:UC,contextformtogglebutton:jC}))].concat(VC)),XC=function(t){return xi("ContextForm",GC,t)},YC=ti([qi("type","contexttoolbar"),Mi("items")].concat(VC)),qC=function(t){return xi("ContextToolbar",YC,t)},KC=function(t){var n={};return Y(t,(function(t){n[t]={}})),pt(n)},JC=function(t){var n=t.ui.registry.getAll().popups,e=bt(n,(function(t){return MC(t).fold((function(t){throw new Error(ki(t))}),w)})),o=KC(kt(e,(function(t){return t.ch}))),r=Ot(e),i=function(t){return J(r,(function(n){return n.ch===t}))};return{dataset:e,triggerChars:o,lookupByChar:i}};(function(t){t[t["CLOSE_ON_EXECUTE"]=0]="CLOSE_ON_EXECUTE",t[t["BUBBLE_TO_SANDBOX"]=1]="BUBBLE_TO_SANDBOX"})($k||($k={}));var $C,QC=$k,ZC="tox-menu-nav__js",tO="tox-collection__item",nO="tox-swatch",eO={normal:ZC,color:nO},oO="tox-collection__item--enabled",rO="tox-collection__group-heading",iO="tox-collection__item-icon",uO="tox-collection__item-label",aO="tox-collection__item-accessory",cO="tox-collection__item-caret",sO="tox-collection__item-checkmark",fO="tox-collection__item--active",lO="tox-collection__item-container",dO="tox-collection__item-container--column",mO="tox-collection__item-container--row",gO="tox-collection__item-container--align-right",pO="tox-collection__item-container--align-left",hO="tox-collection__item-container--valign-top",vO="tox-collection__item-container--valign-middle",bO="tox-collection__item-container--valign-bottom",yO=function(t){return _t(eO,t).getOr(ZC)},xO=function(t){return"color"===t?"tox-swatches":"tox-menu"},wO=function(t){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:xO(t),tieredMenu:"tox-tiered-menu"}},SO=function(t){var n=wO(t);return{backgroundMenu:n.backgroundMenu,selectedMenu:n.selectedMenu,menu:n.menu,selectedItem:n.selectedItem,item:yO(t)}},kO=function(t,n,e){var o=wO(e);return{tag:"div",classes:et([[o.menu,"tox-menu-"+n+"-column"],t?[o.hasIcons]:[]])}},CO=[Lw.parts.items({})],OO=function(t,n,e){var o=wO(e),r={tag:"div",classes:et([[o.tieredMenu]])};return{dom:r,markers:SO(e)}},_O=function(t,n){return function(e){var o=G(e,n);return X(o,(function(n){return{dom:t,components:n}}))}},TO=function(t){return{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Lw.parts.items({preprocess:"auto"!==t?_O({tag:"div",classes:["tox-swatches__row"]},t):w})]}]}},EO=function(t){return{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Lw.parts.items({preprocess:_O({tag:"div",classes:["tox-collection__group"]},t)})]}},DO=function(t,n){var e=[],o=[];return Y(t,(function(t,r){n(t,r)?(o.length>0&&e.push(o),o=[],Tt(t.dom,"innerHtml")&&o.push(t)):o.push(t)})),o.length>0&&e.push(o),X(e,(function(t){return{dom:{tag:"div",classes:["tox-collection__group"]},components:t}}))},AO=function(t,n,e){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===t?["tox-collection--list"]:["tox-collection--grid"])},components:[Lw.parts.items({preprocess:function(e){return"auto"!==t&&t>1?_O({tag:"div",classes:["tox-collection__group"]},t)(e):DO(e,(function(t,e){return"separator"===n[e].type}))}})]}},BO=function(t,n){return{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Lw.parts.items({preprocess:function(n){return DO(n,(function(n,e){return"separator"===t[e].type}))}})]}},MO=function(t){return j(t,(function(t){return"icon"in t&&void 0!==t.icon}))},FO=function(t){return N.none()},IO=function(t,n,e,o,r){var i=BO(e);return{value:t,dom:i.dom,components:i.components,items:e}},RO=function(t,n,e,o,r){if("color"===r){var i=TO(o);return{value:t,dom:i.dom,components:i.components,items:e}}if("normal"===r&&"auto"===o){i=AO(o,e);return{value:t,dom:i.dom,components:i.components,items:e}}if("normal"===r&&1===o){i=AO(1,e);return{value:t,dom:i.dom,components:i.components,items:e}}if("normal"===r){i=AO(o,e);return{value:t,dom:i.dom,components:i.components,items:e}}if("listpreview"===r&&"auto"!==o){i=EO(o);return{value:t,dom:i.dom,components:i.components,items:e}}return{value:t,dom:kO(n,o,r),components:CO,items:e}},NO=[Mi("type"),Mi("src"),ji("alt"),eu("classes",[],si)],PO=ti(NO),HO=[Mi("type"),Mi("text"),ji("name"),eu("classes",["tox-collection__item-label"],si)],VO=ti(HO),LO=$r((function(){return pi("type",{cardimage:PO,cardtext:VO,cardcontainer:zO})})),zO=ti([Mi("type"),$i("direction","horizontal"),$i("align","left"),$i("valign","middle"),Vi("items",LO)]),UO=[Zi("disabled",!1),ji("text"),ji("shortcut"),_i("value","value",Or((function(){return rc("menuitem-value")})),ui()),qi("meta",{})],jO=ti([Mi("type"),ji("label"),Vi("items",LO),tu("onSetup",(function(){return h})),tu("onAction",h)].concat(UO)),WO=function(t){return xi("cardmenuitem",jO,t)},GO=ti([Mi("type"),Zi("active",!1),ji("icon")].concat(UO)),XO=function(t){return xi("choicemenuitem",GO,t)},YO=[Mi("type"),Mi("fancytype"),tu("onAction",h)],qO=[qi("initData",{})].concat(YO),KO=[ou("initData",{},[Zi("allowCustomColors",!0),Gi("colors",ui())])].concat(YO),JO=Ci("fancytype",{inserttable:qO,colorswatch:KO}),$O=function(t){return xi("fancymenuitem",JO,t)},QO=ti([Mi("type"),tu("onSetup",(function(){return h})),tu("onAction",h),ji("icon")].concat(UO)),ZO=function(t){return xi("menuitem",QO,t)},t_=ti([Mi("type"),Ri("getSubmenuItems"),tu("onSetup",(function(){return h})),ji("icon")].concat(UO)),n_=function(t){return xi("nestedmenuitem",t_,t)},e_=ti([Mi("type"),ji("icon"),Zi("active",!1),tu("onSetup",(function(){return h})),Ri("onAction")].concat(UO)),o_=function(t){return xi("togglemenuitem",e_,t)},r_=function(t,n,e){var o=Km(t.element,"."+e);if(o.length>0){var r=nt(o,(function(t){var e=t.dom.getBoundingClientRect().top,r=o[0].dom.getBoundingClientRect().top;return Math.abs(e-r)>n})).getOr(o.length);return N.some({numColumns:r,numRows:Math.ceil(o.length/r)})}return N.none()},i_=function(t,n){return ml([zx(t,n)])},u_=function(t){return i_(rc("unnamed-events"),t)},a_={namedEvents:i_,unnamedEvents:u_},c_=rc("tooltip.exclusive"),s_=rc("tooltip.show"),f_=rc("tooltip.hide"),l_=function(t,n,e){t.getSystem().broadcastOn([c_],{})},d_=function(t,n,e,o){e.getTooltip().each((function(t){t.getSystem().isConnected()&&Vx.set(t,o)}))},m_=Object.freeze({__proto__:null,hideAllExclusive:l_,setComponents:d_}),g_=function(t,n){var e=function(e){n.getTooltip().each((function(o){Yg(o),t.onHide(e,o),n.clearTooltip()})),n.clearTimer()},o=function(e){if(!n.isShowing()){l_(e);var o=t.lazySink(e).getOrDie(),r=e.getSystem().build({dom:t.tooltipDom,components:t.tooltipComponents,events:Ea("normal"===t.mode?[Ma(Pu(),(function(t){Sa(e,s_)})),Ma(Ru(),(function(t){Sa(e,f_)}))]:[]),behaviours:ml([Vx.config({})])});n.setTooltip(r),Wg(o,r),t.onShow(e,r),Hg.position(o,r,{anchor:t.anchor(e)})}};return Ea(et([[Ma(s_,(function(e){n.resetTimer((function(){o(e)}),t.delay)})),Ma(f_,(function(o){n.resetTimer((function(){e(o)}),t.delay)})),Ma(ta(),(function(t,n){var o=n;o.universal||U(o.channels,c_)&&e(t)})),ja((function(t){e(t)}))],"normal"===t.mode?[Ma(Hu(),(function(t){Sa(t,s_)})),Ma(Qu(),(function(t){Sa(t,f_)})),Ma(Pu(),(function(t){Sa(t,s_)})),Ma(Ru(),(function(t){Sa(t,f_)}))]:[Ma(xa(),(function(t,n){Sa(t,s_)})),Ma(wa(),(function(t){Sa(t,f_)}))]]))},p_=Object.freeze({__proto__:null,events:g_}),h_=[Di("lazySink"),Di("tooltipDom"),qi("exclusive",!0),qi("tooltipComponents",[]),qi("delay",300),Qi("mode","normal",["normal","follow-highlight"]),qi("anchor",(function(t){return{type:"hotspot",hotspot:t,layouts:{onLtr:x([Yf,Xf,Uf,Wf,jf,Gf]),onRtl:x([Yf,Xf,Uf,Wf,jf,Gf])}}})),cf("onHide"),cf("onShow")],v_=function(){var t=rd(),n=rd(),e=function(){t.on(clearTimeout)},o=function(n,o){e(),t.set(setTimeout(n,o))},r=x("not-implemented");return Oc({getTooltip:n.get,isShowing:n.isSet,setTooltip:n.set,clearTooltip:n.clear,clearTimer:e,resetTimer:o,readState:r})},b_=Object.freeze({__proto__:null,init:v_}),y_=pl({fields:h_,name:"tooltipping",active:p_,state:b_,apis:m_}),x_=function(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")},w_=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),S_=tinymce.util.Tools.resolve("tinymce.EditorManager"),k_=function(t){var n=t.getParam("skin"),e=t.getParam("skin_url");if(!1!==n){var o=n||"oxide";e=e?t.documentBaseURI.toAbsolute(e):S_.baseURL+"/skins/ui/"+o}return e},C_=function(t){return t.getParam("readonly",!1,"boolean")},O_=function(t){return!1===t.getParam("skin")},__=function(t){return t.getParam("height",Math.max(t.getElement().offsetHeight,200))},T_=function(t){return t.getParam("width",w_.DOM.getStyle(t.getElement(),"width"))},E_=function(t){return N.from(t.getParam("min_width")).filter(g)},D_=function(t){return N.from(t.getParam("min_height")).filter(g)},A_=function(t){return N.from(t.getParam("max_width")).filter(g)},B_=function(t){return N.from(t.getParam("max_height")).filter(g)},M_=function(t){return N.from(t.getParam("style_formats")).filter(a)},F_=function(t){return t.getParam("style_formats_merge",!1,"boolean")},I_=function(t){return t.getParam("lineheight_formats","1 1.1 1.2 1.3 1.4 1.5 2","string").split(" ")},R_=function(t){return t.getParam("content_langs",void 0,"array")},N_=function(t){return t.getParam("removed_menuitems","")},P_=function(t){return!1!==t.getParam("menubar",!0,"boolean")},H_=function(t){var n=t.getParam("toolbar",!0),e=!0===n,o=i(n),r=a(n)&&n.length>0;return!L_(t)&&(r||o||e)},V_=function(t){var n=W(9,(function(n){return t.getParam("toolbar"+(n+1),!1,"string")})),e=J(n,(function(t){return"string"===typeof t}));return e.length>0?N.some(e):N.none()},L_=function(t){return V_(t).fold((function(){var n=t.getParam("toolbar",[],"string[]");return n.length>0}),T)};(function(t){t["default"]="wrap",t["floating"]="floating",t["sliding"]="sliding",t["scrolling"]="scrolling"})($C||($C={}));var z_,U_=function(t){return t.getParam("toolbar_mode","","string")};(function(t){t["auto"]="auto",t["top"]="top",t["bottom"]="bottom"})(z_||(z_={}));var j_,W_=function(t){return t.getParam("toolbar_groups",{},"object")},G_=function(t){return t.getParam("toolbar_location",z_.auto,"string")},X_=function(t){return G_(t)===z_.bottom},Y_=function(t){return t.getParam("fixed_toolbar_container","","string")},q_=function(t){return t.getParam("fixed_toolbar_container_target")},K_=function(t){return t.getParam("toolbar_persist",!1,"boolean")},J_=function(t){if(!t.inline)return N.none();var n=Y_(t);if(n.length>0)return Hs(Pe(),n);var e=q_(t);return d(e)?N.some(nn.fromDom(e)):N.none()},$_=function(t){return t.inline&&J_(t).isSome()},Q_=function(t){var n=J_(t);return n.getOrThunk((function(){return Ae(De(nn.fromDom(t.getElement())))}))},Z_=function(t){return t.inline&&!P_(t)&&!H_(t)&&!L_(t)},tT=function(t){var n=t.getParam("toolbar_sticky",!1,"boolean");return(n||t.inline)&&!$_(t)&&!Z_(t)},nT=function(t){return t.getParam("toolbar_sticky_offset",0,"number")},eT=function(t){return t.getParam("draggable_modal",!1,"boolean")},oT=function(t){var n=t.getParam("menu");return n?bt(n,(function(t){return D(D({},t),{items:t.items})})):{}},rT=function(t){return t.getParam("menubar")},iT=function(t){return t.getParam("toolbar",!0)},uT=function(t){return t.getParam("file_picker_callback")},aT=function(t){return t.getParam("file_picker_types")},cT=function(t){return t.getParam("file_browser_callback_types")},sT=function(t){return!1===t.getParam("typeahead_urls")},fT=function(t){return t.getParam("anchor_top","#top")},lT=function(t){return t.getParam("anchor_bottom","#bottom")},dT=function(t){var n=t.getParam("file_picker_validator_handler",void 0,"function");return void 0===n?t.getParam("filepicker_validator_handler",void 0,"function"):n},mT="silver.readonly",gT=ti([Ii("readonly")]),pT=function(t,n){var e=t.outerContainer,o=e.element;n&&(t.mothership.broadcastOn([yp()],{target:o}),t.uiMothership.broadcastOn([yp()],{target:o})),t.mothership.broadcastOn([mT],{readonly:n}),t.uiMothership.broadcastOn([mT],{readonly:n})},hT=function(t,n){t.on("init",(function(){t.mode.isReadOnly()&&pT(n,!0)})),t.on("SwitchMode",(function(){return pT(n,t.mode.isReadOnly())})),C_(t)&&t.setMode("readonly")},vT=function(){var t;return yl.config({channels:(t={},t[mT]={schema:gT,onReceive:function(t,n){Pv.set(t,n.readonly)}},t)})},bT=function(t){return Pv.config({disabled:t,disableClass:"tox-collection__item--state-disabled"})},yT=function(t){return Pv.config({disabled:t})},xT=function(t){return Pv.config({disabled:t,disableClass:"tox-tbtn--disabled"})},wT=function(t){return Pv.config({disabled:t,disableClass:"tox-tbtn--disabled",useNative:!1})},ST={item:bT,button:yT,splitButton:xT,toolbarButton:wT},kT=function(t,n){var e=t.getApi(n);return function(t){t(e)}},CT=function(t,n){return Ua((function(e){var o=kT(t,e);o((function(e){var o=t.onSetup(e);m(o)&&n.set(o)}))}))},OT=function(t,n){return ja((function(e){return kT(t,e)(n.get())}))},_T=function(t,n){return Ga((function(e,o){kT(t,e)(t.onAction),t.triggersSubmenu||n!==QC.CLOSE_ON_EXECUTE||(Sa(e,ia()),o.stop())}))},TT=(j_={},j_[na()]=["disabling","alloy.base.behaviour","toggling","item-events"],j_),ET=Bt,DT=function(t,n,e,o){var r=ru(h);return{type:"item",dom:n.dom,components:ET(n.optComponents),data:t.data,eventOrder:TT,hasSubmenu:t.triggersSubmenu,itemBehaviours:ml([zx("item-events",[_T(t,e),CT(t,r),OT(t,r)]),ST.item((function(){return t.disabled||o.isDisabled()})),vT(),Vx.config({})].concat(t.itemBehaviours))}},AT=function(t){return{value:t.value,meta:D({text:t.text.getOr("")},t.meta)}},BT=tinymce.util.Tools.resolve("tinymce.Env"),MT=function(t){var n={alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"},e={meta:"Ctrl",access:"Shift+Alt"},o=BT.mac?n:e,r=t.split("+"),i=X(r,(function(t){var n=t.toLowerCase().trim();return Tt(o,n)?o[n]:t}));return BT.mac?i.join(""):i.join("+")},FT=function(t,n,e){return void 0===e&&(e=[iO]),Gk(t,{tag:"div",classes:e},n)},IT=function(t){return{dom:{tag:"div",classes:[uO]},components:[ks(Ik.translate(t))]}},RT=function(t,n){return{dom:{tag:"div",classes:n,innerHtml:t}}},NT=function(t,n){return{dom:{tag:"div",classes:[uO]},components:[{dom:{tag:t.tag,styles:t.styles},components:[ks(Ik.translate(n))]}]}},PT=function(t){return{dom:{tag:"div",classes:[aO],innerHtml:MT(t)}}},HT=function(t){return FT("checkmark",t,[sO])},VT=function(t){return FT("chevron-right",t,[cO])},LT=function(t){return FT("chevron-down",t,[cO])},zT=function(t,n){var e="vertical"===t.direction?dO:mO,o="left"===t.align?pO:gO,r=function(){switch(t.valign){case"top":return hO;case"middle":return vO;case"bottom":return bO}};return{dom:{tag:"div",classes:[lO,e,o,r()]},components:n}},UT=function(t,n,e){return{dom:{tag:"img",classes:n,attributes:{src:t,alt:e.getOr("")}}}},jT=function(t,n,e){var o="custom",r="remove",i=t.ariaLabel,u=t.value,a=t.iconContent.map((function(t){return zk(t,n.icons,e)})),c=function(){var t=nO,e=a.getOr(""),c=i.map((function(t){return{title:n.translate(t)}})).getOr({}),s={tag:"div",attributes:c,classes:[t]};return D(D({},s),u===o?{tag:"button",classes:B(B([],s.classes,!0),["tox-swatches__picker-btn"],!1),innerHtml:e}:u===r?{classes:B(B([],s.classes,!0),["tox-swatch--remove"],!1),innerHtml:e}:{attributes:D(D({},s.attributes),{"data-mce-color":u}),styles:{"background-color":u}})};return{dom:c(),optComponents:[]}},WT=function(t){var n=t.map((function(t){return{attributes:{title:Ik.translate(t)}}})).getOr({});return D({tag:"div",classes:[ZC,tO]},n)},GT=function(t,n,e,o){var r={tag:"div",classes:[iO]},i=function(t){return Gk(t,r,n.icons,o)},u=function(){return N.some({dom:r})},a=e?t.iconContent.map(i).orThunk(u):N.none(),c=t.checkMark,s=N.from(t.meta).fold((function(){return IT}),(function(t){return Tt(t,"style")?k(NT,t.style):IT})),f=t.htmlContent.fold((function(){return t.textContent.map(s)}),(function(t){return N.some(RT(t,[uO]))})),l={dom:WT(t.ariaLabel),optComponents:[a,f,t.shortcutContent.map(PT),c,t.caret]};return l},XT=function(t,n,e,o){return void 0===o&&(o=N.none()),"color"===t.presets?jT(t,n,o):GT(t,n,e,o)},YT=function(t,n){return _t(t,"tooltipWorker").map((function(t){return[y_.config({lazySink:n.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(t){return{type:"submenu",item:t,overrides:{maxHeightFunction:Td}}},mode:"follow-highlight",onShow:function(n,e){t((function(t){y_.setComponents(n,[Cs({element:nn.fromDom(t)})])}))}})]})).getOr([])},qT=function(t){return w_.DOM.encode(t)},KT=function(t,n){var e=Ik.translate(t),o=qT(e);if(n.length>0){var r=new RegExp(x_(n),"gi");return o.replace(r,(function(t){return'<span class="tox-autocompleter-highlight">'+t+"</span>"}))}return o},JT=function(t,n,e,o,r,i,u,a){void 0===a&&(a=!0);var c=XT({presets:o,textContent:N.none(),htmlContent:e?t.text.map((function(t){return KT(t,n)})):N.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:N.none(),checkMark:N.none(),caret:N.none(),value:t.value},u.providers,a,t.icon);return DT({data:AT(t),disabled:t.disabled,getApi:x({}),onAction:function(n){return r(t.value,t.meta)},onSetup:x(h),triggersSubmenu:!1,itemBehaviours:YT(t.meta,u)},c,i,u.providers)},$T=function(t,n){return X(t,(function(t){switch(t.type){case"cardcontainer":return zT(t,$T(t.items,n));case"cardimage":return UT(t.src,t.classes,t.alt);case"cardtext":var e=t.name.exists((function(t){return U(n.cardText.highlightOn,t)})),o=e?N.from(n.cardText.matchText).getOr(""):"";return RT(KT(t.text,o),t.classes)}}))},QT=function(t,n,e,o){var r=function(t){return{isDisabled:function(){return Pv.isDisabled(t)},setDisabled:function(n){Pv.set(t,n),Y(Km(t.element,"*"),(function(e){t.getSystem().getByDom(e).each((function(t){t.hasConfigured(Pv)&&Pv.set(t,n)}))}))}}},i={dom:WT(t.label),optComponents:[N.some({dom:{tag:"div",classes:[lO,mO]},components:$T(t.items,o)})]};return DT({data:AT(D({text:N.none()},t)),disabled:t.disabled,getApi:r,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:N.from(o.itemBehaviours).getOr([])},i,n,e.providers)},ZT=function(t,n,e,o,r,i,u,a){void 0===a&&(a=!0);var c=function(t){return{setActive:function(n){pw.set(t,n)},isActive:function(){return pw.isOn(t)},isDisabled:function(){return Pv.isDisabled(t)},setDisabled:function(n){return Pv.set(t,n)}}},s=XT({presets:e,textContent:n?t.text:N.none(),htmlContent:N.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:n?t.shortcut:N.none(),checkMark:n?N.some(HT(u.icons)):N.none(),caret:N.none(),value:t.value},u,a);return Sr(DT({data:AT(t),disabled:t.disabled,getApi:c,onAction:function(n){return o(t.value)},onSetup:function(t){return t.setActive(r),h},triggersSubmenu:!1,itemBehaviours:[]},s,i,u),{toggling:{toggleClass:oO,toggleOnExecute:!1,selected:t.active}})},tE=zh(Ew(),Dw()),nE=function(t){return{value:t}},eE=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,oE=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,rE=function(t){return eE.test(t)||oE.test(t)},iE=function(t){return Lt(t,"#").toUpperCase()},uE=function(t){return rE(t)?N.some({value:iE(t)}):N.none()},aE=function(t){var n=t.value.replace(eE,(function(t,n,e,o){return n+n+e+e+o+o}));return{value:n}},cE=function(t){var n=aE(t),e=oE.exec(n.value);return null===e?["FFFFFF","FF","FF","FF"]:e},sE=function(t){var n=t.toString(16);return(1===n.length?"0"+n:n).toUpperCase()},fE=function(t){var n=sE(t.red)+sE(t.green)+sE(t.blue);return nE(n)},lE=Math.min,dE=Math.max,mE=Math.round,gE=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,pE=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,hE=function(t,n,e,o){return{red:t,green:n,blue:e,alpha:o}},vE=function(t){var n=parseInt(t,10);return n.toString()===t&&n>=0&&n<=255},bE=function(t){var n,e,o,r=(t.hue||0)%360,i=t.saturation/100,u=t.value/100;if(i=dE(0,lE(i,1)),u=dE(0,lE(u,1)),0===i)return n=e=o=mE(255*u),hE(n,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),f=u-c;switch(Math.floor(a)){case 0:n=c,e=s,o=0;break;case 1:n=s,e=c,o=0;break;case 2:n=0,e=c,o=s;break;case 3:n=0,e=s,o=c;break;case 4:n=s,e=0,o=c;break;case 5:n=c,e=0,o=s;break;default:n=e=o=0}return n=mE(255*(n+f)),e=mE(255*(e+f)),o=mE(255*(o+f)),hE(n,e,o,1)},yE=function(t){var n=cE(t),e=parseInt(n[1],16),o=parseInt(n[2],16),r=parseInt(n[3],16);return hE(e,o,r,1)},xE=function(t,n,e,o){var r=parseInt(t,10),i=parseInt(n,10),u=parseInt(e,10),a=parseFloat(o);return hE(r,i,u,a)},wE=function(t){if("transparent"===t)return N.some(hE(0,0,0,0));var n=gE.exec(t);if(null!==n)return N.some(xE(n[1],n[2],n[3],"1"));var e=pE.exec(t);return null!==e?N.some(xE(e[1],e[2],e[3],e[4])):N.none()},SE=function(t){return"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")"},kE=hE(255,0,0,1),CE=function(t){return t.fire("SkinLoaded")},OE=function(t,n){return t.fire("SkinLoadError",n)},_E=function(t){return t.fire("ResizeEditor")},TE=function(t,n){return t.fire("ResizeContent",n)},EE=function(t,n){return t.fire("ScrollContent",n)},DE=function(t,n){return t.fire("TextColorChange",n)},AE=function(t,n,e){return{hue:t,saturation:n,value:e}},BE=function(t){var n=0,e=0,o=0,r=t.red/255,i=t.green/255,u=t.blue/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));if(a===c)return o=a,AE(0,0,100*o);var s=r===a?i-u:u===a?r-i:u-r;return n=r===a?3:u===a?1:5,n=60*(n-s/(c-a)),e=(c-a)/c,o=c,AE(Math.round(n),Math.round(100*e),Math.round(100*o))},ME=function(t){return BE(yE(t))},FE=function(t){return fE(bE(t))},IE=function(t){return uE(t).orThunk((function(){return wE(t).map(fE)})).getOrThunk((function(){var n=document.createElement("canvas");n.height=1,n.width=1;var e=n.getContext("2d");e.clearRect(0,0,n.width,n.height),e.fillStyle="#FFFFFF",e.fillStyle=t,e.fillRect(0,0,1,1);var o=e.getImageData(0,0,1,1).data,r=o[0],i=o[1],u=o[2],a=o[3];return fE(hE(r,i,u,a))}))},RE=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),NE="tinymce-custom-colors";function PE(t){void 0===t&&(t=10);var n=RE.getItem(NE),e=i(n)?JSON.parse(n):[],o=function(n){var e=t-n.length;return e<0?n.slice(0,t):n},r=o(e),u=function(n){z(r,n).each(a),r.unshift(n),r.length>t&&r.pop(),RE.setItem(NE,JSON.stringify(r))},a=function(t){r.splice(t,1)},c=function(){return r.slice(0)};return{add:u,state:c}}var HE,VE="choiceitem",LE=[{type:VE,text:"Light Green",value:"#BFEDD2"},{type:VE,text:"Light Yellow",value:"#FBEEB8"},{type:VE,text:"Light Red",value:"#F8CAC6"},{type:VE,text:"Light Purple",value:"#ECCAFA"},{type:VE,text:"Light Blue",value:"#C2E0F4"},{type:VE,text:"Green",value:"#2DC26B"},{type:VE,text:"Yellow",value:"#F1C40F"},{type:VE,text:"Red",value:"#E03E2D"},{type:VE,text:"Purple",value:"#B96AD9"},{type:VE,text:"Blue",value:"#3598DB"},{type:VE,text:"Dark Turquoise",value:"#169179"},{type:VE,text:"Orange",value:"#E67E23"},{type:VE,text:"Dark Red",value:"#BA372A"},{type:VE,text:"Dark Purple",value:"#843FA1"},{type:VE,text:"Dark Blue",value:"#236FA1"},{type:VE,text:"Light Gray",value:"#ECF0F1"},{type:VE,text:"Medium Gray",value:"#CED4D9"},{type:VE,text:"Gray",value:"#95A5A6"},{type:VE,text:"Dark Gray",value:"#7E8C8D"},{type:VE,text:"Navy Blue",value:"#34495E"},{type:VE,text:"Black",value:"#000000"},{type:VE,text:"White",value:"#ffffff"}],zE=PE(10),UE=function(t){for(var n=[],e=0;e<t.length;e+=2)n.push({text:t[e+1],value:"#"+IE(t[e]).value,type:"choiceitem"});return n},jE=function(t,n){return t.getParam("color_cols",n,"number")},WE=function(t){return!1!==t.getParam("custom_colors")},GE=function(t){return t.getParam("color_map")},XE=function(t){var n=GE(t);return void 0!==n?UE(n):LE},YE=function(){return X(zE.state(),(function(t){return{type:VE,text:t,value:t}}))},qE=function(t){zE.add(t)},KE="#000000",JE=function(t,n){var e;return t.dom.getParents(t.selection.getStart(),(function(t){var o;(o=t.style["forecolor"===n?"color":"background-color"])&&(e=e||o)})),N.from(e)},$E=function(t,n,e){t.undoManager.transact((function(){t.focus(),t.formatter.apply(n,{value:e}),t.nodeChanged()}))},QE=function(t,n){t.undoManager.transact((function(){t.focus(),t.formatter.remove(n,{value:null},null,!0),t.nodeChanged()}))},ZE=function(t){t.addCommand("mceApplyTextcolor",(function(n,e){$E(t,n,e)})),t.addCommand("mceRemoveTextcolor",(function(n){QE(t,n)}))},tD=function(t){return Math.max(5,Math.ceil(Math.sqrt(t)))},nD=function(t){var n=XE(t),e=tD(n.length);return jE(t,e)},eD=function(t){var n="choiceitem",e={type:n,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"},o={type:n,text:"Custom color",icon:"color-picker",value:"custom"};return t?[e,o]:[e]},oD=function(t,n,e,o){if("custom"===e){var r=sD(t);r((function(e){e.each((function(e){qE(e),t.execCommand("mceApplyTextcolor",n,e),o(e)}))}),KE)}else"remove"===e?(o(""),t.execCommand("mceRemoveTextcolor",n)):(o(e),t.execCommand("mceApplyTextcolor",n,e))},rD=function(t,n){return t.concat(YE().concat(eD(n)))},iD=function(t,n){return function(e){e(rD(t,n))}},uD=function(t,n,e){var o="forecolor"===n?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";t.setIconFill(o,e)},aD=function(t,n,e,o,r){t.ui.registry.addSplitButton(n,{tooltip:o,presets:"color",icon:"forecolor"===n?"text-color":"highlight-bg-color",select:function(n){var o=JE(t,e);return o.bind((function(t){return wE(t).map((function(t){var e=fE(t).value;return Ut(n.toLowerCase(),e)}))})).getOr(!1)},columns:nD(t),fetch:iD(XE(t),WE(t)),onAction:function(n){oD(t,e,r.get(),h)},onItemAction:function(o,i){oD(t,e,i,(function(e){r.set(e),DE(t,{name:n,color:e})}))},onSetup:function(e){uD(e,n,r.get());var o=function(t){t.name===n&&uD(e,t.name,t.color)};return t.on("TextColorChange",o),function(){t.off("TextColorChange",o)}}})},cD=function(t,n,e,o){t.ui.registry.addNestedMenuItem(n,{text:o,icon:"forecolor"===n?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(n){oD(t,e,n.value,h)}}]}})},sD=function(t){return function(n,e){var o=!1,r=function(e){var r=e.getData(),i=r.colorpicker;o?(n(N.from(i)),e.close()):t.windowManager.alert(t.translate(["Invalid hex color code: {0}",i]))},i=function(t,n){"hex-valid"===n.name&&(o=n.value)},u={colorpicker:e};t.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:u,onAction:i,onSubmit:r,onClose:h,onCancel:function(){n(N.none())}})}},fD=function(t){ZE(t);var n=ru(KE),e=ru(KE);aD(t,"forecolor","forecolor","Text color",n),aD(t,"backcolor","hilitecolor","Background color",e),cD(t,"forecolor","forecolor","Text color"),cD(t,"backcolor","hilitecolor","Background color")},lD=function(t,n,e,o,r,i,u,a){var c=MO(n),s="color"!==r?"normal":"color",f=dD(n,e,o,s,i,u,a);return RO(t,c,f,o,r)},dD=function(t,n,e,o,r,i,u){return Bt(X(t,(function(a){return"choiceitem"===a.type?XO(a).fold(FO,(function(c){return N.some(ZT(c,1===e,o,n,i(a.value),r,u,MO(t)))})):N.none()})))},mD=function(t,n){var e=SO(n);if(1===t)return{mode:"menu",moveOnTab:!0};if("auto"===t)return{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}};var o="color"===n?"tox-swatches__row":"tox-collection__group";return{mode:"matrix",rowSelector:"."+o}},gD=function(t,n){return 1===t?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===t?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===n?".tox-swatches__row":".tox-collection__group",cell:"color"===n?"."+nO:"."+tO}}},pD=function(t,n){var e=hD(t,n),o=n.colorinput.getColorCols(),r="color",i=lD(rc("menu-value"),e,(function(n){t.onAction({value:n})}),o,r,QC.CLOSE_ON_EXECUTE,_,n.shared.providers),u=D(D({},i),{markers:SO(r),movement:mD(o,r)});return{type:"widget",data:{value:rc("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[tE.widget(Lw.sketch(u))]}},hD=function(t,n){var e=t.initData.allowCustomColors&&n.colorinput.hasCustomColors();return t.initData.colors.fold((function(){return rD(n.colorinput.getColors(),e)}),(function(t){return t.concat(eD(e))}))},vD=rc("cell-over"),bD=rc("cell-execute"),yD=function(t,n,e){var o,r=function(e){return ka(e,vD,{row:t,col:n})},i=function(e){return ka(e,bD,{row:t,col:n})},u=function(t,n){n.stop(),i(t)};return Ts({dom:{tag:"div",attributes:(o={role:"button"},o["aria-labelledby"]=e,o)},behaviours:ml([zx("insert-table-picker-cell",[Ma(Pu(),Jx.focus),Ma(na(),i),Ma(Wu(),u),Ma(oa(),u)]),pw.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Jx.config({onFocus:r})])})},xD=function(t,n,e){for(var o=[],r=0;r<n;r++){for(var i=[],u=0;u<e;u++)i.push(yD(r,u,t));o.push(i)}return o},wD=function(t,n,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)pw.set(t[i][u],i<=n&&u<=e)},SD=function(t){return ot(t,(function(t){return X(t,Es)}))},kD=function(t,n){return ks(n+"x"+t)},CD=function(t){var n=10,e=10,o=rc("size-label"),r=xD(o,n,e),i=kD(0,0),u=TS({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:o}},components:[i],behaviours:ml([Vx.config({})])});return{type:"widget",data:{value:rc("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[tE.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:SD(r).concat(u.asSpec()),behaviours:ml([zx("insert-table-picker",[Ua((function(t){Vx.set(u.get(t),[i])})),Ha(vD,(function(t,o,i){var a=i.event,c=a.row,s=a.col;wD(r,c,s,n,e),Vx.set(u.get(t),[kD(c+1,s+1)])})),Ha(bD,(function(n,e,o){var r=o.event,i=r.row,u=r.col;t.onAction({numRows:i+1,numColumns:u+1}),Sa(n,ia())}))]),Dx.config({initSize:{numRows:n,numColumns:e},mode:"flatgrid",selector:'[role="button"]'})])})]}},OD={inserttable:CD,colorswatch:pD},_D=function(t,n){return _t(OD,t.fancytype).map((function(e){return e(t,n)}))},TD=function(t,n,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i=r?LT(e.icons):VT(e.icons),u=function(t){return{isDisabled:function(){return Pv.isDisabled(t)},setDisabled:function(n){return Pv.set(t,n)}}},a=XT({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:N.none(),ariaLabel:t.text,caret:N.some(i),checkMark:N.none(),shortcutContent:t.shortcut},e,o);return DT({data:AT(t),getApi:u,disabled:t.disabled,onAction:h,onSetup:t.onSetup,triggersSubmenu:!0,itemBehaviours:[]},a,n,e)},ED=function(t,n,e,o){void 0===o&&(o=!0);var r=function(t){return{isDisabled:function(){return Pv.isDisabled(t)},setDisabled:function(n){return Pv.set(t,n)}}},i=XT({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:N.none(),ariaLabel:t.text,caret:N.none(),checkMark:N.none(),shortcutContent:t.shortcut},e,o);return DT({data:AT(t),getApi:r,disabled:t.disabled,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},i,n,e)},DD=function(t){var n=t.text.fold((function(){return{}}),(function(t){return{innerHtml:t}}));return{type:"separator",dom:D({tag:"div",classes:[tO,rO]},n),components:[]}},AD=function(t,n,e,o){void 0===o&&(o=!0);var r=function(t){return{setActive:function(n){pw.set(t,n)},isActive:function(){return pw.isOn(t)},isDisabled:function(){return Pv.isDisabled(t)},setDisabled:function(n){return Pv.set(t,n)}}},i=XT({iconContent:t.icon,textContent:t.text,htmlContent:N.none(),ariaLabel:t.text,checkMark:N.some(HT(e.icons)),caret:N.none(),shortcutContent:t.shortcut,presets:"normal",meta:t.meta},e,o);return Sr(DT({data:AT(t),disabled:t.disabled,getApi:r,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},i,n,e),{toggling:{toggleClass:oO,toggleOnExecute:!1,selected:t.active}})},BD=JT,MD=DD,FD=ED,ID=TD,RD=AD,ND=_D,PD=QT;(function(t){t[t["ContentFocus"]=0]="ContentFocus",t[t["UiFocus"]=1]="UiFocus"})(HE||(HE={}));var HD=function(t,n,e,o,r){var i=e.shared.providers,u=function(t){return r?D(D({},t),{shortcut:N.none(),icon:t.text.isSome()?N.none():t.icon}):t};switch(t.type){case"menuitem":return ZO(t).fold(FO,(function(t){return N.some(FD(u(t),n,i,o))}));case"nestedmenuitem":return n_(t).fold(FO,(function(t){return N.some(ID(u(t),n,i,o,r))}));case"togglemenuitem":return o_(t).fold(FO,(function(t){return N.some(RD(u(t),n,i,o))}));case"separator":return TC(t).fold(FO,(function(t){return N.some(MD(t))}));case"fancymenuitem":return $O(t).fold(FO,(function(t){return ND(u(t),e)}));default:return N.none()}},VD=function(t,n,e,o,r,i,u){var a=1===o,c=!a||MO(t);return Bt(X(t,(function(t){switch(t.type){case"separator":return AC(t).fold(FO,(function(t){return N.some(MD(t))}));case"cardmenuitem":return WO(t).fold(FO,(function(t){return N.some(PD(D(D({},t),{onAction:function(n){t.onAction(n),e(t.value,t.meta)}}),r,i,{itemBehaviours:YT(t.meta,i),cardText:{matchText:n,highlightOn:u}}))}));case"autocompleteitem":default:return BC(t).fold(FO,(function(t){return N.some(BD(t,n,a,"normal",e,r,i,c))}))}})))},LD=function(t,n,e,o,r){var i=MO(n),u=Bt(X(n,(function(t){var n=function(t){return r?!Tt(t,"text"):i},u=function(t){return HD(t,e,o,n(t),r)};return"nestedmenuitem"===t.type&&t.getSubmenuItems().length<=0?u(D(D({},t),{disabled:!0})):u(t)}))),a=r?IO:RO;return a(t,i,u,1,"normal")},zD=function(t){return Qw.singleData(t.value,t)},UD=function(t,n,e,o){var r=e===HE.ContentFocus?_b():Ob(),i=mD(n,o),u=SO(o);return{dom:t.dom,components:t.components,items:t.items,value:t.value,markers:{selectedItem:u.selectedItem,item:u.item},movement:i,fakeFocus:e===HE.ContentFocus,focusManager:r,menuBehaviours:a_.unnamedEvents("auto"!==n?[]:[Ua((function(t,n){r_(t,4,u.item).each((function(n){var e=n.numColumns,o=n.numRows;Dx.setGridSize(t,o,e)}))}))])}},jD=function(t,n){var e=rd(),o=ru(!1),r=Ts(nS.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:ml([zx("dismissAutocompleter",[Ma(ga(),(function(){return c()}))])]),lazySink:n.getSink})),i=function(){return nS.isOpen(r)},u=function(){return e.get().isSome()},a=function(){u()&&nS.hide(r)},c=function(){if(u()){var n=e.get().map((function(t){return t.element}));iC(n.getOr(nn.fromDom(t.selection.getNode()))).each(Po),a(),e.clear(),o.set(!1)}},s=pn((function(){return JC(t)})),f=function(e,r){var i=gt(r,(function(t){return N.from(t.columns)})).getOr(1);return ot(r,(function(r){var u=r.items;return VD(u,r.matchText,(function(n,i){var u=t.selection.getRng();dC(t.dom,u,e).fold((function(){}),(function(t){var e=t.range,u={hide:function(){c()},reload:function(t){a(),g(t)}};o.set(!0),r.onAction(u,e,n,i),o.set(!1)}))}),i,QC.BUBBLE_TO_SANDBOX,n,r.highlightOn)}))},l=function(n){if(!u()){var r=rC(t,n.range);e.set({triggerChar:n.triggerChar,element:r,matchLength:n.text.length}),o.set(!1)}},d=function(n,e,o,i){n.matchLength=e.text.length;var u=gt(o,(function(t){return N.from(t.columns)})).getOr(1);nS.showAt(r,Lw.sketch(UD(RO("autocompleter-value",!0,i,u,"normal"),u,HE.ContentFocus,"normal")),{anchor:{type:"node",root:nn.fromDom(t.getBody()),node:N.from(n.element)}}),nS.getContent(r).each(ob.highlightFirst)},m=function(n){return e.get().map((function(e){return dC(t.dom,t.selection.getRng(),e.triggerChar).bind((function(e){return OC(t,s,e,n)}))})).getOrThunk((function(){return CC(t,s)}))},g=function(t){m(t).fold(c,(function(t){l(t.context),t.lookupData.then((function(n){e.get().map((function(e){var o=t.context;if(e.triggerChar===o.triggerChar){var r=f(o.triggerChar,n);r.length>0?d(e,o,n,r):o.text.length-e.matchLength>=10?c():a()}}))}))}))},p=Zk((function(t){27!==t.which&&g()}),50),h={onKeypress:p,cancelIfNecessary:c,isMenuOpen:i,isActive:u,isProcessingAction:o.get,getView:function(){return nS.getContent(r)}};!1===t.hasPlugin("rtc")&&gC.setup(h,t)},WD={register:jD},GD=function(t,n,e){return Vs(t,n,e).isSome()},XD=function(t,n){var e=null,o=function(){for(var o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];e=setTimeout((function(){t.apply(null,o),e=null}),n)},r=function(){null!==e&&(clearTimeout(e),e=null)};return{cancel:r,schedule:o}},YD=5,qD=400,KD=function(t){var n=t.raw;return void 0===n.touches||1!==n.touches.length?N.none():N.some(n.touches[0])},JD=function(t,n){var e=Math.abs(t.clientX-n.x),o=Math.abs(t.clientY-n.y);return e>YD||o>YD},$D=function(t){var n=rd(),e=ru(!1),o=XD((function(n){t.triggerEvent(ra(),n),e.set(!0)}),qD),r=function(t){return KD(t).each((function(r){o.cancel();var i={x:r.clientX,y:r.clientY,target:t.target};o.schedule(t),e.set(!1),n.set(i)})),N.none()},i=function(t){return o.cancel(),KD(t).each((function(t){n.on((function(e){JD(t,e)&&n.clear()}))})),N.none()},u=function(r){o.cancel();var i=function(t){return se(t.target,r.target)};return n.get().filter(i).map((function(n){return e.get()?(r.prevent(),!1):t.triggerEvent(oa(),r)}))},a=gu([{key:Du(),value:r},{key:Au(),value:i},{key:Bu(),value:u}]),c=function(t,n){return _t(a,n).bind((function(n){return n(t)}))};return{fireIfReady:c}},QD=function(t){var n=t.raw;return n.which===rb[0]&&!U(["input","textarea"],cn(t.target))&&!GD(t.target,'[contenteditable="true"]')},ZD=function(){return ee().browser.isFirefox()},tA=function(t,n){return ZD()?ad(t,"focus",n):ud(t,"focusin",n)},nA=function(t,n){return ZD()?ad(t,"blur",n):ud(t,"focusout",n)},eA=function(t,n){var e=D({stopBackspace:!0},n),o=["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"],r=$D(e),i=X(o.concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(function(n){return ud(t,n,(function(t){r.fireIfReady(t,n).each((function(n){n&&t.kill()}));var o=e.triggerEvent(n,t);o&&t.kill()}))})),u=rd(),a=ud(t,"paste",(function(t){r.fireIfReady(t,"paste").each((function(n){n&&t.kill()}));var n=e.triggerEvent("paste",t);n&&t.kill(),u.set(setTimeout((function(){e.triggerEvent(Zu(),t)}),0))})),c=ud(t,"keydown",(function(t){var n=e.triggerEvent("keydown",t);n?t.kill():e.stopBackspace&&QD(t)&&t.prevent()})),s=tA(t,(function(t){var n=e.triggerEvent("focusin",t);n&&t.kill()})),f=rd(),l=nA(t,(function(t){var n=e.triggerEvent("focusout",t);n&&t.kill(),f.set(setTimeout((function(){e.triggerEvent(Qu(),t)}),0))})),d=function(){Y(i,(function(t){t.unbind()})),c.unbind(),s.unbind(),l.unbind(),a.unbind(),u.on(clearTimeout),f.on(clearTimeout)};return{unbind:d}},oA=function(t,n){var e=_t(t,"target").getOr(n);return ru(e)},rA=function(t,n){var e=ru(!1),o=ru(!1),r=function(){e.set(!0)},i=function(){o.set(!0)};return{stop:r,cut:i,isStopped:e.get,isCut:o.get,event:t,setSource:n.set,getSource:n.get}},iA=function(t){var n=ru(!1),e=function(){n.set(!0)};return{stop:e,cut:h,isStopped:n.get,isCut:_,event:t,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}},uA=uu.generate([{stopped:[]},{resume:["element"]},{complete:[]}]),aA=function(t,n,e,o,r,i){var u=t(n,o),a=rA(e,r);return u.fold((function(){return i.logEventNoHandlers(n,o),uA.complete()}),(function(t){var e=t.descHandler,o=Pc(e);return o(a),a.isStopped()?(i.logEventStopped(n,t.element,e.purpose),uA.stopped()):a.isCut()?(i.logEventCut(n,t.element,e.purpose),uA.complete()):ve(t.element).fold((function(){return i.logNoParent(n,t.element,e.purpose),uA.complete()}),(function(o){return i.logEventResponse(n,t.element,e.purpose),uA.resume(o)}))}))},cA=function(t,n,e,o,r,i){return aA(t,n,e,o,r,i).fold(T,(function(o){return cA(t,n,e,o,r,i)}),_)},sA=function(t,n,e,o,r){var i=oA(e,o);return aA(t,n,e,o,i,r)},fA=function(t,n,e){var o=iA(n);return Y(t,(function(t){var n=t.descHandler,e=Pc(n);e(o)})),o.isStopped()},lA=function(t,n,e,o){return dA(t,n,e,e.target,o)},dA=function(t,n,e,o,r){var i=oA(e,o);return cA(t,n,e,o,i,r)},mA=function(t,n){return{element:t,descHandler:n}},gA=function(t,n){return{id:t,descHandler:n}},pA=function(){var t={},n=function(n,e,o){vt(o,(function(o,r){var i=void 0!==t[r]?t[r]:{};i[e]=Nc(o,n),t[r]=i}))},e=function(t,n){return lc(n).bind((function(n){return _t(t,n)})).map((function(t){return mA(n,t)}))},o=function(n){return _t(t,n).map((function(t){return kt(t,(function(t,n){return gA(n,t)}))})).getOr([])},r=function(n,o,r){return _t(t,o).bind((function(t){return xu(r,(function(n){return e(t,n)}),n)}))},i=function(n){vt(t,(function(t,e){Tt(t,n)&&delete t[n]}))};return{registerId:n,unregisterId:i,filterByType:o,find:r}},hA=function(){var t=pA(),n={},e=function(t){var n=t.element;return lc(n).getOrThunk((function(){return sc("uid-",t.element)}))},o=function(t,e){var o=n[e];if(o!==t)throw new Error('The tagId "'+e+'" is already used by: '+Za(o.element)+"\nCannot use it for: "+Za(t.element)+"\nThe conflicting element is"+(Ne(o.element)?" ":" not ")+"already in the DOM");i(t)},r=function(r){var i=e(r);Et(n,i)&&o(r,i);var u=[r];t.registerId(u,i,r.events),n[i]=r},i=function(e){lc(e.element).each((function(e){delete n[e],t.unregisterId(e)}))},u=function(n){return t.filterByType(n)},a=function(n,e,o){return t.find(n,e,o)},c=function(t){return _t(n,t)};return{find:a,filter:u,register:r,unregister:i,getById:c}},vA=function(t){var n=t.dom,e=n.attributes,o=A(n,["attributes"]);return{uid:t.uid,dom:D({tag:"div",attributes:D({role:"presentation"},e)},o),components:t.components,behaviours:eh(t.containerBehaviours),events:t.events,domModification:t.domModification,eventOrder:t.eventOrder}},bA=dv({name:"Container",factory:vA,configFields:[qi("components",[]),nh("containerBehaviours",[]),qi("events",{}),qi("domModification",{}),qi("eventOrder",{})]}),yA=function(t){var n=function(n){return ve(t.element).fold(T,(function(t){return se(n,t)}))},e=hA(),o=function(t,o){return e.find(n,t,o)},r=eA(t.element,{triggerEvent:function(t,n){return $s(t,n.target,(function(e){return lA(o,t,n,e)}))}}),i={debugInfo:x("real"),triggerEvent:function(t,n,e){$s(t,n,(function(r){return dA(o,t,e,n,r)}))},triggerFocus:function(t,n){lc(t).fold((function(){kl(t)}),(function(e){$s($u(),t,(function(e){return sA(o,$u(),{originator:n,kill:h,prevent:h,target:t},t,e),!1}))}))},triggerEscape:function(t,n){i.triggerEvent("keydown",t.element,n.event)},getByUid:function(t){return p(t)},getByDom:function(t){return v(t)},build:Ts,addToGui:function(t){c(t)},removeFromGui:function(t){s(t)},addToWorld:function(t){u(t)},removeFromWorld:function(t){a(t)},broadcast:function(t){d(t)},broadcastOn:function(t,n){m(t,n)},broadcastEvent:function(t,n){g(t,n)},isConnected:T},u=function(t){t.connect(i),dn(t.element)||(e.register(t),Y(t.components(),u),i.triggerEvent(aa(),t.element,{target:t.element}))},a=function(t){dn(t.element)||(Y(t.components(),a),e.unregister(t)),t.disconnect()},c=function(n){Wg(t,n)},s=function(t){Yg(t)},f=function(){r.unbind(),No(t.element)},l=function(t){var n=e.filter(ta());Y(n,(function(n){var e=n.descHandler,o=Pc(e);o(t)}))},d=function(t){l({universal:!0,data:t})},m=function(t,n){l({universal:!1,channels:t,data:n})},g=function(t,n){var o=e.filter(t);return fA(o,n)},p=function(t){return e.getById(t).fold((function(){return rr.error(new Error('Could not find component with uid: "'+t+'" in system.'))}),rr.value)},v=function(t){var n=lc(t).getOr("not found");return p(n)};return u(t),{root:t,element:t.element,destroy:f,add:c,remove:s,getByUid:p,getByDom:v,addToWorld:u,removeFromWorld:a,broadcast:d,broadcastOn:m,broadcastEvent:g}},xA=function(t,n){return{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:X(t.items,n.interpreter)}},wA=x([qi("prefix","form-field"),nh("fieldBehaviours",[bv,th])]),SA=x([Rh({schema:[Di("dom")],name:"label"}),Rh({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[Di("text")],name:"aria-descriptor"}),Fh({factory:{sketch:function(t){var n=du(t,["factory"]);return t.factory.sketch(n)}},schema:[Di("factory")],name:"field"})]),kA=function(t,n,e,o){var r=oh(t.fieldBehaviours,[bv.config({find:function(n){return qh(n,t,"field")}}),th.config({store:{mode:"manual",getValue:function(t){return bv.getCurrent(t).bind(th.getValue)},setValue:function(t,n){bv.getCurrent(t).each((function(t){th.setValue(t,n)}))}}})]),i=Ea([Ua((function(n,e){var o=Jh(n,t,["label","field","aria-descriptor"]);o.field().each((function(n){var e=rc(t.prefix);o.label().each((function(t){Le(t.element,"for",e),Le(n.element,"id",e)})),o["aria-descriptor"]().each((function(e){var o=rc(t.prefix);Le(e.element,"id",o),Le(n.element,"aria-describedby",o)}))}))}))]),u={getField:function(n){return qh(n,t,"field")},getLabel:function(n){return qh(n,t,"label")}};return{uid:t.uid,dom:t.dom,components:n,behaviours:r,events:i,apis:u}},CA=mv({name:"FormField",configFields:wA(),partFields:SA(),factory:kA,apis:{getField:function(t,n){return t.getField(n)},getLabel:function(t,n){return t.getLabel(n)}}}),OA=function(t,n){return Ac({attributes:gu([{key:n.tabAttr,value:"true"}])})},_A=Object.freeze({__proto__:null,exhibit:OA}),TA=[qi("tabAttr","data-alloy-tabstop")],EA=pl({fields:TA,name:"tabstopping",active:_A}),DA=tinymce.util.Tools.resolve("tinymce.html.Entities"),AA=function(t,n,e,o){var r=MA(t,n,e,o);return CA.sketch(r)},BA=function(t,n){return AA(t,n,[],[])},MA=function(t,n,e,o){return{dom:IA(e),components:t.toArray().concat([n]),fieldBehaviours:ml(o)}},FA=function(){return IA([])},IA=function(t){return{tag:"div",classes:["tox-form__group"].concat(t)}},RA=function(t,n){return CA.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}})},NA=rc("form-component-change"),PA=rc("form-close"),HA=rc("form-cancel"),VA=rc("form-action"),LA=rc("form-submit"),zA=rc("form-block"),UA=rc("form-unblock"),jA=rc("form-tabchange"),WA=rc("form-resize"),GA=function(t,n){var e,o=t.label.map((function(t){return RA(t,n)})),r=function(t){return function(n,e){Vs(e.event.target,"[data-collection-item-value]").each((function(o){t(n,e,o,Ue(o,"data-collection-item-value"))}))}},i=function(e,o){var r=X(o,(function(e){var o=Ik.translate(e.text),r=1===t.columns?'<div class="tox-collection__item-label">'+o+"</div>":"",i='<div class="tox-collection__item-icon">'+e.icon+"</div>",u={_:" "," - ":" ","-":" "},a=o.replace(/\_| \- |\-/g,(function(t){return u[t]})),c=n.isDisabled()?" tox-collection__item--state-disabled":"";return'<div class="tox-collection__item'+c+'" tabindex="-1" data-collection-item-value="'+DA.encodeAllRaw(e.value)+'" title="'+a+'" aria-label="'+a+'">'+i+r+"</div>"})),i="auto"!==t.columns&&t.columns>1?G(r,t.columns):[r],u=X(i,(function(t){return'<div class="tox-collection__group">'+t.join("")+"</div>"}));qa(e.element,u.join(""))},u=r((function(e,o,r,i){o.stop(),n.isDisabled()||ka(e,VA,{name:t.name,value:i})})),a=[Ma(Pu(),r((function(t,n,e){kl(e)}))),Ma(Wu(),u),Ma(oa(),u),Ma(Hu(),r((function(t,n,e){Hs(t.element,"."+fO).each((function(t){as(t,fO)})),is(e,fO)}))),Ma(Vu(),r((function(t){Hs(t.element,"."+fO).each((function(t){as(t,fO)}))}))),Ga(r((function(n,e,o,r){ka(n,VA,{name:t.name,value:r})})))],c=function(t,n){return X(Km(t.element,".tox-collection__item"),n)},s=CA.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==t.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:ml([Pv.config({disabled:n.isDisabled,onDisabled:function(t){c(t,(function(t){is(t,"tox-collection__item--state-disabled"),Le(t,"aria-disabled",!0)}))},onEnabled:function(t){c(t,(function(t){as(t,"tox-collection__item--state-disabled"),Ge(t,"aria-disabled")}))}}),vT(),Vx.config({}),th.config({store:{mode:"memory",initialValue:[]},onSetValue:function(n,e){i(n,e),"auto"===t.columns&&r_(n,5,"tox-collection__item").each((function(t){var e=t.numRows,o=t.numColumns;Dx.setGridSize(n,e,o)})),Sa(n,WA)}}),EA.config({}),Dx.config(gD(t.columns,"normal")),zx("collection-events",a)]),eventOrder:(e={},e[na()]=["disabling","alloy.base.behaviour","collection-events"],e)}),f=["tox-form__group--collection"];return AA(o,s,f,[])},XA=x([Li("data"),qi("inputAttributes",{}),qi("inputStyles",{}),qi("tag","input"),qi("inputClasses",[]),cf("onSetValue"),qi("styles",{}),qi("eventOrder",{}),nh("inputBehaviours",[th,Jx]),qi("selectOnFocus",!0)]),YA=function(t){return ml([Jx.config({onFocus:t.selectOnFocus?function(t){var n=t.element,e=ds(n);n.dom.setSelectionRange(0,e.length)}:h})])},qA=function(t){return D(D({},YA(t)),oh(t.inputBehaviours,[th.config({store:D(D({mode:"manual"},t.data.map((function(t){return{initialValue:t}})).getOr({})),{getValue:function(t){return ds(t.element)},setValue:function(t,n){var e=ds(t.element);e!==n&&ms(t.element,n)}}),onSetValue:t.onSetValue})]))},KA=function(t){return{tag:t.tag,attributes:D({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}},JA=function(t,n){return{uid:t.uid,dom:KA(t),components:[],behaviours:qA(t),eventOrder:t.eventOrder}},$A=dv({name:"Input",configFields:XA(),factory:JA}),QA={},ZA={exports:QA};(function(n,e,o,r){(function(t,r){"object"===typeof e&&"undefined"!==typeof o?o.exports=r():"function"===typeof n&&n.amd?n(r):(t="undefined"!==typeof globalThis?globalThis:t||self,t.EphoxContactWrapper=r())})(this,(function(){var n="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof t?t:"undefined"!==typeof self?self:{},e={exports:{}};(function(t){(function(n){var e=setTimeout;function o(){}function r(t,n){return function(){t.apply(n,arguments)}}function i(t){if("object"!==typeof this)throw new TypeError("Promises must be constructed via new");if("function"!==typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],l(t,this)}function u(t,n){while(3===t._state)t=t._value;0!==t._state?(t._handled=!0,i._immediateFn((function(){var e=1===t._state?n.onFulfilled:n.onRejected;if(null!==e){var o;try{o=e(t._value)}catch(r){return void c(n.promise,r)}a(n.promise,o)}else(1===t._state?a:c)(n.promise,t._value)}))):t._deferreds.push(n)}function a(t,n){try{if(n===t)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"===typeof n||"function"===typeof n)){var e=n.then;if(n instanceof i)return t._state=3,t._value=n,void s(t);if("function"===typeof e)return void l(r(e,n),t)}t._state=1,t._value=n,s(t)}catch(o){c(t,o)}}function c(t,n){t._state=2,t._value=n,s(t)}function s(t){2===t._state&&0===t._deferreds.length&&i._immediateFn((function(){t._handled||i._unhandledRejectionFn(t._value)}));for(var n=0,e=t._deferreds.length;n<e;n++)u(t,t._deferreds[n]);t._deferreds=null}function f(t,n,e){this.onFulfilled="function"===typeof t?t:null,this.onRejected="function"===typeof n?n:null,this.promise=e}function l(t,n){var e=!1;try{t((function(t){e||(e=!0,a(n,t))}),(function(t){e||(e=!0,c(n,t))}))}catch(o){if(e)return;e=!0,c(n,o)}}i.prototype["catch"]=function(t){return this.then(null,t)},i.prototype.then=function(t,n){var e=new this.constructor(o);return u(this,new f(t,n,e)),e},i.all=function(t){var n=Array.prototype.slice.call(t);return new i((function(t,e){if(0===n.length)return t([]);var o=n.length;function r(i,u){try{if(u&&("object"===typeof u||"function"===typeof u)){var a=u.then;if("function"===typeof a)return void a.call(u,(function(t){r(i,t)}),e)}n[i]=u,0===--o&&t(n)}catch(c){e(c)}}for(var i=0;i<n.length;i++)r(i,n[i])}))},i.resolve=function(t){return t&&"object"===typeof t&&t.constructor===i?t:new i((function(n){n(t)}))},i.reject=function(t){return new i((function(n,e){e(t)}))},i.race=function(t){return new i((function(n,e){for(var o=0,r=t.length;o<r;o++)t[o].then(n,e)}))},i._immediateFn="function"===typeof setImmediate?function(t){setImmediate(t)}:function(t){e(t,0)},i._unhandledRejectionFn=function(t){"undefined"!==typeof console&&console},i._setImmediateFn=function(t){i._immediateFn=t},i._setUnhandledRejectionFn=function(t){i._unhandledRejectionFn=t},t.exports?t.exports=i:n.Promise||(n.Promise=i)})(n)})(e);var o=e.exports,r=function(){return"undefined"!==typeof window?window:Function("return this;")()}(),i={boltExport:r.Promise||o};return i}))})(void 0,QA,ZA);var tB,nB=ZA.exports.boltExport,eB=function(t){var n=N.none(),e=[],o=function(t){return eB((function(n){r((function(e){n(t(e))}))}))},r=function(t){u()?c(t):e.push(t)},i=function(t){u()||(n=N.some(t),a(e),e=[])},u=function(){return n.isSome()},a=function(t){Y(t,c)},c=function(t){n.each((function(n){setTimeout((function(){t(n)}),0)}))};return t(i),{get:r,map:o,isReady:u}},oB=function(t){return eB((function(n){n(t)}))},rB={nu:eB,pure:oB},iB=function(t){setTimeout((function(){throw t}),0)},uB=function(t){var n=function(n){t().then(n,iB)},e=function(n){return uB((function(){return t().then(n)}))},o=function(n){return uB((function(){return t().then((function(t){return n(t).toPromise()}))}))},r=function(n){return uB((function(){return t().then((function(){return n.toPromise()}))}))},i=function(){return rB.nu(n)},u=function(){var n=null;return uB((function(){return null===n&&(n=t()),n}))},a=t;return{map:e,bind:o,anonBind:r,toLazy:i,toCached:u,toPromise:a,get:n}},aB=function(t){return uB((function(){return new nB(t)}))},cB=function(t){return uB((function(){return nB.resolve(t)}))},sB={nu:aB,pure:cB},fB=["input","textarea"],lB=function(t){var n=cn(t);return U(fB,n)},dB=function(t,n){var e=n.getRoot(t).getOr(t.element);as(e,n.invalidClass),n.notify.each((function(n){lB(t.element)&&Le(t.element,"aria-invalid",!1),n.getContainer(t).each((function(t){qa(t,n.validHtml)})),n.onValid(t)}))},mB=function(t,n,e,o){var r=n.getRoot(t).getOr(t.element);is(r,n.invalidClass),n.notify.each((function(n){lB(t.element)&&Le(t.element,"aria-invalid",!0),n.getContainer(t).each((function(t){qa(t,o)})),n.onInvalid(t,o)}))},gB=function(t,n,e){return n.validator.fold((function(){return sB.pure(rr.value(!0))}),(function(n){return n.validate(t)}))},pB=function(t,n,e){return n.notify.each((function(n){n.onValidate(t)})),gB(t,n).map((function(o){return t.getSystem().isConnected()?o.fold((function(o){return mB(t,n,e,o),rr.error(o)}),(function(e){return dB(t,n),rr.value(e)})):rr.error("No longer in system")}))},hB=function(t,n){var e=n.getRoot(t).getOr(t.element);return cs(e,n.invalidClass)},vB=Object.freeze({__proto__:null,markValid:dB,markInvalid:mB,query:gB,run:pB,isInvalid:hB}),bB=function(t,n){return t.validator.map((function(e){return Ea([Ma(e.onEvent,(function(e){pB(e,t,n).get(w)}))].concat(e.validateOnLoad?[Ua((function(e){pB(e,t,n).get(h)}))]:[]))})).getOr({})},yB=Object.freeze({__proto__:null,events:bB}),xB=[Di("invalidClass"),qi("getRoot",N.none),Xi("notify",[qi("aria","alert"),qi("getContainer",N.none),qi("validHtml",""),cf("onValid"),cf("onInvalid"),cf("onValidate")]),Xi("validator",[Di("validate"),qi("onEvent","input"),qi("validateOnLoad",!0)])],wB=pl({fields:xB,name:"invalidating",active:yB,apis:vB,extra:{validation:function(t){return function(n){var e=th.getValue(n);return sB.pure(t(e))}}}}),SB=function(t,n,e,o){return e.getOrCreate(t,n,o)},kB=Object.freeze({__proto__:null,getCoupled:SB}),CB=[Ai("others",bi(rr.value,ui()))],OB=function(){var t={},n=function(n,e,o){var r=pt(e.others);if(r)return _t(t,o).getOrThunk((function(){var r=_t(e.others,o).getOrDie("No information found for coupled component: "+o),i=r(n),u=n.getSystem().build(i);return t[o]=u,u}));throw new Error("Cannot find coupled component: "+o+". Known coupled components: "+JSON.stringify(r,null,2))},e=x({});return Oc({readState:e,getOrCreate:n})},_B=Object.freeze({__proto__:null,init:OB}),TB=pl({fields:CB,name:"coupling",apis:kB,state:_B}),EB=x("sink"),DB=x(Rh({name:EB(),overrides:x({dom:{tag:"div"},behaviours:ml([Hg.config({useFixed:T})]),events:Ea([Va(Lu()),Va(Fu()),Va(Wu())])})}));(function(t){t[t["HighlightFirst"]=0]="HighlightFirst",t[t["HighlightNone"]=1]="HighlightNone"})(tB||(tB={}));var AB,BB=function(t,n){var e=t.getHotspot(n).getOr(n),o="hotspot",r=t.getAnchorOverrides();return t.layouts.fold((function(){return{type:o,hotspot:e,overrides:r}}),(function(t){return{type:o,hotspot:e,overrides:r,layouts:t}}))},MB=function(t,n,e){var o=t.fetch;return o(e).map(n)},FB=function(t,n,e,o,r,i,u){var a=MB(t,n,o),c=HB(o,t);return a.map((function(t){return t.bind((function(t){return N.from(Qw.sketch(D(D({},i.menu()),{uid:dc(""),data:t,highlightImmediately:u===tB.HighlightFirst,onOpenMenu:function(t,n){var o=c().getOrDie();Hg.position(o,n,{anchor:e}),bp.decloak(r)},onOpenSubmenu:function(t,n,e){var o=c().getOrDie();Hg.position(o,e,{anchor:{type:"submenu",item:n}}),bp.decloak(r)},onRepositionMenu:function(t,n,o){var r=c().getOrDie();Hg.position(r,n,{anchor:e}),Y(o,(function(t){Hg.position(r,t.triggeredMenu,{anchor:{type:"submenu",item:t.triggeringItem}})}))},onEscape:function(){return Jx.focus(o),bp.close(r),N.some(!0)}})))}))}))},IB=function(t,n,e,o,r,i,u){var a=BB(t,e),c=FB(t,n,a,e,o,r,u);return c.map((function(t){return t.fold((function(){bp.isOpen(o)&&bp.close(o)}),(function(t){bp.cloak(o),bp.open(o,t),i(o)})),o}))},RB=function(t,n,e,o,r,i,u){return bp.close(o),sB.pure(o)},NB=function(t,n,e,o,r,i){var u=TB.getCoupled(e,"sandbox"),a=bp.isOpen(u),c=a?RB:IB;return c(t,n,e,u,o,r,i)},PB=function(t,n,e){var o=bv.getCurrent(n).getOr(n),r=bo(t.element);e?qe(o.element,"min-width",r+"px"):vo(o.element,r)},HB=function(t,n){return t.getSystem().getByUid(n.uid+"-"+EB()).map((function(t){return function(){return rr.value(t)}})).getOrThunk((function(){return n.lazySink.fold((function(){return function(){return rr.error(new Error("No internal sink is specified, nor could an external sink be found"))}}),(function(n){return function(){return n(t)}}))}))},VB=function(t){bp.getState(t).each((function(t){Qw.repositionMenus(t)}))},LB=function(t,n,e){var o=zs(),r=function(r,i){var u=BB(t,n);o.link(n.element),t.matchWidth&&PB(u.hotspot,i,t.useMinWidth),t.onOpen(u,r,i),void 0!==e&&void 0!==e.onOpen&&e.onOpen(r,i)},i=function(t,r){o.unlink(n.element),void 0!==e&&void 0!==e.onClose&&e.onClose(t,r)},u=HB(n,t);return{dom:{tag:"div",classes:t.sandboxClasses,attributes:{id:o.id,role:"listbox"}},behaviours:rh.augment(t.sandboxBehaviours,[th.config({store:{mode:"memory",initialValue:n}}),bp.config({onOpen:r,onClose:i,isPartOf:function(t,e,o){return js(e,o)||js(n,o)},getAttachPoint:function(){return u().getOrDie()}}),bv.config({find:function(t){return bp.getState(t).bind((function(t){return bv.getCurrent(t)}))}}),yl.config({channels:D(D({},kp({isExtraPart:_})),Op({doReposition:VB}))})])}},zB=function(t){var n=TB.getCoupled(t,"sandbox");VB(n)},UB=function(){return[qi("sandboxClasses",[]),rh.field("sandboxBehaviours",[bv,yl,bp,th])]},jB=x([Di("dom"),Di("fetch"),cf("onOpen"),sf("onExecute"),qi("getHotspot",N.some),qi("getAnchorOverrides",x({})),Vd(),nh("dropdownBehaviours",[pw,TB,Dx,Jx]),Di("toggleClass"),qi("eventOrder",{}),Li("lazySink"),qi("matchWidth",!1),qi("useMinWidth",!1),Li("role")].concat(UB())),WB=x([Ih({schema:[rf()],name:"menu",defaults:function(t){return{onExecute:t.onExecute}}}),DB()]),GB=function(t,n,e,o){var r,i=function(n){return _t(t.dom,"attributes").bind((function(t){return _t(t,n)}))},u=function(t){bp.getState(t).each((function(t){Qw.highlightPrimary(t)}))},a=function(n){var e=u;NB(t,w,n,o,e,tB.HighlightFirst).get(h)},c={expand:function(n){pw.isOn(n)||NB(t,w,n,o,h,tB.HighlightNone).get(h)},open:function(n){pw.isOn(n)||NB(t,w,n,o,h,tB.HighlightFirst).get(h)},isOpen:pw.isOn,close:function(n){pw.isOn(n)&&NB(t,w,n,o,h,tB.HighlightFirst).get(h)},repositionMenus:function(t){pw.isOn(t)&&zB(t)}},s=function(t,n){return Ca(t),N.some(!0)};return{uid:t.uid,dom:t.dom,components:n,behaviours:oh(t.dropdownBehaviours,[pw.config({toggleClass:t.toggleClass,aria:{mode:"expanded"}}),TB.config({others:{sandbox:function(n){return LB(t,n,{onOpen:function(){return pw.on(n)},onClose:function(){return pw.off(n)}})}}}),Dx.config({mode:"special",onSpace:s,onEnter:s,onDown:function(t,n){if(XB.isOpen(t)){var e=TB.getCoupled(t,"sandbox");u(e)}else XB.open(t);return N.some(!0)},onEscape:function(t,n){return XB.isOpen(t)?(XB.close(t),N.some(!0)):N.none()}}),Jx.config({})]),events:vw(N.some(a)),eventOrder:D(D({},t.eventOrder),(r={},r[na()]=["disabling","toggling","alloy.base.behaviour"],r)),apis:c,domModification:{attributes:D(D({"aria-haspopup":"true"},t.role.fold((function(){return{}}),(function(t){return{role:t}}))),"button"===t.dom.tag?{type:i("type").getOr("button")}:{})}}},XB=mv({name:"Dropdown",configFields:jB(),partFields:WB(),factory:GB,apis:{open:function(t,n){return t.open(n)},expand:function(t,n){return t.expand(n)},close:function(t,n){return t.close(n)},isOpen:function(t,n){return t.isOpen(n)},repositionMenus:function(t,n){return t.repositionMenus(n)}}}),YB=function(){return Ac({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})},qB=function(){return Ea([Da(qu(),T)])},KB=Object.freeze({__proto__:null,events:qB,exhibit:YB}),JB=pl({fields:[],name:"unselecting",active:KB}),$B=function(t,n){return XB.sketch({dom:t.dom,components:t.components,toggleClass:"mce-active",dropdownBehaviours:ml([ST.button(n.providers.isDisabled),vT(),JB.config({}),EA.config({})]),layouts:t.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:n.getSink,fetch:function(e){return sB.nu((function(n){return t.fetch(n)})).map((function(o){return N.from(zD(Sr(lD(rc("menu-value"),o,(function(n){t.onItemAction(e,n)}),t.columns,t.presets,QC.CLOSE_ON_EXECUTE,_,n.providers),{movement:mD(t.columns,t.presets)})))}))},parts:{menu:OO(!1,1,t.presets)}})},QB=rc("color-input-change"),ZB=rc("color-swatch-change"),tM=rc("color-picker-cancel"),nM=function(t,n,e){var o=CA.parts.field({factory:$A,inputClasses:["tox-textfield"],onSetValue:function(t){return wB.run(t).get(h)},inputBehaviours:ml([Pv.config({disabled:n.providers.isDisabled}),vT(),EA.config({}),wB.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(t){return ve(t.element)},notify:{onValid:function(t){var n=th.getValue(t);ka(t,QB,{color:n})}},validator:{validateOnLoad:!1,validate:function(t){var n=th.getValue(t);if(0===n.length)return sB.pure(rr.value(!0));var e=nn.fromTag("span");qe(e,"background-color",n);var o=Ze(e,"background-color").fold((function(){return rr.error("blah")}),(function(t){return rr.value(n)}));return sB.pure(o)}}})]),selectOnFocus:!1}),r=t.label.map((function(t){return RA(t,n.providers)})),i=function(t,n){ka(t,ZB,{value:n})},u=function(t,n){a.getOpt(t).each((function(t){"custom"===n?e.colorPicker((function(n){n.fold((function(){return Sa(t,tM)}),(function(n){i(t,n),qE(n)}))}),"#ffffff"):i(t,"remove"===n?"":n)}))},a=TS($B({dom:{tag:"span",attributes:{"aria-label":n.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[jf,Uf,Yf]},onLtr:function(){return[Uf,jf,Yf]}},components:[],fetch:iD(e.getColors(),e.hasCustomColors()),columns:e.getColorCols(),presets:"color",onItemAction:u},n));return CA.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[o,a.asSpec()]}]),fieldBehaviours:ml([zx("form-field-events",[Ma(QB,(function(n,e){a.getOpt(n).each((function(t){qe(t.element,"background-color",e.event.color)})),ka(n,NA,{name:t.name})})),Ma(ZB,(function(t,n){CA.getField(t).each((function(e){th.setValue(e,n.event.value),bv.getCurrent(t).each(Jx.focus)}))})),Ma(tM,(function(t,n){CA.getField(t).each((function(n){bv.getCurrent(t).each(Jx.focus)}))}))])])})},eM=Rh({schema:[Di("dom")],name:"label"}),oM=function(t){return Rh({name:t+"-edge",overrides:function(n){var e=n.model.manager.edgeActions[t];return e.fold((function(){return{}}),(function(t){return{events:Ea([Fa(Du(),(function(n,e,o){return t(n,o)}),[n]),Fa(Fu(),(function(n,e,o){return t(n,o)}),[n]),Fa(Iu(),(function(n,e,o){o.mouseIsDown.get()&&t(n,o)}),[n])])}}))}})},rM=oM("top-left"),iM=oM("top"),uM=oM("top-right"),aM=oM("right"),cM=oM("bottom-right"),sM=oM("bottom"),fM=oM("bottom-left"),lM=oM("left"),dM=Fh({name:"thumb",defaults:x({dom:{styles:{position:"absolute"}}}),overrides:function(t){return{events:Ea([Pa(Du(),t,"spectrum"),Pa(Au(),t,"spectrum"),Pa(Bu(),t,"spectrum"),Pa(Fu(),t,"spectrum"),Pa(Iu(),t,"spectrum"),Pa(Nu(),t,"spectrum")])}}}),mM=Fh({schema:[Ti("mouseIsDown",(function(){return ru(!1)}))],name:"spectrum",overrides:function(t){var n=t.model,e=n.manager,o=function(n,o){return e.getValueFromEvent(o).map((function(o){return e.setValueFrom(n,t,o)}))};return{behaviours:ml([Dx.config({mode:"special",onLeft:function(n){return e.onLeft(n,t)},onRight:function(n){return e.onRight(n,t)},onUp:function(n){return e.onUp(n,t)},onDown:function(n){return e.onDown(n,t)}}),Jx.config({})]),events:Ea([Ma(Du(),o),Ma(Au(),o),Ma(Fu(),o),Ma(Iu(),(function(n,e){t.mouseIsDown.get()&&o(n,e)}))])}}}),gM=[eM,lM,aM,iM,sM,rM,uM,fM,cM,dM,mM],pM="slider.change.value",hM=x(pM),vM=function(t){return-1!==t.type.indexOf("touch")},bM=function(t){var n=t.event.raw;if(vM(n)){var e=n;return void 0!==e.touches&&1===e.touches.length?N.some(e.touches[0]).map((function(t){return fo(t.clientX,t.clientY)})):N.none()}var o=n;return void 0!==o.clientX?N.some(o).map((function(t){return fo(t.clientX,t.clientY)})):N.none()},yM="top",xM="right",wM="bottom",SM="left",kM=function(t){return t.model.minX},CM=function(t){return t.model.minY},OM=function(t){return t.model.minX-1},_M=function(t){return t.model.minY-1},TM=function(t){return t.model.maxX},EM=function(t){return t.model.maxY},DM=function(t){return t.model.maxX+1},AM=function(t){return t.model.maxY+1},BM=function(t,n,e){return n(t)-e(t)},MM=function(t){return BM(t,TM,kM)},FM=function(t){return BM(t,EM,CM)},IM=function(t){return MM(t)/2},RM=function(t){return FM(t)/2},NM=function(t){return t.stepSize},PM=function(t){return t.snapToGrid},HM=function(t){return t.snapStart},VM=function(t){return t.rounded},LM=function(t,n){return void 0!==t[n+"-edge"]},zM=function(t){return LM(t,SM)},UM=function(t){return LM(t,xM)},jM=function(t){return LM(t,yM)},WM=function(t){return LM(t,wM)},GM=function(t){return t.model.value.get()},XM=function(t){return{x:t}},YM=function(t){return{y:t}},qM=function(t,n){return{x:t,y:n}},KM=function(t,n){ka(t,hM(),{value:n})},JM=function(t,n){KM(t,qM(OM(n),_M(n)))},$M=function(t,n){KM(t,YM(_M(n)))},QM=function(t,n){KM(t,qM(IM(n),_M(n)))},ZM=function(t,n){KM(t,qM(DM(n),_M(n)))},tF=function(t,n){KM(t,XM(DM(n)))},nF=function(t,n){KM(t,qM(DM(n),RM(n)))},eF=function(t,n){KM(t,qM(DM(n),AM(n)))},oF=function(t,n){KM(t,YM(AM(n)))},rF=function(t,n){KM(t,qM(IM(n),AM(n)))},iF=function(t,n){KM(t,qM(OM(n),AM(n)))},uF=function(t,n){KM(t,XM(OM(n)))},aF=function(t,n){KM(t,qM(OM(n),RM(n)))},cF=function(t,n,e,o){return t<n?t:t>e?e:t===n?n-1:Math.max(n,t-o)},sF=function(t,n,e,o){return t>e?t:t<n?n:t===e?e+1:Math.min(e,t+o)},fF=function(t,n,e){return Math.max(n,Math.min(e,t))},lF=function(t,n,e,o,r){return r.fold((function(){var r=t-n,i=Math.round(r/o)*o;return fF(n+i,n-1,e+1)}),(function(n){var r=(t-n)%o,i=Math.round(r/o),u=Math.floor((t-n)/o),a=Math.floor((e-n)/o),c=Math.min(a,u+i),s=n+c*o;return Math.max(n,s)}))},dF=function(t,n,e){return Math.min(e,Math.max(t,n))-n},mF=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.step,u=t.snap,a=t.snapStart,c=t.rounded,s=t.hasMinEdge,f=t.hasMaxEdge,l=t.minBound,d=t.maxBound,m=t.screenRange,g=s?n-1:n,p=f?e+1:e;if(r<l)return g;if(r>d)return p;var h=dF(r,l,d),v=fF(h/m*o+n,g,p);return u&&v>=n&&v<=e?lF(v,n,e,i,a):c?Math.round(v):v},gF=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.hasMinEdge,u=t.hasMaxEdge,a=t.maxBound,c=t.maxOffset,s=t.centerMinEdge,f=t.centerMaxEdge;return r<n?i?0:s:r>e?u?a:f:(r-n)/o*c},pF="top",hF="right",vF="bottom",bF="left",yF="width",xF="height",wF=function(t){return t.element.dom.getBoundingClientRect()},SF=function(t,n){return t[n]},kF=function(t){var n=wF(t);return SF(n,bF)},CF=function(t){var n=wF(t);return SF(n,hF)},OF=function(t){var n=wF(t);return SF(n,pF)},_F=function(t){var n=wF(t);return SF(n,vF)},TF=function(t){var n=wF(t);return SF(n,yF)},EF=function(t){var n=wF(t);return SF(n,xF)},DF=function(t,n,e){return(t+n)/2-e},AF=function(t,n){var e=wF(t),o=wF(n),r=SF(e,bF),i=SF(e,hF),u=SF(o,bF);return DF(r,i,u)},BF=function(t,n){var e=wF(t),o=wF(n),r=SF(e,pF),i=SF(e,vF),u=SF(o,pF);return DF(r,i,u)},MF=function(t,n){ka(t,hM(),{value:n})},FF=function(t){return{x:t}},IF=function(t,n,e){var o={min:kM(n),max:TM(n),range:MM(n),value:e,step:NM(n),snap:PM(n),snapStart:HM(n),rounded:VM(n),hasMinEdge:zM(n),hasMaxEdge:UM(n),minBound:kF(t),maxBound:CF(t),screenRange:TF(t)};return mF(o)},RF=function(t,n,e){var o=IF(t,n,e),r=FF(o);return MF(t,r),o},NF=function(t,n){var e=kM(n);MF(t,FF(e))},PF=function(t,n){var e=TM(n);MF(t,FF(e))},HF=function(t,n,e){var o=t>0?sF:cF,r=o(GM(e).x,kM(e),TM(e),NM(e));return MF(n,FF(r)),N.some(r)},VF=function(t){return function(n,e){return HF(t,n,e).map(T)}},LF=function(t){var n=bM(t);return n.map((function(t){return t.left}))},zF=function(t,n,e,o,r){var i=0,u=TF(t),a=o.bind((function(n){return N.some(AF(n,t))})).getOr(i),c=r.bind((function(n){return N.some(AF(n,t))})).getOr(u),s={min:kM(n),max:TM(n),range:MM(n),value:e,hasMinEdge:zM(n),hasMaxEdge:UM(n),minBound:kF(t),minOffset:i,maxBound:CF(t),maxOffset:u,centerMinEdge:a,centerMaxEdge:c};return gF(s)},UF=function(t,n,e,o,r,i){var u=zF(n,i,e,o,r);return kF(n)-kF(t)+u},jF=function(t,n,e,o){var r=GM(e),i=UF(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=bo(n.element)/2;qe(n.element,"left",i-u+"px")},WF=VF(-1),GF=VF(1),XF=N.none,YF=N.none,qF={"top-left":N.none(),top:N.none(),"top-right":N.none(),right:N.some(tF),"bottom-right":N.none(),bottom:N.none(),"bottom-left":N.none(),left:N.some(uF)},KF=Object.freeze({__proto__:null,setValueFrom:RF,setToMin:NF,setToMax:PF,findValueOfOffset:IF,getValueFromEvent:LF,findPositionOfValue:UF,setPositionFromValue:jF,onLeft:WF,onRight:GF,onUp:XF,onDown:YF,edgeActions:qF}),JF=function(t,n){ka(t,hM(),{value:n})},$F=function(t){return{y:t}},QF=function(t,n,e){var o={min:CM(n),max:EM(n),range:FM(n),value:e,step:NM(n),snap:PM(n),snapStart:HM(n),rounded:VM(n),hasMinEdge:jM(n),hasMaxEdge:WM(n),minBound:OF(t),maxBound:_F(t),screenRange:EF(t)};return mF(o)},ZF=function(t,n,e){var o=QF(t,n,e),r=$F(o);return JF(t,r),o},tI=function(t,n){var e=CM(n);JF(t,$F(e))},nI=function(t,n){var e=EM(n);JF(t,$F(e))},eI=function(t,n,e){var o=t>0?sF:cF,r=o(GM(e).y,CM(e),EM(e),NM(e));return JF(n,$F(r)),N.some(r)},oI=function(t){return function(n,e){return eI(t,n,e).map(T)}},rI=function(t){var n=bM(t);return n.map((function(t){return t.top}))},iI=function(t,n,e,o,r){var i=0,u=EF(t),a=o.bind((function(n){return N.some(BF(n,t))})).getOr(i),c=r.bind((function(n){return N.some(BF(n,t))})).getOr(u),s={min:CM(n),max:EM(n),range:FM(n),value:e,hasMinEdge:jM(n),hasMaxEdge:WM(n),minBound:OF(t),minOffset:i,maxBound:_F(t),maxOffset:u,centerMinEdge:a,centerMaxEdge:c};return gF(s)},uI=function(t,n,e,o,r,i){var u=iI(n,i,e,o,r);return OF(n)-OF(t)+u},aI=function(t,n,e,o){var r=GM(e),i=uI(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),u=uo(n.element)/2;qe(n.element,"top",i-u+"px")},cI=N.none,sI=N.none,fI=oI(-1),lI=oI(1),dI={"top-left":N.none(),top:N.some($M),"top-right":N.none(),right:N.none(),"bottom-right":N.none(),bottom:N.some(oF),"bottom-left":N.none(),left:N.none()},mI=Object.freeze({__proto__:null,setValueFrom:ZF,setToMin:tI,setToMax:nI,findValueOfOffset:QF,getValueFromEvent:rI,findPositionOfValue:uI,setPositionFromValue:aI,onLeft:cI,onRight:sI,onUp:fI,onDown:lI,edgeActions:dI}),gI=function(t,n){ka(t,hM(),{value:n})},pI=function(t,n){return{x:t,y:n}},hI=function(t,n,e){var o=IF(t,n,e.left),r=QF(t,n,e.top),i=pI(o,r);return gI(t,i),i},vI=function(t,n,e,o){var r=t>0?sF:cF,i=n?GM(o).x:r(GM(o).x,kM(o),TM(o),NM(o)),u=n?r(GM(o).y,CM(o),EM(o),NM(o)):GM(o).y;return gI(e,pI(i,u)),N.some(i)},bI=function(t,n){return function(e,o){return vI(t,n,e,o).map(T)}},yI=function(t,n){var e=kM(n),o=CM(n);gI(t,pI(e,o))},xI=function(t,n){var e=TM(n),o=EM(n);gI(t,pI(e,o))},wI=function(t){return bM(t)},SI=function(t,n,e,o){var r=GM(e),i=UF(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=uI(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),a=bo(n.element)/2,c=uo(n.element)/2;qe(n.element,"left",i-a+"px"),qe(n.element,"top",u-c+"px")},kI=bI(-1,!1),CI=bI(1,!1),OI=bI(-1,!0),_I=bI(1,!0),TI={"top-left":N.some(JM),top:N.some(QM),"top-right":N.some(ZM),right:N.some(nF),"bottom-right":N.some(eF),bottom:N.some(rF),"bottom-left":N.some(iF),left:N.some(aF)},EI=Object.freeze({__proto__:null,setValueFrom:hI,setToMin:yI,setToMax:xI,getValueFromEvent:wI,setPositionFromValue:SI,onLeft:kI,onRight:CI,onUp:OI,onDown:_I,edgeActions:TI}),DI=[qi("stepSize",1),qi("onChange",h),qi("onChoose",h),qi("onInit",h),qi("onDragStart",h),qi("onDragEnd",h),qi("snapToGrid",!1),qi("rounded",!0),Li("snapStart"),Ai("model",Ci("mode",{x:[qi("minX",0),qi("maxX",100),Ti("value",(function(t){return ru(t.mode.minX)})),Di("getInitialValue"),df("manager",KF)],y:[qi("minY",0),qi("maxY",100),Ti("value",(function(t){return ru(t.mode.minY)})),Di("getInitialValue"),df("manager",mI)],xy:[qi("minX",0),qi("maxX",100),qi("minY",0),qi("maxY",100),Ti("value",(function(t){return ru({x:t.mode.minX,y:t.mode.minY})})),Di("getInitialValue"),df("manager",EI)]})),nh("sliderBehaviours",[Dx,th]),Ti("mouseIsDown",(function(){return ru(!1)}))],AI=function(t,n,e,o){var r,i=function(n){return Kh(n,t,"thumb")},u=function(n){return Kh(n,t,"spectrum")},a=function(n){return qh(n,t,"left-edge")},c=function(n){return qh(n,t,"right-edge")},s=function(n){return qh(n,t,"top-edge")},f=function(n){return qh(n,t,"bottom-edge")},l=t.model,d=l.manager,m=function(n,e){d.setPositionFromValue(n,e,t,{getLeftEdge:a,getRightEdge:c,getTopEdge:s,getBottomEdge:f,getSpectrum:u})},g=function(t,n){l.value.set(n);var e=i(t);m(t,e)},p=function(n,e){g(n,e);var o=i(n);return t.onChange(n,o,e),N.some(!0)},h=function(n){d.setToMin(n,t)},v=function(n){d.setToMax(n,t)},b=function(n){var e=function(){qh(n,t,"thumb").each((function(e){var o=l.value.get();t.onChoose(n,e,o)}))},o=t.mouseIsDown.get();t.mouseIsDown.set(!1),o&&e()},y=function(n,e){e.stop(),t.mouseIsDown.set(!0),t.onDragStart(n,i(n))},x=function(n,e){e.stop(),t.onDragEnd(n,i(n)),b(n)};return{uid:t.uid,dom:t.dom,components:n,behaviours:oh(t.sliderBehaviours,[Dx.config({mode:"special",focusIn:function(n){return qh(n,t,"spectrum").map(Dx.focusIn).map(T)}}),th.config({store:{mode:"manual",getValue:function(t){return l.value.get()}}}),yl.config({channels:(r={},r[wp()]={onReceive:b},r)})]),events:Ea([Ma(hM(),(function(t,n){p(t,n.event.value)})),Ua((function(n,e){var o=l.getInitialValue();l.value.set(o);var r=i(n);m(n,r);var a=u(n);t.onInit(n,r,a,l.value.get())})),Ma(Du(),y),Ma(Bu(),x),Ma(Fu(),y),Ma(Nu(),x)]),apis:{resetToMin:h,resetToMax:v,setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},BI=mv({name:"Slider",configFields:DI,partFields:gM,factory:AI,apis:{setValue:function(t,n,e){t.setValue(n,e)},resetToMin:function(t,n){t.resetToMin(n)},resetToMax:function(t,n){t.resetToMax(n)},refresh:function(t,n){t.refresh(n)}}}),MI=rc("rgb-hex-update"),FI=rc("slider-update"),II=rc("palette-update"),RI=function(t,n){var e=BI.parts.spectrum({dom:{tag:"div",classes:[n("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=BI.parts.thumb({dom:{tag:"div",classes:[n("hue-slider-thumb")],attributes:{role:"presentation"}}});return BI.sketch({dom:{tag:"div",classes:[n("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:x({y:0})},components:[e,o],sliderBehaviours:ml([Jx.config({})]),onChange:function(t,n,e){ka(t,FI,{value:e})}})},NI="form",PI=[nh("formBehaviours",[th])],HI=function(t){return"<alloy.field."+t+">"},VI=function(t){var n=function(){var t=[],n=function(n,e){return t.push(n),jh(NI,HI(n),e)};return{field:n,record:x(t)}}(),e=t(n),o=n.record(),r=X(o,(function(t){return Fh({name:t,pname:HI(t)})}));return uv(NI,PI,r,zI,e)},LI=function(t,n){return t.fold((function(){return rr.error(n)}),rr.value)},zI=function(t,n){return{uid:t.uid,dom:t.dom,components:n,behaviours:oh(t.formBehaviours,[th.config({store:{mode:"manual",getValue:function(n){var e=$h(n,t);return bt(e,(function(t,n){return t().bind((function(t){var e=bv.getCurrent(t);return LI(e,new Error("Cannot find a current component to extract the value from for form part '"+n+"': "+Za(t.element)))})).map(th.getValue)}))},setValue:function(n,e){vt(e,(function(e,o){qh(n,t,o).each((function(t){bv.getCurrent(t).each((function(t){th.setValue(t,e)}))}))}))}}})]),apis:{getField:function(n,e){return qh(n,t,e).bind(bv.getCurrent)}}}},UI={getField:kc((function(t,n,e){return t.getField(n,e)})),sketch:VI},jI=rc("valid-input"),WI=rc("invalid-input"),GI=rc("validating-input"),XI="colorcustom.rgb.",YI=function(t,n,e,o){var r=function(e,o){return wB.config({invalidClass:n("invalid"),notify:{onValidate:function(t){ka(t,GI,{type:e})},onValid:function(t){ka(t,jI,{type:e,value:th.getValue(t)})},onInvalid:function(t){ka(t,WI,{type:e,value:th.getValue(t)})}},validator:{validate:function(n){var e=th.getValue(n),r=o(e)?rr.value(!0):rr.error(t("aria.input.invalid"));return sB.pure(r)},validateOnLoad:!1}})},i=function(e,o,i,u,a){var c=t(XI+"range"),s=CA.parts.label({dom:{tag:"label",innerHtml:i,attributes:{"aria-label":u}}}),f=CA.parts.field({data:a,factory:$A,inputAttributes:D({type:"text"},"hex"===o?{"aria-live":"polite"}:{}),inputClasses:[n("textfield")],inputBehaviours:ml([r(o,e),EA.config({})]),onSetValue:function(t){if(wB.isInvalid(t)){var n=wB.run(t);n.get(h)}}}),l=[s,f],d="hex"!==o?[CA.parts["aria-descriptor"]({text:c})]:[],m=l.concat(d);return{dom:{tag:"div",attributes:{role:"presentation"}},components:m}},u=function(t,n){var e=fE(n);return UI.getField(t,"hex").each((function(n){Jx.isFocused(n)||th.setValue(t,{hex:e.value})})),e},a=function(t,n){var e=n.red,o=n.green,r=n.blue;th.setValue(t,{red:e,green:o,blue:r})},c=TS({dom:{tag:"div",classes:[n("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),s=function(t,n){c.getOpt(t).each((function(t){qe(t.element,"background-color","#"+n.value)}))},f=function(){var r={red:ru(N.some(255)),green:ru(N.some(255)),blue:ru(N.some(255)),hex:ru(N.some("ffffff"))},f=function(t,n){var e=yE(n);a(t,e),g(e)},l=function(t){return r[t].get()},d=function(t,n){r[t].set(n)},m=function(){return l("red").bind((function(t){return l("green").bind((function(n){return l("blue").map((function(e){return hE(t,n,e,1)}))}))}))},g=function(t){var n=t.red,e=t.green,o=t.blue;d("red",N.some(n)),d("green",N.some(e)),d("blue",N.some(o))},p=function(t,n){var e=n.event;"hex"!==e.type?d(e.type,N.none()):o(t)},h=function(t,n){e(t);var o=nE(n);d("hex",N.some(n));var r=yE(o);a(t,r),g(r),ka(t,MI,{hex:o}),s(t,o)},v=function(t,n,e){var o=parseInt(e,10);d(n,N.some(o)),m().each((function(n){var e=u(t,n);ka(t,MI,{hex:e}),s(t,e)}))},b=function(t){return"hex"===t.type},y=function(t,n){var e=n.event;b(e)?h(t,e.value):v(t,e.type,e.value)},x=function(n){return{label:t(XI+n+".label"),description:t(XI+n+".description")}},w=x("red"),S=x("green"),k=x("blue"),C=x("hex");return Sr(UI.sketch((function(e){return{dom:{tag:"form",classes:[n("rgb-form")],attributes:{"aria-label":t("aria.color.picker")}},components:[e.field("red",CA.sketch(i(vE,"red",w.label,w.description,255))),e.field("green",CA.sketch(i(vE,"green",S.label,S.description,255))),e.field("blue",CA.sketch(i(vE,"blue",k.label,k.description,255))),e.field("hex",CA.sketch(i(rE,"hex",C.label,C.description,"ffffff"))),c.asSpec()],formBehaviours:ml([wB.config({invalidClass:n("form-invalid")}),zx("rgb-form-events",[Ma(jI,y),Ma(WI,p),Ma(GI,p)])])}})),{apis:{updateHex:function(t,n){th.setValue(t,{hex:n.value}),f(t,n),s(t,n)}}})},l=dv({factory:f,name:"RgbForm",configFields:[],apis:{updateHex:function(t,n,e){t.updateHex(n,e)}},extraApis:{}});return l},qI=function(t,n){var e=BI.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[n("sv-palette-spectrum")]}}),o=BI.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[n("sv-palette-thumb")],innerHtml:"<div class="+n("sv-palette-inner-thumb")+' role="presentation"></div>'}}),r=function(t,n){var e=t.width,o=t.height,r=t.getContext("2d");if(null!==r){r.fillStyle=n,r.fillRect(0,0,e,o);var i=r.createLinearGradient(0,0,e,0);i.addColorStop(0,"rgba(255,255,255,1)"),i.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=i,r.fillRect(0,0,e,o);var u=r.createLinearGradient(0,0,0,o);u.addColorStop(0,"rgba(0,0,0,0)"),u.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=u,r.fillRect(0,0,e,o)}},i=function(t,n){var e=t.components()[0].element.dom,o=AE(n,100,100),i=bE(o);r(e,SE(i))},u=function(t,n){var e=BE(yE(n));BI.setValue(t,{x:e.saturation,y:100-e.value})},a=function(t){var i=x({x:0,y:0}),u=function(t,n,e){ka(t,II,{value:e})},a=function(t,n,e,o){r(e.element.dom,SE(kE))},c=ml([bv.config({find:N.some}),Jx.config({})]);return BI.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[n("sv-palette")]},model:{mode:"xy",getInitialValue:i},rounded:!1,components:[e,o],onChange:u,onInit:a,sliderBehaviours:c})},c=dv({factory:a,name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:function(t,n,e){i(n,e)},setThumb:function(t,n,e){u(n,e)}},extraApis:{}});return c},KI=function(t,n){var e=function(e){var o=YI(t,n,e.onValidHex,e.onInvalidHex),r=qI(t,n),i=function(t){return(100-t)/100*360},u=function(t){return 100-t/360*100},a={paletteRgba:ru(kE),paletteHue:ru(0)},c=TS(RI(t,n)),s=TS(r.sketch({})),f=TS(o.sketch({})),l=function(t,n,e){s.getOpt(t).each((function(t){r.setHue(t,e)}))},d=function(t,n){f.getOpt(t).each((function(t){o.updateHex(t,n)}))},m=function(t,n,e){c.getOpt(t).each((function(t){BI.setValue(t,{y:u(e)})}))},g=function(t,n){s.getOpt(t).each((function(t){r.setThumb(t,n)}))},p=function(t,n){var e=yE(t);a.paletteRgba.set(e),a.paletteHue.set(n)},h=function(t,n,e,o){p(n,e),Y(o,(function(o){o(t,n,e)}))},v=function(){var t=[d];return function(n,e){var o=e.event.value,r=a.paletteHue.get(),i=AE(r,o.x,100-o.y),u=FE(i);h(n,u,r,t)}},b=function(){var t=[l,d];return function(n,e){var o=i(e.event.value.y),r=a.paletteRgba.get(),u=BE(r),c=AE(o,u.saturation,u.value),s=FE(c);h(n,s,o,t)}},y=function(){var t=[l,m,g];return function(n,e){var o=e.event.hex,r=ME(o);h(n,o,r.hue,t)}};return{uid:e.uid,dom:e.dom,components:[s.asSpec(),c.asSpec(),f.asSpec()],behaviours:ml([zx("colour-picker-events",[Ma(MI,y()),Ma(II,v()),Ma(FI,b())]),bv.config({find:function(t){return f.getOpt(t)}}),Dx.config({mode:"acyclic"})])}},o=dv({name:"ColourPicker",configFields:[Di("dom"),qi("onValidHex",h),qi("onInvalidHex",h)],factory:e});return o},JI=function(){return bv.config({find:N.some})},$I=function(t){return bv.config({find:t.getOpt})},QI=function(t){return bv.config({find:function(n){return Se(n.element,t).bind((function(t){return n.getSystem().getByDom(t).toOptional()}))}})},ZI={self:JI,memento:$I,childAt:QI},tR={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},nR=function(t){return tR[t]},eR=function(t){return nR(t)},oR=function(t){var n=function(t){return"tox-"+t},e=KI(eR,n),o=function(t){ka(t,VA,{name:"hex-valid",value:!0})},r=function(t){ka(t,VA,{name:"hex-valid",value:!1})},i=TS(e.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:o,onInvalidHex:r}));return{dom:{tag:"div"},components:[i.asSpec()],behaviours:ml([th.config({store:{mode:"manual",getValue:function(t){var n=i.get(t),e=bv.getCurrent(n),o=e.bind((function(t){var n=th.getValue(t);return n.hex}));return o.map((function(t){return"#"+t})).getOr("")},setValue:function(t,n){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/,o=e.exec(n),r=i.get(t),u=bv.getCurrent(r);u.fold((function(){}),(function(t){th.setValue(t,{hex:N.from(o[1]).getOr("")}),UI.getField(t,"hex").each((function(t){Sa(t,Uu())}))}))}}}),ZI.self()])}},rR=tinymce.util.Tools.resolve("tinymce.Resource"),iR=function(t){return Tt(t,"init")},uR=function(t){var n=rd(),e=TS({dom:{tag:t.tag}}),o=rd();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:ml([zx("custom-editor-events",[Ua((function(r){e.getOpt(r).each((function(e){(iR(t)?t.init(e.element.dom):rR.load(t.scriptId,t.scriptUrl).then((function(n){return n(e.element.dom,t.settings)}))).then((function(t){o.on((function(n){t.setValue(n)})),o.clear(),n.set(t)}))}))}))]),th.config({store:{mode:"manual",getValue:function(){return n.get().fold((function(){return o.get().getOr("")}),(function(t){return t.getValue()}))},setValue:function(t,e){n.get().fold((function(){o.set(e)}),(function(t){return t.setValue(e)}))}}}),ZI.self()]),components:[e.asSpec()]}},aR=tinymce.util.Tools.resolve("tinymce.util.Tools"),cR=ti([qi("preprocess",w),qi("postprocess",w)]),sR=function(t,n){var e=Si("RepresentingConfigs.memento processors",cR,n);return th.config({store:{mode:"manual",getValue:function(n){var o=t.get(n),r=th.getValue(o);return e.postprocess(r)},setValue:function(n,o){var r=e.preprocess(o),i=t.get(n);th.setValue(i,r)}}})},fR=function(t,n,e){return th.config(Sr({store:{mode:"manual",getValue:n,setValue:e}},t.map((function(t){return{store:{initialValue:t}}})).getOr({})))},lR=function(t,n,e){return fR(t,(function(t){return n(t.element)}),(function(t,n){return e(t.element,n)}))},dR=function(t){return lR(t,ds,ms)},mR=function(t){return lR(t,Ya,qa)},gR=function(t){return th.config({store:{mode:"memory",initialValue:t}})},pR={memento:sR,withElement:lR,withComp:fR,domValue:dR,domHtml:mR,memory:gR},hR="jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp",vR=function(t,n){var e=aR.explode(n.getSetting("images_file_types",hR,"string")),o=function(t){return j(e,(function(n){return Wt(t.name.toLowerCase(),"."+n.toLowerCase())}))};return J(mt(t),o)},bR=function(t,n){var e=function(t,n){n.stop()},o=function(t){return function(n,e){Y(t,(function(t){t(n,e)}))}},r=function(t,n){if(!Pv.isDisabled(t)){var e=n.event.raw;u(t,e.dataTransfer.files)}},i=function(t,n){var e=n.event.raw.target;u(t,e.files)},u=function(e,o){th.setValue(e,vR(o,n)),ka(e,NA,{name:t.name})},a=TS({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:ml([zx("input-file-events",[Va(Wu()),Va(oa())])])}),c=function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:ml([pR.memory([]),ZI.self(),Pv.config({}),pw.config({toggleClass:"dragenter",toggleOnExecute:!1}),zx("dropzone-events",[Ma("dragenter",o([e,pw.toggle])),Ma("dragleave",o([e,pw.toggle])),Ma("dragover",e),Ma("drop",o([e,r])),Ma(ju(),i)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:n.translate("Drop an image here")}},kS.sketch({dom:{tag:"button",innerHtml:n.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[a.asSpec()],action:function(t){var n=a.get(t);n.element.dom.click()},buttonBehaviours:ml([EA.config({}),ST.button(n.isDisabled),vT()])})]}]}},s=t.label.map((function(t){return RA(t,n)})),f=CA.parts.field({factory:{sketch:c}});return AA(s,f,["tox-form__group--stretched"],[])},yR=function(t,n){return{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+t.columns+"col"]},components:X(t.items,n.interpreter)}},xR=rc("alloy-fake-before-tabstop"),wR=rc("alloy-fake-after-tabstop"),SR=function(t){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:t},behaviours:ml([Jx.config({ignore:!0}),EA.config({})])}},kR=function(t){return{dom:{tag:"div",classes:["tox-navobj"]},components:[SR([xR]),t,SR([wR])],behaviours:ml([ZI.childAt(1)])}},CR=function(t,n){ka(t,Lu(),{raw:{which:9,shiftKey:n}})},OR=function(t,n){var e=n.element;cs(e,xR)?CR(t,!0):cs(e,wR)&&CR(t,!1)},_R=function(t){return GD(t,["."+xR,"."+wR].join(","),_)},TR=!(ee().browser.isIE()||ee().browser.isEdge()),ER=function(t){var n=ru("");return{getValue:function(t){return n.get()},setValue:function(e,o){if(t)Le(e.element,"srcdoc",o);else{Le(e.element,"src","javascript:''");var r=e.element.dom.contentWindow.document;r.open(),r.write(o),r.close()}n.set(o)}}},DR=function(t,n){var e=TR&&t.sandboxed,o=D(D({},t.label.map((function(t){return{title:t}})).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),r=ER(e),i=t.label.map((function(t){return RA(t,n)})),u=function(t){return kR({uid:t.uid,dom:{tag:"iframe",attributes:o},behaviours:ml([EA.config({}),Jx.config({}),pR.withComp(N.none(),r.getValue,r.setValue)])})},a=CA.parts.field({factory:{sketch:u}});return AA(i,a,["tox-form__group--stretched"],[])},AR=function(t,n){return FR(document.createElement("canvas"),t,n)},BR=function(t){var n=AR(t.width,t.height),e=MR(n);return e.drawImage(t,0,0),n},MR=function(t){return t.getContext("2d")},FR=function(t,n,e){return t.width=n,t.height=e,t},IR=function(t){return t.naturalWidth||t.width},RR=function(t){return t.naturalHeight||t.height},NR=function(t){return new nB((function(n,e){var o=URL.createObjectURL(t),r=new Image,i=function(){r.removeEventListener("load",u),r.removeEventListener("error",a)},u=function(){i(),n(r)},a=function(){i(),e("Unable to load data of type "+t.type+": "+o)};r.addEventListener("load",u),r.addEventListener("error",a),r.src=o,r.complete&&setTimeout(u,0)}))},PR=function(t){var n=t.split(","),e=/data:([^;]+)/.exec(n[0]);if(!e)return N.none();for(var o=e[1],r=n[1],i=1024,u=atob(r),a=u.length,c=Math.ceil(a/i),s=new Array(c),f=0;f<c;++f){for(var l=f*i,d=Math.min(l+i,a),m=new Array(d-l),g=l,p=0;g<d;++p,++g)m[p]=u[g].charCodeAt(0);s[f]=new Uint8Array(m)}return N.some(new Blob(s,{type:o}))},HR=function(t){return new nB((function(n,e){PR(t).fold((function(){e("uri is not base64: "+t)}),n)}))},VR=function(t,n,e){return n=n||"image/png",m(HTMLCanvasElement.prototype.toBlob)?new nB((function(o,r){t.toBlob((function(t){t?o(t):r()}),n,e)})):HR(t.toDataURL(n,e))},LR=function(t,n,e){return n=n||"image/png",t.toDataURL(n,e)},zR=function(t){return NR(t).then((function(t){jR(t);var n=AR(IR(t),RR(t)),e=MR(n);return e.drawImage(t,0,0),n}))},UR=function(t){return new nB((function(n){var e=new FileReader;e.onloadend=function(){n(e.result)},e.readAsDataURL(t)}))},jR=function(t){URL.revokeObjectURL(t.src)},WR=function(t,n,e){var o=n.type,r=x(o),i=function(){return nB.resolve(n)},u=x(e),a=function(){return e.split(",")[1]},c=function(n,e){return t.then((function(t){return VR(t,n,e)}))},s=function(n,e){return t.then((function(t){return LR(t,n,e)}))},f=function(t,n){return s(t,n).then((function(t){return t.split(",")[1]}))},l=function(){return t.then(BR)};return{getType:r,toBlob:i,toDataURL:u,toBase64:a,toAdjustedBlob:c,toAdjustedDataURL:s,toAdjustedBase64:f,toCanvas:l}},GR=function(t){return UR(t).then((function(n){return WR(zR(t),t,n)}))},XR=function(t,n){return VR(t,n).then((function(n){return WR(nB.resolve(t),n,t.toDataURL())}))},YR=function(t){return GR(t)},qR=function(t,n,e){var o="string"===typeof t?parseFloat(t):t;return o>e?o=e:o<n&&(o=n),o},KR=function(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]},JR=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10],$R=function(t,n){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=n[u+5*i];for(u=0;u<5;u++){e=0;for(var a=0;a<5;a++)e+=t[u+5*a]*o[a];r[u+5*i]=e}}return r},QR=function(t,n){var e;return n=qR(n,-1,1),n*=100,n<0?e=127+n/100*127:(e=n%1,e=0===e?JR[n]:JR[Math.floor(n)]*(1-e)+JR[Math.floor(n)+1]*e,e=127*e+127),$R(t,[e/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])},ZR=function(t,n){return n=qR(255*n,-255,255),$R(t,[1,0,0,0,n,0,1,0,0,n,0,0,1,0,n,0,0,0,1,0,0,0,0,0,1])},tN=function(t,n,e,o){return n=qR(n,0,2),e=qR(e,0,2),o=qR(o,0,2),$R(t,[n,0,0,0,0,0,e,0,0,0,0,0,o,0,0,0,0,0,1,0,0,0,0,0,1])},nN=function(t,n){return t.toCanvas().then((function(e){return eN(e,t.getType(),n)}))},eN=function(t,n,e){var o=MR(t),r=function(t,n){for(var e,o,r,i,u=t.data,a=n[0],c=n[1],s=n[2],f=n[3],l=n[4],d=n[5],m=n[6],g=n[7],p=n[8],h=n[9],v=n[10],b=n[11],y=n[12],x=n[13],w=n[14],S=n[15],k=n[16],C=n[17],O=n[18],_=n[19],T=0;T<u.length;T+=4)e=u[T],o=u[T+1],r=u[T+2],i=u[T+3],u[T]=e*a+o*c+r*s+i*f+l,u[T+1]=e*d+o*m+r*g+i*p+h,u[T+2]=e*v+o*b+r*y+i*x+w,u[T+3]=e*S+o*k+r*C+i*O+_;return t},i=r(o.getImageData(0,0,t.width,t.height),e);return o.putImageData(i,0,0),XR(t,n)},oN=function(t,n){return t.toCanvas().then((function(e){return rN(e,t.getType(),n)}))},rN=function(t,n,e){var o=MR(t),r=function(t,n,e){for(var o=function(t,n,e){return t>e?t=e:t<n&&(t=n),t},r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=t.data,a=n.data,c=t.width,s=t.height,f=0;f<s;f++)for(var l=0;l<c;l++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(l+h-i,0,c-1),b=o(f+p-i,0,s-1),y=4*(b*c+v),x=e[p*r+h];d+=u[y]*x,m+=u[y+1]*x,g+=u[y+2]*x}var w=4*(f*c+l);a[w]=o(d,0,255),a[w+1]=o(m,0,255),a[w+2]=o(g,0,255)}return n},i=o.getImageData(0,0,t.width,t.height),u=o.getImageData(0,0,t.width,t.height);return u=r(i,u,e),o.putImageData(u,0,0),XR(t,n)},iN=function(t){var n=function(n,e,o){for(var r=MR(n),i=new Array(256),u=function(t,n){for(var e=t.data,o=0;o<e.length;o+=4)e[o]=n[e[o]],e[o+1]=n[e[o+1]],e[o+2]=n[e[o+2]];return t},a=0;a<i.length;a++)i[a]=t(a,o);var c=u(r.getImageData(0,0,n.width,n.height),i);return r.putImageData(c,0,0),XR(n,e)};return function(t,e){return t.toCanvas().then((function(o){return n(o,t.getType(),e)}))}},uN=function(t){return function(n,e){return nN(n,t(KR(),e))}},aN=function(t){return function(n){return nN(n,t)}},cN=function(t){return function(n){return oN(n,t)}},sN=aN([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1]),fN=uN(ZR),lN=uN(QR),dN=function(t,n,e,o){return nN(t,tN(KR(),n,e,o))},mN=cN([0,-1,0,-1,5,-1,0,-1,0]),gN=iN((function(t,n){return 255*Math.pow(t/255,1-n)})),pN=function(t,n,e){var o=IR(t),r=RR(t),i=n/o,u=e/r,a=!1;(i<.5||i>2)&&(i=i<.5?.5:2,a=!0),(u<.5||u>2)&&(u=u<.5?.5:2,a=!0);var c=hN(t,i,u);return a?c.then((function(t){return pN(t,n,e)})):c},hN=function(t,n,e){return new nB((function(o){var r=IR(t),i=RR(t),u=Math.floor(r*n),a=Math.floor(i*e),c=AR(u,a),s=MR(c);s.drawImage(t,0,0,r,i,0,0,u,a),o(c)}))},vN=function(t,n){void 0===n&&(n=2);var e=Math.pow(10,n),o=Math.round(t*e);return Math.ceil(o/e)},bN=function(t,n){return t.toCanvas().then((function(e){return yN(e,t.getType(),n)}))},yN=function(t,n,e){var o=e<0?360+e:e,r=o*Math.PI/180,i=t.width,u=t.height,a=Math.sin(r),c=Math.cos(r),s=vN(Math.abs(i*c)+Math.abs(u*a)),f=vN(Math.abs(i*a)+Math.abs(u*c)),l=AR(s,f),d=MR(l);return d.translate(s/2,f/2),d.rotate(r),d.drawImage(t,-i/2,-u/2),XR(l,n)},xN=function(t,n){return t.toCanvas().then((function(e){return wN(e,t.getType(),n)}))},wN=function(t,n,e){var o=AR(t.width,t.height),r=MR(o);return"v"===e?(r.scale(1,-1),r.drawImage(t,0,-o.height)):(r.scale(-1,1),r.drawImage(t,-o.width,0)),XR(o,n)},SN=function(t,n,e,o,r){return t.toCanvas().then((function(i){return kN(i,t.getType(),n,e,o,r)}))},kN=function(t,n,e,o,r,i){var u=AR(r,i),a=MR(u);return a.drawImage(t,-e,-o),XR(u,n)},CN=function(t,n,e){return t.toCanvas().then((function(o){return pN(o,n,e).then((function(n){return XR(n,t.getType())}))}))},ON=function(t){return sN(t)},_N=function(t){return mN(t)},TN=function(t,n){return gN(t,n)},EN=function(t,n,e,o){return dN(t,n,e,o)},DN=function(t,n){return fN(t,n)},AN=function(t,n){return lN(t,n)},BN=function(t,n){return xN(t,n)},MN=function(t,n,e,o,r){return SN(t,n,e,o,r)},FN=function(t,n,e){return CN(t,n,e)},IN=function(t,n){return bN(t,n)},RN=function(t,n,e){return Gk(t,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:e},n)},NN=function(t,n){return RN(t,n,[])},PN=function(t,n){return RN(t,n,[Vx.config({})])},HN=function(t,n,e){return{dom:{tag:"span",innerHtml:e.translate(t),classes:[n+"__select-label"]},behaviours:ml([Vx.config({})])}},VN=rc("toolbar.button.execute"),LN=function(t){return Ga((function(n,e){kT(t,n)((function(e){ka(n,VN,{buttonApi:e}),t.onAction(e)}))}))},zN=(AB={},AB[na()]=["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],AB),UN=rc("update-menu-text"),jN=rc("update-menu-icon"),WN=function(t,n,e){var o=ru(h),r=t.text.map((function(t){return TS(HN(t,n,e.providers))})),i=t.icon.map((function(t){return TS(PN(t,e.providers.icons))})),u=function(t,n){var e=th.getValue(t);return Jx.focus(e),ka(e,"keydown",{raw:n.event.raw}),XB.close(e),N.some(!0)},a=t.role.fold((function(){return{}}),(function(t){return{role:t}})),c=t.tooltip.fold((function(){return{}}),(function(t){var n=e.providers.translate(t);return{title:n,"aria-label":n}})),s=Gk("chevron-down",{tag:"div",classes:[n+"__select-chevron"]},e.providers.icons),f=TS(XB.sketch(D(D(D({},t.uid?{uid:t.uid}:{}),a),{dom:{tag:"button",classes:[n,n+"--select"].concat(X(t.classes,(function(t){return n+"--"+t}))),attributes:D({},c)},components:ET([i.map((function(t){return t.asSpec()})),r.map((function(t){return t.asSpec()})),N.some(s)]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:ml(B(B([],t.dropdownBehaviours,!0),[ST.button((function(){return t.disabled||e.providers.isDisabled()})),vT(),JB.config({}),Vx.config({}),zx("dropdown-events",[CT(t,o),OT(t,o)]),zx("menubutton-update-display-text",[Ma(UN,(function(t,n){r.bind((function(n){return n.getOpt(t)})).each((function(t){Vx.set(t,[ks(e.providers.translate(n.event.text))])}))})),Ma(jN,(function(t,n){i.bind((function(n){return n.getOpt(t)})).each((function(t){Vx.set(t,[PN(n.event.icon,e.providers.icons)])}))}))])],!1)),eventOrder:Sr(zN,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:ml([Dx.config({mode:"special",onLeft:u,onRight:u})]),lazySink:e.getSink,toggleClass:n+"--active",parts:{menu:OO(!1,t.columns,t.presets)},fetch:function(n){return sB.nu(k(t.fetch,n))}})));return f.asSpec()},GN=function(t){return i(t)},XN=function(t){return"separator"===t.type},YN=function(t){return Tt(t,"getSubmenuItems")},qN={type:"separator"},KN=function(t,n){var e=Q(t,(function(t,e){return GN(e)?""===e?t:"|"===e?t.length>0&&!XN(t[t.length-1])?t.concat([qN]):t:Tt(n,e.toLowerCase())?t.concat([n[e.toLowerCase()]]):t:t.concat([e])}),[]);return e.length>0&&XN(e[e.length-1])&&e.pop(),e},JN=function(t,n){var e=t.getSubmenuItems(),o=ZN(e,n),r=Sr(o.menus,mu(t.value,o.items)),i=Sr(o.expansions,mu(t.value,t.value));return{item:t,menus:r,expansions:i}},$N=function(t,n){return YN(t)?JN(t,n):{item:t,menus:{},expansions:{}}},QN=function(t){if(XN(t))return t;var n=_t(t,"value").getOrThunk((function(){return rc("generated-menu-item")}));return Sr({value:n},t)},ZN=function(t,n){var e=KN(i(t)?t.split(" "):t,n);return $(e,(function(t,e){var o=QN(e),r=$N(o,n);return{menus:Sr(t.menus,r.menus),items:[r.item].concat(t.items),expansions:Sr(t.expansions,r.expansions)}}),{menus:{},expansions:{},items:[]})},tP=function(t,n,e,o){var r=rc("primary-menu"),i=ZN(t,e.shared.providers.menuItems());if(0===i.items.length)return N.none();var u=LD(r,i.items,n,e,o),a=bt(i.menus,(function(t,o){return LD(o,t,n,e,!1)})),c=Sr(a,mu(r,u));return N.from(Qw.tieredData(r,c,i.expansions))},nP=function(t){return{isDisabled:function(){return Pv.isDisabled(t)},setDisabled:function(n){return Pv.set(t,n)},setActive:function(n){var e=t.element;n?(is(e,"tox-tbtn--enabled"),Le(e,"aria-pressed",!0)):(as(e,"tox-tbtn--enabled"),Ge(e,"aria-pressed"))},isActive:function(){return cs(t.element,"tox-tbtn--enabled")}}},eP=function(t,n,e,o){return WN({text:t.text,icon:t.icon,tooltip:t.tooltip,role:o,fetch:function(n,o){t.fetch((function(t){o(tP(t,QC.CLOSE_ON_EXECUTE,e,!1))}))},onSetup:t.onSetup,getApi:nP,columns:1,presets:"normal",classes:[],dropdownBehaviours:[EA.config({})]},n,e.shared)},oP=function(t,n,e){var o=function(t){return function(o){var r=!o.isActive();o.setActive(r),t.storage.set(r),e.shared.getSink().each((function(e){n().getOpt(e).each((function(n){kl(n.element),ka(n,VA,{name:t.name,value:t.storage.get()})}))}))}},r=function(t){return function(n){n.setActive(t.storage.get())}};return function(n){n(X(t,(function(t){var n=t.text.fold((function(){return{}}),(function(t){return{text:t}}));return D(D({type:t.type,active:!1},n),{onAction:o(t),onSetup:r(t)})})))}},rP=function(t,n,e,o,r,i){void 0===e&&(e=[]);var u=n.fold((function(){return{}}),(function(t){return{action:t}})),a=D({buttonBehaviours:ml([ST.button((function(){return t.disabled||i.isDisabled()})),vT(),EA.config({}),zx("button press",[Ba("click"),Ba("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},u),c=Sr(a,{dom:o});return Sr(c,{components:r})},iP=function(t,n,e,o){void 0===o&&(o=[]);var r=t.tooltip.map((function(t){return{"aria-label":e.translate(t),title:e.translate(t)}})).getOr({}),i={tag:"button",classes:["tox-tbtn"],attributes:r},u=t.icon.map((function(t){return NN(t,e.icons)})),a=ET([u]);return rP(t,n,o,i,a,e)},uP=function(t,n,e,o){void 0===o&&(o=[]);var r=iP(t,N.some(n),e,o);return kS.sketch(r)},aP=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(t.text),u=t.icon?t.icon.map((function(t){return NN(t,e.icons)})):N.none(),a=u.isSome()?ET([u]):[],c=u.isSome()?{}:{innerHtml:i},s=B(B(B(B([],t.primary||t.borderless?["tox-button"]:["tox-button","tox-button--secondary"],!0),u.isSome()?["tox-button--icon"]:[],!0),t.borderless?["tox-button--naked"]:[],!0),r,!0),f=D(D({tag:"button",classes:s},c),{attributes:{title:i}});return rP(t,n,o,f,a,e)},cP=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=aP(t,N.some(n),e,o,r);return kS.sketch(i)},sP=function(t,n){return function(e){"custom"===n?ka(e,VA,{name:t,value:{}}):"submit"===n?Sa(e,LA):"cancel"===n&&Sa(e,HA)}},fP=function(t,n){return"menu"===n},lP=function(t,n){return"custom"===n||"cancel"===n||"submit"===n},dP=function(t,n,e){if(fP(t,n)){var o=function(){return u},r=t,i=D(D({},t),{onSetup:function(n){return n.setDisabled(t.disabled),h},fetch:oP(r.items,o,e)}),u=TS(eP(i,"tox-tbtn",e,N.none()));return u.asSpec()}if(lP(t,n)){var a=sP(t.name,n),c=D(D({},t),{borderless:!1});return cP(c,a,e.shared.providers,[])}},mP=function(t,n){var e=sP(t.name,"custom");return BA(N.none(),CA.parts.field(D({factory:kS},aP(t,N.some(e),n,[pR.memory(""),ZI.self()]))))},gP=x([qi("field1Name","field1"),qi("field2Name","field2"),ff("onLockedChange"),uf(["lockClass"]),qi("locked",!1),rh.field("coupledFieldBehaviours",[bv,th])]),pP=function(t,n,e){return qh(t,n,e).bind(bv.getCurrent)},hP=function(t,n){return Fh({factory:CA,name:t,overrides:function(t){return{fieldBehaviours:ml([zx("coupled-input-behaviour",[Ma(Uu(),(function(e){pP(e,t,n).each((function(n){qh(e,t,"lock").each((function(o){pw.isOn(o)&&t.onLockedChange(e,n,o)}))}))}))])])}}})},vP=x([hP("field1","field2"),hP("field2","field1"),Fh({factory:kS,schema:[Di("dom")],name:"lock",overrides:function(t){return{buttonBehaviours:ml([pw.config({selected:t.locked,toggleClass:t.markers.lockClass,aria:{mode:"pressed"}})])}}})]),bP=function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,behaviours:rh.augment(t.coupledFieldBehaviours,[bv.config({find:N.some}),th.config({store:{mode:"manual",getValue:function(n){var e,o=Zh(n,t,["field1","field2"]);return e={},e[t.field1Name]=th.getValue(o.field1()),e[t.field2Name]=th.getValue(o.field2()),e},setValue:function(n,e){var o=Zh(n,t,["field1","field2"]);Et(e,t.field1Name)&&th.setValue(o.field1(),e[t.field1Name]),Et(e,t.field2Name)&&th.setValue(o.field2(),e[t.field2Name])}}})]),apis:{getField1:function(n){return qh(n,t,"field1")},getField2:function(n){return qh(n,t,"field2")},getLock:function(n){return qh(n,t,"lock")}}}},yP=mv({name:"FormCoupledInputs",configFields:gP(),partFields:vP(),factory:bP,apis:{getField1:function(t,n){return t.getField1(n)},getField2:function(t,n){return t.getField2(n)},getLock:function(t,n){return t.getLock(n)}}}),xP=function(t){var n={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4},e=function(t){return t in n?n[t]:1},o=t.value.toFixed(e(t.unit));return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+t.unit},wP=function(t){var n=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/,e=n.exec(t);if(null!==e){var o=parseFloat(e[1]),r=e[2];return rr.value({value:o,unit:r})}return rr.error(t)},SP=function(t,n){var e={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},o=function(t){return Tt(e,t)};return t.unit===n?N.some(t.value):o(t.unit)&&o(n)?e[t.unit]===e[n]?N.some(t.value):N.some(t.value/e[t.unit]*e[n]):N.none()},kP=function(t){return N.none()},CP=function(t,n){return function(e){return SP(e,n).map((function(e){return{value:e*t,unit:n}}))}},OP=function(t,n){var e=wP(t).toOptional(),o=wP(n).toOptional();return Ft(e,o,(function(t,n){return SP(t,n.unit).map((function(t){return n.value/t})).map((function(t){return CP(t,n.unit)})).getOr(kP)})).getOr(kP)},_P=function(t,n){var e=kP,o=rc("ratio-event"),r=function(t){return Gk(t,{tag:"span",classes:["tox-icon","tox-lock-icon__"+t]},n.icons)},i=yP.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:n.translate(t.label.getOr("Constrain proportions"))}},components:[r("lock"),r("unlock")],buttonBehaviours:ml([Pv.config({disabled:function(){return t.disabled||n.isDisabled()}}),vT(),EA.config({})])}),u=function(t){return{dom:{tag:"div",classes:["tox-form__group"]},components:t}},a=function(e){return CA.parts.field({factory:$A,inputClasses:["tox-textfield"],inputBehaviours:ml([Pv.config({disabled:function(){return t.disabled||n.isDisabled()}}),vT(),EA.config({}),zx("size-input-events",[Ma(Hu(),(function(t,n){ka(t,o,{isField1:e})})),Ma(ju(),(function(n,e){ka(n,NA,{name:t.name})}))])]),selectOnFocus:!1})},c=function(t){return{dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}}},s=yP.parts.field1(u([CA.parts.label(c("Width")),a(!0)])),f=yP.parts.field2(u([CA.parts.label(c("Height")),a(!1)]));return yP.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[s,f,u([c("&nbsp;"),i])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(t,n,o){wP(th.getValue(t)).each((function(t){e(t).each((function(t){th.setValue(n,xP(t))}))}))},coupledFieldBehaviours:ml([Pv.config({disabled:function(){return t.disabled||n.isDisabled()},onDisabled:function(t){yP.getField1(t).bind(CA.getField).each(Pv.disable),yP.getField2(t).bind(CA.getField).each(Pv.disable),yP.getLock(t).each(Pv.disable)},onEnabled:function(t){yP.getField1(t).bind(CA.getField).each(Pv.enable),yP.getField2(t).bind(CA.getField).each(Pv.enable),yP.getLock(t).each(Pv.enable)}}),vT(),zx("size-input-events2",[Ma(o,(function(t,n){var o=n.event.isField1,r=o?yP.getField1(t):yP.getField2(t),i=o?yP.getField2(t):yP.getField1(t),u=r.map(th.getValue).getOr(""),a=i.map(th.getValue).getOr("");e=OP(u,a)}))])])})},TP=x(rc("undo")),EP=x(rc("redo")),DP=x(rc("zoom")),AP=x(rc("back")),BP=x(rc("apply")),MP=x(rc("swap")),FP=x(rc("transform")),IP=x(rc("temp-transform")),RP=x(rc("transform-apply")),NP={undo:TP,redo:EP,zoom:DP,back:AP,apply:BP,swap:MP,transform:FP,tempTransform:IP,transformApply:RP},PP=x("save-state"),HP=x("disable"),VP=x("enable"),LP={formActionEvent:VA,saveState:PP,disable:HP,enable:VP},zP=function(t,n){var e=function(t,e,o,r){return TS(cP({name:t,text:t,disabled:o,primary:r,icon:N.none(),borderless:!1},e,n))},o=function(t,e,o,r){return TS(uP({name:t,icon:N.some(t),tooltip:N.some(e),disabled:r,primary:!1,borderless:!1},o,n))},r=function(t,n){t.map((function(t){var e=t.get(n);e.hasConfigured(Pv)&&Pv.disable(e)}))},i=function(t,n){t.map((function(t){var e=t.get(n);e.hasConfigured(Pv)&&Pv.enable(e)}))},u={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},a=h,c=function(t,n,e){ka(t,n,e)},s=function(t){return Sa(t,LP.disable())},f=function(t){return Sa(t,LP.enable())},l=function(t,n){s(t),c(t,NP.transform(),{transform:n}),f(t)},d=function(t,n){s(t),c(t,NP.tempTransform(),{transform:n}),f(t)},m=function(t){return function(){ut.getOpt(t).each((function(t){Vx.set(t,[rt])}))}},g=function(t,n){s(t),c(t,NP.transformApply(),{transform:n,swap:m(t)}),f(t)},p=function(){return e("Back",(function(t){return c(t,NP.back(),{swap:m(t)})}),!1,!1)},v=function(){return TS({dom:{tag:"div",classes:["tox-spacer"]},behaviours:ml([Pv.config({})])})},b=function(){return e("Apply",(function(t){return c(t,NP.apply(),{swap:m(t)})}),!0,!0)},y=function(){return function(n){var e=t.getRect();return MN(n,e.x,e.y,e.w,e.h)}},w=[p(),v(),e("Apply",(function(n){var e=y();g(n,e),t.hideCrop()}),!1,!0)],S=bA.sketch({dom:u,components:w.map((function(t){return t.asSpec()})),containerBehaviours:ml([zx("image-tools-crop-buttons-events",[Ma(LP.disable(),(function(t,n){r(w,t)})),Ma(LP.enable(),(function(t,n){i(w,t)}))])])}),k=TS(_P({name:"size",label:N.none(),constrain:!0,disabled:!1},n)),C=function(t,n){return function(e){return FN(e,t,n)}},O=[p(),v(),k,v(),e("Apply",(function(t){k.getOpt(t).each((function(n){var e=th.getValue(n),o=parseInt(e.width,10),r=parseInt(e.height,10),i=C(o,r);g(t,i)}))}),!1,!0)],_=bA.sketch({dom:u,components:O.map((function(t){return t.asSpec()})),containerBehaviours:ml([zx("image-tools-resize-buttons-events",[Ma(LP.disable(),(function(t,n){r(O,t)})),Ma(LP.enable(),(function(t,n){i(O,t)}))])])}),T=function(t,n){return function(e){return t(e,n)}},E=T(BN,"h"),D=T(BN,"v"),A=T(IN,-90),B=T(IN,90),M=function(t,n){d(t,n)},F=[p(),v(),o("flip-horizontally","Flip horizontally",(function(t){M(t,E)}),!1),o("flip-vertically","Flip vertically",(function(t){M(t,D)}),!1),o("rotate-left","Rotate counterclockwise",(function(t){M(t,A)}),!1),o("rotate-right","Rotate clockwise",(function(t){M(t,B)}),!1),v(),b()],I=bA.sketch({dom:u,components:F.map((function(t){return t.asSpec()})),containerBehaviours:ml([zx("image-tools-fliprotate-buttons-events",[Ma(LP.disable(),(function(t,n){r(F,t)})),Ma(LP.enable(),(function(t,n){i(F,t)}))])])}),R=function(t,e,o,r,i){var u=BI.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}}),a=BI.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),c=BI.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return TS(BI.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:o,maxX:i,getInitialValue:x({x:r})},components:[u,a,c],sliderBehaviours:ml([Jx.config({})]),onChoose:e}))},P=function(t,n,e,o,r){var i=function(t,e,o){var r=T(n,o.x/100);l(t,r)};return R(t,i,e,o,r)},H=function(t,n,e,o,r){return[p(),P(t,n,e,o,r),b()]},V=function(t,n,e,o,a){var c=H(t,n,e,o,a);return bA.sketch({dom:u,components:c.map((function(t){return t.asSpec()})),containerBehaviours:ml([zx("image-tools-filter-panel-buttons-events",[Ma(LP.disable(),(function(t,n){r(c,t)})),Ma(LP.enable(),(function(t,n){i(c,t)}))])])})},L=[p(),v(),b()],z=bA.sketch({dom:u,components:L.map((function(t){return t.asSpec()}))}),U=V("Brightness",DN,-100,0,100),j=V("Contrast",AN,-100,0,100),W=V("Gamma",TN,-100,0,100),G=function(t,n,e){return function(o){return EN(o,t,n,e)}},X=function(t){var n=function(t,n,e){var o=Y.getOpt(t),r=K.getOpt(t),i=q.getOpt(t);o.each((function(n){r.each((function(e){i.each((function(o){var r=th.getValue(n).x/100,i=th.getValue(o).x/100,u=th.getValue(e).x/100,a=G(r,i,u);l(t,a)}))}))}))};return R(t,n,0,100,200)},Y=X("R"),q=X("G"),K=X("B"),J=[p(),Y,q,K,b()],$=bA.sketch({dom:u,components:J.map((function(t){return t.asSpec()}))}),Q=function(t,n,e){return function(o){var r=function(){ut.getOpt(o).each((function(n){Vx.set(n,[t]),e(n)}))};c(o,NP.swap(),{transform:n,swap:r})}},Z=function(n){t.showCrop()},tt=function(n){k.getOpt(n).each((function(n){var e=t.getMeasurements(),o=e.width,r=e.height;th.setValue(n,{width:o,height:r})}))},nt=N.some(_N),et=N.some(ON),ot=[o("crop","Crop",Q(S,N.none(),Z),!1),o("resize","Resize",Q(_,N.none(),tt),!1),o("orientation","Orientation",Q(I,N.none(),a),!1),o("brightness","Brightness",Q(U,N.none(),a),!1),o("sharpen","Sharpen",Q(z,nt,a),!1),o("contrast","Contrast",Q(j,N.none(),a),!1),o("color-levels","Color levels",Q($,N.none(),a),!1),o("gamma","Gamma",Q(W,N.none(),a),!1),o("invert","Invert",Q(z,et,a),!1)],rt=bA.sketch({dom:u,components:ot.map((function(t){return t.asSpec()}))}),it=bA.sketch({dom:{tag:"div"},components:[rt],containerBehaviours:ml([Vx.config({})])}),ut=TS(it),at=function(t){return ut.getOpt(t).map((function(t){var n=t.components()[0];return n.components()[n.components().length-1]}))};return{memContainer:ut,getApplyButton:at}},UP=tinymce.util.Tools.resolve("tinymce.geom.Rect"),jP=tinymce.util.Tools.resolve("tinymce.util.Observable"),WP=tinymce.util.Tools.resolve("tinymce.util.VK"),GP=function(t){var n=Math.max,e=t.documentElement,o=t.body,r=n(e.scrollWidth,o.scrollWidth),i=n(e.clientWidth,o.clientWidth),u=n(e.offsetWidth,o.offsetWidth),a=n(e.scrollHeight,o.scrollHeight),c=n(e.clientHeight,o.clientHeight),s=n(e.offsetHeight,o.offsetHeight);return{width:r<u?i:r,height:a<s?c:a}},XP=function(t){return d(t.changedTouches)},YP=function(t){if(XP(t))for(var n="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<n.length;e++)t[n[e]]=t.changedTouches[0][n[e]]};function qP(t,n){var e,o,r,i,u,a,c,s=[],f=[],l=null!==(e=n.document)&&void 0!==e?e:document,m=null!==(o=n.root)&&void 0!==o?o:l,g=nn.fromDom(l),p=nn.fromDom(m.getElementById(null!==(r=n.handle)&&void 0!==r?r:t)),h=function(t){var e=t.raw,o=GP(l);YP(e),t.prevent(),u=e.button,a=e.screenX,c=e.screenY;var r=$e(p,"cursor");i=nn.fromTag("div",l),Ke(i,{position:"absolute",top:"0",left:"0",width:o.width+"px",height:o.height+"px","z-index":"2147483647",opacity:"0.0001",cursor:r}),Bo(He(g),i),f.push(ud(g,"mousemove",v),ud(g,"touchmove",v),ud(g,"mouseup",b),ud(g,"touchend",b)),n.start(e)},v=function(t){var e=t.raw;if(YP(e),e.button!==u)return b(t);e.deltaX=e.screenX-a,e.deltaY=e.screenY-c,t.prevent(),n.drag(e)},b=function(t){YP(t.raw),Y(f,(function(t){return t.unbind()})),f=[],No(i),n.stop&&n.stop(t.raw)},y=function(){Y(f.concat(s),(function(t){return t.unbind()})),f=[],s=[],d(i)&&No(i)};return s.push(ud(p,"mousedown",h),ud(p,"touchstart",h)),{destroy:y}}var KP=0,JP=function(t,n,e,o,r){var i,u=[],a="tox-",c=a+"crid-"+KP++,s=nn.fromDom(o),f=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],l=["top","right","bottom","left"],d=function(t,n){return{x:n.x+t.x,y:n.y+t.y,w:n.w,h:n.h}},m=function(t,n){return{x:n.x-t.x,y:n.y-t.y,w:n.w,h:n.h}},g=function(){return m(e,t)},p=function(n,o,r,i){var u=o.x+r*n.deltaX,a=o.y+i*n.deltaY,c=Math.max(20,o.w+r*n.deltaW),s=Math.max(20,o.h+i*n.deltaH),f=t=UP.clamp({x:u,y:a,w:c,h:s},e,"move"===n.name);f=m(e,f),C.fire("updateRect",{rect:f}),w(f)},h=function(){var n=function(n){var e;return qP(c,{document:o.ownerDocument,root:De(s).dom,handle:c+"-"+n.name,start:function(){e=t},drag:function(t){p(n,e,t.deltaX,t.deltaY)}})},e=nn.fromTag("div");ze(e,{id:c,class:a+"croprect-container",role:"grid","aria-dropeffect":"execute"}),Bo(s,e),Y(l,(function(t){Hs(s,"#"+c).each((function(n){var e=nn.fromTag("div");ze(e,{id:c+"-"+t,class:a+"croprect-block","data-mce-bogus":"all"}),qe(e,"display","none"),Bo(n,e)}))})),Y(f,(function(t){Hs(s,"#"+c).each((function(n){var e=nn.fromTag("div");ze(e,{id:c+"-"+t.name,"aria-label":t.label,"aria-grabbed":"false","data-mce-bogus":"all",role:"gridcell",tabindex:"-1",title:t.label}),ss(e,[a+"croprect-handle",a+"croprect-handle-"+t.name]),qe(e,"display","none"),Bo(n,e)}))})),i=X(f,n),b(t);var d=function(t){Le(t.target,"aria-grabbed","focus"===t.raw.type?"true":"false")},m=function(n){var e;Y(f,(function(t){if(Ue(n.target,"id")===c+"-"+t.name)return e=t,!1}));var o=function(t,n,o,r,i){t.stopPropagation(),t.preventDefault(),p(e,o,r,i)};switch(n.raw.keyCode){case WP.LEFT:o(n,e,t,-10,0);break;case WP.RIGHT:o(n,e,t,10,0);break;case WP.UP:o(n,e,t,0,-10);break;case WP.DOWN:o(n,e,t,0,10);break;case WP.ENTER:case WP.SPACEBAR:n.prevent(),r();break}};u.push(ud(s,"focusin",d),ud(s,"focusout",d),ud(s,"keydown",m))},v=function(t){var n=B(B([],X(f,(function(t){return"#"+c+"-"+t.name})),!0),X(l,(function(t){return"#"+c+"-"+t})),!0).join(","),e=Km(s,n);Y(e,t?function(t){return eo(t,"display")}:function(t){return qe(t,"display","none")})},b=function(t){var e=function(t,n){Hs(s,"#"+c+"-"+t).each((function(t){Ke(t,{left:n.x+"px",top:n.y+"px",width:Math.max(0,n.w)+"px",height:Math.max(0,n.h)+"px"})}))};Y(f,(function(n){Hs(s,"#"+c+"-"+n.name).each((function(e){Ke(e,{left:t.w*n.xMul+t.x+"px",top:t.h*n.yMul+t.y+"px"})}))})),e("top",{x:n.x,y:n.y,w:n.w,h:t.y-n.y}),e("right",{x:t.x+t.w,y:t.y,w:n.w-t.x-t.w+n.x,h:t.h}),e("bottom",{x:n.x,y:t.y+t.h,w:n.w,h:n.h-t.y-t.h+n.y}),e("left",{x:n.x,y:t.y,w:t.x-n.x,h:t.h}),e("move",t)},y=function(n){t=n,b(t)},x=function(e){n=e,b(t)},w=function(t){y(d(e,t))},S=function(n){e=n,b(t)},k=function(){Y(i,(function(t){return t.destroy()})),i=[],Y(u,(function(t){return t.unbind()})),u=[]};h();var C=D(D({},jP),{toggleVisibility:v,setClampRect:S,setRect:y,getInnerRect:g,setInnerRect:w,setViewPortRect:x,destroy:k});return C},$P={create:JP},QP=function(t){return new pC((function(n){var e=function(){t.removeEventListener("load",e),n(t)};t.complete?n(t):t.addEventListener("load",e)}))},ZP=function(t){var n=TS({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),e=ru(1),o=od(),r=ru({x:0,y:0,w:1,h:1}),i=ru({x:0,y:0,w:1,h:1}),u=function(t,i){g.getOpt(t).each((function(t){var u=e.get(),a=bo(t.element),c=uo(t.element),s=i.dom.naturalWidth*u,f=i.dom.naturalHeight*u,l=Math.max(0,a/2-s/2),d=Math.max(0,c/2-f/2),m={left:l.toString()+"px",top:d.toString()+"px",width:s.toString()+"px",height:f.toString()+"px",position:"absolute"};Ke(i,m),n.getOpt(t).each((function(t){Ke(t.element,m)})),o.run((function(t){var n=r.get();t.setRect({x:n.x*u+l,y:n.y*u+d,w:n.w*u,h:n.h*u}),t.setClampRect({x:l,y:d,w:s,h:f}),t.setViewPortRect({x:0,y:0,w:a,h:c})}))}))},a=function(t,n){g.getOpt(t).each((function(t){var o=bo(t.element),r=uo(t.element),i=n.dom.naturalWidth,u=n.dom.naturalHeight,a=Math.min(o/i,r/u);a>=1?e.set(1):e.set(a)}))},c=function(t,n){var e=nn.fromTag("img");return Le(e,"src",n),QP(e.dom).then((function(){t.getSystem().isConnected()&&g.getOpt(t).map((function(t){var n=Cs({element:e});Vx.replaceAt(t,1,N.some(n));var o=i.get(),c={x:0,y:0,w:e.dom.naturalWidth,h:e.dom.naturalHeight};i.set(c);var s=UP.inflate(c,-20,-20);r.set(s),o.w===c.w&&o.h===c.h||a(t,e),u(t,e)}))}))},s=function(t,n){var o=e.get(),r=n>0?Math.min(2,o+.1):Math.max(.1,o-.1);e.set(r),g.getOpt(t).each((function(t){var n=t.components()[1].element;u(t,n)}))},f=function(){o.run((function(t){t.toggleVisibility(!0)}))},l=function(){o.run((function(t){t.toggleVisibility(!1)}))},d=function(){return r.get()},m=bA.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[n.asSpec(),{dom:{tag:"img",attributes:{src:t}}},{dom:{tag:"div"},behaviours:ml([zx("image-panel-crop-events",[Ua((function(t){g.getOpt(t).each((function(t){var n=t.element.dom,i=$P.create({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},n,h);i.toggleVisibility(!1),i.on("updateRect",(function(t){var n=t.rect,o=e.get(),i={x:Math.round(n.x/o),y:Math.round(n.y/o),w:Math.round(n.w/o),h:Math.round(n.h/o)};r.set(i)})),o.set(i)}))})),ja((function(){o.clear()}))])])}],containerBehaviours:ml([Vx.config({}),zx("image-panel-events",[Ua((function(n){c(n,t)}))])])}),g=TS(m),p=function(){var t=i.get();return{width:t.w,height:t.h}};return{memContainer:g,updateSrc:c,zoom:s,showCrop:f,hideCrop:l,getRect:d,getMeasurements:p}},tH=function(t,n,e,o,r){return uP({name:t,icon:N.some(n),disabled:e,tooltip:N.some(t),primary:!1,borderless:!1},o,r)},nH=function(t,n){n?Pv.enable(t):Pv.disable(t)},eH=function(t){var n=function(t,n,r){e.getOpt(t).each((function(t){nH(t,n)})),o.getOpt(t).each((function(t){nH(t,r)}))},e=TS(tH("Undo","undo",!0,(function(t){ka(t,NP.undo(),{direction:1})}),t)),o=TS(tH("Redo","redo",!0,(function(t){ka(t,NP.redo(),{direction:1})}),t)),r=bA.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[e.asSpec(),o.asSpec(),tH("Zoom in","zoom-in",!1,(function(t){ka(t,NP.zoom(),{direction:1})}),t),tH("Zoom out","zoom-out",!1,(function(t){ka(t,NP.zoom(),{direction:-1})}),t)]});return{container:r,updateButtonUndoStates:n}};function oH(){var t=[],n=-1,e=function(e){var o=t.splice(++n);return t.push(e),{state:e,removed:o}},o=function(){if(i())return t[--n]},r=function(){if(u())return t[++n]},i=function(){return n>0},u=function(){return-1!==n&&n<t.length-1};return{data:t,add:e,undo:o,redo:r,canUndo:i,canRedo:u}}var rH,iH=function(t){var n=ru(t),e=rd(),o=oH();o.add(t);var r=function(){return n.get()},i=function(t){n.set(t)},u=function(){return e.get().getOrThunk(n.get)},a=function(t){var n=c(t);return l(),e.set(n),n.url},c=function(t){return{blob:t,url:URL.createObjectURL(t)}},s=function(t){URL.revokeObjectURL(t.url)},f=function(t){aR.each(t,s)},l=function(){e.on(s),e.clear()},d=function(t){var n=c(t);i(n);var e=o.add(n).removed;return f(e),n.url},m=function(t){var n=c(t);return e.set(n),n.url},g=function(t){return e.get().fold(h,(function(n){d(n.blob),t()}))},p=function(){var t=o.undo();return i(t),t.url},v=function(){var t=o.redo();return i(t),t.url},b=function(){var t=o.canUndo(),n=o.canRedo();return{undoEnabled:t,redoEnabled:n}};return{getBlobState:r,setBlobState:i,addBlobState:d,getTempState:u,updateTempState:a,addTempState:m,applyTempState:g,destroyTempState:l,undo:p,redo:v,getHistoryStates:b}},uH=function(t,n){var e=iH(t.currentState),o=function(t,n){var e=n.event.direction;C.zoom(t,e)},r=function(t){var n=e.getHistoryStates();O.updateButtonUndoStates(t,n.undoEnabled,n.redoEnabled),ka(t,LP.formActionEvent,{name:LP.saveState(),value:n.undoEnabled})},i=function(t){O.updateButtonUndoStates(t,!1,!1)},u=function(t,n){var o=e.undo();l(t,o).then((function(n){f(t),r(t)}))},a=function(t,n){var o=e.redo();l(t,o).then((function(n){f(t),r(t)}))},c=function(t){return t.toBlob()},s=function(t){ka(t,LP.formActionEvent,{name:LP.disable(),value:{}})},f=function(t){_.getApplyButton(t).each((function(t){Pv.enable(t)})),ka(t,LP.formActionEvent,{name:LP.enable(),value:{}})},l=function(t,n){return s(t),C.updateSrc(t,n)},d=function(t,n,e,o,i){s(t),YR(n).then(e).then(c).then(o).then((function(n){return l(t,n)})).then((function(){r(t),i(),f(t)})).catch((function(n){t.getSystem().isConnected()&&f(t)}))},m=function(t,n,o){var r=e.getBlobState().blob,i=function(t){return e.updateTempState(t)};d(t,r,n,i,o)},g=function(t,n){var o=e.getTempState().blob,r=function(t){return e.addTempState(t)};d(t,o,n,r,h)},p=function(t,n,o){var r=e.getBlobState().blob,i=function(n){var o=e.addBlobState(n);return b(t),o};d(t,r,n,i,o)},v=function(t,n){var o=function(){b(t);var e=n.event.swap;e()};e.applyTempState(o)},b=function(t){var n=e.getBlobState().url;return e.destroyTempState(),r(t),n},y=function(t){var n=b(t);l(t,n).then((function(n){f(t)}))},x=function(t,n){y(t);var e=n.event.swap;e(),C.hideCrop()},w=function(t,n){return m(t,n.event.transform,h)},S=function(t,n){return g(t,n.event.transform)},k=function(t,n){return p(t,n.event.transform,n.event.swap)},C=ZP(t.currentState.url),O=eH(n),_=zP(C,n),T=function(t,n){i(t);var e=n.event.transform,o=n.event.swap;e.fold((function(){o()}),(function(n){m(t,n,o)}))};return{dom:{tag:"div",attributes:{role:"presentation"}},components:[_.memContainer.asSpec(),C.memContainer.asSpec(),O.container],behaviours:ml([th.config({store:{mode:"manual",getValue:function(){return e.getBlobState()}}}),zx("image-tools-events",[Ma(NP.undo(),u),Ma(NP.redo(),a),Ma(NP.zoom(),o),Ma(NP.back(),x),Ma(NP.apply(),v),Ma(NP.transform(),w),Ma(NP.tempTransform(),S),Ma(NP.transformApply(),k),Ma(NP.swap(),T)]),ZI.self()])}},aH=function(t,n){var e={dom:{tag:"label",innerHtml:n.providers.translate(t.label),classes:["tox-label"]}},o=X(t.items,n.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[e].concat(o),behaviours:ml([ZI.self(),Vx.config({}),pR.domHtml(N.none()),Dx.config({mode:"acyclic"})])}},cH=function(t){return!Tt(t,"items")},sH="data-value",fH=function(t,n,e,o){return X(e,(function(e){return cH(e)?{type:"togglemenuitem",text:e.text,value:e.value,active:e.value===o,onAction:function(){th.setValue(t,e.value),ka(t,NA,{name:n}),Jx.focus(t)}}:{type:"nestedmenuitem",text:e.text,getSubmenuItems:function(){return fH(t,n,e.items,o)}}}))},lH=function(t,n){return gt(t,(function(t){return cH(t)?Nt(t.value===n,t):lH(t.items,n)}))},dH=function(t,n){var e=n.shared.providers,o=lt(t.items).filter(cH),r=t.label.map((function(t){return RA(t,e)})),i=CA.parts.field({dom:{},factory:{sketch:function(e){return WN({uid:e.uid,text:o.map((function(t){return t.text})),icon:N.none(),tooltip:t.label,role:N.none(),fetch:function(e,o){var r=fH(e,t.name,t.items,th.getValue(e));o(tP(r,QC.CLOSE_ON_EXECUTE,n,!1))},onSetup:x(h),getApi:x({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[EA.config({}),th.config({store:{mode:"manual",initialValue:o.map((function(t){return t.value})).getOr(""),getValue:function(t){return Ue(t.element,sH)},setValue:function(n,e){lH(t.items,e).each((function(t){Le(n.element,sH,t.value),ka(n,UN,{text:t.text})}))}}})]},"tox-listbox",n.shared)}}}),u={dom:{tag:"div",classes:["tox-listboxfield"]},components:[i]};return CA.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:et([r.toArray(),[u]]),fieldBehaviours:ml([Pv.config({disabled:x(t.disabled),onDisabled:function(t){CA.getField(t).each(Pv.disable)},onEnabled:function(t){CA.getField(t).each(Pv.enable)}})])})},mH=function(t,n){return{dom:{tag:"div",classes:t.classes},components:X(t.items,n.shared.interpreter)}},gH=function(t,n){var e=X(t.options,(function(t){return{dom:{tag:"option",value:t.value,innerHtml:t.text}}})),o=t.data.map((function(t){return mu("initialValue",t)})).getOr({});return{uid:t.uid,dom:{tag:"select",classes:t.selectClasses,attributes:t.selectAttributes},components:e,behaviours:oh(t.selectBehaviours,[Jx.config({}),th.config({store:D({mode:"manual",getValue:function(t){return ds(t.element)},setValue:function(n,e){var o=tt(t.options,(function(t){return t.value===e}));o.isSome()&&ms(n.element,e)}},o)})])}},pH=dv({name:"HtmlSelect",configFields:[Di("options"),nh("selectBehaviours",[Jx,th]),qi("selectClasses",[]),qi("selectAttributes",{}),Li("data")],factory:gH}),hH=function(t,n){var e=X(t.items,(function(t){return{text:n.translate(t.text),value:t.value}})),o=t.label.map((function(t){return RA(t,n)})),r=CA.parts.field({dom:{},selectAttributes:{size:t.size},options:e,factory:pH,selectBehaviours:ml([Pv.config({disabled:function(){return t.disabled||n.isDisabled()}}),EA.config({}),zx("selectbox-change",[Ma(ju(),(function(n,e){ka(n,NA,{name:t.name})}))])])}),i=t.size>1?N.none():N.some(Gk("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},n.icons)),u={dom:{tag:"div",classes:["tox-selectfield"]},components:et([[r],i.toArray()])};return CA.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:et([o.toArray(),[u]]),fieldBehaviours:ml([Pv.config({disabled:function(){return t.disabled||n.isDisabled()},onDisabled:function(t){CA.getField(t).each(Pv.disable)},onEnabled:function(t){CA.getField(t).each(Pv.enable)}}),vT()])})},vH=function(t,n){var e=function(t){return{dom:{tag:"th",innerHtml:n.translate(t)}}},o=function(t){return{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:X(t,e)}]}},r=function(t){return{dom:{tag:"td",innerHtml:n.translate(t)}}},i=function(t){return{dom:{tag:"tr"},components:X(t,r)}},u=function(t){return{dom:{tag:"tbody"},components:X(t,i)}};return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[o(t.header),u(t.cells)],behaviours:ml([EA.config({}),Jx.config({})])}},bH=function(t,n){var e=t.label.map((function(t){return RA(t,n)})),o=[Pv.config({disabled:function(){return t.disabled||n.isDisabled()}}),vT(),Dx.config({mode:"execution",useEnter:!0!==t.multiline,useControlEnter:!0===t.multiline,execute:function(t){return Sa(t,LA),N.some(!0)}}),zx("textfield-change",[Ma(Uu(),(function(n,e){ka(n,NA,{name:t.name})})),Ma(Zu(),(function(n,e){ka(n,NA,{name:t.name})}))]),EA.config({})],r=t.validation.map((function(t){return wB.config({getRoot:function(t){return ve(t.element)},invalidClass:"tox-invalid",validator:{validate:function(n){var e=th.getValue(n),o=t.validator(e);return sB.pure(!0===o?rr.value(e):rr.error(o))},validateOnLoad:t.validateOnLoad}})})).toArray(),i=t.placeholder.fold(x({}),(function(t){return{placeholder:n.translate(t)}})),u=t.inputMode.fold(x({}),(function(t){return{inputmode:t}})),a=D(D({},i),u),c=CA.parts.field({tag:!0===t.multiline?"textarea":"input",inputAttributes:a,inputClasses:[t.classname],inputBehaviours:ml(et([o,r])),selectOnFocus:!1,factory:$A}),s=t.flex?["tox-form__group--stretched"]:[],f=s.concat(t.maximized?["tox-form-group--maximize"]:[]),l=[Pv.config({disabled:function(){return t.disabled||n.isDisabled()},onDisabled:function(t){CA.getField(t).each(Pv.disable)},onEnabled:function(t){CA.getField(t).each(Pv.enable)}}),vT()];return AA(e,c,f,l)},yH=function(t,n){return bH({name:t.name,multiline:!1,label:t.label,inputMode:t.inputMode,placeholder:t.placeholder,flex:!1,disabled:t.disabled,classname:"tox-textfield",validation:N.none(),maximized:t.maximized},n)},xH=function(t,n){return bH({name:t.name,multiline:!0,label:t.label,inputMode:N.none(),placeholder:t.placeholder,flex:!0,disabled:t.disabled,classname:"tox-textarea",validation:N.none(),maximized:t.maximized},n)},wH=function(t,n){var e=t.stream.streams,o=e.setup(t,n);return Ea([Ma(t.event,o),ja((function(){return n.cancel()}))].concat(t.cancelEvent.map((function(t){return[Ma(t,(function(){return n.cancel()}))]})).getOr([])))},SH=Object.freeze({__proto__:null,events:wH}),kH=function(t){var n=ru(null),e=function(){return{timer:null!==n.get()?"set":"unset"}},o=function(t){n.set(t)},r=function(){var t=n.get();null!==t&&t.cancel()};return Oc({readState:e,setTimer:o,cancel:r})},CH=function(t){return t.stream.streams.state(t)},OH=Object.freeze({__proto__:null,throttle:kH,init:CH}),_H=function(t,n){var e=t.stream,o=Zk(t.onStream,e.delay);return n.setTimer(o),function(t,n){o.throttle(t,n),e.stopEvent&&n.stop()}},TH=[Ai("stream",Ci("mode",{throttle:[Di("delay"),qi("stopEvent",!0),df("streams",{setup:_H,state:kH})]})),qi("event","input"),Li("cancelEvent"),ff("onStream")],EH=pl({fields:TH,name:"streaming",active:SH,state:OH}),DH=function(t,n,e){var o=th.getValue(e);th.setValue(n,o),BH(n)},AH=function(t,n){var e=t.element,o=ds(e),r=e.dom;"number"!==Ue(e,"type")&&n(r,o)},BH=function(t){AH(t,(function(t,n){return t.setSelectionRange(n.length,n.length)}))},MH=function(t,n){AH(t,(function(t,e){return t.setSelectionRange(n,e.length)}))},FH=function(t,n,e){if(t.selectsOver){var o=th.getValue(n),r=t.getDisplayText(o),i=th.getValue(e),u=t.getDisplayText(i);return 0===u.indexOf(r)?N.some((function(){DH(t,n,e),MH(n,r.length)})):N.none()}return N.none()},IH=x("alloy.typeahead.itemexecute"),RH=function(t,n,e,o){var r=function(n,e,r){t.previewing.set(!1);var i=TB.getCoupled(n,"sandbox");if(bp.isOpen(i))bv.getCurrent(i).each((function(t){ob.getHighlighted(t).fold((function(){r(t)}),(function(){Ta(i,t.element,"keydown",e)}))}));else{var a=function(t){bv.getCurrent(t).each(r)};IB(t,u(n),n,i,o,a,tB.HighlightFirst).get(h)}},i=YA(t),u=function(t){return function(n){return n.map((function(n){var e=Ot(n.menus),o=ot(e,(function(t){return J(t.items,(function(t){return"item"===t.type}))})),r=th.getState(t);return r.update(X(o,(function(t){return t.data}))),n}))}},a=[Jx.config({}),th.config({onSetValue:t.onSetValue,store:D({mode:"dataset",getDataKey:function(t){return ds(t.element)},getFallbackEntry:function(t){return{value:t,meta:{}}},setValue:function(n,e){ms(n.element,t.model.getDisplayText(e))}},t.initialData.map((function(t){return mu("initialValue",t)})).getOr({}))}),EH.config({stream:{mode:"throttle",delay:t.responseTime,stopEvent:!1},onStream:function(n,e){var r=TB.getCoupled(n,"sandbox"),i=Jx.isFocused(n);if(i&&ds(n.element).length>=t.minChars){var a=bv.getCurrent(r).bind((function(t){return ob.getHighlighted(t).map(th.getValue)}));t.previewing.set(!0);var c=function(n){bv.getCurrent(r).each((function(n){a.fold((function(){t.model.selectsOver&&ob.highlightFirst(n)}),(function(t){ob.highlightBy(n,(function(n){var e=th.getValue(n);return e.value===t.value})),ob.getHighlighted(n).orThunk((function(){return ob.highlightFirst(n),N.none()}))}))}))};IB(t,u(n),n,r,o,c,tB.HighlightFirst).get(h)}},cancelEvent:ua()}),Dx.config({mode:"special",onDown:function(t,n){return r(t,n,ob.highlightFirst),N.some(!0)},onEscape:function(t){var n=TB.getCoupled(t,"sandbox");return bp.isOpen(n)?(bp.close(n),N.some(!0)):N.none()},onUp:function(t,n){return r(t,n,ob.highlightLast),N.some(!0)},onEnter:function(n){var e=TB.getCoupled(n,"sandbox"),o=bp.isOpen(e);if(o&&!t.previewing.get())return bv.getCurrent(e).bind((function(t){return ob.getHighlighted(t)})).map((function(t){return ka(n,IH(),{item:t}),!0}));var r=th.getValue(n);return Sa(n,ua()),t.onExecute(e,n,r),o&&bp.close(e),N.some(!0)}}),pw.config({toggleClass:t.markers.openClass,aria:{mode:"expanded"}}),TB.config({others:{sandbox:function(n){return LB(t,n,{onOpen:function(){return pw.on(n)},onClose:function(){return pw.off(n)}})}}}),zx("typeaheadevents",[Ga((function(n){var e=h;NB(t,u(n),n,o,e,tB.HighlightFirst).get(h)})),Ma(IH(),(function(n,e){var o=TB.getCoupled(n,"sandbox");DH(t.model,n,e.event.item),Sa(n,ua()),t.onItemExecute(n,o,e.event.item,th.getValue(n)),bp.close(o),BH(n)}))].concat(t.dismissOnBlur?[Ma(Qu(),(function(t){var n=TB.getCoupled(t,"sandbox");Tl(n.element).isNone()&&bp.close(n)}))]:[]))];return{uid:t.uid,dom:KA(Sr(t,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:D(D({},i),oh(t.typeaheadBehaviours,a)),eventOrder:t.eventOrder}},NH=x([Li("lazySink"),Di("fetch"),qi("minChars",5),qi("responseTime",1e3),cf("onOpen"),qi("getHotspot",N.some),qi("getAnchorOverrides",x({})),qi("layouts",N.none()),qi("eventOrder",{}),ou("model",{},[qi("getDisplayText",(function(t){return void 0!==t.meta&&void 0!==t.meta.text?t.meta.text:t.value})),qi("selectsOver",!0),qi("populateFromBrowse",!0)]),cf("onSetValue"),sf("onExecute"),cf("onItemExecute"),qi("inputClasses",[]),qi("inputAttributes",{}),qi("inputStyles",{}),qi("matchWidth",!0),qi("useMinWidth",!1),qi("dismissOnBlur",!0),uf(["openClass"]),Li("initialData"),nh("typeaheadBehaviours",[Jx,th,EH,Dx,pw,TB]),Ti("previewing",(function(){return ru(!0)}))].concat(XA()).concat(UB())),PH=x([Ih({schema:[rf()],name:"menu",overrides:function(t){return{fakeFocus:!0,onHighlight:function(n,e){t.previewing.get()?n.getSystem().getByUid(t.uid).each((function(o){FH(t.model,o,e).fold((function(){return ob.dehighlight(n,e)}),(function(t){return t()}))})):n.getSystem().getByUid(t.uid).each((function(n){t.model.populateFromBrowse&&DH(t.model,n,e)})),t.previewing.set(!1)},onExecute:function(n,e){return n.getSystem().getByUid(t.uid).toOptional().map((function(t){return ka(t,IH(),{item:e}),!0}))},onHover:function(n,e){t.previewing.set(!1),n.getSystem().getByUid(t.uid).each((function(n){t.model.populateFromBrowse&&DH(t.model,n,e)}))}}}})]),HH=mv({name:"Typeahead",configFields:NH(),partFields:PH(),factory:RH}),VH=function(t){var n=function(){return VH(t.toCached())},e=function(n){return VH(t.bind((function(t){return t.fold((function(t){return sB.pure(rr.error(t))}),(function(t){return n(t)}))})))},o=function(n){return VH(t.map((function(t){return t.bind(n)})))},r=function(n){return VH(t.map((function(t){return t.map(n)})))},i=function(n){return VH(t.map((function(t){return t.mapError(n)})))},u=function(n,e){return t.map((function(t){return t.fold(n,e)}))},a=function(n,e){return VH(sB.nu((function(o){var r=!1,i=setTimeout((function(){r=!0,o(rr.error(e()))}),n);t.get((function(t){r||(clearTimeout(i),o(t))}))})))};return D(D({},t),{toCached:n,bindFuture:e,bindResult:o,mapResult:r,mapError:i,foldResult:u,withTimeout:a})},LH=function(t){return VH(sB.nu(t))},zH=function(t){return VH(sB.pure(rr.value(t)))},UH=function(t){return VH(sB.pure(rr.error(t)))},jH=function(t){return VH(sB.pure(t))},WH=function(t){return VH(t.map(rr.value))},GH=function(t){return LH((function(n){t.then((function(t){n(rr.value(t))}),(function(t){n(rr.error(t))}))}))},XH={nu:LH,wrap:VH,pure:zH,value:zH,error:UH,fromResult:jH,fromFuture:WH,fromPromise:GH},YH={type:"separator"},qH=function(t){return{type:"menuitem",value:t.url,text:t.title,meta:{attach:t.attach},onAction:h}},KH=function(t,n){return{type:"menuitem",value:n,text:t,meta:{attach:void 0},onAction:h}},JH=function(t){return X(t,qH)},$H=function(t,n){return J(n,(function(n){return n.type===t}))},QH=function(t,n){return JH($H(t,n))},ZH=function(t){return QH("header",t.targets)},tV=function(t){return QH("anchor",t.targets)},nV=function(t){return N.from(t.anchorTop).map((function(t){return KH("<top>",t)})).toArray()},eV=function(t){return N.from(t.anchorBottom).map((function(t){return KH("<bottom>",t)})).toArray()},oV=function(t){return X(t,(function(t){return KH(t,t)}))},rV=function(t){return Q(t,(function(t,n){var e=0===t.length||0===n.length;return e?t.concat(n):t.concat(YH,n)}),[])},iV=function(t,n){var e=t.toLowerCase();return J(n,(function(t){var n=void 0!==t.meta&&void 0!==t.meta.text?t.meta.text:t.text;return Ut(n.toLowerCase(),e)||Ut(t.value.toLowerCase(),e)}))},uV=function(t,n,e){var o=th.getValue(n),r=void 0!==o.meta.text?o.meta.text:o.value,i=e.getLinkInformation();return i.fold((function(){return[]}),(function(n){var o=iV(r,oV(e.getHistory(t)));return"file"===t?rV([o,iV(r,ZH(n)),iV(r,et([nV(n),tV(n),eV(n)]))]):o}))},aV=rc("aria-invalid"),cV=function(t,n,e){var o,r=n.shared.providers,i=function(n){var o=th.getValue(n);e.addToHistory(o.value,t.filetype)},u=CA.parts.field({factory:HH,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":aV,type:"url"},minChars:0,responseTime:0,fetch:function(o){var r=uV(t.filetype,o,e),i=tP(r,QC.BUBBLE_TO_SANDBOX,n,!1);return sB.pure(i)},getHotspot:function(t){return m.getOpt(t)},onSetValue:function(t,n){t.hasConfigured(wB)&&wB.run(t).get(h)},typeaheadBehaviours:ml(et([e.getValidationHandler().map((function(n){return wB.config({getRoot:function(t){return ve(t.element)},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(t,n){s.getOpt(t).each((function(t){Le(t.element,"title",r.translate(n))}))}},validator:{validate:function(e){var o=th.getValue(e);return XH.nu((function(e){n({type:t.filetype,url:o.value},(function(t){if("invalid"===t.status){var n=rr.error(t.message);e(n)}else{var o=rr.value(t.message);e(o)}}))}))},validateOnLoad:!1}})})).toArray(),[Pv.config({disabled:function(){return t.disabled||r.isDisabled()}}),EA.config({}),zx("urlinput-events",et(["file"===t.filetype?[Ma(Uu(),(function(n){ka(n,NA,{name:t.name})}))]:[],[Ma(ju(),(function(n){ka(n,NA,{name:t.name}),i(n)})),Ma(Zu(),(function(n){ka(n,NA,{name:t.name}),i(n)}))]]))]])),eventOrder:(o={},o[Uu()]=["streaming","urlinput-events","invalidating"],o),model:{getDisplayText:function(t){return t.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:n.shared.getSink,parts:{menu:OO(!1,1,"normal")},onExecute:function(t,n,e){ka(n,LA,{})},onItemExecute:function(n,e,o,r){i(n),ka(n,NA,{name:t.name})}}),a=t.label.map((function(t){return RA(t,r)})),c=function(t,n,e,o){return void 0===e&&(e=t),void 0===o&&(o=t),Gk(e,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+t],attributes:D({title:r.translate(o),"aria-live":"polite"},n.fold((function(){return{}}),(function(t){return{id:t}})))},r.icons)},s=TS(c("invalid",N.some(aV),"warning")),f=TS({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[s.asSpec()]}),l=e.getUrlPicker(t.filetype),d=rc("browser.url.event"),m=TS({dom:{tag:"div",classes:["tox-control-wrap"]},components:[u,f.asSpec()],behaviours:ml([Pv.config({disabled:function(){return t.disabled||r.isDisabled()}})])}),g=TS(cP({name:t.name,icon:N.some("browse"),text:t.label.getOr(""),disabled:t.disabled,primary:!1,borderless:!0},(function(t){return Sa(t,d)}),r,[],["tox-browse-url"])),p=function(){return{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:et([[m.asSpec()],l.map((function(){return g.asSpec()})).toArray()])}},v=function(n){bv.getCurrent(n).each((function(e){var o=th.getValue(e),r=D({fieldname:t.name},o);l.each((function(o){o(r).get((function(o){th.setValue(e,o),ka(n,NA,{name:t.name})}))}))}))};return CA.sketch({dom:FA(),components:a.toArray().concat([p()]),fieldBehaviours:ml([Pv.config({disabled:function(){return t.disabled||r.isDisabled()},onDisabled:function(t){CA.getField(t).each(Pv.disable),g.getOpt(t).each(Pv.disable)},onEnabled:function(t){CA.getField(t).each(Pv.enable),g.getOpt(t).each(Pv.enable)}}),vT(),zx("url-input-events",[Ma(d,v)])])})},sV=function(t,n){return bA.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+t.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[kS.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:Lk(t.icon,n.icons),attributes:{title:n.translate(t.iconTooltip)}},action:function(n){ka(n,VA,{name:"alert-banner",value:t.url})},buttonBehaviours:ml([jk()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:n.translate(t.text)}}]})},fV=function(t,n){var e=th.config({store:{mode:"manual",getValue:function(t){var n=t.element.dom;return n.checked},setValue:function(t,n){var e=t.element.dom;e.checked=n}}}),o=function(t){return t.element.dom.click(),N.some(!0)},r=CA.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:ml([ZI.self(),Pv.config({disabled:function(){return t.disabled||n.isDisabled()}}),EA.config({}),Jx.config({}),e,Dx.config({mode:"special",onEnter:o,onSpace:o,stopSpaceKeyup:!0}),zx("checkbox-events",[Ma(ju(),(function(n,e){ka(n,NA,{name:t.name})}))])])}),i=CA.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:n.translate(t.label)},behaviours:ml([JB.config({})])}),u=function(t){var e="checked"===t?"selected":"unselected";return Gk(e,{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+t]},n.icons)},a=TS({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[u("checked"),u("unchecked")]});return CA.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[r,a.asSpec(),i],fieldBehaviours:ml([Pv.config({disabled:function(){return t.disabled||n.isDisabled()},disableClass:"tox-checkbox--disabled",onDisabled:function(t){CA.getField(t).each(Pv.disable)},onEnabled:function(t){CA.getField(t).each(Pv.enable)}}),vT()])})},lV=function(t){return"presentation"===t.presets?bA.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html}}):bA.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html,attributes:{role:"document"}},containerBehaviours:ml([EA.config({}),Jx.config({})])})},dV=function(t){return function(n,e,o){return _t(e,"name").fold((function(){return t(e,o)}),(function(r){return n.field(r,t(e,o))}))}},mV=function(t){return function(n,e,o){var r=Sr(e,{source:"dynamic"});return dV(t)(n,r,o)}},gV={bar:dV((function(t,n){return xA(t,n.shared)})),collection:dV((function(t,n){return GA(t,n.shared.providers)})),alertbanner:dV((function(t,n){return sV(t,n.shared.providers)})),input:dV((function(t,n){return yH(t,n.shared.providers)})),textarea:dV((function(t,n){return xH(t,n.shared.providers)})),label:dV((function(t,n){return aH(t,n.shared)})),iframe:mV((function(t,n){return DR(t,n.shared.providers)})),button:dV((function(t,n){return mP(t,n.shared.providers)})),checkbox:dV((function(t,n){return fV(t,n.shared.providers)})),colorinput:dV((function(t,n){return nM(t,n.shared,n.colorinput)})),colorpicker:dV(oR),dropzone:dV((function(t,n){return bR(t,n.shared.providers)})),grid:dV((function(t,n){return yR(t,n.shared)})),listbox:dV((function(t,n){return dH(t,n)})),selectbox:dV((function(t,n){return hH(t,n.shared.providers)})),sizeinput:dV((function(t,n){return _P(t,n.shared.providers)})),urlinput:dV((function(t,n){return cV(t,n,n.urlinput)})),customeditor:dV(uR),htmlpanel:dV(lV),imagetools:dV((function(t,n){return uH(t,n.shared.providers)})),table:dV((function(t,n){return vH(t,n.shared.providers)})),panel:dV((function(t,n){return mH(t,n)}))},pV={field:function(t,n){return n}},hV=function(t,n,e){var o=Sr(e,{shared:{interpreter:function(n){return vV(t,n,o)}}});return vV(t,n,o)},vV=function(t,n,e){return _t(gV,n.type).fold((function(){return n}),(function(o){return o(t,n,e)}))},bV=function(t,n){var e=pV;return vV(e,t,n)},yV={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},xV=function(t,n,e){var o=12,r={maxHeightFunction:Td()},i=function(){return{type:"node",root:Ae(t()),node:N.from(t()),bubble:Md(o,o,yV),layouts:{onRtl:function(){return[dS]},onLtr:function(){return[lS]}},overrides:r}},u=function(){return{type:"hotspot",hotspot:n(),bubble:Md(-o,o,yV),layouts:{onRtl:function(){return[Uf]},onLtr:function(){return[jf]}},overrides:r}};return function(){return e()?i():u()}},wV=function(t,n,e){var o=function(){return{type:"node",root:Ae(t()),node:N.from(t()),layouts:{onRtl:function(){return[mS]},onLtr:function(){return[mS]}}}},r=function(){return{type:"hotspot",hotspot:n(),layouts:{onRtl:function(){return[Yf]},onLtr:function(){return[Yf]}}}};return function(){return e()?o():r()}},SV=function(t,n){return function(){return{type:"selection",root:n(),getSelection:function(){var n=t.selection.getRng();return N.some(Cm.range(nn.fromDom(n.startContainer),n.startOffset,nn.fromDom(n.endContainer),n.endOffset))}}}},kV=function(t){return function(n){return{type:"node",root:t(),node:n}}},CV=function(t,n,e){var o=$_(t),r=function(){return nn.fromDom(t.getBody())},i=function(){return nn.fromDom(t.getContentAreaContainer())},u=function(){return o||!e()};return{inlineDialog:xV(i,n,u),banner:wV(i,n,u),cursor:SV(t,r),node:kV(r)}},OV=function(t){return function(n,e){var o=sD(t);o(n,e)}},_V=function(t){return function(){return WE(t)}},TV=function(t){return function(){return XE(t)}},EV=function(t){return function(){return nD(t)}},DV=function(t){return{colorPicker:OV(t),hasCustomColors:_V(t),getColors:TV(t),getColorCols:EV(t)}},AV=function(t){return function(){return eT(t)}},BV=function(t){return{isDraggableModal:AV(t)}},MV=function(t){var n=ru(X_(t)?"bottom":"top");return{isPositionedAtTop:function(){return"top"===n.get()},getDockingMode:n.get,setDockingMode:n.set}},FV=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],IV=function(t){return Tt(t,"items")},RV=function(t){return Tt(t,"block")},NV=function(t){return Tt(t,"inline")},PV=function(t){return Tt(t,"selector")},HV=function(t){return Q(t,(function(t,n){if(IV(n)){var e=HV(n.items);return{customFormats:t.customFormats.concat(e.customFormats),formats:t.formats.concat([{title:n.title,items:e.formats}])}}if(NV(n)||RV(n)||PV(n)){var o=i(n.name)?n.name:n.title.toLowerCase(),r="custom-"+o;return{customFormats:t.customFormats.concat([{name:r,format:n}]),formats:t.formats.concat([{title:n.title,format:r,icon:n.icon}])}}return D(D({},t),{formats:t.formats.concat(n)})}),{customFormats:[],formats:[]})},VV=function(t,n){var e=HV(n),o=function(n){Y(n,(function(n){t.formatter.has(n.name)||t.formatter.register(n.name,n.format)}))};return t.formatter?o(e.customFormats):t.on("init",(function(){o(e.customFormats)})),e.formats},LV=function(t){return M_(t).map((function(n){var e=VV(t,n);return F_(t)?FV.concat(e):e})).getOr(FV)},zV=function(t,n,e){var o={type:"formatter",isSelected:n(t.format),getStylePreview:e(t.format)};return Sr(t,o)},UV=function(t,n,e,o){var r=function(t){return zV(t,e,o)},u=function(t){var n={type:"submenu"};return Sr(t,n)},a=function(n){var r=i(n.name)?n.name:rc(n.title),u="custom-"+r,a={type:"formatter",format:u,isSelected:e(u),getStylePreview:o(u)},c=Sr(n,a);return t.formatter.register(r,c),c},c=function(t){return X(t,(function(t){var n=pt(t);if(Et(t,"items")){var e=c(t.items);return Sr(u(t),{getStyleItems:x(e)})}return Et(t,"format")?r(t):1===n.length&&U(n,"title")?Sr(t,{type:"separator"}):a(t)}))};return c(n)},jV=function(t){var n=function(n){return function(){return t.formatter.match(n)}},e=function(n){return function(){var e=t.formatter.get(n);return void 0!==e?N.some({tag:e.length>0&&(e[0].inline||e[0].block)||"div",styles:t.dom.parseStyle(t.formatter.getCssText(n))}):N.none()}},o=function(t){var n=t.items;return void 0!==n&&n.length>0?ot(n,o):[t.format]},r=ru([]),i=ru([]),u=ru([]),a=ru([]),c=ru(!1);t.on("PreInit",(function(u){var a=LV(t),c=UV(t,a,n,e);r.set(c),i.set(ot(c,o))})),t.on("addStyleModifications",(function(r){var i=UV(t,r.items,n,e);u.set(i),c.set(r.replace),a.set(ot(i,o))}));var s=function(){var t=c.get()?[]:r.get(),n=u.get();return t.concat(n)},f=function(){var t=c.get()?[]:i.get(),n=a.get();return t.concat(n)};return{getData:s,getFlattenedKeys:f}},WV=function(t){return d(t)&&1===t.nodeType},GV=aR.trim,XV=function(t){return function(n){if(WV(n)){if(n.contentEditable===t)return!0;if(n.getAttribute("data-mce-contenteditable")===t)return!0}return!1}},YV=XV("true"),qV=XV("false"),KV=function(t,n,e,o,r){return{type:t,title:n,url:e,level:o,attach:r}},JV=function(t){while(t=t.parentNode){var n=t.contentEditable;if(n&&"inherit"!==n)return YV(t)}return!1},$V=function(t,n){return X(Km(nn.fromDom(n),t),(function(t){return t.dom}))},QV=function(t){return t.innerText||t.textContent},ZV=function(t){return t.id?t.id:rc("h")},tL=function(t){return t&&"A"===t.nodeName&&void 0!==(t.id||t.name)},nL=function(t){return tL(t)&&oL(t)},eL=function(t){return t&&/^(H[1-6])$/.test(t.nodeName)},oL=function(t){return JV(t)&&!qV(t)},rL=function(t){return eL(t)&&oL(t)},iL=function(t){return eL(t)?parseInt(t.nodeName.substr(1),10):0},uL=function(t){var n=ZV(t),e=function(){t.id=n};return KV("header",QV(t),"#"+n,iL(t),e)},aL=function(t){var n=t.id||t.name,e=QV(t);return KV("anchor",e||"#"+n,"#"+n,0,h)},cL=function(t){return X(J(t,rL),uL)},sL=function(t){return X(J(t,nL),aL)},fL=function(t){var n=$V("h1,h2,h3,h4,h5,h6,a:not([href])",t);return n},lL=function(t){return GV(t.title).length>0},dL=function(t){var n=fL(t);return J(cL(n).concat(sL(n)),lL)},mL={find:dL},gL="tinymce-url-history",pL=5,hL=function(t){return i(t)&&/^https?/.test(t)},vL=function(t){return a(t)&&t.length<=pL&&rt(t,hL)},bL=function(t){return u(t)&&Ct(t,(function(t){return!vL(t)})).isNone()},yL=function(){var t,n=RE.getItem(gL);if(null===n)return{};try{t=JSON.parse(n)}catch(e){if(e instanceof SyntaxError)return{};throw e}return bL(t)?t:{}},xL=function(t){if(!bL(t))throw new Error("Bad format for history:\n"+JSON.stringify(t));RE.setItem(gL,JSON.stringify(t))},wL=function(t){var n=yL();return _t(n,t).getOr([])},SL=function(t,n){if(hL(t)){var e=yL(),o=_t(e,n).getOr([]),r=J(o,(function(n){return n!==t}));e[n]=[t].concat(r).slice(0,pL),xL(e)}},kL=function(t){return!!t},CL=function(t){return bt(aR.makeMap(t,/[, ]/),kL)},OL=function(t){return N.from(uT(t)).filter(m)},_L=function(t){var n=N.some(aT(t)).filter(kL),e=N.some(cT(t)).filter(kL),o=n.or(e).map(CL);return OL(t).fold(_,(function(t){return o.fold(T,(function(t){return pt(t).length>0&&t}))}))},TL=function(t,n){var e=_L(t);return s(e)?e?OL(t):N.none():e[n]?OL(t):N.none()},EL=function(t,n){return TL(t,n).map((function(e){return function(o){return sB.nu((function(r){var a=function(t,n){if(!i(t))throw new Error("Expected value to be string");if(void 0!==n&&!u(n))throw new Error("Expected meta to be a object");var e={value:t,meta:n};r(e)},c=D({filetype:n,fieldname:o.fieldname},N.from(o.meta).getOr({}));e.call(t,a,o.value,c)}))}}))},DL=function(t){return N.from(t).filter(i).getOrUndefined()},AL=function(t){return sT(t)?N.none():N.some({targets:mL.find(t.getBody()),anchorTop:DL(fT(t)),anchorBottom:DL(lT(t))})},BL=function(t){return N.from(dT(t))},ML=function(t){return{getHistory:wL,addToHistory:SL,getLinkInformation:function(){return AL(t)},getValidationHandler:function(){return BL(t)},getUrlPicker:function(n){return EL(t,n)}}},FL=function(t,n,e){var o=ru(!1),r=MV(n),i={shared:{providers:{icons:function(){return n.ui.registry.getAll().icons},menuItems:function(){return n.ui.registry.getAll().menuItems},translate:Ik.translate,isDisabled:function(){return n.mode.isReadOnly()||n.ui.isDisabled()},getSetting:n.getParam.bind(n)},interpreter:function(t){return bV(t,i)},anchors:CV(n,e,r.isPositionedAtTop),header:r,getSink:function(){return rr.value(t)}},urlinput:ML(n),styleselect:jV(n),colorinput:DV(n),dialog:BV(n),isContextMenuOpen:function(){return o.get()},setContextMenuState:function(t){return o.set(t)}};return i},IL=function(t,n,e){var o=function(t,o){Y([n,e],(function(n){n.broadcastEvent(t,o)}))},r=function(t,o){Y([n,e],(function(n){n.broadcastOn([t],o)}))},i=function(t){return r(yp(),{target:t.target})},u=Sl(),a=ud(u,"touchstart",i),c=ud(u,"touchmove",(function(t){return o(ca(),t)})),s=ud(u,"touchend",(function(t){return o(sa(),t)})),f=ud(u,"mousedown",i),l=ud(u,"mouseup",(function(t){0===t.raw.button&&r(wp(),{target:t.target})})),d=function(t){return r(yp(),{target:nn.fromDom(t.target)})},m=function(t){0===t.button&&r(wp(),{target:nn.fromDom(t.target)})},g=function(){Y(t.editorManager.get(),(function(n){t!==n&&n.fire("DismissPopups",{relatedTarget:t})}))},p=function(t){return o(fa(),cd(t))},h=function(t){r(xp(),{}),o(la(),cd(t))},v=function(){return r(xp(),{})},b=function(n){n.state&&r(yp(),{target:nn.fromDom(t.getContainer())})},y=function(t){r(yp(),{target:nn.fromDom(t.relatedTarget.getContainer())})};t.on("PostRender",(function(){t.on("click",d),t.on("tap",d),t.on("mouseup",m),t.on("mousedown",g),t.on("ScrollWindow",p),t.on("ResizeWindow",h),t.on("ResizeEditor",v),t.on("AfterProgressState",b),t.on("DismissPopups",y)})),t.on("remove",(function(){t.off("click",d),t.off("tap",d),t.off("mouseup",m),t.off("mousedown",g),t.off("ScrollWindow",p),t.off("ResizeWindow",h),t.off("ResizeEditor",v),t.off("AfterProgressState",b),t.off("DismissPopups",y),f.unbind(),a.unbind(),c.unbind(),s.unbind(),l.unbind()})),t.on("detach",(function(){Qg(n),Qg(e),n.destroy(),e.destroy()}))},RL=ev,NL=Hh,PL=x([qi("shell",!1),Di("makeItem"),qi("setupItem",h),rh.field("listBehaviours",[Vx])]),HL=function(){return{behaviours:ml([Vx.config({})])}},VL=Rh({name:"items",overrides:HL}),LL=x([VL]),zL=x("CustomList"),UL=function(t,n,e,o){var r=function(n,e){u(n).fold((function(){throw new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(function(o){var r=Vx.contents(o),i=e.length,u=i-r.length,a=u>0?W(u,(function(){return t.makeItem()})):[],c=r.slice(i);Y(c,(function(t){return Vx.remove(o,t)})),Y(a,(function(t){return Vx.append(o,t)}));var s=Vx.contents(o);Y(s,(function(o,r){t.setupItem(n,o,e[r],r)}))}))},i=t.shell?{behaviours:[Vx.config({})],components:[]}:{behaviours:[],components:n},u=function(n){return t.shell?N.some(n):qh(n,t,"items")};return{uid:t.uid,dom:t.dom,components:i.components,behaviours:oh(t.listBehaviours,i.behaviours),apis:{setItems:r}}},jL=mv({name:zL(),configFields:PL(),partFields:LL(),factory:UL,apis:{setItems:function(t,n,e){t.setItems(n,e)}}}),WL=x([Di("dom"),qi("shell",!0),nh("toolbarBehaviours",[Vx])]),GL=function(){return{behaviours:ml([Vx.config({})])}},XL=x([Rh({name:"groups",overrides:GL})]),YL=function(t,n,e,o){var r=function(t,n){i(t).fold((function(){throw new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(function(t){Vx.set(t,n)}))},i=function(n){return t.shell?N.some(n):qh(n,t,"groups")},u=t.shell?{behaviours:[Vx.config({})],components:[]}:{behaviours:[],components:n};return{uid:t.uid,dom:t.dom,components:u.components,behaviours:oh(t.toolbarBehaviours,u.behaviours),apis:{setGroups:r},domModification:{attributes:{role:"group"}}}},qL=mv({name:"Toolbar",configFields:WL(),partFields:XL(),factory:YL,apis:{setGroups:function(t,n,e){t.setGroups(n,e)}}}),KL=h,JL=_,$L=x([]),QL=Object.freeze({__proto__:null,setup:KL,isDocked:JL,getBehaviours:$L}),ZL=function(t){var n=Dt(Ze(t,"position"),"fixed"),e=n?N.none():ye(t);return e.orThunk((function(){var n=nn.fromTag("span");return ve(t).bind((function(t){Bo(t,n);var e=ye(n);return No(n),e}))}))},tz=function(t){return ZL(t).map(go).getOrThunk((function(){return fo(0,0)}))},nz=uu.generate([{static:[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),ez=function(t,n){var e=t.element;is(e,n.transitionClass),as(e,n.fadeOutClass),is(e,n.fadeInClass),n.onShow(t)},oz=function(t,n){var e=t.element;is(e,n.transitionClass),as(e,n.fadeInClass),is(e,n.fadeOutClass),n.onHide(t)},rz=function(t,n){return t.y<n.bottom&&t.bottom>n.y},iz=function(t,n){return t.y>=n.y},uz=function(t,n){return t.bottom<=n.bottom},az=function(t,n,e){return rt(t,(function(t){switch(t){case"bottom":return uz(n,e);case"top":return iz(n,e)}}))},cz=function(t,n){return n.getInitialPos().map((function(n){return $o(n.bounds.x,n.bounds.y,bo(t),uo(t))}))},sz=function(t,n,e){e.setInitialPos({style:to(t),position:$e(t,"position")||"static",bounds:n})},fz=function(t,n,e){return e.getInitialPos().bind((function(o){switch(e.clearInitialPos(),o.position){case"static":return N.some(nz.static());case"absolute":var r=ZL(t).map(Qo).getOrThunk((function(){return Qo(Pe())}));return N.some(nz.absolute(Dl("absolute",_t(o.style,"left").map((function(t){return n.x-r.x})),_t(o.style,"top").map((function(t){return n.y-r.y})),_t(o.style,"right").map((function(t){return r.right-n.right})),_t(o.style,"bottom").map((function(t){return r.bottom-n.bottom})))));default:return N.none()}}))},lz=function(t,n,e){return cz(t,e).filter((function(t){return az(e.getModes(),t,n)})).bind((function(n){return fz(t,n,e)}))},dz=function(t,n,e){var o=Qo(t);if(az(e.getModes(),o,n))return N.none();sz(t,o,e);var r=tr(),i=o.x-r.x,u=n.y-r.y,a=r.bottom-n.bottom,c=o.y<=n.y;return N.some(nz.fixed(Dl("fixed",N.some(i),c?N.some(u):N.none(),N.none(),c?N.none():N.some(a))))},mz=function(t,n,e){var o=t.element,r=Dt(Ze(o,"position"),"fixed");return r?lz(o,n,e):dz(o,n,e)},gz=function(t,n){var e=t.element;return cz(e,n).bind((function(t){return fz(e,t,n)}))},pz=function(t,n,e){e.setDocked(!1),Y(["left","right","top","bottom","position"],(function(n){return eo(t.element,n)})),n.onUndocked(t)},hz=function(t,n,e,o){var r="fixed"===o.position;e.setDocked(r),Bl(t.element,o);var i=r?n.onDocked:n.onUndocked;i(t)},vz=function(t,n,e,o,r){void 0===r&&(r=!1),n.contextual.each((function(n){n.lazyContext(t).each((function(i){var u=rz(i,o);if(u!==e.isVisible())if(e.setVisible(u),r&&!u)ss(t.element,[n.fadeOutClass]),n.onHide(t);else{var a=u?ez:oz;a(t,n)}}))}))},bz=function(t,n,e){var o=n.lazyViewport(t),r=e.isDocked();r&&vz(t,n,e,o),mz(t,o,e).each((function(r){r.fold((function(){return pz(t,n,e)}),(function(o){return hz(t,n,e,o)}),(function(r){vz(t,n,e,o,!0),hz(t,n,e,r)}))}))},yz=function(t,n,e){var o=t.element;e.setDocked(!1),gz(t,e).each((function(o){o.fold((function(){return pz(t,n,e)}),(function(o){return hz(t,n,e,o)}),h)})),e.setVisible(!0),n.contextual.each((function(n){fs(o,[n.fadeInClass,n.fadeOutClass,n.transitionClass]),n.onShow(t)})),xz(t,n,e)},xz=function(t,n,e){t.getSystem().isConnected()&&bz(t,n,e)},wz=function(t,n,e){e.isDocked()&&yz(t,n,e)},Sz=function(t,n,e){return e.isDocked()},kz=function(t,n,e,o){return e.setModes(o)},Cz=function(t,n,e){return e.getModes()},Oz=Object.freeze({__proto__:null,refresh:xz,reset:wz,isDocked:Sz,getModes:Cz,setModes:kz}),_z=function(t,n){return Ea([za(Xu(),(function(e,o){t.contextual.each((function(t){if(cs(e.element,t.transitionClass)){fs(e.element,[t.transitionClass,t.fadeInClass]);var r=n.isVisible()?t.onShown:t.onHidden;r(e)}o.stop()}))})),Ma(fa(),(function(e,o){xz(e,t,n)})),Ma(la(),(function(e,o){wz(e,t,n)}))])},Tz=Object.freeze({__proto__:null,events:_z}),Ez=[Xi("contextual",[Mi("fadeInClass"),Mi("fadeOutClass"),Mi("transitionClass"),Ri("lazyContext"),cf("onShow"),cf("onShown"),cf("onHide"),cf("onHidden")]),tu("lazyViewport",tr),eu("modes",["top","bottom"],si),cf("onDocked"),cf("onUndocked")],Dz=function(t){var n=ru(!1),e=ru(!0),o=rd(),r=ru(t.modes),i=function(){return"docked:  "+n.get()+", visible: "+e.get()+", modes: "+r.get().join(",")};return Oc({isDocked:n.get,setDocked:n.set,getInitialPos:o.get,setInitialPos:o.set,clearInitialPos:o.clear,isVisible:e.get,setVisible:e.set,getModes:r.get,setModes:r.set,readState:i})},Az=Object.freeze({__proto__:null,init:Dz}),Bz=pl({fields:Ez,name:"docking",active:Tz,apis:Oz,state:Az}),Mz=x(rc("toolbar-height-change")),Fz={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},Iz="tox-tinymce--toolbar-sticky-on",Rz="tox-tinymce--toolbar-sticky-off",Nz=function(t,n){var e=me(n),o=e.dom.defaultView.innerHeight,r=Ho(e),i=nn.fromDom(t.elm),u=Zo(i),a=uo(i),c=u.y,s=c+a,f=go(n),l=uo(n),d=f.top,m=d+l,g=Math.abs(d-r.top)<2,p=Math.abs(m-(r.top+o))<2;if(g&&c<m)Vo(r.left,c-l,e);else if(p&&s>d){var h=c-o+a+l;Vo(r.left,h,e)}},Pz=function(t,n){return U(Bz.getModes(t),n)},Hz=function(t){var n=function(t){return ao(t)+(parseInt($e(t,"margin-top"),10)||0)+(parseInt($e(t,"margin-bottom"),10)||0)},e=t.element;ve(e).each((function(o){var r="padding-"+Bz.getModes(t)[0];if(Bz.isDocked(t)){var i=bo(o);qe(e,"width",i+"px"),qe(o,r,n(e)+"px")}else eo(e,"width"),eo(o,r)}))},Vz=function(t,n){n?(as(t,Fz.fadeOutClass),ss(t,[Fz.transitionClass,Fz.fadeInClass])):(as(t,Fz.fadeInClass),ss(t,[Fz.fadeOutClass,Fz.transitionClass]))},Lz=function(t,n){var e=nn.fromDom(t.getContainer());n?(is(e,Iz),as(e,Rz)):(is(e,Rz),as(e,Iz))},zz=function(t,n){var e=me(n);_l(e).filter((function(t){return!se(n,t)})).filter((function(n){return se(n,nn.fromDom(e.dom.body))||de(t,n)})).each((function(){return kl(n)}))},Uz=function(t,n){return Tl(t).orThunk((function(){return n().toOptional().bind((function(t){return Tl(t.element)}))}))},jz=function(t,n,e){t.inline||(n.header.isPositionedAtTop()||t.on("ResizeEditor",(function(){e().each(Bz.reset)})),t.on("ResizeWindow ResizeEditor",(function(){e().each(Hz)})),t.on("SkinLoaded",(function(){e().each((function(t){Bz.isDocked(t)?Bz.reset(t):Bz.refresh(t)}))})),t.on("FullscreenStateChanged",(function(){e().each(Bz.reset)}))),t.on("AfterScrollIntoView",(function(t){e().each((function(n){Bz.refresh(n);var e=n.element;$b(e)&&Nz(t,e)}))})),t.on("PostRender",(function(){Lz(t,!1)}))},Wz=function(t){return t().map(Bz.isDocked).getOr(!1)},Gz=function(){var t;return[yl.config({channels:(t={},t[Mz()]={onReceive:Hz},t)})]},Xz=function(t,n){var e=rd(),o=n.getSink,r=function(t){o().each((function(n){return t(n.element)}))},i=function(n){t.inline||Hz(n),Lz(t,Bz.isDocked(n)),n.getSystem().broadcastOn([xp()],{}),o().each((function(t){return t.getSystem().broadcastOn([xp()],{})}))},u=t.inline?[]:Gz();return B([Jx.config({}),Bz.config({contextual:D({lazyContext:function(n){var e=ao(n.element),o=t.inline?t.getContentAreaContainer():t.getContainer(),r=Qo(nn.fromDom(o)),i=r.height-e,u=r.y+(Pz(n,"top")?0:e);return N.some($o(r.x,u,r.width,i))},onShow:function(){r((function(t){return Vz(t,!0)}))},onShown:function(t){r((function(t){return fs(t,[Fz.transitionClass,Fz.fadeInClass])})),e.get().each((function(n){zz(t.element,n),e.clear()}))},onHide:function(t){Uz(t.element,o).fold(e.clear,e.set),r((function(t){return Vz(t,!1)}))},onHidden:function(){r((function(t){return fs(t,[Fz.transitionClass])}))}},Fz),lazyViewport:function(n){var e=tr(),o=nT(t),r=e.y+(Pz(n,"top")?o:0),i=e.height-(Pz(n,"bottom")?o:0);return $o(e.x,r,e.width,i)},modes:[n.header.getDockingMode()],onDocked:i,onUndocked:i})],u,!0)},Yz=Object.freeze({__proto__:null,setup:jz,isDocked:Wz,getBehaviours:Xz}),qz=function(t){var n=t.editor,e=t.sticky?Xz:$L;return{uid:t.uid,dom:t.dom,components:t.components,behaviours:ml(e(n,t.sharedBackstage))}},Kz=ti([Mi("type"),Ai("items",ei([ii([Mi("name"),Vi("items",si)]),si]))].concat(FC)),Jz=function(t){return xi("GroupToolbarButton",Kz,t)},$z=[ji("text"),ji("tooltip"),ji("icon"),Ri("fetch"),tu("onSetup",(function(){return h}))],Qz=ti(B([Mi("type")],$z,!0)),Zz=function(t){return xi("menubutton",Qz,t)},tU=ti([Mi("type"),ji("tooltip"),ji("icon"),ji("text"),Wi("select"),Ri("fetch"),tu("onSetup",(function(){return h})),Qi("presets","normal",["normal","color","listpreview"]),qi("columns",1),Ri("onAction"),Ri("onItemAction")]),nU=function(t){return xi("SplitButton",tU,t)},eU=function(t,n){var e=function(t,e){var o=X(e,(function(t){var e={type:"menubutton",text:t.text,fetch:function(n){n(t.getItems())}},o=Zz(e).mapError((function(t){return ki(t)})).getOrDie();return eP(o,"tox-mbtn",n.backstage,N.some("menuitem"))}));Vx.set(t,o)},o={focus:Dx.focusIn,setMenus:e};return{uid:t.uid,dom:t.dom,components:[],behaviours:ml([Vx.config({}),zx("menubar-events",[Ua((function(n){t.onSetup(n)})),Ma(Pu(),(function(t,n){Hs(t.element,".tox-mbtn--active").each((function(e){Vs(n.event.target,".tox-mbtn").each((function(n){se(e,n)||t.getSystem().getByDom(e).each((function(e){t.getSystem().getByDom(n).each((function(t){XB.expand(t),XB.close(e),Jx.focus(t)}))}))}))}))})),Ma(ha(),(function(t,n){n.event.prevFocus.bind((function(n){return t.getSystem().getByDom(n).toOptional()})).each((function(e){n.event.newFocus.bind((function(n){return t.getSystem().getByDom(n).toOptional()})).each((function(t){XB.isOpen(e)&&(XB.expand(t),XB.close(e))}))}))}))]),Dx.config({mode:"flow",selector:".tox-mbtn",onEscape:function(n){return t.onEscape(n),N.some(!0)}}),EA.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},oU=dv({factory:eU,name:"silver.Menubar",configFields:[Di("dom"),Di("uid"),Di("onEscape"),Di("backstage"),qi("onSetup",h)],apis:{focus:function(t,n){t.focus(n)},setMenus:function(t,n,e){t.setMenus(n,e)}}}),rU=function(t,n){return n.getAnimationRoot.fold((function(){return t.element}),(function(n){return n(t)}))},iU=function(t){return t.dimension.property},uU=function(t,n){return t.dimension.getDimension(n)},aU=function(t,n){var e=rU(t,n);fs(e,[n.shrinkingClass,n.growingClass])},cU=function(t,n){as(t.element,n.openClass),is(t.element,n.closedClass),qe(t.element,iU(n),"0px"),oo(t.element)},sU=function(t,n){as(t.element,n.closedClass),is(t.element,n.openClass),eo(t.element,iU(n))},fU=function(t,n,e,o){e.setCollapsed(),qe(t.element,iU(n),uU(n,t.element)),oo(t.element),aU(t,n),cU(t,n),n.onStartShrink(t),n.onShrunk(t)},lU=function(t,n,e,o){var r=o.getOrThunk((function(){return uU(n,t.element)}));e.setCollapsed(),qe(t.element,iU(n),r),oo(t.element);var i=rU(t,n);as(i,n.growingClass),is(i,n.shrinkingClass),cU(t,n),n.onStartShrink(t)},dU=function(t,n,e){var o=uU(n,t.element),r="0px"===o?fU:lU;r(t,n,e,N.some(o))},mU=function(t,n,e){var o=rU(t,n),r=cs(o,n.shrinkingClass),i=uU(n,t.element);sU(t,n);var u=uU(n,t.element),a=function(){qe(t.element,iU(n),i),oo(t.element)},c=function(){cU(t,n)},s=r?a:c;s(),as(o,n.shrinkingClass),is(o,n.growingClass),sU(t,n),qe(t.element,iU(n),u),e.setExpanded(),n.onStartGrow(t)},gU=function(t,n,e){if(e.isExpanded()){eo(t.element,iU(n));var o=uU(n,t.element);qe(t.element,iU(n),o)}},pU=function(t,n,e){e.isExpanded()||mU(t,n,e)},hU=function(t,n,e){e.isExpanded()&&dU(t,n,e)},vU=function(t,n,e){e.isExpanded()&&fU(t,n,e)},bU=function(t,n,e){return e.isExpanded()},yU=function(t,n,e){return e.isCollapsed()},xU=function(t,n,e){var o=rU(t,n);return!0===cs(o,n.growingClass)},wU=function(t,n,e){var o=rU(t,n);return!0===cs(o,n.shrinkingClass)},SU=function(t,n,e){return xU(t,n)||wU(t,n)},kU=function(t,n,e){var o=e.isExpanded()?dU:mU;o(t,n,e)},CU=Object.freeze({__proto__:null,refresh:gU,grow:pU,shrink:hU,immediateShrink:vU,hasGrown:bU,hasShrunk:yU,isGrowing:xU,isShrinking:wU,isTransitioning:SU,toggleGrow:kU,disableTransitions:aU}),OU=function(t,n,e){var o=n.expanded;return Ac(o?{classes:[n.openClass],styles:{}}:{classes:[n.closedClass],styles:mu(n.dimension.property,"0px")})},_U=function(t,n){return Ea([za(Xu(),(function(e,o){var r=o.event.raw;if(r.propertyName===t.dimension.property){aU(e,t),n.isExpanded()&&eo(e.element,t.dimension.property);var i=n.isExpanded()?t.onGrown:t.onShrunk;i(e)}}))])},TU=Object.freeze({__proto__:null,exhibit:OU,events:_U}),EU=[Di("closedClass"),Di("openClass"),Di("shrinkingClass"),Di("growingClass"),Li("getAnimationRoot"),cf("onShrunk"),cf("onStartShrink"),cf("onGrown"),cf("onStartGrow"),qi("expanded",!1),Ai("dimension",Ci("property",{width:[df("property","width"),df("getDimension",(function(t){return bo(t)+"px"}))],height:[df("property","height"),df("getDimension",(function(t){return uo(t)+"px"}))]}))],DU=function(t){var n=ru(t.expanded),e=function(){return"expanded: "+n.get()};return Oc({isExpanded:function(){return!0===n.get()},isCollapsed:function(){return!1===n.get()},setCollapsed:k(n.set,!1),setExpanded:k(n.set,!0),readState:e})},AU=Object.freeze({__proto__:null,init:DU}),BU=pl({fields:EU,name:"sliding",active:TU,apis:CU,state:AU}),MU="container",FU=[nh("slotBehaviours",[])],IU=function(t){return"<alloy.field."+t+">"},RU=function(t){var n=function(){var t=[],n=function(n,e){return t.push(n),jh(MU,IU(n),e)};return{slot:n,record:x(t)}}(),e=t(n),o=n.record(),r=X(o,(function(t){return Fh({name:t,pname:IU(t)})}));return uv(MU,FU,r,NU,e)},NU=function(t,n){var e=function(n){return Qh(t)},o=function(n,e){return qh(n,t,e)},r=function(n,e){return function(o,r){return qh(o,t,r).map((function(t){return n(t,r)})).getOr(e)}},i=function(t){return function(n,e){Y(e,(function(e){return t(n,e)}))}},u=function(t,n){return"true"!==Ue(t.element,"aria-hidden")},a=function(t,n){if(!u(t)){var e=t.element;eo(e,"display"),Ge(e,"aria-hidden"),ka(t,va(),{name:n,visible:!0})}},c=function(t,n){if(u(t)){var e=t.element;qe(e,"display","none"),Le(e,"aria-hidden","true"),ka(t,va(),{name:n,visible:!1})}},s=r(u,!1),f=r(c),l=i(f),d=function(t){return l(t,e())},m=r(a),g={getSlotNames:e,getSlot:o,isShowing:s,hideSlot:f,hideAllSlots:d,showSlot:m};return{uid:t.uid,dom:t.dom,components:n,behaviours:eh(t.slotBehaviours),apis:g}},PU=bt({getSlotNames:function(t,n){return t.getSlotNames(n)},getSlot:function(t,n,e){return t.getSlot(n,e)},isShowing:function(t,n,e){return t.isShowing(n,e)},hideSlot:function(t,n,e){return t.hideSlot(n,e)},hideAllSlots:function(t,n){return t.hideAllSlots(n)},showSlot:function(t,n,e){return t.showSlot(n,e)}},(function(t){return kc(t)})),HU=D(D({},PU),{sketch:RU}),VU=ti([ji("icon"),ji("tooltip"),tu("onShow",h),tu("onHide",h),tu("onSetup",(function(){return h}))]),LU=function(t){return xi("sidebar",VU,t)},zU=function(t){var n=t.ui.registry.getAll().sidebars;Y(pt(n),(function(e){var o=n[e],r=function(){return Dt(N.from(t.queryCommandValue("ToggleSidebar")),e)};t.ui.registry.addToggleButton(e,{icon:o.icon,tooltip:o.tooltip,onAction:function(n){t.execCommand("ToggleSidebar",!1,e),n.setActive(r())},onSetup:function(n){var e=function(){return n.setActive(r())};return t.on("ToggleSidebar",e),function(){t.off("ToggleSidebar",e)}}})}))},UU=function(t){return{element:function(){return t.element.dom}}},jU=function(t,n){var e=X(pt(n),(function(t){var e=n[t],o=wi(LU(e));return{name:t,getApi:UU,onSetup:o.onSetup,onShow:o.onShow,onHide:o.onHide}}));return X(e,(function(n){var o=ru(h);return t.slot(n.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:a_.unnamedEvents([CT(n,o),OT(n,o),Ma(va(),(function(t,n){var o=n.event,r=tt(e,(function(t){return t.name===o.name}));r.each((function(n){var e=o.visible?n.onShow:n.onHide;e(n.getApi(t))}))}))])})}))},WU=function(t){return HU.sketch((function(n){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:jU(n,t),slotBehaviours:a_.unnamedEvents([Ua((function(t){return HU.hideAllSlots(t)}))])}}))},GU=function(t,n){var e=bv.getCurrent(t);e.each((function(t){return Vx.set(t,[WU(n)])}))},XU=function(t,n){var e=bv.getCurrent(t);e.each((function(t){var e=bv.getCurrent(t);e.each((function(e){BU.hasGrown(t)?HU.isShowing(e,n)?BU.shrink(t):(HU.hideAllSlots(e),HU.showSlot(e,n)):(HU.hideAllSlots(e),HU.showSlot(e,n),BU.grow(t))}))}))},YU=function(t){var n=bv.getCurrent(t);return n.bind((function(t){var n=BU.isGrowing(t)||BU.hasGrown(t);if(n){var e=bv.getCurrent(t);return e.bind((function(t){return tt(HU.getSlotNames(t),(function(n){return HU.isShowing(t,n)}))}))}return N.none()}))},qU=rc("FixSizeEvent"),KU=rc("AutoSizeEvent"),JU=function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:ml([EA.config({}),Jx.config({}),BU.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(t){var n=bv.getCurrent(t);n.each(HU.hideAllSlots),Sa(t,KU)},onGrown:function(t){Sa(t,KU)},onStartGrow:function(t){ka(t,qU,{width:Ze(t.element,"width").getOr("")})},onStartShrink:function(t){ka(t,qU,{width:bo(t.element)+"px"})}}),Vx.config({}),bv.config({find:function(t){var n=Vx.contents(t);return lt(n)}})])}],behaviours:ml([ZI.childAt(0),zx("sidebar-sliding-events",[Ma(qU,(function(t,n){qe(t.element,"width",n.event.width)})),Ma(KU,(function(t,n){eo(t.element,"width")}))])])}},$U=function(t,n,e,o){Le(t.element,"aria-busy",!0);var r=n.getRoot(t).getOr(t),i=ml([Dx.config({mode:"special",onTab:function(){return N.some(!0)},onShiftTab:function(){return N.some(!0)}}),Jx.config({})]),u=o(r,i),a=r.getSystem().build(u);Vx.append(r,Es(a)),a.hasConfigured(Dx)&&n.focus&&Dx.focusIn(a),e.isBlocked()||n.onBlock(t),e.blockWith((function(){return Vx.remove(r,a)}))},QU=function(t,n,e){Ge(t.element,"aria-busy"),e.isBlocked()&&n.onUnblock(t),e.clear()},ZU=Object.freeze({__proto__:null,block:$U,unblock:QU}),tj=[tu("getRoot",N.none),Zi("focus",!0),cf("onBlock"),cf("onUnblock")],nj=function(){var t=nd(),n=function(n){t.set({destroy:n})};return Oc({readState:t.isSet,blockWith:n,clear:t.clear,isBlocked:t.isSet})},ej=Object.freeze({__proto__:null,init:nj}),oj=pl({fields:tj,name:"blocking",apis:ZU,state:ej}),rj=function(t){return function(n,e){return{dom:{tag:"div",attributes:{"aria-label":t.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:_S('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}}},ij=function(t){return bv.getCurrent(t).each((function(t){return kl(t.element)}))},uj=function(t,n){var e="tabindex",o="data-mce-"+e;N.from(t.iframeElement).map(nn.fromDom).each((function(t){n?(je(t,e).each((function(n){return Le(t,o,n)})),Le(t,e,-1)):(Ge(t,e),je(t,o).each((function(n){Le(t,e,n),Ge(t,o)})))}))},aj=function(t,n,e,o){var r=n.element;if(uj(t,e),e)oj.block(n,rj(o)),eo(r,"display"),Ge(r,"aria-hidden"),t.hasFocus()&&ij(n);else{var i=bv.getCurrent(n).exists((function(t){return Ol(t.element)}));oj.unblock(n),qe(r,"display","none"),Le(r,"aria-hidden","true"),i&&t.focus()}},cj=function(t){return{uid:t.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:ml([Vx.config({}),oj.config({focus:!1}),bv.config({find:function(t){return lt(t.components())}})]),components:[]}},sj=function(t){return"focusin"===t.type},fj=function(t){if(sj(t)){var n=t.composed?lt(t.composedPath()):N.from(t.target);return n.map(nn.fromDom).filter(ln).exists((function(t){return cs(t,"mce-pastebin")}))}return!1},lj=function(t,n,e){var o=ru(!1),r=rd(),i=function(e){o.get()&&!fj(e)&&(e.preventDefault(),ij(n()),t.editorManager.setActive(t))};t.inline||t.on("PreInit",(function(){t.dom.bind(t.getWin(),"focusin",i),t.on("BeforeExecCommand",(function(t){"mcefocus"===t.command.toLowerCase()&&!0!==t.value&&i(t)}))}));var u=function(r){r!==o.get()&&(o.set(r),aj(t,n(),r,e.providers),t.fire("AfterProgressState",{state:r}))};t.on("ProgressState",(function(n){if(r.on(wS.clearTimeout),g(n.time)){var e=wS.setEditorTimeout(t,(function(){return u(n.state)}),n.time);r.set(e)}else u(n.state),r.clear()}))},dj=function(t,n){var e={len:0,list:[]},o=Q(t,(function(t,e){var o=n(e,t.len);return o.fold(x(t),(function(n){return{len:n.finish,list:t.list.concat([n])}}))}),e);return o.list},mj=function(t,n,e){return{within:t,extra:n,withinWidth:e}},gj=function(t,n,e){var o=dj(t,(function(t,n){var o=e(t);return N.some({element:t,start:n,finish:n+o,width:o})})),r=J(o,(function(t){return t.finish<=n})),i=$(r,(function(t,n){return t+n.width}),0),u=o.slice(r.length);return{within:r,extra:u,withinWidth:i}},pj=function(t){return X(t,(function(t){return t.element}))},hj=function(t,n,e){var o=pj(t.concat(n));return mj(o,[],e)},vj=function(t,n,e,o){var r=pj(t).concat([e]);return mj(r,pj(n),o)},bj=function(t,n,e){return mj(pj(t),[],e)},yj=function(t,n,e){var o=gj(n,t,e);return 0===o.extra.length?N.some(o):N.none()},xj=function(t,n,e,o){var r=yj(t,n,e).getOrThunk((function(){return gj(n,t-e(o),e)})),i=r.within,u=r.extra,a=r.withinWidth;return 1===u.length&&u[0].width<=e(o)?hj(i,u,a):u.length>=1?vj(i,u,o,a):bj(i,u,a)},wj=function(t,n){var e=X(n,(function(t){return Es(t)}));qL.setGroups(t,e)},Sj=function(t){return gt(t,(function(t){return Tl(t.element).bind((function(n){return t.getSystem().getByDom(n).toOptional()}))}))},kj=function(t,n,e){var o=n.builtGroups.get();if(0!==o.length){var r=Kh(t,n,"primary"),i=TB.getCoupled(t,"overflowGroup");qe(r.element,"visibility","hidden");var u=o.concat([i]),a=Sj(u);e([]),wj(r,u);var c=bo(r.element),s=xj(c,n.builtGroups.get(),(function(t){return bo(t.element)}),i);0===s.extra.length?(Vx.remove(r,i),e([])):(wj(r,s.within),e(s.extra)),eo(r.element,"visibility"),oo(r.element),a.each(Jx.focus)}},Cj=x([nh("splitToolbarBehaviours",[TB]),Ti("builtGroups",(function(){return ru([])}))]),Oj=x([uf(["overflowToggledClass"]),Wi("getOverflowBounds"),Di("lazySink"),Ti("overflowGroups",(function(){return ru([])}))].concat(Cj())),_j=x([Fh({factory:qL,schema:WL(),name:"primary"}),Ih({schema:WL(),name:"overflow"}),Ih({name:"overflow-button"}),Ih({name:"overflow-group"})]),Tj=x((function(t,n){xo(t,Math.floor(n))})),Ej=x([uf(["toggledClass"]),Di("lazySink"),Ri("fetch"),Wi("getBounds"),Xi("fireDismissalEventInstead",[qi("event",ga())]),Vd()]),Dj=x([Ih({name:"button",overrides:function(t){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:ml([pw.config({toggleClass:t.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),Ih({factory:qL,schema:WL(),name:"toolbar",overrides:function(t){return{toolbarBehaviours:ml([Dx.config({mode:"cyclic",onEscape:function(n){return qh(n,t,"button").each(Jx.focus),N.none()}})])}}})]),Aj=function(t,n){var e=TB.getCoupled(t,"toolbarSandbox");bp.isOpen(e)?bp.close(e):bp.open(e,n.toolbar())},Bj=function(t,n,e,o){var r=e.getBounds.map((function(t){return t()})),i=e.lazySink(t).getOrDie();Hg.positionWithinBounds(i,n,{anchor:{type:"hotspot",hotspot:t,layouts:o,overrides:{maxWidthFunction:Tj()}}},r)},Mj=function(t,n,e,o,r){qL.setGroups(n,r),Bj(t,n,e,o),pw.on(t)},Fj=function(t,n,e){var o=zs(),r=function(r,i){e.fetch().get((function(r){Mj(t,i,e,n.layouts,r),o.link(t.element),Dx.focusIn(i)}))},i=function(){pw.off(t),Jx.focus(t),o.unlink(t.element)};return{dom:{tag:"div",attributes:{id:o.id}},behaviours:ml([Dx.config({mode:"special",onEscape:function(t){return bp.close(t),N.some(!0)}}),bp.config({onOpen:r,onClose:i,isPartOf:function(n,e,o){return js(e,o)||js(t,o)},getAttachPoint:function(){return e.lazySink(t).getOrDie()}}),yl.config({channels:D(D({},kp(D({isExtraPart:_},e.fireDismissalEventInstead.map((function(t){return{fireEventInstead:{event:t.event}}})).getOr({})))),Op({doReposition:function(){bp.getState(TB.getCoupled(t,"toolbarSandbox")).each((function(o){Bj(t,o,e,n.layouts)}))}}))})])}},Ij=function(t,n,e,o){return D(D({},kS.sketch(D(D({},o.button()),{action:function(t){Aj(t,o)},buttonBehaviours:rh.augment({dump:o.button().buttonBehaviours},[TB.config({others:{toolbarSandbox:function(n){return Fj(n,e,t)}}})])}))),{apis:{setGroups:function(n,o){bp.getState(TB.getCoupled(n,"toolbarSandbox")).each((function(r){Mj(n,r,t,e.layouts,o)}))},reposition:function(n){bp.getState(TB.getCoupled(n,"toolbarSandbox")).each((function(o){Bj(n,o,t,e.layouts)}))},toggle:function(t){Aj(t,o)},getToolbar:function(t){return bp.getState(TB.getCoupled(t,"toolbarSandbox"))},isOpen:function(t){return bp.isOpen(TB.getCoupled(t,"toolbarSandbox"))}}})},Rj=mv({name:"FloatingToolbarButton",factory:Ij,configFields:Ej(),partFields:Dj(),apis:{setGroups:function(t,n,e){t.setGroups(n,e)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},getToolbar:function(t,n){return t.getToolbar(n)},isOpen:function(t,n){return t.isOpen(n)}}}),Nj=x([Di("items"),uf(["itemSelector"]),nh("tgroupBehaviours",[Dx])]),Pj=x([Nh({name:"items",unit:"item"})]),Hj=function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,behaviours:oh(t.tgroupBehaviours,[Dx.config({mode:"flow",selector:t.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}},Vj=mv({name:"ToolbarGroup",configFields:Nj(),partFields:Pj(),factory:Hj}),Lj=function(t){return X(t,(function(t){return Es(t)}))},zj=function(t,n,e){kj(t,e,(function(o){e.overflowGroups.set(o),n.getOpt(t).each((function(t){Rj.setGroups(t,Lj(o))}))}))},Uj=function(t,n,e,o){var r=TS(Rj.sketch({fetch:function(){return sB.nu((function(n){n(Lj(t.overflowGroups.get()))}))},layouts:{onLtr:function(){return[jf,Uf]},onRtl:function(){return[Uf,jf]},onBottomLtr:function(){return[Gf,Wf]},onBottomRtl:function(){return[Wf,Gf]}},getBounds:e.getOverflowBounds,lazySink:t.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:t.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:t.uid,dom:t.dom,components:n,behaviours:oh(t.splitToolbarBehaviours,[TB.config({others:{overflowGroup:function(){return Vj.sketch(D(D({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(n,e){t.builtGroups.set(X(e,n.getSystem().build)),zj(n,r,t)},refresh:function(n){return zj(n,r,t)},toggle:function(t){r.getOpt(t).each((function(t){Rj.toggle(t)}))},isOpen:function(t){return r.getOpt(t).map(Rj.isOpen).getOr(!1)},reposition:function(t){r.getOpt(t).each((function(t){Rj.reposition(t)}))},getOverflow:function(t){return r.getOpt(t).bind(Rj.getToolbar)}},domModification:{attributes:{role:"group"}}}},jj=mv({name:"SplitFloatingToolbar",configFields:Oj(),partFields:_j(),factory:Uj,apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)},getOverflow:function(t,n){return t.getOverflow(n)}}}),Wj=x([uf(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),cf("onOpened"),cf("onClosed")].concat(Cj())),Gj=x([Fh({factory:qL,schema:WL(),name:"primary"}),Fh({factory:qL,schema:WL(),name:"overflow",overrides:function(t){return{toolbarBehaviours:ml([BU.config({dimension:{property:"height"},closedClass:t.markers.closedClass,openClass:t.markers.openClass,shrinkingClass:t.markers.shrinkingClass,growingClass:t.markers.growingClass,onShrunk:function(n){qh(n,t,"overflow-button").each((function(t){pw.off(t),Jx.focus(t)})),t.onClosed(n)},onGrown:function(n){Dx.focusIn(n),t.onOpened(n)},onStartGrow:function(n){qh(n,t,"overflow-button").each(pw.on)}}),Dx.config({mode:"acyclic",onEscape:function(n){return qh(n,t,"overflow-button").each(Jx.focus),N.some(!0)}})])}}}),Ih({name:"overflow-button",overrides:function(t){return{buttonBehaviours:ml([pw.config({toggleClass:t.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),Ih({name:"overflow-group"})]),Xj=function(t,n){return qh(t,n,"overflow").map(BU.hasGrown).getOr(!1)},Yj=function(t,n){qh(t,n,"overflow-button").bind((function(){return qh(t,n,"overflow")})).each((function(e){qj(t,n),BU.toggleGrow(e)}))},qj=function(t,n){qh(t,n,"overflow").each((function(e){kj(t,n,(function(t){var n=X(t,(function(t){return Es(t)}));qL.setGroups(e,n)})),qh(t,n,"overflow-button").each((function(t){BU.hasGrown(e)&&pw.on(t)})),BU.refresh(e)}))},Kj=function(t,n,e,o){var r="alloy.toolbar.toggle",i=function(n,e){var o=X(e,n.getSystem().build);t.builtGroups.set(o)};return{uid:t.uid,dom:t.dom,components:n,behaviours:oh(t.splitToolbarBehaviours,[TB.config({others:{overflowGroup:function(t){return Vj.sketch(D(D({},o["overflow-group"]()),{items:[kS.sketch(D(D({},o["overflow-button"]()),{action:function(n){Sa(t,r)}}))]}))}}}),zx("toolbar-toggle-events",[Ma(r,(function(n){Yj(n,t)}))])]),apis:{setGroups:function(n,e){i(n,e),qj(n,t)},refresh:function(n){return qj(n,t)},toggle:function(n){return Yj(n,t)},isOpen:function(n){return Xj(n,t)}},domModification:{attributes:{role:"group"}}}},Jj=mv({name:"SplitSlidingToolbar",configFields:Wj(),partFields:Gj(),factory:Kj,apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)}}}),$j=function(t){var n=t.title.fold((function(){return{}}),(function(t){return{attributes:{title:t}}}));return{dom:D({tag:"div",classes:["tox-toolbar__group"]},n),components:[Vj.parts.items({})],items:t.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:ml([EA.config({}),Jx.config({})])}},Qj=function(t){return Vj.sketch($j(t))},Zj=function(t,n){var e=Ua((function(n){var e=X(t.initGroups,Qj);qL.setGroups(n,e)}));return ml([ST.toolbarButton(t.providers.isDisabled),vT(),Dx.config({mode:n,onEscape:t.onEscape,selector:".tox-toolbar__group"}),zx("toolbar-events",[e])])},tW=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return{uid:t.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":$j({title:N.none(),items:[]}),"overflow-button":iP({name:"more",icon:N.some("more-drawer"),disabled:!1,tooltip:N.some("More..."),primary:!1,borderless:!1},N.none(),t.providers)},splitToolbarBehaviours:Zj(t,n)}},nW=function(t){var n=tW(t),e=4,o=jj.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return jj.sketch(D(D({},n),{lazySink:t.getSink,getOverflowBounds:function(){var n=t.moreDrawerData.lazyHeader().element,o=Zo(n),r=pe(n),i=Zo(r),u=Math.max(r.dom.scrollHeight,i.height);return $o(o.x+e,i.y,o.width-2*e,u)},parts:D(D({},n.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:t.attributes}}}),components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))},eW=function(t){var n=Jj.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=Jj.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=tW(t);return Jj.sketch(D(D({},o),{components:[n,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(t){t.getSystem().broadcastOn([Mz()],{type:"opened"})},onClosed:function(t){t.getSystem().broadcastOn([Mz()],{type:"closed"})}}))},oW=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return qL.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(t.type===$C.scrolling?["tox-toolbar--scrolling"]:[])},components:[qL.parts.groups({})],toolbarBehaviours:Zj(t,n)})},rW=function(t,n,e){var o={getSocket:function(n){return RL.getPart(n,t,"socket")},setSidebar:function(n,e){RL.getPart(n,t,"sidebar").each((function(t){return GU(t,e)}))},toggleSidebar:function(n,e){RL.getPart(n,t,"sidebar").each((function(t){return XU(t,e)}))},whichSidebar:function(n){return RL.getPart(n,t,"sidebar").bind(YU).getOrNull()},getHeader:function(n){return RL.getPart(n,t,"header")},getToolbar:function(n){return RL.getPart(n,t,"toolbar")},setToolbar:function(n,e){RL.getPart(n,t,"toolbar").each((function(t){t.getApis().setGroups(t,e)}))},setToolbars:function(n,e){RL.getPart(n,t,"multiple-toolbar").each((function(t){jL.setItems(t,e)}))},refreshToolbar:function(n){var e=RL.getPart(n,t,"toolbar");e.each((function(t){return t.getApis().refresh(t)}))},toggleToolbarDrawer:function(n){RL.getPart(n,t,"toolbar").each((function(t){Rt(t.getApis().toggle,(function(n){return n(t)}))}))},isToolbarDrawerToggled:function(n){return RL.getPart(n,t,"toolbar").bind((function(t){return N.from(t.getApis().isOpen).map((function(n){return n(t)}))})).getOr(!1)},getThrobber:function(n){return RL.getPart(n,t,"throbber")},focusToolbar:function(n){var e=RL.getPart(n,t,"toolbar").orThunk((function(){return RL.getPart(n,t,"multiple-toolbar")}));e.each((function(t){Dx.focusIn(t)}))},setMenubar:function(n,e){RL.getPart(n,t,"menubar").each((function(t){oU.setMenus(t,e)}))},focusMenubar:function(n){RL.getPart(n,t,"menubar").each((function(t){oU.focus(t)}))}};return{uid:t.uid,dom:t.dom,components:n,apis:o,behaviours:t.behaviours}},iW=NL.optional({factory:oU,name:"menubar",schema:[Di("backstage")]}),uW=function(t){return t.type===$C.sliding?eW:t.type===$C.floating?nW:oW},aW=NL.optional({factory:{sketch:function(t){return jL.sketch({uid:t.uid,dom:t.dom,listBehaviours:ml([Dx.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return oW({type:t.type,uid:rc("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:t.providers,onEscape:function(){return t.onEscape(),N.some(!0)}})},setupItem:function(t,n,e,o){qL.setGroups(n,e)},shell:!0})}},name:"multiple-toolbar",schema:[Di("dom"),Di("onEscape")]}),cW=NL.optional({factory:{sketch:function(t){var n=uW(t),e={type:t.type,uid:t.uid,onEscape:function(){return t.onEscape(),N.some(!0)},cyclicKeying:!1,initGroups:[],getSink:t.getSink,providers:t.providers,moreDrawerData:{lazyToolbar:t.lazyToolbar,lazyMoreButton:t.lazyMoreButton,lazyHeader:t.lazyHeader},attributes:t.attributes};return n(e)}},name:"toolbar",schema:[Di("dom"),Di("onEscape"),Di("getSink")]}),sW=NL.optional({factory:{sketch:qz},name:"header",schema:[Di("dom")]}),fW=NL.optional({name:"socket",schema:[Di("dom")]}),lW=NL.optional({factory:{sketch:JU},name:"sidebar",schema:[Di("dom")]}),dW=NL.optional({factory:{sketch:cj},name:"throbber",schema:[Di("dom")]}),mW=mv({name:"OuterContainer",factory:rW,configFields:[Di("dom"),Di("behaviours")],partFields:[sW,iW,cW,aW,fW,lW,dW],apis:{getSocket:function(t,n){return t.getSocket(n)},setSidebar:function(t,n,e){t.setSidebar(n,e)},toggleSidebar:function(t,n,e){t.toggleSidebar(n,e)},whichSidebar:function(t,n){return t.whichSidebar(n)},getHeader:function(t,n){return t.getHeader(n)},getToolbar:function(t,n){return t.getToolbar(n)},setToolbar:function(t,n,e){var o=X(e,(function(t){return Qj(t)}));t.setToolbar(n,o)},setToolbars:function(t,n,e){var o=X(e,(function(t){return X(t,Qj)}));t.setToolbars(n,o)},refreshToolbar:function(t,n){return t.refreshToolbar(n)},toggleToolbarDrawer:function(t,n){t.toggleToolbarDrawer(n)},isToolbarDrawerToggled:function(t,n){return t.isToolbarDrawerToggled(n)},getThrobber:function(t,n){return t.getThrobber(n)},setMenubar:function(t,n,e){t.setMenubar(n,e)},focusMenubar:function(t,n){t.focusMenubar(n)},focusToolbar:function(t,n){t.focusToolbar(n)}}}),gW="file edit view insert format tools table help",pW={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},hW=function(t,n,e){var o=N_(e).split(/[ ,]/);return{text:t.title,getItems:function(){return ot(t.items,(function(t){var e=t.toLowerCase();return 0===e.trim().length||j(o,(function(t){return t===e}))?[]:"separator"===e||"|"===e?[{type:"separator"}]:n.menuItems[e]?[n.menuItems[e]]:[]}))}}},vW=function(t){return"string"===typeof t?t.split(" "):t},bW=function(t,n){var e=D(D({},pW),n.menus),o=pt(n.menus).length>0,r=void 0===n.menubar||!0===n.menubar?vW(gW):vW(!1===n.menubar?"":n.menubar),i=J(r,(function(t){var e=Tt(pW,t);return o?e||_t(n.menus,t).exists((function(t){return Tt(t,"items")})):e})),u=X(i,(function(o){var r=e[o];return hW({title:r.title,items:vW(r.items)},n,t)}));return J(u,(function(t){var n=function(t){return"separator"!==t.type};return t.getItems().length>0&&j(t.getItems(),n)}))},yW=function(t){var n=function(){t._skinLoaded=!0,CE(t)};return function(){t.initialized?n():t.on("init",n)}},xW=function(t,n){return function(){return OE(t,{message:n})}},wW=function(t,n,e){return new pC((function(o,r){e.load(n,o,r),t.on("remove",(function(){return e.unload(n)}))}))},SW=function(t,n){var e=n+"/skin.min.css";return wW(t,e,t.ui.styleSheetLoader)},kW=function(t,n){var e=Be(nn.fromDom(t.getElement()));if(e){var o=n+"/skin.shadowdom.min.css";return wW(t,o,w_.DOM.styleSheetLoader)}return pC.resolve()},CW=function(t,n){var e=k_(n);e&&n.contentCSS.push(e+(t?"/content.inline":"/content")+".min.css"),!1===O_(n)&&i(e)?pC.all([SW(n,e),kW(n,e)]).then(yW(n),xW(n,"Skin could not be loaded")):yW(n)()},OW=k(CW,!1),_W=k(CW,!0),TW=function(t,n){return function(e){var o=ed(),r=function(){e.setActive(t.formatter.match(n));var r=t.formatter.formatChanged(n,e.setActive);o.set(r)};return t.initialized?r():t.once("init",r),function(){t.off("init",r),o.clear()}}},EW=function(t,n,e){return function(o){var r=function(){return e(o)},i=function(){e(o),t.on(n,r)};return t.initialized?i():t.once("init",i),function(){t.off("init",i),t.off(n,r)}}},DW=function(t){return function(n){return function(){t.undoManager.transact((function(){t.focus(),t.execCommand("mceToggleFormat",!1,n.format)}))}}},AW=function(t,n){return function(){return t.execCommand(n)}},BW=function(t,n,e){var o=function(t,o,i,u){var a=n.shared.providers.translate(t.title);if("separator"===t.type)return N.some({type:"separator",text:a});if("submenu"===t.type){var c=ot(t.getStyleItems(),(function(t){return r(t,o,u)}));return 0===o&&c.length<=0?N.none():N.some({type:"nestedmenuitem",text:a,disabled:c.length<=0,getSubmenuItems:function(){return ot(t.getStyleItems(),(function(t){return r(t,o,u)}))}})}return N.some(D({type:"togglemenuitem",text:a,icon:t.icon,active:t.isSelected(u),disabled:i,onAction:e.onAction(t)},t.getStylePreview().fold((function(){return{}}),(function(t){return{meta:{style:t}}}))))},r=function(t,n,r){var i="formatter"===t.type&&e.isInvalid(t);return 0===n?i?[]:o(t,n,!1,r).toArray():o(t,n,i,r).toArray()},i=function(t){var n=e.getCurrentValue(),o=e.shouldHide?0:1;return ot(t,(function(t){return r(t,o,n)}))},u=function(t,n){return function(e,o){var r=n(),u=i(r),a=tP(u,QC.CLOSE_ON_EXECUTE,t,!1);o(a)}};return{validateItems:i,getFetch:u}},MW=function(t,n,e){var o=e.dataset,r="basic"===o.type?function(){return X(o.data,(function(t){return zV(t,e.isSelectedFor,e.getPreviewFor)}))}:o.getData;return{items:BW(t,n,e),getStyleItems:r}},FW=function(t,n,e){var o=MW(t,n,e),r=o.items,i=o.getStyleItems,u=function(t){return{getComponent:x(t)}},a=EW(t,"NodeChange",(function(t){var n=t.getComponent();e.updateText(n)}));return WN({text:e.icon.isSome()?N.none():e.text,icon:e.icon,tooltip:N.from(e.tooltip),role:N.none(),fetch:r.getFetch(n,i),onSetup:a,getApi:u,columns:1,presets:"normal",classes:e.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",n.shared)},IW=function(t){return X(t,(function(t){var n=t,e=t,o=t.split("=");return o.length>1&&(n=o[0],e=o[1]),{title:n,format:e}}))},RW=function(t){return{type:"basic",data:t}};(function(t){t[t["SemiColon"]=0]="SemiColon",t[t["Space"]=1]="Space"})(rH||(rH={}));var NW,PW=function(t,n){return n===rH.SemiColon?t.replace(/;$/,"").split(";"):t.split(" ")},HW=function(t,n,e,o){var r=t.getParam(n,e,"string"),i=IW(PW(r,o));return{type:"basic",data:i}},VW=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],LW=function(t){var n=function(){return tt(VW,(function(n){return t.formatter.match(n.format)}))},e=function(n){return function(){return t.formatter.match(n)}},o=function(t){return N.none},r=function(t){var e=n(),o=e.fold(x("left"),(function(t){return t.title.toLowerCase()}));ka(t,jN,{icon:"align-"+o})},i=RW(VW),u=function(n){return function(){return tt(VW,(function(t){return t.format===n.format})).each((function(n){return t.execCommand(n.command)}))}};return{tooltip:"Align",text:N.none(),icon:N.some("align-left"),isSelectedFor:e,getCurrentValue:N.none,getPreviewFor:o,onAction:u,updateText:r,dataset:i,shouldHide:!1,isInvalid:function(n){return!t.formatter.canApply(n.format)}}},zW=function(t,n){return FW(t,n,LW(t))},UW=function(t,n){var e=MW(t,n,LW(t));t.ui.registry.addNestedMenuItem("align",{text:n.shared.providers.translate("Align"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})},jW="Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",WW=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],GW=function(t){var n=t.split(/\s*,\s*/);return X(n,(function(t){return t.replace(/^['"]+|['"]+$/g,"")}))},XW=function(t){var n=function(){var n=GW(t.toLowerCase());return rt(WW,(function(t){return n.indexOf(t.toLowerCase())>-1}))};return 0===t.indexOf("-apple-system")&&n()},YW=function(t){var n="System Font",e=function(){var e=function(t){return t?GW(t)[0]:""},o=t.queryCommandValue("FontName"),r=c.data,i=o?o.toLowerCase():"",u=tt(r,(function(t){var n=t.format;return n.toLowerCase()===i||e(n).toLowerCase()===e(i).toLowerCase()})).orThunk((function(){return Nt(XW(i),{title:n,format:i})}));return{matchOpt:u,font:o}},o=function(t){return function(n){return n.exists((function(n){return n.format===t}))}},r=function(){var t=e().matchOpt;return t},i=function(t){return function(){return N.some({tag:"div",styles:-1===t.indexOf("dings")?{"font-family":t}:{}})}},u=function(n){return function(){t.undoManager.transact((function(){t.focus(),t.execCommand("FontName",!1,n.format)}))}},a=function(t){var n=e(),o=n.matchOpt,r=n.font,i=o.fold(x(r),(function(t){return t.title}));ka(t,UN,{text:i})},c=HW(t,"font_formats",jW,rH.SemiColon);return{tooltip:"Fonts",text:N.some(n),icon:N.none(),isSelectedFor:o,getCurrentValue:r,getPreviewFor:i,onAction:u,updateText:a,dataset:c,shouldHide:!1,isInvalid:_}},qW=function(t,n){return FW(t,n,YW(t))},KW=function(t,n){var e=MW(t,n,YW(t));t.ui.registry.addNestedMenuItem("fontformats",{text:n.shared.providers.translate("Fonts"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})},JW="8pt 10pt 12pt 14pt 18pt 24pt 36pt",$W={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},QW={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},ZW=function(t,n){var e=Math.pow(10,n);return Math.round(t*e)/e},tG=function(t,n){return/[0-9.]+px$/.test(t)?ZW(72*parseInt(t,10)/96,n||0)+"pt":_t(QW,t).getOr(t)},nG=function(t){return _t($W,t).getOr("")},eG=function(t){var n=function(){var n=N.none(),e=a.data,o=t.queryCommandValue("FontSize");if(o)for(var r=function(t){var r=tG(o,t),i=nG(r);n=tt(e,(function(t){return t.format===o||t.format===r||t.format===i}))},i=3;n.isNone()&&i>=0;i--)r(i);return{matchOpt:n,size:o}},e=function(t){return function(n){return n.exists((function(n){return n.format===t}))}},o=function(){var t=n().matchOpt;return t},r=x(N.none),i=function(n){return function(){t.undoManager.transact((function(){t.focus(),t.execCommand("FontSize",!1,n.format)}))}},u=function(t){var e=n(),o=e.matchOpt,r=e.size,i=o.fold(x(r),(function(t){return t.title}));ka(t,UN,{text:i})},a=HW(t,"fontsize_formats",JW,rH.Space);return{tooltip:"Font sizes",text:N.some("12pt"),icon:N.none(),isSelectedFor:e,getPreviewFor:r,getCurrentValue:o,onAction:i,updateText:u,dataset:a,shouldHide:!1,isInvalid:_}},oG=function(t,n){return FW(t,n,eG(t))},rG=function(t,n){var e=MW(t,n,eG(t));t.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})},iG=function(t,n){var e=n(),o=X(e,(function(t){return t.format}));return N.from(t.formatter.closest(o)).bind((function(t){return tt(e,(function(n){return n.format===t}))})).orThunk((function(){return Nt(t.formatter.match("p"),{title:"Paragraph",format:"p"})}))},uG="Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",aG=function(t){var n="Paragraph",e=function(n){return function(){return t.formatter.match(n)}},o=function(n){return function(){var e=t.formatter.get(n);return N.some({tag:e.length>0&&(e[0].inline||e[0].block)||"div",styles:t.dom.parseStyle(t.formatter.getCssText(n))})}},r=function(e){var o=iG(t,(function(){return i.data})),r=o.fold(x(n),(function(t){return t.title}));ka(e,UN,{text:r})},i=HW(t,"block_formats",uG,rH.SemiColon);return{tooltip:"Blocks",text:N.some(n),icon:N.none(),isSelectedFor:e,getCurrentValue:N.none,getPreviewFor:o,onAction:DW(t),updateText:r,dataset:i,shouldHide:!1,isInvalid:function(n){return!t.formatter.canApply(n.format)}}},cG=function(t,n){return FW(t,n,aG(t))},sG=function(t,n){var e=MW(t,n,aG(t));t.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})},fG=function(t,n){var e="Paragraph",o=function(n){return function(){return t.formatter.match(n)}},r=function(n){return function(){var e=t.formatter.get(n);return void 0!==e?N.some({tag:e.length>0&&(e[0].inline||e[0].block)||"div",styles:t.dom.parseStyle(t.formatter.getCssText(n))}):N.none()}},i=function(n){var o=function(t){var n=t.items;return void 0!==n&&n.length>0?ot(n,o):[{title:t.title,format:t.format}]},r=ot(LV(t),o),i=iG(t,x(r)),u=i.fold(x(e),(function(t){return t.title}));ka(n,UN,{text:u})};return{tooltip:"Formats",text:N.some(e),icon:N.none(),isSelectedFor:o,getCurrentValue:N.none,getPreviewFor:r,onAction:DW(t),updateText:i,shouldHide:t.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(n){return!t.formatter.canApply(n.format)},dataset:n}},lG=function(t,n){var e=D({type:"advanced"},n.styleselect);return FW(t,n,fG(t,e))},dG=function(t,n){var e=D({type:"advanced"},n.styleselect),o=MW(t,n,fG(t,e));t.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return o.items.validateItems(o.getStyleItems())}})},mG=function(t,n){var e=function(e,o){t.updateState.each((function(t){var r=t(e,o);n.set(r)})),t.renderComponents.each((function(t){var r=t(o,n.get()),i=X(r,e.getSystem().build);jg(e,i)}))};return Ea([Ma(ta(),(function(n,o){var r=o;if(!r.universal){var i=t.channel;U(r.channels,i)&&e(n,r.data)}})),Ua((function(n,o){t.initialData.each((function(t){e(n,t)}))}))])},gG=Object.freeze({__proto__:null,events:mG}),pG=function(t,n,e){return e},hG=Object.freeze({__proto__:null,getState:pG}),vG=[Di("channel"),Li("renderComponents"),Li("updateState"),Li("initialData")],bG=function(){var t=ru(N.none()),n=function(){return t.set(N.none())},e=function(){return t.get().getOr("none")};return{readState:e,get:t.get,set:t.set,clear:n}},yG=Object.freeze({__proto__:null,init:bG}),xG=pl({fields:vG,name:"reflecting",active:gG,apis:hG,state:yG}),wG=x([Di("toggleClass"),Di("fetch"),ff("onExecute"),qi("getHotspot",N.some),qi("getAnchorOverrides",x({})),Vd(),ff("onItemExecute"),Li("lazySink"),Di("dom"),cf("onOpen"),nh("splitDropdownBehaviours",[TB,Dx,Jx]),qi("matchWidth",!1),qi("useMinWidth",!1),qi("eventOrder",{}),Li("role")].concat(UB())),SG=Fh({factory:kS,schema:[Di("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:ml([Jx.revoke()])}},overrides:function(t){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(t.uid).each(Ca)},buttonBehaviours:ml([pw.config({toggleOnExecute:!1,toggleClass:t.toggleClass})])}}}),kG=Fh({factory:kS,schema:[Di("dom")],name:"button",defaults:function(){return{buttonBehaviours:ml([Jx.revoke()])}},overrides:function(t){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(t.uid).each((function(e){t.onExecute(e,n)}))}}}}),CG=x([SG,kG,Rh({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[Di("text")],name:"aria-descriptor"}),Ih({schema:[rf()],name:"menu",defaults:function(t){return{onExecute:function(n,e){n.getSystem().getByUid(t.uid).each((function(o){t.onItemExecute(o,n,e)}))}}}}),DB()]),OG=function(t,n,e,o){var r,i=function(t){bv.getCurrent(t).each((function(t){ob.highlightFirst(t),Dx.focusIn(t)}))},u=function(n){var e=i;NB(t,w,n,o,e,tB.HighlightFirst).get(h)},a=function(t){return u(t),N.some(!0)},c=function(n){var e=Kh(n,t,"button");return Ca(e),N.some(!0)},s=D(D({},Ea([Ua((function(n,e){var o=qh(n,t,"aria-descriptor");o.each((function(t){var e=rc("aria");Le(t.element,"id",e),Le(n.element,"aria-describedby",e)}))}))])),vw(N.some(u))),f={repositionMenus:function(t){pw.isOn(t)&&zB(t)}};return{uid:t.uid,dom:t.dom,components:n,apis:f,eventOrder:D(D({},t.eventOrder),(r={},r[na()]=["disabling","toggling","alloy.base.behaviour"],r)),events:s,behaviours:oh(t.splitDropdownBehaviours,[TB.config({others:{sandbox:function(n){var e=Kh(n,t,"arrow"),o={onOpen:function(){pw.on(e),pw.on(n)},onClose:function(){pw.off(e),pw.off(n)}};return LB(t,n,o)}}}),Dx.config({mode:"special",onSpace:c,onEnter:c,onDown:a}),Jx.config({}),pw.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:t.role.getOr("button"),"aria-haspopup":!0}}}},_G=mv({name:"SplitDropdown",configFields:wG(),partFields:CG(),factory:OG,apis:{repositionMenus:function(t,n){return t.repositionMenus(n)}}}),TG=function(t){return{isDisabled:function(){return Pv.isDisabled(t)},setDisabled:function(n){return Pv.set(t,n)}}},EG=function(t){return{setActive:function(n){pw.set(t,n)},isActive:function(){return pw.isOn(t)},isDisabled:function(){return Pv.isDisabled(t)},setDisabled:function(n){return Pv.set(t,n)}}},DG=function(t,n){return t.map((function(t){return{"aria-label":n.translate(t),title:n.translate(t)}})).getOr({})},AG=rc("focus-button"),BG=function(t,n,e,o,r,i){var u;return{dom:{tag:"button",classes:["tox-tbtn"].concat(n.isSome()?["tox-tbtn--select"]:[]),attributes:DG(e,i)},components:ET([t.map((function(t){return NN(t,i.icons)})),n.map((function(t){return HN(t,"tox-tbtn",i)}))]),eventOrder:(u={},u[Fu()]=["focusing","alloy.base.behaviour","common-button-display-events"],u),buttonBehaviours:ml([ST.toolbarButton(i.isDisabled),vT(),zx("common-button-display-events",[Ma(Fu(),(function(t,n){n.event.prevent(),Sa(t,AG)}))])].concat(o.map((function(e){return xG.config({channel:e,initialData:{icon:t,text:n},renderComponents:function(t,n){return ET([t.icon.map((function(t){return NN(t,i.icons)})),t.text.map((function(t){return HN(t,"tox-tbtn",i)}))])}})})).toArray()).concat(r.getOr([])))}},MG=function(t,n,e,o){var r=n.shared;return Rj.sketch({lazySink:r.getSink,fetch:function(){return sB.nu((function(n){n(X(e(t.items),Qj))}))},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:BG(t.icon,t.text,t.tooltip,N.none(),N.none(),r.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:o}}}})},FG=function(t,n,e){var o=ru(h),r=BG(t.icon,t.text,t.tooltip,N.none(),N.none(),e);return kS.sketch({dom:r.dom,components:r.components,eventOrder:zN,buttonBehaviours:ml([zx("toolbar-button-events",[LN({onAction:t.onAction,getApi:n.getApi}),CT(n,o),OT(n,o)]),ST.toolbarButton((function(){return t.disabled||e.isDisabled()})),vT()].concat(n.toolbarButtonBehaviours))})},IG=function(t,n){return RG(t,n,[])},RG=function(t,n,e){return FG(t,{toolbarButtonBehaviours:[].concat(e.length>0?[zx("toolbarButtonWith",e)]:[]),getApi:TG,onSetup:t.onSetup},n)},NG=function(t,n){return PG(t,n,[])},PG=function(t,n,e){return Sr(FG(t,{toolbarButtonBehaviours:[Vx.config({}),pw.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(e.length>0?[zx("toolbarToggleButtonWith",e)]:[]),getApi:EG,onSetup:t.onSetup},n))},HG=function(t,n,e){return function(o){return sB.nu((function(t){return n.fetch(t)})).map((function(r){return N.from(zD(Sr(lD(rc("menu-value"),r,(function(e){n.onItemAction(t(o),e)}),n.columns,n.presets,QC.CLOSE_ON_EXECUTE,n.select.getOr(_),e),{movement:mD(n.columns,n.presets),menuBehaviours:a_.unnamedEvents("auto"!==n.columns?[]:[Ua((function(t,e){r_(t,4,yO(n.presets)).each((function(n){var e=n.numRows,o=n.numColumns;Dx.setGridSize(t,e,o)}))}))])})))}))}},VG=function(t,n){var e,o=rc("channel-update-split-dropdown-display"),r=function(t){return{isDisabled:function(){return Pv.isDisabled(t)},setDisabled:function(n){return Pv.set(t,n)},setIconFill:function(n,e){Hs(t.element,'svg path[id="'+n+'"], rect[id="'+n+'"]').each((function(t){Le(t,"fill",e)}))},setIconStroke:function(n,e){Hs(t.element,'svg path[id="'+n+'"], rect[id="'+n+'"]').each((function(t){Le(t,"stroke",e)}))},setActive:function(n){Le(t.element,"aria-pressed",n),Hs(t.element,"span").each((function(e){t.getSystem().getByDom(e).each((function(t){return pw.set(t,n)}))}))},isActive:function(){return Hs(t.element,"span").exists((function(n){return t.getSystem().getByDom(n).exists(pw.isOn)}))}}},i=ru(h),u={getApi:r,onSetup:t.onSetup};return _G.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:D({"aria-pressed":!1},DG(t.tooltip,n.providers))},onExecute:function(n){t.onAction(r(n))},onItemExecute:function(t,n,e){},splitDropdownBehaviours:ml([ST.splitButton(n.providers.isDisabled),vT(),zx("split-dropdown-events",[Ma(AG,Jx.focus),CT(u,i),OT(u,i)]),JB.config({})]),eventOrder:(e={},e[da()]=["alloy.base.behaviour","split-dropdown-events"],e),toggleClass:"tox-tbtn--enabled",lazySink:n.getSink,fetch:HG(r,t,n.providers),parts:{menu:OO(!1,t.columns,t.presets)},components:[_G.parts.button(BG(t.icon,t.text,N.none(),N.some(o),N.some([pw.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),n.providers)),_G.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Lk("chevron-down",n.providers.icons)},buttonBehaviours:ml([ST.splitButton(n.providers.isDisabled),vT(),jk()])}),_G.parts["aria-descriptor"]({text:n.providers.translate("To open the popup, press Shift+Enter")})]})},LG=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],zG=function(t,n){return function(e,o,r){var i=t(e).mapError((function(t){return ki(t)})).getOrDie();return n(i,o,r)}},UG={button:zG(RC,(function(t,n){return IG(t,n.backstage.shared.providers)})),togglebutton:zG(HC,(function(t,n){return NG(t,n.backstage.shared.providers)})),menubutton:zG(Zz,(function(t,n){return eP(t,"tox-tbtn",n.backstage,N.none())})),splitbutton:zG(nU,(function(t,n){return VG(t,n.backstage.shared)})),grouptoolbarbutton:zG(Jz,(function(t,n,e){var o,r=e.ui.registry.getAll().buttons,i=function(t){return JG(e,{buttons:r,toolbar:t,allowToolbarGroups:!1},n,N.none())},u=(o={},o[Pd]=n.backstage.shared.header.isPositionedAtTop()?Vl.TopToBottom:Vl.BottomToTop,o);switch(U_(e)){case $C.floating:return MG(t,n.backstage,i,u);default:throw new Error("Toolbar groups are only supported when using floating toolbar mode")}})),styleSelectButton:function(t,n){return lG(t,n.backstage)},fontsizeSelectButton:function(t,n){return oG(t,n.backstage)},fontSelectButton:function(t,n){return qW(t,n.backstage)},formatButton:function(t,n){return cG(t,n.backstage)},alignMenuButton:function(t,n){return zW(t,n.backstage)}},jG=function(t,n,e){return _t(UG,t.type).fold((function(){return N.none()}),(function(o){return N.some(o(t,n,e))}))},WG={styleselect:UG.styleSelectButton,fontsizeselect:UG.fontsizeSelectButton,fontselect:UG.fontSelectButton,formatselect:UG.formatButton,align:UG.alignMenuButton},GG=function(t){var n=X(LG,(function(n){var e=J(n.items,(function(n){return Tt(t,n)||Tt(WG,n)}));return{name:n.name,items:e}}));return J(n,(function(t){return t.items.length>0}))},XG=function(t){var n=t.split("|");return X(n,(function(t){return{items:t.trim().split(" ")}}))},YG=function(t){return p(t,(function(t){return Tt(t,"name")&&Tt(t,"items")}))},qG=function(t){var n=t.toolbar,e=t.buttons;return!1===n?[]:void 0===n||!0===n?GG(e):i(n)?XG(n):YG(n)?n:[]},KG=function(t,n,e,o,r,i){return _t(n,e.toLowerCase()).orThunk((function(){return i.bind((function(t){return gt(t,(function(t){return _t(n,t+e.toLowerCase())}))}))})).fold((function(){return _t(WG,e.toLowerCase()).map((function(n){return n(t,r)})).orThunk((function(){return N.none()}))}),(function(n){return"grouptoolbarbutton"!==n.type||o?jG(n,r,t):N.none()}))},JG=function(t,n,e,o){var r=qG(n),i=X(r,(function(r){var i=ot(r.items,(function(r){return 0===r.trim().length?[]:KG(t,n.buttons,r,n.allowToolbarGroups,e,o).toArray()}));return{title:N.from(t.translate(r.name)),items:i}}));return J(i,(function(t){return t.items.length>0}))},$G=function(t,n,e,o){var r=n.outerContainer,u=e.toolbar,a=e.buttons;if(p(u,i)){var c=u.map((function(n){var r={toolbar:n,buttons:a,allowToolbarGroups:e.allowToolbarGroups};return JG(t,r,{backstage:o},N.none())}));mW.setToolbars(r,c)}else mW.setToolbar(r,JG(t,e,{backstage:o},N.none()))},QG=ee(),ZG=QG.os.isiOS()&&QG.os.version.major<=12,tX=function(t,n){var e=t.dom,o=t.getWin(),r=t.getDoc().documentElement,i=ru(fo(o.innerWidth,o.innerHeight)),u=ru(fo(r.offsetWidth,r.offsetHeight)),a=function(){var n=i.get();n.left===o.innerWidth&&n.top===o.innerHeight||(i.set(fo(o.innerWidth,o.innerHeight)),TE(t))},c=function(){var n=t.getDoc().documentElement,e=u.get();e.left===n.offsetWidth&&e.top===n.offsetHeight||(u.set(fo(n.offsetWidth,n.offsetHeight)),TE(t))},s=function(n){return EE(t,n)};e.bind(o,"resize",a),e.bind(o,"scroll",s);var f=ad(nn.fromDom(t.getBody()),"load",c),l=n.uiMothership.element;t.on("hide",(function(){qe(l,"display","none")})),t.on("show",(function(){eo(l,"display")})),t.on("NodeChange",c),t.on("remove",(function(){f.unbind(),e.unbind(o,"resize",a),e.unbind(o,"scroll",s),o=null}))},nX=function(t,n,e,o,r){var i=ru(0),u=n.outerContainer;OW(t);var a=nn.fromDom(r.targetNode),c=Ae(De(a));Jg(a,n.mothership),Kg(c,n.uiMothership),t.on("PostRender",(function(){$G(t,n,e,o),i.set(t.getWin().innerWidth),mW.setMenubar(u,bW(t,e)),mW.setSidebar(u,e.sidebar),tX(t,n)}));var s=mW.getSocket(u).getOrDie("Could not find expected socket element");if(ZG){Ke(s.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});var f=Qk((function(){t.fire("ScrollContent")}),20),l=ud(s.element,"scroll",f.throttle);t.on("remove",l.unbind)}hT(t,n),t.addCommand("ToggleSidebar",(function(n,e){mW.toggleSidebar(u,e),t.fire("ToggleSidebar")})),t.addQueryValueHandler("ToggleSidebar",(function(){return mW.whichSidebar(u)}));var d=U_(t),m=function(){mW.refreshToolbar(n.outerContainer)};d!==$C.sliding&&d!==$C.floating||t.on("ResizeWindow ResizeEditor ResizeContent",(function(){var n=t.getWin().innerWidth;n!==i.get()&&(m(),i.set(n))}));var g={enable:function(){pT(n,!1)},disable:function(){pT(n,!0)},isDisabled:function(){return Pv.isDisabled(u)}};return{iframeContainer:s.element.dom,editorContainer:u.element.dom,api:g}},eX=Object.freeze({__proto__:null,render:nX}),oX=function(t){var n=/^[0-9\.]+(|px)$/i;return n.test(""+t)?N.some(parseInt(""+t,10)):N.none()},rX=function(t){return g(t)?t+"px":t},iX=function(t,n,e){var o=n.filter((function(n){return t<n})),r=e.filter((function(n){return t>n}));return o.or(r).getOr(t)},uX=function(t){var n=__(t),e=D_(t),o=B_(t);return oX(n).map((function(t){return iX(t,e,o)}))},aX=function(t){var n=uX(t);return n.getOr(__(t))},cX=function(t){var n=T_(t),e=E_(t),o=A_(t);return oX(n).map((function(t){return iX(t,e,o)}))},sX=function(t){var n=cX(t);return n.getOr(T_(t))},fX=function(t,n,e,o,r){var i=e.uiMothership,u=e.outerContainer,a=w_.DOM,c=$_(t),s=tT(t),f=A_(t).or(cX(t)),l=o.shared.header,d=l.isPositionedAtTop,m=U_(t),g=m===$C.sliding||m===$C.floating,p=ru(!1),h=function(){return p.get()&&!t.removed},v=function(t){return g?t.fold(x(0),(function(t){return t.components().length>1?uo(t.components()[1].element):0})):0},b=function(e){switch(G_(t)){case z_.auto:var o=mW.getToolbar(u),r=v(o),i=uo(e.element)-r,a=Qo(n),c=a.y>i;if(c)return"top";var s=pe(n),f=Math.max(s.dom.scrollHeight,uo(s)),l=a.bottom<f-i;if(l)return"bottom";var d=tr(),m=d.bottom<a.bottom-i;return m?"bottom":"top";case z_.bottom:return"bottom";case z_.top:default:return"top"}},y=function(t){var n=r.get();Bz.setModes(n,[t]),l.setDockingMode(t);var e=d()?Vl.TopToBottom:Vl.BottomToTop;Le(n.element,Pd,e)},w=function(){var t=f.getOrThunk((function(){var t=oX($e(Pe(),"margin-left")).getOr(0);return bo(Pe())-go(n).left+t}));qe(r.get().element,"max-width",t+"px")},S=function(){var t=mW.getToolbar(u),e=v(t),o=Qo(n),i=d()?Math.max(o.y-uo(r.get().element)+e,0):o.bottom;Ke(u.element,{position:"absolute",top:Math.round(i)+"px",left:Math.round(o.x)+"px"})},k=function(){i.broadcastOn([xp()],{})},C=function(t){if(void 0===t&&(t=!1),h()){if(c||w(),g&&mW.refreshToolbar(u),c||S(),s){var n=r.get();t?Bz.reset(n):Bz.refresh(n)}k()}},O=function(t){if(void 0===t&&(t=!0),!c&&s&&h()){var n=l.getDockingMode(),e=b(r.get());e!==n&&(y(e),t&&C(!0))}},_=function(){p.set(!0),qe(u.element,"display","flex"),a.addClass(t.getBody(),"mce-edit-focus"),eo(i.element,"display"),O(!1),C()},T=function(){p.set(!1),e.outerContainer&&(qe(u.element,"display","none"),a.removeClass(t.getBody(),"mce-edit-focus")),qe(i.element,"display","none")};return{isVisible:h,isPositionedAtTop:d,show:_,hide:T,update:C,updateMode:O,repositionPopups:k}},lX=function(t,n){var e=Qo(t);return{pos:n?e.y:e.bottom,bounds:e}},dX=function(t,n,e,o){var r=ru(lX(n,e.isPositionedAtTop())),i=function(o){var i=lX(n,e.isPositionedAtTop()),u=i.pos,a=i.bounds,c=r.get(),s=c.pos,f=c.bounds,l=a.height!==f.height||a.width!==f.width;r.set({pos:u,bounds:a}),l&&TE(t,o),e.isVisible()&&(s!==u?e.update(!0):l&&(e.updateMode(),e.repositionPopups()))};o||(t.on("activate",e.show),t.on("deactivate",e.hide)),t.on("SkinLoaded ResizeWindow",(function(){return e.update(!0)})),t.on("NodeChange keydown",(function(t){wS.requestAnimationFrame((function(){return i(t)}))})),t.on("ScrollWindow",(function(){return e.updateMode()}));var u=ed();u.set(ad(nn.fromDom(t.getBody()),"load",i)),t.on("remove",(function(){u.clear()}))},mX=function(t,n,e,o,r){var i=n.mothership,u=n.uiMothership,a=n.outerContainer,c=ru(null),s=nn.fromDom(r.targetNode),f=fX(t,s,n,o,c),l=K_(t);_W(t);var d=function(){if(c.get())f.show();else{c.set(mW.getHeader(a).getOrDie());var r=Q_(t);Kg(r,i),Kg(r,u),$G(t,n,e,o),mW.setMenubar(a,bW(t,e)),f.show(),dX(t,s,f,l),t.nodeChanged()}};t.on("show",d),t.on("hide",f.hide),l||(t.on("focus",d),t.on("blur",f.hide)),t.on("init",(function(){(t.hasFocus()||l)&&d()})),hT(t,n);var m={show:function(){f.show()},hide:function(){f.hide()},enable:function(){pT(n,!1)},disable:function(){pT(n,!0)},isDisabled:function(){return Pv.isDisabled(a)}};return{editorContainer:a.element.dom,api:m}},gX=Object.freeze({__proto__:null,render:mX}),pX="contexttoolbar-show",hX="contexttoolbar-hide",vX=function(t){return{hide:function(){return Sa(t,ia())},getValue:function(){return th.getValue(t)}}},bX=function(t,n){return Ma(VN,(function(e,o){var r=t.get(e),i=vX(r);n.onAction(i,o.event.buttonApi)}))},yX=function(t,n,e){var o=n.original;o.primary;var r=A(o,["primary"]),i=wi(RC(D(D({},r),{type:"button",onAction:h})));return RG(i,e.backstage.shared.providers,[bX(t,n)])},xX=function(t,n,e){var o=n.original;o.primary;var r=A(o,["primary"]),i=wi(HC(D(D({},r),{type:"togglebutton",onAction:h})));return PG(i,e.backstage.shared.providers,[bX(t,n)])},wX=function(t,n,e){var o={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===n.type?xX(t,n,o):yX(t,n,o)},SX=function(t,n,e){var o=X(n,(function(n){return TS(wX(t,n,e))})),r=function(){return X(o,(function(t){return t.asSpec()}))},i=function(t){return gt(n,(function(n,e){return n.primary?N.from(o[e]).bind((function(n){return n.getOpt(t)})).filter(C(Pv.isDisabled)):N.none()}))};return{asSpecs:r,findPrimary:i}},kX=function(t,n){var e=t.label.fold((function(){return{}}),(function(t){return{"aria-label":t}})),o=TS($A.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:e,selectOnFocus:!0,inputBehaviours:ml([Dx.config({mode:"special",onEnter:function(t){return r.findPrimary(t).map((function(t){return Ca(t),!0}))},onLeft:function(t,n){return n.cut(),N.none()},onRight:function(t,n){return n.cut(),N.none()}})])})),r=SX(o,t.commands,n);return[{title:N.none(),items:[o.asSpec()]},{title:N.none(),items:r.asSpecs()}]},CX=function(t,n,e){return oW({type:t,uid:rc("context-toolbar"),initGroups:kX(n,e),onEscape:N.none,cyclicKeying:!0,providers:e})},OX={renderContextForm:CX,buildInitGroups:kX},_X=function(t,n,e){return void 0===e&&(e=.01),n.bottom-t.y>=e&&t.bottom-n.y>=e},TX=function(t){var n=t.getBoundingClientRect();if(n.height<=0&&n.width<=0){var e=Oe(nn.fromDom(t.startContainer),t.startOffset).element,o=dn(e)?ve(e):N.some(e);return o.filter(ln).map((function(t){return t.dom.getBoundingClientRect()})).getOr(n)}return n},EX=function(t){var n=t.selection.getRng(),e=TX(n);if(t.inline){var o=Ho();return $o(o.left+e.left,o.top+e.top,e.width,e.height)}var r=Zo(nn.fromDom(t.getBody()));return $o(r.x+e.left,r.y+e.top,e.width,e.height)},DX=function(t,n){return n.filter(Ne).map(Zo).getOrThunk((function(){return EX(t)}))},AX=function(t,n,e){var o=Math.max(t.x+e,n.x),r=Math.min(t.right-e,n.right);return{x:o,width:r-o}},BX=function(t,n,e,o,r,i){var u=nn.fromDom(t.getContainer()),a=Hs(u,".tox-editor-header").getOr(u),c=Qo(a),s=c.y>=n.bottom,f=o&&!s;if(t.inline&&f)return{y:Math.max(c.bottom+i,e.y),bottom:e.bottom};if(t.inline&&!f)return{y:e.y,bottom:Math.min(c.y-i,e.bottom)};var l="line"===r?Qo(u):n;return f?{y:Math.max(c.bottom+i,e.y),bottom:Math.min(l.bottom-i,e.bottom)}:{y:Math.max(l.y+i,e.y),bottom:Math.min(c.y-i,e.bottom)}},MX=function(t,n,e,o){void 0===o&&(o=0);var r=Uo(window),i=Qo(nn.fromDom(t.getContentAreaContainer())),u=P_(t)||H_(t)||L_(t),a=AX(i,r,o),c=a.x,s=a.width;if(t.inline&&!u)return $o(c,r.y,s,r.height);var f=n.header.isPositionedAtTop(),l=BX(t,i,r,f,e,o),d=l.y,m=l.bottom;return $o(c,d,s,m-d)},FX=12,IX={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},RX={maxHeightFunction:Td(),maxWidthFunction:Tj()},NX=function(t,n){var e=t.selection.getRng(),o=Oe(nn.fromDom(e.startContainer),e.startOffset);return e.startContainer===e.endContainer&&e.startOffset===e.endOffset-1&&se(o.element,n)},PX=function(t,n,e){var o=Ze(t,"position");qe(t,"position",n);var r=e(t);return o.each((function(n){return qe(t,"position",n)})),r},HX=function(t){return"node"===t},VX=function(t,n,e,o,r){var i=EX(t),u=o.lastElement().exists((function(t){return se(e,t)}));if(NX(t,e))return u?bS:mS;if(u)return PX(n,o.getMode(),(function(){var t=_X(i,Qo(n));return t&&!o.isReposition()?xS:bS}));var a="fixed"===o.getMode()?r.y+Ho().top:r.y,c=uo(n)+FX;return a+c<=i.y?mS:gS},LX=function(t,n,e,o){var r=function(n){return function(o,r,i,u,a){var c=VX(t,u,n,e,a),s=D(D({},o),{y:a.y,height:a.height});return D(D({},c(s,r,i,u,a)),{alwaysFit:!0})}},i=function(t){return HX(o)?[r(t)]:[]},u={onLtr:function(t){return[Xf,Yf,Wf,Uf,Gf,jf].concat(i(t))},onRtl:function(t){return[Xf,Yf,Gf,jf,Wf,Uf].concat(i(t))}},a={onLtr:function(t){return[Yf,Uf,jf,Wf,Gf,Xf].concat(i(t))},onRtl:function(t){return[Yf,jf,Uf,Gf,Wf,Xf].concat(i(t))}};return n?a:u},zX=function(t,n,e,o){return"line"===n?{bubble:Md(FX,0,IX),layouts:{onLtr:function(){return[qf]},onRtl:function(){return[Kf]}},overrides:RX}:{bubble:Md(0,FX,IX,1/FX),layouts:LX(t,e,o,n),overrides:RX}},UX=function(t,n){var e=J(n,(function(n){return n.predicate(t.dom)})),o=K(e,(function(t){return"contexttoolbar"===t.type})),r=o.pass,i=o.fail;return{contextToolbars:r,contextForms:i}},jX=function(t){if(t.length<=1)return t;var n=function(n){return j(t,(function(t){return t.position===n}))},e=function(n){return J(t,(function(t){return t.position===n}))},o=n("selection"),r=n("node");if(o||r){if(r&&o){var i=e("node"),u=X(e("selection"),(function(t){return D(D({},t),{position:"node"})}));return i.concat(u)}return e(o?"selection":"node")}return e("line")},WX=function(t){if(t.length<=1)return t;var n=function(n){return tt(t,(function(t){return t.position===n}))},e=n("selection").orThunk((function(){return n("node")})).orThunk((function(){return n("line")})).map((function(t){return t.position}));return e.fold((function(){return[]}),(function(n){return J(t,(function(t){return t.position===n}))}))},GX=function(t,n,e){var o=UX(t,n);if(o.contextForms.length>0)return N.some({elem:t,toolbars:[o.contextForms[0]]});var r=UX(t,e);if(r.contextForms.length>0)return N.some({elem:t,toolbars:[r.contextForms[0]]});if(o.contextToolbars.length>0||r.contextToolbars.length>0){var i=jX(o.contextToolbars.concat(r.contextToolbars));return N.some({elem:t,toolbars:i})}return N.none()},XX=function(t,n,e){return t(n)?N.none():yu(n,(function(t){if(ln(t)){var n=UX(t,e.inNodeScope),o=n.contextToolbars,r=n.contextForms,i=r.length>0?r:WX(o);return i.length>0?N.some({elem:t,toolbars:i}):N.none()}return N.none()}),t)},YX=function(t,n){var e=nn.fromDom(n.getBody()),o=function(t){return se(t,e)},r=function(t){return!o(t)&&!de(e,t)},i=nn.fromDom(n.selection.getNode());return r(i)?N.none():GX(i,t.inNodeScope,t.inEditorScope).orThunk((function(){return XX(o,i,t)}))},qX=function(t,n){var e={},o=[],r=[],i={},u={},a=function(t,a){var c=wi(XC(a));e[t]=c,c.launch.map((function(e){i["form:"+t]=D(D({},a.launch),{type:"contextformtogglebutton"===e.type?"togglebutton":"button",onAction:function(){n(c)}})})),"editor"===c.scope?r.push(c):o.push(c),u[t]=c},c=function(t,n){qC(n).each((function(e){"editor"===n.scope?r.push(e):o.push(e),u[t]=e}))},s=pt(t);return Y(s,(function(n){var e=t[n];"contextform"===e.type?a(n,e):"contexttoolbar"===e.type&&c(n,e)})),{forms:e,inNodeScope:o,inEditorScope:r,lookupTable:u,formNavigators:i}},KX=rc("forward-slide"),JX=rc("backward-slide"),$X=rc("change-slide-event"),QX="tox-pop--resizing",ZX=function(t){var n=ru([]);return nS.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(t){n.set([]),nS.getContent(t).each((function(t){eo(t.element,"visibility")})),as(t.element,QX),eo(t.element,"width")},inlineBehaviours:ml([zx("context-toolbar-events",[za(Xu(),(function(t,n){"width"===n.event.raw.propertyName&&(as(t.element,QX),eo(t.element,"width"))})),Ma($X,(function(t,n){var e=t.element;eo(e,"width");var o=bo(e);nS.setContent(t,n.event.contents),is(e,QX);var r=bo(e);qe(e,"width",o+"px"),nS.getContent(t).each((function(t){n.event.focus.bind((function(t){return kl(t),Tl(e)})).orThunk((function(){return Dx.focusIn(t),_l(De(e))}))})),wS.setTimeout((function(){qe(t.element,"width",r+"px")}),0)})),Ma(KX,(function(t,e){nS.getContent(t).each((function(e){n.set(n.get().concat([{bar:e,focus:_l(De(t.element))}]))})),ka(t,$X,{contents:e.event.forwardContents,focus:N.none()})})),Ma(JX,(function(t,e){dt(n.get()).each((function(e){n.set(n.get().slice(0,n.get().length-1)),ka(t,$X,{contents:Es(e.bar),focus:e.focus})}))}))]),Dx.config({mode:"special",onEscape:function(e){return dt(n.get()).fold((function(){return t.onEscape()}),(function(t){return Sa(e,JX),N.some(!0)}))}})]),lazySink:function(){return rr.value(t.sink)}})},tY="tox-pop--transition",nY=function(t,n,e,o){var r=o.backstage,i=r.shared,u=ee().deviceType.isTouch,a=rd(),c=rd(),s=rd(),f=Ts(ZX({sink:e,onEscape:function(){return t.focus(),N.some(!0)}})),l=function(){var n=s.get().getOr("node"),e=HX(n)?1:0;return MX(t,i,n,e)},d=function(){return!t.removed&&!(u()&&r.isContextMenuOpen())},m=function(t){return Dt(Ft(t,a.get(),se),!0)},g=function(){if(d()){var n=l(),e=Dt(s.get(),"node")?DX(t,a.get()):EX(t);return n.height<=0||!_X(e,n)}return!0},p=function(){a.clear(),c.clear(),s.clear(),nS.hide(f)},v=function(){if(nS.isOpen(f)){var t=f.element;eo(t,"display"),g()?qe(t,"display","none"):(c.set(0),nS.reposition(f))}},b=function(n){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[n],behaviours:ml([Dx.config({mode:"acyclic"}),zx("pop-dialog-wrap-events",[Ua((function(n){t.shortcuts.add("ctrl+F9","focus statusbar",(function(){return Dx.focusIn(n)}))})),ja((function(n){t.shortcuts.remove("ctrl+F9")}))])])}},y=pn((function(){return qX(n,(function(t){var n=S([t]);ka(f,KX,{forwardContents:b(n)})}))})),x=function(n,e){return JG(t,{buttons:n,toolbar:e.items,allowToolbarGroups:!1},o,N.some(["form:"]))},w=function(t,n){return OX.buildInitGroups(t,n)},S=function(n){var e=t.ui.registry.getAll().buttons,o=y(),r=D(D({},e),o.formNavigators),u=U_(t)===$C.scrolling?$C.scrolling:$C.default,a=et(X(n,(function(t){return"contexttoolbar"===t.type?x(r,t):w(t,i.providers)})));return oW({type:u,uid:rc("context-toolbar"),initGroups:a,onEscape:N.none,cyclicKeying:!0,providers:i.providers})},k=function(n,o){var r="node"===n?i.anchors.node(o):i.anchors.cursor(),s=zX(t,n,u(),{lastElement:a.get,isReposition:function(){return Dt(c.get(),0)},getMode:function(){return Hg.getMode(e)}});return Sr(r,s)},C=function(t,n){if(O.cancel(),d()){var o=S(t),r=t[0].position,i=k(r,n);s.set(r),c.set(1);var u=f.element;eo(u,"display"),m(n)||(as(u,tY),Hg.reset(e,f)),nS.showWithinBounds(f,b(o),{anchor:i,transition:{classes:[tY],mode:"placement"}},(function(){return N.some(l())})),n.fold(a.clear,a.set),g()&&qe(u,"display","none")}},O=Zk((function(){if(t.hasFocus()&&!t.removed)if(cs(f.element,tY))O.throttle();else{var n=y();YX(n,t).fold(p,(function(t){C(t.toolbars,N.some(t.elem))}))}}),17);t.on("init",(function(){t.on("remove",p),t.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",v),t.on("click keyup focus SetContent",O.throttle),t.on(hX,p),t.on(pX,(function(n){var e=y();_t(e.lookupTable,n.toolbarKey).each((function(e){C([e],Nt(n.target!==t,n.target)),nS.getContent(f).each(Dx.focusIn)}))})),t.on("focusout",(function(n){wS.setEditorTimeout(t,(function(){Tl(e.element).isNone()&&Tl(f.element).isNone()&&p()}),0)})),t.on("SwitchMode",(function(){t.mode.isReadOnly()&&p()})),t.on("AfterProgressState",(function(n){n.state?p():t.hasFocus()&&O.throttle()})),t.on("NodeChange",(function(t){Tl(f.element).fold(O.throttle,h)}))}))},eY=function(t){var n=[{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}];Y(n,(function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onAction:AW(t,n.cmd),onSetup:TW(t,n.name)})})),t.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onAction:AW(t,"JustifyNone")})},oY={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},rY=function(){var t="[0-9]+",n="[+-]?"+t,e="[eE]"+n,o="\\.",r=function(t){return"(?:"+t+")?"},i=["Infinity",t+o+r(t)+r(e),o+t+r(e),t+r(e)].join("|"),u="[+-]?(?:"+i+")";return new RegExp("^("+u+")(.*)$")}(),iY=function(t,n){return j(n,(function(n){return j(oY[n],(function(n){return t===n}))}))},uY=function(t,n){var e=N.from(rY.exec(t));return e.bind((function(t){var e=Number(t[1]),o=t[2];return iY(o,n)?N.some({value:e,unit:o}):N.none()}))},aY=function(t,n){return uY(t,n).map((function(t){var n=t.value,e=t.unit;return n+e}))},cY=function(t,n){var e=function(){var e=n.getOptions(t),o=n.getCurrent(t).map(n.hash),r=rd();return X(e,(function(e){return{type:"togglemenuitem",text:n.display(e),onSetup:function(i){var u=function(t){t&&(r.on((function(t){return t.setActive(!1)})),r.set(i)),i.setActive(t)};u(Dt(o,n.hash(e)));var a=n.watcher(t,e,u);return function(){r.clear(),a()}},onAction:function(){return n.setCurrent(t,e)}}}))};t.ui.registry.addMenuButton(n.name,{tooltip:n.text,icon:n.icon,fetch:function(t){return t(e())},onSetup:n.onToolbarSetup}),t.ui.registry.addNestedMenuItem(n.name,{type:"nestedmenuitem",text:n.text,getSubmenuItems:e,onSetup:n.onMenuSetup})},sY={name:"lineheight",text:"Line height",icon:"line-height",getOptions:I_,hash:function(t){return aY(t,["fixed","relative","empty"]).getOr(t)},display:w,watcher:function(t,n,e){return t.formatter.formatChanged("lineheight",e,!1,{value:n}).unbind},getCurrent:function(t){return N.from(t.queryCommandValue("LineHeight"))},setCurrent:function(t,n){return t.execCommand("LineHeight",!1,n)}},fY=function(t){var n=N.from(R_(t));return n.map((function(n){return{name:"language",text:"Language",icon:"language",getOptions:x(n),hash:function(t){return f(t.customCode)?t.code:t.code+"/"+t.customCode},display:function(t){return t.title},watcher:function(t,n,e){return t.formatter.formatChanged("lang",e,!1,{value:n.code,customValue:n.customCode}).unbind},getCurrent:function(t){var n=nn.fromDom(t.selection.getNode());return xu(n,(function(t){return N.some(t).filter(ln).bind((function(t){var n=je(t,"lang");return n.map((function(n){var e=je(t,"data-mce-lang").getOrUndefined();return{code:n,customCode:e,title:""}}))}))}))},setCurrent:function(t,n){return t.execCommand("Lang",!1,n)},onToolbarSetup:function(n){var e=ed();return n.setActive(t.formatter.match("lang",{},void 0,!0)),e.set(t.formatter.formatChanged("lang",n.setActive,!0)),e.clear}}}))},lY=function(t){cY(t,sY),fY(t).each((function(n){return cY(t,n)}))},dY=function(t,n){UW(t,n),KW(t,n),dG(t,n),sG(t,n),rG(t,n)},mY=function(t){return EW(t,"NodeChange",(function(n){n.setDisabled(!t.queryCommandState("outdent"))}))},gY=function(t){t.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:mY(t),onAction:AW(t,"outdent")}),t.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:AW(t,"indent")})},pY=function(t){gY(t)},hY=function(t,n){return function(){t.execCommand("mceToggleFormat",!1,n)}},vY=function(t){aR.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],(function(n,e){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onSetup:TW(t,n.name),onAction:hY(t,n.name)})}));for(var n=1;n<=6;n++){var e="h"+n;t.ui.registry.addToggleButton(e,{text:e.toUpperCase(),tooltip:"Heading "+n,onSetup:TW(t,e),onAction:hY(t,e)})}},bY=function(t){aR.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],(function(n){t.ui.registry.addButton(n.name,{tooltip:n.text,icon:n.icon,onAction:AW(t,n.action)})}))},yY=function(t){aR.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onAction:AW(t,n.action),onSetup:TW(t,n.name)})}))},xY=function(t){vY(t),bY(t),yY(t)},wY=function(t){aR.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],(function(n){t.ui.registry.addMenuItem(n.name,{text:n.text,icon:n.icon,shortcut:n.shortcut,onAction:AW(t,n.action)})})),t.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:hY(t,"code")})},SY=function(t){xY(t),wY(t)},kY=function(t,n){return EW(t,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(function(e){e.setDisabled(t.mode.isReadOnly()||!t.undoManager[n]())}))},CY=function(t){t.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:kY(t,"hasUndo"),onAction:AW(t,"undo")}),t.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:kY(t,"hasRedo"),onAction:AW(t,"redo")})},OY=function(t){t.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",disabled:!0,onSetup:kY(t,"hasUndo"),onAction:AW(t,"undo")}),t.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",disabled:!0,onSetup:kY(t,"hasRedo"),onAction:AW(t,"redo")})},_Y=function(t){CY(t),OY(t)},TY=function(t){return EW(t,"VisualAid",(function(n){n.setActive(t.hasVisual)}))},EY=function(t){t.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:TY(t),onAction:AW(t,"mceToggleVisualAid")})},DY=function(t){t.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:AW(t,"mceToggleVisualAid")})},AY=function(t){DY(t),EY(t)},BY=function(t,n){eY(t),SY(t),dY(t,n),_Y(t),fD(t),AY(t),pY(t),lY(t)},MY=function(t,n){return{type:"makeshift",x:t,y:n}},FY=function(t,n,e){return MY(t.x+n,t.y+e)},IY=function(t){return"longpress"===t.type||0===t.type.indexOf("touch")},RY=function(t){if(IY(t)){var n=t.touches[0];return MY(n.pageX,n.pageY)}return MY(t.pageX,t.pageY)},NY=function(t){if(IY(t)){var n=t.touches[0];return MY(n.clientX,n.clientY)}return MY(t.clientX,t.clientY)},PY=function(t,n){var e=w_.DOM.getPos(t);return FY(n,e.x,e.y)},HY=function(t,n){return"contextmenu"===n.type||"longpress"===n.type?t.inline?RY(n):PY(t.getContentAreaContainer(),NY(n)):VY(t)},VY=function(t){return{type:"selection",root:nn.fromDom(t.selection.getNode())}},LY=function(t){return{type:"node",node:N.some(nn.fromDom(t.selection.getNode())),root:nn.fromDom(t.getBody())}},zY=function(t,n,e){switch(e){case"node":return LY(t);case"point":return HY(t,n);case"selection":return VY(t)}},UY=function(t,n,e,o,r,i){var u=e(),a=zY(t,n,i);tP(u,QC.CLOSE_ON_EXECUTE,o,!1).map((function(t){n.preventDefault(),nS.showMenuAt(r,{anchor:a},{menu:{markers:SO("normal")},data:t})}))},jY={onLtr:function(){return[Yf,Uf,jf,Wf,Gf,Xf,mS,gS,dS,fS,lS,sS]},onRtl:function(){return[Yf,jf,Uf,Gf,Wf,Xf,mS,gS,lS,sS,dS,fS]}},WY=12,GY={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},XY=function(t,n){var e=t.selection;if(e.isCollapsed()||n.touches.length<1)return!1;var o=n.touches[0],r=e.getRng(),i=eg(t.getWin(),Cm.domRange(r));return i.exists((function(t){return t.left<=o.clientX&&t.right>=o.clientX&&t.top<=o.clientY&&t.bottom>=o.clientY}))},YY=function(t){var n=t.selection.getRng(),e=function(){wS.setEditorTimeout(t,(function(){t.selection.setRng(n)}),10),i()};t.once("touchend",e);var o=function(t){t.preventDefault(),t.stopImmediatePropagation()};t.on("mousedown",o,!0);var r=function(){return i()};t.once("longpresscancel",r);var i=function(){t.off("touchend",e),t.off("longpresscancel",r),t.off("mousedown",o)}},qY=function(t,n,e){var o=zY(t,n,e),r="point"===e?WY:0;return D({bubble:Md(0,r,GY),layouts:jY,overrides:{maxWidthFunction:Tj(),maxHeightFunction:Td()}},o)},KY=function(t,n,e,o,r,i,u){var a=qY(t,n,i);tP(e,QC.CLOSE_ON_EXECUTE,o,!0).map((function(e){n.preventDefault(),nS.showMenuWithinBounds(r,{anchor:a},{menu:{markers:SO("normal"),highlightImmediately:u},data:e,type:"horizontal"},(function(){return N.some(MX(t,o.shared,"node"===i?"node":"selection"))})),t.fire(hX)}))},JY=function(t,n,e,o,r,i){var u=ee(),a=u.os.isiOS(),c=u.os.isOSX(),s=u.os.isAndroid(),f=u.deviceType.isTouch(),l=function(){return!(s||a||c&&f)},d=function(){var u=e();KY(t,n,u,o,r,i,l())};if((c||a)&&"node"!==i){var m=function(){YY(t),d()};XY(t,n)?m():(t.once("selectionchange",m),t.once("touchend",(function(){return t.off("selectionchange",m)})))}else d()},$Y=function(t){return"string"===typeof t?t.split(/[ ,]/):t},QY=function(t){return t.getParam("contextmenu_never_use_native",!1,"boolean")},ZY=function(t,n,e){var o=t.ui.registry.getAll().contextMenus;return N.from(t.getParam(n)).map($Y).getOrThunk((function(){return J($Y(e),(function(t){return Tt(o,t)}))}))},tq=function(t){return!1===t.getParam("contextmenu")},nq=function(t){return ZY(t,"contextmenu","link linkchecker image imagetools table spellchecker configurepermanentpen")},eq=function(t){return t.getParam("contextmenu_avoid_overlap","","string")},oq=function(t){return i(t)?"|"===t:"separator"===t.type},rq={type:"separator"},iq=function(t){var n=function(t){return{text:t.text,icon:t.icon,disabled:t.disabled,shortcut:t.shortcut}};if(i(t))return t;switch(t.type){case"separator":return rq;case"submenu":return D(D({type:"nestedmenuitem"},n(t)),{getSubmenuItems:function(){var n=t.getSubmenuItems();return i(n)?n:X(n,iq)}});default:return D(D({type:"menuitem"},n(t)),{onAction:v(t.onAction)})}},uq=function(t,n){if(0===n.length)return t;var e=dt(t).filter((function(t){return!oq(t)})),o=e.fold((function(){return[]}),(function(t){return[rq]}));return t.concat(o).concat(n).concat([rq])},aq=function(t,n,e){var o=Q(n,(function(n,o){return _t(t,o.toLowerCase()).map((function(t){var o=t.update(e);if(i(o))return uq(n,o.split(" "));if(o.length>0){var r=X(o,iq);return uq(n,r)}return n})).getOrThunk((function(){return n.concat([o])}))}),[]);return o.length>0&&oq(o[o.length-1])&&o.pop(),o},cq=function(t,n){return n.ctrlKey&&!QY(t)},sq=function(t,n){return"longpress"!==n.type&&(2!==n.button||n.target===t.getBody()&&""===n.pointerType)},fq=function(t,n){return sq(t,n)?t.selection.getStart(!0):n.target},lq=function(t,n){var e=eq(t),o=sq(t,n)?"selection":"point";if(Yt(e)){var r=fq(t,n),i=GD(nn.fromDom(r),e);return i?"node":o}return o},dq=function(t,n,e){var o=ee(),r=o.deviceType.isTouch,i=Ts(nS.sketch({dom:{tag:"div"},lazySink:n,onEscape:function(){return t.focus()},onShow:function(){return e.setContextMenuState(!0)},onHide:function(){return e.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:ml([zx("dismissContextMenu",[Ma(ga(),(function(n,e){bp.close(n),t.focus()}))])])})),u=function(t){return nS.hide(i)},a=function(n){if(QY(t)&&n.preventDefault(),!cq(t,n)&&!tq(t)){var o=lq(t,n),u=function(){var e=fq(t,n),o=t.ui.registry.getAll(),r=nq(t);return aq(o.contextMenus,r,e)},a=r()?JY:UY;a(t,n,u,e,i,o)}};t.on("init",(function(){var n="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(r()?"":" ResizeWindow");t.on(n,u),t.on("longpress contextmenu",a)}))},mq=uu.generate([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),gq=function(t){return function(n){return n.translate(-t.left,-t.top)}},pq=function(t){return function(n){return n.translate(t.left,t.top)}},hq=function(t){return function(n,e){return Q(t,(function(t,n){return n(t)}),fo(n,e))}},vq=function(t,n,e){return t.fold(hq([pq(e),gq(n)]),hq([gq(n)]),hq([]))},bq=function(t,n,e){return t.fold(hq([pq(e)]),hq([]),hq([pq(n)]))},yq=function(t,n,e){return t.fold(hq([]),hq([gq(e)]),hq([pq(n),gq(e)]))},xq=function(t,n,e,o,r,i){var u=bq(t,r,i),a=bq(n,r,i);return Math.abs(u.left-a.left)<=e&&Math.abs(u.top-a.top)<=o},wq=function(t,n,e,o,r,i){var u=bq(t,r,i),a=bq(n,r,i),c=Math.abs(u.left-a.left),s=Math.abs(u.top-a.top);return fo(c,s)},Sq=function(t,n,e){var o=t.fold((function(t,n){return{position:N.some("absolute"),left:N.some(t+"px"),top:N.some(n+"px")}}),(function(t,n){return{position:N.some("absolute"),left:N.some(t-e.left+"px"),top:N.some(n-e.top+"px")}}),(function(t,n){return{position:N.some("fixed"),left:N.some(t+"px"),top:N.some(n+"px")}}));return D({right:N.none(),bottom:N.none()},o)},kq=function(t,n,e){return t.fold((function(t,o){return Oq(t+n,o+e)}),(function(t,o){return _q(t+n,o+e)}),(function(t,o){return Tq(t+n,o+e)}))},Cq=function(t,n,e,o){var r=function(t,r){return function(i,u){var a=t(n,e,o);return r(i.getOr(a.left),u.getOr(a.top))}};return t.fold(r(yq,Oq),r(bq,_q),r(vq,Tq))},Oq=mq.offset,_q=mq.absolute,Tq=mq.fixed,Eq=function(t,n){var e=Ue(t,n);return f(e)?NaN:parseInt(e,10)},Dq=function(t,n){var e=t.element,o=Eq(e,n.leftAttr),r=Eq(e,n.topAttr);return isNaN(o)||isNaN(r)?N.none():N.some(fo(o,r))},Aq=function(t,n,e){var o=t.element;Le(o,n.leftAttr,e.left+"px"),Le(o,n.topAttr,e.top+"px")},Bq=function(t,n){var e=t.element;Ge(e,n.leftAttr),Ge(e,n.topAttr)},Mq=function(t,n,e,o){return Dq(t,n).fold((function(){return e}),(function(t){return Tq(t.left+o.left,t.top+o.top)}))},Fq=function(t,n,e,o,r,i){var u=Mq(t,n,e,o),a=n.mustSnap?Nq(t,n,u,r,i):Pq(t,n,u,r,i),c=vq(u,r,i);return Aq(t,n,c),a.fold((function(){return{coord:Tq(c.left,c.top),extra:N.none()}}),(function(t){return{coord:t.output,extra:t.extra}}))},Iq=function(t,n){Bq(t,n)},Rq=function(t,n,e,o){return gt(t,(function(t){var r=t.sensor,i=xq(n,r,t.range.left,t.range.top,e,o);return i?N.some({output:Cq(t.output,n,e,o),extra:t.extra}):N.none()}))},Nq=function(t,n,e,o,r){var i=n.getSnapPoints(t),u=Rq(i,e,o,r);return u.orThunk((function(){var t=Q(i,(function(t,n){var i=n.sensor,u=wq(e,i,n.range.left,n.range.top,o,r);return t.deltas.fold((function(){return{deltas:N.some(u),snap:N.some(n)}}),(function(e){var o=(u.left+u.top)/2,r=(e.left+e.top)/2;return o<=r?{deltas:N.some(u),snap:N.some(n)}:t}))}),{deltas:N.none(),snap:N.none()});return t.snap.map((function(t){return{output:Cq(t.output,e,o,r),extra:t.extra}}))}))},Pq=function(t,n,e,o,r){var i=n.getSnapPoints(t);return Rq(i,e,o,r)},Hq=function(t,n,e){return{coord:Cq(t.output,t.output,n,e),extra:t.extra}},Vq=function(t,n,e,o){var r=n.getTarget(t.element);if(n.repositionTarget){var i=me(t.element),u=Ho(i),a=tz(r),c=Hq(o,u,a),s=Sq(c.coord,u,a);Je(r,s)}},Lq=Object.freeze({__proto__:null,snapTo:Vq}),zq="data-initial-z-index",Uq=function(t){ve(t.element).filter(ln).each((function(t){je(t,zq).fold((function(){return eo(t,"z-index")}),(function(n){return qe(t,"z-index",n)})),Ge(t,zq)}))},jq=function(t){ve(t.element).filter(ln).each((function(n){Ze(n,"z-index").each((function(t){Le(n,zq,t)})),qe(n,"z-index",$e(t.element,"z-index"))}))},Wq=function(t,n){t.getSystem().addToGui(n),jq(n)},Gq=function(t){Uq(t),t.getSystem().removeFromGui(t)},Xq=function(t,n,e){return t.getSystem().build(bA.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[n]},events:e}))},Yq=Xi("snaps",[Di("getSnapPoints"),cf("onSensor"),Di("leftAttr"),Di("topAttr"),qi("lazyViewport",tr),qi("mustSnap",!1)]),qq=[qi("useFixed",_),Di("blockerClass"),qi("getTarget",w),qi("onDrag",h),qi("repositionTarget",!0),qi("onDrop",h),tu("getBounds",tr),Yq],Kq=function(t){return It(Ze(t,"left"),Ze(t,"top"),Ze(t,"position"),(function(t,n,e){var o="fixed"===e?Tq:Oq;return o(parseInt(t,10),parseInt(n,10))})).getOrThunk((function(){var n=go(t);return _q(n.left,n.top)}))},Jq=function(t,n,e,o,r){var i=r.bounds,u=bq(n,e,o),a=Df(u.left,i.x,i.x+i.width-r.width),c=Df(u.top,i.y,i.y+i.height-r.height),s=_q(a,c);return n.fold((function(){var t=yq(s,e,o);return Oq(t.left,t.top)}),x(s),(function(){var t=vq(s,e,o);return Tq(t.left,t.top)}))},$q=function(t,n,e,o,r,i,u){var a=n.fold((function(){var t=kq(e,i.left,i.top),n=vq(t,o,r);return Tq(n.left,n.top)}),(function(n){var u=Fq(t,n,e,i,o,r);return u.extra.each((function(e){n.onSensor(t,e)})),u.coord}));return Jq(t,a,o,r,u)},Qq=function(t,n,e,o){var r=n.getTarget(t.element);if(n.repositionTarget){var i=me(t.element),u=Ho(i),a=tz(r),c=Kq(r),s=$q(t,n.snaps,c,u,a,o,e),f=Sq(s,u,a);Je(r,f)}n.onDrag(t,r,o)},Zq=function(t,n){return{bounds:t.getBounds(),height:ao(n.element),width:yo(n.element)}},tK=function(t,n,e,o,r){var i=e.update(o,r),u=e.getStartData().getOrThunk((function(){return Zq(n,t)}));i.each((function(e){Qq(t,n,u,e)}))},nK=function(t,n,e,o){n.each(Gq),e.snaps.each((function(n){Iq(t,n)}));var r=e.getTarget(t.element);o.reset(),e.onDrop(t,r)},eK=function(t){return function(n,e){var o=function(t){e.setStartData(Zq(n,t))};return Ea(B([Ma(fa(),(function(t){e.getStartData().each((function(){return o(t)}))}))],t(n,e,o),!0))}},oK=function(t){return Ea([Ma(Fu(),t.forceDrop),Ma(Nu(),t.drop),Ma(Iu(),(function(n,e){t.move(e.event)})),Ma(Ru(),t.delayDrop)])},rK=function(t){return N.from(fo(t.x,t.y))},iK=function(t,n){return fo(n.left-t.left,n.top-t.top)},uK=Object.freeze({__proto__:null,getData:rK,getDelta:iK}),aK=function(t,n,e){return[Ma(Fu(),(function(o,r){var i=r.event.raw;if(0===i.button){r.stop();var u=function(){return nK(o,N.some(s),t,n)},a=XD(u,200),c={drop:u,delayDrop:a.schedule,forceDrop:u,move:function(e){a.cancel(),tK(o,t,n,uK,e)}},s=Xq(o,t.blockerClass,oK(c)),f=function(){e(o),Wq(o,s)};f()}}))]},cK=B(B([],qq,!0),[df("dragger",{handlers:eK(aK)})],!1),sK=function(t){return Ea([Ma(Du(),t.forceDrop),Ma(Bu(),t.drop),Ma(Mu(),t.drop),Ma(Au(),(function(n,e){t.move(e.event)}))])},fK=function(t){var n=t[0];return N.some(fo(n.clientX,n.clientY))},lK=function(t){var n=t.raw,e=n.touches;return 1===e.length?fK(e):N.none()},dK=function(t,n){return fo(n.left-t.left,n.top-t.top)},mK=Object.freeze({__proto__:null,getData:lK,getDelta:dK}),gK=function(t,n,e){var o=rd(),r=function(e){nK(e,o.get(),t,n),o.clear()};return[Ma(Du(),(function(i,u){u.stop();var a=function(){return r(i)},c={drop:a,delayDrop:h,forceDrop:a,move:function(e){tK(i,t,n,mK,e)}},s=Xq(i,t.blockerClass,sK(c));o.set(s);var f=function(){e(i),Wq(i,s)};f()})),Ma(Au(),(function(e,o){o.stop(),tK(e,t,n,mK,o.event)})),Ma(Bu(),(function(t,n){n.stop(),r(t)})),Ma(Mu(),r)]},pK=B(B([],qq,!0),[df("dragger",{handlers:eK(gK)})],!1),hK=function(t,n,e){return B(B([],aK(t,n,e),!0),gK(t,n,e),!0)},vK=B(B([],qq,!0),[df("dragger",{handlers:eK(hK)})],!1),bK=cK,yK=pK,xK=vK,wK=Object.freeze({__proto__:null,mouse:bK,touch:yK,mouseOrTouch:xK}),SK=function(){var t=N.none(),n=N.none(),e=function(){t=N.none(),n=N.none()},o=function(n,e){var o=t.map((function(t){return n.getDelta(t,e)}));return t=N.some(e),o},r=function(t,n){return t.getData(n).bind((function(n){return o(t,n)}))},i=function(t){n=N.some(t)},u=function(){return n},a=x({});return Oc({readState:a,reset:e,update:r,getStartData:u,setStartData:i})},kK=Object.freeze({__proto__:null,init:SK}),CK=vl({branchKey:"mode",branches:wK,name:"dragging",active:{events:function(t,n){var e=t.dragger;return e.handlers(t,n)}},extra:{snap:function(t){return{sensor:t.sensor,range:t.range,output:t.output,extra:N.from(t.extra)}}},state:kK,apis:Lq}),OK=40,_K=OK/2,TK=function(t,n,e,o,r,i){return t.fold((function(){return CK.snap({sensor:_q(e-_K,o-_K),range:fo(r,i),output:_q(N.some(e),N.some(o)),extra:{td:n}})}),(function(t){var r=e-_K,i=o-_K,u=OK,a=OK,c=t.element.dom.getBoundingClientRect();return CK.snap({sensor:_q(r,i),range:fo(u,a),output:_q(N.some(e-c.width/2),N.some(o-c.height/2)),extra:{td:n}})}))},EK=function(t,n,e){var o=function(t,n){return t.exists((function(t){return se(t,n)}))};return{getSnapPoints:t,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(t,r){var i=r.td;o(n.get(),i)||(n.set(i),e(i))},mustSnap:!0}},DK=function(t){return TS(kS.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:ml([CK.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:t}),JB.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))},AK=function(t,n){var e=ru([]),o=ru([]),r=ru(!1),i=rd(),u=rd(),a=function(t){var e=Zo(t);return TK(m.getOpt(n),t,e.x,e.y,e.width,e.height)},c=function(){return X(e.get(),(function(t){return a(t)}))},s=function(t){var e=Zo(t);return TK(g.getOpt(n),t,e.right,e.bottom,e.width,e.height)},f=function(){return X(o.get(),(function(t){return s(t)}))},l=EK(c,i,(function(n){u.get().each((function(e){t.fire("TableSelectorChange",{start:n,finish:e})}))})),d=EK(f,u,(function(n){i.get().each((function(e){t.fire("TableSelectorChange",{start:e,finish:n})}))})),m=DK(l),g=DK(d),p=Ts(m.asSpec()),h=Ts(g.asSpec()),v=function(n,e,o,r){var i=e.dom.getBoundingClientRect();eo(n.element,"display");var u=he(nn.fromDom(t.getBody())).dom.innerHeight,a=o(i),c=r(i,u);(a||c)&&qe(n.element,"display","none")},b=function(t,n,e,o){var r=e(n);CK.snapTo(t,r);var i=function(t){return t[o]<0},u=function(t,n){return t[o]>n};v(t,n,i,u)},y=function(t){return b(p,t,a,"top")},x=function(){return i.get().each(y)},w=function(t){return b(h,t,s,"bottom")},S=function(){return u.get().each(w)};ee().deviceType.isTouch()&&(t.on("TableSelectionChange",(function(t){r.get()||(Wg(n,p),Wg(n,h),r.set(!0)),i.set(t.start),u.set(t.finish),t.otherCells.each((function(n){e.set(n.upOrLeftCells),o.set(n.downOrRightCells),y(t.start),w(t.finish)}))})),t.on("ResizeEditor ResizeWindow ScrollContent",(function(){x(),S()})),t.on("TableSelectionClear",(function(){r.get()&&(Yg(p),Yg(h),r.set(!1)),i.clear(),u.clear()})))},BK=function(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return!0;if("bookmark"===t.getAttribute("data-mce-type"))return!0}return!1},MK=function(t,n,e){n.delimiter||(n.delimiter="»");var o=function(o){var r=o||[],i=X(r,(function(n,o){return kS.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":o,"tab-index":-1,"aria-level":o+1},innerHtml:n.name},action:function(e){t.focus(),t.selection.select(n.element),t.nodeChanged()},buttonBehaviours:ml([ST.button(e.isDisabled),vT()])})})),u={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+n.delimiter+" "}};return Q(i.slice(1),(function(t,n){var e=t;return e.push(u),e.push(n),e}),[i[0]])},r=function(n){var e=[],o=n.length;while(o-- >0){var r=n[o];if(1===r.nodeType&&!BK(r)){var i=t.fire("ResolveName",{name:r.nodeName.toLowerCase(),target:r});if(i.isDefaultPrevented()||e.push({name:i.name,element:r}),i.isPropagationStopped())break}}return e};return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:ml([Dx.config({mode:"flow",selector:"div[role=button]"}),Pv.config({disabled:e.isDisabled}),vT(),EA.config({}),Vx.config({}),zx("elementPathEvents",[Ua((function(n,e){t.shortcuts.add("alt+F11","focus statusbar elementpath",(function(){return Dx.focusIn(n)})),t.on("NodeChange",(function(t){var e=r(t.parents);e.length>0?Vx.set(n,o(e)):Vx.set(n,[])}))}))])]),components:[]}};(function(t){t[t["None"]=0]="None",t[t["Both"]=1]="Both",t[t["Vertical"]=2]="Vertical"})(NW||(NW={}));var FK=function(t,n,e,o,r){var i={};return i.height=iX(o+n.top,D_(t),B_(t)),e===NW.Both&&(i.width=iX(r+n.left,E_(t),A_(t))),i},IK=function(t,n,e){var o=nn.fromDom(t.getContainer()),r=FK(t,n,e,uo(o),bo(o));vt(r,(function(t,n){return qe(o,n,rX(t))})),_E(t)},RK=function(t){var n=!t.hasPlugin("autoresize"),e=t.getParam("resize",n);return!1===e?NW.None:"both"===e?NW.Both:NW.Vertical},NK=function(t,n,e,o){var r=20,i=fo(e*r,o*r);return IK(t,i,n),N.some(!0)},PK=function(t,n){var e=RK(t);return e===NW.None?N.none():N.some(Gk("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:n.translate("Resize")},behaviours:[CK.config({mode:"mouse",repositionTarget:!1,onDrag:function(n,o,r){return IK(t,r,e)},blockerClass:"tox-blocker"}),Dx.config({mode:"special",onLeft:function(){return NK(t,e,-1,0)},onRight:function(){return NK(t,e,1,0)},onUp:function(){return NK(t,e,0,-1)},onDown:function(){return NK(t,e,0,1)}}),EA.config({}),Jx.config({})]},n.icons))},HK=function(t,n){var e,o=function(t,e,o){return Vx.set(t,[ks(n.translate(["{0} "+o,e[o]]))])};return kS.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:ml([ST.button(n.isDisabled),vT(),EA.config({}),Vx.config({}),th.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),zx("wordcount-events",[Ga((function(t){var n=th.getValue(t),e="words"===n.mode?"characters":"words";th.setValue(t,{mode:e,count:n.count}),o(t,n.count,e)})),Ua((function(n){t.on("wordCountUpdate",(function(t){var e=th.getValue(n).mode;th.setValue(n,{mode:e,count:t.wordCount}),o(n,t.wordCount,e)}))}))])]),eventOrder:(e={},e[na()]=["disabling","alloy.base.behaviour","wordcount-events"],e)})},VK=function(t,n){var e=function(){var t=Ik.translate(["Powered by {0}","Tiny"]),n='<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+t+'">'+t+"</a>";return{dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:n}}},o=function(){var o=[];return t.getParam("elementpath",!0,"boolean")&&o.push(MK(t,{},n)),t.hasPlugin("wordcount")&&o.push(HK(t,n)),t.getParam("branding",!0,"boolean")&&o.push(e()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:o}]:[]},r=function(){var e=o(),r=PK(t,n);return e.concat(r.toArray())};return{dom:{tag:"div",classes:["tox-statusbar"]},components:r()}},LK=function(t){var n,e=t.inline,o=e?gX:eX,r=tT(t)?Yz:QL,i=N.none(),u=ee(),a=u.browser.isIE(),c=a?["tox-platform-ie"]:[],s=u.deviceType.isTouch(),f="tox-platform-touch",l=s?[f]:[],d=X_(t),m=Q_(t),g=Ik.isRtl()?{attributes:{dir:"rtl"}}:{},p={attributes:(n={},n[Pd]=d?Vl.BottomToTop:Vl.TopToBottom,n)},v=function(){return i.bind(mW.getHeader)},b=function(){return r.isDocked(v)},y=function(){qe(Q.element,"width",document.body.clientWidth+"px")},x=function(){var t=se(Pe(),m)&&"grid"===$e(m,"display"),n={dom:D({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c).concat(l)},g),behaviours:ml([Hg.config({useFixed:function(){return b()}})])},e={dom:{styles:{width:document.body.clientWidth+"px"}},events:Ea([Ma(la(),y)])};return Sr(n,t?e:{})},w=Ts(x()),S=function(){return rr.value(w)},k=TS({dom:{tag:"div",classes:["tox-anchorbar"]}}),C=function(){return i.bind((function(t){return k.getOpt(t)})).getOrDie("Could not find a anchor bar element")},O=function(){return i.bind((function(t){return mW.getToolbar(t)})).getOrDie("Could not find more toolbar element")},_=function(){return i.bind((function(t){return mW.getThrobber(t)})).getOrDie("Could not find throbber element")},T=FL(w,t,C),E=mW.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:T,onEscape:function(){t.focus()}}),A=U_(t),B=mW.parts.toolbar(D({dom:{tag:"div",classes:["tox-toolbar"]},getSink:S,providers:T.shared.providers,onEscape:function(){t.focus()},type:A,lazyToolbar:O,lazyHeader:function(){return v().getOrDie("Could not find header element")}},p)),M=mW.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:T.shared.providers,onEscape:function(){t.focus()},type:A}),F=mW.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),I=mW.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),R=mW.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:T}),P=t.getParam("statusbar",!0,"boolean"),H=P&&!e?N.some(VK(t,T.shared.providers)):N.none(),V={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[F,I]},L=L_(t),z=H_(t),U=P_(t),j=function(){return L?[M]:z?[B]:[]},W=mW.parts.header({dom:D({tag:"div",classes:["tox-editor-header"]},p),components:et([U?[E]:[],j(),$_(t)?[]:[k.asSpec()]]),sticky:tT(t),editor:t,sharedBackstage:T.shared}),G=et([d?[]:[W],e?[]:[V],d?[W]:[]]),X={dom:{tag:"div",classes:["tox-editor-container"]},components:G},Y=et([[X],e?[]:H.toArray(),[R]]),q=Z_(t),K=D(D({role:"application"},Ik.isRtl()?{dir:"rtl"}:{}),q?{"aria-hidden":"true"}:{}),J=Ts(mW.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(e?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(l).concat(c),styles:D({visibility:"hidden"},q?{opacity:"0",border:"0"}:{}),attributes:K},components:Y,behaviours:ml([vT(),Pv.config({disableClass:"tox-tinymce--disabled"}),Dx.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])}));i=N.some(J),t.shortcuts.add("alt+F9","focus menubar",(function(){mW.focusMenubar(J)})),t.shortcuts.add("alt+F10","focus toolbar",(function(){mW.focusToolbar(J)})),t.addCommand("ToggleToolbarDrawer",(function(){mW.toggleToolbarDrawer(J)})),t.addQueryStateHandler("ToggleToolbarDrawer",(function(){return mW.isToolbarDrawerToggled(J)}));var $=yA(J),Q=yA(w);IL(t,$,Q);var Z=function(){var t={broadcastAll:Q.broadcast,broadcastOn:Q.broadcastOn,register:h};return{channels:t}},tt=function(){var n=rX(aX(t)),e=rX(sX(t));return t.inline||(no("div","width",e)&&qe(J.element,"width",e),no("div","height",n)?qe(J.element,"height",n):qe(J.element,"height","200px")),n},nt=function(){r.setup(t,T.shared,v),BY(t,T),dq(t,S,T),zU(t),lj(t,_,T.shared),bt(W_(t),(function(n,e){t.ui.registry.addGroupToolbarButton(e,n)}));var n=t.ui.registry.getAll(),e=n.buttons,i=n.menuItems,u=n.contextToolbars,a=n.sidebars,c=V_(t),s={menuItems:i,menus:oT(t),menubar:rT(t),toolbar:c.getOrThunk((function(){return iT(t)})),allowToolbarGroups:A===$C.floating,buttons:e,sidebar:a};nY(t,u,w,{backstage:T}),AK(t,w);var f=t.getElement(),l=tt(),d={mothership:$,uiMothership:Q,outerContainer:J},m={targetNode:f,height:l};return o.render(t,d,s,T,m)};return{mothership:$,uiMothership:Q,backstage:T,renderUI:nt,getUi:Z}},zK=function(t,n){var e=N.from(Ue(t,"id")).fold((function(){var t=rc("dialog-describe");return Le(n,"id",t),t}),w);Le(t,"aria-describedby",e)},UK=function(t,n){var e=je(t,"id").fold((function(){var t=rc("dialog-label");return Le(n,"id",t),t}),w);Le(t,"aria-labelledby",e)},jK=x([Di("lazySink"),Li("dragBlockClass"),tu("getBounds",tr),qi("useTabstopAt",T),qi("eventOrder",{}),nh("modalBehaviours",[Dx]),sf("onExecute"),lf("onEscape")]),WK={sketch:w},GK=x([Rh({name:"draghandle",overrides:function(t,n){return{behaviours:ml([CK.config({mode:"mouse",getTarget:function(t){return Ns(t,'[role="dialog"]').getOr(t)},blockerClass:t.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(n,null,2)).message),getBounds:t.getDragBounds})])}}}),Fh({schema:[Di("dom")],name:"title"}),Fh({factory:WK,schema:[Di("dom")],name:"close"}),Fh({factory:WK,schema:[Di("dom")],name:"body"}),Rh({factory:WK,schema:[Di("dom")],name:"footer"}),Ih({factory:{sketch:function(t,n){return D(D({},t),{dom:n.dom,components:n.components})}},schema:[qi("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),qi("components",[])],name:"blocker"})]),XK=function(t,n,e,o){var r,i=rd(),u=function(n){i.set(n);var e=t.lazySink(n).getOrDie(),r=o.blocker(),u=e.getSystem().build(D(D({},r),{components:r.components.concat([Es(n)]),behaviours:ml([Jx.config({}),zx("dialog-blocker-events",[za(Hu(),(function(){Dx.focusIn(n)}))])])}));Wg(e,u),Dx.focusIn(n)},a=function(t){i.clear(),ve(t.element).each((function(n){t.getSystem().getByDom(n).each((function(t){Yg(t)}))}))},c=function(n){return Kh(n,t,"body")},s=function(n){return Kh(n,t,"footer")},f=function(t,n){oj.block(t,n)},l=function(t){oj.unblock(t)},d=rc("modal-events"),m=D(D({},t.eventOrder),(r={},r[da()]=[d].concat(t.eventOrder["alloy.system.attached"]||[]),r));return{uid:t.uid,dom:t.dom,components:n,apis:{show:u,hide:a,getBody:c,getFooter:s,setIdle:l,setBusy:f},eventOrder:m,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:oh(t.modalBehaviours,[Vx.config({}),Dx.config({mode:"cyclic",onEnter:t.onExecute,onEscape:t.onEscape,useTabstopAt:t.useTabstopAt}),oj.config({getRoot:i.get}),zx(d,[Ua((function(n){UK(n.element,Kh(n,t,"title").element),zK(n.element,Kh(n,t,"body").element)}))])])}},YK=mv({name:"ModalDialog",configFields:jK(),partFields:GK(),factory:XK,apis:{show:function(t,n){t.show(n)},hide:function(t,n){t.hide(n)},getBody:function(t,n){return t.getBody(n)},getFooter:function(t,n){return t.getFooter(n)},setBusy:function(t,n,e){t.setBusy(n,e)},setIdle:function(t,n){t.setIdle(n)}}}),qK=ti([Mi("type"),Mi("name")].concat(UO)),KK=fi,JK=[_i("name","name",Or((function(){return rc("button-name")})),si),ji("icon"),Qi("align","end",["start","end"]),Zi("primary",!1),Zi("disabled",!1)],$K=B(B([],JK,!0),[Mi("text")],!1),QK=B([Fi("type",["submit","cancel","custom"])],$K,!0),ZK=B([Fi("type",["menu"]),ji("text"),ji("tooltip"),ji("icon"),Vi("items",qK)],JK,!0),tJ=Ci("type",{submit:QK,cancel:QK,custom:QK,menu:ZK}),nJ=[Mi("type"),Mi("text"),Fi("level",["info","warn","error","success"]),Mi("icon"),qi("url","")],eJ=ti(nJ),oJ=function(t){return[Mi("type"),t]},rJ=[Mi("type"),Mi("text"),Zi("disabled",!1),Zi("primary",!1),_i("name","name",Or((function(){return rc("button-name")})),si),ji("icon"),Zi("borderless",!1)],iJ=ti(rJ),uJ=[Mi("type"),Mi("name"),Mi("label"),Zi("disabled",!1)],aJ=ti(uJ),cJ=fi,sJ=[Mi("type"),Mi("name")],fJ=sJ.concat([ji("label")]),lJ=fJ.concat([qi("columns","auto")]),dJ=ti(lJ),mJ=ii([Mi("value"),Mi("text"),Mi("icon")]),gJ=fJ,pJ=ti(gJ),hJ=si,vJ=fJ,bJ=ti(vJ),yJ=si,xJ=sJ.concat([$i("tag","textarea"),Mi("scriptId"),Mi("scriptUrl"),nu("settings",void 0)]),wJ=sJ.concat([$i("tag","textarea"),Ri("init")]),SJ=vi((function(t){return xi("customeditor.old",Zr(wJ),t).orThunk((function(){return xi("customeditor.new",Zr(xJ),t)}))})),kJ=si,CJ=fJ,OJ=ti(CJ),_J=hi(),TJ=function(t){return[Mi("type"),Bi("columns"),t]},EJ=[Mi("type"),Mi("html"),Qi("presets","presentation",["presentation","document"])],DJ=ti(EJ),AJ=fJ.concat([Zi("sandboxed",!0)]),BJ=ti(AJ),MJ=si,FJ=fJ.concat([Ai("currentState",ti([Di("blob"),Mi("url")]))]),IJ=ti(FJ),RJ=fJ.concat([ji("inputMode"),ji("placeholder"),Zi("maximized",!1),Zi("disabled",!1)]),NJ=ti(RJ),PJ=si,HJ=function(t){return[Mi("type"),Mi("label"),t]},VJ=[Mi("text"),Mi("value")],LJ=[Mi("text"),Vi("items",Oi("items",(function(){return zJ})))],zJ=ei([ti(VJ),ti(LJ)]),UJ=fJ.concat([Vi("items",zJ),Zi("disabled",!1)]),jJ=ti(UJ),WJ=si,GJ=fJ.concat([Hi("items",[Mi("text"),Mi("value")]),Ji("size",1),Zi("disabled",!1)]),XJ=ti(GJ),YJ=si,qJ=fJ.concat([Zi("constrain",!0),Zi("disabled",!1)]),KJ=ti(qJ),JJ=ti([Mi("width"),Mi("height")]),$J=[Mi("type"),Vi("header",si),Vi("cells",ni(si))],QJ=ti($J),ZJ=fJ.concat([ji("placeholder"),Zi("maximized",!1),Zi("disabled",!1)]),t$=ti(ZJ),n$=si,e$=fJ.concat([Qi("filetype","file",["image","media","file"]),qi("disabled",!1)]),o$=ti(e$),r$=ti([Mi("value"),qi("meta",{})]),i$=function(t){return _i("items","items",Cr(),ni(vi((function(n){return xi("Checking item of "+t,u$,n).fold((function(t){return rr.error(ki(t))}),(function(t){return rr.value(t)}))}))))},u$=$r((function(){return pi("type",{alertbanner:eJ,bar:ti(oJ(i$("bar"))),button:iJ,checkbox:aJ,colorinput:pJ,colorpicker:bJ,dropzone:OJ,grid:ti(TJ(i$("grid"))),iframe:BJ,input:NJ,listbox:jJ,selectbox:XJ,sizeinput:KJ,textarea:t$,urlinput:o$,customeditor:SJ,htmlpanel:DJ,imagetools:IJ,collection:dJ,label:ti(HJ(i$("label"))),table:QJ,panel:c$})})),a$=[Mi("type"),qi("classes",[]),Vi("items",u$)],c$=ti(a$),s$=[_i("name","name",Or((function(){return rc("tab-name")})),si),Mi("title"),Vi("items",u$)],f$=[Mi("type"),Hi("tabs",s$)],l$=ti(f$),d$=$K,m$=tJ,g$=ti([Mi("title"),Ai("body",pi("type",{panel:c$,tabpanel:l$})),$i("size","normal"),Vi("buttons",m$),qi("initialData",{}),tu("onAction",h),tu("onChange",h),tu("onSubmit",h),tu("onClose",h),tu("onCancel",h),qi("onTabChange",h)]),p$=function(t){return xi("dialog",g$,t)},h$=ti(B([Fi("type",["cancel","custom"])],d$,!0)),v$=ti([Mi("title"),Mi("url"),Ui("height"),Ui("width"),Gi("buttons",h$),tu("onAction",h),tu("onCancel",h),tu("onClose",h),tu("onMessage",h)]),b$=function(t){return xi("dialog",v$,t)},y$=function(t){return u(t)?[t].concat(ot(Ot(t),y$)):a(t)?ot(t,y$):[]},x$=function(t){return i(t.type)&&i(t.name)},w$={checkbox:cJ,colorinput:hJ,colorpicker:yJ,dropzone:_J,input:PJ,iframe:MJ,sizeinput:JJ,selectbox:YJ,listbox:WJ,size:JJ,textarea:n$,urlinput:r$,customeditor:kJ,collection:mJ,togglemenuitem:KK},S$=function(t){return N.from(w$[t.type])},k$=function(t){return J(y$(t),x$)},C$=function(t){var n=k$(t),e=ot(n,(function(t){return S$(t).fold((function(){return[]}),(function(n){return[Ai(t.name,n)]}))}));return ti(e)},O$=function(t){var n=wi(p$(t)),e=C$(t),o=t.initialData;return{internalDialog:n,dataValidator:e,initialData:o}},_$={open:function(t,n){var e=O$(n);return t(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(t,n){var e=wi(b$(n));return t(e)},redial:function(t){return O$(t)}},T$=function(t){var n=[],e={};return vt(t,(function(t,o){t.fold((function(){n.push(o)}),(function(t){e[o]=t}))})),n.length>0?rr.error(n):rr.value(e)},E$=function(t,n){var e=TS(UI.sketch((function(e){return{dom:{tag:"div",classes:["tox-form"].concat(t.classes)},components:X(t.items,(function(t){return hV(e,t,n)}))}})));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[e.asSpec()]}],behaviours:ml([Dx.config({mode:"acyclic",useTabstopAt:C(_R)}),ZI.memento(e),pR.memento(e,{postprocess:function(t){return T$(t).fold((function(t){return{}}),w)}})])}},D$=function(t,n){return{uid:t.uid,dom:t.dom,components:t.components,events:vw(t.action),behaviours:oh(t.tabButtonBehaviours,[Jx.config({}),Dx.config({mode:"execution",useSpace:!0,useEnter:!0}),th.config({store:{mode:"memory",initialValue:t.value}})]),domModification:t.domModification}},A$=dv({name:"TabButton",configFields:[qi("uid",void 0),Di("value"),_i("dom","dom",Er((function(){return{attributes:{role:"tab",id:rc("aria"),"aria-selected":"false"}}})),ui()),Li("action"),qi("domModification",{}),nh("tabButtonBehaviours",[Jx,Dx,th]),Di("view")],factory:D$}),B$=x([Di("tabs"),Di("dom"),qi("clickToDismiss",!1),nh("tabbarBehaviours",[ob,Dx]),uf(["tabClass","selectedClass"])]),M$=Nh({factory:A$,name:"tabs",unit:"tab",overrides:function(t){var n=function(t,n){ob.dehighlight(t,n),ka(t,ya(),{tabbar:t,button:n})},e=function(t,n){ob.highlight(t,n),ka(t,ba(),{tabbar:t,button:n})};return{action:function(o){var r=o.getSystem().getByUid(t.uid).getOrDie(),i=ob.isHighlighted(r,o),u=function(){return i&&t.clickToDismiss?n:i?h:e}();u(r,o)},domModification:{classes:[t.markers.tabClass]}}}}),F$=x([M$]),I$=function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:oh(t.tabbarBehaviours,[ob.config({highlightClass:t.markers.selectedClass,itemClass:t.markers.tabClass,onHighlight:function(t,n){Le(n.element,"aria-selected","true")},onDehighlight:function(t,n){Le(n.element,"aria-selected","false")}}),Dx.config({mode:"flow",getInitial:function(t){return ob.getHighlighted(t).map((function(t){return t.element}))},selector:"."+t.markers.tabClass,executeOnMove:!0})])}},R$=mv({name:"Tabbar",configFields:B$(),partFields:F$(),factory:I$}),N$=function(t,n){return{uid:t.uid,dom:t.dom,behaviours:oh(t.tabviewBehaviours,[Vx.config({})]),domModification:{attributes:{role:"tabpanel"}}}},P$=dv({name:"Tabview",configFields:[nh("tabviewBehaviours",[Vx])],factory:N$}),H$=x([qi("selectFirst",!0),cf("onChangeTab"),cf("onDismissTab"),qi("tabs",[]),nh("tabSectionBehaviours",[])]),V$=Fh({factory:R$,schema:[Di("dom"),Pi("markers",[Di("tabClass"),Di("selectedClass")])],name:"tabbar",defaults:function(t){return{tabs:t.tabs}}}),L$=Fh({factory:P$,name:"tabview"}),z$=x([V$,L$]),U$=function(t,n,e,o){var r=function(n){var e=th.getValue(n);qh(n,t,"tabview").each((function(o){var r=tt(t.tabs,(function(t){return t.value===e}));r.each((function(e){var r=e.view();je(n.element,"id").each((function(t){Le(o.element,"aria-labelledby",t)})),Vx.set(o,r),t.onChangeTab(o,n,r)}))}))},i=function(n,e){qh(n,t,"tabbar").each((function(t){e(t).each(Ca)}))};return{uid:t.uid,dom:t.dom,components:n,behaviours:eh(t.tabSectionBehaviours),events:Ea(et([t.selectFirst?[Ua((function(t,n){i(t,ob.getFirst)}))]:[],[Ma(ba(),(function(t,n){var e=n.event.button;r(e)})),Ma(ya(),(function(n,e){var o=e.event.button;t.onDismissTab(n,o)}))]])),apis:{getViewItems:function(n){return qh(n,t,"tabview").map((function(t){return Vx.contents(t)})).getOr([])},showTab:function(t,n){var e=function(t){var e=ob.getCandidates(t),o=tt(e,(function(t){return th.getValue(t)===n}));return o.filter((function(n){return!ob.isHighlighted(t,n)}))};i(t,e)}}}},j$=mv({name:"TabSection",configFields:H$(),partFields:z$(),factory:U$,apis:{getViewItems:function(t,n){return t.getViewItems(n)},showTab:function(t,n,e){t.showTab(n,e)}}}),W$=function(t,n,e){return X(t,(function(o,r){Vx.set(e,t[r].view());var i=n.dom.getBoundingClientRect();return Vx.set(e,[]),i.height}))},G$=function(t){return lt(st(t,(function(t,n){return t>n?-1:t<n?1:0})))},X$=function(t,n,e){var o,r=pe(t).dom,i=Ns(t,".tox-dialog-wrap").getOr(t),u="fixed"===$e(i,"position");o=u?Math.max(r.clientHeight,window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight);var a=uo(n),c=n.dom.offsetLeft>=e.dom.offsetLeft+bo(e),s=c?Math.max(uo(e),a):a,f=parseInt($e(t,"margin-top"),10)||0,l=parseInt($e(t,"margin-bottom"),10)||0,d=uo(t)+f+l,m=d-s;return o-m},Y$=function(t,n){lt(t).each((function(t){return j$.showTab(n,t.value)}))},q$=function(t,n){qe(t,"height",n+"px"),ee().browser.isIE()?eo(t,"flex-basis"):qe(t,"flex-basis",n+"px")},K$=function(t,n,e){Ns(t,'[role="dialog"]').each((function(t){Hs(t,'[role="tablist"]').each((function(o){e.get().map((function(e){return qe(n,"height","0"),qe(n,"flex-basis","0"),Math.min(e,X$(t,n,o))})).each((function(t){q$(n,t)}))}))}))},J$=function(t){return Hs(t,'[role="tabpanel"]')},$$=function(t){var n=function(){var n=rd(),e=[Ua((function(e){var o=e.element;J$(o).each((function(r){qe(r,"visibility","hidden"),e.getSystem().getByDom(r).toOptional().each((function(e){var o=W$(t,r,e),i=G$(o);i.fold(n.clear,n.set)})),K$(o,r,n),eo(r,"visibility"),Y$(t,e),wS.requestAnimationFrame((function(){K$(o,r,n)}))}))})),Ma(la(),(function(t){var e=t.element;J$(e).each((function(t){K$(e,t,n)}))})),Ma(WA,(function(t,e){var o=t.element;J$(o).each((function(t){var e=_l(De(t));qe(t,"visibility","hidden");var r=Ze(t,"height").map((function(t){return parseInt(t,10)}));eo(t,"height"),eo(t,"flex-basis");var i=t.dom.getBoundingClientRect().height,u=r.forall((function(t){return i>t}));u?(n.set(i),K$(o,t,n)):r.each((function(n){q$(t,n)})),eo(t,"visibility"),e.each(kl)}))}))],o=!1;return{extraEvents:e,selectFirst:o}}(),e=function(){var t=[],n=!0;return{extraEvents:t,selectFirst:n}}();return{smartTabHeight:n,naiveTabHeight:e}},Q$="send-data-to-section",Z$="send-data-to-view",tQ=function(t,n){var e=ru({}),o=function(t){var n=th.getValue(t),o=T$(n).getOr({}),r=e.get(),i=Sr(r,o);e.set(i)},r=function(t){var n=e.get();th.setValue(t,n)},i=ru(null),u=X(t.tabs,(function(t){return{value:t.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:n.shared.providers.translate(t.title)},view:function(){return[UI.sketch((function(e){return{dom:{tag:"div",classes:["tox-form"]},components:X(t.items,(function(t){return hV(e,t,n)})),formBehaviours:ml([Dx.config({mode:"acyclic",useTabstopAt:C(_R)}),zx("TabView.form.events",[Ua(r),ja(o)]),yl.config({channels:gu([{key:Q$,value:{onReceive:o}},{key:Z$,value:{onReceive:r}}])})])}}))]}}})),a=$$(u).smartTabHeight;return j$.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(t,n,e){var o=th.getValue(n);ka(t,jA,{name:o,oldName:i.get()}),i.set(o)},tabs:u,components:[j$.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[R$.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:ml([EA.config({})])}),j$.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:a.selectFirst,tabSectionBehaviours:ml([zx("tabpanel",a.extraEvents),Dx.config({mode:"acyclic"}),bv.config({find:function(t){return lt(j$.getViewItems(t))}}),th.config({store:{mode:"manual",getValue:function(t){return t.getSystem().broadcastOn([Q$],{}),e.get()},setValue:function(t,n){e.set(n),t.getSystem().broadcastOn([Z$],{})}}})])})},nQ=rc("update-dialog"),eQ=rc("update-title"),oQ=rc("update-body"),rQ=rc("update-footer"),iQ=rc("body-send-message"),uQ=function(t,n,e,o){var r=function(t){switch(t.body.type){case"tabpanel":return[tQ(t.body,e)];default:return[E$(t.body,e)]}},i=function(t,n){return N.some({isTabPanel:function(){return"tabpanel"===n.body.type}})},u={"aria-live":"polite"};return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:D(D({},n.map((function(t){return{id:t}})).getOr({})),o?u:{})},components:[],behaviours:ml([ZI.childAt(0),xG.config({channel:oQ,updateState:i,renderComponents:r,initialData:t})])}},aQ=function(t,n,e,o){return uQ(t,N.some(n),e,o)},cQ=function(t,n){var e=uQ(t,N.none(),n,!1);return YK.parts.body(e)},sQ=function(t){var n={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[kR({dom:{tag:"iframe",attributes:{src:t.url}},behaviours:ml([EA.config({}),Jx.config({})])})]}],behaviours:ml([Dx.config({mode:"acyclic",useTabstopAt:C(_R)})])};return YK.parts.body(n)},fQ=BT.deviceType.isTouch(),lQ=function(t,n){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[t,n]}},dQ=function(t,n){return YK.parts.close(kS.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close")}},action:t,buttonBehaviours:ml([EA.config({})])}))},mQ=function(){return YK.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})},gQ=function(t,n){return YK.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:_S("<p>"+Fk(n.translate(t))+"</p>")}]}]})},pQ=function(t){return YK.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:t})},hQ=function(t,n){return[bA.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:t}),bA.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:n})]},vQ=function(t){var n,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return YK.sketch({lazySink:t.lazySink,onEscape:function(n){return t.onEscape(n),N.some(!0)},useTabstopAt:function(t){return!_R(t)},dom:{tag:"div",classes:[e].concat(t.extraClasses),styles:D({position:"relative"},t.extraStyles)},components:B([t.header,t.body],t.footer.toArray(),!0),parts:{blocker:{dom:_S('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:fQ?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:ml(B([Jx.config({}),zx("dialog-events",t.dialogEvents.concat([za(Hu(),(function(t,n){Dx.focusIn(t)}))])),zx("scroll-lock",[Ua((function(){is(Pe(),i)})),ja((function(){as(Pe(),i)}))])],t.extraBehaviours,!0)),eventOrder:D((n={},n[na()]=["dialog-events"],n[da()]=["scroll-lock","dialog-events","alloy.base.behaviour"],n[ma()]=["alloy.base.behaviour","dialog-events","scroll-lock"],n),t.eventOrder)})},bQ=function(t){return kS.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close"),title:t.translate("Close")}},components:[Gk("close",{tag:"div",classes:["tox-icon"]},t.icons)],action:function(t){Sa(t,HA)}})},yQ=function(t,n,e){var o=function(t){return[ks(e.translate(t.title))]};return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:D({},n.map((function(t){return{id:t}})).getOr({}))},components:o(t),behaviours:ml([xG.config({channel:eQ,renderComponents:o})])}},xQ=function(){return{dom:_S('<div class="tox-dialog__draghandle"></div>')}},wQ=function(t,n,e){return bA.sketch({dom:_S('<div class="tox-dialog__header"></div>'),components:[yQ(t,N.some(n),e),xQ(),bQ(e)],containerBehaviours:ml([CK.config({mode:"mouse",blockerClass:"blocker",getTarget:function(t){return Vs(t,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])})},SQ=function(t,n){var e=YK.parts.title(yQ(t,N.none(),n)),o=YK.parts.draghandle(xQ()),r=YK.parts.close(bQ(n)),i=[e].concat(t.draggable?[o]:[]).concat([r]);return bA.sketch({dom:_S('<div class="tox-dialog__header"></div>'),components:i})},kQ=function(t,n){return SQ({title:n.shared.providers.translate(t),draggable:n.dialog.isDraggableModal()},n.shared.providers)},CQ=function(t,n,e){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.translate(t)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:n,components:[{dom:_S('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}},OQ=function(t,n,e){return{onClose:function(){return e.closeWindow()},onBlock:function(e){YK.setBusy(t(),(function(t,o){return CQ(e.message,o,n)}))},onUnblock:function(){YK.setIdle(t())}}},_Q=function(t,n,e,o){var r,i=function(t,n){return N.some(n)};return Ts(vQ(D(D({},t),{lazySink:o.shared.getSink,extraBehaviours:B([xG.config({channel:nQ,updateState:i,initialData:n}),pR.memory({})],t.extraBehaviours,!0),onEscape:function(t){Sa(t,HA)},dialogEvents:e,eventOrder:(r={},r[ta()]=[xG.name(),yl.name()],r[da()]=["scroll-lock",xG.name(),"messages","dialog-events","alloy.base.behaviour"],r[ma()]=["alloy.base.behaviour","dialog-events","messages",xG.name(),"scroll-lock"],r)})))},TQ=function(t){var n=function(t){var n=X(t.items,(function(t){var n=ru(!1);return D(D({},t),{storage:n})}));return D(D({},t),{items:n})};return X(t,(function(t){return"menu"===t.type?n(t):t}))},EQ=function(t){return Q(t,(function(t,n){if("menu"===n.type){var e=n;return Q(e.items,(function(t,n){return t[n.name]=n.storage,t}),t)}return t}),{})},DQ=function(t,n){return[Ha(Hu(),OR),t(PA,(function(t,e){n.onClose(),e.onClose()})),t(HA,(function(t,n,e,o){n.onCancel(t),Sa(o,PA)})),Ma(UA,(function(t,e){return n.onUnblock()})),Ma(zA,(function(t,e){return n.onBlock(e.event)}))]},AQ=function(t,n){var e=function(n,e){return Ma(n,(function(n,r){o(n,(function(o,i){e(t(),o,r.event,n)}))}))},o=function(t,n){xG.getState(t).get().each((function(e){n(e,t)}))};return B(B([],DQ(e,n),!0),[e(VA,(function(t,n,e){n.onAction(t,{name:e.name})}))],!1)},BQ=function(t,n,e){var o=function(n,e){return Ma(n,(function(n,o){r(n,(function(r,i){e(t(),r,o.event,n)}))}))},r=function(t,n){xG.getState(t).get().each((function(e){n(e.internalDialog,t)}))};return B(B([],DQ(o,n),!0),[o(LA,(function(t,n){return n.onSubmit(t)})),o(NA,(function(t,n,e){n.onChange(t,{name:e.name})})),o(VA,(function(t,n,o,r){var i=function(){return Dx.focusIn(r)},u=function(t){return We(t,"disabled")||je(t,"aria-disabled").exists((function(t){return"true"===t}))},a=De(r.element),c=_l(a);n.onAction(t,{name:o.name,value:o.value}),_l(a).fold(i,(function(t){u(t)||c.exists((function(n){return de(t,n)&&u(n)}))?i():e().toOptional().filter((function(n){return!de(n.element,t)})).each(i)}))})),o(jA,(function(t,n,e){n.onTabChange(t,{newTabName:e.name,oldTabName:e.oldName})})),ja((function(n){var e=t();th.setValue(n,e.getData())}))],!1)},MQ={initUrlDialog:AQ,initDialog:BQ},FQ=function(t,n){return dP(t,t.type,n)},IQ=function(t,n,e){return tt(n,(function(t){return t.name===e})).bind((function(n){return n.memento.getOpt(t)}))},RQ=function(t,n){var e=n.map((function(t){return t.footerButtons})).getOr([]),o=K(e,(function(t){return"start"===t.align})),r=function(t,n){return bA.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+t]},components:X(n,(function(t){return t.memento.asSpec()}))})},i=r("start",o.pass),u=r("end",o.fail);return[i,u]},NQ=function(t,n){var e=function(t,e){var o=X(e.buttons,(function(t){var e=TS(FQ(t,n));return{name:t.name,align:t.align,memento:e}})),r=function(t,n){return IQ(t,o,n)};return N.some({lookupByName:r,footerButtons:o})};return{dom:_S('<div class="tox-dialog__footer"></div>'),components:[],behaviours:ml([xG.config({channel:rQ,initialData:t,updateState:e,renderComponents:RQ})])}},PQ=function(t,n){return NQ(t,n)},HQ=function(t,n){return YK.parts.footer(NQ(t,n))},VQ=function(t,n){var e=t.getRoot();if(e.getSystem().isConnected()){var o=bv.getCurrent(t.getFormWrapper()).getOr(t.getFormWrapper());return UI.getField(o,n).fold((function(){var e=t.getFooter(),r=xG.getState(e);return r.get().bind((function(t){return t.lookupByName(o,n)}))}),(function(t){return N.some(t)}))}return N.none()},LQ=function(t,n){var e=t.getRoot();return xG.getState(e).get().map((function(t){return wi(xi("data",t.dataValidator,n))})).getOr(n)},zQ=function(t,n,e){var o=function(n){var e=t.getRoot();e.getSystem().isConnected()&&n(e)},r=function(){var n=t.getRoot(),o=n.getSystem().isConnected()?t.getFormWrapper():n,r=th.getValue(o),i=bt(e,(function(t){return t.get()}));return D(D({},r),i)},u=function(n){o((function(o){var r=p.getData(),i=D(D({},r),n),u=LQ(t,i),a=t.getFormWrapper();th.setValue(a,u),vt(e,(function(t,n){Tt(i,n)&&t.set(i[n])}))}))},a=function(n){VQ(t,n).each(Pv.disable)},c=function(n){VQ(t,n).each(Pv.enable)},s=function(n){VQ(t,n).each(Jx.focus)},f=function(t){if(!i(t))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");o((function(n){ka(n,zA,{message:t})}))},l=function(){o((function(t){Sa(t,UA)}))},d=function(n){o((function(e){var o=t.getBody(),r=xG.getState(o);r.get().exists((function(t){return t.isTabPanel()}))&&bv.getCurrent(o).each((function(t){j$.showTab(t,n)}))}))},m=function(t){o((function(e){var o=n(t);e.getSystem().broadcastOn([nQ],o),e.getSystem().broadcastOn([eQ],o.internalDialog),e.getSystem().broadcastOn([oQ],o.internalDialog),e.getSystem().broadcastOn([rQ],o.internalDialog),p.setData(o.initialData)}))},g=function(){o((function(t){Sa(t,PA)}))},p={getData:r,setData:u,disable:a,enable:c,focus:s,block:f,unblock:l,showTab:d,redial:m,close:g};return p},UQ=function(t){switch(t){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}},jQ=function(t,n,e){var o=kQ(t.internalDialog.title,e),r=cQ({body:t.internalDialog.body},e),i=TQ(t.internalDialog.buttons),u=EQ(i),a=HQ({buttons:i},e),c=MQ.initDialog((function(){return m}),OQ((function(){return l}),e.shared.providers,n),e.shared.getSink),s=UQ(t.internalDialog.size),f={header:o,body:r,footer:N.some(a),extraClasses:s,extraBehaviours:[],extraStyles:{}},l=_Q(f,t,c,e),d=function(){var t=function(){var t=YK.getBody(l);return bv.getCurrent(t).getOr(t)};return{getRoot:x(l),getBody:function(){return YK.getBody(l)},getFooter:function(){return YK.getFooter(l)},getFormWrapper:t}}(),m=zQ(d,n.redial,u);return{dialog:l,instanceApi:m}},WQ=function(t,n,e,o){var r,i,u=rc("dialog-label"),a=rc("dialog-content"),c=function(t,n){return N.some(n)},s=TS(wQ({title:t.internalDialog.title,draggable:!0},u,e.shared.providers)),f=TS(aQ({body:t.internalDialog.body},a,e,o)),l=TQ(t.internalDialog.buttons),d=EQ(l),m=TS(PQ({buttons:l},e)),g=MQ.initDialog((function(){return h}),{onBlock:function(t){oj.block(p,(function(n,o){return CQ(t.message,o,e.shared.providers)}))},onUnblock:function(){oj.unblock(p)},onClose:function(){return n.closeWindow()}},e.shared.getSink),p=Ts({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:(r={role:"dialog"},r["aria-labelledby"]=u,r["aria-describedby"]=a,r)},eventOrder:(i={},i[ta()]=[xG.name(),yl.name()],i[na()]=["execute-on-form"],i[da()]=["reflecting","execute-on-form"],i),behaviours:ml([Dx.config({mode:"cyclic",onEscape:function(t){return Sa(t,PA),N.some(!0)},useTabstopAt:function(t){return!_R(t)&&("button"!==cn(t)||"disabled"!==Ue(t,"disabled"))}}),xG.config({channel:nQ,updateState:c,initialData:t}),Jx.config({}),zx("execute-on-form",g.concat([za(Hu(),(function(t,n){Dx.focusIn(t)}))])),oj.config({getRoot:function(){return N.some(p)}}),Vx.config({}),pR.memory({})]),components:[s.asSpec(),f.asSpec(),m.asSpec()]}),h=zQ({getRoot:x(p),getFooter:function(){return m.get(p)},getBody:function(){return f.get(p)},getFormWrapper:function(){var t=f.get(p);return bv.getCurrent(t).getOr(t)}},n.redial,d);return{dialog:p,instanceApi:h}},GQ=tinymce.util.Tools.resolve("tinymce.util.URI"),XQ=function(t){var n=function(n){t.getSystem().isConnected()&&n(t)},e=function(t){if(!i(t))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((function(n){ka(n,zA,{message:t})}))},o=function(){n((function(t){Sa(t,UA)}))},r=function(){n((function(t){Sa(t,PA)}))},u=function(t){n((function(n){n.getSystem().broadcastOn([iQ],t)}))};return{block:e,unblock:o,close:r,sendMessage:u}},YQ=["insertContent","setContent","execCommand","close","block","unblock"],qQ=function(t){return u(t)&&-1!==YQ.indexOf(t.mceAction)},KQ=function(t){return!qQ(t)&&u(t)&&Tt(t,"mceAction")},JQ=function(t,n,e){switch(e.mceAction){case"insertContent":t.insertContent(e.content);break;case"setContent":t.setContent(e.content);break;case"execCommand":var o=!!s(e.ui)&&e.ui;t.execCommand(e.cmd,o,e.value);break;case"close":n.close();break;case"block":n.block(e.message);break;case"unblock":n.unblock();break}},$Q=function(t,n,e,o){var r,i=kQ(t.title,o),u=sQ(t),a=t.buttons.bind((function(t){return 0===t.length?N.none():N.some(HQ({buttons:t},o))})),c=MQ.initUrlDialog((function(){return v}),OQ((function(){return h}),o.shared.providers,n)),s=D(D({},t.height.fold((function(){return{}}),(function(t){return{height:t+"px","max-height":t+"px"}}))),t.width.fold((function(){return{}}),(function(t){return{width:t+"px","max-width":t+"px"}}))),f=t.width.isNone()&&t.height.isNone()?["tox-dialog--width-lg"]:[],l=new GQ(t.url,{base_uri:new GQ(window.location.href)}),d=l.protocol+"://"+l.host+(l.port?":"+l.port:""),m=ed(),g=[zx("messages",[Ua((function(){var n=ud(nn.fromDom(window),"message",(function(n){if(l.isSameOrigin(new GQ(n.raw.origin))){var o=n.raw.data;qQ(o)?JQ(e,v,o):KQ(o)&&t.onMessage(v,o)}}));m.set(n)})),ja(m.clear)]),yl.config({channels:(r={},r[iQ]={onReceive:function(t,n){Hs(t.element,"iframe").each((function(t){var e=t.dom.contentWindow;e.postMessage(n,d)}))}},r)})],p={header:i,body:u,footer:a,extraClasses:f,extraBehaviours:g,extraStyles:s},h=_Q(p,t,c,o),v=XQ(h);return{dialog:h,instanceApi:v}},QQ=function(t){var n=t.backstage.shared,e=function(e,o){var r=function(){YK.hide(c),o()},i=TS(dP({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:N.none()},"cancel",t.backstage)),u=mQ(),a=dQ(r,n.providers),c=Ts(vQ({lazySink:function(){return n.getSink()},header:lQ(u,a),body:gQ(e,n.providers),footer:N.some(pQ(hQ([],[i.asSpec()]))),onEscape:r,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Ma(HA,r)],eventOrder:{}}));YK.show(c);var s=i.get(c);Jx.focus(s)};return{open:e}},ZQ=function(t){var n=t.backstage.shared,e=function(e,o){var r=function(t){YK.hide(s),o(t)},i=TS(dP({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:N.none()},"submit",t.backstage)),u=dP({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:N.none()},"cancel",t.backstage),a=mQ(),c=dQ((function(){return r(!1)}),n.providers),s=Ts(vQ({lazySink:function(){return n.getSink()},header:lQ(a,c),body:gQ(e,n.providers),footer:N.some(pQ(hQ([],[u,i.asSpec()]))),onEscape:function(){return r(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Ma(HA,(function(){return r(!1)})),Ma(LA,(function(){return r(!0)}))],eventOrder:{}}));YK.show(s);var f=i.get(s);Jx.focus(f)};return{open:e}},tZ=function(t,n){return wi(xi("data",n,t))},nZ=function(t){return GD(t,".tox-alert-dialog")||GD(t,".tox-confirm-dialog")},eZ=function(t,n,e){return n&&e?[]:[Bz.config({contextual:{lazyContext:function(){return N.some(Qo(nn.fromDom(t.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]},oZ=function(t){var n=t.backstage,e=t.editor,o=tT(e),r=QQ(t),i=ZQ(t),u=function(t,e,o){return void 0!==e&&"toolbar"===e.inline?f(t,n.shared.anchors.inlineDialog(),o,e.ariaAttrs):void 0!==e&&"cursor"===e.inline?f(t,n.shared.anchors.cursor(),o,e.ariaAttrs):s(t,o)},a=function(t,n){return c(t,n)},c=function(t,o){var r=function(t){var r=$Q(t,{closeWindow:function(){YK.hide(r.dialog),o(r.instanceApi)}},e,n);return YK.show(r.dialog),r.instanceApi};return _$.openUrl(r,t)},s=function(t,e){var o=function(t,o,r){var i=o,u={dataValidator:r,initialData:i,internalDialog:t},a=jQ(u,{redial:_$.redial,closeWindow:function(){YK.hide(a.dialog),e(a.instanceApi)}},n);return YK.show(a.dialog),a.instanceApi.setData(i),a.instanceApi};return _$.open(o,t)},f=function(t,r,i,u){var a=function(t,a,c){var s=tZ(a,c),f=rd(),l=n.shared.header.isPositionedAtTop(),d={dataValidator:c,initialData:s,internalDialog:t},m=function(){return f.on((function(t){nS.reposition(t),Bz.refresh(t)}))},g=WQ(d,{redial:_$.redial,closeWindow:function(){f.on(nS.hide),e.off("ResizeEditor",m),f.clear(),i(g.instanceApi)}},n,u),p=Ts(nS.sketch(D(D({lazySink:n.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},l?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:ml(B([zx("window-manager-inline-events",[Ma(ga(),(function(t,n){Sa(g.dialog,HA)}))])],eZ(e,o,l),!0)),isExtraPart:function(t,n){return nZ(n)}})));return f.set(p),nS.showWithin(p,Es(g.dialog),{anchor:r},N.some(Pe())),o&&l||(Bz.refresh(p),e.on("ResizeEditor",m)),g.instanceApi.setData(s),Dx.focusIn(g.dialog),g.instanceApi};return _$.open(a,t)},l=function(t,n){i.open(t,(function(t){n(t)}))},d=function(t,n){r.open(t,(function(){n()}))},m=function(t){t.close()};return{open:u,openUrl:a,alert:d,close:m,confirm:l}};function rZ(){E.add("silver",(function(t){var n=LK(t),e=n.uiMothership,o=n.backstage,r=n.renderUI,i=n.getUi;WD.register(t,o.shared);var u=oZ({editor:t,backstage:o});return{renderUI:r,getWindowManagerImpl:x(u),getNotificationManagerImpl:function(){return Jk(t,{backstage:o},e)},ui:i()}}))}rZ()})()}).call(this,e("c8ba"))}}]);