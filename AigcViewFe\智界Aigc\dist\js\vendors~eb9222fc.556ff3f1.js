(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~eb9222fc"],{"04f7":function(t,e,n){"use strict";n.d(e,"b",(function(){return b})),n.d(e,"a",(function(){return g}));var r=n("07fd"),i=n("e0d3"),o=n("6d8b"),a=n("2b17"),u=n("b7d9"),s=n("edae"),c=n("ec6f"),f=function(){function t(){}return t.prototype.getRawData=function(){throw new Error("not supported")},t.prototype.getRawDataItem=function(t){throw new Error("not supported")},t.prototype.cloneRawData=function(){},t.prototype.getDimensionInfo=function(t){},t.prototype.cloneAllDimensionInfo=function(){},t.prototype.count=function(){},t.prototype.retrieveValue=function(t,e){},t.prototype.retrieveValueFromItem=function(t,e){},t.prototype.convertValue=function(t,e){return Object(u["d"])(t,e)},t}();function l(t,e){var n=new f,i=t.data,u=n.sourceFormat=t.sourceFormat,c=t.startIndex,l="";t.seriesLayoutBy!==r["a"]&&Object(s["c"])(l);var v=[],b={},g=t.dimensionsDefine;if(g)Object(o["k"])(g,(function(t,e){var n=t.name,r={index:e,name:n,displayName:t.displayName};if(v.push(r),null!=n){var i="";Object(o["q"])(b,n)&&Object(s["c"])(i),b[n]=r}}));else for(var O=0;O<t.dimensionsDetectedCount;O++)v.push({index:O});var j=Object(a["c"])(u,r["a"]);e.__isBuiltIn&&(n.getRawDataItem=function(t){return j(i,c,v,t)},n.getRawData=Object(o["c"])(d,null,t)),n.cloneRawData=Object(o["c"])(m,null,t);var D=Object(a["b"])(u,r["a"]);n.count=Object(o["c"])(D,null,i,c,v);var y=Object(a["d"])(u);n.retrieveValue=function(t,e){var n=j(i,c,v,t);return _(n,e)};var _=n.retrieveValueFromItem=function(t,e){if(null!=t){var n=v[e];return n?y(t,e,n.name):void 0}};return n.getDimensionInfo=Object(o["c"])(p,null,v,b),n.cloneAllDimensionInfo=Object(o["c"])(h,null,v),n}function d(t){var e=t.sourceFormat;if(!j(e)){var n="";0,Object(s["c"])(n)}return t.data}function m(t){var e=t.sourceFormat,n=t.data;if(!j(e)){var i="";0,Object(s["c"])(i)}if(e===r["c"]){for(var a=[],u=0,c=n.length;u<c;u++)a.push(n[u].slice());return a}if(e===r["e"]){for(a=[],u=0,c=n.length;u<c;u++)a.push(Object(o["m"])({},n[u]));return a}}function p(t,e,n){if(null!=n)return Object(o["z"])(n)||!isNaN(n)&&!Object(o["q"])(e,n)?t[n]:Object(o["q"])(e,n)?e[n]:void 0}function h(t){return Object(o["d"])(t)}var v=Object(o["f"])();function b(t){t=Object(o["d"])(t);var e=t.type,n="";e||Object(s["c"])(n);var r=e.split(":");2!==r.length&&Object(s["c"])(n);var i=!1;"echarts"===r[0]&&(e=r[1],i=!0),t.__isBuiltIn=i,v.set(e,t)}function g(t,e,n){var r=Object(i["r"])(t),o=r.length,a="";o||Object(s["c"])(a);for(var u=0,c=o;u<c;u++){var f=r[u];e=O(f,e,n,1===o?null:u),u!==c-1&&(e.length=Math.max(e.length,1))}return e}function O(t,e,n,a){var u="";e.length||Object(s["c"])(u),Object(o["A"])(t)||Object(s["c"])(u);var f=t.type,d=v.get(f);d||Object(s["c"])(u);var m=Object(o["H"])(e,(function(t){return l(t,d)})),p=Object(i["r"])(d.transform({upstream:m[0],upstreamList:m,config:Object(o["d"])(t.config)}));return Object(o["H"])(p,(function(t,n){var i="";Object(o["A"])(t)||Object(s["c"])(i),t.data||Object(s["c"])(i);var a,u=Object(c["d"])(t.data);j(u)||Object(s["c"])(i);var f=e[0];if(f&&0===n&&!t.dimensions){var l=f.startIndex;l&&(t.data=f.data.slice(0,l).concat(t.data)),a={seriesLayoutBy:r["a"],sourceHeader:l,dimensions:f.metaRawOption.dimensions}}else a={seriesLayoutBy:r["a"],sourceHeader:0,dimensions:t.dimensions};return Object(c["b"])(t.data,a,null)}))}function j(t){return t===r["c"]||t===r["e"]}},"0f99":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"g",(function(){return s})),n.d(e,"c",(function(){return c})),n.d(e,"d",(function(){return f})),n.d(e,"f",(function(){return l})),n.d(e,"e",(function(){return d})),n.d(e,"b",(function(){return m}));var r=n("e0d3"),i=n("6d8b"),o=n("07fd"),a={Must:1,Might:2,Not:3},u=Object(r["o"])();function s(t){u(t).datasetMap=Object(i["f"])()}function c(t,e,n){var r={},o=l(e);if(!o||!t)return r;var a,s,c=[],f=[],d=e.ecModel,m=u(d).datasetMap,p=o.uid+"_"+n.seriesLayoutBy;t=t.slice(),Object(i["k"])(t,(function(e,n){var o=Object(i["A"])(e)?e:t[n]={name:e};"ordinal"===o.type&&null==a&&(a=n,s=b(o)),r[o.name]=[]}));var h=m.get(p)||m.set(p,{categoryWayDim:s,valueWayDim:0});function v(t,e,n){for(var r=0;r<n;r++)t.push(e+r)}function b(t){var e=t.dimsDef;return e?e.length:1}return Object(i["k"])(t,(function(t,e){var n=t.name,i=b(t);if(null==a){var o=h.valueWayDim;v(r[n],o,i),v(f,o,i),h.valueWayDim+=i}else if(a===e)v(r[n],0,i),v(c,0,i);else{o=h.categoryWayDim;v(r[n],o,i),v(f,o,i),h.categoryWayDim+=i}})),c.length&&(r.itemName=c),f.length&&(r.seriesName=f),r}function f(t,e,n){var r={},u=l(t);if(!u)return r;var s,c=e.sourceFormat,f=e.dimensionsDefine;c!==o["e"]&&c!==o["d"]||Object(i["k"])(f,(function(t,e){"name"===(Object(i["A"])(t)?t.name:t)&&(s=e)}));var d=function(){for(var t={},r={},i=[],o=0,u=Math.min(5,n);o<u;o++){var l=p(e.data,c,e.seriesLayoutBy,f,e.startIndex,o);i.push(l);var d=l===a.Not;if(d&&null==t.v&&o!==s&&(t.v=o),(null==t.n||t.n===t.v||!d&&i[t.n]===a.Not)&&(t.n=o),m(t)&&i[t.n]!==a.Not)return t;d||(l===a.Might&&null==r.v&&o!==s&&(r.v=o),null!=r.n&&r.n!==r.v||(r.n=o))}function m(t){return null!=t.v&&null!=t.n}return m(t)?t:m(r)?r:null}();if(d){r.value=[d.v];var m=null!=s?s:d.n;r.itemName=[m],r.seriesName=[m]}return r}function l(t){var e=t.get("data",!0);if(!e)return Object(r["v"])(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},r["b"]).models[0]}function d(t){return t.get("transform",!0)||t.get("fromTransformResult",!0)?Object(r["v"])(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},r["b"]).models:[]}function m(t,e){return p(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function p(t,e,n,u,s,c){var f,l,d,m=5;if(Object(i["E"])(t))return a.Not;if(u){var p=u[c];Object(i["A"])(p)?(l=p.name,d=p.type):Object(i["C"])(p)&&(l=p)}if(null!=d)return"ordinal"===d?a.Must:a.Not;if(e===o["c"]){var h=t;if(n===o["b"]){for(var v=h[c],b=0;b<(v||[]).length&&b<m;b++)if(null!=(f=S(v[s+b])))return f}else for(b=0;b<h.length&&b<m;b++){var g=h[s+b];if(g&&null!=(f=S(g[c])))return f}}else if(e===o["e"]){var O=t;if(!l)return a.Not;for(b=0;b<O.length&&b<m;b++){var j=O[b];if(j&&null!=(f=S(j[l])))return f}}else if(e===o["d"]){var D=t;if(!l)return a.Not;v=D[l];if(!v||Object(i["E"])(v))return a.Not;for(b=0;b<v.length&&b<m;b++)if(null!=(f=S(v[b])))return f}else if(e===o["f"]){var y=t;for(b=0;b<y.length&&b<m;b++){j=y[b];var _=Object(r["h"])(j);if(!Object(i["t"])(_))return a.Not;if(null!=(f=S(_[c])))return f}}function S(t){var e=Object(i["C"])(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?a.Might:a.Not:e&&"-"!==t?a.Must:void 0}return a.Not}},"22b4":function(t,e,n){"use strict";n.d(e,"a",(function(){return m}));var r=n("1be7"),i=n("b12f"),o=n("e887"),a=n("6cb7"),u=n("4f85"),s=n("6d8b"),c=n("58c9"),f=n("697e7"),l=[],d={registerPreprocessor:r["j"],registerProcessor:r["k"],registerPostInit:r["h"],registerPostUpdate:r["i"],registerUpdateLifecycle:r["m"],registerAction:r["c"],registerCoordinateSystem:r["d"],registerLayout:r["e"],registerVisual:r["n"],registerTransform:r["l"],registerLoading:r["f"],registerMap:r["g"],registerImpl:c["b"],PRIORITY:r["a"],ComponentModel:a["a"],ComponentView:i["a"],SeriesModel:u["b"],ChartView:o["a"],registerComponentModel:function(t){a["a"].registerClass(t)},registerComponentView:function(t){i["a"].registerClass(t)},registerSeriesModel:function(t){u["b"].registerClass(t)},registerChartView:function(t){o["a"].registerClass(t)},registerSubTypeDefaulter:function(t,e){a["a"].registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){Object(f["c"])(t,e)}};function m(t){Object(s["t"])(t)?Object(s["k"])(t,(function(t){m(t)})):Object(s["r"])(l,t)>=0||(l.push(t),Object(s["w"])(t)&&(t={install:t}),t.install(d))}},"2b17":function(t,e,n){"use strict";n.d(e,"a",(function(){return d})),n.d(e,"c",(function(){return h})),n.d(e,"b",(function(){return g})),n.d(e,"d",(function(){return D})),n.d(e,"e",(function(){return _}));var r,i,o,a,u,s=n("6d8b"),c=n("e0d3"),f=n("ec6f"),l=n("07fd"),d=function(){function t(t,e){var n=Object(f["e"])(t)?t:Object(f["c"])(t);this._source=n;var r=this._data=n.data;n.sourceFormat===l["g"]&&(this._offset=0,this._dimSize=e,this._data=r),u(this,r,n)}return t.prototype.getSource=function(){return this._source},t.prototype.count=function(){return 0},t.prototype.getItem=function(t,e){},t.prototype.appendData=function(t){},t.prototype.clean=function(){},t.protoInitialize=function(){var e=t.prototype;e.pure=!1,e.persistent=!0}(),t.internalField=function(){var t;u=function(t,i,o){var u=o.sourceFormat,c=o.seriesLayoutBy,f=o.startIndex,d=o.dimensionsDefine,m=a[y(u,c)];if(Object(s["m"])(t,m),u===l["g"])t.getItem=e,t.count=r,t.fillStorage=n;else{var p=h(u,c);t.getItem=Object(s["c"])(p,null,i,f,d);var v=g(u,c);t.count=Object(s["c"])(v,null,i,f,d)}};var e=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,r=this._dimSize,i=r*t,o=0;o<r;o++)e[o]=n[i+o];return e},n=function(t,e,n,r){for(var i=this._data,o=this._dimSize,a=0;a<o;a++){for(var u=r[a],s=null==u[0]?1/0:u[0],c=null==u[1]?-1/0:u[1],f=e-t,l=n[a],d=0;d<f;d++){var m=i[d*o+a];l[t+d]=m,m<s&&(s=m),m>c&&(c=m)}u[0]=s,u[1]=c}},r=function(){return this._data?this._data.length/this._dimSize:0};function i(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}t={},t[l["c"]+"_"+l["a"]]={pure:!0,appendData:i},t[l["c"]+"_"+l["b"]]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[l["e"]]={pure:!0,appendData:i},t[l["d"]]={pure:!0,appendData:function(t){var e=this._data;Object(s["k"])(t,(function(t,n){for(var r=e[n]||(e[n]=[]),i=0;i<(t||[]).length;i++)r.push(t[i])}))}},t[l["f"]]={appendData:i},t[l["g"]]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},a=t}(),t}(),m=function(t,e,n,r){return t[r]},p=(r={},r[l["c"]+"_"+l["a"]]=function(t,e,n,r){return t[r+e]},r[l["c"]+"_"+l["b"]]=function(t,e,n,r,i){r+=e;for(var o=i||[],a=t,u=0;u<a.length;u++){var s=a[u];o[u]=s?s[r]:null}return o},r[l["e"]]=m,r[l["d"]]=function(t,e,n,r,i){for(var o=i||[],a=0;a<n.length;a++){var u=n[a].name;0;var s=t[u];o[a]=s?s[r]:null}return o},r[l["f"]]=m,r);function h(t,e){var n=p[y(t,e)];return n}var v=function(t,e,n){return t.length},b=(i={},i[l["c"]+"_"+l["a"]]=function(t,e,n){return Math.max(0,t.length-e)},i[l["c"]+"_"+l["b"]]=function(t,e,n){var r=t[0];return r?Math.max(0,r.length-e):0},i[l["e"]]=v,i[l["d"]]=function(t,e,n){var r=n[0].name;var i=t[r];return i?i.length:0},i[l["f"]]=v,i);function g(t,e){var n=b[y(t,e)];return n}var O=function(t,e,n){return t[e]},j=(o={},o[l["c"]]=O,o[l["e"]]=function(t,e,n){return t[n]},o[l["d"]]=O,o[l["f"]]=function(t,e,n){var r=Object(c["h"])(t);return r instanceof Array?r[e]:r},o[l["g"]]=O,o);function D(t){var e=j[t];return e}function y(t,e){return t===l["c"]?t+"_"+e:t}function _(t,e,n){if(t){var r=t.getRawDataItem(e);if(null!=r){var i=t.getStore(),o=i.getSource().sourceFormat;if(null!=n){var a=t.getDimensionIndex(n),u=i.getDimensionProperty(a);return D(o)(r,a,u)}var s=r;return o===l["f"]&&(s=Object(c["h"])(r)),s}}}},"2f45":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return s}));var r=n("6d8b"),i=n("07fd"),o=function(){function t(t,e){this._encode=t,this._schema=e}return t.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},t.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},t}();function a(t,e){var n={},a=n.encode={},s=Object(r["f"])(),f=[],l=[],d={};Object(r["k"])(t.dimensions,(function(e){var n=t.getDimensionInfo(e),r=n.coordDim;if(r){0;var o=n.coordDimIndex;u(a,r)[o]=e,n.isExtraCoord||(s.set(r,1),c(n.type)&&(f[0]=e),u(d,r)[o]=t.getDimensionIndex(n.name)),n.defaultTooltip&&l.push(e)}i["i"].each((function(t,e){var r=u(a,e),i=n.otherDims[e];null!=i&&!1!==i&&(r[i]=n.name)}))}));var m=[],p={};s.each((function(t,e){var n=a[e];p[e]=n[0],m=m.concat(n)})),n.dataDimsOnCoord=m,n.dataDimIndicesOnCoord=Object(r["H"])(m,(function(e){return t.getDimensionInfo(e).storeDimIndex})),n.encodeFirstDimNotExtra=p;var h=a.label;h&&h.length&&(f=h.slice());var v=a.tooltip;return v&&v.length?l=v.slice():l.length||(l=f.slice()),a.defaultedLabel=f,a.defaultedTooltip=l,n.userOutput=new o(d,e),n}function u(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function s(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function c(t){return!("ordinal"===t||"time"===t)}},"6a2f":function(t,e,n){"use strict";var r=n("6d8b"),i=n("e0d3"),o=Object(i["o"])();function a(t){var e=t.mainData,n=t.datas;n||(n={main:e},t.datasAttr={main:"data"}),t.datas=t.mainData=null,m(e,n,t),Object(r["k"])(n,(function(n){Object(r["k"])(e.TRANSFERABLE_METHODS,(function(e){n.wrapMethod(e,Object(r["h"])(u,t))}))})),e.wrapMethod("cloneShallow",Object(r["h"])(c,t)),Object(r["k"])(e.CHANGABLE_METHODS,(function(n){e.wrapMethod(n,Object(r["h"])(s,t))})),Object(r["b"])(n[e.dataType]===e)}function u(t,e){if(d(this)){var n=Object(r["m"])({},o(this).datas);n[this.dataType]=e,m(e,n,t)}else p(e,this.dataType,o(this).mainData,t);return e}function s(t,e){return t.struct&&t.struct.update(),e}function c(t,e){return Object(r["k"])(o(e).datas,(function(n,r){n!==e&&p(n.cloneShallow(),r,e,t)})),e}function f(t){var e=o(this).mainData;return null==t||null==e?e:o(e).datas[t]}function l(){var t=o(this).mainData;return null==t?[{data:t}]:Object(r["H"])(Object(r["F"])(o(t).datas),(function(e){return{type:e,data:o(t).datas[e]}}))}function d(t){return o(t).mainData===t}function m(t,e,n){o(t).datas={},Object(r["k"])(e,(function(e,r){p(e,r,t,n)}))}function p(t,e,n,r){o(n).datas[e]=t,o(t).mainData=n,t.dataType=e,r.struct&&(t[r.structAttr]=r.struct,r.struct[r.datasAttr[e]]=t),t.getLinkedData=f,t.getLinkedDataAll=l}e["a"]=a},"80b9":function(t,e,n){"use strict";n.d(e,"a",(function(){return s})),n.d(e,"d",(function(){return c})),n.d(e,"b",(function(){return f})),n.d(e,"c",(function(){return l})),n.d(e,"e",(function(){return d}));var r=n("6d8b"),i=n("e0d3"),o=n("ec6f"),a=Object(i["o"])(),u={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},s=function(){function t(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return t.prototype.isDimensionOmitted=function(){return this._dimOmitted},t.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=l(this.source)))},t.prototype.getSourceDimensionIndex=function(t){return Object(r["P"])(this._dimNameMap.get(t),-1)},t.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},t.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Object(o["f"])(this.source),n=!d(t),r="",i=[],a=0,s=0;a<t;a++){var c=void 0,f=void 0,l=void 0,m=this.dimensions[s];if(m&&m.storeDimIndex===a)c=e?m.name:null,f=m.type,l=m.ordinalMeta,s++;else{var p=this.getSourceDimension(a);p&&(c=e?p.name:null,f=p.type)}i.push({property:c,type:f,ordinalMeta:l}),!e||null==c||m&&m.isCalculationCoord||(r+=n?c.replace(/\`/g,"`1").replace(/\$/g,"`2"):c),r+="$",r+=u[f]||"f",l&&(r+=l.uid),r+="$"}var h=this.source,v=[h.seriesLayoutBy,h.startIndex,r].join("$$");return{dimensions:i,hash:v}},t.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var r=void 0,i=this.dimensions[n];if(i&&i.storeDimIndex===e)i.isCalculationCoord||(r=i.name),n++;else{var o=this.getSourceDimension(e);o&&(r=o.name)}t.push(r)}return t},t.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},t}();function c(t){return t instanceof s}function f(t){for(var e=Object(r["f"])(),n=0;n<(t||[]).length;n++){var i=t[n],o=Object(r["A"])(i)?i.name:i;null!=o&&null==e.get(o)&&e.set(o,n)}return e}function l(t){var e=a(t);return e.dimNameMap||(e.dimNameMap=f(t.dimensionsDefine))}function d(t){return t>30}},aa74:function(t,e,n){"use strict";var r=n("1be7");n.d(e,"a",(function(){return r["b"]}));var i=n("22b4"),o=n("ee29");Object(i["a"])(o["a"])},b1d4:function(t,e,n){"use strict";n.d(e,"a",(function(){return l}));var r=n("07fd"),i=n("cd70"),o=n("6d8b"),a=n("ec6f"),u=n("d0ce"),s=n("e0d3"),c=n("0f99"),f=n("80b9");function l(t,e){Object(a["e"])(t)||(t=Object(a["c"])(t)),e=e||{};var n=e.coordDimensions||[],l=e.dimensionsDefine||t.dimensionsDefine||[],h=Object(o["f"])(),v=[],b=m(t,n,l,e.dimensionsCount),g=e.canOmitUnusedDimensions&&Object(f["e"])(b),O=l===t.dimensionsDefine,j=O?Object(f["c"])(t):Object(f["b"])(l),D=e.encodeDefine;!D&&e.encodeDefaulter&&(D=e.encodeDefaulter(t,b));for(var y=Object(o["f"])(D),_=new u["a"](b),S=0;S<_.length;S++)_[S]=-1;function C(t){var e=_[t];if(e<0){var n=l[t],r=Object(o["A"])(n)?n:{name:n},a=new i["a"],u=r.name;null!=u&&null!=j.get(u)&&(a.name=a.displayName=u),null!=r.type&&(a.type=r.type),null!=r.displayName&&(a.displayName=r.displayName);var s=v.length;return _[t]=s,a.storeDimIndex=t,v.push(a),a}return v[e]}if(!g)for(S=0;S<b;S++)C(S);y.each((function(t,e){var n=Object(s["r"])(t).slice();if(1===n.length&&!Object(o["C"])(n[0])&&n[0]<0)y.set(e,!1);else{var r=y.set(e,[]);Object(o["k"])(n,(function(t,n){var i=Object(o["C"])(t)?j.get(t):t;null!=i&&i<b&&(r[n]=i,N(C(i),e,n))}))}}));var I=0;function N(t,e,n){null!=r["i"].get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,h.set(e,!0))}Object(o["k"])(n,(function(t){var e,n,r,i;if(Object(o["C"])(t))e=t,i={};else{i=t,e=i.name;var a=i.ordinalMeta;i.ordinalMeta=null,i=Object(o["m"])({},i),i.ordinalMeta=a,n=i.dimsDef,r=i.otherDims,i.name=i.coordDim=i.coordDimIndex=i.dimsDef=i.otherDims=null}var u=y.get(e);if(!1!==u){if(u=Object(s["r"])(u),!u.length)for(var c=0;c<(n&&n.length||1);c++){while(I<b&&null!=C(I).coordDim)I++;I<b&&u.push(I++)}Object(o["k"])(u,(function(t,a){var u=C(t);if(O&&null!=i.type&&(u.type=i.type),N(Object(o["i"])(u,i),e,a),null==u.name&&n){var s=n[a];!Object(o["A"])(s)&&(s={name:s}),u.name=u.displayName=s.name,u.defaultTooltip=s.defaultTooltip}r&&Object(o["i"])(u.otherDims,r)}))}}));var w=e.generateCoord,M=e.generateCoordCount,x=null!=M;M=w?M||1:0;var L=w||"value";function k(t){null==t.name&&(t.name=t.coordDim)}if(g)Object(o["k"])(v,(function(t){k(t)})),v.sort((function(t,e){return t.storeDimIndex-e.storeDimIndex}));else for(var F=0;F<b;F++){var B=C(F),T=B.coordDim;null==T&&(B.coordDim=p(L,h,x),B.coordDimIndex=0,(!w||M<=0)&&(B.isExtraCoord=!0),M--),k(B),null!=B.type||Object(c["b"])(t,F)!==c["a"].Must&&(!B.isExtraCoord||null==B.otherDims.itemName&&null==B.otherDims.seriesName)||(B.type="ordinal")}return d(v),new f["a"]({source:t,dimensions:v,fullDimensionCount:b,dimensionOmitted:g})}function d(t){for(var e=Object(o["f"])(),n=0;n<t.length;n++){var r=t[n],i=r.name,a=e.get(i)||0;a>0&&(r.name=i+(a-1)),a++,e.set(i,a)}}function m(t,e,n,r){var i=Math.max(t.dimensionsDetectedCount||1,e.length,n.length,r||0);return Object(o["k"])(e,(function(t){var e;Object(o["A"])(t)&&(e=t.dimsDef)&&(i=Math.max(i,e.length))})),i}function p(t,e,n){if(n||e.hasKey(t)){var r=0;while(e.hasKey(t+r))r++;t+=r}return e.set(t,!0),t}},b7d9:function(t,e,n){"use strict";n.d(e,"d",(function(){return a})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return l})),n.d(e,"b",(function(){return m}));var r=n("3842"),i=n("6d8b"),o=n("edae");function a(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"!==n||Object(i["z"])(t)||null==t||"-"===t||(t=+Object(r["n"])(t)),null==t||""===t?NaN:Number(t))}var u=Object(i["f"])({number:function(t){return parseFloat(t)},time:function(t){return+Object(r["n"])(t)},trim:function(t){return Object(i["C"])(t)?Object(i["T"])(t):t}});function s(t){return u.get(t)}var c={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return t>e},gte:function(t,e){return t>=e}},f=function(){function t(t,e){if(!Object(i["z"])(e)){var n="";0,Object(o["c"])(n)}this._opFn=c[t],this._rvalFloat=Object(r["m"])(e)}return t.prototype.evaluate=function(t){return Object(i["z"])(t)?this._opFn(t,this._rvalFloat):this._opFn(Object(r["m"])(t),this._rvalFloat)},t}(),l=function(){function t(t,e){var n="desc"===t;this._resultLT=n?1:-1,null==e&&(e=n?"min":"max"),this._incomparable="min"===e?-1/0:1/0}return t.prototype.evaluate=function(t,e){var n=Object(i["z"])(t)?t:Object(r["m"])(t),o=Object(i["z"])(e)?e:Object(r["m"])(e),a=isNaN(n),u=isNaN(o);if(a&&(n=this._incomparable),u&&(o=this._incomparable),a&&u){var s=Object(i["C"])(t),c=Object(i["C"])(e);s&&(n=c?t:0),c&&(o=s?e:0)}return n<o?this._resultLT:n>o?-this._resultLT:0},t}(),d=function(){function t(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=Object(r["m"])(e)}return t.prototype.evaluate=function(t){var e=t===this._rval;if(!e){var n=typeof t;n===this._rvalTypeof||"number"!==n&&"number"!==this._rvalTypeof||(e=Object(r["m"])(t)===this._rvalFloat)}return this._isEQ?e:!e},t}();function m(t,e){return"eq"===t||"ne"===t?new d("eq"===t,e):Object(i["q"])(c,t)?new f(t,e):null}},ee1a:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"c",(function(){return u})),n.d(e,"b",(function(){return s}));var r=n("6d8b"),i=n("80b9");function o(t,e,n){n=n||{};var i,o,u,s=n.byIndex,c=n.stackedCoordDimension;a(e)?i=e:(o=e.schema,i=o.dimensions,u=e.store);var f,l,d,m,p=!(!t||!t.get("stack"));if(Object(r["k"])(i,(function(t,e){Object(r["C"])(t)&&(i[e]=t={name:t}),p&&!t.isExtraCoord&&(s||f||!t.ordinalMeta||(f=t),l||"ordinal"===t.type||"time"===t.type||c&&c!==t.coordDim||(l=t))})),!l||s||f||(s=!0),l){d="__\0ecstackresult_"+t.id,m="__\0ecstackedover_"+t.id,f&&(f.createInvertedIndices=!0);var h=l.coordDim,v=l.type,b=0;Object(r["k"])(i,(function(t){t.coordDim===h&&b++}));var g={name:d,coordDim:h,coordDimIndex:b,type:v,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},O={name:m,coordDim:m,coordDimIndex:b+1,type:v,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1};o?(u&&(g.storeDimIndex=u.ensureCalculationDimension(m,v),O.storeDimIndex=u.ensureCalculationDimension(d,v)),o.appendCalculationDimension(g),o.appendCalculationDimension(O)):(i.push(g),i.push(O))}return{stackedDimension:l&&l.name,stackedByDimension:f&&f.name,isStackedByIndex:s,stackedOverDimension:m,stackResultDimension:d}}function a(t){return!Object(i["d"])(t.schema)}function u(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function s(t,e){return u(t,e)?t.getCalculationInfo("stackResultDimension"):e}},f72b:function(t,e,n){"use strict";n.d(e,"a",(function(){return f})),n.d(e,"b",(function(){return l}));var r=n("6d8b"),i=n("ec6f"),o=n("07fd"),a=n("0f99"),u=n("04f7"),s=n("d0ce"),c=n("2b17"),f=function(){function t(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return t.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},t.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},t.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},t.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},t.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n=this._sourceHost,a=this._getUpstreamSourceManagers(),u=!!a.length;if(d(n)){var s=n,c=void 0,f=void 0,l=void 0;if(u){var m=a[0];m.prepareSource(),l=m.getSource(),c=l.data,f=l.sourceFormat,e=[m._getVersionSign()]}else c=s.get("data",!0),f=Object(r["E"])(c)?o["g"]:o["f"],e=[];var p=this._getSourceMetaRawOption()||{},h=l&&l.metaRawOption||{},v=Object(r["P"])(p.seriesLayoutBy,h.seriesLayoutBy)||null,b=Object(r["P"])(p.sourceHeader,h.sourceHeader),g=Object(r["P"])(p.dimensions,h.dimensions),O=v!==h.seriesLayoutBy||!!b!==!!h.sourceHeader||g;t=O?[Object(i["b"])(c,{seriesLayoutBy:v,sourceHeader:b,dimensions:g},f)]:[]}else{var j=n;if(u){var D=this._applyTransform(a);t=D.sourceList,e=D.upstreamSignList}else{var y=j.get("source",!0);t=[Object(i["b"])(y,this._getSourceMetaRawOption(),null)],e=[]}}this._setLocalSource(t,e)},t.prototype._applyTransform=function(t){var e,n=this._sourceHost,o=n.get("transform",!0),a=n.get("fromTransformResult",!0);if(null!=a){var s="";1!==t.length&&m(s)}var c=[],f=[];return Object(r["k"])(t,(function(t){t.prepareSource();var e=t.getSource(a||0),n="";null==a||e||m(n),c.push(e),f.push(t._getVersionSign())})),o?e=Object(u["a"])(o,c,{datasetIndex:n.componentIndex}):null!=a&&(e=[Object(i["a"])(c[0])]),{sourceList:e,upstreamSignList:f}},t.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},t.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},t.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},t.prototype._innerGetDataStore=function(t,e,n){var r=0,i=this._storeList,o=i[r];o||(o=i[r]={});var a=o[n];if(!a){var u=this._getUpstreamSourceManagers()[0];d(this._sourceHost)&&u?a=u._innerGetDataStore(t,e,n):(a=new s["b"],a.initData(new c["a"](e,t.length),t)),o[n]=a}return a},t.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(d(t)){var e=Object(a["f"])(t);return e?[e.getSourceManager()]:[]}return Object(r["H"])(Object(a["e"])(t),(function(t){return t.getSourceManager()}))},t.prototype._getSourceMetaRawOption=function(){var t,e,n,r=this._sourceHost;if(d(r))t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var i=r;t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)}return{seriesLayoutBy:t,sourceHeader:e,dimensions:n}},t}();function l(t){var e=t.option.transform;e&&Object(r["R"])(t.option.transform)}function d(t){return"series"===t.mainType}function m(t){throw new Error(t)}}}]);