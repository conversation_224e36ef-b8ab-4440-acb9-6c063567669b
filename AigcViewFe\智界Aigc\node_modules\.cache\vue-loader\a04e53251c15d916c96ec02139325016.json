{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue?vue&type=template&id=2cb5f0cc&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue", "mtime": 1753848313296}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"credits-page\"},[_vm._m(0),_c('div',{staticClass:\"credits-content\"},[_c('div',{staticClass:\"balance-overview\"},[_c('div',{staticClass:\"balance-cards\"},[_c('StatsCard',{attrs:{\"value\":_vm.balanceData.currentBalance,\"unit\":\"元\",\"label\":\"当前余额\",\"icon\":\"anticon anticon-wallet\",\"icon-color\":\"#10b981\",\"trend\":_vm.balanceTrend,\"loading\":_vm.loading},on:{\"click\":_vm.handleQuickRecharge}}),_c('StatsCard',{attrs:{\"value\":_vm.balanceData.totalRecharge,\"unit\":\"元\",\"label\":\"累计充值\",\"icon\":\"anticon anticon-plus-circle\",\"icon-color\":\"#7c8aed\",\"loading\":_vm.loading}}),_c('StatsCard',{attrs:{\"value\":_vm.balanceData.totalConsumption,\"unit\":\"元\",\"label\":\"累计消费\",\"icon\":\"anticon anticon-minus-circle\",\"icon-color\":\"#ef4444\",\"loading\":_vm.loading}}),_c('StatsCard',{attrs:{\"value\":_vm.balanceData.monthlyConsumption,\"unit\":\"元\",\"label\":\"本月消费\",\"icon\":\"anticon anticon-bar-chart\",\"icon-color\":\"#f59e0b\",\"trend\":_vm.monthlyTrend,\"loading\":_vm.loading}})],1),_c('div',{staticClass:\"quick-recharge\"},[_c('h3',{staticClass:\"section-title\"},[_vm._v(\"快速充值\")]),_c('div',{staticClass:\"recharge-options\"},_vm._l((_vm.rechargeOptions),function(option){return _c('div',{key:option.amount,staticClass:\"recharge-option\",class:{ selected: _vm.selectedAmount === option.amount },on:{\"click\":function($event){return _vm.selectRechargeAmount(option.amount)}}},[_c('div',{staticClass:\"option-amount\"},[_vm._v(\"¥\"+_vm._s(option.amount))])])}),0),_c('div',{staticClass:\"custom-amount\"},[_c('a-input-number',{staticStyle:{\"flex\":\"1\"},attrs:{\"min\":0.01,\"max\":10000,\"step\":0.01,\"placeholder\":\"自定义金额（最低0.01元）\",\"size\":\"large\"},on:{\"change\":_vm.onCustomAmountChange},model:{value:(_vm.customAmount),callback:function ($$v) {_vm.customAmount=$$v},expression:\"customAmount\"}}),_c('span',{staticClass:\"currency\"},[_vm._v(\"元\")])],1),_c('div',{staticClass:\"recharge-action\"},[_c('div',{staticClass:\"recharge-amount-display\"},[_c('span',{staticClass:\"amount-label\"},[_vm._v(\"充值金额：\")]),_c('span',{staticClass:\"amount-value\",class:{ 'no-amount': _vm.finalRechargeAmount <= 0 }},[_vm._v(\"\\n              \"+_vm._s(_vm.finalRechargeAmount > 0 ? (\"¥\" + _vm.finalRechargeAmount) : '请选择充值金额')+\"\\n            \")])]),_c('a-button',{attrs:{\"type\":\"primary\",\"size\":\"large\",\"loading\":_vm.rechargeLoading,\"disabled\":_vm.finalRechargeAmount <= 0},on:{\"click\":_vm.handleRecharge}},[_vm._v(\"\\n            立即充值\\n          \")])],1)])]),_c('DataTable',{ref:\"transactionTable\",attrs:{\"title\":\"交易记录\",\"data-source\":_vm.transactionList,\"columns\":_vm.transactionColumns,\"loading\":_vm.transactionLoading,\"pagination\":_vm.pagination,\"show-action-column\":false,\"type-options\":_vm.transactionTypeOptions,\"status-options\":[],\"show-search\":true,\"type-filter-placeholder\":\"交易类型\",\"status-filter-placeholder\":\"交易状态\",\"search-placeholder\":\"搜索交易描述\",\"date-filter-placeholder\":['交易时间', '交易时间']},on:{\"filter-change\":_vm.handleFilterChange,\"table-change\":_vm.handleTableChange,\"refresh\":_vm.loadTransactionData},scopedSlots:_vm._u([{key:\"actions\",fn:function(){return [_c('a-button',{staticStyle:{\"margin-right\":\"8px\",\"background\":\"linear-gradient(135deg, #64748b 0%, #475569 100%)\",\"border\":\"none\",\"border-radius\":\"8px\",\"box-shadow\":\"0 4px 12px rgba(100, 116, 139, 0.3)\",\"color\":\"white\"},on:{\"click\":_vm.handleResetFilters}},[_c('a-icon',{staticStyle:{\"margin-right\":\"6px\"},attrs:{\"type\":\"reload\"}}),_vm._v(\"\\n          重置\\n        \")],1),_c('a-button',{staticStyle:{\"background\":\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\"border\":\"none\",\"border-radius\":\"8px\",\"box-shadow\":\"0 4px 12px rgba(102, 126, 234, 0.3)\"},attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleExportTransactions}},[_c('a-icon',{staticStyle:{\"margin-right\":\"6px\"},attrs:{\"type\":\"download\"}}),_vm._v(\"\\n          导出交易记录\\n        \")],1)]},proxy:true}])})],1),_c('a-modal',{attrs:{\"title\":\"确认充值\",\"footer\":null,\"width\":\"500px\"},model:{value:(_vm.showRechargeModal),callback:function ($$v) {_vm.showRechargeModal=$$v},expression:\"showRechargeModal\"}},[_c('div',{staticClass:\"recharge-confirm\"},[_c('div',{staticClass:\"confirm-info\"},[_c('div',{staticClass:\"info-row\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(\"充值金额：\")]),_c('span',{staticClass:\"info-value\"},[_vm._v(\"¥\"+_vm._s(_vm.finalRechargeAmount))])]),_c('div',{staticClass:\"info-row total\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(\"到账金额：\")]),_c('span',{staticClass:\"info-value\"},[_vm._v(\"¥\"+_vm._s(_vm.finalRechargeAmount))])])]),_c('div',{staticClass:\"payment-methods\"},[_c('h4',[_vm._v(\"支付方式\")]),_c('a-radio-group',{attrs:{\"size\":\"large\"},model:{value:(_vm.selectedPaymentMethod),callback:function ($$v) {_vm.selectedPaymentMethod=$$v},expression:\"selectedPaymentMethod\"}},[_c('a-radio-button',{attrs:{\"value\":\"alipay-page\"}},[_c('i',{staticClass:\"anticon anticon-alipay\"}),_vm._v(\"\\n            支付宝网页\\n          \")])],1)],1),_c('div',{staticClass:\"modal-actions\"},[_c('a-button',{on:{\"click\":function($event){_vm.showRechargeModal = false}}},[_vm._v(\"取消\")]),_c('a-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.paymentLoading},on:{\"click\":_vm.handleConfirmRecharge}},[_vm._v(\"\\n          确认支付\\n        \")])],1)])]),_c('a-modal',{attrs:{\"title\":\"交易详情\",\"footer\":null,\"width\":\"600px\"},model:{value:(_vm.showTransactionDetail),callback:function ($$v) {_vm.showTransactionDetail=$$v},expression:\"showTransactionDetail\"}},[(_vm.selectedTransaction)?_c('div',{staticClass:\"transaction-detail\"},[_c('div',{staticClass:\"detail-header\"},[_c('div',{staticClass:\"transaction-type\",class:_vm.getTransactionTypeClass(_vm.selectedTransaction.transactionType)},[_c('i',{class:_vm.getTransactionTypeIcon(_vm.selectedTransaction.transactionType)}),_vm._v(\"\\n          \"+_vm._s(_vm.getTransactionTypeText(_vm.selectedTransaction.transactionType))+\"\\n        \")]),_c('div',{staticClass:\"transaction-amount\",class:_vm.getAmountClass(_vm.selectedTransaction.transactionType)},[_vm._v(\"\\n          \"+_vm._s(_vm.formatAmount(_vm.selectedTransaction.amount, _vm.selectedTransaction.transactionType))+\"\\n        \")])]),_c('div',{staticClass:\"detail-content\"},[_c('div',{staticClass:\"detail-row\"},[_c('span',{staticClass:\"detail-label\"},[_vm._v(\"交易单号：\")]),_c('span',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedTransaction.id))])]),_c('div',{staticClass:\"detail-row\"},[_c('span',{staticClass:\"detail-label\"},[_vm._v(\"交易时间：\")]),_c('span',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.formatDateTime(_vm.selectedTransaction.transactionTime)))])]),_c('div',{staticClass:\"detail-row\"},[_c('span',{staticClass:\"detail-label\"},[_vm._v(\"交易描述：\")]),_c('span',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedTransaction.description))])]),_c('div',{staticClass:\"detail-row\"},[_c('span',{staticClass:\"detail-label\"},[_vm._v(\"交易前余额：\")]),_c('span',{staticClass:\"detail-value\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.selectedTransaction.balanceBefore)))])]),_c('div',{staticClass:\"detail-row\"},[_c('span',{staticClass:\"detail-label\"},[_vm._v(\"交易后余额：\")]),_c('span',{staticClass:\"detail-value\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.selectedTransaction.balanceAfter)))])]),(_vm.selectedTransaction.relatedOrderId)?_c('div',{staticClass:\"detail-row\"},[_c('span',{staticClass:\"detail-label\"},[_vm._v(\"关联订单：\")]),_c('span',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedTransaction.relatedOrderId))])]):_vm._e()])]):_vm._e()])],1)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"page-header\"},[_c('h1',{staticClass:\"page-title\"},[_vm._v(\"账户管理\")]),_c('p',{staticClass:\"page-description\"},[_vm._v(\"管理您的账户余额、查看交易记录和充值\")])])}]\n\nexport { render, staticRenderFns }"]}