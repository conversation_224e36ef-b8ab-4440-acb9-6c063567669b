(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["app~558ff103"],{"1c34":function(t,e,a){"use strict";var i=a("8b1e"),n=a.n(i);n.a},2191:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"setting-drawer"},[a("a-drawer",{staticStyle:{height:"100%",overflow:"auto"},attrs:{width:"300",placement:"right",closable:!1,visible:t.visible},on:{close:t.onClose}},[a("div",{staticClass:"setting-drawer-index-content"},[a("div",{style:{marginBottom:"24px"}},[a("h3",{staticClass:"setting-drawer-index-title"},[t._v("整体风格设置")]),a("div",{staticClass:"setting-drawer-index-blockChecbox"},[a("a-tooltip",[a("template",{slot:"title"},[t._v("\n                暗色菜单风格\n              ")]),a("div",{staticClass:"setting-drawer-index-item",on:{click:function(e){return t.handleMenuTheme("dark")}}},[a("img",{attrs:{src:"https://gw.alipayobjects.com/zos/rmsportal/LCkqqYNmvBEbokSDscrm.svg",alt:"dark"}}),"dark"===t.navTheme?a("div",{staticClass:"setting-drawer-index-selectIcon"},[a("a-icon",{attrs:{type:"check"}})],1):t._e()])],2),a("a-tooltip",[a("template",{slot:"title"},[t._v("\n                亮色菜单风格\n              ")]),a("div",{staticClass:"setting-drawer-index-item",on:{click:function(e){return t.handleMenuTheme("light")}}},[a("img",{attrs:{src:"https://gw.alipayobjects.com/zos/rmsportal/jpRkZQMyYRryryPNtyIC.svg",alt:"light"}}),"dark"!==t.navTheme?a("div",{staticClass:"setting-drawer-index-selectIcon"},[a("a-icon",{attrs:{type:"check"}})],1):t._e()])],2)],1)]),a("div",{style:{marginBottom:"24px"}},[a("h3",{staticClass:"setting-drawer-index-title"},[t._v("主题色")]),a("div",{staticStyle:{height:"20px"}},t._l(t.colorList,(function(e,i){return a("a-tooltip",{key:i,staticClass:"setting-drawer-theme-color-colorBlock"},[a("template",{slot:"title"},[t._v("\n                "+t._s(e.key)+"\n              ")]),a("a-tag",{attrs:{color:e.color},on:{click:function(a){return t.changeColor(e.color)}}},[e.color===t.primaryColor?a("a-icon",{attrs:{type:"check"}}):t._e()],1)],2)})),1)]),a("a-divider"),a("div",{style:{marginBottom:"24px"}},[a("h3",{staticClass:"setting-drawer-index-title"},[t._v("导航模式")]),a("div",{staticClass:"setting-drawer-index-blockChecbox"},[a("a-tooltip",[a("template",{slot:"title"},[t._v("\n                侧边栏导航\n              ")]),a("div",{staticClass:"setting-drawer-index-item",on:{click:function(e){return t.handleLayout("sidemenu")}}},[a("img",{attrs:{src:"https://gw.alipayobjects.com/zos/rmsportal/JopDzEhOqwOjeNTXkoje.svg",alt:"sidemenu"}}),"sidemenu"===t.layoutMode?a("div",{staticClass:"setting-drawer-index-selectIcon"},[a("a-icon",{attrs:{type:"check"}})],1):t._e()])],2),a("a-tooltip",[a("template",{slot:"title"},[t._v("\n                顶部栏导航\n              ")]),a("div",{staticClass:"setting-drawer-index-item",on:{click:function(e){return t.handleLayout("topmenu")}}},[a("img",{attrs:{src:"https://gw.alipayobjects.com/zos/rmsportal/KDNDBbriJhLwuqMoxcAr.svg",alt:"topmenu"}}),"sidemenu"!==t.layoutMode?a("div",{staticClass:"setting-drawer-index-selectIcon"},[a("a-icon",{attrs:{type:"check"}})],1):t._e()])],2)],1),a("div",{style:{marginTop:"24px"}},[a("a-list",{attrs:{split:!1}},[a("a-list-item",[a("a-tooltip",{attrs:{slot:"actions"},slot:"actions"},[a("template",{slot:"title"},[t._v("\n                    该设定仅 [顶部栏导航] 时有效\n                  ")]),a("a-select",{staticStyle:{width:"80px"},attrs:{size:"small",defaultValue:t.contentWidth},on:{change:t.handleContentWidthChange}},[a("a-select-option",{attrs:{value:"Fixed"}},[t._v("固定")]),"sidemenu"!==t.layoutMode?a("a-select-option",{attrs:{value:"Fluid"}},[t._v("流式")]):t._e()],1)],2),a("a-list-item-meta",[a("div",{attrs:{slot:"title"},slot:"title"},[t._v("内容区域宽度")])])],1),a("a-list-item",[a("a-switch",{attrs:{slot:"actions",size:"small",defaultChecked:t.fixedHeader},on:{change:t.handleFixedHeader},slot:"actions"}),a("a-list-item-meta",[a("div",{attrs:{slot:"title"},slot:"title"},[t._v("固定 Header")])])],1),a("a-list-item",[a("a-switch",{attrs:{slot:"actions",size:"small",disabled:!t.fixedHeader,defaultChecked:t.autoHideHeader},on:{change:t.handleFixedHeaderHidden},slot:"actions"}),a("a-list-item-meta",[a("div",{style:{textDecoration:t.fixedHeader?"unset":"line-through"},attrs:{slot:"title"},slot:"title"},[t._v("下滑时隐藏 Header")])])],1),a("a-list-item",[a("a-switch",{attrs:{slot:"actions",size:"small",disabled:"topmenu"===t.layoutMode,checked:t.dataFixSiderbar},on:{change:t.handleFixSiderbar},slot:"actions"}),a("a-list-item-meta",[a("div",{style:{textDecoration:"topmenu"===t.layoutMode?"line-through":"unset"},attrs:{slot:"title"},slot:"title"},[t._v("固定侧边菜单")])])],1)],1)],1)]),a("a-divider"),a("div",{style:{marginBottom:"24px"}},[a("h3",{staticClass:"setting-drawer-index-title"},[t._v("其他设置")]),a("div",[a("a-list",{attrs:{split:!1}},[a("a-list-item",[a("a-switch",{attrs:{slot:"actions",size:"small",defaultChecked:t.colorWeak},on:{change:t.onColorWeak},slot:"actions"}),a("a-list-item-meta",[a("div",{attrs:{slot:"title"},slot:"title"},[t._v("色弱模式")])])],1),a("a-list-item",[a("a-switch",{attrs:{slot:"actions",size:"small",defaultChecked:t.multipage},on:{change:t.onMultipageWeak},slot:"actions"}),a("a-list-item-meta",[a("div",{attrs:{slot:"title"},slot:"title"},[t._v("多页签模式")])])],1)],1)],1)]),a("a-divider"),a("div",{style:{marginBottom:"24px"}},[a("a-alert",{attrs:{type:"warning"}},[a("span",{attrs:{slot:"message"},slot:"message"},[t._v("\n              配置栏只在开发环境用于预览，生产环境不会展现，请手动修改配置文件\n              "),a("a",{attrs:{href:"https://github.com/sendya/ant-design-pro-vue/blob/master/src/defaultSettings.js",target:"_blank"}},[t._v("src/defaultSettings.js")])])])],1)],1),t.visible?a("div",{staticClass:"setting-drawer-index-handle",on:{click:t.toggle}},[a("a-icon",{attrs:{type:"close"}})],1):t._e()])],1)},n=[],s=a("c16f"),l=a("5d9b"),o=a("4fe7"),r=a("5976"),d=a("ac0d"),c=a("ca00"),u={components:{DetailList:s["default"],SettingItem:l["default"]},mixins:[d["a"],d["b"]],data:function(){return{visible:!1,colorList:r["a"],dataFixSiderbar:!1}},mounted:function(){this.primaryColor!==o["a"].primaryColor&&Object(r["c"])(this.primaryColor),this.colorWeak!==o["a"].colorWeak&&Object(r["b"])(this.colorWeak),this.multipage!==o["a"].multipage&&this.$store.dispatch("ToggleMultipage",this.multipage)},methods:{showDrawer:function(){this.visible=!0},onClose:function(){this.visible=!1},toggle:function(){this.visible=!this.visible},onColorWeak:function(t){this.$store.dispatch("ToggleWeak",t),Object(r["b"])(t)},onMultipageWeak:function(t){this.$store.dispatch("ToggleMultipage",t)},handleMenuTheme:function(t){this.$store.dispatch("ToggleTheme",t)},handleLayout:function(t){this.$store.dispatch("ToggleLayoutMode",t),this.handleFixSiderbar(!1),Object(c["p"])()},handleContentWidthChange:function(t){this.$store.dispatch("ToggleContentWidth",t)},changeColor:function(t){this.primaryColor!==t&&(this.$store.dispatch("ToggleColor",t),Object(r["c"])(t))},handleFixedHeader:function(t){this.$store.dispatch("ToggleFixedHeader",t)},handleFixedHeaderHidden:function(t){this.$store.dispatch("ToggleFixedHeaderHidden",t)},handleFixSiderbar:function(t){"topmenu"===this.layoutMode&&(t=!1),this.dataFixSiderbar=t,this.$store.dispatch("ToggleFixSiderbar",t)}}},h=u,p=(a("50bb"),a("2877")),m=Object(p["a"])(h,i,n,!1,null,"14515006",null);e["default"]=m.exports},"439a":function(t,e,a){},"50bb":function(t,e,a){"use strict";var i=a("ab6f"),n=a.n(i);n.a},"5d9b":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"setting-drawer-index-item"},[a("h3",{staticClass:"setting-drawer-index-title"},[t._v(t._s(t.title))]),t._t("default"),t.divider?a("a-divider"):t._e()],2)},n=[],s={name:"SettingItem",props:{title:{type:String,default:""},divider:{type:Boolean,default:!1}}},l=s,o=(a("b078"),a("2877")),r=Object(o["a"])(l,i,n,!1,null,"be52e722",null);e["default"]=r.exports},"603b":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-header-index-wide"},[a("a-breadcrumb",{staticClass:"breadcrumb"},t._l(t.breadList,(function(e,i){return a("a-breadcrumb-item",{key:i},[e.name!=t.name?a("router-link",{attrs:{to:{path:e.path}}},[t._v("\n          "+t._s(e.meta.title)+"\n        ")]):a("span",[t._v(t._s(e.meta.title))])],1)})),1),a("div",{staticClass:"detail"},[t.$route.meta.hiddenHeaderContent?t._e():a("div",{staticClass:"main"},[a("div",{staticClass:"row"},[t.logo?a("img",{staticClass:"logo",attrs:{src:t.logo}}):t._e(),t.title?a("h1",{staticClass:"title"},[t._v(t._s(t.title))]):t._e(),a("div",{staticClass:"action"},[t._t("action")],2)]),a("div",{staticClass:"row"},[t.avatar?a("div",{staticClass:"avatar"},[a("a-avatar",{attrs:{src:t.avatar}})],1):t._e(),this.$slots.content?a("div",{staticClass:"headerContent"},[t._t("content")],2):t._e(),this.$slots.extra?a("div",{staticClass:"extra"},[t._t("extra")],2):t._e()]),a("div",[t._t("pageMenu")],2)])])],1)])},n=[],s=a("e97b"),l={name:"PageHeader",components:{"s-breadcrumb":s["default"]},props:{title:{type:String,default:"",required:!1},breadcrumb:{type:Array,default:null,required:!1},logo:{type:String,default:"",required:!1},avatar:{type:String,default:"",required:!1}},data:function(){return{name:"",breadList:[]}},created:function(){this.getBreadcrumb()},methods:{getBreadcrumb:function(){var t=this;this.breadList=[],this.name=this.$route.name,this.$route.matched.forEach((function(e){t.breadList.push(e)}))}},watch:{$route:function(){this.getBreadcrumb()}}},o=l,r=(a("b5e4"),a("2877")),d=Object(r["a"])(o,i,n,!1,null,"c3445be0",null);e["default"]=d.exports},"630d":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.headerBarFixed?t._e():a("a-layout-header",{class:[t.fixedHeader&&"ant-header-fixedHeader",t.sidebarOpened?"ant-header-side-opened":"ant-header-side-closed"],style:{padding:"0"}},["sidemenu"===t.mode?a("div",{staticClass:"header",class:t.theme},["mobile"===t.device?a("a-icon",{staticClass:"trigger",attrs:{type:t.collapsed?"menu-fold":"menu-unfold"},on:{click:t.toggle}}):a("a-icon",{staticClass:"trigger",attrs:{type:t.collapsed?"menu-unfold":"menu-fold"},on:{click:t.toggle}}),"desktop"===t.device?a("span",[t._v("欢迎进入 智界AIGC")]):a("span",[t._v("智界AIGC")]),a("user-menu",{attrs:{theme:t.theme}})],1):a("div",{class:["top-nav-header-index",t.theme]},[a("div",{staticClass:"header-index-wide"},[a("div",{staticClass:"header-index-left",style:t.topMenuStyle.headerIndexLeft},[a("logo",{staticClass:"top-nav-header",style:t.topMenuStyle.topNavHeader,attrs:{"show-title":"mobile"!==t.device}}),"mobile"!==t.device?a("div",{style:t.topMenuStyle.topSmenuStyle},[a("s-menu",{attrs:{mode:"horizontal",menu:t.menus,theme:t.theme},on:{updateMenuTitle:t.handleUpdateMenuTitle}})],1):a("a-icon",{staticClass:"trigger",attrs:{type:t.collapsed?"menu-fold":"menu-unfold"},on:{click:t.toggle}})],1),a("user-menu",{staticClass:"header-index-right",style:t.topMenuStyle.headerIndexRight,attrs:{theme:t.theme}})],1)])])},n=[],s=a("8d4d"),l=a("955f"),o=a("a250"),r=a("ac0d"),d={name:"GlobalHeader",components:{UserMenu:s["default"],SMenu:l["a"],Logo:o["default"]},mixins:[r["a"]],props:{mode:{type:String,default:"sidemenu"},menus:{type:Array,required:!0},theme:{type:String,required:!1,default:"dark"},collapsed:{type:Boolean,required:!1,default:!1},device:{type:String,required:!1,default:"desktop"}},data:function(){return{headerBarFixed:!1,topMenuStyle:{headerIndexLeft:{},topNavHeader:{},headerIndexRight:{},topSmenuStyle:{}},chatStatus:""}},watch:{device:function(){"topmenu"===this.mode&&this.buildTopMenuStyle()},mode:function(t){"topmenu"===t&&this.buildTopMenuStyle()}},mounted:function(){window.addEventListener("scroll",this.handleScroll),"topmenu"===this.mode&&this.buildTopMenuStyle()},methods:{handleScroll:function(){if(this.autoHideHeader){var t=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;this.headerBarFixed=t>100}else this.headerBarFixed=!1},toggle:function(){this.$emit("toggle")},buildTopMenuStyle:function(){if("topmenu"===this.mode)if("mobile"===this.device)this.topMenuStyle.topNavHeader={},this.topMenuStyle.topSmenuStyle={},this.topMenuStyle.headerIndexRight={},this.topMenuStyle.headerIndexLeft={};else{var t="360px";this.topMenuStyle.topNavHeader={"min-width":"165px"},this.topMenuStyle.topSmenuStyle={width:"calc(100% - 165px)"},this.topMenuStyle.headerIndexRight={"min-width":t},this.topMenuStyle.headerIndexLeft={width:"calc(100% - ".concat(t,")")}}},handleUpdateMenuTitle:function(t){this.$emit("updateMenuTitle",t)}}},c=d,u=(a("7da5"),a("2877")),h=Object(u["a"])(c,i,n,!1,null,"0a12432e",null);e["default"]=h.exports},"65ee":function(t,e,a){},"7da5":function(t,e,a){"use strict";var i=a("439a"),n=a.n(i);n.a},8415:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("a-layout",{staticClass:"layout",class:[e.device]},["sidemenu"===e.layoutMode?["mobile"===e.device?i("a-drawer",{attrs:{wrapClassName:"drawer-sider "+e.navTheme,placement:"left",closable:!1,visible:e.collapsed,width:"200px"},on:{close:function(){return t.collapsed=!1}}},["mobile"===e.device?i("side-menu",{attrs:{mode:"inline",menus:e.menus,theme:e.navTheme,collapsed:!1,collapsible:!0},on:{menuSelect:e.menuSelect,updateMenuTitle:e.handleUpdateMenuTitle}}):e._e()],1):e._e(),i("side-menu",{directives:[{name:"show",rawName:"v-show",value:"desktop"===e.device,expression:"device === 'desktop'"}],attrs:{mode:"inline",menus:e.menus,theme:e.navTheme,collapsed:e.collapsed,collapsible:!0},on:{menuSelect:e.myMenuSelect,updateMenuTitle:e.handleUpdateMenuTitle}})]:["mobile"===e.device?i("a-drawer",{attrs:{wrapClassName:"drawer-sider "+e.navTheme,placement:"left",closable:!1,visible:e.collapsed,width:"200px"},on:{close:function(){return t.collapsed=!1}}},[i("side-menu",{attrs:{mode:"inline",menus:e.menus,theme:e.navTheme,collapsed:!1,collapsible:!0},on:{menuSelect:e.menuSelect,updateMenuTitle:e.handleUpdateMenuTitle}})],1):e._e()],i("a-layout",{class:[e.layoutMode,"content-width-"+e.contentWidth],style:{paddingLeft:e.fixSiderbar&&e.isDesktop()?(e.sidebarOpened?200:80)+"px":"0"}},[i("global-header",{attrs:{mode:e.layoutMode,menus:e.menus,theme:e.navTheme,collapsed:e.collapsed,device:e.device},on:{toggle:e.toggle,updateMenuTitle:e.handleUpdateMenuTitle}}),i("a-layout-content",{style:{height:"100%",paddingTop:e.fixedHeader?"59px":"0"}},[e._t("default")],2),i("a-layout-footer",{staticStyle:{padding:"0px"}},[i("global-footer")],1)],1)],2)},n=[],s=a("e5f9"),l=a("630d"),o=a("911c"),r=a("ca00"),d=a("2f62"),c=a("ac0d");function u(t,e){var a;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(a=h(t))||e&&t&&"number"===typeof t.length){a&&(t=a);var i=0,n=function(){};return{s:n,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,l=!0,o=!1;return{s:function(){a=t[Symbol.iterator]()},n:function(){var t=a.next();return l=t.done,t},e:function(t){o=!0,s=t},f:function(){try{l||null==a.return||a.return()}finally{if(o)throw s}}}}function h(t,e){if(t){if("string"===typeof t)return p(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=new Array(e);a<e;a++)i[a]=t[a];return i}function m(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function f(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?m(Object(a),!0).forEach((function(e){v(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):m(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function v(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var g={name:"GlobalLayout",components:{SideMenu:s["default"],GlobalHeader:l["default"],GlobalFooter:o["default"]},mixins:[c["a"],c["b"]],data:function(){return{collapsed:!1,activeMenu:{},menus:[]}},computed:f({},Object(d["d"])({mainRouters:function(t){return t.permission.addRouters},permissionMenuList:function(t){return t.user.permissionList}})),watch:{sidebarOpened:function(t){this.collapsed=!t}},created:function(){this.menus=this.permissionMenuList,this.collapsed=!this.sidebarOpened},methods:f(f({},Object(d["b"])(["setSidebar"])),{},{toggle:function(){this.collapsed=!this.collapsed,this.setSidebar(!this.collapsed),Object(r["p"])()},menuSelect:function(){this.isDesktop()||(this.collapsed=!1)},myMenuSelect:function(t){this.findMenuBykey(this.menus,t.key),this.$emit("dynamicRouterShow",t.key,this.activeMenu.meta.title)},findMenuBykey:function(t,e){var a,i=u(t);try{for(i.s();!(a=i.n()).done;){var n=a.value;n.path==e?this.activeMenu=f({},n):n.children&&n.children.length>0&&this.findMenuBykey(n.children,e)}}catch(s){i.e(s)}finally{i.f()}},handleUpdateMenuTitle:function(t){this.findMenuBykey(this.menus,t.path),this.activeMenu.meta.title=t.meta.title,this.$emit("dynamicRouterShow",t.path,this.activeMenu.meta.title)}})},y=g,b=(a("f491"),a("2877")),x=Object(b["a"])(y,i,n,!1,null,null,null);e["default"]=x.exports},"8b1e":function(t,e,a){},"911c":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"footer"},[a("div",{staticClass:"copyright"},[t._v("\n    Copyright\n    "),a("a-icon",{attrs:{type:"copyright"}}),t._v("\n    2025 "),a("span",[t._v("JeecgBoot")])],1)])},n=[],s={name:"LayoutFooter"},l=s,o=(a("a2270"),a("2877")),r=Object(o["a"])(l,i,n,!1,null,"15f82391",null);e["default"]=r.exports},a2270:function(t,e,a){"use strict";var i=a("f94c"),n=a.n(i);n.a},ab6f:function(t,e,a){},b078:function(t,e,a){"use strict";var i=a("bd7f"),n=a.n(i);n.a},b445:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{style:t.$route.meta.pageHeader?null:"margin: -10px -24px 0;"},[t.$route.meta.pageHeader?t._e():a("page-header",{attrs:{title:t.title,logo:t.logo,avatar:t.avatar}},[t._t("action",null,{slot:"action"}),t._t("headerContent",null,{slot:"content"}),!this.$slots.headerContent&&t.desc?a("div",{attrs:{slot:"content"},slot:"content"},[a("p",{staticStyle:{"font-size":"14px",color:"rgba(0,0,0,.65)"}},[t._v(t._s(t.desc))]),a("div",{staticClass:"link"},[t._l(t.linkList,(function(e,i){return[a("a",{key:i,attrs:{href:e.href}},[a("a-icon",{attrs:{type:e.icon}}),a("span",[t._v(t._s(e.title))])],1)]}))],2)]):t._e(),t._t("extra",null,{slot:"extra"}),a("div",{attrs:{slot:"pageMenu"},slot:"pageMenu"},[t.search?a("div",{staticClass:"page-menu-search"},[a("a-input-search",{staticStyle:{width:"80%","max-width":"522px"},attrs:{placeholder:"请输入...",size:"large",enterButton:"搜索"}})],1):t._e(),t.tabs&&t.tabs.items?a("div",{staticClass:"page-menu-tabs"},[a("a-tabs",{attrs:{tabBarStyle:{margin:0},activeKey:t.tabs.active()},on:{change:t.tabs.callback}},t._l(t.tabs.items,(function(t){return a("a-tab-pane",{key:t.key,attrs:{tab:t.title}})})),1)],1):t._e()])],2),a("div",{staticClass:"content"},[a("div",{class:["page-header-index-wide"]},[t._t("default")],2)])],1)},n=[],s=a("603b"),l={name:"LayoutContent",components:{PageHeader:s["default"]},props:{desc:{type:String,default:null},logo:{type:String,default:null},title:{type:String,default:null},avatar:{type:String,default:null},linkList:{type:Array,default:null},extraImage:{type:String,default:null},search:{type:Boolean,default:!1},tabs:{type:Object,default:function(){}}},methods:{}},o=l,r=(a("1c34"),a("2877")),d=Object(r["a"])(o,i,n,!1,null,"15dc04d7",null);e["default"]=d.exports},b5e4:function(t,e,a){"use strict";var i=a("f7d8"),n=a.n(i);n.a},bd7f:function(t,e,a){},f491:function(t,e,a){"use strict";var i=a("65ee"),n=a.n(i);n.a},f7d8:function(t,e,a){},f94c:function(t,e,a){}}]);