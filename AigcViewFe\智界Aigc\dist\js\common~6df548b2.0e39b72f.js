(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~6df548b2"],{"018a":function(t,e,a){},1392:function(t,e,a){"use strict";var n=a("e7e8"),i=a.n(n);i.a},1594:function(t,e,a){"use strict";var n=a("7e59"),i=a.n(n);i.a},"19c0f":function(t,e,a){},"412f":function(t,e,a){"use strict";var n=a("a854"),i=a.n(n);i.a},4318:function(t,e,a){"use strict";var n=a("018a"),i=a.n(n);i.a},"5eea":function(t,e,a){},6187:function(t,e,a){"use strict";var n=a("61cc"),i=a.n(n);i.a},"61cc":function(t,e,a){},"62cc":function(t,e,a){"use strict";var n=a("19c0f"),i=a.n(n);i.a},"67d0":function(t,e,a){"use strict";var n=a("5eea"),i=a.n(n);i.a},6993:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"usage-page"},[t._m(0),a("div",{staticClass:"api-stats"},[a("div",{staticClass:"stats-cards"},[a("StatsCard",{attrs:{value:t.apiStats.todayCalls,label:"今日调用",icon:"anticon anticon-api","icon-color":"#7c8aed",loading:t.loading}}),a("StatsCard",{attrs:{value:t.apiStats.monthCalls,label:"本月调用",icon:"anticon anticon-bar-chart","icon-color":"#10b981",loading:t.loading}}),a("StatsCard",{attrs:{value:t.apiStats.totalCalls,label:"累计调用",icon:"anticon anticon-line-chart","icon-color":"#f59e0b",loading:t.loading}}),a("StatsCard",{attrs:{value:t.apiStats.successRate,unit:"%",label:"成功率",icon:"anticon anticon-check-circle","icon-color":"#10b981",loading:t.loading}})],1)]),a("DataTable",{ref:"dataTable",attrs:{title:"插件使用记录","data-source":t.usageList,columns:t.usageColumns,loading:t.tableLoading,pagination:t.pagination,"type-options":t.categoryOptions,"status-options":[],"show-action-column":!1,"show-search":!1,"show-order-search":!1,"show-product-search":!0,"show-date-filter":!0,"type-filter-placeholder":"插件分类","type-filter-default-text":"全部插件分类","status-filter-placeholder":"","status-filter-default-text":"","product-search-placeholder":"搜索插件名称","date-filter-placeholder":["使用开始时间","使用结束时间"]},on:{"filter-change":t.handleFilterChange,"table-change":t.handleTableChange},scopedSlots:t._u([{key:"actions",fn:function(){return[a("a-button",{staticStyle:{"margin-right":"8px",background:"linear-gradient(135deg, #64748b 0%, #475569 100%)",border:"none","border-radius":"8px","box-shadow":"0 4px 12px rgba(100, 116, 139, 0.3)",color:"white"},on:{click:t.handleResetFilters}},[a("a-icon",{staticStyle:{"margin-right":"6px"},attrs:{type:"reload"}}),t._v("\n        重置\n      ")],1),a("a-button",{staticStyle:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none","border-radius":"8px","box-shadow":"0 4px 12px rgba(102, 126, 234, 0.3)"},attrs:{type:"primary"},on:{click:t.handleExportUsage}},[a("a-icon",{staticStyle:{"margin-right":"6px"},attrs:{type:"download"}}),t._v("\n        导出使用记录\n      ")],1)]},proxy:!0}])})],1)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-header"},[a("h1",{staticClass:"page-title"},[t._v("使用记录")]),a("p",{staticClass:"page-description"},[t._v("查看您的插件使用历史和API调用统计")])])}],r=a("a34a"),s=a.n(r),o=a("1189"),c=a("4dbe"),l=a("77ea"),d=a("89f2");function u(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function p(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?u(Object(a),!0).forEach((function(e){h(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function h(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function f(t,e,a,n,i,r,s){try{var o=t[r](s),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(n,i)}function m(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function s(t){f(r,n,i,s,o,"next",t)}function o(t){f(r,n,i,s,o,"throw",t)}s(void 0)}))}}var g={name:"UserCenterUsage",components:{StatsCard:o["default"],DataTable:c["default"]},data:function(){var t=this;return{loading:!0,tableLoading:!1,apiStats:{todayCalls:0,monthCalls:0,totalCalls:0,successRate:0},filters:{type:"",status:"",dateRange:[],productKeyword:""},usageList:[],pagination:{current:1,pageSize:10,total:0},usageColumns:[{title:"插件名称",dataIndex:"plugin_name",key:"plugin_name",width:200,align:"center",customRender:function(t){return t||"未知插件"}},{title:"插件分类",dataIndex:"category",key:"category",width:120,align:"center",customRender:function(e){return t.getCategoryText(e)}},{title:"调用次数",dataIndex:"call_count",key:"call_count",width:120,align:"center",customRender:function(t){return"".concat(t||0,"次")}},{title:"消费金额",dataIndex:"total_cost",key:"total_cost",width:120,align:"center",customRender:function(t){return"¥".concat(parseFloat(t||0).toFixed(2))}},{title:"最后使用时间",dataIndex:"last_used_time",key:"last_used_time",width:150,align:"center",customRender:function(e){return t.formatDateTime(e)}}],categoryOptions:[]}},mounted:function(){var t=m(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.initDictConfig();case 2:return t.next=4,this.loadData();case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),computed:{paginationConfig:function(){return p(p({},this.pagination),{},{showSizeChanger:!0,showQuickJumper:!0,showTotal:function(t,e){return"第 ".concat(e[0],"-").concat(e[1]," 条，共 ").concat(t," 条")},pageSizeOptions:["10","20","50","100"]})}},methods:{initDictConfig:function(){var t=m(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(d["d"])("plugin_category");case 3:e=t.sent,e.success&&(this.categoryOptions=e.result.map((function(t){return{value:t.value,label:t.text}}))),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadData:function(){var t=m(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,Promise.all([this.loadApiStats(),this.loadUsageList()]);case 4:t.next=10;break;case 6:t.prev=6,t.t0=t["catch"](0),this.$message.error("加载数据失败，请刷新重试");case 10:return t.prev=10,this.loading=!1,t.finish(10);case 13:case"end":return t.stop()}}),t,this,[[0,6,10,13]])})));function e(){return t.apply(this,arguments)}return e}(),loadApiStats:function(){var t=m(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(l["h"])({timeRange:"all"});case 3:e=t.sent,e.success&&(this.apiStats=e.result||{}),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadUsageList:function(){var t=m(s.a.mark((function t(){var e,a,n,i;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.tableLoading=!0,e={pageNo:this.pagination.current,pageSize:this.pagination.pageSize,category:this.filters.type,pluginKeyword:this.filters.productKeyword},this.filters.dateRange&&2===this.filters.dateRange.length&&(e.startDate=this.filters.dateRange[0].format("YYYY-MM-DD"),e.endDate=this.filters.dateRange[1].format("YYYY-MM-DD")),t.next=6,Object(l["y"])(e);case 6:a=t.sent,a.success&&(n=a.result&&a.result.records||[],i=JSON.parse(JSON.stringify(n)),this.usageList=i,this.pagination.total=a.result&&a.result.total||0),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](0),this.usageList=[];case 15:return t.prev=15,this.tableLoading=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[0,11,15,18]])})));function e(){return t.apply(this,arguments)}return e}(),handleFilterChange:function(t){this.filters=p(p({},this.filters),t),this.pagination.current=1,this.loadUsageList()},handleResetFilters:function(){this.$refs.dataTable&&this.$refs.dataTable.resetFilters&&this.$refs.dataTable.resetFilters()},handleTableChange:function(t){var e=t.pagination;this.pagination=p(p({},this.pagination),e),this.loadUsageList()},handleExportUsage:function(){var t=m(s.a.mark((function t(){var e,a,n,i,r,o,c;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在导出使用记录...",0),e={category:this.filters.type,pluginKeyword:this.filters.productKeyword},this.filters.dateRange&&2===this.filters.dateRange.length&&(e.startDate=this.filters.dateRange[0].format("YYYY-MM-DD"),e.endDate=this.filters.dateRange[1].format("YYYY-MM-DD")),t.next=7,Object(l["f"])(e);case 7:a=t.sent,this.$message.destroy(),n=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),i=window.URL.createObjectURL(n),r=document.createElement("a"),r.href=i,o=new Date,c=o.getFullYear()+String(o.getMonth()+1).padStart(2,"0")+String(o.getDate()).padStart(2,"0")+"_"+String(o.getHours()).padStart(2,"0")+String(o.getMinutes()).padStart(2,"0"),r.download="使用记录_".concat(c,".xlsx"),document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(i),this.$message.success("使用记录导出成功！"),t.next=28;break;case 23:t.prev=23,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("导出失败，请重试");case 28:case"end":return t.stop()}}),t,this,[[0,23]])})));function e(){return t.apply(this,arguments)}return e}(),getCategoryText:function(t){return Object(d["a"])(this.categoryOptions,t)||"未知分类"},formatDateTime:function(t){if(!t)return"-";try{var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}},formatNumber:function(t){return t?parseFloat(t).toFixed(2):"0.00"}}},v=g,b=(a("67d0"),a("2877")),x=Object(b["a"])(v,n,i,!1,null,"12b1bf28",null);e["default"]=x.exports},"7aa6":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"overview-page"},[a("div",{staticClass:"welcome-section"},[a("div",{staticClass:"welcome-content"},[a("h1",{staticClass:"welcome-title"},[t._v("\n        欢迎回来，"+t._s(t.userInfo&&t.userInfo.nickname||"智界用户")+"！\n      ")]),a("p",{staticClass:"welcome-subtitle"},[t._v("\n        "+t._s(t.getWelcomeMessage())+"\n      ")])]),a("div",{staticClass:"welcome-actions"},[a("button",{staticClass:"action-btn primary",on:{click:t.handleQuickRecharge}},[a("i",{staticClass:"anticon anticon-plus-circle"}),t._v("\n        快速充值\n      ")]),t.shouldShowCompleteProfile?a("button",{staticClass:"action-btn secondary",on:{click:t.handleViewProfile}},[a("i",{staticClass:"anticon anticon-user"}),t._v("\n        完善资料\n      ")]):t._e()])]),a("div",{staticClass:"user-info-section"},[a("UserInfoCard",{attrs:{"user-info":t.userInfo,loading:t.loading},on:{edit:t.handleEditProfile,"api-key-regenerated":t.handleApiKeyRegenerated}})],1),a("div",{staticClass:"stats-section"},[a("div",{staticClass:"stats-grid"},[a("div",{staticClass:"balance-card-enhanced"},[a("StatsCard",{attrs:{value:t.userInfo&&t.userInfo.accountBalance,unit:"元",label:"账户余额",icon:"anticon anticon-wallet","icon-color":"#10b981",trend:t.balanceTrend,loading:t.loading}}),a("div",{staticClass:"balance-actions"},[a("button",{staticClass:"recharge-btn-enhanced",on:{click:t.handleRecharge}},[t._m(0),a("div",{staticClass:"btn-glow"})])])],1),a("StatsCard",{attrs:{value:t.overviewData&&t.overviewData.totalConsumption,unit:"元",label:"累计消费",icon:"anticon anticon-shopping","icon-color":"#ef4444",trend:t.consumptionTrend,loading:t.loading}}),a("StatsCard",{attrs:{value:t.overviewData&&t.overviewData.apiCallsToday,label:"今日调用",icon:"anticon anticon-api","icon-color":"#7c8aed",trend:t.apiCallsTrend,loading:t.loading},on:{click:t.handleViewUsage}})],1)]),a("div",{staticClass:"main-content"},[a("div",{staticClass:"membership-status-card-full"},[a("div",{staticClass:"membership-header"},[t._m(1),a("div",{staticClass:"membership-info"},[a("h4",[t._v("会员状态")]),a("span",{staticClass:"membership-level",class:t.memberLevelClass},[t._v(t._s(t.memberLevelText))])])]),a("div",{staticClass:"membership-details"},[a("div",{staticClass:"membership-days"},[a("div",{staticClass:"days-number"},[t._v(t._s(t.overviewData&&t.overviewData.memberDaysLeft||0))]),a("div",{staticClass:"days-label"},[t._v("剩余天数")])]),a("div",{staticClass:"membership-progress"},[a("div",{staticClass:"progress-bar"},[a("div",{staticClass:"progress-fill",style:{width:t.membershipProgressPercent+"%"}})]),a("div",{staticClass:"progress-text"},[t._v("\n            "+t._s(t.membershipProgressText)+"\n          ")])])]),a("button",{staticClass:"membership-action",on:{click:t.handleViewMembership}},[t._v("\n        升级/续费会员\n      ")])]),a("div",{staticClass:"bottom-row"},[a("div",{staticClass:"orders-card"},[t._m(2),a("div",{staticClass:"card-stats"},[a("div",{staticClass:"stat-item"},[a("span",{staticClass:"stat-value"},[t._v(t._s(t.recentOrders.length||0))]),a("span",{staticClass:"stat-label"},[t._v("最近订单")])]),a("div",{staticClass:"stat-item"},[a("span",{staticClass:"stat-value"},[t._v(t._s(t.getPendingOrdersCount()))]),a("span",{staticClass:"stat-label"},[t._v("待处理")])])]),a("button",{staticClass:"card-action",on:{click:t.handleViewOrders}},[t._v("\n          查看全部订单\n        ")])]),a("div",{staticClass:"usage-records-card"},[a("div",{staticClass:"card-header"},[a("h3",{staticClass:"card-title"},[t._v("使用记录")]),a("button",{staticClass:"view-all-btn",on:{click:t.handleViewAllUsage}},[t._v("\n            查看全部\n          ")])]),a("div",{staticClass:"usage-records-container"},[!t.loading&&t.recentUsage&&t.recentUsage.length?a("div",{staticClass:"usage-records-list"},t._l(t.recentUsage,(function(e){return a("div",{key:e.id,staticClass:"usage-record-item"},[a("div",{staticClass:"record-icon",class:t.getUsageIconClass(e.plugin_name)},[a("i",{class:t.getUsageIcon(e.plugin_name)})]),a("div",{staticClass:"record-content"},[a("div",{staticClass:"record-title"},[t._v(t._s(e.plugin_name||"未知插件"))]),a("div",{staticClass:"record-description"},[t._v(t._s(e.call_count||0)+"次调用 · 消费 "+t._s(e.total_cost||0)+"元")]),a("div",{staticClass:"record-time"},[t._v(t._s(t.formatRelativeTime(e.last_used_time)))])])])})),0):t.loading?a("div",{staticClass:"usage-records-loading"},t._l(8,(function(e){return a("div",{key:e,staticClass:"record-skeleton"},[a("div",{staticClass:"skeleton-icon"}),t._m(3,!0)])})),0):a("div",{staticClass:"usage-records-empty"},[a("i",{staticClass:"anticon anticon-file-text"}),a("p",[t._v(t._s(null===t.recentUsage?"数据加载失败":"暂无使用记录"))])])])])])])])},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"btn-content"},[a("i",{staticClass:"anticon anticon-plus-circle btn-icon"}),a("span",{staticClass:"btn-text"},[t._v("立即充值")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"membership-icon"},[a("i",{staticClass:"anticon anticon-crown"})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"card-header"},[a("div",{staticClass:"card-icon"},[a("i",{staticClass:"anticon anticon-shopping-cart"})]),a("div",{staticClass:"card-info"},[a("h4",[t._v("订单记录")]),a("p",[t._v("管理您的购买记录和订单状态")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"skeleton-content"},[a("div",{staticClass:"skeleton-line"}),a("div",{staticClass:"skeleton-line short"})])}],r=a("a34a"),s=a.n(r),o=a("3c24"),c=a("1189"),l=a("77ea"),d=a("36d5f");function u(t,e){return g(t)||m(t,e)||h(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){if(t){if("string"===typeof t)return f(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=new Array(e);a<e;a++)n[a]=t[a];return n}function m(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],n=!0,i=!1,r=void 0;try{for(var s,o=t[Symbol.iterator]();!(n=(s=o.next()).done);n=!0)if(a.push(s.value),e&&a.length===e)break}catch(c){i=!0,r=c}finally{try{n||null==o["return"]||o["return"]()}finally{if(i)throw r}}return a}}function g(t){if(Array.isArray(t))return t}function v(t,e,a,n,i,r,s){try{var o=t[r](s),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(n,i)}function b(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function s(t){v(r,n,i,s,o,"next",t)}function o(t){v(r,n,i,s,o,"throw",t)}s(void 0)}))}}var x={name:"UserCenterOverview",mixins:[d["a"]],components:{UserInfoCard:o["default"],StatsCard:c["default"]},props:{userInfo:{type:Object,default:function(){return{}}}},data:function(){return{loading:!0,overviewData:null,recentActivities:null,recentOrders:[],recentUsage:[]}},computed:{balanceTrend:function(){return null},consumptionTrend:function(){return null},apiCallsTrend:function(){return null},memberLevelClass:function(){var t=this.getUserRole();return t&&"string"===typeof t?"level-".concat(t.toLowerCase()):"level-user"},memberLevelText:function(){var t=this.getUserRole();if(!t)return"普通用户";var e={user:"普通用户",vip:"VIP会员",svip:"SVIP会员",admin:"管理员",USER:"普通用户",VIP:"VIP会员",SVIP:"SVIP会员",ADMIN:"管理员","普通用户":"普通用户","VIP会员":"VIP会员","SVIP会员":"SVIP会员","管理员":"管理员"};return e[t]||"普通用户"},membershipProgressPercent:function(){if(!this.overviewData||!this.overviewData.memberDaysLeft)return 0;var t=this.overviewData.memberDaysLeft,e=this.overviewData.memberTotalDays||365;return this.overviewData.memberTotalDays||(e=t<=31?31:t<=90?90:t<=365?365:t+30),Math.max(0,Math.min(100,t/e*100))},membershipProgressText:function(){if(!this.overviewData||!this.overviewData.memberDaysLeft)return"未开通会员";var t=this.overviewData.memberDaysLeft;return t>30?"还有 ".concat(Math.floor(t/30)," 个月"):"还有 ".concat(t," 天")},shouldShowCompleteProfile:function(){if(!this.userInfo)return!0;var t=this.userInfo.email&&""!==this.userInfo.email.trim(),e=this.userInfo.phone&&""!==this.userInfo.phone.trim();return!(t&&e)}},mounted:function(){var t=b(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.loadData();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{loadData:function(){var t=b(s.a.mark((function t(){var e,a,n,i,r,o,c;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,Promise.all([Object(l["r"])(),Object(l["m"])({limit:10}),Object(l["u"])({pageNo:1,pageSize:5}),Object(l["y"])({pageNo:1,pageSize:10})]);case 4:if(e=t.sent,a=u(e,4),n=a[0],i=a[1],r=a[2],o=a[3],n.success){t.next=12;break}throw new Error("获取概览数据失败: ".concat(n.message||"未知错误"));case 12:if(this.overviewData=n.result||{},i.success){t.next=17;break}throw new Error("获取活动记录失败: ".concat(i.message||"未知错误"));case 17:this.recentActivities=i.result||[],r.success?this.recentOrders=r.result&&r.result.records||[]:this.recentOrders=[],o.success?this.recentUsage=o.result&&o.result.records||[]:this.recentUsage=[],t.next=36;break;case 22:if(t.prev=22,t.t0=t["catch"](0),!this.handleError){t.next=31;break}if(c=this.handleError(t.t0,"加载数据失败，请刷新重试"),!c){t.next=29;break}return t.abrupt("return");case 29:t.next=32;break;case 31:this.$message.error(t.t0.message||"加载数据失败，请刷新重试");case 32:this.overviewData={},this.recentActivities=[],this.recentOrders=[],this.recentUsage=[];case 36:return t.prev=36,this.loading=!1,t.finish(36);case 39:case"end":return t.stop()}}),t,this,[[0,22,36,39]])})));function e(){return t.apply(this,arguments)}return e}(),getWelcomeMessage:function(){var t=(new Date).getHours();return t<6?"夜深了，注意休息哦":t<12?"早上好，新的一天开始了":t<18?"下午好，工作顺利":"晚上好，辛苦了一天"},getActivityIconClass:function(t){var e={recharge:"activity-recharge",consume:"activity-consume",login:"activity-login",api:"activity-api"};return e[t]||"activity-default"},getActivityIcon:function(t){var e={recharge:"anticon anticon-plus-circle",consume:"anticon anticon-minus-circle",login:"anticon anticon-login",api:"anticon anticon-api"};return e[t]||"anticon anticon-info-circle"},getUsageIconClass:function(t){if(!t)return"usage-default";var e=t.toLowerCase();return e.includes("html")||e.includes("网页")?"usage-html":e.includes("xiaohongshu")||e.includes("小红书")?"usage-xiaohongshu":e.includes("image")||e.includes("图片")?"usage-image":e.includes("text")||e.includes("文本")?"usage-text":"usage-api"},getUsageIcon:function(t){if(!t)return"anticon anticon-api";var e=t.toLowerCase();return e.includes("html")||e.includes("网页")?"anticon anticon-global":e.includes("xiaohongshu")||e.includes("小红书")?"anticon anticon-heart":e.includes("image")||e.includes("图片")?"anticon anticon-picture":e.includes("text")||e.includes("文本")?"anticon anticon-file-text":"anticon anticon-api"},formatRelativeTime:function(t){if(!t)return"";var e=new Date,a=new Date(t),n=e-a,i=Math.floor(n/6e4),r=Math.floor(n/36e5),s=Math.floor(n/864e5);return i<1?"刚刚":i<60?"".concat(i,"分钟前"):r<24?"".concat(r,"小时前"):s<7?"".concat(s,"天前"):a.toLocaleDateString("zh-CN")},handleQuickRecharge:function(){this.$emit("navigate","credits")},handleViewProfile:function(){this.$emit("navigate","profile")},handleViewCredits:function(){this.$emit("navigate","credits")},handleViewUsage:function(){this.$emit("navigate","usage")},handleViewMembership:function(){this.$emit("navigate","membership")},handleEditProfile:function(){this.$emit("navigate","profile")},handleApiKeyRegenerated:function(t){this.userInfo&&(this.userInfo.apiKey=t),this.fetchUserInfo()},handleRecharge:function(){this.$emit("navigate","credits")},handleUpgradeMembership:function(){this.$emit("navigate","membership")},handleViewOrders:function(){this.$emit("navigate","orders")},handleGenerateApiKey:function(){this.$emit("navigate","profile")},handleViewAllUsage:function(){this.$emit("navigate","usage")},getPendingOrdersCount:function(){return this.recentOrders&&Array.isArray(this.recentOrders)?this.recentOrders.filter((function(t){return 1===t.order_status||2===t.order_status})).length:0},getUserRole:function(){if(this.userInfo&&this.userInfo.currentRole)return this.userInfo.currentRole;try{var t=JSON.parse(localStorage.getItem("userInfo")||"{}"),e=localStorage.getItem("userRole"),a=localStorage.getItem("roleCode"),n=t.role||t.userRole||t.roleCode||e||a;return n||(n="user"),n}catch(i){return"user"}}}},w=x,y=(a("62cc"),a("2877")),C=Object(y["a"])(w,n,i,!1,null,"afb186e8",null);e["default"]=C.exports},"7e59":function(t,e,a){},"7ea6":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"orders-page"},[t._m(0),a("div",{staticClass:"orders-content"},[a("div",{staticClass:"order-stats"},[a("div",{staticClass:"stats-toggle"},[a("div",{staticClass:"toggle-container"},[a("div",{staticClass:"toggle-option",class:{active:"total"===t.statsMode},on:{click:function(e){t.statsMode="total",t.handleStatsModeChange()}}},[a("i",{staticClass:"anticon anticon-bar-chart"}),a("span",[t._v("总统计")])]),a("div",{staticClass:"toggle-option",class:{active:"month"===t.statsMode},on:{click:function(e){t.statsMode="month",t.handleStatsModeChange()}}},[a("i",{staticClass:"anticon anticon-calendar"}),a("span",[t._v("本月统计")])]),a("div",{staticClass:"toggle-slider",class:{"slide-right":"month"===t.statsMode}})])]),a("div",{staticClass:"stats-cards"},[a("StatsCard",{attrs:{value:t.currentStats.completedOrders,label:"total"===t.statsMode?"总订单完成数":"本月订单完成数",icon:"anticon anticon-check-circle","icon-color":"#10b981",loading:t.loading}}),a("StatsCard",{attrs:{value:t.currentStats.amount,unit:"元",label:"total"===t.statsMode?"总消费金额":"本月消费金额",icon:"anticon anticon-pay-circle","icon-color":"#ef4444",loading:t.loading}}),a("StatsCard",{attrs:{value:t.orderStats.pendingOrders,label:"未付款订单",icon:"anticon anticon-clock-circle","icon-color":"#f59e0b",loading:t.loading}}),a("StatsCard",{attrs:{value:t.currentStats.cancelledOrders,label:"total"===t.statsMode?"总取消订单数":"本月取消订单数",icon:"anticon anticon-close-circle","icon-color":"#6b7280",loading:t.loading}})],1)]),a("DataTable",{ref:"dataTable",attrs:{title:"订单记录","data-source":t.orderList,columns:t.orderColumns,loading:t.tableLoading,pagination:t.pagination,"type-options":t.orderTypeOptions,"status-options":[],"show-action-column":!1,"show-refresh":!1,"show-search":!1,"show-order-search":!0,"show-product-search":!0,"type-filter-placeholder":"全部订单类型","status-filter-placeholder":"全部订单状态","order-search-placeholder":"搜索订单号","product-search-placeholder":"搜索商品名称"},on:{"filter-change":t.handleFilterChange,"table-change":t.handleTableChange,refresh:t.loadOrderList,"view-detail":t.handleViewDetail},scopedSlots:t._u([{key:"actions",fn:function(){return[a("a-button",{staticStyle:{"margin-right":"8px",background:"linear-gradient(135deg, #64748b 0%, #475569 100%)",border:"none","border-radius":"8px","box-shadow":"0 4px 12px rgba(100, 116, 139, 0.3)",color:"white"},on:{click:t.handleResetFilters}},[a("a-icon",{staticStyle:{"margin-right":"6px"},attrs:{type:"reload"}}),t._v("\n          重置\n        ")],1),a("a-button",{staticStyle:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none","border-radius":"8px","box-shadow":"0 4px 12px rgba(102, 126, 234, 0.3)"},attrs:{type:"primary"},on:{click:t.handleExportOrders}},[a("a-icon",{staticStyle:{"margin-right":"6px"},attrs:{type:"download"}}),t._v("\n          导出订单\n        ")],1)]},proxy:!0},{key:"actions-column",fn:function(e){var n=e.record;return[a("a-button",{attrs:{type:"link",size:"small"},on:{click:function(e){return t.handleViewDetail(n)}}},[t._v("\n          查看详情\n        ")]),1===n.order_status?a("a-button",{attrs:{type:"link",size:"small"},on:{click:function(e){return t.handlePayOrder(n)}}},[t._v("\n          立即支付\n        ")]):t._e(),1===n.order_status?a("a-button",{attrs:{type:"link",size:"small"},on:{click:function(e){return t.handleCancelOrder(n)}}},[t._v("\n          取消订单\n        ")]):t._e(),[2,3].includes(n.order_status)&&"plugin"===n.order_type?a("a-button",{attrs:{type:"link",size:"small"},on:{click:function(e){return t.handleDownload(n)}}},[t._v("\n          下载\n        ")]):t._e(),3===n.order_status&&t.canRefund(n)?a("a-button",{attrs:{type:"link",size:"small"},on:{click:function(e){return t.handleRefund(n)}}},[t._v("\n          申请退款\n        ")]):t._e()]}}])})],1),a("a-modal",{attrs:{title:"订单详情",footer:null,width:"700px"},model:{value:t.showOrderDetail,callback:function(e){t.showOrderDetail=e},expression:"showOrderDetail"}},[t.selectedOrder?a("div",{staticClass:"order-detail"},[a("div",{staticClass:"detail-header"},[a("div",{staticClass:"order-basic-info"},[a("h3",[t._v(t._s(t.selectedOrder.title))]),a("p",{staticClass:"order-no"},[t._v("订单号："+t._s(t.selectedOrder.orderNo))]),a("div",{staticClass:"order-meta"},[a("span",{staticClass:"order-type",class:t.getOrderTypeClass(t.selectedOrder.orderType)},[a("i",{class:t.getOrderTypeIcon(t.selectedOrder.orderType)}),t._v("\n              "+t._s(t.getOrderTypeText(t.selectedOrder.orderType))+"\n            ")]),a("span",{staticClass:"order-status",class:t.getStatusClass(t.selectedOrder.status)},[t._v("\n              "+t._s(t.getStatusText(t.selectedOrder.status))+"\n            ")])])]),a("div",{staticClass:"order-amount-info"},[a("div",{staticClass:"amount-label"},[t._v("订单金额")]),a("div",{staticClass:"amount-value"},[t._v("¥"+t._s(t.formatNumber(t.selectedOrder.amount)))])])]),a("div",{staticClass:"detail-content"},[a("div",{staticClass:"detail-section"},[a("h4",[t._v("商品信息")]),a("div",{staticClass:"product-info"},[t.selectedOrder.productImage?a("div",{staticClass:"product-image"},[a("img",{attrs:{src:t.selectedOrder.productImage,alt:t.selectedOrder.title}})]):t._e(),a("div",{staticClass:"product-details"},[a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("商品名称：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.title))])]),t.selectedOrder.productDescription?a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("商品描述：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.productDescription))])]):t._e(),t.selectedOrder.specification?a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("规格：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.specification))])]):t._e(),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("数量：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.quantity||1))])])])])]),a("div",{staticClass:"detail-section"},[a("h4",[t._v("订单信息")]),a("div",{staticClass:"order-timeline"},t._l(t.orderTimeline,(function(e){return a("div",{key:e.time,staticClass:"timeline-item"},[a("div",{staticClass:"timeline-dot",class:{active:e.active}}),a("div",{staticClass:"timeline-content"},[a("div",{staticClass:"timeline-title"},[t._v(t._s(e.title))]),a("div",{staticClass:"timeline-time"},[t._v(t._s(e.time))])])])})),0)]),t.selectedOrder.paymentInfo?a("div",{staticClass:"detail-section"},[a("h4",[t._v("支付信息")]),a("div",{staticClass:"payment-info"},[a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("支付方式：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.paymentInfo.method))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("支付时间：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.formatDateTime(t.selectedOrder.paymentInfo.time)))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("交易单号：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.paymentInfo.transactionId))])])])]):t._e()]),a("div",{staticClass:"detail-actions"},[a("a-button",{on:{click:function(e){t.showOrderDetail=!1}}},[t._v("关闭")]),1===t.selectedOrder.status?a("a-button",{attrs:{type:"primary"},on:{click:function(e){return t.handlePayOrder(t.selectedOrder)}}},[t._v("\n          立即支付\n        ")]):t._e()],1)]):t._e()])],1)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-header"},[a("h1",{staticClass:"page-title"},[t._v("订单记录")]),a("p",{staticClass:"page-description"},[t._v("查看您的插件购买、会员订阅和充值记录")])])}],r=a("a34a"),s=a.n(r),o=a("1189"),c=a("4dbe"),l=a("77ea");function d(t,e,a,n,i,r,s){try{var o=t[r](s),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(n,i)}function u(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function s(t){d(r,n,i,s,o,"next",t)}function o(t){d(r,n,i,s,o,"throw",t)}s(void 0)}))}}function p(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function h(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?p(Object(a),!0).forEach((function(e){f(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):p(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function f(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var m={name:"UserCenterOrders",components:{StatsCard:o["default"],DataTable:c["default"]},data:function(){var t=this,e=this.$createElement;return{loading:!0,tableLoading:!1,statsMode:"total",orderStats:{totalCompletedOrders:0,totalAmount:0,totalCancelledOrders:0,monthCompletedOrders:0,monthAmount:0,monthCancelledOrders:0,pendingOrders:0},filters:{orderType:"",status:"",dateRange:[],keyword:""},orderList:[],rawOrderData:[],pagination:{current:1,pageSize:10,total:0},orderColumns:[{title:"商品名称",dataIndex:"description",key:"description",width:200,align:"center",customRender:function(t){return t||"订单商品"}},{title:"订单号",dataIndex:"related_order_id",key:"related_order_id",width:200,align:"center",customRender:function(t){return t||"-"}},{title:"订单类型",dataIndex:"order_type",key:"order_type",width:120,align:"center",customRender:function(t){var e={plugin:"插件购买",membership:"会员订阅",recharge:"账户充值"};return e[t]||"未知类型"}},{title:"交易金额",dataIndex:"amount",key:"amount",width:120,align:"center",customRender:function(t,a){var n=parseFloat(t||0).toFixed(2),i=[2,3,4].includes(Number(a.transaction_type)),r=i?"+":"-",s=i?"color: #52c41a; font-weight: 600;":"color: #ff4d4f; font-weight: 600;";return e("span",{style:s},[r,"¥",n])}},{title:"余额",dataIndex:"balance_after",key:"balance_after",width:120,align:"center",customRender:function(t){var a=parseFloat(t||0).toFixed(2),n="color: #1890ff; font-weight: 600;";return e("span",{style:n},["¥",a])}},{title:"创建时间",dataIndex:"create_time",key:"create_time",width:150,align:"center",customRender:function(e){return t.formatDateTime(e)}},{title:"状态",dataIndex:"order_status",key:"order_status",width:150,align:"center",customRender:function(t){var a={1:{text:"未付款",style:"color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"},3:{text:"已完成",style:"color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"},4:{text:"已取消",style:"color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"},5:{text:"已退款",style:"color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"}},n=a[t]||{text:"未知状态",style:"color: #8c8c8c; background: #f5f5f5; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"};return e("span",{style:n.style},[n.text])}}],orderTypeOptions:[{value:"plugin",label:"插件购买"},{value:"membership",label:"会员订阅"},{value:"recharge",label:"账户充值"}],orderStatusOptions:[{value:1,label:"未付款"},{value:3,label:"已完成"},{value:4,label:"已取消"},{value:5,label:"已退款"}],showOrderDetail:!1,selectedOrder:null,orderTimeline:[]}},computed:{paginationConfig:function(){return h(h({},this.pagination),{},{showSizeChanger:!0,showQuickJumper:!0,showTotal:function(t,e){return"第 ".concat(e[0],"-").concat(e[1]," 条，共 ").concat(t," 条")},pageSizeOptions:["10","20","50","100"]})},currentStats:function(){return"month"===this.statsMode?{completedOrders:this.orderStats.monthCompletedOrders,amount:this.orderStats.monthAmount,cancelledOrders:this.orderStats.monthCancelledOrders}:{completedOrders:this.orderStats.totalCompletedOrders,amount:this.orderStats.totalAmount,cancelledOrders:this.orderStats.totalCancelledOrders}}},mounted:function(){var t=u(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.loadData();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{loadData:function(){var t=u(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,Promise.all([this.loadOrderStats(),this.loadOrderList()]);case 4:t.next=10;break;case 6:t.prev=6,t.t0=t["catch"](0),this.$message.error("加载数据失败，请刷新重试");case 10:return t.prev=10,this.loading=!1,t.finish(10);case 13:case"end":return t.stop()}}),t,this,[[0,6,10,13]])})));function e(){return t.apply(this,arguments)}return e}(),loadOrderStats:function(){var t=u(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(l["l"])();case 3:e=t.sent,e.success?this.orderStats=e.result||{}:this.orderStats={totalCompletedOrders:0,totalAmount:0,totalCancelledOrders:0,monthCompletedOrders:0,monthAmount:0,monthCancelledOrders:0,pendingOrders:0},t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0);case 11:case"end":return t.stop()}}),t,this,[[0,8]])})));function e(){return t.apply(this,arguments)}return e}(),loadOrderList:function(){var t=u(s.a.mark((function t(){var e,a,n,i;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.tableLoading=!0,e={pageNo:this.pagination.current,pageSize:this.pagination.pageSize,orderType:this.filters.type,status:this.filters.status,orderKeyword:this.filters.orderKeyword,productKeyword:this.filters.productKeyword},this.filters.dateRange&&2===this.filters.dateRange.length&&(e.startDate=this.filters.dateRange[0].format("YYYY-MM-DD"),e.endDate=this.filters.dateRange[1].format("YYYY-MM-DD")),t.next=6,Object(l["u"])(e);case 6:a=t.sent,a.success&&(n=a.result&&a.result.records||[],n.length>0&&Object.keys(n[0]).forEach((function(t){})),i=JSON.parse(JSON.stringify(n)),this.orderList=i,this.pagination.total=a.result&&a.result.total||0,this.orderList.length),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](0),this.orderList=[];case 15:return t.prev=15,this.tableLoading=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[0,11,15,18]])})));function e(){return t.apply(this,arguments)}return e}(),handleFilterChange:function(t){this.filters=h(h({},this.filters),t),this.pagination.current=1,this.loadOrderList()},handleResetFilters:function(){this.$refs.dataTable&&this.$refs.dataTable.resetFilters&&this.$refs.dataTable.resetFilters()},handleTableChange:function(t){var e=t.pagination;this.pagination=h(h({},this.pagination),e),this.loadOrderList()},handleViewDetail:function(){var t=u(s.a.mark((function t(e){var a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(l["t"])(e.id);case 3:a=t.sent,a.success&&(this.selectedOrder=a.result,this.generateOrderTimeline(this.selectedOrder),this.showOrderDetail=!0),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.$message.error("获取订单详情失败");case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(e){return t.apply(this,arguments)}return e}(),handlePayOrder:function(){var t=u(s.a.mark((function t(e){var a=this;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{this.$confirm({title:"确认支付",content:"确定要支付订单 ".concat(e.related_order_id," 吗？金额：¥").concat(e.amount),onOk:function(){var t=u(s.a.mark((function t(){var n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(l["A"])({orderId:e.related_order_id});case 3:if(n=t.sent,!n.success){t.next=10;break}return a.$message.success("支付成功！"),t.next=8,a.loadData();case 8:t.next=11;break;case 10:a.$message.error(n.message||"支付失败");case 11:t.next=17;break;case 13:t.prev=13,t.t0=t["catch"](0),a.$message.error(t.t0.response&&t.t0.response.data&&t.t0.response.data.message||"支付失败，请重试");case 17:case"end":return t.stop()}}),t,null,[[0,13]])})));function n(){return t.apply(this,arguments)}return n}()})}catch(n){}case 1:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),handleStatsModeChange:function(){this.statsMode},handleCancelOrder:function(t){var e=this;this.$confirm({title:"确认取消订单",content:"确定要取消订单 ".concat(t.orderNo," 吗？"),onOk:function(){var t=u(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{e.$message.success("订单已取消"),e.loadOrderList()}catch(a){e.$message.error("取消订单失败")}case 1:case"end":return t.stop()}}),t)})));function a(){return t.apply(this,arguments)}return a}()})},handleDownload:function(t){this.$message.info("开始下载：".concat(t.title))},handleRefund:function(t){this.$message.info("申请退款：".concat(t.orderNo))},handleExportOrders:function(){var t=u(s.a.mark((function t(){var e,a,n,i,r,o,c;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在导出订单数据...",0),e={orderType:this.filters.type,status:this.filters.status,orderKeyword:this.filters.orderKeyword,productKeyword:this.filters.productKeyword},this.filters.dateRange&&2===this.filters.dateRange.length&&(e.startDate=this.filters.dateRange[0].format("YYYY-MM-DD"),e.endDate=this.filters.dateRange[1].format("YYYY-MM-DD")),t.next=7,Object(l["d"])(e);case 7:a=t.sent,this.$message.destroy(),n=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),i=window.URL.createObjectURL(n),r=document.createElement("a"),r.href=i,o=new Date,c=o.getFullYear()+String(o.getMonth()+1).padStart(2,"0")+String(o.getDate()).padStart(2,"0")+"_"+String(o.getHours()).padStart(2,"0")+String(o.getMinutes()).padStart(2,"0"),r.download="订单记录_".concat(c,".xlsx"),document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(i),this.$message.success("订单数据导出成功！"),t.next=28;break;case 23:t.prev=23,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("导出失败，请重试");case 28:case"end":return t.stop()}}),t,this,[[0,23]])})));function e(){return t.apply(this,arguments)}return e}(),handleTestMembershipOrder:function(){var t=u(s.a.mark((function t(){var e,a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在创建测试订单...",0),e={membershipLevel:2,duration:1,amount:99,planName:"VIP会员月卡",features:["高级AI服务","每日100次调用","优先客服"]},t.next=6,Object(l["b"])(e);case 6:if(a=t.sent,this.$message.destroy(),!a.success){t.next=15;break}return this.$message.success("测试订单创建成功！订单号: ".concat(a.result.orderId)),t.next=12,this.loadData();case 12:this.$message.info("页面数据已自动刷新，请查看订单列表和统计数据"),t.next=16;break;case 15:this.$message.error("创建订单失败: ".concat(a.message||"未知错误"));case 16:t.next=23;break;case 18:t.prev=18,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("创建订单异常: ".concat(t.t0.message||"网络错误"));case 23:case"end":return t.stop()}}),t,this,[[0,18]])})));function e(){return t.apply(this,arguments)}return e}(),generateOrderTimeline:function(t){this.orderTimeline=[{title:"订单创建",time:this.formatDateTime(t.createTime),active:!0},{title:"等待支付",time:t.status>=1?this.formatDateTime(t.createTime):"",active:t.status>=1},{title:"订单完成",time:t.status>=3?this.formatDateTime(t.transaction_time):"",active:t.status>=3}]},canRefund:function(t){var e=["plugin","membership"],a=[3],n=!0;return e.includes(t.orderType)&&a.includes(t.status)&&n},getOrderTypeClass:function(t){var e={plugin:"type-plugin",membership:"type-membership",recharge:"type-recharge"};return e[t]||""},getOrderTypeIcon:function(t){var e={plugin:"anticon anticon-appstore",membership:"anticon anticon-crown",recharge:"anticon anticon-wallet"};return e[t]||"anticon anticon-shopping"},getOrderTitle:function(t){if(!t)return"订单商品";if(t.product_info)try{var e={};if(e="string"===typeof t.product_info?JSON.parse(t.product_info):t.product_info,e.planName)return e.planName}catch(a){}return t.description||"订单商品"},getOrderTypeText:function(t){var e={plugin:"插件购买",membership:"会员订阅",recharge:"账户充值"},a=e[t]||"未知类型";return a},getStatusClass:function(t){var e={1:"status-pending",3:"status-completed",4:"status-cancelled",5:"status-refunded"};return e[t]||""},getStatusText:function(t){var e={1:"待支付",3:"已完成",4:"已取消",5:"已退款"},a=e[t]||"未知状态";return a},formatNumber:function(t){return t?parseFloat(t).toFixed(2):"0.00"},formatDateTime:function(t){if(!t)return"-";try{var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}}}},g=m,v=(a("1392"),a("2877")),b=Object(v["a"])(g,n,i,!1,null,"036df3f5",null);e["default"]=b.exports},8525:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"notifications-page"},[t._m(0),a("div",{staticClass:"notification-stats"},[a("div",{staticClass:"stat-card"},[t._m(1),a("div",{staticClass:"stat-content"},[a("div",{staticClass:"stat-number"},[t._v(t._s(t.unreadCount))]),a("div",{staticClass:"stat-label"},[t._v("未读通知")])])]),a("div",{staticClass:"stat-card"},[t._m(2),a("div",{staticClass:"stat-content"},[a("div",{staticClass:"stat-number"},[t._v(t._s(t.totalCount))]),a("div",{staticClass:"stat-label"},[t._v("总通知数")])])])]),a("div",{staticClass:"action-bar"},[a("div",{staticClass:"action-left"},[a("a-button",{attrs:{disabled:0===t.unreadCount,loading:t.markingAllRead},on:{click:t.handleMarkAllRead}},[a("i",{staticClass:"anticon anticon-check"}),t._v("\n        全部标记已读\n      ")]),a("a-button",{attrs:{loading:t.loading},on:{click:t.handleRefresh}},[a("i",{staticClass:"anticon anticon-reload"}),t._v("\n        刷新\n      ")])],1),a("div",{staticClass:"action-right"},[a("a-select",{staticStyle:{width:"120px"},on:{change:t.handleFilterChange},model:{value:t.filterType,callback:function(e){t.filterType=e},expression:"filterType"}},[a("a-select-option",{attrs:{value:"all"}},[t._v("全部")]),a("a-select-option",{attrs:{value:"unread"}},[t._v("未读")]),a("a-select-option",{attrs:{value:"read"}},[t._v("已读")])],1)],1)]),a("div",{staticClass:"notifications-container"},[t.loading&&0===t.notifications.length?a("div",{staticClass:"loading-container"},[a("a-spin",{attrs:{size:"large",tip:"加载中..."}},[a("div",{staticClass:"loading-placeholder"})])],1):0===t.filteredNotifications.length?a("div",{staticClass:"empty-container"},[t._m(3),a("h3",[t._v(t._s("unread"===t.filterType?"暂无未读通知":"暂无通知"))]),a("p",[t._v(t._s("unread"===t.filterType?"所有通知都已阅读":"系统暂时没有发送任何通知"))])]):a("div",{staticClass:"notifications-list"},t._l(t.filteredNotifications,(function(e){return a("div",{key:e.id,staticClass:"notification-card",class:{unread:!e.readFlag}},[a("div",{staticClass:"notification-header"},[a("div",{staticClass:"notification-title"},[a("span",{staticClass:"title-text"},[t._v(t._s(e.esTitle))]),e.readFlag?t._e():a("span",{staticClass:"unread-badge"},[t._v("未读")])]),a("div",{staticClass:"notification-actions"},[e.readFlag?t._e():a("a-button",{attrs:{type:"link",size:"small",loading:e.marking},on:{click:function(a){return t.handleMarkAsRead(e)}}},[t._v("\n              标记已读\n            ")]),a("span",{staticClass:"notification-time"},[t._v(t._s(t.formatTime(e.createTime)))])],1)]),a("div",{staticClass:"notification-content"},[a("p",[t._v(t._s(e.esContent))])]),a("div",{staticClass:"notification-meta"},[a("span",{staticClass:"notification-type"},[t._v("系统通知")]),a("span",{staticClass:"notification-id"},[t._v("ID: "+t._s(e.id))])])])})),0),t.filteredNotifications.length>0?a("div",{staticClass:"pagination-container"},[a("a-pagination",{attrs:{total:t.totalCount,"page-size":t.pageSize,"show-size-changer":!1,"show-quick-jumper":!0,"show-total":function(t,e){return"第 "+e[0]+"-"+e[1]+" 条，共 "+t+" 条"}},on:{change:t.handlePageChange},model:{value:t.currentPage,callback:function(e){t.currentPage=e},expression:"currentPage"}})],1):t._e()])])},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-header"},[a("h1",{staticClass:"page-title"},[t._v("系统通知")]),a("p",{staticClass:"page-description"},[t._v("查看和管理您的系统通知消息")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"stat-icon unread"},[a("i",{staticClass:"anticon anticon-bell"})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"stat-icon total"},[a("i",{staticClass:"anticon anticon-message"})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"empty-icon"},[a("i",{staticClass:"anticon anticon-bell"})])}],r=a("a34a"),s=a.n(r),o=a("ff1f");function c(t,e){var a;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(a=l(t))||e&&t&&"number"===typeof t.length){a&&(t=a);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,o=!1;return{s:function(){a=t[Symbol.iterator]()},n:function(){var t=a.next();return s=t.done,t},e:function(t){o=!0,r=t},f:function(){try{s||null==a.return||a.return()}finally{if(o)throw r}}}}function l(t,e){if(t){if("string"===typeof t)return d(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=new Array(e);a<e;a++)n[a]=t[a];return n}function u(t,e,a,n,i,r,s){try{var o=t[r](s),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(n,i)}function p(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function s(t){u(r,n,i,s,o,"next",t)}function o(t){u(r,n,i,s,o,"throw",t)}s(void 0)}))}}var h={name:"NotificationsPage",data:function(){return{loading:!1,markingAllRead:!1,notifications:[],currentPage:1,pageSize:10,totalCount:0,filterType:"all"}},computed:{unreadCount:function(){return this.notifications.filter((function(t){return!t.readFlag})).length},filteredNotifications:function(){return"unread"===this.filterType?this.notifications.filter((function(t){return!t.readFlag})):"read"===this.filterType?this.notifications.filter((function(t){return t.readFlag})):this.notifications}},mounted:function(){var t=p(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.loadNotifications();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{loadNotifications:function(){var t=p(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,Object(o["b"])({pageNo:this.currentPage,pageSize:this.pageSize});case 4:e=t.sent,e.success?(this.notifications=e.result.records||[],this.totalCount=e.result.total||0):this.$message.error(e.message||"获取通知失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),this.$message.error("获取通知失败，请重试");case 12:return t.prev=12,this.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function e(){return t.apply(this,arguments)}return e}(),handleMarkAsRead:function(){var t=p(s.a.mark((function t(e){var a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$set(e,"marking",!0),t.next=4,Object(o["f"])(e.id);case 4:a=t.sent,a.success?(e.readFlag=!0,this.$notification.success({message:"标记已读成功",description:"通知已标记为已读状态。",placement:"topRight",duration:2,style:{width:"300px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})):this.$notification.error({message:"标记失败",description:a.message||"标记操作失败，请稍后重试。",placement:"topRight",duration:3,style:{width:"320px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),this.$notification.error({message:"标记失败",description:"网络连接异常，请检查网络后重试。",placement:"topRight",duration:3,style:{width:"320px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 12:return t.prev=12,this.$set(e,"marking",!1),t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function e(e){return t.apply(this,arguments)}return e}(),handleMarkAllRead:function(){var t=p(s.a.mark((function t(){var e,a,n,i;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:t.prev=0,this.markingAllRead=!0,e=this.notifications.filter((function(t){return!t.readFlag})),a=c(e),t.prev=4,a.s();case 6:if((n=a.n()).done){t.next=13;break}return i=n.value,t.next=10,Object(o["f"])(i.id);case 10:i.readFlag=!0;case 11:t.next=6;break;case 13:t.next=18;break;case 15:t.prev=15,t.t0=t["catch"](4),a.e(t.t0);case 18:return t.prev=18,a.f(),t.finish(18);case 21:this.$notification.success({message:"全部标记已读成功",description:"所有未读通知已标记为已读状态。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.next=28;break;case 24:t.prev=24,t.t1=t["catch"](0),this.$notification.error({message:"批量标记失败",description:"批量标记操作失败，请检查网络后重试。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 28:return t.prev=28,this.markingAllRead=!1,t.finish(28);case 31:case"end":return t.stop()}}),t,this,[[0,24,28,31],[4,15,18,21]])})));function e(){return t.apply(this,arguments)}return e}(),handleRefresh:function(){var t=p(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.loadNotifications();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleFilterChange:function(){this.currentPage=1},handlePageChange:function(){var t=p(s.a.mark((function t(e){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.currentPage=e,t.next=3,this.loadNotifications();case 3:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),formatTime:function(t){if(!t)return"";var e=new Date(t),a=new Date,n=a-e,i=Math.floor(n/6e4),r=Math.floor(n/36e5),s=Math.floor(n/864e5);return i<1?"刚刚":i<60?"".concat(i,"分钟前"):r<24?"".concat(r,"小时前"):s<7?"".concat(s,"天前"):e.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}}},f=h,m=(a("412f"),a("2877")),g=Object(m["a"])(f,n,i,!1,null,"0ebeabb4",null);e["default"]=g.exports},a854:function(t,e,a){},ab07:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"referral-page"},[t._m(0),a("div",{staticClass:"referral-content"},[a("div",{staticClass:"referral-stats"},[a("StatsCard",{attrs:{value:t.referralStats.totalReferrals,label:"推荐人数",icon:"anticon anticon-team","icon-color":"#7c8aed",loading:t.loading}}),a("StatsCard",{attrs:{value:t.referralStats.totalRewards,unit:"元",label:"累计奖励",icon:"anticon anticon-gift","icon-color":"#10b981",loading:t.loading}}),a("StatsCard",{attrs:{value:t.referralStats.availableRewards,unit:"元",label:"可提现金额",icon:"anticon anticon-wallet","icon-color":"#f59e0b",loading:t.loading}}),a("StatsCard",{attrs:{value:t.referralStats.monthlyReferrals,label:"本月推荐",icon:"anticon anticon-calendar","icon-color":"#ef4444",trend:t.monthlyTrend,loading:t.loading}})],1),a("div",{staticClass:"referral-link-section"},[a("h3",{staticClass:"section-title"},[t._v("我的推荐链接")]),a("div",{staticClass:"link-generator"},[a("div",{staticClass:"link-display"},[a("div",{staticClass:"link-input"},[a("a-input",{attrs:{value:t.referralLink,readonly:"",size:"large",placeholder:"点击生成推荐链接"}}),a("a-button",{attrs:{type:"primary",size:"large",loading:t.linkLoading},on:{click:t.handleCopyLink}},[a("i",{staticClass:"anticon anticon-copy"}),t._v("\n              复制链接\n            ")])],1),a("div",{staticClass:"link-actions"},[a("a-button",{attrs:{loading:t.linkLoading},on:{click:t.handleGenerateLink}},[a("i",{staticClass:"anticon anticon-reload"}),t._v("\n              重新生成\n            ")]),a("a-button",{on:{click:t.handleGenerateQRCode}},[a("i",{staticClass:"anticon anticon-qrcode"}),t._v("\n              生成二维码\n            ")]),a("a-button",{on:{click:t.handleShareToSocial}},[a("i",{staticClass:"anticon anticon-share-alt"}),t._v("\n              分享到社交媒体\n            ")])],1)]),a("div",{staticClass:"referral-tips"},[a("h4",[t._v("推荐奖励规则")]),a("ul",[a("li",[t._v("好友通过您的链接注册并订阅会员，您可获得其订阅金额的 "),a("strong",[t._v(t._s(t.currentCommissionRate)+"%")]),t._v(" 佣金")]),a("li",[t._v("普通用户："+t._s(t.normalRate)+"% 基础佣金，邀请10人升至"+t._s(t.normalHighRate)+"%，邀请30人升至"+t._s(t.normalTopRate)+"%")]),a("li",[t._v("VIP会员："+t._s(t.vipRate)+"% 基础佣金，邀请10人升至"+t._s(t.vipHighRate)+"%，邀请30人升至"+t._s(t.vipTopRate)+"%")]),a("li",[t._v("SVIP会员：直接享受 "+t._s(t.svipRate)+"% 最高佣金")]),a("li",[t._v("佣金将在好友完成订阅后24小时内到账")]),a("li",[t._v("累计佣金满100元即可申请提现")])])])])]),a("div",{staticClass:"referral-records"},[a("div",{staticClass:"records-header"},[a("h3",{staticClass:"section-title"},[t._v("推荐记录")]),a("div",{staticClass:"records-filters"},[a("a-select",{staticStyle:{width:"120px"},attrs:{placeholder:"状态筛选"},on:{change:t.handleRecordFilterChange},model:{value:t.recordFilters.status,callback:function(e){t.$set(t.recordFilters,"status",e)},expression:"recordFilters.status"}},[a("a-select-option",{attrs:{value:""}},[t._v("全部状态")]),a("a-select-option",{attrs:{value:"pending"}},[t._v("待确认")]),a("a-select-option",{attrs:{value:"confirmed"}},[t._v("已确认")]),a("a-select-option",{attrs:{value:"rewarded"}},[t._v("已奖励")])],1),a("a-range-picker",{staticStyle:{width:"240px"},on:{change:t.handleRecordFilterChange},model:{value:t.recordFilters.dateRange,callback:function(e){t.$set(t.recordFilters,"dateRange",e)},expression:"recordFilters.dateRange"}})],1)]),a("a-table",{attrs:{columns:t.recordColumns,"data-source":t.referralRecords,loading:t.recordLoading,pagination:t.recordPagination,"row-key":"id"},on:{change:t.handleRecordTableChange},scopedSlots:t._u([{key:"friendInfo",fn:function(e){var n=e.record;return[a("div",{staticClass:"friend-info"},[a("div",{staticClass:"friend-avatar"},[a("img",{attrs:{src:n.friendAvatar||t.defaultAvatar,alt:n.friendNickname}})]),a("div",{staticClass:"friend-details"},[a("div",{staticClass:"friend-name"},[t._v(t._s(n.friendNickname||"新用户"))]),a("div",{staticClass:"friend-email"},[t._v(t._s(t.maskEmail(n.friendEmail)))])])])]}},{key:"rewardAmount",fn:function(e){var n=e.text;return[a("span",{staticClass:"reward-amount"},[t._v("¥"+t._s(t.formatNumber(n)))])]}},{key:"status",fn:function(e){var n=e.text;return[a("span",{staticClass:"record-status",class:t.getRecordStatusClass(n)},[t._v("\n            "+t._s(t.getRecordStatusText(n))+"\n          ")])]}},{key:"time",fn:function(e){var n=e.text;return[a("span",{staticClass:"record-time"},[t._v(t._s(t.formatDateTime(n)))])]}}])})],1),a("div",{staticClass:"withdrawal-section"},[a("h3",{staticClass:"section-title"},[t._v("奖励提现")]),a("div",{staticClass:"withdrawal-card"},[a("div",{staticClass:"withdrawal-info"},[a("div",{staticClass:"balance-display"},[a("div",{staticClass:"balance-item"},[a("div",{staticClass:"balance-label"},[t._v("可提现余额")]),a("div",{staticClass:"balance-amount"},[t._v("¥"+t._s(t.formatNumber(t.withdrawalInfo.availableAmount||0)))])]),a("div",{staticClass:"balance-item"},[a("div",{staticClass:"balance-label"},[t._v("冻结金额")]),a("div",{staticClass:"balance-amount frozen"},[t._v("¥"+t._s(t.formatNumber(t.withdrawalInfo.frozenAmount||0)))])])]),t.withdrawalInfo.hasPendingRequest?a("div",{staticClass:"withdrawal-status-tip"},[a("a-alert",{attrs:{message:"您有提现申请正在审核中",description:"申请金额：¥"+t.formatNumber(t.withdrawalInfo.pendingAmount)+"，申请时间："+t.formatDateTime(t.withdrawalInfo.pendingTime),type:"info","show-icon":""}})],1):t._e(),t._m(1)]),t.withdrawalInfo.canWithdraw?a("div",{staticClass:"withdrawal-form"},[a("a-form",{attrs:{layout:"vertical"}},[a("a-form-item",{attrs:{label:"提现金额"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{min:50,max:t.withdrawalInfo.availableAmount,step:10,placeholder:"请输入提现金额",size:"large"},model:{value:t.withdrawalAmount,callback:function(e){t.withdrawalAmount=e},expression:"withdrawalAmount"}}),a("div",{staticClass:"amount-tips"},[a("span",[t._v("最低50元")]),a("a",{on:{click:t.setMaxAmount}},[t._v("全部提现")])])],1),a("a-form-item",{attrs:{label:"真实姓名"}},[a("a-input",{attrs:{placeholder:"请输入真实姓名",size:"large"},model:{value:t.realName,callback:function(e){t.realName=e},expression:"realName"}})],1),a("a-form-item",{attrs:{label:"支付宝账号"}},[a("a-input",{attrs:{placeholder:"请输入支付宝账号（手机号）",size:"large"},model:{value:t.alipayAccount,callback:function(e){t.alipayAccount=e},expression:"alipayAccount"}})],1),a("div",{staticClass:"withdrawal-summary"},[a("div",{staticClass:"summary-row"},[a("span",[t._v("提现金额：")]),a("span",[t._v("¥"+t._s(t.formatNumber(t.withdrawalAmount)))])]),a("div",{staticClass:"summary-row total"},[a("span",[t._v("实际到账：")]),a("span",[t._v("¥"+t._s(t.formatNumber(t.withdrawalAmount)))])])]),a("a-button",{attrs:{type:"primary",size:"large",disabled:!t.isFormValid,loading:t.withdrawalLoading,block:""},on:{click:t.showConfirmModal}},[t._v("\n              申请提现\n            ")])],1)],1):a("div",{staticClass:"withdrawal-disabled"},[a("a-alert",{attrs:{message:t.withdrawalInfo.message,type:"warning","show-icon":""}})],1)])]),a("div",{staticClass:"withdrawal-history"},[a("h3",{staticClass:"section-title"},[t._v("提现记录")]),a("a-table",{attrs:{columns:t.withdrawalColumns,"data-source":t.withdrawalHistory,loading:t.withdrawalHistoryLoading,pagination:t.withdrawalPagination,"row-key":"id"},on:{change:t.handleWithdrawalTableChange},scopedSlots:t._u([{key:"amount",fn:function(e,n){return[a("span",{staticClass:"amount"},[t._v("¥"+t._s(t.formatNumber(n.withdrawal_amount)))])]}},{key:"status",fn:function(e,n){return[a("a-tag",{attrs:{color:t.getWithdrawalStatusColor(n.status)}},[t._v("\n            "+t._s(n.statusText||t.getWithdrawalStatusText(n.status,n.review_remark))+"\n          ")])]}},{key:"time",fn:function(e){return[a("span",{staticClass:"time-text"},[t._v(t._s(t.formatDateTime(e)))])]}}])})],1)]),a("a-modal",{attrs:{title:"确认提现信息",footer:null,width:"500px"},model:{value:t.showConfirmWithdrawal,callback:function(e){t.showConfirmWithdrawal=e},expression:"showConfirmWithdrawal"}},[a("div",{staticClass:"withdrawal-confirm"},[a("a-alert",{staticStyle:{"margin-bottom":"20px"},attrs:{message:"请仔细核对提现信息，提交后无法修改",type:"warning","show-icon":""}}),a("div",{staticClass:"confirm-info"},[a("div",{staticClass:"info-row"},[a("span",{staticClass:"label"},[t._v("提现金额：")]),a("span",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.withdrawalAmount)))])]),a("div",{staticClass:"info-row"},[a("span",{staticClass:"label"},[t._v("真实姓名：")]),a("span",{staticClass:"value"},[t._v(t._s(t.realName))])]),a("div",{staticClass:"info-row"},[a("span",{staticClass:"label"},[t._v("支付宝账号：")]),a("span",{staticClass:"value"},[t._v(t._s(t.alipayAccount))])]),a("div",{staticClass:"info-row"},[a("span",{staticClass:"label"},[t._v("实际到账：")]),a("span",{staticClass:"value highlight"},[t._v("¥"+t._s(t.formatNumber(t.withdrawalAmount)))])])]),a("div",{staticClass:"confirm-checkbox"},[a("a-checkbox",{model:{value:t.confirmChecked,callback:function(e){t.confirmChecked=e},expression:"confirmChecked"}},[t._v("\n          我已仔细核对以上信息，确认无误\n        ")])],1),a("div",{staticClass:"confirm-actions"},[a("a-button",{staticStyle:{"margin-right":"10px"},on:{click:function(e){t.showConfirmWithdrawal=!1}}},[t._v("\n          取消\n        ")]),a("a-button",{attrs:{type:"primary",disabled:!t.confirmChecked,loading:t.withdrawalLoading},on:{click:t.handleConfirmWithdrawal}},[t._v("\n          确认提交\n        ")])],1)],1)]),a("a-modal",{attrs:{title:"推荐二维码",footer:null,width:"400px"},model:{value:t.showQRModal,callback:function(e){t.showQRModal=e},expression:"showQRModal"}},[a("div",{staticClass:"qr-modal"},[a("div",{ref:"qrCodeContainer",staticClass:"qr-code"}),a("div",{staticClass:"qr-tips"},[a("p",[t._v("扫描二维码或分享链接给好友")]),a("p",[t._v("好友注册并充值后您将获得奖励")])]),a("div",{staticClass:"qr-actions"},[a("a-button",{on:{click:t.handleDownloadQR}},[t._v("下载二维码")]),a("a-button",{attrs:{type:"primary"},on:{click:t.handleCopyLink}},[t._v("复制链接")])],1)])])],1)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-header"},[a("h1",{staticClass:"page-title"},[t._v("推荐奖励")]),a("p",{staticClass:"page-description"},[t._v("邀请好友注册，获得丰厚奖励")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"withdrawal-rules"},[a("h4",[t._v("提现规则")]),a("ul",[a("li",[t._v("最低提现金额：50元")]),a("li",[t._v("支付宝账号：仅支持手机号")]),a("li",[t._v("无手续费")]),a("li",[t._v("到账时间：1-3个工作日")]),a("li",[t._v("同一时间只能有一个待审核申请")])])])}],r=a("a34a"),s=a.n(r),o=a("1189"),c=a("77ea");function l(t,e){return f(t)||h(t,e)||u(t,e)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"===typeof t)return p(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=new Array(e);a<e;a++)n[a]=t[a];return n}function h(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],n=!0,i=!1,r=void 0;try{for(var s,o=t[Symbol.iterator]();!(n=(s=o.next()).done);n=!0)if(a.push(s.value),e&&a.length===e)break}catch(c){i=!0,r=c}finally{try{n||null==o["return"]||o["return"]()}finally{if(i)throw r}}return a}}function f(t){if(Array.isArray(t))return t}function m(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function g(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?m(Object(a),!0).forEach((function(e){v(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):m(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function v(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function b(t,e,a,n,i,r,s){try{var o=t[r](s),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(n,i)}function x(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function s(t){b(r,n,i,s,o,"next",t)}function o(t){b(r,n,i,s,o,"throw",t)}s(void 0)}))}}var w={name:"UserCenterReferral",components:{StatsCard:o["default"]},data:function(){return{loading:!0,linkLoading:!1,recordLoading:!1,withdrawalLoading:!1,withdrawalHistoryLoading:!1,defaultAvatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face",referralStats:{totalReferrals:0,totalRewards:0,availableRewards:0,monthlyReferrals:0},commissionConfig:{userType:"NORMAL",commissionLevel:1,inviteCount:0},normalRate:30,normalHighRate:40,normalTopRate:50,vipRate:35,vipHighRate:45,vipTopRate:50,svipRate:50,currentCommissionRate:30,referralLink:"",showQRModal:!1,recordFilters:{status:"",dateRange:[]},referralRecords:[],recordPagination:{current:1,pageSize:10,total:0},recordColumns:[{title:"好友信息",key:"friendInfo",width:200,scopedSlots:{customRender:"friendInfo"}},{title:"注册时间",dataIndex:"registerTime",key:"registerTime",width:150,scopedSlots:{customRender:"time"}},{title:"奖励金额",dataIndex:"rewardAmount",key:"rewardAmount",width:120,align:"right",scopedSlots:{customRender:"rewardAmount"}},{title:"状态",dataIndex:"status",key:"status",width:100,scopedSlots:{customRender:"status"}}],withdrawalAmount:50,realName:"",alipayAccount:"",withdrawalInfo:{availableAmount:0,frozenAmount:0,minWithdrawalAmount:50,hasPendingRequest:!1,pendingAmount:0,pendingTime:null,canWithdraw:!1,message:""},showConfirmWithdrawal:!1,confirmChecked:!1,withdrawalHistory:[],withdrawalPagination:{current:1,pageSize:10,total:0},withdrawalColumns:[{title:"提现金额",dataIndex:"withdrawal_amount",key:"amount",width:120,align:"right",scopedSlots:{customRender:"amount"}},{title:"真实姓名",dataIndex:"alipay_name",key:"realName",width:100},{title:"支付宝账号",dataIndex:"alipay_account",key:"alipayAccount",ellipsis:!0},{title:"申请时间",dataIndex:"apply_time",key:"applyTime",width:150,scopedSlots:{customRender:"time"}},{title:"审核时间",dataIndex:"review_time",key:"reviewTime",width:150,scopedSlots:{customRender:"time"}},{title:"状态",dataIndex:"status",key:"status",width:100,scopedSlots:{customRender:"status"}}]}},computed:{monthlyTrend:function(){return{type:"up",value:20.5,text:"较上月增长20.5%"}},isFormValid:function(){return this.withdrawalAmount>=50&&this.withdrawalAmount<=this.withdrawalInfo.availableAmount&&""!==this.realName.trim()&&""!==this.alipayAccount.trim()}},mounted:function(){var t=x(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.loadData();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{loadData:function(){var t=x(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,Promise.all([this.loadReferralStats(),this.loadReferralLink(),this.loadReferralRecords(),this.loadWithdrawalInfo(),this.loadWithdrawalHistory(),this.loadCommissionConfig()]);case 4:t.next=10;break;case 6:t.prev=6,t.t0=t["catch"](0),this.$message.error("加载数据失败，请刷新重试");case 10:return t.prev=10,this.loading=!1,t.finish(10);case 13:case"end":return t.stop()}}),t,this,[[0,6,10,13]])})));function e(){return t.apply(this,arguments)}return e}(),loadReferralStats:function(){var t=x(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["o"])();case 3:e=t.sent,e.success&&(this.referralStats=e.result||{}),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadReferralLink:function(){var t=x(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,this.referralLink){t.next=4;break}return t.next=4,this.handleGenerateLink();case 4:t.next=9;break;case 6:t.prev=6,t.t0=t["catch"](0);case 9:case"end":return t.stop()}}),t,this,[[0,6]])})));function e(){return t.apply(this,arguments)}return e}(),loadReferralRecords:function(){var t=x(s.a.mark((function t(){var e,a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.recordLoading=!0,e=g({current:this.recordPagination.current,size:this.recordPagination.pageSize},this.recordFilters),t.next=5,Object(c["n"])(e);case 5:a=t.sent,a.success&&(this.referralRecords=a.result&&a.result.records||[],this.recordPagination.total=a.result&&a.result.total||0),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),this.referralRecords=[];case 13:return t.prev=13,this.recordLoading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,this,[[0,9,13,16]])})));function e(){return t.apply(this,arguments)}return e}(),loadCommissionConfig:function(){var t=x(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/api/user/commission-config");case 3:e=t.sent,e.data&&e.data.success&&(this.commissionConfig=e.data.result||{},this.calculateCurrentCommissionRate()),t.next=12;break;case 7:t.prev=7,t.t0=t["catch"](0),this.commissionConfig={userType:"NORMAL",commissionLevel:1,inviteCount:0},this.calculateCurrentCommissionRate();case 12:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),calculateCurrentCommissionRate:function(){var t=this.commissionConfig,e=t.userType,a=t.inviteCount;this.currentCommissionRate="SVIP"===e?this.svipRate:"VIP"===e?a>=30?this.vipTopRate:a>=10?this.vipHighRate:this.vipRate:a>=30?this.normalTopRate:a>=10?this.normalHighRate:this.normalRate},loadWithdrawalInfo:function(){var t=x(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/api/usercenter/withdrawalInfo");case 3:e=t.sent,e.data.success&&(this.withdrawalInfo=e.data.result),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadWithdrawalHistory:function(){var t=x(s.a.mark((function t(){var e,a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.withdrawalHistoryLoading=!0,e={current:this.withdrawalPagination.current,size:this.withdrawalPagination.pageSize},t.next=5,this.$http.get("/api/usercenter/withdrawalHistory",{params:e});case 5:a=t.sent,a&&a.success?(this.withdrawalHistory=a.result&&a.result.records||[],this.withdrawalPagination.total=a.result&&a.result.total||0):this.withdrawalHistory=[],t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0);case 12:return t.prev=12,this.withdrawalHistoryLoading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,9,12,15]])})));function e(){return t.apply(this,arguments)}return e}(),handleGenerateLink:function(){var t=x(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.linkLoading=!0,t.next=4,Object(c["g"])();case 4:e=t.sent,e.success&&(this.referralLink=e.result&&e.result.link||"",this.$message.success("推荐链接生成成功")),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),this.$message.error("生成链接失败，请重试");case 12:return t.prev=12,this.linkLoading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function e(){return t.apply(this,arguments)}return e}(),handleCopyLink:function(){var t=this;this.referralLink?navigator.clipboard?navigator.clipboard.writeText(this.referralLink).then((function(){t.$message.success("推荐链接已复制到剪贴板")})).catch((function(){t.fallbackCopyTextToClipboard(t.referralLink)})):this.fallbackCopyTextToClipboard(this.referralLink):this.$message.warning("请先生成推荐链接")},fallbackCopyTextToClipboard:function(t){var e=document.createElement("textarea");e.value=t,e.style.position="fixed",e.style.left="-999999px",e.style.top="-999999px",document.body.appendChild(e),e.focus(),e.select();try{document.execCommand("copy"),this.$message.success("推荐链接已复制到剪贴板")}catch(a){this.$message.error("复制失败，请手动复制")}document.body.removeChild(e)},handleGenerateQRCode:function(){var t=this;this.referralLink?(this.showQRModal=!0,this.$nextTick((function(){t.renderQRCode()}))):this.$message.warning("请先生成推荐链接")},renderQRCode:function(){},handleDownloadQR:function(){this.$message.info("下载二维码功能开发中...")},handleShareToSocial:function(){this.$message.info("分享到社交媒体功能开发中...")},setMaxAmount:function(){this.withdrawalAmount=this.withdrawalInfo.availableAmount},showConfirmModal:function(){if(this.isFormValid)if(/^[\u4e00-\u9fa5]{2,4}$/.test(this.realName.trim())){var t=/^1[3-9]\d{9}$/;t.test(this.alipayAccount)?(this.showConfirmWithdrawal=!0,this.confirmChecked=!1):this.$message.error("请输入正确的手机号")}else this.$message.error("请输入正确的中文姓名（2-4个汉字）");else this.$message.warning("请完善提现信息")},handleConfirmWithdrawal:function(){var t=x(s.a.mark((function t(){var e,a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.withdrawalLoading=!0,e={withdrawalAmount:this.withdrawalAmount,realName:this.realName.trim(),alipayAccount:this.alipayAccount.trim()},t.next=5,this.$http.post("/api/usercenter/applyWithdrawal",e);case 5:if(a=t.sent,!a.data.success){t.next=16;break}return this.$message.success("提现申请提交成功，请等待审核"),this.showConfirmWithdrawal=!1,this.withdrawalAmount=50,this.realName="",this.alipayAccount="",t.next=14,Promise.all([this.loadWithdrawalInfo(),this.loadWithdrawalHistory()]);case 14:t.next=17;break;case 16:this.$message.error(a.data.message||"提现申请失败");case 17:t.next=23;break;case 19:t.prev=19,t.t0=t["catch"](0),this.$message.error("提现申请失败，请重试");case 23:return t.prev=23,this.withdrawalLoading=!1,t.finish(23);case 26:case"end":return t.stop()}}),t,this,[[0,19,23,26]])})));function e(){return t.apply(this,arguments)}return e}(),handleRecordFilterChange:function(){this.recordPagination.current=1,this.loadReferralRecords()},handleRecordTableChange:function(t){var e=t.pagination;this.recordPagination=g(g({},this.recordPagination),e),this.loadReferralRecords()},handleWithdrawalTableChange:function(t){var e=t.pagination;this.withdrawalPagination=g(g({},this.withdrawalPagination),e),this.loadWithdrawalHistory()},maskEmail:function(t){if(!t)return"";var e=t.split("@"),a=l(e,2),n=a[0],i=a[1];return n.length<=3?"".concat(n[0],"***@").concat(i):"".concat(n.substring(0,3),"***@").concat(i)},getRecordStatusClass:function(t){var e={pending:"status-pending",confirmed:"status-confirmed",rewarded:"status-rewarded"};return e[t]||""},getRecordStatusText:function(t){var e={pending:"待确认",confirmed:"已确认",rewarded:"已奖励"};return e[t]||"未知状态"},getWithdrawalStatusColor:function(t){var e={1:"orange",2:"green",3:"red"};return e[t]||"default"},getWithdrawalStatusText:function(t,e){var a={1:"待审核",2:"已发放",3:"审核拒绝"},n=a[t]||"未知状态";return 3===t&&e&&(n+="（".concat(e,"）")),n},formatNumber:function(t){return t?parseFloat(t).toFixed(2):"0.00"},formatDateTime:function(t){if(!t)return"-";try{var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}}}},y=w,C=(a("4318"),a("2877")),k=Object(C["a"])(y,n,i,!1,null,"350f49e2",null);e["default"]=k.exports},b3db:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"credits-page"},[t._m(0),a("div",{staticClass:"credits-content"},[a("div",{staticClass:"balance-overview"},[a("div",{staticClass:"balance-cards"},[a("StatsCard",{attrs:{value:t.balanceData.currentBalance,unit:"元",label:"当前余额",icon:"anticon anticon-wallet","icon-color":"#10b981",trend:t.balanceTrend,loading:t.loading},on:{click:t.handleQuickRecharge}}),a("StatsCard",{attrs:{value:t.balanceData.totalRecharge,unit:"元",label:"累计充值",icon:"anticon anticon-plus-circle","icon-color":"#7c8aed",loading:t.loading}}),a("StatsCard",{attrs:{value:t.balanceData.totalConsumption,unit:"元",label:"累计消费",icon:"anticon anticon-minus-circle","icon-color":"#ef4444",loading:t.loading}}),a("StatsCard",{attrs:{value:t.balanceData.monthlyConsumption,unit:"元",label:"本月消费",icon:"anticon anticon-bar-chart","icon-color":"#f59e0b",trend:t.monthlyTrend,loading:t.loading}})],1),a("div",{staticClass:"quick-recharge"},[a("h3",{staticClass:"section-title"},[t._v("快速充值")]),a("div",{staticClass:"recharge-options"},t._l(t.rechargeOptions,(function(e){return a("div",{key:e.amount,staticClass:"recharge-option",class:{selected:t.selectedAmount===e.amount},on:{click:function(a){return t.selectRechargeAmount(e.amount)}}},[a("div",{staticClass:"option-amount"},[t._v("¥"+t._s(e.amount))])])})),0),a("div",{staticClass:"custom-amount"},[a("a-input-number",{staticStyle:{flex:"1"},attrs:{min:.01,max:1e4,step:.01,placeholder:"自定义金额（最低0.01元）",size:"large"},on:{change:t.onCustomAmountChange},model:{value:t.customAmount,callback:function(e){t.customAmount=e},expression:"customAmount"}}),a("span",{staticClass:"currency"},[t._v("元")])],1),a("div",{staticClass:"recharge-action"},[a("div",{staticClass:"recharge-amount-display"},[a("span",{staticClass:"amount-label"},[t._v("充值金额：")]),a("span",{staticClass:"amount-value",class:{"no-amount":t.finalRechargeAmount<=0}},[t._v("\n              "+t._s(t.finalRechargeAmount>0?"¥"+t.finalRechargeAmount:"请选择充值金额")+"\n            ")])]),a("a-button",{attrs:{type:"primary",size:"large",loading:t.rechargeLoading,disabled:t.finalRechargeAmount<=0},on:{click:t.handleRecharge}},[t._v("\n            立即充值\n          ")])],1)])]),a("DataTable",{ref:"transactionTable",attrs:{title:"交易记录","data-source":t.transactionList,columns:t.transactionColumns,loading:t.transactionLoading,pagination:t.pagination,"show-action-column":!1,"type-options":t.transactionTypeOptions,"status-options":[],"show-search":!0,"type-filter-placeholder":"交易类型","status-filter-placeholder":"交易状态","search-placeholder":"搜索交易描述","date-filter-placeholder":["交易时间","交易时间"]},on:{"filter-change":t.handleFilterChange,"table-change":t.handleTableChange,refresh:t.loadTransactionData},scopedSlots:t._u([{key:"actions",fn:function(){return[a("a-button",{staticStyle:{"margin-right":"8px",background:"linear-gradient(135deg, #64748b 0%, #475569 100%)",border:"none","border-radius":"8px","box-shadow":"0 4px 12px rgba(100, 116, 139, 0.3)",color:"white"},on:{click:t.handleResetFilters}},[a("a-icon",{staticStyle:{"margin-right":"6px"},attrs:{type:"reload"}}),t._v("\n          重置\n        ")],1),a("a-button",{staticStyle:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none","border-radius":"8px","box-shadow":"0 4px 12px rgba(102, 126, 234, 0.3)"},attrs:{type:"primary"},on:{click:t.handleExportTransactions}},[a("a-icon",{staticStyle:{"margin-right":"6px"},attrs:{type:"download"}}),t._v("\n          导出交易记录\n        ")],1)]},proxy:!0}])})],1),a("a-modal",{attrs:{title:"确认充值",footer:null,width:"500px"},model:{value:t.showRechargeModal,callback:function(e){t.showRechargeModal=e},expression:"showRechargeModal"}},[a("div",{staticClass:"recharge-confirm"},[a("div",{staticClass:"confirm-info"},[a("div",{staticClass:"info-row"},[a("span",{staticClass:"info-label"},[t._v("充值金额：")]),a("span",{staticClass:"info-value"},[t._v("¥"+t._s(t.finalRechargeAmount))])]),a("div",{staticClass:"info-row total"},[a("span",{staticClass:"info-label"},[t._v("到账金额：")]),a("span",{staticClass:"info-value"},[t._v("¥"+t._s(t.finalRechargeAmount))])])]),a("div",{staticClass:"payment-methods"},[a("h4",[t._v("支付方式")]),a("a-radio-group",{attrs:{size:"large"},model:{value:t.selectedPaymentMethod,callback:function(e){t.selectedPaymentMethod=e},expression:"selectedPaymentMethod"}},[a("a-radio-button",{attrs:{value:"alipay-page"}},[a("i",{staticClass:"anticon anticon-alipay"}),t._v("\n            支付宝网页\n          ")])],1)],1),a("div",{staticClass:"modal-actions"},[a("a-button",{on:{click:function(e){t.showRechargeModal=!1}}},[t._v("取消")]),a("a-button",{attrs:{type:"primary",loading:t.paymentLoading},on:{click:t.handleConfirmRecharge}},[t._v("\n          确认支付\n        ")])],1)])]),a("a-modal",{attrs:{title:"交易详情",footer:null,width:"600px"},model:{value:t.showTransactionDetail,callback:function(e){t.showTransactionDetail=e},expression:"showTransactionDetail"}},[t.selectedTransaction?a("div",{staticClass:"transaction-detail"},[a("div",{staticClass:"detail-header"},[a("div",{staticClass:"transaction-type",class:t.getTransactionTypeClass(t.selectedTransaction.transactionType)},[a("i",{class:t.getTransactionTypeIcon(t.selectedTransaction.transactionType)}),t._v("\n          "+t._s(t.getTransactionTypeText(t.selectedTransaction.transactionType))+"\n        ")]),a("div",{staticClass:"transaction-amount",class:t.getAmountClass(t.selectedTransaction.transactionType)},[t._v("\n          "+t._s(t.formatAmount(t.selectedTransaction.amount,t.selectedTransaction.transactionType))+"\n        ")])]),a("div",{staticClass:"detail-content"},[a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("交易单号：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.selectedTransaction.id))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("交易时间：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.formatDateTime(t.selectedTransaction.transactionTime)))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("交易描述：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.selectedTransaction.description))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("交易前余额：")]),a("span",{staticClass:"detail-value"},[t._v("¥"+t._s(t.formatNumber(t.selectedTransaction.balanceBefore)))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("交易后余额：")]),a("span",{staticClass:"detail-value"},[t._v("¥"+t._s(t.formatNumber(t.selectedTransaction.balanceAfter)))])]),t.selectedTransaction.relatedOrderId?a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("关联订单：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.selectedTransaction.relatedOrderId))])]):t._e()])]):t._e()])],1)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-header"},[a("h1",{staticClass:"page-title"},[t._v("账户管理")]),a("p",{staticClass:"page-description"},[t._v("管理您的账户余额、查看交易记录和充值")])])}],r=a("a34a"),s=a.n(r),o=a("1189"),c=a("4dbe"),l=a("77ea"),d=a("36d5f");function u(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function p(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?u(Object(a),!0).forEach((function(e){m(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function h(t,e,a,n,i,r,s){try{var o=t[r](s),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(n,i)}function f(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function s(t){h(r,n,i,s,o,"next",t)}function o(t){h(r,n,i,s,o,"throw",t)}s(void 0)}))}}function m(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var g={name:"UserCenterCredits",mixins:[d["a"]],components:{StatsCard:o["default"],DataTable:c["default"]},data:function(){var t=this.$createElement;return{loading:!0,transactionLoading:!1,chartLoading:!1,rechargeLoading:!1,paymentLoading:!1,balanceData:{currentBalance:0,totalRecharge:0,totalConsumption:0,monthlyConsumption:0},rechargeOptions:[{amount:50},{amount:100},{amount:300},{amount:500},{amount:1e3}],selectedAmount:0,customAmount:null,showRechargeModal:!1,selectedPaymentMethod:"alipay-page",transactionList:[],pagination:{current:1,pageSize:10,total:0},filters:{},transactionColumns:[{title:"交易时间",dataIndex:"transactionTime",key:"transactionTime",width:180,align:"center",customRender:function(t){if(!t)return"-";try{var e;return e=new Date(t),e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}}},{title:"交易类型",dataIndex:"transactionType",key:"transactionType",width:120,align:"center",customRender:function(t){var e,a=(e={1:"消费",2:"充值",3:"退款",4:"奖励",5:"会员订阅"},m(e,"1","消费"),m(e,"2","充值"),m(e,"3","退款"),m(e,"4","奖励"),m(e,"5","会员订阅"),e),n=a[t]||t||"-";return n}},{title:"订单号",dataIndex:"relatedOrderId",key:"relatedOrderId",width:200,align:"center",customRender:function(t){return t||"-"}},{title:"交易描述",dataIndex:"description",key:"description",align:"center",ellipsis:!0},{title:"交易金额",dataIndex:"amount",key:"amount",width:120,align:"center",customRender:function(e,a){var n=parseFloat(e||0).toFixed(2),i=[2,3,4].includes(Number(a.transactionType)),r=i?"+":"-",s=i?"color: #52c41a; font-weight: 600;":"color: #ff4d4f; font-weight: 600;";return t("span",{style:s},[r,"¥",n])}},{title:"余额",dataIndex:"balanceAfter",key:"balanceAfter",width:120,align:"center",customRender:function(e){var a=parseFloat(e||0).toFixed(2),n="color: #1890ff; font-weight: 600;";return t("span",{style:n},["¥",a])}},{title:"状态",dataIndex:"orderStatus",key:"orderStatus",width:100,align:"center",customRender:function(e){if(void 0!==e&&null!==e){var a,n=(a={1:{text:"待支付",style:"color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"},2:{text:"已支付",style:"color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"},3:{text:"已完成",style:"color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"},4:{text:"已取消",style:"color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"},5:{text:"已退款",style:"color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"}},m(a,"1",{text:"待支付",style:"color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"}),m(a,"2",{text:"已支付",style:"color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"}),m(a,"3",{text:"已完成",style:"color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"}),m(a,"4",{text:"已取消",style:"color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"}),m(a,"5",{text:"已退款",style:"color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"}),a),i=n[e]||{text:"已完成",style:"color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"};return t("span",{style:i.style},[i.text])}var r="color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;";return t("span",{style:r},["已完成"])}}],transactionTypeOptions:[{value:1,label:"消费"},{value:2,label:"充值"},{value:3,label:"退款"},{value:4,label:"奖励"},{value:5,label:"会员订阅"}],showTransactionDetail:!1,selectedTransaction:null}},computed:{balanceTrend:function(){return null},monthlyTrend:function(){return null},finalRechargeAmount:function(){return this.selectedAmount||this.customAmount||0}},mounted:function(){var t=f(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.loadData();case 2:this.checkPaymentSuccess();case 3:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{loadData:function(){var t=f(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,Promise.all([this.loadBalanceData(),this.loadTransactionData()]);case 4:t.next=10;break;case 6:t.prev=6,t.t0=t["catch"](0),this.$message.error("加载数据失败，请刷新重试");case 10:return t.prev=10,this.loading=!1,t.finish(10);case 13:case"end":return t.stop()}}),t,this,[[0,6,10,13]])})));function e(){return t.apply(this,arguments)}return e}(),loadBalanceData:function(){var t=f(s.a.mark((function t(){var e,a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(l["q"])();case 3:e=t.sent,e.success&&(a=e.result||{},this.balanceData={currentBalance:a.accountBalance||0,totalRecharge:a.totalRecharge||0,totalConsumption:a.totalConsumption||0,monthlyConsumption:a.monthlyConsumption||0,transactionCount:a.transactionCount||0}),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadTransactionData:function(){var t=f(s.a.mark((function t(){var e,a,n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.transactionLoading=!0,e=p({current:this.pagination.current,size:this.pagination.pageSize},this.filters),this.filters.dateRange&&2===this.filters.dateRange.length&&(e.startDate=this.filters.dateRange[0].format("YYYY-MM-DD"),e.endDate=this.filters.dateRange[1].format("YYYY-MM-DD"),delete e.dateRange),t.next=7,Object(l["p"])(e);case 7:a=t.sent,a.success&&(n=a.result&&a.result.records||[],n.length,this.transactionList=n,this.pagination.total=a.result&&a.result.total||0),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](0),this.transactionList=[];case 15:return t.prev=15,this.transactionLoading=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[0,11,15,18]])})));function e(){return t.apply(this,arguments)}return e}(),selectRechargeAmount:function(t){this.selectedAmount=t,this.customAmount=null},onCustomAmountChange:function(t){t&&t>0&&(this.selectedAmount=0)},handleQuickRecharge:function(){this.selectedAmount=100,this.handleRecharge()},handleRecharge:function(){!this.finalRechargeAmount||this.finalRechargeAmount<.01?this.$message.warning("请选择或输入充值金额，最低0.01元"):this.showRechargeModal=!0},handleConfirmRecharge:function(){var t=f(s.a.mark((function t(){var e,a,n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.paymentLoading=!0,e={amount:this.finalRechargeAmount,paymentMethod:this.selectedPaymentMethod},t.next=5,Object(l["c"])(e);case 5:if(a=t.sent,!a.success){t.next=13;break}return n=a.result,t.next=10,this.handleAlipayPagePayment(n.orderId,n.amount);case 10:this.showRechargeModal=!1,t.next=14;break;case 13:this.$message.error(a.message||"创建充值订单失败");case 14:t.next=20;break;case 16:t.prev=16,t.t0=t["catch"](0),this.$message.error("充值失败，请重试");case 20:return t.prev=20,this.paymentLoading=!1,t.finish(20);case 23:case"end":return t.stop()}}),t,this,[[0,16,20,23]])})));function e(){return t.apply(this,arguments)}return e}(),handleAlipayPagePayment:function(){var t=f(s.a.mark((function t(e,a){var n,i,r,o,c;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在跳转到支付宝支付...",0),n={orderId:e,amount:a,subject:"智界Aigc账户充值",body:"充值金额：¥".concat(a)},t.next=7,this.$http.post("/api/alipay/createOrder",n);case 7:if(i=t.sent,this.$message.destroy(),!i.success){t.next=24;break}if(r=i.result.payForm,r){t.next=16;break}return this.$message.error("支付表单为空"),t.abrupt("return");case 16:o=document.createElement("div"),o.innerHTML=r,document.body.appendChild(o),c=o.querySelector("form"),c?c.submit():this.$message.error("支付表单创建失败"),setTimeout((function(){document.body.contains(o)&&document.body.removeChild(o)}),1e3),t.next=26;break;case 24:this.$message.error(i.message||"创建支付订单失败");case 26:t.next=33;break;case 28:t.prev=28,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("支付宝支付失败，请重试");case 33:case"end":return t.stop()}}),t,this,[[0,28]])})));function e(e,a){return t.apply(this,arguments)}return e}(),handleAlipayQrPayment:function(){var t=f(s.a.mark((function t(e,a){var n,i,r;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在生成支付二维码...",0),n={orderId:e,amount:a,subject:"智界Aigc账户充值",body:"充值金额：¥".concat(a)},t.next=7,this.$http.post("/api/alipay/createQrOrder",n);case 7:if(i=t.sent,this.$message.destroy(),!i.success){t.next=19;break}if(r=i.result.qrCode,r){t.next=16;break}return this.$message.error("支付二维码生成失败"),t.abrupt("return");case 16:this.showQrCodeModal(r,e,a),t.next=21;break;case 19:this.$message.error(i.message||"创建扫码支付订单失败");case 21:t.next=28;break;case 23:t.prev=23,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("支付宝扫码支付失败，请重试");case 28:case"end":return t.stop()}}),t,this,[[0,23]])})));function e(e,a){return t.apply(this,arguments)}return e}(),showQrCodeModal:function(t,e,a){var n=this;this.$modal.info({title:"支付宝扫码支付",width:400,content:function(e){return e("div",{style:{textAlign:"center",padding:"20px"}},[e("div",{style:{marginBottom:"16px",fontSize:"16px",fontWeight:"bold"}},"充值金额：¥".concat(a)),e("div",{style:{marginBottom:"16px",color:"#666"}},"请使用支付宝扫描下方二维码完成支付"),e("div",{style:{display:"flex",justifyContent:"center",marginBottom:"16px"}},[e("img",{src:"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=".concat(encodeURIComponent(t)),style:{width:"200px",height:"200px",border:"1px solid #d9d9d9"}})]),e("div",{style:{color:"#999",fontSize:"12px"}},"支付完成后页面将自动跳转")])},onOk:function(){n.checkOrderStatus(e)}}),this.startOrderStatusPolling(e)},startOrderStatusPolling:function(t){var e=this,a=setInterval(f(s.a.mark((function n(){var i;return s.a.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,e.$http.get("/api/alipay/queryOrder?orderId=".concat(t));case 3:i=n.sent,i.success&&"TRADE_SUCCESS"===i.result.tradeStatus&&(clearInterval(a),e.$modal.destroyAll(),e.$message.success("支付成功！正在更新账户余额..."),setTimeout((function(){e.loadData()}),1e3)),n.next=10;break;case 7:n.prev=7,n.t0=n["catch"](0);case 10:case"end":return n.stop()}}),n,null,[[0,7]])}))),3e3);setTimeout((function(){clearInterval(a)}),9e5)},checkOrderStatus:function(){var t=f(s.a.mark((function t(e){var a,n=this;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/api/alipay/queryOrder?orderId=".concat(e));case 3:a=t.sent,a.success&&"TRADE_SUCCESS"===a.result.tradeStatus?(this.$message.success("支付成功！正在更新账户余额..."),setTimeout((function(){n.loadData()}),1e3)):this.$message.info("订单尚未支付，请继续扫码支付"),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.$message.error("查询订单状态失败");case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(e){return t.apply(this,arguments)}return e}(),checkPaymentSuccess:function(){var t=this,e=new URLSearchParams(window.location.search),a=e.get("paymentSuccess"),n=e.get("orderId");if("true"===a&&n){this.$message.success("支付成功！正在更新账户余额..."),setTimeout((function(){t.loadData()}),1e3);var i=window.location.pathname;window.history.replaceState({},document.title,i)}},handleFilterChange:function(t){this.filters=t,this.pagination.current=1,this.loadTransactionData()},handleTableChange:function(t){var e=t.pagination;this.pagination=p(p({},this.pagination),e),this.loadTransactionData()},handleResetFilters:function(){this.$refs.transactionTable&&this.$refs.transactionTable.resetFilters(),this.filters={},this.pagination.current=1,this.loadTransactionData(),this.$message.success("筛选条件已重置")},handleExportTransactions:function(){var t=f(s.a.mark((function t(){var e,a,n,i,r,o,c;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在导出交易记录...",0),e={transactionType:this.filters.type,status:this.filters.status,keyword:this.filters.keyword},this.filters.dateRange&&2===this.filters.dateRange.length&&(e.startDate=this.filters.dateRange[0].format("YYYY-MM-DD"),e.endDate=this.filters.dateRange[1].format("YYYY-MM-DD")),t.next=7,Object(l["e"])(e);case 7:a=t.sent,this.$message.destroy(),n=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),i=window.URL.createObjectURL(n),r=document.createElement("a"),r.href=i,o=new Date,c=o.getFullYear()+String(o.getMonth()+1).padStart(2,"0")+String(o.getDate()).padStart(2,"0")+"_"+String(o.getHours()).padStart(2,"0")+String(o.getMinutes()).padStart(2,"0"),r.download="交易记录_".concat(c,".xlsx"),document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(i),this.$message.success("交易记录导出成功！"),t.next=28;break;case 23:t.prev=23,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("导出失败，请重试");case 28:case"end":return t.stop()}}),t,this,[[0,23]])})));function e(){return t.apply(this,arguments)}return e}(),handleViewTransactionDetail:function(t){this.selectedTransaction=t,this.showTransactionDetail=!0},getTransactionTypeClass:function(t){var e={1:"type-consume",2:"type-recharge",3:"type-refund",4:"type-reward"};return e[t]||""},getTransactionTypeIcon:function(t){var e={1:"anticon anticon-minus-circle",2:"anticon anticon-plus-circle",3:"anticon anticon-undo",4:"anticon anticon-gift"};return e[t]||"anticon anticon-question-circle"},getTransactionTypeText:function(t){var e={1:"消费",2:"充值",3:"退款",4:"奖励"};return e[t]||"未知"},getAmountClass:function(t){return{"amount-positive":[2,3,4].includes(t),"amount-negative":1===t}},formatAmount:function(t,e){var a=[2,3,4].includes(e)?"+":"-";return"".concat(a,"¥").concat(this.formatNumber(Math.abs(t)))},formatNumber:function(t){return t?parseFloat(t).toFixed(2):"0.00"},formatDateTime:function(t){if(!t)return"-";try{var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}}}},v=g,b=(a("6187"),a("2877")),x=Object(b["a"])(v,n,i,!1,null,"2cb5f0cc",null);e["default"]=x.exports},c6c6:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"profile-page"},[t._m(0),a("div",{staticClass:"profile-content"},[a("div",{staticClass:"settings-section"},[a("h2",{staticClass:"section-title"},[t._v("基本信息")]),a("div",{staticClass:"settings-card"},[a("a-form",{attrs:{form:t.basicForm,layout:"vertical"},on:{submit:t.handleBasicInfoSubmit}},[a("div",{staticClass:"form-row"},[a("a-form-item",{staticClass:"avatar-item",attrs:{label:"头像"}},[a("j-image-upload",{staticClass:"avatar-uploader",attrs:{text:"更换头像",bizPath:"avatar"},on:{change:t.handleAvatarChange},model:{value:t.userInfo.avatar,callback:function(e){t.$set(t.userInfo,"avatar",e)},expression:"userInfo.avatar"}})],1)],1),a("div",{staticClass:"form-row"},[a("a-form-item",{staticClass:"form-item",attrs:{label:"昵称"}},[a("div",{staticClass:"nickname-input-wrapper"},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["nickname",{initialValue:t.userInfo&&t.userInfo.nickname||"",rules:[{required:!0,message:"请输入昵称"},{min:2,message:"昵称长度不能少于2个字符"},{max:20,message:"昵称长度不能超过20个字符"},{pattern:/^[\u4e00-\u9fa5a-zA-Z0-9_]+$/,message:"昵称只能包含中文、英文、数字和下划线"}]}],expression:"['nickname', {\n                    initialValue: (userInfo && userInfo.nickname) || '',\n                    rules: [\n                      { required: true, message: '请输入昵称' },\n                      { min: 2, message: '昵称长度不能少于2个字符' },\n                      { max: 20, message: '昵称长度不能超过20个字符' },\n                      { pattern: /^[\\u4e00-\\u9fa5a-zA-Z0-9_]+$/, message: '昵称只能包含中文、英文、数字和下划线' }\n                    ]\n                  }]"}],key:"nickname-input-stable",ref:"nicknameInput",attrs:{placeholder:"请输入昵称（2-20个字符）",size:"large"},on:{input:t.handleNicknameInput,blur:t.handleNicknameBlur}}),t.nicknameStatusIcon?a("div",{staticClass:"nickname-status-icon"},[a("a-icon",{style:{color:t.nicknameStatusColor},attrs:{type:t.nicknameStatusIcon,spin:t.nicknameValidating}})],1):t._e()],1),t.nicknameStatusText?a("div",{staticClass:"nickname-validation-tip",class:{success:!0===t.nicknameValidation.valid,error:!1===t.nicknameValidation.valid,validating:t.nicknameValidating}},[a("a-icon",{attrs:{type:t.nicknameStatusIcon,spin:t.nicknameValidating}}),a("span",[t._v(t._s(t.nicknameStatusText))])],1):t._e(),t.nicknameValidating||null!==t.nicknameValidation.valid?t._e():a("div",{staticClass:"nickname-hint"},[a("a-icon",{staticStyle:{color:"#1890ff","margin-right":"4px"},attrs:{type:"info-circle"}}),a("span",[t._v("昵称将在失去焦点时自动校验")])],1)]),a("a-form-item",{staticClass:"form-item",attrs:{label:"邮箱"}},[a("div",{staticClass:"input-with-button"},[a("div",{staticClass:"email-input-wrapper"},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["email",{initialValue:t.userInfo&&t.userInfo.email||"",rules:[{required:!0,message:"请输入邮箱"},{type:"email",message:"请输入有效的邮箱地址"}]}],expression:"['email', {\n                      initialValue: (userInfo && userInfo.email) || '',\n                      rules: [\n                        { required: true, message: '请输入邮箱' },\n                        { type: 'email', message: '请输入有效的邮箱地址' }\n                      ]\n                    }]"}],key:"email-input-stable",ref:"emailInput",attrs:{placeholder:"请输入邮箱",size:"large",disabled:!t.emailEditable},on:{input:t.handleFormChange,blur:t.handleEmailBlur}}),t.emailStatusIcon?a("div",{staticClass:"email-status-icon"},[a("a-icon",{style:{color:t.emailStatusColor},attrs:{type:t.emailStatusIcon,spin:t.emailValidating}})],1):t._e()],1),t.emailStatusText?a("div",{staticClass:"verify-tip-inline",class:{success:!0===t.emailValidation.valid,error:!1===t.emailValidation.valid,validating:t.emailValidating}},[a("a-icon",{staticStyle:{"margin-right":"4px"},attrs:{type:t.emailStatusIcon,spin:t.emailValidating}}),a("span",[t._v(t._s(t.emailStatusText))])],1):t._e(),t.isValidEmail?a("a-button",{staticClass:"verify-button",attrs:{type:"primary",size:"large",loading:"sending"===t.emailVerificationState,disabled:t.emailCountdown>0||"unlocked"===t.emailVerificationState},on:{click:t.handleSendEmailCode}},[t._v("\n                  "+t._s(t.emailButtonText)+"\n                ")]):t._e()],1),"verifying"===t.emailVerificationState?a("div",{staticClass:"verify-code-input"},[a("a-input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请输入6位验证码",size:"large",maxlength:"6"},model:{value:t.emailVerifyCode,callback:function(e){t.emailVerifyCode=e},expression:"emailVerifyCode"}}),a("a-button",{attrs:{type:"primary",size:"large",disabled:!t.emailVerifyCode||6!==t.emailVerifyCode.length},on:{click:t.handleVerifyEmailCode}},[t._v("\n                  验证\n                ")])],1):t._e(),t.isValidEmail&&"unlocked"!==t.emailVerificationState?a("div",{staticClass:"verify-tip-inline"},[a("a-icon",{staticStyle:{color:"#1890ff","margin-right":"4px"},attrs:{type:"info-circle"}}),!1===t.emailValidation.valid?a("span",[t._v(t._s(t.emailValidation.message)+"，需要通过邮箱验证码验证后才能修改")]):t.emailValidating?a("span",[t._v("正在校验邮箱，请稍候...")]):a("span",[t._v("当前邮箱已绑定，需要通过邮箱验证码验证后才能修改")])],1):t._e()])],1),a("div",{staticClass:"form-row"},[a("a-form-item",{staticClass:"form-item",attrs:{label:"手机号"}},[a("div",{staticClass:"input-with-button"},[a("div",{staticClass:"phone-input-wrapper"},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{initialValue:t.userInfo&&t.userInfo.phone||"",rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号"},{pattern:/^\d+$/,message:"手机号只能包含数字"}]}],expression:"['phone', {\n                      initialValue: (userInfo && userInfo.phone) || '',\n                      rules: [\n                        { required: true, message: '请输入手机号' },\n                        { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号' },\n                        { pattern: /^\\d+$/, message: '手机号只能包含数字' }\n                      ]\n                    }]"}],key:"phone-input-stable",ref:"phoneInput",attrs:{placeholder:"请输入手机号",size:"large",disabled:!t.phoneEditable},on:{input:t.handleFormChange,blur:t.handlePhoneBlur}}),t.phoneStatusIcon?a("div",{staticClass:"phone-status-icon"},[a("a-icon",{style:{color:t.phoneStatusColor},attrs:{type:t.phoneStatusIcon,spin:t.phoneValidating}})],1):t._e()],1),t.isValidPhone?a("a-button",{staticClass:"verify-button",attrs:{type:"primary",size:"large",loading:"sending"===t.phoneVerificationState,disabled:t.phoneCountdown>0||"unlocked"===t.phoneVerificationState},on:{click:t.handleSendPhoneCode}},[t._v("\n                  "+t._s(t.phoneButtonText)+"\n                ")]):t._e()],1),t.phoneStatusText?a("div",{staticClass:"verify-tip-inline",class:{success:!0===t.phoneValidation.valid,error:!1===t.phoneValidation.valid,validating:t.phoneValidating}},[a("a-icon",{staticStyle:{"margin-right":"4px"},attrs:{type:t.phoneStatusIcon,spin:t.phoneValidating}}),a("span",[t._v(t._s(t.phoneStatusText))])],1):t._e(),"verifying"===t.phoneVerificationState?a("div",{staticClass:"verify-code-input"},[a("a-input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请输入6位验证码",size:"large",maxlength:"6"},model:{value:t.phoneVerifyCode,callback:function(e){t.phoneVerifyCode=e},expression:"phoneVerifyCode"}}),a("a-button",{attrs:{type:"primary",size:"large",disabled:!t.phoneVerifyCode||6!==t.phoneVerifyCode.length},on:{click:t.handleVerifyPhoneCode}},[t._v("\n                  验证\n                ")])],1):t._e(),t.isValidPhone&&"unlocked"!==t.phoneVerificationState?a("div",{staticClass:"verify-tip-inline"},[a("a-icon",{staticStyle:{color:"#1890ff","margin-right":"4px"},attrs:{type:"info-circle"}}),!1===t.phoneValidation.valid?a("span",[t._v(t._s(t.phoneValidation.message)+"，需要通过短信验证码验证后才能修改")]):t.phoneValidating?a("span",[t._v("正在校验手机号，请稍候...")]):a("span",[t._v("当前手机号已绑定，需要通过短信验证码验证后才能修改")])],1):t._e()]),a("a-form-item",{staticClass:"form-item",attrs:{label:"注册时间"}},[a("a-input",{attrs:{value:t.formatDate(t.userInfo&&t.userInfo.createTime||null),disabled:"",size:"large"}})],1)],1),a("div",{staticClass:"form-actions"},[a("div",{staticClass:"button-group"},[a("a-button",{attrs:{type:"primary","html-type":"submit",loading:t.basicLoading,disabled:!t.canSaveBasicInfo,size:"large"}},[t._v("\n                保存基本信息\n              ")]),t.hasChanges?a("a-button",{staticStyle:{"margin-left":"12px"},attrs:{size:"large"},on:{click:t.handleResetChanges}},[t._v("\n                还原修改\n              ")]):t._e()],1),t.hasChanges&&!t.canSaveBasicInfo?a("div",{staticClass:"save-disabled-tip"},[a("a-icon",{staticStyle:{color:"#faad14","margin-right":"4px"},attrs:{type:"exclamation-circle"}}),t.nicknameValidating||t.phoneValidating||t.emailValidating?a("span",[t._v("\n                请等待校验完成（\n                "),t.nicknameValidating?a("span",[t._v("昵称")]):t._e(),t.phoneValidating?a("span",[t._v(t._s(t.nicknameValidating?"、":"")+"手机号")]):t._e(),t.emailValidating?a("span",[t._v(t._s(t.nicknameValidating||t.phoneValidating?"、":"")+"邮箱")]):t._e(),t._v("\n                ）\n              ")]):!1===t.nicknameValidation.valid||!1===t.phoneValidation.valid||!1===t.emailValidation.valid?a("span",[t._v("\n                校验失败，请修改后重试（\n                "),!1===t.nicknameValidation.valid?a("span",[t._v("昵称")]):t._e(),!1===t.phoneValidation.valid?a("span",[t._v(t._s(!1===t.nicknameValidation.valid?"、":"")+"手机号")]):t._e(),!1===t.emailValidation.valid?a("span",[t._v(t._s(!1===t.nicknameValidation.valid||!1===t.phoneValidation.valid?"、":"")+"邮箱")]):t._e(),t._v("\n                ）\n              ")]):a("span",[t._v("请完成校验后再保存")])],1):t._e()])])],1)]),a("div",{staticClass:"settings-section"},[a("h2",{staticClass:"section-title"},[t._v("安全设置")]),a("div",{staticClass:"settings-card"},[a("div",{staticClass:"security-item"},[a("div",{staticClass:"security-info"},[a("h3",[t._v("API 密钥")]),a("p",[t._v("用于调用智界AIGC的API服务")]),a("div",{staticClass:"api-key-display"},[a("span",{staticClass:"api-key"},[t._v(t._s(t.maskedApiKey))]),a("a-button",{attrs:{type:"link",disabled:!(t.userInfo&&t.userInfo.apiKey)},on:{click:t.handleCopyApiKey}},[a("i",{staticClass:"anticon anticon-copy"}),t._v("\n                复制\n              ")])],1)]),a("a-button",{attrs:{loading:t.apiKeyLoading},on:{click:t.showApiKeyResetConfirm}},[t._v("\n            重新生成\n          ")])],1),a("div",{staticClass:"security-item"},[t._m(1),a("a-button",{on:{click:function(e){t.showChangePasswordDialog=!0}}},[t._v("修改密码")])],1),a("div",{staticClass:"security-item"},[t._m(2),a("a-button",{attrs:{type:"danger"},on:{click:t.handleLogout}},[t._v("退出登录")])],1)])])]),a("LogoutConfirmDialog",{attrs:{visible:t.showChangePasswordDialog,title:t.passwordDialogTitle,confirmText:t.passwordDialogConfirmText,cancelText:"取消",loading:t.passwordLoading,loadingText:t.passwordDialogLoadingText,dialogType:"changePassword",isFirstTimePassword:!t.userInfo.passwordChanged},on:{confirm:t.handlePasswordDialogConfirm,cancel:t.handlePasswordDialogCancel,"update:visible":function(e){t.showChangePasswordDialog=e}}}),a("LogoutConfirmDialog",{attrs:{visible:t.showLogoutDialog,title:"确认退出登录",confirmText:"确认退出",cancelText:"取消",loading:t.logoutLoading,loadingText:"退出中..."},on:{confirm:t.handleLogoutConfirm,cancel:t.handleLogoutCancel,"update:visible":function(e){t.showLogoutDialog=e}}}),a("ConfirmDialog",{attrs:{visible:t.showApiKeyConfirmDialog,title:"确认重置API Key",type:"warning",loading:t.apiKeyLoading,"confirm-text":"确定重置","loading-text":"重置中..."},on:{"update:visible":function(e){t.showApiKeyConfirmDialog=e},confirm:t.handleRegenerateApiKey,cancel:t.hideApiKeyResetConfirm}},[a("p",[a("strong",[t._v("API Key是您访问系统的唯一凭证，具有唯一性，请妥善保管。")])]),a("p",[t._v("重置后原有的API Key将立即失效，所有使用旧API Key的应用将无法正常工作。")]),a("p",{staticStyle:{color:"#f59e0b","font-weight":"600","margin-top":"1rem"}},[t._v("确定要重置吗？")])])],1)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-header"},[a("h1",{staticClass:"page-title"},[t._v("账户设置")]),a("p",{staticClass:"page-description"},[t._v("管理您的个人信息、安全设置和API密钥")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"security-info"},[a("h3",[t._v("登录密码")]),a("p",[t._v("定期更换密码可以提高账户安全性")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"security-info"},[a("h3",[t._v("退出登录")]),a("p",[t._v("退出当前账户，清除本地登录状态")])])}],r=a("a34a"),s=a.n(r),o=a("77ea"),c=a("36d5f"),l=a("e610"),d=a("886b"),u=a("1741");function p(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function h(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?p(Object(a),!0).forEach((function(e){f(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):p(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function f(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function m(t,e,a,n,i,r,s){try{var o=t[r](s),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(n,i)}function g(t){return function(){var e=this,a=arguments;return new Promise((function(n,i){var r=t.apply(e,a);function s(t){m(r,n,i,s,o,"next",t)}function o(t){m(r,n,i,s,o,"throw",t)}s(void 0)}))}}var v={name:"UserCenterProfile",mixins:[c["a"]],components:{JImageUpload:l["default"],LogoutConfirmDialog:d["default"],ConfirmDialog:u["default"]},props:{userInfo:{type:Object,default:function(){return{}}}},data:function(){return{defaultAvatar:"/default-avatar.png",basicLoading:!1,passwordLoading:!1,apiKeyLoading:!1,originalData:{},hasChanges:!1,showChangePasswordDialog:!1,showLogoutDialog:!1,logoutLoading:!1,logoutDialogContent:"您确定要退出登录吗？退出后需要重新登录才能访问个人中心。",showApiKeyConfirmDialog:!1,phoneVerificationState:"locked",emailVerificationState:"locked",phoneVerifyCode:"",emailVerifyCode:"",phoneCountdown:0,emailCountdown:0,phoneTimer:null,emailTimer:null,nicknameValidating:!1,nicknameValidation:{valid:null,message:"",errorType:""},nicknameDebounceTimer:null,phoneValidating:!1,phoneValidation:{valid:null,message:"",errorType:""},phoneDebounceTimer:null,emailValidating:!1,emailValidation:{valid:null,message:"",errorType:""},emailDebounceTimer:null}},computed:{maskedApiKey:function(){var t=this.userInfo&&this.userInfo.apiKey;return t?t.length>8?"".concat(t.substring(0,4)).concat("*".repeat(t.length-8)).concat(t.substring(t.length-4)):t:"未生成"},passwordDialogTitle:function(){return this.userInfo&&this.userInfo.passwordChanged?"修改密码":"设置密码"},passwordDialogConfirmText:function(){return this.userInfo&&this.userInfo.passwordChanged?"确认修改":"设置密码"},passwordDialogLoadingText:function(){return this.userInfo&&this.userInfo.passwordChanged?"修改中...":"设置中..."},isValidPhone:function(){var t=this.userInfo&&this.userInfo.phone;return t&&/^1[3-9]\d{9}$/.test(t)},isValidEmail:function(){var t=this.userInfo&&this.userInfo.email;return t&&/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(t)},phoneEditable:function(){return!this.isValidPhone||"unlocked"===this.phoneVerificationState},emailEditable:function(){return!this.isValidEmail||"unlocked"===this.emailVerificationState},phoneButtonText:function(){if(!this.isValidPhone)return"";if(this.phoneCountdown>0)return"".concat(this.phoneCountdown,"s后重发");switch(this.phoneVerificationState){case"sending":return"发送中...";case"verifying":return"验证中...";case"unlocked":return"已验证";default:return"发送验证码"}},emailButtonText:function(){if(!this.isValidEmail)return"";if(this.emailCountdown>0)return"".concat(this.emailCountdown,"s后重发");switch(this.emailVerificationState){case"sending":return"发送中...";case"verifying":return"验证中...";case"unlocked":return"已验证";default:return"发送验证码"}},nicknameStatusIcon:function(){return this.nicknameValidating?"loading":!0===this.nicknameValidation.valid?"check-circle":!1===this.nicknameValidation.valid?"close-circle":null},nicknameStatusColor:function(){return this.nicknameValidating?"#1890ff":!0===this.nicknameValidation.valid?"#52c41a":!1===this.nicknameValidation.valid?"#ff4d4f":"#d9d9d9"},nicknameStatusText:function(){return this.nicknameValidating?"校验中...":!0===this.nicknameValidation.valid?"昵称可用":!1===this.nicknameValidation.valid?this.nicknameValidation.message||"昵称不可用":""},phoneStatusIcon:function(){return this.phoneValidating?"loading":!0===this.phoneValidation.valid?"check-circle":!1===this.phoneValidation.valid?"close-circle":null},phoneStatusColor:function(){return this.phoneValidating?"#1890ff":!0===this.phoneValidation.valid?"#52c41a":!1===this.phoneValidation.valid?"#ff4d4f":"#d9d9d9"},phoneStatusText:function(){return this.phoneValidating?"校验中...":!0===this.phoneValidation.valid?"手机号可用":!1===this.phoneValidation.valid?this.phoneValidation.message||"手机号不可用":null},emailStatusIcon:function(){return this.emailValidating?"loading":!0===this.emailValidation.valid?"check-circle":!1===this.emailValidation.valid?"close-circle":null},emailStatusColor:function(){return this.emailValidating?"#1890ff":!0===this.emailValidation.valid?"#52c41a":!1===this.emailValidation.valid?"#ff4d4f":"#d9d9d9"},emailStatusText:function(){return this.emailValidating?"校验中...":!0===this.emailValidation.valid?"邮箱可用":!1===this.emailValidation.valid?this.emailValidation.message||"邮箱不可用":null},canSaveBasicInfo:function(){if(!this.hasChanges)return!1;var t=this.basicForm?this.basicForm.getFieldValue("nickname"):"",e=this.basicForm?this.basicForm.getFieldValue("phone"):"",a=this.basicForm?this.basicForm.getFieldValue("email"):"",n=this.originalData.nickname||"",i=this.originalData.phone||"",r=this.originalData.email||"",s=t!==n,o=e!==i,c=a!==r;if(s){if(this.nicknameValidating)return!1;if(!1===this.nicknameValidation.valid)return!1;if(t&&t.length>=2&&null===this.nicknameValidation.valid)return!1}if(o){if(this.phoneValidating)return!1;if(!1===this.phoneValidation.valid)return!1;if(e&&/^1[3-9]\d{9}$/.test(e)&&null===this.phoneValidation.valid)return!1}if(c){if(this.emailValidating)return!1;if(!1===this.emailValidation.valid)return!1;if(a&&/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(a)&&null===this.emailValidation.valid)return!1}return!0}},watch:{userInfo:{handler:function(t,e){var a=this;!t||0!==Object.keys(this.originalData).length&&this.originalData.nickname||(this.originalData={nickname:t.nickname||"",email:t.email||"",phone:t.phone||"",avatar:t.avatar||""}),e&&t&&e.avatar!==t.avatar&&this.$nextTick((function(){a.checkForChanges()}))},deep:!0,immediate:!0}},beforeCreate:function(){this.basicForm=this.$form.createForm(this)},beforeDestroy:function(){this.clearPhoneTimer(),this.clearEmailTimer(),this.clearNicknameTimer(),this.clearPhoneDebounceTimer(),this.clearEmailDebounceTimer()},created:function(){this.loadDefaultAvatar()},methods:{loadDefaultAvatar:function(){var t=g(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/sys/common/default-avatar-url");case 3:e=t.sent,e&&e.success&&e.result&&(this.defaultAvatar=e.result),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),handleFormChange:function(){var t=this;this.$nextTick((function(){t.checkForChanges()}))},checkForChanges:function(){if(0!==Object.keys(this.originalData).length){var t=this.basicForm.getFieldsValue(),e=this.userInfo.avatar||"";this.hasChanges=t.nickname!==this.originalData.nickname||t.email!==this.originalData.email||t.phone!==this.originalData.phone||e!==this.originalData.avatar}},handleResetChanges:function(){var t=this.originalData,e=t&&(void 0!==t.nickname||void 0!==t.email||void 0!==t.phone);e||(t=this.userInfo||{});var a={nickname:t.nickname||"",email:t.email||"",phone:t.phone||""};this.basicForm.setFieldsValue(a),this.$emit("avatar-updated",t.avatar||""),this.nicknameValidation={valid:null,message:"",errorType:""},this.nicknameValidating=!1,this.clearNicknameTimer(),this.hasChanges=!1,this.$notification.info({message:"已还原修改",description:"所有修改已还原到原始状态。",placement:"topRight",duration:2,style:{width:"320px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})},handleAvatarChange:function(){var t=g(s.a.mark((function t(e){var a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=6,Object(o["F"])(e);case 6:if(a=t.sent,!a.success){t.next=15;break}this.originalData.avatar=e,this.$emit("avatar-updated",e),this.checkForChanges(),this.$notification.success({message:"头像更新成功",description:"您的头像已成功更新并保存。",placement:"topRight",duration:3,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.next=16;break;case 15:throw new Error(a.message||"保存头像到数据库失败");case 16:t.next=23;break;case 18:t.prev=18,t.t0=t["catch"](2),this.$emit("avatar-updated",this.originalData.avatar),this.$notification.error({message:"头像保存失败",description:t.t0.message||"头像保存失败，请重试。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 23:case"end":return t.stop()}}),t,this,[[2,18]])})));function e(e){return t.apply(this,arguments)}return e}(),handleLogout:function(){this.showLogoutDialog=!0},handleLogoutConfirm:function(){var t=g(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.logoutLoading=!0,t.next=4,this.performCompleteLogout();case 4:this.$notification.success({message:"退出成功",description:"您已成功退出登录，即将跳转到首页。",placement:"topRight",duration:3,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),this.showLogoutDialog=!1,setTimeout((function(){window.location.href="/home"}),1500),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),this.$notification.error({message:"退出失败",description:t.t0.message||"退出登录时发生错误，请重试。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 13:return t.prev=13,this.logoutLoading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,this,[[0,9,13,16]])})));function e(){return t.apply(this,arguments)}return e}(),handleLogoutCancel:function(){this.showLogoutDialog=!1},performCompleteLogout:function(){var t=g(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,!this.performLogout){t.next=7;break}return t.next=5,this.performLogout();case 5:t.next=10;break;case 7:return t.next=9,this.$store.dispatch("Logout");case 9:this.clearBasicUserData();case 10:return t.abrupt("return",Promise.resolve());case 14:return t.prev=14,t.t0=t["catch"](0),t.abrupt("return",Promise.reject(t.t0));case 18:case"end":return t.stop()}}),t,this,[[0,14]])})));function e(){return t.apply(this,arguments)}return e}(),clearBasicUserData:function(){try{var t=["Access-Token","USER_INFO","USER_NAME","userRole"];t.forEach((function(t){localStorage.getItem(t)&&localStorage.removeItem(t)})),this.userInfo={},this.originalData={},this.hasChanges=!1}catch(e){}},handleBasicInfoSubmit:function(t){var e=this;t.preventDefault(),this.canSaveBasicInfo?this.nicknameValidating?this.$notification.warning({message:"昵称校验中",description:"请等待昵称校验完成后再保存",placement:"topRight",duration:3}):!1!==this.nicknameValidation.valid?this.basicForm.validateFields(function(){var t=g(s.a.mark((function t(a,n){var i;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a){t.next=28;break}return t.prev=1,e.basicLoading=!0,t.prev=3,t.next=6,Object(o["G"])({email:n.email});case 6:return t.next=8,Object(o["H"])({nickname:n.nickname,phone:n.phone});case 8:t.next=13;break;case 10:throw t.prev=10,t.t0=t["catch"](3),new Error("信息更新失败: ".concat(t.t0.message||"未知错误"));case 13:e.originalData={nickname:n.nickname,email:n.email,phone:n.phone,avatar:e.userInfo.avatar},e.hasChanges=!1,e.$notification.success({message:"基本信息更新成功",description:"您的个人信息已成功更新。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),e.$emit("info-updated",h(h({},n),{},{avatar:e.userInfo.avatar})),t.next=25;break;case 19:t.prev=19,t.t1=t["catch"](1),i="基本信息更新失败，请检查网络后重试。",t.t1.message&&(i=t.t1.message.includes("email")?"邮箱格式不正确或已被其他用户使用。":t.t1.message.includes("phone")?"手机号格式不正确或已被其他用户使用。":t.t1.message.includes("nickname")?"昵称不符合要求或已被其他用户使用。":"更新失败：".concat(t.t1.message)),e.$notification.error({message:"更新失败",description:i,placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 25:return t.prev=25,e.basicLoading=!1,t.finish(25);case 28:case"end":return t.stop()}}),t,null,[[1,19,25,28],[3,10]])})));return function(e,a){return t.apply(this,arguments)}}()):this.$notification.error({message:"昵称校验失败",description:this.nicknameValidation.message||"昵称不符合要求，请修改后重试",placement:"topRight",duration:4}):this.$notification.warning({message:"无法保存",description:"请确保所有信息都已正确填写和校验",placement:"topRight",duration:3})},handleCopyApiKey:function(){var t=this,e=this.userInfo&&this.userInfo.apiKey;e?navigator.clipboard?navigator.clipboard.writeText(e).then((function(){t.$notification.success({message:"API Key已复制",description:"API Key已成功复制到剪贴板，可以粘贴使用了。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})})).catch((function(){t.fallbackCopyTextToClipboard(e)})):this.fallbackCopyTextToClipboard(e):this.$notification.warning({message:"API Key未生成",description:"请先生成API Key后再进行复制操作。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})},fallbackCopyTextToClipboard:function(t){var e=document.createElement("textarea");e.value=t,e.style.position="fixed",e.style.left="-999999px",e.style.top="-999999px",document.body.appendChild(e),e.focus(),e.select();try{var a=document.execCommand("copy");if(!a)throw new Error("execCommand返回false");this.$notification.success({message:"API Key已复制",description:"API Key已成功复制到剪贴板，可以粘贴使用了。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})}catch(n){this.$notification.error({message:"复制失败",description:"无法自动复制到剪贴板，请手动选择并复制API Key。",placement:"topRight",duration:4,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})}document.body.removeChild(e)},showApiKeyResetConfirm:function(){this.showApiKeyConfirmDialog=!0},hideApiKeyResetConfirm:function(){this.showApiKeyConfirmDialog=!1},handleRegenerateApiKey:function(){var t=g(s.a.mark((function t(){var e,a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=1,this.apiKeyLoading=!0,t.next=5,Object(o["B"])();case 5:e=t.sent,e.success?(a=e.result||e.data,this.userInfo&&this.$set(this.userInfo,"apiKey",a),this.$notification.success({message:"API Key重置成功",description:"新的API Key已生成，请妥善保管。旧的API Key已失效。",placement:"topRight",duration:4.5,style:{width:"400px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),this.$emit("api-key-updated",a),this.hideApiKeyResetConfirm()):this.$notification.error({message:"API Key重置失败",description:e.message||"重置操作未能完成，请稍后重试。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](1),this.$notification.error({message:"API Key重新生成失败",description:"网络连接异常或服务器错误，请检查网络后重试。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 13:return t.prev=13,this.apiKeyLoading=!1,t.finish(13);case 17:case"end":return t.stop()}}),t,this,[[1,9,13,17]])})));function e(){return t.apply(this,arguments)}return e}(),handlePasswordDialogConfirm:function(){var t=g(s.a.mark((function t(){var e,a,n,i,r,c,l,d;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,this.passwordLoading=!0,e=document.getElementById("oldPassword"),a=document.getElementById("newPassword"),n=document.getElementById("confirmPassword"),i=e?e.value:"",r=a?a.value:"",c=n?n.value:"",r&&c){t.next=10;break}throw new Error("请填写完整的密码信息");case 10:if(r===c){t.next=12;break}throw new Error("两次输入的密码不一致");case 12:if(!this.userInfo.passwordChanged||i){t.next=14;break}throw new Error("请输入当前密码");case 14:if(!this.userInfo.passwordChanged||!i||r!==i){t.next=16;break}throw new Error("新密码不能与当前密码相同");case 16:if(l=this.validatePasswordStrength(r),l.isValid){t.next=19;break}throw new Error(l.errors.join("，"));case 19:return d={newPassword:r,confirmPassword:c},this.userInfo.passwordChanged&&(d.oldPassword=i),t.next=23,Object(o["a"])(d);case 23:this.$notification.success({message:"密码修改成功",description:"您的登录密码已成功修改，请妥善保管新密码。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),this.showChangePasswordDialog=!1,this.userInfo&&(this.userInfo.passwordChanged=1),this.$emit("password-changed"),this.$emit("refresh-user-info"),t.next=33;break;case 30:t.prev=30,t.t0=t["catch"](0),this.$notification.error({message:"密码修改失败",description:t.t0.message||"修改密码时发生错误，请重试。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 33:return t.prev=33,this.passwordLoading=!1,t.finish(33);case 36:case"end":return t.stop()}}),t,this,[[0,30,33,36]])})));function e(){return t.apply(this,arguments)}return e}(),handlePasswordDialogCancel:function(){this.showChangePasswordDialog=!1},validatePasswordStrength:function(t){var e=[];t.length<8&&e.push("密码长度至少8位");var a=0;return/[A-Z]/.test(t)&&a++,/[a-z]/.test(t)&&a++,/\d/.test(t)&&a++,/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(t)&&a++,a<3&&e.push("至少包含3种字符类型（大写字母、小写字母、数字、特殊字符）"),/(.)\1{3,}/.test(t)&&e.push("密码不能包含连续4个相同字符"),/^123456/.test(t)&&e.push("密码不能以123456开头"),/^password$/i.test(t)&&e.push("密码不能是password"),{isValid:0===e.length,errors:e}},formatDate:function(t){if(!t)return"未知";try{var e=new Date(t);return e.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})}catch(a){return"未知"}},handleSendPhoneCode:function(){var t=g(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.isValidPhone&&!(this.phoneCountdown>0)){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,this.phoneVerificationState="sending",t.next=6,Object(o["D"])(this.userInfo.phone,"changePhone");case 6:if(e=t.sent,!e.success){t.next=13;break}this.phoneVerificationState="verifying",this.startPhoneCountdown(),this.$notification.success({message:"验证码发送成功",description:"验证码已发送到您的手机，请查收。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.next=14;break;case 13:throw new Error(e.message||"发送失败");case 14:t.next=21;break;case 16:t.prev=16,t.t0=t["catch"](2),this.phoneVerificationState="locked",this.$notification.error({message:"验证码发送失败",description:t.t0.message||"发送验证码时发生错误，请重试。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 21:case"end":return t.stop()}}),t,this,[[2,16]])})));function e(){return t.apply(this,arguments)}return e}(),handleSendEmailCode:function(){var t=g(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.isValidEmail&&!(this.emailCountdown>0)){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,this.emailVerificationState="sending",t.next=6,Object(o["C"])(this.userInfo.email,"changeEmail");case 6:if(e=t.sent,!e.success){t.next=13;break}this.emailVerificationState="verifying",this.startEmailCountdown(),this.$notification.success({message:"验证码发送成功",description:"验证码已发送到您的邮箱，请查收。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.next=14;break;case 13:throw new Error(e.message||"发送失败");case 14:t.next=21;break;case 16:t.prev=16,t.t0=t["catch"](2),this.emailVerificationState="locked",this.$notification.error({message:"验证码发送失败",description:t.t0.message||"发送验证码时发生错误，请重试。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 21:case"end":return t.stop()}}),t,this,[[2,16]])})));function e(){return t.apply(this,arguments)}return e}(),handleVerifyPhoneCode:function(){var t=g(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.phoneVerifyCode&&6===this.phoneVerifyCode.length){t.next=3;break}return this.$notification.warning({message:"请输入6位验证码",placement:"topRight",duration:2}),t.abrupt("return");case 3:return t.prev=3,t.next=6,Object(o["K"])(this.userInfo.phone,this.phoneVerifyCode,"sms","changePhone");case 6:if(e=t.sent,!e.success){t.next=14;break}this.phoneVerificationState="unlocked",this.phoneVerifyCode="",this.clearPhoneTimer(),this.$notification.success({message:"验证成功",description:"手机号验证成功，现在可以修改手机号了。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.next=15;break;case 14:throw new Error(e.message||"验证失败");case 15:t.next=21;break;case 17:t.prev=17,t.t0=t["catch"](3),this.$notification.error({message:"验证码错误",description:t.t0.message||"验证码错误或已过期，请重新输入。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 21:case"end":return t.stop()}}),t,this,[[3,17]])})));function e(){return t.apply(this,arguments)}return e}(),handleVerifyEmailCode:function(){var t=g(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.emailVerifyCode&&6===this.emailVerifyCode.length){t.next=3;break}return this.$notification.warning({message:"请输入6位验证码",placement:"topRight",duration:2}),t.abrupt("return");case 3:return t.prev=3,t.next=6,Object(o["K"])(this.userInfo.email,this.emailVerifyCode,"email","changeEmail");case 6:if(e=t.sent,!e.success){t.next=14;break}this.emailVerificationState="unlocked",this.emailVerifyCode="",this.clearEmailTimer(),this.$notification.success({message:"验证成功",description:"邮箱验证成功，现在可以修改邮箱了。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.next=15;break;case 14:throw new Error(e.message||"验证失败");case 15:t.next=21;break;case 17:t.prev=17,t.t0=t["catch"](3),this.$notification.error({message:"验证码错误",description:t.t0.message||"验证码错误或已过期，请重新输入。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 21:case"end":return t.stop()}}),t,this,[[3,17]])})));function e(){return t.apply(this,arguments)}return e}(),startPhoneCountdown:function(){var t=this;this.phoneCountdown=60,this.phoneTimer=setInterval((function(){t.phoneCountdown--,t.phoneCountdown<=0&&t.clearPhoneTimer()}),1e3)},startEmailCountdown:function(){var t=this;this.emailCountdown=60,this.emailTimer=setInterval((function(){t.emailCountdown--,t.emailCountdown<=0&&t.clearEmailTimer()}),1e3)},clearPhoneTimer:function(){this.phoneTimer&&(clearInterval(this.phoneTimer),this.phoneTimer=null),this.phoneCountdown=0},clearEmailTimer:function(){this.emailTimer&&(clearInterval(this.emailTimer),this.emailTimer=null),this.emailCountdown=0},handleNicknameInput:function(){this.nicknameValidation={valid:null,message:"",errorType:""}},handleNicknameBlur:function(t){var e=this,a=t.target.value,n=this.originalData.nickname||"";this.nicknameDebounceTimer&&clearTimeout(this.nicknameDebounceTimer),this.handleFormChange(),!a||a.length<2||(a!==n?this.nicknameDebounceTimer=setTimeout((function(){e.validateNicknameAsync(a)}),200):this.nicknameValidation={valid:!0,message:"昵称未变化",errorType:""})},handlePhoneBlur:function(t){var e=this,a=t.target.value,n=this.originalData.phone||"";this.phoneDebounceTimer&&clearTimeout(this.phoneDebounceTimer),this.handleFormChange(),a&&/^1[3-9]\d{9}$/.test(a)?a!==n?this.phoneDebounceTimer=setTimeout((function(){e.validatePhoneAsync(a)}),300):this.phoneValidation={valid:!0,message:"手机号未变化",errorType:""}:this.phoneValidation={valid:null,message:"",errorType:""}},handleEmailBlur:function(t){var e=this,a=t.target.value,n=this.originalData.email||"";this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer),this.handleFormChange(),a&&/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(a)?a!==n?this.emailDebounceTimer=setTimeout((function(){e.validateEmailAsync(a)}),300):this.emailValidation={valid:!0,message:"邮箱未变化",errorType:""}:this.emailValidation={valid:null,message:"",errorType:""}},validateNicknameAsync:function(){var t=g(s.a.mark((function t(e){var a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e&&!(e.length<2)){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,this.nicknameValidating=!0,t.next=6,Object(o["J"])(e);case 6:a=t.sent,a.success?this.nicknameValidation={valid:!0,message:"昵称可用",errorType:""}:this.nicknameValidation={valid:!1,message:a.message||"昵称不可用",errorType:"UNKNOWN"},t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](2),this.nicknameValidation={valid:!1,message:t.t0.message||"校验失败，请重试",errorType:"ERROR"};case 14:return t.prev=14,this.nicknameValidating=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[2,10,14,17]])})));function e(e){return t.apply(this,arguments)}return e}(),clearNicknameTimer:function(){this.nicknameDebounceTimer&&(clearTimeout(this.nicknameDebounceTimer),this.nicknameDebounceTimer=null)},validatePhoneAsync:function(){var t=g(s.a.mark((function t(e){var a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.phoneValidating=!0,this.phoneValidation={valid:null,message:"校验中...",errorType:""},t.next=5,this.$http.get("/sys/user/checkPhoneUnique",{params:{phone:e}});case 5:a=t.sent,a.success?this.phoneValidation={valid:!0,message:"手机号可用",errorType:""}:this.phoneValidation={valid:!1,message:a.message||"该手机号已被其他用户使用",errorType:"duplicate"},t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),this.phoneValidation={valid:!1,message:"校验失败，请稍后重试",errorType:"network"};case 13:return t.prev=13,this.phoneValidating=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,this,[[0,9,13,16]])})));function e(e){return t.apply(this,arguments)}return e}(),validateEmailAsync:function(){var t=g(s.a.mark((function t(e){var a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.emailValidating=!0,this.emailValidation={valid:null,message:"校验中...",errorType:""},t.next=5,this.$http.get("/sys/user/checkEmailUnique",{params:{email:e}});case 5:a=t.sent,a.success?this.emailValidation={valid:!0,message:"邮箱可用",errorType:""}:this.emailValidation={valid:!1,message:a.message||"该邮箱已被其他用户使用",errorType:"duplicate"},t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),this.emailValidation={valid:!1,message:"校验失败，请稍后重试",errorType:"network"};case 13:return t.prev=13,this.emailValidating=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,this,[[0,9,13,16]])})));function e(e){return t.apply(this,arguments)}return e}(),clearPhoneDebounceTimer:function(){this.phoneDebounceTimer&&(clearTimeout(this.phoneDebounceTimer),this.phoneDebounceTimer=null)},clearEmailDebounceTimer:function(){this.emailDebounceTimer&&(clearTimeout(this.emailDebounceTimer),this.emailDebounceTimer=null)}}},b=v,x=(a("1594"),a("2877")),w=Object(x["a"])(b,n,i,!1,null,"14aa3459",null);e["default"]=w.exports},e7e8:function(t,e,a){}}]);