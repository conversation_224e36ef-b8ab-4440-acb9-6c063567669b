{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue", "mtime": 1753847752802}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport StatsCard from '../components/StatsCard.vue';\nimport DataTable from '../components/DataTable.vue';\nimport { getTransactionList, getTransactionStats, createRechargeOrder, exportTransactions // getRechargeOptions, // 不再需要，使用前端固定配置\n// getConsumptionChart // 临时注释，等后端接口实现\n} from '@/api/usercenter';\nimport { authMixin } from '@/mixins/authMixin';\nexport default {\n  name: 'UserCenterCredits',\n  mixins: [authMixin],\n  components: {\n    StatsCard: StatsCard,\n    DataTable: DataTable\n  },\n  data: function data() {\n    var h = this.$createElement;\n    return {\n      loading: true,\n      transactionLoading: false,\n      chartLoading: false,\n      rechargeLoading: false,\n      paymentLoading: false,\n      // 余额数据\n      balanceData: {\n        currentBalance: 0,\n        totalRecharge: 0,\n        totalConsumption: 0,\n        monthlyConsumption: 0\n      },\n      // 充值相关 - 固定配置\n      rechargeOptions: [{\n        amount: 50\n      }, {\n        amount: 100\n      }, {\n        amount: 300\n      }, {\n        amount: 500\n      }, {\n        amount: 1000\n      }],\n      selectedAmount: 0,\n      customAmount: null,\n      showRechargeModal: false,\n      selectedPaymentMethod: 'alipay-page',\n      // 默认选择网页支付\n      // 交易记录\n      transactionList: [],\n      pagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      filters: {},\n      // 表格列配置\n      transactionColumns: [{\n        title: '交易时间',\n        dataIndex: 'transactionTime',\n        key: 'transactionTime',\n        width: 180,\n        align: 'center',\n        customRender: function customRender(text) {\n          if (!text) return '-';\n\n          try {\n            var date;\n\n            if (typeof text === 'number') {\n              date = new Date(text);\n            } else {\n              date = new Date(text);\n            }\n\n            return date.toLocaleString('zh-CN', {\n              year: 'numeric',\n              month: '2-digit',\n              day: '2-digit',\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n          } catch (error) {\n            return '-';\n          }\n        }\n      }, {\n        title: '交易类型',\n        dataIndex: 'transactionType',\n        key: 'transactionType',\n        width: 120,\n        align: 'center',\n        customRender: function customRender(text) {\n          var _typeMap;\n\n          console.log('🔍 交易类型 customRender - 接收到的值:', text, '类型:', _typeof(text));\n          var typeMap = (_typeMap = {\n            1: '消费',\n            2: '充值',\n            3: '退款',\n            4: '奖励',\n            5: '会员订阅'\n          }, _defineProperty(_typeMap, \"1\", '消费'), _defineProperty(_typeMap, \"2\", '充值'), _defineProperty(_typeMap, \"3\", '退款'), _defineProperty(_typeMap, \"4\", '奖励'), _defineProperty(_typeMap, \"5\", '会员订阅'), _typeMap);\n          var result = typeMap[text] || text || '-';\n          console.log('🔍 交易类型 customRender - 返回结果:', result);\n          return result;\n        }\n      }, {\n        title: '订单号',\n        dataIndex: 'relatedOrderId',\n        key: 'relatedOrderId',\n        width: 200,\n        align: 'center',\n        customRender: function customRender(text) {\n          return text || '-';\n        }\n      }, {\n        title: '交易描述',\n        dataIndex: 'description',\n        key: 'description',\n        align: 'center',\n        ellipsis: true\n      }, {\n        title: '交易金额',\n        dataIndex: 'amount',\n        key: 'amount',\n        width: 120,\n        align: 'center',\n        customRender: function customRender(text, record) {\n          var amount = parseFloat(text || 0).toFixed(2);\n          var isPositive = [2, 3, 4].includes(Number(record.transactionType)); // 充值、退款、奖励为正\n\n          var prefix = isPositive ? '+' : '-';\n          var colorStyle = isPositive ? 'color: #52c41a; font-weight: 600;' : 'color: #ff4d4f; font-weight: 600;';\n          return h(\"span\", {\n            \"style\": colorStyle\n          }, [prefix, \"\\xA5\", amount]);\n        }\n      }, {\n        title: '余额',\n        dataIndex: 'balanceAfter',\n        key: 'balanceAfter',\n        width: 120,\n        align: 'center',\n        customRender: function customRender(text) {\n          var balance = parseFloat(text || 0).toFixed(2);\n          var balanceStyle = 'color: #1890ff; font-weight: 600;';\n          return h(\"span\", {\n            \"style\": balanceStyle\n          }, [\"\\xA5\", balance]);\n        }\n      }, {\n        title: '状态',\n        dataIndex: 'orderStatus',\n        key: 'orderStatus',\n        width: 100,\n        align: 'center',\n        customRender: function customRender(text) {\n          // 后端查询条件已经限制为 order_status = 3（已完成）\n          // 所以这里的数据都应该是已完成的交易\n          // 如果有明确的状态字段，使用映射\n          if (text !== undefined && text !== null) {\n            var _statusMap;\n\n            var statusMap = (_statusMap = {\n              1: {\n                text: '待支付',\n                style: 'color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n              },\n              2: {\n                text: '已支付',\n                style: 'color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n              },\n              3: {\n                text: '已完成',\n                style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n              },\n              4: {\n                text: '已取消',\n                style: 'color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n              },\n              5: {\n                text: '已退款',\n                style: 'color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n              }\n            }, _defineProperty(_statusMap, \"1\", {\n              text: '待支付',\n              style: 'color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n            }), _defineProperty(_statusMap, \"2\", {\n              text: '已支付',\n              style: 'color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n            }), _defineProperty(_statusMap, \"3\", {\n              text: '已完成',\n              style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n            }), _defineProperty(_statusMap, \"4\", {\n              text: '已取消',\n              style: 'color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n            }), _defineProperty(_statusMap, \"5\", {\n              text: '已退款',\n              style: 'color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n            }), _statusMap);\n            var status = statusMap[text] || {\n              text: '已完成',\n              style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n            };\n            return h(\"span\", {\n              \"style\": status.style\n            }, [status.text]);\n          } // 如果没有状态字段，默认为已完成\n          // 因为后端查询已经过滤为已完成的交易\n\n\n          var defaultStyle = 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;';\n          return h(\"span\", {\n            \"style\": defaultStyle\n          }, [\"\\u5DF2\\u5B8C\\u6210\"]);\n        }\n      }],\n      // 筛选选项\n      transactionTypeOptions: [{\n        value: 1,\n        label: '消费'\n      }, {\n        value: 2,\n        label: '充值'\n      }, {\n        value: 3,\n        label: '退款'\n      }, {\n        value: 4,\n        label: '奖励'\n      }, {\n        value: 5,\n        label: '会员订阅'\n      }],\n      // 🚫 取消状态筛选选项\n      // transactionStatusOptions: [\n      //   { value: 1, label: '待支付' },\n      //   { value: 3, label: '已完成' },\n      //   { value: 4, label: '已取消' },\n      //   { value: 5, label: '已退款' }\n      // ],\n      // 交易详情\n      showTransactionDetail: false,\n      selectedTransaction: null\n    };\n  },\n  computed: {\n    balanceTrend: function balanceTrend() {\n      // 根据实际数据计算趋势（暂时返回null，等待后端提供趋势数据）\n      // TODO: 后端需要提供上月对比数据来计算真实趋势\n      return null;\n    },\n    monthlyTrend: function monthlyTrend() {\n      // 根据实际数据计算月度趋势（暂时返回null，等待后端提供趋势数据）\n      // TODO: 后端需要提供月度对比数据来计算真实趋势\n      return null;\n    },\n    finalRechargeAmount: function finalRechargeAmount() {\n      return this.selectedAmount || this.customAmount || 0;\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.loadData();\n\n            case 2:\n              // 检查是否是支付成功返回\n              this.checkPaymentSuccess();\n\n            case 3:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    loadData: function () {\n      var _loadData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                this.loading = true; // 并行加载数据\n\n                _context2.next = 4;\n                return Promise.all([this.loadBalanceData(), this.loadTransactionData()]);\n\n              case 4:\n                _context2.next = 10;\n                break;\n\n              case 6:\n                _context2.prev = 6;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('加载账户管理数据失败:', _context2.t0);\n                this.$message.error('加载数据失败，请刷新重试');\n\n              case 10:\n                _context2.prev = 10;\n                this.loading = false;\n                return _context2.finish(10);\n\n              case 13:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 6, 10, 13]]);\n      }));\n\n      function loadData() {\n        return _loadData.apply(this, arguments);\n      }\n\n      return loadData;\n    }(),\n    loadBalanceData: function () {\n      var _loadBalanceData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var response, stats;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                _context3.next = 3;\n                return getTransactionStats();\n\n              case 3:\n                response = _context3.sent;\n\n                if (response.success) {\n                  // 修复：使用 result 字段并正确映射字段名\n                  stats = response.result || {}; // 🔧 修复字段映射问题\n\n                  this.balanceData = {\n                    currentBalance: stats.accountBalance || 0,\n                    // 后端返回accountBalance，前端期望currentBalance\n                    totalRecharge: stats.totalRecharge || 0,\n                    // 字段名一致\n                    totalConsumption: stats.totalConsumption || 0,\n                    // 字段名一致\n                    monthlyConsumption: stats.monthlyConsumption || 0,\n                    // 如果后端有这个字段\n                    transactionCount: stats.transactionCount || 0 // 交易记录总数\n\n                  };\n                  console.log('🔍 Credits页面 - 余额数据映射结果:', this.balanceData);\n                  console.log('🔍 Credits页面 - 后端原始数据:', stats);\n                }\n\n                _context3.next = 10;\n                break;\n\n              case 7:\n                _context3.prev = 7;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('加载余额数据失败:', _context3.t0);\n\n              case 10:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 7]]);\n      }));\n\n      function loadBalanceData() {\n        return _loadBalanceData.apply(this, arguments);\n      }\n\n      return loadBalanceData;\n    }(),\n    loadTransactionData: function () {\n      var _loadTransactionData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var params, response, records;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                this.transactionLoading = true;\n                params = _objectSpread({\n                  current: this.pagination.current,\n                  size: this.pagination.pageSize\n                }, this.filters); // 🔧 处理日期范围筛选\n\n                if (this.filters.dateRange && this.filters.dateRange.length === 2) {\n                  params.startDate = this.filters.dateRange[0].format('YYYY-MM-DD');\n                  params.endDate = this.filters.dateRange[1].format('YYYY-MM-DD');\n                  console.log('🔍 日期范围筛选 - 原始dateRange:', this.filters.dateRange);\n                  console.log('🔍 日期范围筛选 - startDate:', params.startDate, 'endDate:', params.endDate); // 移除dateRange，避免传递给后端\n\n                  delete params.dateRange;\n                }\n\n                console.log('🔍 Credits页面 - 最终查询参数:', params);\n                _context4.next = 7;\n                return getTransactionList(params);\n\n              case 7:\n                response = _context4.sent;\n\n                if (response.success) {\n                  // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法\n                  records = response.result && response.result.records || []; // 调试：打印后端返回的数据结构\n\n                  console.log('🔍 Credits页面 - 后端返回的完整响应:', response);\n                  console.log('🔍 Credits页面 - 交易记录数组:', records);\n\n                  if (records.length > 0) {\n                    console.log('🔍 Credits页面 - 第一条记录详情:', records[0]);\n                    console.log('🔍 Credits页面 - 第一条记录的所有字段:', Object.keys(records[0]));\n                    console.log('🔍 Credits页面 - transactionType字段值:', records[0].transactionType, '类型:', _typeof(records[0].transactionType));\n                    console.log('🔍 Credits页面 - status字段值:', records[0].status, '类型:', _typeof(records[0].status)); // 检查所有可能的状态相关字段\n\n                    console.log('🔍 Credits页面 - 检查状态相关字段:');\n                    console.log('  - status:', records[0].status);\n                    console.log('  - transactionStatus:', records[0].transactionStatus);\n                    console.log('  - orderStatus:', records[0].orderStatus);\n                    console.log('  - paymentStatus:', records[0].paymentStatus);\n                    console.log('  - state:', records[0].state);\n                    console.log('  - delFlag:', records[0].delFlag);\n                  }\n\n                  this.transactionList = records;\n                  this.pagination.total = response.result && response.result.total || 0;\n                }\n\n                _context4.next = 15;\n                break;\n\n              case 11:\n                _context4.prev = 11;\n                _context4.t0 = _context4[\"catch\"](0);\n                console.error('加载交易记录失败:', _context4.t0);\n                this.transactionList = [];\n\n              case 15:\n                _context4.prev = 15;\n                this.transactionLoading = false;\n                return _context4.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 11, 15, 18]]);\n      }));\n\n      function loadTransactionData() {\n        return _loadTransactionData.apply(this, arguments);\n      }\n\n      return loadTransactionData;\n    }(),\n    // 充值相关方法\n    selectRechargeAmount: function selectRechargeAmount(amount) {\n      this.selectedAmount = amount;\n      this.customAmount = null;\n    },\n    // 自定义金额变化\n    onCustomAmountChange: function onCustomAmountChange(value) {\n      if (value && value > 0) {\n        this.selectedAmount = 0; // 清空预设金额选择\n      }\n    },\n    handleQuickRecharge: function handleQuickRecharge() {\n      // 重置选择状态\n      this.selectedAmount = 0;\n      this.customAmount = null; // 显示充值模态框\n\n      this.showRechargeModal = true;\n    },\n    handleRecharge: function handleRecharge() {\n      if (!this.finalRechargeAmount || this.finalRechargeAmount < 0.01) {\n        this.$message.warning('请选择或输入充值金额，最低0.01元');\n        return;\n      }\n\n      this.showRechargeModal = true;\n    },\n    handleConfirmRecharge: function () {\n      var _handleConfirmRecharge = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        var orderData, response, result;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                _context5.prev = 0;\n                this.paymentLoading = true;\n                orderData = {\n                  amount: this.finalRechargeAmount,\n                  paymentMethod: this.selectedPaymentMethod\n                };\n                _context5.next = 5;\n                return createRechargeOrder(orderData);\n\n              case 5:\n                response = _context5.sent;\n\n                if (!response.success) {\n                  _context5.next = 13;\n                  break;\n                }\n\n                result = response.result; // 只支持支付宝网页支付\n\n                _context5.next = 10;\n                return this.handleAlipayPagePayment(result.orderId, result.amount);\n\n              case 10:\n                this.showRechargeModal = false;\n                _context5.next = 14;\n                break;\n\n              case 13:\n                this.$message.error(response.message || '创建充值订单失败');\n\n              case 14:\n                _context5.next = 20;\n                break;\n\n              case 16:\n                _context5.prev = 16;\n                _context5.t0 = _context5[\"catch\"](0);\n                console.error('创建充值订单失败:', _context5.t0);\n                this.$message.error('充值失败，请重试');\n\n              case 20:\n                _context5.prev = 20;\n                this.paymentLoading = false;\n                return _context5.finish(20);\n\n              case 23:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[0, 16, 20, 23]]);\n      }));\n\n      function handleConfirmRecharge() {\n        return _handleConfirmRecharge.apply(this, arguments);\n      }\n\n      return handleConfirmRecharge;\n    }(),\n    // 处理支付宝网页支付\n    handleAlipayPagePayment: function () {\n      var _handleAlipayPagePayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6(orderId, amount) {\n        var paymentData, payResponse, payForm, div, form;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                console.log('🔍 开始处理支付宝支付 - 订单号:', orderId, '金额:', amount);\n                this.$message.loading('正在跳转到支付宝支付...', 0); // 调用支付宝支付接口\n\n                paymentData = {\n                  orderId: orderId,\n                  amount: amount,\n                  subject: '智界Aigc账户充值',\n                  body: \"\\u5145\\u503C\\u91D1\\u989D\\uFF1A\\xA5\".concat(amount)\n                };\n                console.log('🔍 发送支付请求数据:', paymentData);\n                _context6.next = 7;\n                return this.$http.post('/api/alipay/createOrder', paymentData);\n\n              case 7:\n                payResponse = _context6.sent;\n                console.log('🔍 支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context6.next = 24;\n                  break;\n                }\n\n                payForm = payResponse.result.payForm;\n                console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空');\n\n                if (payForm) {\n                  _context6.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付表单为空');\n                return _context6.abrupt(\"return\");\n\n              case 16:\n                // 创建表单并提交到支付宝\n                div = document.createElement('div');\n                div.innerHTML = payForm;\n                document.body.appendChild(div);\n                form = div.querySelector('form');\n\n                if (form) {\n                  console.log('🔍 找到支付表单，准备提交');\n                  form.submit();\n                } else {\n                  console.error('🔍 未找到支付表单');\n                  this.$message.error('支付表单创建失败');\n                } // 清理DOM\n\n\n                setTimeout(function () {\n                  if (document.body.contains(div)) {\n                    document.body.removeChild(div);\n                  }\n                }, 1000);\n                _context6.next = 26;\n                break;\n\n              case 24:\n                console.error('🔍 支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建支付订单失败');\n\n              case 26:\n                _context6.next = 33;\n                break;\n\n              case 28:\n                _context6.prev = 28;\n                _context6.t0 = _context6[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝支付失败:', _context6.t0);\n                this.$message.error('支付宝支付失败，请重试');\n\n              case 33:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 28]]);\n      }));\n\n      function handleAlipayPagePayment(_x, _x2) {\n        return _handleAlipayPagePayment.apply(this, arguments);\n      }\n\n      return handleAlipayPagePayment;\n    }(),\n    // 处理支付宝扫码支付\n    handleAlipayQrPayment: function () {\n      var _handleAlipayQrPayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7(orderId, amount) {\n        var paymentData, payResponse, qrCode;\n        return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n          while (1) {\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                _context7.prev = 0;\n                console.log('🔍 开始处理支付宝扫码支付 - 订单号:', orderId, '金额:', amount);\n                this.$message.loading('正在生成支付二维码...', 0); // 调用支付宝扫码支付接口\n\n                paymentData = {\n                  orderId: orderId,\n                  amount: amount,\n                  subject: '智界Aigc账户充值',\n                  body: \"\\u5145\\u503C\\u91D1\\u989D\\uFF1A\\xA5\".concat(amount)\n                };\n                console.log('🔍 发送扫码支付请求数据:', paymentData);\n                _context7.next = 7;\n                return this.$http.post('/api/alipay/createQrOrder', paymentData);\n\n              case 7:\n                payResponse = _context7.sent;\n                console.log('🔍 扫码支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context7.next = 19;\n                  break;\n                }\n\n                qrCode = payResponse.result.qrCode;\n                console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空');\n\n                if (qrCode) {\n                  _context7.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付二维码生成失败');\n                return _context7.abrupt(\"return\");\n\n              case 16:\n                // 显示二维码支付弹窗\n                this.showQrCodeModal(qrCode, orderId, amount);\n                _context7.next = 21;\n                break;\n\n              case 19:\n                console.error('🔍 扫码支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建扫码支付订单失败');\n\n              case 21:\n                _context7.next = 28;\n                break;\n\n              case 23:\n                _context7.prev = 23;\n                _context7.t0 = _context7[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝扫码支付失败:', _context7.t0);\n                this.$message.error('支付宝扫码支付失败，请重试');\n\n              case 28:\n              case \"end\":\n                return _context7.stop();\n            }\n          }\n        }, _callee7, this, [[0, 23]]);\n      }));\n\n      function handleAlipayQrPayment(_x3, _x4) {\n        return _handleAlipayQrPayment.apply(this, arguments);\n      }\n\n      return handleAlipayQrPayment;\n    }(),\n    // 显示二维码支付弹窗\n    showQrCodeModal: function showQrCodeModal(qrCode, orderId, amount) {\n      var _this = this;\n\n      this.$modal.info({\n        title: '支付宝扫码支付',\n        width: 400,\n        content: function content(h) {\n          return h('div', {\n            style: {\n              textAlign: 'center',\n              padding: '20px'\n            }\n          }, [h('div', {\n            style: {\n              marginBottom: '16px',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            }\n          }, \"\\u5145\\u503C\\u91D1\\u989D\\uFF1A\\xA5\".concat(amount)), h('div', {\n            style: {\n              marginBottom: '16px',\n              color: '#666'\n            }\n          }, '请使用支付宝扫描下方二维码完成支付'), h('div', {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              marginBottom: '16px'\n            }\n          }, [h('img', {\n            src: \"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=\".concat(encodeURIComponent(qrCode)),\n            style: {\n              width: '200px',\n              height: '200px',\n              border: '1px solid #d9d9d9'\n            }\n          })]), h('div', {\n            style: {\n              color: '#999',\n              fontSize: '12px'\n            }\n          }, '支付完成后页面将自动跳转')]);\n        },\n        onOk: function onOk() {\n          // 用户点击确定后，可以查询订单状态\n          _this.checkOrderStatus(orderId);\n        }\n      }); // 定时查询订单状态\n\n      this.startOrderStatusPolling(orderId);\n    },\n    // 开始轮询订单状态\n    startOrderStatusPolling: function startOrderStatusPolling(orderId) {\n      var _this2 = this;\n\n      var pollInterval = setInterval( /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n          while (1) {\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                _context8.prev = 0;\n                _context8.next = 3;\n                return _this2.$http.get(\"/api/alipay/queryOrder?orderId=\".concat(orderId));\n\n              case 3:\n                response = _context8.sent;\n\n                if (response.success && response.result.tradeStatus === 'TRADE_SUCCESS') {\n                  clearInterval(pollInterval);\n\n                  _this2.$modal.destroyAll();\n\n                  _this2.$message.success('支付成功！正在更新账户余额...');\n\n                  setTimeout(function () {\n                    _this2.loadData();\n                  }, 1000);\n                }\n\n                _context8.next = 10;\n                break;\n\n              case 7:\n                _context8.prev = 7;\n                _context8.t0 = _context8[\"catch\"](0);\n                console.error('查询订单状态失败:', _context8.t0);\n\n              case 10:\n              case \"end\":\n                return _context8.stop();\n            }\n          }\n        }, _callee8, null, [[0, 7]]);\n      })), 3000); // 每3秒查询一次\n      // 15分钟后停止轮询\n\n      setTimeout(function () {\n        clearInterval(pollInterval);\n      }, 15 * 60 * 1000);\n    },\n    // 检查订单状态\n    checkOrderStatus: function () {\n      var _checkOrderStatus = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9(orderId) {\n        var _this3 = this;\n\n        var response;\n        return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n          while (1) {\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                _context9.prev = 0;\n                _context9.next = 3;\n                return this.$http.get(\"/api/alipay/queryOrder?orderId=\".concat(orderId));\n\n              case 3:\n                response = _context9.sent;\n\n                if (response.success && response.result.tradeStatus === 'TRADE_SUCCESS') {\n                  this.$message.success('支付成功！正在更新账户余额...');\n                  setTimeout(function () {\n                    _this3.loadData();\n                  }, 1000);\n                } else {\n                  this.$message.info('订单尚未支付，请继续扫码支付');\n                }\n\n                _context9.next = 11;\n                break;\n\n              case 7:\n                _context9.prev = 7;\n                _context9.t0 = _context9[\"catch\"](0);\n                console.error('查询订单状态失败:', _context9.t0);\n                this.$message.error('查询订单状态失败');\n\n              case 11:\n              case \"end\":\n                return _context9.stop();\n            }\n          }\n        }, _callee9, this, [[0, 7]]);\n      }));\n\n      function checkOrderStatus(_x5) {\n        return _checkOrderStatus.apply(this, arguments);\n      }\n\n      return checkOrderStatus;\n    }(),\n    // 检查支付成功状态\n    checkPaymentSuccess: function checkPaymentSuccess() {\n      var _this4 = this;\n\n      var urlParams = new URLSearchParams(window.location.search);\n      var paymentSuccess = urlParams.get('paymentSuccess');\n      var orderId = urlParams.get('orderId');\n\n      if (paymentSuccess === 'true' && orderId) {\n        console.log('🎉 检测到支付成功返回 - 订单号:', orderId); // 显示支付成功消息\n\n        this.$message.success('支付成功！正在更新账户余额...'); // 刷新数据\n\n        setTimeout(function () {\n          _this4.loadData();\n        }, 1000); // 清理URL参数\n\n        var newUrl = window.location.pathname;\n        window.history.replaceState({}, document.title, newUrl);\n      }\n    },\n    // 表格相关方法\n    handleFilterChange: function handleFilterChange(filters) {\n      this.filters = filters;\n      this.pagination.current = 1;\n      this.loadTransactionData();\n    },\n    handleTableChange: function handleTableChange(_ref2) {\n      var pagination = _ref2.pagination;\n      this.pagination = _objectSpread(_objectSpread({}, this.pagination), pagination);\n      this.loadTransactionData();\n    },\n    // 重置筛选条件\n    handleResetFilters: function handleResetFilters() {\n      // 调用DataTable组件的重置方法\n      if (this.$refs.transactionTable) {\n        this.$refs.transactionTable.resetFilters();\n      } // 重置本地筛选条件\n\n\n      this.filters = {};\n      this.pagination.current = 1; // 重新加载数据\n\n      this.loadTransactionData();\n      this.$message.success('筛选条件已重置');\n    },\n    // 导出交易记录\n    handleExportTransactions: function () {\n      var _handleExportTransactions = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10() {\n        var params, response, blob, url, link, now, timestamp;\n        return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n          while (1) {\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                _context10.prev = 0;\n                this.$message.loading('正在导出交易记录...', 0); // 使用当前的筛选条件导出\n\n                params = {\n                  transactionType: this.filters.type,\n                  status: this.filters.status,\n                  keyword: this.filters.keyword\n                }; // 处理日期范围\n\n                if (this.filters.dateRange && this.filters.dateRange.length === 2) {\n                  params.startDate = this.filters.dateRange[0].format('YYYY-MM-DD');\n                  params.endDate = this.filters.dateRange[1].format('YYYY-MM-DD');\n                }\n\n                console.log('🎯 导出交易记录参数:', params);\n                _context10.next = 7;\n                return exportTransactions(params);\n\n              case 7:\n                response = _context10.sent;\n                this.$message.destroy(); // 创建下载链接\n\n                blob = new Blob([response], {\n                  type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n                });\n                url = window.URL.createObjectURL(blob);\n                link = document.createElement('a');\n                link.href = url; // 生成文件名\n\n                now = new Date();\n                timestamp = now.getFullYear() + String(now.getMonth() + 1).padStart(2, '0') + String(now.getDate()).padStart(2, '0') + '_' + String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');\n                link.download = \"\\u4EA4\\u6613\\u8BB0\\u5F55_\".concat(timestamp, \".xlsx\");\n                document.body.appendChild(link);\n                link.click();\n                document.body.removeChild(link);\n                window.URL.revokeObjectURL(url);\n                this.$message.success('交易记录导出成功！');\n                _context10.next = 28;\n                break;\n\n              case 23:\n                _context10.prev = 23;\n                _context10.t0 = _context10[\"catch\"](0);\n                this.$message.destroy();\n                console.error('🎯 导出交易记录失败:', _context10.t0);\n                this.$message.error('导出失败，请重试');\n\n              case 28:\n              case \"end\":\n                return _context10.stop();\n            }\n          }\n        }, _callee10, this, [[0, 23]]);\n      }));\n\n      function handleExportTransactions() {\n        return _handleExportTransactions.apply(this, arguments);\n      }\n\n      return handleExportTransactions;\n    }(),\n    handleViewTransactionDetail: function handleViewTransactionDetail(transaction) {\n      this.selectedTransaction = transaction;\n      this.showTransactionDetail = true;\n    },\n    // 工具方法\n    getTransactionTypeClass: function getTransactionTypeClass(type) {\n      var classMap = {\n        1: 'type-consume',\n        2: 'type-recharge',\n        3: 'type-refund',\n        4: 'type-reward'\n      };\n      return classMap[type] || '';\n    },\n    getTransactionTypeIcon: function getTransactionTypeIcon(type) {\n      var iconMap = {\n        1: 'anticon anticon-minus-circle',\n        2: 'anticon anticon-plus-circle',\n        3: 'anticon anticon-undo',\n        4: 'anticon anticon-gift'\n      };\n      return iconMap[type] || 'anticon anticon-question-circle';\n    },\n    getTransactionTypeText: function getTransactionTypeText(type) {\n      var textMap = {\n        1: '消费',\n        2: '充值',\n        3: '退款',\n        4: '奖励'\n      };\n      return textMap[type] || '未知';\n    },\n    getAmountClass: function getAmountClass(type) {\n      return {\n        'amount-positive': [2, 3, 4].includes(type),\n        // 充值、退款、奖励为正\n        'amount-negative': type === 1 // 消费为负\n\n      };\n    },\n    formatAmount: function formatAmount(amount, type) {\n      var prefix = [2, 3, 4].includes(type) ? '+' : '-';\n      return \"\".concat(prefix, \"\\xA5\").concat(this.formatNumber(Math.abs(amount)));\n    },\n    formatNumber: function formatNumber(number) {\n      if (!number) return '0.00';\n      return parseFloat(number).toFixed(2);\n    },\n    formatDateTime: function formatDateTime(dateString) {\n      if (!dateString) return '-';\n\n      try {\n        var date = new Date(dateString);\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } catch (error) {\n        return '-';\n      }\n    }\n  }\n};", {"version": 3, "sources": ["Credits.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgOA,OAAA,SAAA,MAAA,6BAAA;AACA,OAAA,SAAA,MAAA,6BAAA;AACA,SACA,kBADA,EAEA,mBAFA,EAGA,mBAHA,EAIA,kBAJA,CAKA;AACA;AANA,OAOA,kBAPA;AAQA,SAAA,SAAA,QAAA,oBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,mBADA;AAEA,EAAA,MAAA,EAAA,CAAA,SAAA,CAFA;AAGA,EAAA,UAAA,EAAA;AACA,IAAA,SAAA,EAAA,SADA;AAEA,IAAA,SAAA,EAAA;AAFA,GAHA;AAOA,EAAA,IAPA,kBAOA;AAAA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,IADA;AAEA,MAAA,kBAAA,EAAA,KAFA;AAGA,MAAA,YAAA,EAAA,KAHA;AAIA,MAAA,eAAA,EAAA,KAJA;AAKA,MAAA,cAAA,EAAA,KALA;AAOA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,cAAA,EAAA,CADA;AAEA,QAAA,aAAA,EAAA,CAFA;AAGA,QAAA,gBAAA,EAAA,CAHA;AAIA,QAAA,kBAAA,EAAA;AAJA,OARA;AAeA;AACA,MAAA,eAAA,EAAA,CACA;AAAA,QAAA,MAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,MAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,MAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,MAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,MAAA,EAAA;AAAA,OALA,CAhBA;AAuBA,MAAA,cAAA,EAAA,CAvBA;AAwBA,MAAA,YAAA,EAAA,IAxBA;AAyBA,MAAA,iBAAA,EAAA,KAzBA;AA0BA,MAAA,qBAAA,EAAA,aA1BA;AA0BA;AAIA;AACA,MAAA,eAAA,EAAA,EA/BA;AAgCA,MAAA,UAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAhCA;AAqCA,MAAA,OAAA,EAAA,EArCA;AAuCA;AACA,MAAA,kBAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,iBAFA;AAGA,QAAA,GAAA,EAAA,iBAHA;AAIA,QAAA,KAAA,EAAA,GAJA;AAKA,QAAA,KAAA,EAAA,QALA;AAMA,QAAA,YAAA,EAAA,sBAAA,IAAA,EAAA;AACA,cAAA,CAAA,IAAA,EAAA,OAAA,GAAA;;AACA,cAAA;AACA,gBAAA,IAAA;;AACA,gBAAA,OAAA,IAAA,KAAA,QAAA,EAAA;AACA,cAAA,IAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AACA,aAFA,MAEA;AACA,cAAA,IAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AACA;;AACA,mBAAA,IAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,KAAA,EAAA,SAFA;AAGA,cAAA,GAAA,EAAA,SAHA;AAIA,cAAA,IAAA,EAAA,SAJA;AAKA,cAAA,MAAA,EAAA;AALA,aAAA,CAAA;AAOA,WAdA,CAcA,OAAA,KAAA,EAAA;AACA,mBAAA,GAAA;AACA;AACA;AAzBA,OADA,EA4BA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,iBAFA;AAGA,QAAA,GAAA,EAAA,iBAHA;AAIA,QAAA,KAAA,EAAA,GAJA;AAKA,QAAA,KAAA,EAAA,QALA;AAMA,QAAA,YAAA,EAAA,sBAAA,IAAA,EAAA;AAAA;;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,IAAA,EAAA,KAAA,UAAA,IAAA;AACA,cAAA,OAAA;AACA,eAAA,IADA;AAEA,eAAA,IAFA;AAGA,eAAA,IAHA;AAIA,eAAA,IAJA;AAKA,eAAA;AALA,4CAMA,IANA,kCAOA,IAPA,kCAQA,IARA,kCASA,IATA,kCAUA,MAVA,YAAA;AAYA,cAAA,MAAA,GAAA,OAAA,CAAA,IAAA,CAAA,IAAA,IAAA,IAAA,GAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,MAAA;AACA,iBAAA,MAAA;AACA;AAvBA,OA5BA,EAqDA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,SAAA,EAAA,gBAFA;AAGA,QAAA,GAAA,EAAA,gBAHA;AAIA,QAAA,KAAA,EAAA,GAJA;AAKA,QAAA,KAAA,EAAA,QALA;AAMA,QAAA,YAAA,EAAA,sBAAA,IAAA,EAAA;AACA,iBAAA,IAAA,IAAA,GAAA;AACA;AARA,OArDA,EA+DA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,aAFA;AAGA,QAAA,GAAA,EAAA,aAHA;AAIA,QAAA,KAAA,EAAA,QAJA;AAKA,QAAA,QAAA,EAAA;AALA,OA/DA,EAsEA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,KAAA,EAAA,GAJA;AAKA,QAAA,KAAA,EAAA,QALA;AAMA,QAAA,YAAA,EAAA,sBAAA,IAAA,EAAA,MAAA,EAAA;AACA,cAAA,MAAA,GAAA,UAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,cAAA,UAAA,GAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA,MAAA,CAAA,MAAA,CAAA,eAAA,CAAA,CAAA,CAFA,CAEA;;AACA,cAAA,MAAA,GAAA,UAAA,GAAA,GAAA,GAAA,GAAA;AACA,cAAA,UAAA,GAAA,UAAA,GAAA,mCAAA,GAAA,mCAAA;AACA;AAAA,qBAAA;AAAA,cAAA,MAAA,UAAA,MAAA;AACA;AAZA,OAtEA,EAoFA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,cAFA;AAGA,QAAA,GAAA,EAAA,cAHA;AAIA,QAAA,KAAA,EAAA,GAJA;AAKA,QAAA,KAAA,EAAA,QALA;AAMA,QAAA,YAAA,EAAA,sBAAA,IAAA,EAAA;AACA,cAAA,OAAA,GAAA,UAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,cAAA,YAAA,GAAA,mCAAA;AACA;AAAA,qBAAA;AAAA,sBAAA,OAAA;AACA;AAVA,OApFA,EAgGA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,aAFA;AAGA,QAAA,GAAA,EAAA,aAHA;AAIA,QAAA,KAAA,EAAA,GAJA;AAKA,QAAA,KAAA,EAAA,QALA;AAMA,QAAA,YAAA,EAAA,sBAAA,IAAA,EAAA;AACA;AACA;AAEA;AACA,cAAA,IAAA,KAAA,SAAA,IAAA,IAAA,KAAA,IAAA,EAAA;AAAA;;AACA,gBAAA,SAAA;AACA,iBAAA;AAAA,gBAAA,IAAA,EAAA,KAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eADA;AAEA,iBAAA;AAAA,gBAAA,IAAA,EAAA,KAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eAFA;AAGA,iBAAA;AAAA,gBAAA,IAAA,EAAA,KAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eAHA;AAIA,iBAAA;AAAA,gBAAA,IAAA,EAAA,KAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eAJA;AAKA,iBAAA;AAAA,gBAAA,IAAA,EAAA,KAAA;AAAA,gBAAA,KAAA,EAAA;AAAA;AALA,gDAMA;AAAA,cAAA,IAAA,EAAA,KAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aANA,oCAOA;AAAA,cAAA,IAAA,EAAA,KAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aAPA,oCAQA;AAAA,cAAA,IAAA,EAAA,KAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aARA,oCASA;AAAA,cAAA,IAAA,EAAA,KAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aATA,oCAUA;AAAA,cAAA,IAAA,EAAA,KAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aAVA,cAAA;AAYA,gBAAA,MAAA,GAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AAAA,cAAA,IAAA,EAAA,KAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aAAA;AACA;AAAA,uBAAA,MAAA,CAAA;AAAA,gBAAA,MAAA,CAAA,IAAA;AACA,WApBA,CAsBA;AACA;;;AACA,cAAA,YAAA,GAAA,+GAAA;AACA;AAAA,qBAAA;AAAA;AACA;AAhCA,OAhGA,CAxCA;AA4KA;AACA,MAAA,sBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OALA,CA7KA;AAoLA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,MAAA,qBAAA,EAAA,KA7LA;AA8LA,MAAA,mBAAA,EAAA;AA9LA,KAAA;AAgMA,GAxMA;AAyMA,EAAA,QAAA,EAAA;AACA,IAAA,YADA,0BACA;AACA;AACA;AACA,aAAA,IAAA;AACA,KALA;AAOA,IAAA,YAPA,0BAOA;AACA;AACA;AACA,aAAA,IAAA;AACA,KAXA;AAaA,IAAA,mBAbA,iCAaA;AACA,aAAA,KAAA,cAAA,IAAA,KAAA,YAAA,IAAA,CAAA;AACA;AAfA,GAzMA;AA0NA,EAAA,OA1NA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBA2NA,KAAA,QAAA,EA3NA;;AAAA;AA6NA;AACA,mBAAA,mBAAA;;AA9NA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAgOA,EAAA,OAAA,EAAA;AACA,IAAA,QADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,qBAAA,OAAA,GAAA,IAAA,CAHA,CAKA;;AALA;AAAA,uBAMA,OAAA,CAAA,GAAA,CAAA,CACA,KAAA,eAAA,EADA,EAEA,KAAA,mBAAA,EAFA,CAAA,CANA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAWA,gBAAA,OAAA,CAAA,KAAA,CAAA,aAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,cAAA;;AAZA;AAAA;AAcA,qBAAA,OAAA,GAAA,KAAA;AAdA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkBA,IAAA,eAlBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAoBA,mBAAA,EApBA;;AAAA;AAoBA,gBAAA,QApBA;;AAqBA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA;AACA,kBAAA,KAFA,GAEA,QAAA,CAAA,MAAA,IAAA,EAFA,EAIA;;AACA,uBAAA,WAAA,GAAA;AACA,oBAAA,cAAA,EAAA,KAAA,CAAA,cAAA,IAAA,CADA;AACA;AACA,oBAAA,aAAA,EAAA,KAAA,CAAA,aAAA,IAAA,CAFA;AAEA;AACA,oBAAA,gBAAA,EAAA,KAAA,CAAA,gBAAA,IAAA,CAHA;AAGA;AACA,oBAAA,kBAAA,EAAA,KAAA,CAAA,kBAAA,IAAA,CAJA;AAIA;AACA,oBAAA,gBAAA,EAAA,KAAA,CAAA,gBAAA,IAAA,CALA,CAKA;;AALA,mBAAA;AAQA,kBAAA,OAAA,CAAA,GAAA,CAAA,0BAAA,EAAA,KAAA,WAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,wBAAA,EAAA,KAAA;AACA;;AApCA;AAAA;;AAAA;AAAA;AAAA;AAsCA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;;AAtCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA0CA,IAAA,mBA1CA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4CA,qBAAA,kBAAA,GAAA,IAAA;AAEA,gBAAA,MA9CA;AA+CA,kBAAA,OAAA,EAAA,KAAA,UAAA,CAAA,OA/CA;AAgDA,kBAAA,IAAA,EAAA,KAAA,UAAA,CAAA;AAhDA,mBAiDA,KAAA,OAjDA,GAoDA;;AACA,oBAAA,KAAA,OAAA,CAAA,SAAA,IAAA,KAAA,OAAA,CAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,GAAA,KAAA,OAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA,OAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,0BAAA,EAAA,KAAA,OAAA,CAAA,SAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,wBAAA,EAAA,MAAA,CAAA,SAAA,EAAA,UAAA,EAAA,MAAA,CAAA,OAAA,EAJA,CAKA;;AACA,yBAAA,MAAA,CAAA,SAAA;AACA;;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,wBAAA,EAAA,MAAA;AA9DA;AAAA,uBAgEA,kBAAA,CAAA,MAAA,CAhEA;;AAAA;AAgEA,gBAAA,QAhEA;;AAiEA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA;AACA,kBAAA,OAFA,GAEA,QAAA,CAAA,MAAA,IAAA,QAAA,CAAA,MAAA,CAAA,OAAA,IAAA,EAFA,EAIA;;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,2BAAA,EAAA,QAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,wBAAA,EAAA,OAAA;;AACA,sBAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,yBAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,4BAAA,EAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,oCAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,eAAA,EAAA,KAAA,UAAA,OAAA,CAAA,CAAA,CAAA,CAAA,eAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,2BAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,KAAA,UAAA,OAAA,CAAA,CAAA,CAAA,CAAA,MAAA,GAJA,CAMA;;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,0BAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,MAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,wBAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,iBAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,WAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,oBAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,KAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,OAAA;AACA;;AAEA,uBAAA,eAAA,GAAA,OAAA;AACA,uBAAA,UAAA,CAAA,KAAA,GAAA,QAAA,CAAA,MAAA,IAAA,QAAA,CAAA,MAAA,CAAA,KAAA,IAAA,CAAA;AACA;;AA1FA;AAAA;;AAAA;AAAA;AAAA;AA4FA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,eAAA,GAAA,EAAA;;AA7FA;AAAA;AA+FA,qBAAA,kBAAA,GAAA,KAAA;AA/FA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAuGA;AACA,IAAA,oBAxGA,gCAwGA,MAxGA,EAwGA;AACA,WAAA,cAAA,GAAA,MAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,KA3GA;AA6GA;AACA,IAAA,oBA9GA,gCA8GA,KA9GA,EA8GA;AACA,UAAA,KAAA,IAAA,KAAA,GAAA,CAAA,EAAA;AACA,aAAA,cAAA,GAAA,CAAA,CADA,CACA;AACA;AACA,KAlHA;AAoHA,IAAA,mBApHA,iCAoHA;AACA;AACA,WAAA,cAAA,GAAA,CAAA;AACA,WAAA,YAAA,GAAA,IAAA,CAHA,CAIA;;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,KA1HA;AA4HA,IAAA,cA5HA,4BA4HA;AACA,UAAA,CAAA,KAAA,mBAAA,IAAA,KAAA,mBAAA,GAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,oBAAA;AACA;AACA;;AAEA,WAAA,iBAAA,GAAA,IAAA;AACA,KAnIA;AAqIA,IAAA,qBArIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuIA,qBAAA,cAAA,GAAA,IAAA;AAEA,gBAAA,SAzIA,GAyIA;AACA,kBAAA,MAAA,EAAA,KAAA,mBADA;AAEA,kBAAA,aAAA,EAAA,KAAA;AAFA,iBAzIA;AAAA;AAAA,uBA8IA,mBAAA,CAAA,SAAA,CA9IA;;AAAA;AA8IA,gBAAA,QA9IA;;AAAA,qBA+IA,QAAA,CAAA,OA/IA;AAAA;AAAA;AAAA;;AAgJA,gBAAA,MAhJA,GAgJA,QAAA,CAAA,MAhJA,EAkJA;;AAlJA;AAAA,uBAmJA,KAAA,uBAAA,CAAA,MAAA,CAAA,OAAA,EAAA,MAAA,CAAA,MAAA,CAnJA;;AAAA;AAqJA,qBAAA,iBAAA,GAAA,KAAA;AArJA;AAAA;;AAAA;AAuJA,qBAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,UAAA;;AAvJA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA0JA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;;AA3JA;AAAA;AA6JA,qBAAA,cAAA,GAAA,KAAA;AA7JA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiKA;AACA,IAAA,uBAlKA;AAAA,gHAkKA,OAlKA,EAkKA,MAlKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoKA,gBAAA,OAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA;AACA,qBAAA,QAAA,CAAA,OAAA,CAAA,eAAA,EAAA,CAAA,EArKA,CAuKA;;AACA,gBAAA,WAxKA,GAwKA;AACA,kBAAA,OAAA,EAAA,OADA;AAEA,kBAAA,MAAA,EAAA,MAFA;AAGA,kBAAA,OAAA,EAAA,YAHA;AAIA,kBAAA,IAAA,8CAAA,MAAA;AAJA,iBAxKA;AA+KA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,WAAA;AA/KA;AAAA,uBAgLA,KAAA,KAAA,CAAA,IAAA,CAAA,yBAAA,EAAA,WAAA,CAhLA;;AAAA;AAgLA,gBAAA,WAhLA;AAiLA,gBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,WAAA;AACA,qBAAA,QAAA,CAAA,OAAA;;AAlLA,qBAoLA,WAAA,CAAA,OApLA;AAAA;AAAA;AAAA;;AAqLA,gBAAA,OArLA,GAqLA,WAAA,CAAA,MAAA,CAAA,OArLA;AAsLA,gBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,OAAA,GAAA,KAAA,GAAA,IAAA;;AAtLA,oBAwLA,OAxLA;AAAA;AAAA;AAAA;;AAyLA,qBAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AAzLA;;AAAA;AA6LA;AACA,gBAAA,GA9LA,GA8LA,QAAA,CAAA,aAAA,CAAA,KAAA,CA9LA;AA+LA,gBAAA,GAAA,CAAA,SAAA,GAAA,OAAA;AACA,gBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA;AAEA,gBAAA,IAlMA,GAkMA,GAAA,CAAA,aAAA,CAAA,MAAA,CAlMA;;AAmMA,oBAAA,IAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,gBAAA;AACA,kBAAA,IAAA,CAAA,MAAA;AACA,iBAHA,MAGA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,iBAzMA,CA2MA;;;AACA,gBAAA,UAAA,CAAA,YAAA;AACA,sBAAA,QAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;AACA,oBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA;AACA;AACA,iBAJA,EAIA,IAJA,CAAA;AA5MA;AAAA;;AAAA;AAmNA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA,EAAA,WAAA,CAAA,OAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,IAAA,UAAA;;AApNA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAwNA,qBAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,UAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,aAAA;;AA1NA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA8NA;AACA,IAAA,qBA/NA;AAAA,8GA+NA,OA/NA,EA+NA,MA/NA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiOA,gBAAA,OAAA,CAAA,GAAA,CAAA,uBAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA;AACA,qBAAA,QAAA,CAAA,OAAA,CAAA,cAAA,EAAA,CAAA,EAlOA,CAoOA;;AACA,gBAAA,WArOA,GAqOA;AACA,kBAAA,OAAA,EAAA,OADA;AAEA,kBAAA,MAAA,EAAA,MAFA;AAGA,kBAAA,OAAA,EAAA,YAHA;AAIA,kBAAA,IAAA,8CAAA,MAAA;AAJA,iBArOA;AA4OA,gBAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,WAAA;AA5OA;AAAA,uBA6OA,KAAA,KAAA,CAAA,IAAA,CAAA,2BAAA,EAAA,WAAA,CA7OA;;AAAA;AA6OA,gBAAA,WA7OA;AA8OA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,WAAA;AACA,qBAAA,QAAA,CAAA,OAAA;;AA/OA,qBAiPA,WAAA,CAAA,OAjPA;AAAA;AAAA;AAAA;;AAkPA,gBAAA,MAlPA,GAkPA,WAAA,CAAA,MAAA,CAAA,MAlPA;AAmPA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA;;AAnPA,oBAqPA,MArPA;AAAA;AAAA;AAAA;;AAsPA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA;AAtPA;;AAAA;AA0PA;AACA,qBAAA,eAAA,CAAA,MAAA,EAAA,OAAA,EAAA,MAAA;AA3PA;AAAA;;AAAA;AA8PA,gBAAA,OAAA,CAAA,KAAA,CAAA,cAAA,EAAA,WAAA,CAAA,OAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,IAAA,YAAA;;AA/PA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAmQA,qBAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,eAAA;;AArQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAyQA;AACA,IAAA,eA1QA,2BA0QA,MA1QA,EA0QA,OA1QA,EA0QA,MA1QA,EA0QA;AAAA;;AACA,WAAA,MAAA,CAAA,IAAA,CAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,GAFA;AAGA,QAAA,OAAA,EAAA,iBAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,SAAA,EAAA,QADA;AAEA,cAAA,OAAA,EAAA;AAFA;AADA,WAAA,EAKA,CACA,CAAA,CAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,YAAA,EAAA,MADA;AAEA,cAAA,QAAA,EAAA,MAFA;AAGA,cAAA,UAAA,EAAA;AAHA;AADA,WAAA,8CAMA,MANA,EADA,EAQA,CAAA,CAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,YAAA,EAAA,MADA;AAEA,cAAA,KAAA,EAAA;AAFA;AADA,WAAA,EAKA,mBALA,CARA,EAcA,CAAA,CAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,OAAA,EAAA,MADA;AAEA,cAAA,cAAA,EAAA,QAFA;AAGA,cAAA,YAAA,EAAA;AAHA;AADA,WAAA,EAMA,CACA,CAAA,CAAA,KAAA,EAAA;AACA,YAAA,GAAA,0EAAA,kBAAA,CAAA,MAAA,CAAA,CADA;AAEA,YAAA,KAAA,EAAA;AACA,cAAA,KAAA,EAAA,OADA;AAEA,cAAA,MAAA,EAAA,OAFA;AAGA,cAAA,MAAA,EAAA;AAHA;AAFA,WAAA,CADA,CANA,CAdA,EA8BA,CAAA,CAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,KAAA,EAAA,MADA;AAEA,cAAA,QAAA,EAAA;AAFA;AADA,WAAA,EAKA,cALA,CA9BA,CALA,CAAA;AAAA,SAHA;AA6CA,QAAA,IAAA,EAAA,gBAAA;AACA;AACA,UAAA,KAAA,CAAA,gBAAA,CAAA,OAAA;AACA;AAhDA,OAAA,EADA,CAoDA;;AACA,WAAA,uBAAA,CAAA,OAAA;AACA,KAhUA;AAkUA;AACA,IAAA,uBAnUA,mCAmUA,OAnUA,EAmUA;AAAA;;AACA,UAAA,YAAA,GAAA,WAAA,wEAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,MAAA,CAAA,KAAA,CAAA,GAAA,0CAAA,OAAA,EAFA;;AAAA;AAEA,gBAAA,QAFA;;AAGA,oBAAA,QAAA,CAAA,OAAA,IAAA,QAAA,CAAA,MAAA,CAAA,WAAA,KAAA,eAAA,EAAA;AACA,kBAAA,aAAA,CAAA,YAAA,CAAA;;AACA,kBAAA,MAAA,CAAA,MAAA,CAAA,UAAA;;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,kBAAA;;AACA,kBAAA,UAAA,CAAA,YAAA;AACA,oBAAA,MAAA,CAAA,QAAA;AACA,mBAFA,EAEA,IAFA,CAAA;AAGA;;AAVA;AAAA;;AAAA;AAAA;AAAA;AAYA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA,IAcA,IAdA,CAAA,CADA,CAeA;AAEA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,aAAA,CAAA,YAAA,CAAA;AACA,OAFA,EAEA,KAAA,EAAA,GAAA,IAFA,CAAA;AAGA,KAxVA;AA0VA;AACA,IAAA,gBA3VA;AAAA,yGA2VA,OA3VA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBA6VA,KAAA,KAAA,CAAA,GAAA,0CAAA,OAAA,EA7VA;;AAAA;AA6VA,gBAAA,QA7VA;;AA8VA,oBAAA,QAAA,CAAA,OAAA,IAAA,QAAA,CAAA,MAAA,CAAA,WAAA,KAAA,eAAA,EAAA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,kBAAA;AACA,kBAAA,UAAA,CAAA,YAAA;AACA,oBAAA,MAAA,CAAA,QAAA;AACA,mBAFA,EAEA,IAFA,CAAA;AAGA,iBALA,MAKA;AACA,uBAAA,QAAA,CAAA,IAAA,CAAA,gBAAA;AACA;;AArWA;AAAA;;AAAA;AAAA;AAAA;AAuWA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;;AAxWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA4WA;AACA,IAAA,mBA7WA,iCA6WA;AAAA;;AACA,UAAA,SAAA,GAAA,IAAA,eAAA,CAAA,MAAA,CAAA,QAAA,CAAA,MAAA,CAAA;AACA,UAAA,cAAA,GAAA,SAAA,CAAA,GAAA,CAAA,gBAAA,CAAA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,GAAA,CAAA,SAAA,CAAA;;AAEA,UAAA,cAAA,KAAA,MAAA,IAAA,OAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,OAAA,EADA,CAGA;;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,kBAAA,EAJA,CAMA;;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,MAAA,CAAA,QAAA;AACA,SAFA,EAEA,IAFA,CAAA,CAPA,CAWA;;AACA,YAAA,MAAA,GAAA,MAAA,CAAA,QAAA,CAAA,QAAA;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,YAAA,CAAA,EAAA,EAAA,QAAA,CAAA,KAAA,EAAA,MAAA;AACA;AACA,KAjYA;AAmYA;AACA,IAAA,kBApYA,8BAoYA,OApYA,EAoYA;AACA,WAAA,OAAA,GAAA,OAAA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,mBAAA;AACA,KAxYA;AA0YA,IAAA,iBA1YA,oCA0YA;AAAA,UAAA,UAAA,SAAA,UAAA;AACA,WAAA,UAAA,mCAAA,KAAA,UAAA,GAAA,UAAA;AACA,WAAA,mBAAA;AACA,KA7YA;AA+YA;AACA,IAAA,kBAhZA,gCAgZA;AACA;AACA,UAAA,KAAA,KAAA,CAAA,gBAAA,EAAA;AACA,aAAA,KAAA,CAAA,gBAAA,CAAA,YAAA;AACA,OAJA,CAKA;;;AACA,WAAA,OAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,CAAA,CAPA,CAQA;;AACA,WAAA,mBAAA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA,KA3ZA;AA6ZA;AACA,IAAA,wBA9ZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgaA,qBAAA,QAAA,CAAA,OAAA,CAAA,aAAA,EAAA,CAAA,EAhaA,CAkaA;;AACA,gBAAA,MAnaA,GAmaA;AACA,kBAAA,eAAA,EAAA,KAAA,OAAA,CAAA,IADA;AAEA,kBAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAFA;AAGA,kBAAA,OAAA,EAAA,KAAA,OAAA,CAAA;AAHA,iBAnaA,EAyaA;;AACA,oBAAA,KAAA,OAAA,CAAA,SAAA,IAAA,KAAA,OAAA,CAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,GAAA,KAAA,OAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA,OAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CAAA;AACA;;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,MAAA;AA/aA;AAAA,uBAibA,kBAAA,CAAA,MAAA,CAjbA;;AAAA;AAibA,gBAAA,QAjbA;AAkbA,qBAAA,QAAA,CAAA,OAAA,GAlbA,CAobA;;AACA,gBAAA,IArbA,GAqbA,IAAA,IAAA,CAAA,CAAA,QAAA,CAAA,EAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,CArbA;AAsbA,gBAAA,GAtbA,GAsbA,MAAA,CAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAtbA;AAubA,gBAAA,IAvbA,GAubA,QAAA,CAAA,aAAA,CAAA,GAAA,CAvbA;AAwbA,gBAAA,IAAA,CAAA,IAAA,GAAA,GAAA,CAxbA,CA0bA;;AACA,gBAAA,GA3bA,GA2bA,IAAA,IAAA,EA3bA;AA4bA,gBAAA,SA5bA,GA4bA,GAAA,CAAA,WAAA,KACA,MAAA,CAAA,GAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CADA,GAEA,MAAA,CAAA,GAAA,CAAA,OAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAFA,GAEA,GAFA,GAGA,MAAA,CAAA,GAAA,CAAA,QAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAHA,GAIA,MAAA,CAAA,GAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAhcA;AAkcA,gBAAA,IAAA,CAAA,QAAA,sCAAA,SAAA;AACA,gBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,gBAAA,IAAA,CAAA,KAAA;AACA,gBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,gBAAA,MAAA,CAAA,GAAA,CAAA,eAAA,CAAA,GAAA;AAEA,qBAAA,QAAA,CAAA,OAAA,CAAA,WAAA;AAxcA;AAAA;;AAAA;AAAA;AAAA;AA2cA,qBAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,cAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;;AA7cA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAidA,IAAA,2BAjdA,uCAidA,WAjdA,EAidA;AACA,WAAA,mBAAA,GAAA,WAAA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,KApdA;AAwdA;AACA,IAAA,uBAzdA,mCAydA,IAzdA,EAydA;AACA,UAAA,QAAA,GAAA;AACA,WAAA,cADA;AAEA,WAAA,eAFA;AAGA,WAAA,aAHA;AAIA,WAAA;AAJA,OAAA;AAMA,aAAA,QAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,KAjeA;AAmeA,IAAA,sBAneA,kCAmeA,IAneA,EAmeA;AACA,UAAA,OAAA,GAAA;AACA,WAAA,8BADA;AAEA,WAAA,6BAFA;AAGA,WAAA,sBAHA;AAIA,WAAA;AAJA,OAAA;AAMA,aAAA,OAAA,CAAA,IAAA,CAAA,IAAA,iCAAA;AACA,KA3eA;AA6eA,IAAA,sBA7eA,kCA6eA,IA7eA,EA6eA;AACA,UAAA,OAAA,GAAA;AACA,WAAA,IADA;AAEA,WAAA,IAFA;AAGA,WAAA,IAHA;AAIA,WAAA;AAJA,OAAA;AAMA,aAAA,OAAA,CAAA,IAAA,CAAA,IAAA,IAAA;AACA,KArfA;AAufA,IAAA,cAvfA,0BAufA,IAvfA,EAufA;AACA,aAAA;AACA,2BAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA,IAAA,CADA;AACA;AACA,2BAAA,IAAA,KAAA,CAFA,CAEA;;AAFA,OAAA;AAIA,KA5fA;AA8fA,IAAA,YA9fA,wBA8fA,MA9fA,EA8fA,IA9fA,EA8fA;AACA,UAAA,MAAA,GAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA,IAAA,IAAA,GAAA,GAAA,GAAA;AACA,uBAAA,MAAA,iBAAA,KAAA,YAAA,CAAA,IAAA,CAAA,GAAA,CAAA,MAAA,CAAA,CAAA;AACA,KAjgBA;AAmgBA,IAAA,YAngBA,wBAmgBA,MAngBA,EAmgBA;AACA,UAAA,CAAA,MAAA,EAAA,OAAA,MAAA;AACA,aAAA,UAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,KAtgBA;AAwgBA,IAAA,cAxgBA,0BAwgBA,UAxgBA,EAwgBA;AACA,UAAA,CAAA,UAAA,EAAA,OAAA,GAAA;;AAEA,UAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,UAAA,CAAA;AACA,eAAA,IAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,SADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,GAAA,EAAA,SAHA;AAIA,UAAA,IAAA,EAAA,SAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAAA,CAAA;AAOA,OATA,CASA,OAAA,KAAA,EAAA;AACA,eAAA,GAAA;AACA;AACA;AAvhBA;AAhOA,CAAA", "sourcesContent": ["<template>\n  <div class=\"credits-page\">\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">账户管理</h1>\n      <p class=\"page-description\">管理您的账户余额、查看交易记录和充值</p>\n    </div>\n\n    <div class=\"credits-content\">\n      <!-- 余额概览 -->\n      <div class=\"balance-overview\">\n        <div class=\"balance-cards\">\n          <StatsCard\n            :value=\"balanceData.currentBalance\"\n            unit=\"元\"\n            label=\"当前余额\"\n            icon=\"anticon anticon-wallet\"\n            icon-color=\"#10b981\"\n            :trend=\"balanceTrend\"\n            :loading=\"loading\"\n            @click=\"handleQuickRecharge\"\n          />\n          \n          <StatsCard\n            :value=\"balanceData.totalRecharge\"\n            unit=\"元\"\n            label=\"累计充值\"\n            icon=\"anticon anticon-plus-circle\"\n            icon-color=\"#7c8aed\"\n            :loading=\"loading\"\n          />\n          \n          <StatsCard\n            :value=\"balanceData.totalConsumption\"\n            unit=\"元\"\n            label=\"累计消费\"\n            icon=\"anticon anticon-minus-circle\"\n            icon-color=\"#ef4444\"\n            :loading=\"loading\"\n          />\n          \n          <StatsCard\n            :value=\"balanceData.monthlyConsumption\"\n            unit=\"元\"\n            label=\"本月消费\"\n            icon=\"anticon anticon-bar-chart\"\n            icon-color=\"#f59e0b\"\n            :trend=\"monthlyTrend\"\n            :loading=\"loading\"\n          />\n        </div>\n\n        <!-- 快速充值 -->\n        <div class=\"quick-recharge\">\n          <h3 class=\"section-title\">快速充值</h3>\n          <div class=\"recharge-options\">\n            <div\n              v-for=\"option in rechargeOptions\"\n              :key=\"option.amount\"\n              class=\"recharge-option\"\n              :class=\"{ selected: selectedAmount === option.amount }\"\n              @click=\"selectRechargeAmount(option.amount)\"\n            >\n              <div class=\"option-amount\">¥{{ option.amount }}</div>\n            </div>\n          </div>\n          \n          <div class=\"custom-amount\">\n            <a-input-number\n              v-model=\"customAmount\"\n              :min=\"0.01\"\n              :max=\"10000\"\n              :step=\"0.01\"\n              placeholder=\"自定义金额（最低0.01元）\"\n              size=\"large\"\n              style=\"width: 200px\"\n              @change=\"onCustomAmountChange\"\n            />\n            <a-button \n              type=\"primary\" \n              size=\"large\"\n              :loading=\"rechargeLoading\"\n              @click=\"handleRecharge\"\n            >\n              立即充值\n            </a-button>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 交易记录 -->\n      <DataTable\n        ref=\"transactionTable\"\n        title=\"交易记录\"\n        :data-source=\"transactionList\"\n        :columns=\"transactionColumns\"\n        :loading=\"transactionLoading\"\n        :pagination=\"pagination\"\n        :show-action-column=\"false\"\n        :type-options=\"transactionTypeOptions\"\n        :status-options=\"[]\"\n        :show-search=\"true\"\n        type-filter-placeholder=\"交易类型\"\n        status-filter-placeholder=\"交易状态\"\n        search-placeholder=\"搜索交易描述\"\n        :date-filter-placeholder=\"['交易时间', '交易时间']\"\n        @filter-change=\"handleFilterChange\"\n        @table-change=\"handleTableChange\"\n        @refresh=\"loadTransactionData\"\n      >\n        <!-- 自定义操作按钮 -->\n        <template #actions>\n          <a-button\n            @click=\"handleResetFilters\"\n            style=\"margin-right: 8px; background: linear-gradient(135deg, #64748b 0%, #475569 100%); border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3); color: white;\"\n          >\n            <a-icon type=\"reload\" style=\"margin-right: 6px;\" />\n            重置\n          </a-button>\n          <a-button\n            type=\"primary\"\n            @click=\"handleExportTransactions\"\n            style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\"\n          >\n            <a-icon type=\"download\" style=\"margin-right: 6px;\" />\n            导出交易记录\n          </a-button>\n        </template>\n      </DataTable>\n    </div>\n\n    <!-- 充值确认模态框 -->\n    <a-modal\n      v-model=\"showRechargeModal\"\n      title=\"确认充值\"\n      :footer=\"null\"\n      width=\"500px\"\n    >\n      <div class=\"recharge-confirm\">\n        <div class=\"confirm-info\">\n          <div class=\"info-row\">\n            <span class=\"info-label\">充值金额：</span>\n            <span class=\"info-value\">¥{{ finalRechargeAmount }}</span>\n          </div>\n          <div class=\"info-row total\">\n            <span class=\"info-label\">到账金额：</span>\n            <span class=\"info-value\">¥{{ finalRechargeAmount }}</span>\n          </div>\n        </div>\n\n        <div class=\"payment-methods\">\n          <h4>支付方式</h4>\n          <a-radio-group v-model=\"selectedPaymentMethod\" size=\"large\">\n            <a-radio-button value=\"alipay-page\">\n              <i class=\"anticon anticon-alipay\"></i>\n              支付宝网页\n            </a-radio-button>\n          </a-radio-group>\n        </div>\n\n        <div class=\"modal-actions\">\n          <a-button @click=\"showRechargeModal = false\">取消</a-button>\n          <a-button \n            type=\"primary\" \n            :loading=\"paymentLoading\"\n            @click=\"handleConfirmRecharge\"\n          >\n            确认支付\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n\n    <!-- 交易详情模态框 -->\n    <a-modal\n      v-model=\"showTransactionDetail\"\n      title=\"交易详情\"\n      :footer=\"null\"\n      width=\"600px\"\n    >\n      <div class=\"transaction-detail\" v-if=\"selectedTransaction\">\n        <div class=\"detail-header\">\n          <div class=\"transaction-type\" :class=\"getTransactionTypeClass(selectedTransaction.transactionType)\">\n            <i :class=\"getTransactionTypeIcon(selectedTransaction.transactionType)\"></i>\n            {{ getTransactionTypeText(selectedTransaction.transactionType) }}\n          </div>\n          <div class=\"transaction-amount\" :class=\"getAmountClass(selectedTransaction.transactionType)\">\n            {{ formatAmount(selectedTransaction.amount, selectedTransaction.transactionType) }}\n          </div>\n        </div>\n\n        <div class=\"detail-content\">\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易单号：</span>\n            <span class=\"detail-value\">{{ selectedTransaction.id }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易时间：</span>\n            <span class=\"detail-value\">{{ formatDateTime(selectedTransaction.transactionTime) }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易描述：</span>\n            <span class=\"detail-value\">{{ selectedTransaction.description }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易前余额：</span>\n            <span class=\"detail-value\">¥{{ formatNumber(selectedTransaction.balanceBefore) }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易后余额：</span>\n            <span class=\"detail-value\">¥{{ formatNumber(selectedTransaction.balanceAfter) }}</span>\n          </div>\n          <div class=\"detail-row\" v-if=\"selectedTransaction.relatedOrderId\">\n            <span class=\"detail-label\">关联订单：</span>\n            <span class=\"detail-value\">{{ selectedTransaction.relatedOrderId }}</span>\n          </div>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport StatsCard from '../components/StatsCard.vue'\nimport DataTable from '../components/DataTable.vue'\nimport {\n  getTransactionList,\n  getTransactionStats,\n  createRechargeOrder,\n  exportTransactions\n  // getRechargeOptions, // 不再需要，使用前端固定配置\n  // getConsumptionChart // 临时注释，等后端接口实现\n} from '@/api/usercenter'\nimport { authMixin } from '@/mixins/authMixin'\n\nexport default {\n  name: 'UserCenterCredits',\n  mixins: [authMixin],\n  components: {\n    StatsCard,\n    DataTable\n  },\n  data() {\n    return {\n      loading: true,\n      transactionLoading: false,\n      chartLoading: false,\n      rechargeLoading: false,\n      paymentLoading: false,\n      \n      // 余额数据\n      balanceData: {\n        currentBalance: 0,\n        totalRecharge: 0,\n        totalConsumption: 0,\n        monthlyConsumption: 0\n      },\n      \n      // 充值相关 - 固定配置\n      rechargeOptions: [\n        { amount: 50 },\n        { amount: 100 },\n        { amount: 300 },\n        { amount: 500 },\n        { amount: 1000 }\n      ],\n      selectedAmount: 0,\n      customAmount: null,\n      showRechargeModal: false,\n      selectedPaymentMethod: 'alipay-page', // 默认选择网页支付\n      \n\n      \n      // 交易记录\n      transactionList: [],\n      pagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      filters: {},\n\n      // 表格列配置\n      transactionColumns: [\n        {\n          title: '交易时间',\n          dataIndex: 'transactionTime',\n          key: 'transactionTime',\n          width: 180,\n          align: 'center',\n          customRender: (text) => {\n            if (!text) return '-'\n            try {\n              let date\n              if (typeof text === 'number') {\n                date = new Date(text)\n              } else {\n                date = new Date(text)\n              }\n              return date.toLocaleString('zh-CN', {\n                year: 'numeric',\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            } catch (error) {\n              return '-'\n            }\n          }\n        },\n        {\n          title: '交易类型',\n          dataIndex: 'transactionType',\n          key: 'transactionType',\n          width: 120,\n          align: 'center',\n          customRender: (text) => {\n            console.log('🔍 交易类型 customRender - 接收到的值:', text, '类型:', typeof text)\n            const typeMap = {\n              1: '消费',\n              2: '充值',\n              3: '退款',\n              4: '奖励',\n              5: '会员订阅',\n              '1': '消费',\n              '2': '充值',\n              '3': '退款',\n              '4': '奖励',\n              '5': '会员订阅'\n            }\n            const result = typeMap[text] || text || '-'\n            console.log('🔍 交易类型 customRender - 返回结果:', result)\n            return result\n          }\n        },\n        {\n          title: '订单号',\n          dataIndex: 'relatedOrderId',\n          key: 'relatedOrderId',\n          width: 200,\n          align: 'center',\n          customRender: (text) => {\n            return text || '-'\n          }\n        },\n        {\n          title: '交易描述',\n          dataIndex: 'description',\n          key: 'description',\n          align: 'center',\n          ellipsis: true\n        },\n        {\n          title: '交易金额',\n          dataIndex: 'amount',\n          key: 'amount',\n          width: 120,\n          align: 'center',\n          customRender: (text, record) => {\n            const amount = parseFloat(text || 0).toFixed(2)\n            const isPositive = [2, 3, 4].includes(Number(record.transactionType)) // 充值、退款、奖励为正\n            const prefix = isPositive ? '+' : '-'\n            const colorStyle = isPositive ? 'color: #52c41a; font-weight: 600;' : 'color: #ff4d4f; font-weight: 600;'\n            return <span style={colorStyle}>{prefix}¥{amount}</span>\n          }\n        },\n        {\n          title: '余额',\n          dataIndex: 'balanceAfter',\n          key: 'balanceAfter',\n          width: 120,\n          align: 'center',\n          customRender: (text) => {\n            const balance = parseFloat(text || 0).toFixed(2)\n            const balanceStyle = 'color: #1890ff; font-weight: 600;'\n            return <span style={balanceStyle}>¥{balance}</span>\n          }\n        },\n        {\n          title: '状态',\n          dataIndex: 'orderStatus',\n          key: 'orderStatus',\n          width: 100,\n          align: 'center',\n          customRender: (text) => {\n            // 后端查询条件已经限制为 order_status = 3（已完成）\n            // 所以这里的数据都应该是已完成的交易\n\n            // 如果有明确的状态字段，使用映射\n            if (text !== undefined && text !== null) {\n              const statusMap = {\n                1: { text: '待支付', style: 'color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                2: { text: '已支付', style: 'color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                3: { text: '已完成', style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                4: { text: '已取消', style: 'color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                5: { text: '已退款', style: 'color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '1': { text: '待支付', style: 'color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '2': { text: '已支付', style: 'color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '3': { text: '已完成', style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '4': { text: '已取消', style: 'color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '5': { text: '已退款', style: 'color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' }\n              }\n              const status = statusMap[text] || { text: '已完成', style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' }\n              return <span style={status.style}>{status.text}</span>\n            }\n\n            // 如果没有状态字段，默认为已完成\n            // 因为后端查询已经过滤为已完成的交易\n            const defaultStyle = 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n            return <span style={defaultStyle}>已完成</span>\n          }\n        }\n      ],\n\n      // 筛选选项\n      transactionTypeOptions: [\n        { value: 1, label: '消费' },\n        { value: 2, label: '充值' },\n        { value: 3, label: '退款' },\n        { value: 4, label: '奖励' },\n        { value: 5, label: '会员订阅' }\n      ],\n      // 🚫 取消状态筛选选项\n      // transactionStatusOptions: [\n      //   { value: 1, label: '待支付' },\n      //   { value: 3, label: '已完成' },\n      //   { value: 4, label: '已取消' },\n      //   { value: 5, label: '已退款' }\n      // ],\n\n      // 交易详情\n      showTransactionDetail: false,\n      selectedTransaction: null\n    }\n  },\n  computed: {\n    balanceTrend() {\n      // 根据实际数据计算趋势（暂时返回null，等待后端提供趋势数据）\n      // TODO: 后端需要提供上月对比数据来计算真实趋势\n      return null\n    },\n\n    monthlyTrend() {\n      // 根据实际数据计算月度趋势（暂时返回null，等待后端提供趋势数据）\n      // TODO: 后端需要提供月度对比数据来计算真实趋势\n      return null\n    },\n    \n    finalRechargeAmount() {\n      return this.selectedAmount || this.customAmount || 0\n    }\n  },\n  async mounted() {\n    await this.loadData()\n\n    // 检查是否是支付成功返回\n    this.checkPaymentSuccess()\n  },\n  methods: {\n    async loadData() {\n      try {\n        this.loading = true\n        \n        // 并行加载数据\n        await Promise.all([\n          this.loadBalanceData(),\n          this.loadTransactionData()\n        ])\n      } catch (error) {\n        console.error('加载账户管理数据失败:', error)\n        this.$message.error('加载数据失败，请刷新重试')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async loadBalanceData() {\n      try {\n        const response = await getTransactionStats()\n        if (response.success) {\n          // 修复：使用 result 字段并正确映射字段名\n          const stats = response.result || {}\n\n          // 🔧 修复字段映射问题\n          this.balanceData = {\n            currentBalance: stats.accountBalance || 0,        // 后端返回accountBalance，前端期望currentBalance\n            totalRecharge: stats.totalRecharge || 0,          // 字段名一致\n            totalConsumption: stats.totalConsumption || 0,    // 字段名一致\n            monthlyConsumption: stats.monthlyConsumption || 0, // 如果后端有这个字段\n            transactionCount: stats.transactionCount || 0     // 交易记录总数\n          }\n\n          console.log('🔍 Credits页面 - 余额数据映射结果:', this.balanceData)\n          console.log('🔍 Credits页面 - 后端原始数据:', stats)\n        }\n      } catch (error) {\n        console.error('加载余额数据失败:', error)\n      }\n    },\n    \n    async loadTransactionData() {\n      try {\n        this.transactionLoading = true\n\n        const params = {\n          current: this.pagination.current,\n          size: this.pagination.pageSize,\n          ...this.filters\n        }\n\n        // 🔧 处理日期范围筛选\n        if (this.filters.dateRange && this.filters.dateRange.length === 2) {\n          params.startDate = this.filters.dateRange[0].format('YYYY-MM-DD')\n          params.endDate = this.filters.dateRange[1].format('YYYY-MM-DD')\n          console.log('🔍 日期范围筛选 - 原始dateRange:', this.filters.dateRange)\n          console.log('🔍 日期范围筛选 - startDate:', params.startDate, 'endDate:', params.endDate)\n          // 移除dateRange，避免传递给后端\n          delete params.dateRange\n        }\n\n        console.log('🔍 Credits页面 - 最终查询参数:', params)\n\n        const response = await getTransactionList(params)\n        if (response.success) {\n          // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法\n          const records = (response.result && response.result.records) || []\n\n          // 调试：打印后端返回的数据结构\n          console.log('🔍 Credits页面 - 后端返回的完整响应:', response)\n          console.log('🔍 Credits页面 - 交易记录数组:', records)\n          if (records.length > 0) {\n            console.log('🔍 Credits页面 - 第一条记录详情:', records[0])\n            console.log('🔍 Credits页面 - 第一条记录的所有字段:', Object.keys(records[0]))\n            console.log('🔍 Credits页面 - transactionType字段值:', records[0].transactionType, '类型:', typeof records[0].transactionType)\n            console.log('🔍 Credits页面 - status字段值:', records[0].status, '类型:', typeof records[0].status)\n\n          // 检查所有可能的状态相关字段\n          console.log('🔍 Credits页面 - 检查状态相关字段:')\n          console.log('  - status:', records[0].status)\n          console.log('  - transactionStatus:', records[0].transactionStatus)\n          console.log('  - orderStatus:', records[0].orderStatus)\n          console.log('  - paymentStatus:', records[0].paymentStatus)\n          console.log('  - state:', records[0].state)\n          console.log('  - delFlag:', records[0].delFlag)\n          }\n\n          this.transactionList = records\n          this.pagination.total = (response.result && response.result.total) || 0\n        }\n      } catch (error) {\n        console.error('加载交易记录失败:', error)\n        this.transactionList = []\n      } finally {\n        this.transactionLoading = false\n      }\n    },\n\n\n    \n\n    \n    // 充值相关方法\n    selectRechargeAmount(amount) {\n      this.selectedAmount = amount\n      this.customAmount = null\n    },\n\n    // 自定义金额变化\n    onCustomAmountChange(value) {\n      if (value && value > 0) {\n        this.selectedAmount = 0 // 清空预设金额选择\n      }\n    },\n    \n    handleQuickRecharge() {\n      // 重置选择状态\n      this.selectedAmount = 0\n      this.customAmount = null\n      // 显示充值模态框\n      this.showRechargeModal = true\n    },\n    \n    handleRecharge() {\n      if (!this.finalRechargeAmount || this.finalRechargeAmount < 0.01) {\n        this.$message.warning('请选择或输入充值金额，最低0.01元')\n        return\n      }\n\n      this.showRechargeModal = true\n    },\n    \n    async handleConfirmRecharge() {\n      try {\n        this.paymentLoading = true\n\n        const orderData = {\n          amount: this.finalRechargeAmount,\n          paymentMethod: this.selectedPaymentMethod\n        }\n\n        const response = await createRechargeOrder(orderData)\n        if (response.success) {\n          const result = response.result\n\n          // 只支持支付宝网页支付\n          await this.handleAlipayPagePayment(result.orderId, result.amount)\n\n          this.showRechargeModal = false\n        } else {\n          this.$message.error(response.message || '创建充值订单失败')\n        }\n      } catch (error) {\n        console.error('创建充值订单失败:', error)\n        this.$message.error('充值失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付宝网页支付\n    async handleAlipayPagePayment(orderId, amount) {\n      try {\n        console.log('🔍 开始处理支付宝支付 - 订单号:', orderId, '金额:', amount)\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        // 调用支付宝支付接口\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        console.log('🔍 发送支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        console.log('🔍 支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const payForm = payResponse.result.payForm\n          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')\n\n          if (!payForm) {\n            this.$message.error('支付表单为空')\n            return\n          }\n\n          // 创建表单并提交到支付宝\n          const div = document.createElement('div')\n          div.innerHTML = payForm\n          document.body.appendChild(div)\n\n          const form = div.querySelector('form')\n          if (form) {\n            console.log('🔍 找到支付表单，准备提交')\n            form.submit()\n          } else {\n            console.error('🔍 未找到支付表单')\n            this.$message.error('支付表单创建失败')\n          }\n\n          // 清理DOM\n          setTimeout(() => {\n            if (document.body.contains(div)) {\n              document.body.removeChild(div)\n            }\n          }, 1000)\n\n        } else {\n          console.error('🔍 支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建支付订单失败')\n        }\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝支付失败:', error)\n        this.$message.error('支付宝支付失败，请重试')\n      }\n    },\n\n    // 处理支付宝扫码支付\n    async handleAlipayQrPayment(orderId, amount) {\n      try {\n        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', orderId, '金额:', amount)\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        // 调用支付宝扫码支付接口\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        console.log('🔍 发送扫码支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        console.log('🔍 扫码支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')\n\n          if (!qrCode) {\n            this.$message.error('支付二维码生成失败')\n            return\n          }\n\n          // 显示二维码支付弹窗\n          this.showQrCodeModal(qrCode, orderId, amount)\n\n        } else {\n          console.error('🔍 扫码支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建扫码支付订单失败')\n        }\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝扫码支付失败:', error)\n        this.$message.error('支付宝扫码支付失败，请重试')\n      }\n    },\n\n    // 显示二维码支付弹窗\n    showQrCodeModal(qrCode, orderId, amount) {\n      this.$modal.info({\n        title: '支付宝扫码支付',\n        width: 400,\n        content: h => h('div', {\n          style: {\n            textAlign: 'center',\n            padding: '20px'\n          }\n        }, [\n          h('div', {\n            style: {\n              marginBottom: '16px',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            }\n          }, `充值金额：¥${amount}`),\n          h('div', {\n            style: {\n              marginBottom: '16px',\n              color: '#666'\n            }\n          }, '请使用支付宝扫描下方二维码完成支付'),\n          h('div', {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              marginBottom: '16px'\n            }\n          }, [\n            h('img', {\n              src: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`,\n              style: {\n                width: '200px',\n                height: '200px',\n                border: '1px solid #d9d9d9'\n              }\n            })\n          ]),\n          h('div', {\n            style: {\n              color: '#999',\n              fontSize: '12px'\n            }\n          }, '支付完成后页面将自动跳转')\n        ]),\n        onOk: () => {\n          // 用户点击确定后，可以查询订单状态\n          this.checkOrderStatus(orderId)\n        }\n      })\n\n      // 定时查询订单状态\n      this.startOrderStatusPolling(orderId)\n    },\n\n    // 开始轮询订单状态\n    startOrderStatusPolling(orderId) {\n      const pollInterval = setInterval(async () => {\n        try {\n          const response = await this.$http.get(`/api/alipay/queryOrder?orderId=${orderId}`)\n          if (response.success && response.result.tradeStatus === 'TRADE_SUCCESS') {\n            clearInterval(pollInterval)\n            this.$modal.destroyAll()\n            this.$message.success('支付成功！正在更新账户余额...')\n            setTimeout(() => {\n              this.loadData()\n            }, 1000)\n          }\n        } catch (error) {\n          console.error('查询订单状态失败:', error)\n        }\n      }, 3000) // 每3秒查询一次\n\n      // 15分钟后停止轮询\n      setTimeout(() => {\n        clearInterval(pollInterval)\n      }, 15 * 60 * 1000)\n    },\n\n    // 检查订单状态\n    async checkOrderStatus(orderId) {\n      try {\n        const response = await this.$http.get(`/api/alipay/queryOrder?orderId=${orderId}`)\n        if (response.success && response.result.tradeStatus === 'TRADE_SUCCESS') {\n          this.$message.success('支付成功！正在更新账户余额...')\n          setTimeout(() => {\n            this.loadData()\n          }, 1000)\n        } else {\n          this.$message.info('订单尚未支付，请继续扫码支付')\n        }\n      } catch (error) {\n        console.error('查询订单状态失败:', error)\n        this.$message.error('查询订单状态失败')\n      }\n    },\n\n    // 检查支付成功状态\n    checkPaymentSuccess() {\n      const urlParams = new URLSearchParams(window.location.search)\n      const paymentSuccess = urlParams.get('paymentSuccess')\n      const orderId = urlParams.get('orderId')\n\n      if (paymentSuccess === 'true' && orderId) {\n        console.log('🎉 检测到支付成功返回 - 订单号:', orderId)\n\n        // 显示支付成功消息\n        this.$message.success('支付成功！正在更新账户余额...')\n\n        // 刷新数据\n        setTimeout(() => {\n          this.loadData()\n        }, 1000)\n\n        // 清理URL参数\n        const newUrl = window.location.pathname\n        window.history.replaceState({}, document.title, newUrl)\n      }\n    },\n\n    // 表格相关方法\n    handleFilterChange(filters) {\n      this.filters = filters\n      this.pagination.current = 1\n      this.loadTransactionData()\n    },\n\n    handleTableChange({ pagination }) {\n      this.pagination = { ...this.pagination, ...pagination }\n      this.loadTransactionData()\n    },\n\n    // 重置筛选条件\n    handleResetFilters() {\n      // 调用DataTable组件的重置方法\n      if (this.$refs.transactionTable) {\n        this.$refs.transactionTable.resetFilters()\n      }\n      // 重置本地筛选条件\n      this.filters = {}\n      this.pagination.current = 1\n      // 重新加载数据\n      this.loadTransactionData()\n      this.$message.success('筛选条件已重置')\n    },\n\n    // 导出交易记录\n    async handleExportTransactions() {\n      try {\n        this.$message.loading('正在导出交易记录...', 0)\n\n        // 使用当前的筛选条件导出\n        const params = {\n          transactionType: this.filters.type,\n          status: this.filters.status,\n          keyword: this.filters.keyword\n        }\n\n        // 处理日期范围\n        if (this.filters.dateRange && this.filters.dateRange.length === 2) {\n          params.startDate = this.filters.dateRange[0].format('YYYY-MM-DD')\n          params.endDate = this.filters.dateRange[1].format('YYYY-MM-DD')\n        }\n\n        console.log('🎯 导出交易记录参数:', params)\n\n        const response = await exportTransactions(params)\n        this.$message.destroy()\n\n        // 创建下载链接\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n\n        // 生成文件名\n        const now = new Date()\n        const timestamp = now.getFullYear() +\n          String(now.getMonth() + 1).padStart(2, '0') +\n          String(now.getDate()).padStart(2, '0') + '_' +\n          String(now.getHours()).padStart(2, '0') +\n          String(now.getMinutes()).padStart(2, '0')\n\n        link.download = `交易记录_${timestamp}.xlsx`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n        this.$message.success('交易记录导出成功！')\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('🎯 导出交易记录失败:', error)\n        this.$message.error('导出失败，请重试')\n      }\n    },\n    \n    handleViewTransactionDetail(transaction) {\n      this.selectedTransaction = transaction\n      this.showTransactionDetail = true\n    },\n    \n\n    \n    // 工具方法\n    getTransactionTypeClass(type) {\n      const classMap = {\n        1: 'type-consume',\n        2: 'type-recharge',\n        3: 'type-refund',\n        4: 'type-reward'\n      }\n      return classMap[type] || ''\n    },\n    \n    getTransactionTypeIcon(type) {\n      const iconMap = {\n        1: 'anticon anticon-minus-circle',\n        2: 'anticon anticon-plus-circle',\n        3: 'anticon anticon-undo',\n        4: 'anticon anticon-gift'\n      }\n      return iconMap[type] || 'anticon anticon-question-circle'\n    },\n    \n    getTransactionTypeText(type) {\n      const textMap = {\n        1: '消费',\n        2: '充值',\n        3: '退款',\n        4: '奖励'\n      }\n      return textMap[type] || '未知'\n    },\n    \n    getAmountClass(type) {\n      return {\n        'amount-positive': [2, 3, 4].includes(type), // 充值、退款、奖励为正\n        'amount-negative': type === 1 // 消费为负\n      }\n    },\n    \n    formatAmount(amount, type) {\n      const prefix = [2, 3, 4].includes(type) ? '+' : '-'\n      return `${prefix}¥${this.formatNumber(Math.abs(amount))}`\n    },\n    \n    formatNumber(number) {\n      if (!number) return '0.00'\n      return parseFloat(number).toFixed(2)\n    },\n    \n    formatDateTime(dateString) {\n      if (!dateString) return '-'\n      \n      try {\n        const date = new Date(dateString)\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      } catch (error) {\n        return '-'\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.credits-page {\n  padding: 2rem;\n}\n\n.page-header {\n  margin-bottom: 3rem;\n}\n\n.page-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #334155;\n  margin: 0 0 0.5rem 0;\n}\n\n.page-description {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n.credits-content {\n  display: flex;\n  flex-direction: column;\n  gap: 3rem;\n}\n\n/* 余额概览 */\n.balance-overview {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 3rem;\n}\n\n.balance-cards {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 2rem;\n}\n\n/* 快速充值 */\n.quick-recharge {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(124, 138, 237, 0.1);\n  height: fit-content;\n}\n\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #334155;\n  margin: 0 0 2rem 0;\n}\n\n.recharge-options {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.recharge-option {\n  padding: 1rem;\n  border: 2px solid rgba(124, 138, 237, 0.1);\n  border-radius: 12px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: rgba(124, 138, 237, 0.02);\n}\n\n.recharge-option:hover {\n  border-color: rgba(124, 138, 237, 0.3);\n  background: rgba(124, 138, 237, 0.05);\n}\n\n.recharge-option.selected {\n  border-color: #7c8aed;\n  background: rgba(124, 138, 237, 0.1);\n}\n\n.option-amount {\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #334155;\n  margin-bottom: 0.25rem;\n}\n\n\n\n\n\n.custom-amount {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n\n\n/* 充值确认模态框 */\n.recharge-confirm {\n  padding: 1rem 0;\n}\n\n.confirm-info {\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: rgba(124, 138, 237, 0.05);\n  border-radius: 12px;\n}\n\n.info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.info-row:last-child {\n  margin-bottom: 0;\n}\n\n.info-row.total {\n  padding-top: 1rem;\n  border-top: 1px solid rgba(124, 138, 237, 0.1);\n  font-weight: 600;\n}\n\n.info-label {\n  color: #64748b;\n}\n\n.info-value {\n  color: #334155;\n  font-weight: 500;\n}\n\n.info-value.bonus {\n  color: #ef4444;\n}\n\n.payment-methods {\n  margin-bottom: 2rem;\n}\n\n.payment-methods h4 {\n  margin-bottom: 1rem;\n  color: #334155;\n}\n\n.payment-option {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.payment-icon {\n  width: 20px;\n  height: 20px;\n  background-size: contain;\n  background-repeat: no-repeat;\n}\n\n.payment-icon.alipay {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzAwOUZFOCIvPgo8L3N2Zz4K');\n}\n\n.payment-icon.wechat {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzA5QjEzMiIvPgo8L3N2Zz4K');\n}\n\n.payment-icon.bank {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzY0NzQ4QiIvPgo8L3N2Zz4K');\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n}\n\n/* 交易详情模态框 */\n.transaction-detail {\n  padding: 1rem 0;\n}\n\n.detail-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.transaction-type {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.type-recharge {\n  background: rgba(16, 185, 129, 0.1);\n  color: #10b981;\n}\n\n.type-consume {\n  background: rgba(239, 68, 68, 0.1);\n  color: #ef4444;\n}\n\n.type-refund {\n  background: rgba(245, 158, 11, 0.1);\n  color: #f59e0b;\n}\n\n.type-reward {\n  background: rgba(124, 138, 237, 0.1);\n  color: #7c8aed;\n}\n\n.transaction-amount {\n  font-size: 1.5rem;\n  font-weight: 700;\n  font-family: 'Courier New', monospace;\n}\n\n.amount-positive {\n  color: #10b981;\n}\n\n.amount-negative {\n  color: #ef4444;\n}\n\n.detail-content {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.detail-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem 0;\n  border-bottom: 1px solid rgba(124, 138, 237, 0.05);\n}\n\n.detail-row:last-child {\n  border-bottom: none;\n}\n\n.detail-label {\n  color: #64748b;\n  font-weight: 500;\n}\n\n.detail-value {\n  color: #334155;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .balance-overview {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n  \n  .balance-cards {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n  .credits-page {\n    padding: 1rem;\n  }\n  \n  .balance-cards {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .recharge-options {\n    grid-template-columns: 1fr;\n  }\n  \n  .custom-amount {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .chart-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/usercenter/views"}]}