(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~d71bf088"],{"10e8e":function(e,t,a){"use strict";a.d(t,"a",(function(){return Q}));var o=a("7fae"),n=a("cbe5"),i=a("c7a2"),r=a("48a9"),l=a("deca"),s=a("7d6c"),d=a("e887"),u=a("7837"),c=a("861c"),h=a("6d8b"),p=function(){function e(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}return e}(),g=function(e){function t(t){return e.call(this,t)||this}return Object(o["a"])(t,e),t.prototype.getDefaultShape=function(){return new p},t.prototype.buildPath=function(e,t){var a=t.extent;e.moveTo(t.x1,t.y1),e.bezierCurveTo(t.cpx1,t.cpy1,t.cpx2,t.cpy2,t.x2,t.y2),"vertical"===t.orient?(e.lineTo(t.x2+a,t.y2),e.bezierCurveTo(t.cpx2+a,t.cpy2,t.cpx1+a,t.cpy1,t.x1+a,t.y1)):(e.lineTo(t.x2,t.y2+a),e.bezierCurveTo(t.cpx2,t.cpy2+a,t.cpx1,t.cpy1+a,t.x1,t.y1+a)),e.closePath()},t.prototype.highlight=function(){Object(s["r"])(this)},t.prototype.downplay=function(){Object(s["C"])(this)},t}(n["b"]),f=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a._focusAdjacencyDisabled=!1,a}return Object(o["a"])(t,e),t.prototype.render=function(e,t,a){var o=this,n=e.getGraph(),r=this.group,l=e.layoutInfo,d=l.width,p=l.height,f=e.getData(),m=e.getData("edge"),b=e.get("orient");this._model=e,r.removeAll(),r.x=l.x,r.y=l.y,n.eachEdge((function(t){var a=new g,o=Object(c["a"])(a);o.dataIndex=t.dataIndex,o.seriesIndex=e.seriesIndex,o.dataType="edge";var n,i,l,f,v,x,w,I,L=t.getModel(),_=L.getModel("lineStyle"),O=_.get("curveness"),M=t.node1.getLayout(),N=t.node1.getModel(),S=N.get("localX"),j=N.get("localY"),D=t.node2.getLayout(),T=t.node2.getModel(),R=T.get("localX"),C=T.get("localY"),V=t.getLayout();a.shape.extent=Math.max(1,V.dy),a.shape.orient=b,"vertical"===b?(n=(null!=S?S*d:M.x)+V.sy,i=(null!=j?j*p:M.y)+M.dy,l=(null!=R?R*d:D.x)+V.ty,f=null!=C?C*p:D.y,v=n,x=i*(1-O)+f*O,w=l,I=i*O+f*(1-O)):(n=(null!=S?S*d:M.x)+M.dx,i=(null!=j?j*p:M.y)+V.sy,l=null!=R?R*d:D.x,f=(null!=C?C*p:D.y)+V.ty,v=n*(1-O)+l*O,x=i,w=n*O+l*(1-O),I=f),a.setShape({x1:n,y1:i,x2:l,y2:f,cpx1:v,cpy1:x,cpx2:w,cpy2:I}),a.useStyle(_.getItemStyle()),y(a.style,b,t);var k=""+L.get("value"),A=Object(u["e"])(L,"edgeLabel");Object(u["g"])(a,A,{labelFetcher:{getFormattedLabel:function(t,a,o,n,i,r){return e.getFormattedLabel(t,a,"edge",n,Object(h["Q"])(i,A.normal&&A.normal.get("formatter"),k),r)}},labelDataIndex:t.dataIndex,defaultText:k}),a.setTextConfig({position:"inside"});var E=L.getModel("emphasis");Object(s["I"])(a,L,"lineStyle",(function(e){var a=e.getItemStyle();return y(a,b,t),a})),r.add(a),m.setItemGraphicEl(t.dataIndex,a);var P=E.get("focus");Object(s["J"])(a,"adjacency"===P?t.getAdjacentDataIndices():"trajectory"===P?t.getTrajectoryDataIndices():P,E.get("blurScope"),E.get("disabled"))})),n.eachNode((function(t){var a=t.getLayout(),o=t.getModel(),n=o.get("localX"),l=o.get("localY"),h=o.getModel("emphasis"),g=o.get(["itemStyle","borderRadius"])||0,y=new i["a"]({shape:{x:null!=n?n*d:a.x,y:null!=l?l*p:a.y,width:a.dx,height:a.dy,r:g},style:o.getModel("itemStyle").getItemStyle(),z2:10});Object(u["g"])(y,Object(u["e"])(o),{labelFetcher:{getFormattedLabel:function(t,a){return e.getFormattedLabel(t,a,"node")}},labelDataIndex:t.dataIndex,defaultText:t.id}),y.disableLabelAnimation=!0,y.setStyle("fill",t.getVisual("color")),y.setStyle("decal",t.getVisual("style").decal),Object(s["I"])(y,o),r.add(y),f.setItemGraphicEl(t.dataIndex,y),Object(c["a"])(y).dataType="node";var v=h.get("focus");Object(s["J"])(y,"adjacency"===v?t.getAdjacentDataIndices():"trajectory"===v?t.getTrajectoryDataIndices():v,h.get("blurScope"),h.get("disabled"))})),f.eachItemGraphicEl((function(t,n){var i=f.getItemModel(n);i.get("draggable")&&(t.drift=function(t,i){o._focusAdjacencyDisabled=!0,this.shape.x+=t,this.shape.y+=i,this.dirty(),a.dispatchAction({type:"dragNode",seriesId:e.id,dataIndex:f.getRawIndex(n),localX:this.shape.x/d,localY:this.shape.y/p})},t.ondragend=function(){o._focusAdjacencyDisabled=!1},t.draggable=!0,t.cursor="move")})),!this._data&&e.isAnimationEnabled()&&r.setClipPath(v(r.getBoundingRect(),e,(function(){r.removeClipPath()}))),this._data=e.getData()},t.prototype.dispose=function(){},t.type="sankey",t}(d["a"]);function y(e,t,a){switch(e.fill){case"source":e.fill=a.node1.getVisual("color"),e.decal=a.node1.getVisual("style").decal;break;case"target":e.fill=a.node2.getVisual("color"),e.decal=a.node2.getVisual("style").decal;break;case"gradient":var o=a.node1.getVisual("color"),n=a.node2.getVisual("color");Object(h["C"])(o)&&Object(h["C"])(n)&&(e.fill=new r["a"](0,0,+("horizontal"===t),+("vertical"===t),[{color:o,offset:0},{color:n,offset:1}]))}}function v(e,t,a){var o=new i["a"]({shape:{x:e.x-10,y:e.y-10,width:0,height:e.height+20}});return l["c"](o,{shape:{width:e.width+20}},t,a),o}var m=f,b=a("4f85"),x=a("237f"),w=a("4319"),I=a("217c"),L=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a}return Object(o["a"])(t,e),t.prototype.getInitialData=function(e,t){var a=e.edges||e.links||[],o=e.data||e.nodes||[],n=e.levels||[];this.levelModels=[];for(var i=this.levelModels,r=0;r<n.length;r++)null!=n[r].depth&&n[r].depth>=0&&(i[n[r].depth]=new w["a"](n[r],this,t));var l=Object(x["a"])(o,a,this,!0,s);return l.data;function s(e,t){e.wrapMethod("getItemModel",(function(e,t){var a=e.parentModel,o=a.getData().getItemLayout(t);if(o){var n=o.depth,i=a.levelModels[n];i&&(e.parentModel=i)}return e})),t.wrapMethod("getItemModel",(function(e,t){var a=e.parentModel,o=a.getGraph().getEdgeByIndex(t),n=o.node1.getLayout();if(n){var i=n.depth,r=a.levelModels[i];r&&(e.parentModel=r)}return e}))}},t.prototype.setNodePosition=function(e,t){var a=this.option.data||this.option.nodes,o=a[e];o.localX=t[0],o.localY=t[1]},t.prototype.getGraph=function(){return this.getData().graph},t.prototype.getEdgeData=function(){return this.getGraph().edgeData},t.prototype.formatTooltip=function(e,t,a){function o(e){return isNaN(e)||null==e}if("edge"===a){var n=this.getDataParams(e,a),i=n.data,r=n.value,l=i.source+" -- "+i.target;return Object(I["c"])("nameValue",{name:l,value:r,noValue:o(r)})}var s=this.getGraph().getNodeByIndex(e),d=s.getLayout().value,u=this.getDataParams(e,a).data.name;return Object(I["c"])("nameValue",{name:null!=u?u+"":null,value:d,noValue:o(d)})},t.prototype.optionUpdated=function(){},t.prototype.getDataParams=function(t,a){var o=e.prototype.getDataParams.call(this,t,a);if(null==o.value&&"node"===a){var n=this.getGraph().getNodeByIndex(t),i=n.getLayout().value;o.value=i}return o},t.type="series.sankey",t.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},t}(b["b"]),_=L,O=a("f934"),M=a("e0d3");function N(e,t){e.eachSeriesByType("sankey",(function(e){var a=e.get("nodeWidth"),o=e.get("nodeGap"),n=S(e,t);e.layoutInfo=n;var i=n.width,r=n.height,l=e.getGraph(),s=l.nodes,d=l.edges;D(s);var u=h["n"](s,(function(e){return 0===e.getLayout().value})),c=0!==u.length?0:e.get("layoutIterations"),p=e.get("orient"),g=e.get("nodeAlign");j(s,d,a,o,i,r,c,p,g)}))}function S(e,t){return O["g"](e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()})}function j(e,t,a,o,n,i,r,l,s){T(e,t,a,n,i,l,s),A(e,t,i,n,o,r,l),q(e,l)}function D(e){h["k"](e,(function(e){var t=Z(e.outEdges,F),a=Z(e.inEdges,F),o=e.getValue()||0,n=Math.max(t,a,o);e.setLayout({value:n},!0)}))}function T(e,t,a,o,n,i,r){for(var l=[],s=[],d=[],u=[],c=0,h=0;h<t.length;h++)l[h]=1;for(h=0;h<e.length;h++)s[h]=e[h].inEdges.length,0===s[h]&&d.push(e[h]);var p=-1;while(d.length){for(var g=0;g<d.length;g++){var f=d[g],y=f.hostGraph.data.getRawDataItem(f.dataIndex),v=null!=y.depth&&y.depth>=0;v&&y.depth>p&&(p=y.depth),f.setLayout({depth:v?y.depth:c},!0),"vertical"===i?f.setLayout({dy:a},!0):f.setLayout({dx:a},!0);for(var m=0;m<f.outEdges.length;m++){var b=f.outEdges[m],x=t.indexOf(b);l[x]=0;var w=b.node2,I=e.indexOf(w);0===--s[I]&&u.indexOf(w)<0&&u.push(w)}}++c,d=u,u=[]}for(h=0;h<l.length;h++)if(1===l[h])throw new Error("Sankey is a DAG, the original data has cycle!");var L=p>c-1?p:c-1;r&&"left"!==r&&C(e,r,i,L);var _="vertical"===i?(n-a)/L:(o-a)/L;k(e,_,i)}function R(e){var t=e.hostGraph.data.getRawDataItem(e.dataIndex);return null!=t.depth&&t.depth>=0}function C(e,t,a,o){if("right"===t){var n=[],i=e,r=0;while(i.length){for(var l=0;l<i.length;l++){var s=i[l];s.setLayout({skNodeHeight:r},!0);for(var d=0;d<s.inEdges.length;d++){var u=s.inEdges[d];n.indexOf(u.node1)<0&&n.push(u.node1)}}i=n,n=[],++r}h["k"](e,(function(e){R(e)||e.setLayout({depth:Math.max(0,o-e.getLayout().skNodeHeight)},!0)}))}else"justify"===t&&V(e,o)}function V(e,t){h["k"](e,(function(e){R(e)||e.outEdges.length||e.setLayout({depth:t},!0)}))}function k(e,t,a){h["k"](e,(function(e){var o=e.getLayout().depth*t;"vertical"===a?e.setLayout({y:o},!0):e.setLayout({x:o},!0)}))}function A(e,t,a,o,n,i,r){var l=E(e,r);P(l,t,a,o,n,r),G(l,n,a,o,r);for(var s=1;i>0;i--)s*=.99,z(l,s,r),G(l,n,a,o,r),U(l,s,r),G(l,n,a,o,r)}function E(e,t){var a=[],o="vertical"===t?"y":"x",n=Object(M["j"])(e,(function(e){return e.getLayout()[o]}));return n.keys.sort((function(e,t){return e-t})),h["k"](n.keys,(function(e){a.push(n.buckets.get(e))})),a}function P(e,t,a,o,n,i){var r=1/0;h["k"](e,(function(e){var t=e.length,l=0;h["k"](e,(function(e){l+=e.getLayout().value}));var s="vertical"===i?(o-(t-1)*n)/l:(a-(t-1)*n)/l;s<r&&(r=s)})),h["k"](e,(function(e){h["k"](e,(function(e,t){var a=e.getLayout().value*r;"vertical"===i?(e.setLayout({x:t},!0),e.setLayout({dx:a},!0)):(e.setLayout({y:t},!0),e.setLayout({dy:a},!0))}))})),h["k"](t,(function(e){var t=+e.getValue()*r;e.setLayout({dy:t},!0)}))}function G(e,t,a,o,n){var i="vertical"===n?"x":"y";h["k"](e,(function(e){var r,l,s;e.sort((function(e,t){return e.getLayout()[i]-t.getLayout()[i]}));for(var d=0,u=e.length,c="vertical"===n?"dx":"dy",h=0;h<u;h++)l=e[h],s=d-l.getLayout()[i],s>0&&(r=l.getLayout()[i]+s,"vertical"===n?l.setLayout({x:r},!0):l.setLayout({y:r},!0)),d=l.getLayout()[i]+l.getLayout()[c]+t;var p="vertical"===n?o:a;if(s=d-t-p,s>0){r=l.getLayout()[i]-s,"vertical"===n?l.setLayout({x:r},!0):l.setLayout({y:r},!0),d=r;for(h=u-2;h>=0;--h)l=e[h],s=l.getLayout()[i]+l.getLayout()[c]+t-d,s>0&&(r=l.getLayout()[i]-s,"vertical"===n?l.setLayout({x:r},!0):l.setLayout({y:r},!0)),d=l.getLayout()[i]}}))}function z(e,t,a){h["k"](e.slice().reverse(),(function(e){h["k"](e,(function(e){if(e.outEdges.length){var o=Z(e.outEdges,B,a)/Z(e.outEdges,F);if(isNaN(o)){var n=e.outEdges.length;o=n?Z(e.outEdges,H,a)/n:0}if("vertical"===a){var i=e.getLayout().x+(o-Y(e,a))*t;e.setLayout({x:i},!0)}else{var r=e.getLayout().y+(o-Y(e,a))*t;e.setLayout({y:r},!0)}}}))}))}function B(e,t){return Y(e.node2,t)*e.getValue()}function H(e,t){return Y(e.node2,t)}function W(e,t){return Y(e.node1,t)*e.getValue()}function X(e,t){return Y(e.node1,t)}function Y(e,t){return"vertical"===t?e.getLayout().x+e.getLayout().dx/2:e.getLayout().y+e.getLayout().dy/2}function F(e){return e.getValue()}function Z(e,t,a){var o=0,n=e.length,i=-1;while(++i<n){var r=+t(e[i],a);isNaN(r)||(o+=r)}return o}function U(e,t,a){h["k"](e,(function(e){h["k"](e,(function(e){if(e.inEdges.length){var o=Z(e.inEdges,W,a)/Z(e.inEdges,F);if(isNaN(o)){var n=e.inEdges.length;o=n?Z(e.inEdges,X,a)/n:0}if("vertical"===a){var i=e.getLayout().x+(o-Y(e,a))*t;e.setLayout({x:i},!0)}else{var r=e.getLayout().y+(o-Y(e,a))*t;e.setLayout({y:r},!0)}}}))}))}function q(e,t){var a="vertical"===t?"x":"y";h["k"](e,(function(e){e.outEdges.sort((function(e,t){return e.node2.getLayout()[a]-t.node2.getLayout()[a]})),e.inEdges.sort((function(e,t){return e.node1.getLayout()[a]-t.node1.getLayout()[a]}))})),h["k"](e,(function(e){var t=0,a=0;h["k"](e.outEdges,(function(e){e.setLayout({sy:t},!0),t+=e.getLayout().dy})),h["k"](e.inEdges,(function(e){e.setLayout({ty:a},!0),a+=e.getLayout().dy}))}))}var J=a("5f14");function K(e){e.eachSeriesByType("sankey",(function(e){var t=e.getGraph(),a=t.nodes,o=t.edges;if(a.length){var n=1/0,i=-1/0;h["k"](a,(function(e){var t=e.getLayout().value;t<n&&(n=t),t>i&&(i=t)})),h["k"](a,(function(t){var a=new J["a"]({type:"color",mappingMethod:"linear",dataExtent:[n,i],visual:e.get("color")}),o=a.mapValueToVisual(t.getLayout().value),r=t.getModel().get(["itemStyle","color"]);null!=r?(t.setVisual("color",r),t.setVisual("style",{fill:r})):(t.setVisual("color",o),t.setVisual("style",{fill:o}))}))}o.length&&h["k"](o,(function(e){var t=e.getModel().get("lineStyle");e.setVisual("style",t)}))}))}function Q(e){e.registerChartView(m),e.registerSeriesModel(_),e.registerLayout(N),e.registerVisual(K),e.registerAction({type:"dragNode",event:"dragnode",update:"update"},(function(e,t){t.eachComponent({mainType:"series",subType:"sankey",query:e},(function(t){t.setNodePosition(e.dataIndex,[e.localX,e.localY])}))}))}},"128d":function(e,t,a){"use strict";a.d(t,"a",(function(){return Pe}));var o=a("55ac"),n=a("6d8b"),i=["treemapZoomToNode","treemapRender","treemapMove"];function r(e){for(var t=0;t<i.length;t++)e.registerAction({type:i[t],update:"updateView"},n["L"]);e.registerAction({type:"treemapRootToNode",update:"updateView"},(function(e,t){function a(t,a){var n=["treemapZoomToNode","treemapRootToNode"],i=o["c"](e,n,t);if(i){var r=t.getViewRoot();r&&(e.direction=o["a"](r,i.node)?"rollUp":"drillDown"),t.resetViewRoot(i.node)}}t.eachComponent({mainType:"series",subType:"treemap",query:e},a)}))}var l=a("7fae"),s=a("4f85"),d=a("06c7"),u=a("4319"),c=a("e0d3"),h=a("217c"),p=a("933c"),g=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a.preventUsingHoverLayer=!0,a}return Object(l["a"])(t,e),t.prototype.getInitialData=function(e,t){var a={name:e.name,children:e.data};f(a);var o=e.levels||[],i=this.designatedVisualItemStyle={},r=new u["a"]({itemStyle:i},this,t);o=e.levels=y(o,t);var l=n["H"](o||[],(function(e){return new u["a"](e,r,t)}),this),s=d["a"].createTree(a,this,c);function c(e){e.wrapMethod("getItemModel",(function(e,t){var a=s.getNodeByDataIndex(t),o=a?l[a.depth]:null;return e.parentModel=o||r,e}))}return s.data},t.prototype.optionUpdated=function(){this.resetViewRoot()},t.prototype.formatTooltip=function(e,t,a){var o=this.getData(),n=this.getRawValue(e),i=o.getName(e);return Object(h["c"])("nameValue",{name:i,value:n})},t.prototype.getDataParams=function(t){var a=e.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return a.treeAncestors=Object(o["d"])(n,this),a.treePathInfo=a.treeAncestors,a},t.prototype.setLayoutInfo=function(e){this.layoutInfo=this.layoutInfo||{},n["m"](this.layoutInfo,e)},t.prototype.mapIdToIndex=function(e){var t=this._idIndexMap;t||(t=this._idIndexMap=n["f"](),this._idIndexMapCount=0);var a=t.get(e);return null==a&&t.set(e,a=this._idIndexMapCount++),a},t.prototype.getViewRoot=function(){return this._viewRoot},t.prototype.resetViewRoot=function(e){e?this._viewRoot=e:e=this._viewRoot;var t=this.getRawData().tree.root;e&&(e===t||t.contains(e))||(this._viewRoot=t)},t.prototype.enableAriaDecal=function(){Object(p["a"])(this)},t.type="series.treemap",t.layoutMode="box",t.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.1024,scaleLimit:null,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},t}(s["b"]);function f(e){var t=0;n["k"](e.children,(function(e){f(e);var a=e.value;n["t"](a)&&(a=a[0]),t+=a}));var a=e.value;n["t"](a)&&(a=a[0]),(null==a||isNaN(a))&&(a=t),a<0&&(a=0),n["t"](e.value)?e.value[0]=a:e.value=a}function y(e,t){var a=Object(c["r"])(t.get("color")),o=Object(c["r"])(t.get(["aria","decal","decals"]));if(a){var i,r;e=e||[],n["k"](e,(function(e){var t=new u["a"](e),a=t.get("color"),o=t.get("decal");(t.get(["itemStyle","color"])||a&&"none"!==a)&&(i=!0),(t.get(["itemStyle","decal"])||o&&"none"!==o)&&(r=!0)}));var l=e[0]||(e[0]={});return i||(l.color=a.slice()),!r&&o&&(l.decal=o.slice()),e}}var v=g,m=a("2dc5"),b=a("c7a2"),x=a("861c"),w=a("7d6c"),I=a("80f0"),L=a("87b1"),_=a("76a5"),O=a("f934"),M=a("7837"),N=8,S=8,j=5,D=function(){function e(e){this.group=new m["a"],e.add(this.group)}return e.prototype.render=function(e,t,a,o){var n=e.getModel("breadcrumb"),i=this.group;if(i.removeAll(),n.get("show")&&a){var r=n.getModel("itemStyle"),l=n.getModel("emphasis"),s=r.getModel("textStyle"),d=l.getModel(["itemStyle","textStyle"]),u={pos:{left:n.get("left"),right:n.get("right"),top:n.get("top"),bottom:n.get("bottom")},box:{width:t.getWidth(),height:t.getHeight()},emptyItemWidth:n.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(a,u,s),this._renderContent(e,u,r,l,s,d,o),O["i"](i,u.pos,u.box)}},e.prototype._prepare=function(e,t,a){for(var o=e;o;o=o.parentNode){var n=Object(c["e"])(o.getModel().get("name"),""),i=a.getTextRect(n),r=Math.max(i.width+2*N,t.emptyItemWidth);t.totalWidth+=r+S,t.renderList.push({node:o,text:n,width:r})}},e.prototype._renderContent=function(e,t,a,o,i,r,l){for(var s=0,d=t.emptyItemWidth,u=e.get(["breadcrumb","height"]),c=O["e"](t.pos,t.box),h=t.totalWidth,p=t.renderList,g=o.getModel("itemStyle").getItemStyle(),f=p.length-1;f>=0;f--){var y=p[f],v=y.node,m=y.width,b=y.text;h>c.width&&(h-=m-d,m=d,b=null);var x=new L["a"]({shape:{points:T(s,0,m,u,f===p.length-1,0===f)},style:Object(n["i"])(a.getItemStyle(),{lineJoin:"bevel"}),textContent:new _["a"]({style:Object(M["c"])(i,{text:b})}),textConfig:{position:"inside"},z2:1e4*w["j"],onclick:Object(n["h"])(l,v)});x.disableLabelAnimation=!0,x.getTextContent().ensureState("emphasis").style=Object(M["c"])(r,{text:b}),x.ensureState("emphasis").style=g,Object(w["J"])(x,o.get("focus"),o.get("blurScope"),o.get("disabled")),this.group.add(x),R(x,e,v),s+=m+S}},e.prototype.remove=function(){this.group.removeAll()},e}();function T(e,t,a,o,n,i){var r=[[n?e:e-j,t],[e+a,t],[e+a,t+o],[n?e:e-j,t+o]];return!i&&r.splice(2,0,[e+a+j,t+o/2]),!n&&r.push([e,t+o/2]),r}function R(e,t,a){Object(x["a"])(e).eventData={componentType:"series",componentSubType:"treemap",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:a&&a.dataIndex,name:a&&a.name},treePathInfo:a&&Object(o["d"])(a,t)}}var C=D,V=a("4a01"),k=a("9850"),A=a("1687"),E=a("e6cd"),P=a("282b"),G=a("e887"),z=a("19eb"),B=a("eda2"),H=m["a"],W=b["a"],X=3,Y="label",F="upperLabel",Z=10*w["j"],U=2*w["j"],q=3*w["j"],J=Object(P["a"])([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),K=function(e){var t=J(e);return t.stroke=t.fill=t.lineWidth=null,t},Q=Object(c["o"])(),$=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a._state="ready",a._storage=ee(),a}return Object(l["a"])(t,e),t.prototype.render=function(e,t,a,i){var r=t.findComponents({mainType:"series",subType:"treemap",query:i});if(!(Object(n["r"])(r,e)<0)){this.seriesModel=e,this.api=a,this.ecModel=t;var l=["treemapZoomToNode","treemapRootToNode"],s=o["c"](i,l,e),d=i&&i.type,u=e.layoutInfo,c=!this._oldTree,h=this._storage,p="treemapRootToNode"===d&&s&&h?{rootNodeGroup:h.nodeGroup[s.node.getRawIndex()],direction:i.direction}:null,g=this._giveContainerGroup(u),f=e.get("animation"),y=this._doRender(g,e,p);!f||c||d&&"treemapZoomToNode"!==d&&"treemapRootToNode"!==d?y.renderFinally():this._doAnimation(g,y,e,p),this._resetController(a),this._renderBreadcrumb(e,a,s)}},t.prototype._giveContainerGroup=function(e){var t=this._containerGroup;return t||(t=this._containerGroup=new H,this._initEvents(t),this.group.add(t)),t.x=e.x,t.y=e.y,t},t.prototype._doRender=function(e,t,a){var o=t.getData().tree,i=this._oldTree,r=ee(),l=ee(),s=this._storage,d=[];function u(e,o,n,i){return te(t,l,s,a,r,d,e,o,n,i)}g(o.root?[o.root]:[],i&&i.root?[i.root]:[],e,o===i||!i,0);var c=f(s);if(this._oldTree=o,this._storage=l,this._controllerHost){var h=this.seriesModel.layoutInfo,p=o.root.getLayout();p.width===h.width&&p.height===h.height&&(this._controllerHost.zoom=1)}return{lastsForAnimation:r,willDeleteEls:c,renderFinally:y};function g(e,t,a,o,i){function r(e){return e.getId()}function l(n,r){var l=null!=n?e[n]:null,s=null!=r?t[r]:null,d=u(l,s,a,i);d&&g(l&&l.viewChildren||[],s&&s.viewChildren||[],d,o,i+1)}o?(t=e,Object(n["k"])(e,(function(e,t){!e.isRemoved()&&l(t,t)}))):new I["a"](t,e,r,r).add(l).update(l).remove(Object(n["h"])(l,null)).execute()}function f(e){var t=ee();return e&&Object(n["k"])(e,(function(e,a){var o=t[a];Object(n["k"])(e,(function(e){e&&(o.push(e),Q(e).willDelete=!0)}))})),t}function y(){Object(n["k"])(c,(function(e){Object(n["k"])(e,(function(e){e.parent&&e.parent.remove(e)}))})),Object(n["k"])(d,(function(e){e.invisible=!0,e.dirty()}))}},t.prototype._doAnimation=function(e,t,a,o){var i=a.get("animationDurationUpdate"),r=a.get("animationEasing"),l=(Object(n["w"])(i)?0:i)||0,s=(Object(n["w"])(r)?null:r)||"cubicOut",d=E["a"]();Object(n["k"])(t.willDeleteEls,(function(e,t){Object(n["k"])(e,(function(e,a){if(!e.invisible){var n,i=e.parent,r=Q(i);if(o&&"drillDown"===o.direction)n=i===o.rootNodeGroup?{shape:{x:0,y:0,width:r.nodeWidth,height:r.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var u=0,c=0;r.willDelete||(u=r.nodeWidth/2,c=r.nodeHeight/2),n="nodeGroup"===t?{x:u,y:c,style:{opacity:0}}:{shape:{x:u,y:c,width:0,height:0},style:{opacity:0}}}n&&d.add(e,n,l,0,s)}}))})),Object(n["k"])(this._storage,(function(e,a){Object(n["k"])(e,(function(e,o){var i=t.lastsForAnimation[a][o],r={};i&&(e instanceof m["a"]?null!=i.oldX&&(r.x=e.x,r.y=e.y,e.x=i.oldX,e.y=i.oldY):(i.oldShape&&(r.shape=Object(n["m"])({},e.shape),e.setShape(i.oldShape)),i.fadein?(e.setStyle("opacity",0),r.style={opacity:1}):1!==e.style.opacity&&(r.style={opacity:1})),d.add(e,r,l,0,s))}))}),this),this._state="animating",d.finished(Object(n["c"])((function(){this._state="ready",t.renderFinally()}),this)).start()},t.prototype._resetController=function(e){var t=this._controller,a=this._controllerHost;a||(this._controllerHost={target:this.group},a=this._controllerHost),t||(t=this._controller=new V["a"](e.getZr()),t.enable(this.seriesModel.get("roam")),a.zoomLimit=this.seriesModel.get("scaleLimit"),a.zoom=this.seriesModel.get("zoom"),t.on("pan",Object(n["c"])(this._onPan,this)),t.on("zoom",Object(n["c"])(this._onZoom,this)));var o=new k["a"](0,0,e.getWidth(),e.getHeight());t.setPointerChecker((function(e,t,a){return o.contain(t,a)}))},t.prototype._clearController=function(){var e=this._controller;this._controllerHost=null,e&&(e.dispose(),e=null)},t.prototype._onPan=function(e){if("animating"!==this._state&&(Math.abs(e.dx)>X||Math.abs(e.dy)>X)){var t=this.seriesModel.getData().tree.root;if(!t)return;var a=t.getLayout();if(!a)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:a.x+e.dx,y:a.y+e.dy,width:a.width,height:a.height}})}},t.prototype._onZoom=function(e){var t=e.originX,a=e.originY,o=e.scale;if("animating"!==this._state){var n=this.seriesModel.getData().tree.root;if(!n)return;var i=n.getLayout();if(!i)return;var r=new k["a"](i.x,i.y,i.width,i.height),l=null,s=this._controllerHost;l=s.zoomLimit;var d=s.zoom=s.zoom||1;if(d*=o,l){var u=l.min||0,c=l.max||1/0;d=Math.max(Math.min(c,d),u)}var h=d/s.zoom;s.zoom=d;var p=this.seriesModel.layoutInfo;t-=p.x,a-=p.y;var g=A["c"]();A["i"](g,g,[-t,-a]),A["h"](g,g,[h,h]),A["i"](g,g,[t,a]),r.applyTransform(g),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:r.x,y:r.y,width:r.width,height:r.height}})}},t.prototype._initEvents=function(e){var t=this;e.on("click",(function(e){if("ready"===t._state){var a=t.seriesModel.get("nodeClick",!0);if(a){var o=t.findTarget(e.offsetX,e.offsetY);if(o){var n=o.node;if(n.getLayout().isLeafRoot)t._rootToNode(o);else if("zoomToNode"===a)t._zoomToNode(o);else if("link"===a){var i=n.hostTree.data.getItemModel(n.dataIndex),r=i.get("link",!0),l=i.get("target",!0)||"blank";r&&Object(B["i"])(r,l)}}}}}),this)},t.prototype._renderBreadcrumb=function(e,t,a){var n=this;a||(a=null!=e.get("leafDepth",!0)?{node:e.getViewRoot()}:this.findTarget(t.getWidth()/2,t.getHeight()/2),a||(a={node:e.getData().tree.root})),(this._breadcrumb||(this._breadcrumb=new C(this.group))).render(e,t,a.node,(function(t){"animating"!==n._state&&(o["a"](e.getViewRoot(),t)?n._rootToNode({node:t}):n._zoomToNode({node:t}))}))},t.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage=ee(),this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},t.prototype.dispose=function(){this._clearController()},t.prototype._zoomToNode=function(e){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:e.node})},t.prototype._rootToNode=function(e){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:e.node})},t.prototype.findTarget=function(e,t){var a,o=this.seriesModel.getViewRoot();return o.eachNode({attr:"viewChildren",order:"preorder"},(function(o){var n=this._storage.background[o.getRawIndex()];if(n){var i=n.transformCoordToLocal(e,t),r=n.shape;if(!(r.x<=i[0]&&i[0]<=r.x+r.width&&r.y<=i[1]&&i[1]<=r.y+r.height))return!1;a={node:o,offsetX:i[0],offsetY:i[1]}}}),this),a},t.type="treemap",t}(G["a"]);function ee(){return{nodeGroup:[],background:[],content:[]}}function te(e,t,a,o,i,r,l,s,d,u){if(l){var h=l.getLayout(),p=e.getData(),g=l.getModel();if(p.setItemGraphicEl(l.dataIndex,null),h&&h.isInView){var f=h.width,y=h.height,v=h.borderWidth,b=h.invisible,I=l.getRawIndex(),L=s&&s.getRawIndex(),_=l.viewChildren,O=h.upperHeight,N=_&&_.length,S=g.getModel("itemStyle"),j=g.getModel(["emphasis","itemStyle"]),D=g.getModel(["blur","itemStyle"]),T=g.getModel(["select","itemStyle"]),R=S.get("borderRadius")||0,C=ne("nodeGroup",H);if(C){if(d.add(C),C.x=h.x||0,C.y=h.y||0,C.markRedraw(),Q(C).nodeWidth=f,Q(C).nodeHeight=y,h.isAboveViewRoot)return C;var V=ne("background",W,u,U);V&&Z(C,V,N&&h.upperLabelHeight);var k=g.getModel("emphasis"),A=k.get("focus"),E=k.get("blurScope"),P=k.get("disabled"),G="ancestor"===A?l.getAncestorsIndices():"descendant"===A?l.getDescendantIndices():A;if(N)Object(w["y"])(C)&&Object(w["F"])(C,!1),V&&(Object(w["F"])(V,!P),p.setItemGraphicEl(l.dataIndex,V),Object(w["p"])(V,G,E));else{var B=ne("content",W,u,q);B&&$(C,B),V.disableMorphing=!0,V&&Object(w["y"])(V)&&Object(w["F"])(V,!1),Object(w["F"])(C,!P),p.setItemGraphicEl(l.dataIndex,C);var X=g.getShallow("cursor");X&&B.attr("cursor",X),Object(w["p"])(C,G,E)}return C}}}function Z(t,a,o){var n=Object(x["a"])(a);if(n.dataIndex=l.dataIndex,n.seriesIndex=e.seriesIndex,a.setShape({x:0,y:0,width:f,height:y,r:R}),b)ee(a);else{a.invisible=!1;var i=l.getVisual("style"),r=i.stroke,s=K(S);s.fill=r;var d=J(j);d.fill=j.get("borderColor");var u=J(D);u.fill=D.get("borderColor");var c=J(T);if(c.fill=T.get("borderColor"),o){var h=f-2*v;te(a,r,i.opacity,{x:v,y:0,width:h,height:O})}else a.removeTextContent();a.setStyle(s),a.ensureState("emphasis").style=d,a.ensureState("blur").style=u,a.ensureState("select").style=c,Object(w["G"])(a)}t.add(a)}function $(t,a){var o=Object(x["a"])(a);o.dataIndex=l.dataIndex,o.seriesIndex=e.seriesIndex;var n=Math.max(f-2*v,0),i=Math.max(y-2*v,0);if(a.culling=!0,a.setShape({x:v,y:v,width:n,height:i,r:R}),b)ee(a);else{a.invisible=!1;var r=l.getVisual("style"),s=r.fill,d=K(S);d.fill=s,d.decal=r.decal;var u=J(j),c=J(D),h=J(T);te(a,s,r.opacity,null),a.setStyle(d),a.ensureState("emphasis").style=u,a.ensureState("blur").style=c,a.ensureState("select").style=h,Object(w["G"])(a)}t.add(a)}function ee(e){!e.invisible&&r.push(e)}function te(t,a,o,i){var r=g.getModel(i?F:Y),s=Object(c["e"])(g.get("name"),null),d=r.getShallow("show");Object(M["g"])(t,Object(M["e"])(g,i?F:Y),{defaultText:d?s:null,inheritColor:a,defaultOpacity:o,labelFetcher:e,labelDataIndex:l.dataIndex});var u=t.getTextContent();if(u){var p=u.style,f=Object(n["M"])(p.padding||0);i&&(t.setTextConfig({layoutRect:i}),u.disableLabelLayout=!0),u.beforeUpdate=function(){var e=Math.max((i?i.width:t.shape.width)-f[1]-f[3],0),a=Math.max((i?i.height:t.shape.height)-f[0]-f[2],0);p.width===e&&p.height===a||u.setStyle({width:e,height:a})},p.truncateMinChar=2,p.lineOverflow="truncate",oe(p,i,h);var y=u.getState("emphasis");oe(y?y.style:null,i,h)}}function oe(t,a,o){var n=t?t.text:null;if(!a&&o.isLeafRoot&&null!=n){var i=e.get("drillDownIcon",!0);t.text=i?i+" "+n:n}}function ne(e,o,n,r){var l=null!=L&&a[e][L],s=i[e];return l?(a[e][L]=null,ie(s,l)):b||(l=new o,l instanceof z["c"]&&(l.z2=ae(n,r)),re(s,l)),t[e][I]=l}function ie(e,t){var a=e[I]={};t instanceof H?(a.oldX=t.x,a.oldY=t.y):a.oldShape=Object(n["m"])({},t.shape)}function re(e,t){var a=e[I]={},n=l.parentNode,r=t instanceof m["a"];if(n&&(!o||"drillDown"===o.direction)){var s=0,d=0,u=i.background[n.getRawIndex()];!o&&u&&u.oldShape&&(s=u.oldShape.width,d=u.oldShape.height),r?(a.oldX=0,a.oldY=d):a.oldShape={x:s,y:d,width:0,height:0}}a.fadein=!r}}function ae(e,t){return e*Z+t}var oe=$,ne=a("5f14"),ie=a("41ef"),re="itemStyle",le=Object(c["o"])(),se={seriesType:"treemap",reset:function(e){var t=e.getData().tree,a=t.root;a.isRemoved()||de(a,{},e.getViewRoot().getAncestors(),e)}};function de(e,t,a,o){var i=e.getModel(),r=e.getLayout(),l=e.hostTree.data;if(r&&!r.invisible&&r.isInView){var s,d=i.getModel(re),u=ue(d,t,o),c=l.ensureUniqueItemVisual(e.dataIndex,"style"),h=d.get("borderColor"),p=d.get("borderColorSaturation");null!=p&&(s=ce(u),h=he(p,s)),c.stroke=h;var g=e.viewChildren;if(g&&g.length){var f=ge(e,i,r,d,u,g);Object(n["k"])(g,(function(e,t){if(e.depth>=a.length||e===a[e.depth]){var n=ye(i,u,e,t,f,o);de(e,n,a,o)}}))}else s=ce(u),c.fill=s}}function ue(e,t,a){var o=Object(n["m"])({},t),i=a.designatedVisualItemStyle;return Object(n["k"])(["color","colorAlpha","colorSaturation"],(function(a){i[a]=t[a];var n=e.get(a);i[a]=null,null!=n&&(o[a]=n)})),o}function ce(e){var t=pe(e,"color");if(t){var a=pe(e,"colorAlpha"),o=pe(e,"colorSaturation");return o&&(t=Object(ie["g"])(t,null,null,o)),a&&(t=Object(ie["f"])(t,a)),t}}function he(e,t){return null!=t?Object(ie["g"])(t,null,null,e):null}function pe(e,t){var a=e[t];if(null!=a&&"none"!==a)return a}function ge(e,t,a,o,n,i){if(i&&i.length){var r=fe(t,"color")||null!=n.color&&"none"!==n.color&&(fe(t,"colorAlpha")||fe(t,"colorSaturation"));if(r){var l=t.get("visualMin"),s=t.get("visualMax"),d=a.dataExtent.slice();null!=l&&l<d[0]&&(d[0]=l),null!=s&&s>d[1]&&(d[1]=s);var u=t.get("colorMappingBy"),c={type:r.name,dataExtent:d,visual:r.range};"color"!==c.type||"index"!==u&&"id"!==u?c.mappingMethod="linear":(c.mappingMethod="category",c.loop=!0);var h=new ne["a"](c);return le(h).drColorMappingBy=u,h}}}function fe(e,t){var a=e.get(t);return Object(n["t"])(a)&&a.length?{name:t,range:a}:null}function ye(e,t,a,o,i,r){var l=Object(n["m"])({},t);if(i){var s=i.type,d="color"===s&&le(i).drColorMappingBy,u="index"===d?o:"id"===d?r.mapIdToIndex(a.getId()):a.getValue(e.get("visualDimension"));l[s]=i.mapValueToVisual(u)}return l}var ve=a("3842"),me=Math.max,be=Math.min,xe=n["O"],we=n["k"],Ie=["itemStyle","borderWidth"],Le=["itemStyle","gapWidth"],_e=["upperLabel","show"],Oe=["upperLabel","height"],Me={seriesType:"treemap",reset:function(e,t,a,i){var r=a.getWidth(),l=a.getHeight(),s=e.option,d=O["g"](e.getBoxLayoutParams(),{width:a.getWidth(),height:a.getHeight()}),u=s.size||[],c=Object(ve["o"])(xe(d.width,u[0]),r),h=Object(ve["o"])(xe(d.height,u[1]),l),p=i&&i.type,g=["treemapZoomToNode","treemapRootToNode"],f=o["c"](i,g,e),y="treemapRender"===p||"treemapMove"===p?i.rootRect:null,v=e.getViewRoot(),m=o["b"](v);if("treemapMove"!==p){var b="treemapZoomToNode"===p?Ve(e,f,v,c,h):y?[y.width,y.height]:[c,h],x=s.sort;x&&"asc"!==x&&"desc"!==x&&(x="desc");var w={squareRatio:s.squareRatio,sort:x,leafDepth:s.leafDepth};v.hostTree.clearLayouts();var I={x:0,y:0,width:b[0],height:b[1],area:b[0]*b[1]};v.setLayout(I),Ne(v,w,!1,0),I=v.getLayout(),we(m,(function(e,t){var a=(m[t+1]||v).getValue();e.setLayout(n["m"]({dataExtent:[a,a],borderWidth:0,upperHeight:0},I))}))}var L=e.getData().tree.root;L.setLayout(ke(d,y,f),!0),e.setLayoutInfo(d),Ae(L,new k["a"](-d.x,-d.y,r,l),m,v,0)}};function Ne(e,t,a,o){var n,i;if(!e.isRemoved()){var r=e.getLayout();n=r.width,i=r.height;var l=e.getModel(),s=l.get(Ie),d=l.get(Le)/2,u=Ee(l),c=Math.max(s,u),h=s-d,p=c-d;e.setLayout({borderWidth:s,upperHeight:c,upperLabelHeight:u},!0),n=me(n-2*h,0),i=me(i-h-p,0);var g=n*i,f=Se(e,l,g,t,a,o);if(f.length){var y={x:h,y:p,width:n,height:i},v=be(n,i),m=1/0,b=[];b.area=0;for(var x=0,w=f.length;x<w;){var I=f[x];b.push(I),b.area+=I.getLayout().area;var L=Re(b,v,t.squareRatio);L<=m?(x++,m=L):(b.area-=b.pop().getLayout().area,Ce(b,v,y,d,!1),v=be(y.width,y.height),b.length=b.area=0,m=1/0)}if(b.length&&Ce(b,v,y,d,!0),!a){var _=l.get("childrenVisibleMin");null!=_&&g<_&&(a=!0)}for(x=0,w=f.length;x<w;x++)Ne(f[x],t,a,o+1)}}}function Se(e,t,a,o,i,r){var l=e.children||[],s=o.sort;"asc"!==s&&"desc"!==s&&(s=null);var d=null!=o.leafDepth&&o.leafDepth<=r;if(i&&!d)return e.viewChildren=[];l=n["n"](l,(function(e){return!e.isRemoved()})),De(l,s);var u=Te(t,l,s);if(0===u.sum)return e.viewChildren=[];if(u.sum=je(t,a,u.sum,s,l),0===u.sum)return e.viewChildren=[];for(var c=0,h=l.length;c<h;c++){var p=l[c].getValue()/u.sum*a;l[c].setLayout({area:p})}return d&&(l.length&&e.setLayout({isLeafRoot:!0},!0),l.length=0),e.viewChildren=l,e.setLayout({dataExtent:u.dataExtent},!0),l}function je(e,t,a,o,n){if(!o)return a;for(var i=e.get("visibleMin"),r=n.length,l=r,s=r-1;s>=0;s--){var d=n["asc"===o?r-s-1:s].getValue();d/a*t<i&&(l=s,a-=d)}return"asc"===o?n.splice(0,r-l):n.splice(l,r-l),a}function De(e,t){return t&&e.sort((function(e,a){var o="asc"===t?e.getValue()-a.getValue():a.getValue()-e.getValue();return 0===o?"asc"===t?e.dataIndex-a.dataIndex:a.dataIndex-e.dataIndex:o})),e}function Te(e,t,a){for(var o=0,n=0,i=t.length;n<i;n++)o+=t[n].getValue();var r,l=e.get("visualDimension");return t&&t.length?"value"===l&&a?(r=[t[t.length-1].getValue(),t[0].getValue()],"asc"===a&&r.reverse()):(r=[1/0,-1/0],we(t,(function(e){var t=e.getValue(l);t<r[0]&&(r[0]=t),t>r[1]&&(r[1]=t)}))):r=[NaN,NaN],{sum:o,dataExtent:r}}function Re(e,t,a){for(var o=0,n=1/0,i=0,r=void 0,l=e.length;i<l;i++)r=e[i].getLayout().area,r&&(r<n&&(n=r),r>o&&(o=r));var s=e.area*e.area,d=t*t*a;return s?me(d*o/s,s/(d*n)):1/0}function Ce(e,t,a,o,n){var i=t===a.width?0:1,r=1-i,l=["x","y"],s=["width","height"],d=a[l[i]],u=t?e.area/t:0;(n||u>a[s[r]])&&(u=a[s[r]]);for(var c=0,h=e.length;c<h;c++){var p=e[c],g={},f=u?p.getLayout().area/u:0,y=g[s[r]]=me(u-2*o,0),v=a[l[i]]+a[s[i]]-d,m=c===h-1||v<f?v:f,b=g[s[i]]=me(m-2*o,0);g[l[r]]=a[l[r]]+be(o,y/2),g[l[i]]=d+be(o,b/2),d+=m,p.setLayout(g,!0)}a[l[r]]+=u,a[s[r]]-=u}function Ve(e,t,a,o,n){var i,r=(t||{}).node,l=[o,n];if(!r||r===a)return l;var s=o*n,d=s*e.option.zoomToNodeRatio;while(i=r.parentNode){for(var u=0,c=i.children,h=0,p=c.length;h<p;h++)u+=c[h].getValue();var g=r.getValue();if(0===g)return l;d*=u/g;var f=i.getModel(),y=f.get(Ie),v=Math.max(y,Ee(f));d+=4*y*y+(3*y+v)*Math.pow(d,.5),d>ve["a"]&&(d=ve["a"]),r=i}d<s&&(d=s);var m=Math.pow(d/s,.5);return[o*m,n*m]}function ke(e,t,a){if(t)return{x:t.x,y:t.y};var o={x:0,y:0};if(!a)return o;var n=a.node,i=n.getLayout();if(!i)return o;var r=[i.width/2,i.height/2],l=n;while(l){var s=l.getLayout();r[0]+=s.x,r[1]+=s.y,l=l.parentNode}return{x:e.width/2-r[0],y:e.height/2-r[1]}}function Ae(e,t,a,o,n){var i=e.getLayout(),r=a[n],l=r&&r===e;if(!(r&&!l||n===a.length&&e!==o)){e.setLayout({isInView:!0,invisible:!l&&!t.intersect(i),isAboveViewRoot:l},!0);var s=new k["a"](t.x-i.x,t.y-i.y,t.width,t.height);we(e.viewChildren||[],(function(e){Ae(e,s,a,o,n+1)}))}}function Ee(e){return e.get(_e)?e.get(Oe):0}function Pe(e){e.registerSeriesModel(v),e.registerChartView(oe),e.registerVisual(se),e.registerLayout(Me),r(e)}},"54ca":function(e,t,a){"use strict";a.d(t,"a",(function(){return ne}));var o=a("7fae"),n=a("6d8b"),i=a("2dc5"),r=a("deca"),l=a("ac0f"),s=a("861c"),d=a("1418"),u=a("f934");function c(e){var t=e;t.hierNode={defaultAncestor:null,ancestor:t,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};var a,o,n=[t];while(a=n.pop())if(o=a.children,a.isExpand&&o.length)for(var i=o.length,r=i-1;r>=0;r--){var l=o[r];l.hierNode={defaultAncestor:null,ancestor:l,prelim:0,modifier:0,change:0,shift:0,i:r,thread:null},n.push(l)}}function h(e,t){var a=e.isExpand?e.children:[],o=e.parentNode.children,n=e.hierNode.i?o[e.hierNode.i-1]:null;if(a.length){v(e);var i=(a[0].hierNode.prelim+a[a.length-1].hierNode.prelim)/2;n?(e.hierNode.prelim=n.hierNode.prelim+t(e,n),e.hierNode.modifier=e.hierNode.prelim-i):e.hierNode.prelim=i}else n&&(e.hierNode.prelim=n.hierNode.prelim+t(e,n));e.parentNode.hierNode.defaultAncestor=m(e,n,e.parentNode.hierNode.defaultAncestor||o[0],t)}function p(e){var t=e.hierNode.prelim+e.parentNode.hierNode.modifier;e.setLayout({x:t},!0),e.hierNode.modifier+=e.parentNode.hierNode.modifier}function g(e){return arguments.length?e:L}function f(e,t){return e-=Math.PI/2,{x:t*Math.cos(e),y:t*Math.sin(e)}}function y(e,t){return u["g"](e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()})}function v(e){var t=e.children,a=t.length,o=0,n=0;while(--a>=0){var i=t[a];i.hierNode.prelim+=o,i.hierNode.modifier+=o,n+=i.hierNode.change,o+=i.hierNode.shift+n}}function m(e,t,a,o){if(t){var n=e,i=e,r=i.parentNode.children[0],l=t,s=n.hierNode.modifier,d=i.hierNode.modifier,u=r.hierNode.modifier,c=l.hierNode.modifier;while(l=b(l),i=x(i),l&&i){n=b(n),r=x(r),n.hierNode.ancestor=e;var h=l.hierNode.prelim+c-i.hierNode.prelim-d+o(l,i);h>0&&(I(w(l,e,a),e,h),d+=h,s+=h),c+=l.hierNode.modifier,d+=i.hierNode.modifier,s+=n.hierNode.modifier,u+=r.hierNode.modifier}l&&!b(n)&&(n.hierNode.thread=l,n.hierNode.modifier+=c-s),i&&!x(r)&&(r.hierNode.thread=i,r.hierNode.modifier+=d-u,a=e)}return a}function b(e){var t=e.children;return t.length&&e.isExpand?t[t.length-1]:e.hierNode.thread}function x(e){var t=e.children;return t.length&&e.isExpand?t[0]:e.hierNode.thread}function w(e,t,a){return e.hierNode.ancestor.parentNode===t.parentNode?e.hierNode.ancestor:a}function I(e,t,a){var o=a/(t.hierNode.i-e.hierNode.i);t.hierNode.change-=o,t.hierNode.shift+=a,t.hierNode.modifier+=a,t.hierNode.prelim+=a,e.hierNode.change+=o}function L(e,t){return e.parentNode===t.parentNode?1:2}var _=a("e263"),O=a("6cc5"),M=a("01ef"),N=a("4a01"),S=a("c526"),j=a("3842"),D=a("e887"),T=a("cbe5"),R=a("7d6c"),C=function(){function e(){this.parentPoint=[],this.childPoints=[]}return e}(),V=function(e){function t(t){return e.call(this,t)||this}return Object(o["a"])(t,e),t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new C},t.prototype.buildPath=function(e,t){var a=t.childPoints,o=a.length,n=t.parentPoint,i=a[0],r=a[o-1];if(1===o)return e.moveTo(n[0],n[1]),void e.lineTo(i[0],i[1]);var l=t.orient,s="TB"===l||"BT"===l?0:1,d=1-s,u=Object(j["o"])(t.forkPosition,1),c=[];c[s]=n[s],c[d]=n[d]+(r[d]-n[d])*u,e.moveTo(n[0],n[1]),e.lineTo(c[0],c[1]),e.moveTo(i[0],i[1]),c[s]=i[s],e.lineTo(c[0],c[1]),c[s]=r[s],e.lineTo(c[0],c[1]),e.lineTo(r[0],r[1]);for(var h=1;h<o-1;h++){var p=a[h];e.moveTo(p[0],p[1]),c[s]=p[s],e.lineTo(c[0],c[1])}},t}(T["b"]),k=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a._mainGroup=new i["a"],a}return Object(o["a"])(t,e),t.prototype.init=function(e,t){this._controller=new N["a"](t.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},t.prototype.render=function(e,t,a){var o=e.getData(),n=e.layoutInfo,i=this._mainGroup,r=e.get("layout");"radial"===r?(i.x=n.x+n.width/2,i.y=n.y+n.height/2):(i.x=n.x,i.y=n.y),this._updateViewCoordSys(e,a),this._updateController(e,t,a);var l=this._data;o.diff(l).add((function(t){A(o,t)&&E(o,t,null,i,e)})).update((function(t,a){var n=l.getItemGraphicEl(a);A(o,t)?E(o,t,n,i,e):n&&B(l,a,n,i,e)})).remove((function(t){var a=l.getItemGraphicEl(t);a&&B(l,t,a,i,e)})).execute(),this._nodeScaleRatio=e.get("nodeScaleRatio"),this._updateNodeAndLinkScale(e),!0===e.get("expandAndCollapse")&&o.eachItemGraphicEl((function(t,o){t.off("click").on("click",(function(){a.dispatchAction({type:"treeExpandAndCollapse",seriesId:e.id,dataIndex:o})}))})),this._data=o},t.prototype._updateViewCoordSys=function(e,t){var a=e.getData(),o=[];a.each((function(e){var t=a.getItemLayout(e);!t||isNaN(t.x)||isNaN(t.y)||o.push([+t.x,+t.y])}));var n=[],i=[];_["d"](o,n,i);var r=this._min,l=this._max;i[0]-n[0]===0&&(n[0]=r?r[0]:n[0]-1,i[0]=l?l[0]:i[0]+1),i[1]-n[1]===0&&(n[1]=r?r[1]:n[1]-1,i[1]=l?l[1]:i[1]+1);var s=e.coordinateSystem=new O["a"];s.zoomLimit=e.get("scaleLimit"),s.setBoundingRect(n[0],n[1],i[0]-n[0],i[1]-n[1]),s.setCenter(e.get("center"),t),s.setZoom(e.get("zoom")),this.group.attr({x:s.x,y:s.y,scaleX:s.scaleX,scaleY:s.scaleY}),this._min=n,this._max=i},t.prototype._updateController=function(e,t,a){var o=this,n=this._controller,i=this._controllerHost,r=this.group;n.setPointerChecker((function(t,o,n){var i=r.getBoundingRect();return i.applyTransform(r.transform),i.contain(o,n)&&!Object(S["a"])(t,a,e)})),n.enable(e.get("roam")),i.zoomLimit=e.get("scaleLimit"),i.zoom=e.coordinateSystem.getZoom(),n.off("pan").off("zoom").on("pan",(function(t){M["a"](i,t.dx,t.dy),a.dispatchAction({seriesId:e.id,type:"treeRoam",dx:t.dx,dy:t.dy})})).on("zoom",(function(t){M["b"](i,t.scale,t.originX,t.originY),a.dispatchAction({seriesId:e.id,type:"treeRoam",zoom:t.scale,originX:t.originX,originY:t.originY}),o._updateNodeAndLinkScale(e),a.updateLabelLayout()}))},t.prototype._updateNodeAndLinkScale=function(e){var t=e.getData(),a=this._getNodeGlobalScale(e);t.eachItemGraphicEl((function(e,t){e.setSymbolScale(a)}))},t.prototype._getNodeGlobalScale=function(e){var t=e.coordinateSystem;if("view"!==t.type)return 1;var a=this._nodeScaleRatio,o=t.scaleX||1,n=t.getZoom(),i=(n-1)*a+1;return i/o},t.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},t.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},t.type="tree",t}(D["a"]);function A(e,t){var a=e.getItemLayout(t);return a&&!isNaN(a.x)&&!isNaN(a.y)}function E(e,t,a,o,i){var l=!a,u=e.tree.getNodeByDataIndex(t),c=u.getModel(),h=u.getVisual("style").fill,p=!1===u.isExpand&&0!==u.children.length?h:"#fff",g=e.tree.root,f=u.parentNode===g?u:u.parentNode||u,y=e.getItemGraphicEl(f.dataIndex),v=f.getLayout(),m=y?{x:y.__oldX,y:y.__oldY,rawX:y.__radialOldRawX,rawY:y.__radialOldRawY}:v,b=u.getLayout();l?(a=new d["a"](e,t,null,{symbolInnerColor:p,useNameLabel:!0}),a.x=m.x,a.y=m.y):a.updateData(e,t,null,{symbolInnerColor:p,useNameLabel:!0}),a.__radialOldRawX=a.__radialRawX,a.__radialOldRawY=a.__radialRawY,a.__radialRawX=b.rawX,a.__radialRawY=b.rawY,o.add(a),e.setItemGraphicEl(t,a),a.__oldX=a.x,a.__oldY=a.y,r["h"](a,{x:b.x,y:b.y},i);var x=a.getSymbolPath();if("radial"===i.get("layout")){var w=g.children[0],I=w.getLayout(),L=w.children.length,_=void 0,O=void 0;if(b.x===I.x&&!0===u.isExpand&&w.children.length){var M={x:(w.children[0].getLayout().x+w.children[L-1].getLayout().x)/2,y:(w.children[0].getLayout().y+w.children[L-1].getLayout().y)/2};_=Math.atan2(M.y-I.y,M.x-I.x),_<0&&(_=2*Math.PI+_),O=M.x<I.x,O&&(_-=Math.PI)}else _=Math.atan2(b.y-I.y,b.x-I.x),_<0&&(_=2*Math.PI+_),0===u.children.length||0!==u.children.length&&!1===u.isExpand?(O=b.x<I.x,O&&(_-=Math.PI)):(O=b.x>I.x,O||(_-=Math.PI));var N=O?"left":"right",S=c.getModel("label"),j=S.get("rotate"),D=j*(Math.PI/180),T=x.getTextContent();T&&(x.setTextConfig({position:S.get("position")||N,rotation:null==j?-_:D,origin:"center"}),T.setStyle("verticalAlign","middle"))}var C=c.get(["emphasis","focus"]),V="relative"===C?n["e"](u.getAncestorsIndices(),u.getDescendantIndices()):"ancestor"===C?u.getAncestorsIndices():"descendant"===C?u.getDescendantIndices():null;V&&(Object(s["a"])(a).focus=V),P(i,u,g,a,m,v,b,o),a.__edge&&(a.onHoverStateChange=function(t){if("blur"!==t){var o=u.parentNode&&e.getItemGraphicEl(u.parentNode.dataIndex);o&&o.hoverState===R["d"]||Object(R["H"])(a.__edge,t)}})}function P(e,t,a,o,i,s,d,u){var c=t.getModel(),h=e.get("edgeShape"),p=e.get("layout"),g=e.getOrient(),f=e.get(["lineStyle","curveness"]),y=e.get("edgeForkPosition"),v=c.getModel("lineStyle").getLineStyle(),m=o.__edge;if("curve"===h)t.parentNode&&t.parentNode!==a&&(m||(m=o.__edge=new l["a"]({shape:H(p,g,f,i,i)})),r["h"](m,{shape:H(p,g,f,s,d)},e));else if("polyline"===h)if("orthogonal"===p){if(t!==a&&t.children&&0!==t.children.length&&!0===t.isExpand){for(var b=t.children,x=[],w=0;w<b.length;w++){var I=b[w].getLayout();x.push([I.x,I.y])}m||(m=o.__edge=new V({shape:{parentPoint:[d.x,d.y],childPoints:[[d.x,d.y]],orient:g,forkPosition:y}})),r["h"](m,{shape:{parentPoint:[d.x,d.y],childPoints:x}},e)}}else 0;m&&("polyline"!==h||t.isExpand)&&(m.useStyle(n["i"]({strokeNoScale:!0,fill:null},v)),Object(R["I"])(m,c,"lineStyle"),Object(R["G"])(m),u.add(m))}function G(e,t,a,o,n){var i=t.tree.root,l=z(i,e),s=l.source,d=l.sourceLayout,u=t.getItemGraphicEl(e.dataIndex);if(u){var c=t.getItemGraphicEl(s.dataIndex),h=c.__edge,p=u.__edge||(!1===s.isExpand||1===s.children.length?h:void 0),g=o.get("edgeShape"),f=o.get("layout"),y=o.get("orient"),v=o.get(["lineStyle","curveness"]);p&&("curve"===g?r["e"](p,{shape:H(f,y,v,d,d),style:{opacity:0}},o,{cb:function(){a.remove(p)},removeOpt:n}):"polyline"===g&&"orthogonal"===o.get("layout")&&r["e"](p,{shape:{parentPoint:[d.x,d.y],childPoints:[[d.x,d.y]]},style:{opacity:0}},o,{cb:function(){a.remove(p)},removeOpt:n}))}}function z(e,t){var a,o=t.parentNode===e?t:t.parentNode||t;while(a=o.getLayout(),null==a)o=o.parentNode===e?o:o.parentNode||o;return{source:o,sourceLayout:a}}function B(e,t,a,o,n){var i=e.tree.getNodeByDataIndex(t),l=e.tree.root,s=z(l,i).sourceLayout,d={duration:n.get("animationDurationUpdate"),easing:n.get("animationEasingUpdate")};r["e"](a,{x:s.x+1,y:s.y+1},n,{cb:function(){o.remove(a),e.setItemGraphicEl(t,null)},removeOpt:d}),a.fadeOut(null,e.hostModel,{fadeLabel:!0,animation:d}),i.children.forEach((function(t){G(t,e,o,n,d)})),G(i,e,o,n,d)}function H(e,t,a,o,n){var i,r,l,s,d,u,c,h;if("radial"===e){d=o.rawX,c=o.rawY,u=n.rawX,h=n.rawY;var p=f(d,c),g=f(d,c+(h-c)*a),y=f(u,h+(c-h)*a),v=f(u,h);return{x1:p.x||0,y1:p.y||0,x2:v.x||0,y2:v.y||0,cpx1:g.x||0,cpy1:g.y||0,cpx2:y.x||0,cpy2:y.y||0}}return d=o.x,c=o.y,u=n.x,h=n.y,"LR"!==t&&"RL"!==t||(i=d+(u-d)*a,r=c,l=u+(d-u)*a,s=h),"TB"!==t&&"BT"!==t||(i=d,r=c+(h-c)*a,l=u,s=h+(c-h)*a),{x1:d,y1:c,x2:u,y2:h,cpx1:i,cpy1:r,cpx2:l,cpy2:s}}var W=k,X=a("4f85"),Y=a("06c7"),F=a("4319"),Z=a("217c"),U=a("55ac"),q=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.hasSymbolVisual=!0,t.ignoreStyleOnData=!0,t}return Object(o["a"])(t,e),t.prototype.getInitialData=function(e){var t={name:e.name,children:e.data},a=e.leaves||{},o=new F["a"](a,this,this.ecModel),n=Y["a"].createTree(t,this,i);function i(e){e.wrapMethod("getItemModel",(function(e,t){var a=n.getNodeByDataIndex(t);return a&&a.children.length&&a.isExpand||(e.parentModel=o),e}))}var r=0;n.eachNode("preorder",(function(e){e.depth>r&&(r=e.depth)}));var l=e.expandAndCollapse,s=l&&e.initialTreeDepth>=0?e.initialTreeDepth:r;return n.root.eachNode("preorder",(function(e){var t=e.hostTree.data.getRawDataItem(e.dataIndex);e.isExpand=t&&null!=t.collapsed?!t.collapsed:e.depth<=s})),n.data},t.prototype.getOrient=function(){var e=this.get("orient");return"horizontal"===e?e="LR":"vertical"===e&&(e="TB"),e},t.prototype.setZoom=function(e){this.option.zoom=e},t.prototype.setCenter=function(e){this.option.center=e},t.prototype.formatTooltip=function(e,t,a){var o=this.getData().tree,n=o.root.children[0],i=o.getNodeByDataIndex(e),r=i.getValue(),l=i.name;while(i&&i!==n)l=i.parentNode.name+"."+l,i=i.parentNode;return Object(Z["c"])("nameValue",{name:l,value:r,noValue:isNaN(r)||null==r})},t.prototype.getDataParams=function(t){var a=e.prototype.getDataParams.apply(this,arguments),o=this.getData().tree.getNodeByDataIndex(t);return a.treeAncestors=Object(U["d"])(o,this),a.collapsed=!o.isExpand,a},t.type="series.tree",t.layoutMode="box",t.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},t}(X["b"]),J=q;function K(e,t,a){var o,n=[e],i=[];while(o=n.pop())if(i.push(o),o.isExpand){var r=o.children;if(r.length)for(var l=0;l<r.length;l++)n.push(r[l])}while(o=i.pop())t(o,a)}function Q(e,t){var a,o=[e];while(a=o.pop())if(t(a),a.isExpand){var n=a.children;if(n.length)for(var i=n.length-1;i>=0;i--)o.push(n[i])}}function $(e,t){e.eachSeriesByType("tree",(function(e){ee(e,t)}))}function ee(e,t){var a=y(e,t);e.layoutInfo=a;var o=e.get("layout"),n=0,i=0,r=null;"radial"===o?(n=2*Math.PI,i=Math.min(a.height,a.width)/2,r=g((function(e,t){return(e.parentNode===t.parentNode?1:2)/e.depth}))):(n=a.width,i=a.height,r=g());var l=e.getData().tree.root,s=l.children[0];if(s){c(l),K(s,h,r),l.hierNode.modifier=-s.hierNode.prelim,Q(s,p);var d=s,u=s,v=s;Q(s,(function(e){var t=e.getLayout().x;t<d.getLayout().x&&(d=e),t>u.getLayout().x&&(u=e),e.depth>v.depth&&(v=e)}));var m=d===u?1:r(d,u)/2,b=m-d.getLayout().x,x=0,w=0,I=0,L=0;if("radial"===o)x=n/(u.getLayout().x+m+b),w=i/(v.depth-1||1),Q(s,(function(e){I=(e.getLayout().x+b)*x,L=(e.depth-1)*w;var t=f(I,L);e.setLayout({x:t.x,y:t.y,rawX:I,rawY:L},!0)}));else{var _=e.getOrient();"RL"===_||"LR"===_?(w=i/(u.getLayout().x+m+b),x=n/(v.depth-1||1),Q(s,(function(e){L=(e.getLayout().x+b)*w,I="LR"===_?(e.depth-1)*x:n-(e.depth-1)*x,e.setLayout({x:I,y:L},!0)}))):"TB"!==_&&"BT"!==_||(x=n/(u.getLayout().x+m+b),w=i/(v.depth-1||1),Q(s,(function(e){I=(e.getLayout().x+b)*x,L="TB"===_?(e.depth-1)*w:i-(e.depth-1)*w,e.setLayout({x:I,y:L},!0)})))}}}function te(e){e.eachSeriesByType("tree",(function(e){var t=e.getData(),a=t.tree;a.eachNode((function(e){var a=e.getModel(),o=a.getModel("itemStyle").getItemStyle(),i=t.ensureUniqueItemVisual(e.dataIndex,"style");Object(n["m"])(i,o)}))}))}var ae=a("d81e");function oe(e){e.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},(function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},(function(t){var a=e.dataIndex,o=t.getData().tree,n=o.getNodeByDataIndex(a);n.isExpand=!n.isExpand}))})),e.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},(function(e,t,a){t.eachComponent({mainType:"series",subType:"tree",query:e},(function(t){var o=t.coordinateSystem,n=Object(ae["a"])(o,e,void 0,a);t.setCenter&&t.setCenter(n.center),t.setZoom&&t.setZoom(n.zoom)}))}))}function ne(e){e.registerChartView(W),e.registerSeriesModel(J),e.registerLayout($),e.registerVisual(te),oe(e)}},"8acb":function(e,t,a){"use strict";a.d(t,"a",(function(){return W}));var o=a("7fae"),n=a("6d8b"),i=a("e887"),r=a("76a5"),l=a("deca"),s=a("4aa2"),d=a("7d6c"),u=a("7837"),c=a("861c"),h=a("e4b8"),p=a("b3c1"),g=a("857d"),f=a("3842"),y=2,v=4,m=function(e){function t(t,a,o,n){var i=e.call(this)||this;i.z2=y,i.textConfig={inside:!0},Object(c["a"])(i).seriesIndex=a.seriesIndex;var l=new r["a"]({z2:v,silent:t.getModel().get(["label","silent"])});return i.setTextContent(l),i.updateData(!0,t,a,o,n),i}return Object(o["a"])(t,e),t.prototype.updateData=function(e,t,a,o,i){this.node=t,t.piece=this,a=a||this._seriesModel,o=o||this._ecModel;var r=this;Object(c["a"])(r).dataIndex=t.dataIndex;var s=t.getModel(),u=s.getModel("emphasis"),g=t.getLayout(),f=n["m"]({},g);f.label=null;var y=t.getVisual("style");y.lineJoin="bevel";var v=t.getVisual("decal");v&&(y.decal=Object(p["a"])(v,i));var m=Object(h["a"])(s.getModel("itemStyle"),f,!0);n["m"](f,m),n["k"](d["g"],(function(e){var t=r.ensureState(e),a=s.getModel([e,"itemStyle"]);t.style=a.getItemStyle();var o=Object(h["a"])(a,f);o&&(t.shape=o)})),e?(r.setShape(f),r.shape.r=g.r0,l["c"](r,{shape:{r:g.r}},a,t.dataIndex)):(l["h"](r,{shape:f},a),Object(l["g"])(r)),r.useStyle(y),this._updateLabel(a);var b=s.getShallow("cursor");b&&r.attr("cursor",b),this._seriesModel=a||this._seriesModel,this._ecModel=o||this._ecModel;var x=u.get("focus"),w="relative"===x?n["e"](t.getAncestorsIndices(),t.getDescendantIndices()):"ancestor"===x?t.getAncestorsIndices():"descendant"===x?t.getDescendantIndices():x;Object(d["J"])(this,w,u.get("blurScope"),u.get("disabled"))},t.prototype._updateLabel=function(e){var t=this,a=this.node.getModel(),o=a.getModel("label"),i=this.node.getLayout(),r=i.endAngle-i.startAngle,l=(i.startAngle+i.endAngle)/2,s=Math.cos(l),c=Math.sin(l),h=this,p=h.getTextContent(),y=this.node.dataIndex,v=o.get("minAngle")/180*Math.PI,m=o.get("show")&&!(null!=v&&Math.abs(r)<v);function b(e,t){var a=e.get(t);return null==a?o.get(t):a}p.ignore=!m,n["k"](d["a"],(function(o){var d="normal"===o?a.getModel("label"):a.getModel([o,"label"]),v="normal"===o,m=v?p:p.ensureState(o),x=e.getFormattedLabel(y,o);v&&(x=x||t.node.name),m.style=Object(u["c"])(d,{},null,"normal"!==o,!0),x&&(m.style.text=x);var w=d.get("show");null==w||v||(m.ignore=!w);var I,L=b(d,"position"),_=v?h:h.states[o],O=_.style.fill;_.textConfig={outsideFill:"inherit"===d.get("color")?O:null,inside:"outside"!==L};var M=b(d,"distance")||0,N=b(d,"align"),S=b(d,"rotate"),j=.5*Math.PI,D=1.5*Math.PI,T=Object(g["a"])("tangential"===S?Math.PI/2-l:l),R=T>j&&!Object(f["j"])(T-j)&&T<D;"outside"===L?(I=i.r+M,N=R?"right":"left"):N&&"center"!==N?"left"===N?(I=i.r0+M,N=R?"right":"left"):"right"===N&&(I=i.r-M,N=R?"left":"right"):(I=r===2*Math.PI&&0===i.r0?0:(i.r+i.r0)/2,N="center"),m.style.align=N,m.style.verticalAlign=b(d,"verticalAlign")||"middle",m.x=I*s+i.cx,m.y=I*c+i.cy;var C=0;"radial"===S?C=Object(g["a"])(-l)+(R?Math.PI:0):"tangential"===S?C=Object(g["a"])(Math.PI/2-l)+(R?Math.PI:0):n["z"](S)&&(C=S*Math.PI/180),m.rotation=Object(g["a"])(C)})),p.dirtyStyle()},t}(s["a"]),b=m,x=a("80f0"),w=a("55ac"),I="sunburstRootToNode",L="sunburstHighlight",_="sunburstUnhighlight";function O(e){e.registerAction({type:I,update:"updateView"},(function(e,t){function a(t,a){var o=Object(w["c"])(e,[I],t);if(o){var n=t.getViewRoot();n&&(e.direction=Object(w["a"])(n,o.node)?"rollUp":"drillDown"),t.resetViewRoot(o.node)}}t.eachComponent({mainType:"series",subType:"sunburst",query:e},a)})),e.registerAction({type:L,update:"none"},(function(e,t,a){function o(t){var a=Object(w["c"])(e,[L],t);a&&(e.dataIndex=a.node.dataIndex)}e=Object(n["m"])({},e),t.eachComponent({mainType:"series",subType:"sunburst",query:e},o),a.dispatchAction(Object(n["m"])(e,{type:"highlight"}))})),e.registerAction({type:_,update:"updateView"},(function(e,t,a){e=Object(n["m"])({},e),a.dispatchAction(Object(n["m"])(e,{type:"downplay"}))}))}var M=a("eda2"),N=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a}return Object(o["a"])(t,e),t.prototype.render=function(e,t,a,o){var i=this;this.seriesModel=e,this.api=a,this.ecModel=t;var r=e.getData(),l=r.tree.root,s=e.getViewRoot(),d=this.group,u=e.get("renderLabelForZeroData"),c=[];s.eachNode((function(e){c.push(e)}));var h=this._oldChildren||[];function p(e,t){function a(e){return e.getId()}function o(a,o){var n=null==a?null:e[a],i=null==o?null:t[o];g(n,i)}0===e.length&&0===t.length||new x["a"](t,e,a,a).add(o).update(o).remove(n["h"](o,null)).execute()}function g(o,n){if(u||!o||o.getValue()||(o=null),o!==l&&n!==l)if(n&&n.piece)o?(n.piece.updateData(!1,o,e,t,a),r.setItemGraphicEl(o.dataIndex,n.piece)):f(n);else if(o){var i=new b(o,e,t,a);d.add(i),r.setItemGraphicEl(o.dataIndex,i)}}function f(e){e&&e.piece&&(d.remove(e.piece),e.piece=null)}function y(o,n){n.depth>0?(i.virtualPiece?i.virtualPiece.updateData(!1,o,e,t,a):(i.virtualPiece=new b(o,e,t,a),d.add(i.virtualPiece)),n.piece.off("click"),i.virtualPiece.on("click",(function(e){i._rootToNode(n.parentNode)}))):i.virtualPiece&&(d.remove(i.virtualPiece),i.virtualPiece=null)}p(c,h),y(l,s),this._initEvents(),this._oldChildren=c},t.prototype._initEvents=function(){var e=this;this.group.off("click"),this.group.on("click",(function(t){var a=!1,o=e.seriesModel.getViewRoot();o.eachNode((function(o){if(!a&&o.piece&&o.piece===t.target){var n=o.getModel().get("nodeClick");if("rootToNode"===n)e._rootToNode(o);else if("link"===n){var i=o.getModel(),r=i.get("link");if(r){var l=i.get("target",!0)||"_blank";Object(M["i"])(r,l)}}a=!0}}))}))},t.prototype._rootToNode=function(e){e!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:I,from:this.uid,seriesId:this.seriesModel.id,targetNode:e})},t.prototype.containPoint=function(e,t){var a=t.getData(),o=a.getItemLayout(0);if(o){var n=e[0]-o.cx,i=e[1]-o.cy,r=Math.sqrt(n*n+i*i);return r<=o.r&&r>=o.r0}},t.type="sunburst",t}(i["a"]),S=N,j=a("4f85"),D=a("06c7"),T=a("4319"),R=a("933c"),C=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a.ignoreStyleOnData=!0,a}return Object(o["a"])(t,e),t.prototype.getInitialData=function(e,t){var a={name:e.name,children:e.data};V(a);var o=this._levelModels=n["H"](e.levels||[],(function(e){return new T["a"](e,this,t)}),this),i=D["a"].createTree(a,this,r);function r(e){e.wrapMethod("getItemModel",(function(e,t){var a=i.getNodeByDataIndex(t),n=o[a.depth];return n&&(e.parentModel=n),e}))}return i.data},t.prototype.optionUpdated=function(){this.resetViewRoot()},t.prototype.getDataParams=function(t){var a=e.prototype.getDataParams.apply(this,arguments),o=this.getData().tree.getNodeByDataIndex(t);return a.treePathInfo=Object(w["d"])(o,this),a},t.prototype.getLevelModel=function(e){return this._levelModels&&this._levelModels[e.depth]},t.prototype.getViewRoot=function(){return this._viewRoot},t.prototype.resetViewRoot=function(e){e?this._viewRoot=e:e=this._viewRoot;var t=this.getRawData().tree.root;e&&(e===t||t.contains(e))||(this._viewRoot=t)},t.prototype.enableAriaDecal=function(){Object(R["a"])(this)},t.type="series.sunburst",t.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},t}(j["b"]);function V(e){var t=0;n["k"](e.children,(function(e){V(e);var a=e.value;n["t"](a)&&(a=a[0]),t+=a}));var a=e.value;n["t"](a)&&(a=a[0]),(null==a||isNaN(a))&&(a=t),a<0&&(a=0),n["t"](e.value)?e.value[0]=a:e.value=a}var k=C,A=Math.PI/180;function E(e,t,a){t.eachSeriesByType(e,(function(e){var t=e.get("center"),o=e.get("radius");n["t"](o)||(o=[0,o]),n["t"](t)||(t=[t,t]);var i=a.getWidth(),r=a.getHeight(),l=Math.min(i,r),s=Object(f["o"])(t[0],i),d=Object(f["o"])(t[1],r),u=Object(f["o"])(o[0],l/2),c=Object(f["o"])(o[1],l/2),h=-e.get("startAngle")*A,p=e.get("minAngle")*A,g=e.getData().tree.root,y=e.getViewRoot(),v=y.depth,m=e.get("sort");null!=m&&P(y,m);var b=0;n["k"](y.children,(function(e){!isNaN(e.getValue())&&b++}));var x=y.getValue(),w=Math.PI/(x||b)*2,I=y.depth>0,L=y.height-(I?-1:1),_=(c-u)/(L||1),O=e.get("clockwise"),M=e.get("stillShowZeroSum"),N=O?1:-1,S=function(t,a){if(t){var o=a;if(t!==g){var i=t.getValue(),r=0===x&&M?w:i*w;r<p&&(r=p),o=a+N*r;var c=t.depth-v-(I?-1:1),h=u+_*c,y=u+_*(c+1),m=e.getLevelModel(t);if(m){var b=m.get("r0",!0),L=m.get("r",!0),j=m.get("radius",!0);null!=j&&(b=j[0],L=j[1]),null!=b&&(h=Object(f["o"])(b,l/2)),null!=L&&(y=Object(f["o"])(L,l/2))}t.setLayout({angle:r,startAngle:a,endAngle:o,clockwise:O,cx:s,cy:d,r0:h,r:y})}if(t.children&&t.children.length){var D=0;n["k"](t.children,(function(e){D+=S(e,a+D)}))}return o-a}};if(I){var j=u,D=u+_,T=2*Math.PI;g.setLayout({angle:T,startAngle:h,endAngle:h+T,clockwise:O,cx:s,cy:d,r0:j,r:D})}S(y,h)}))}function P(e,t){var a=e.children||[];e.children=G(a,t),a.length&&n["k"](e.children,(function(e){P(e,t)}))}function G(e,t){if(n["w"](t)){var a=n["H"](e,(function(e,t){var a=e.getValue();return{params:{depth:e.depth,height:e.height,dataIndex:e.dataIndex,getValue:function(){return a}},index:t}}));return a.sort((function(e,a){return t(e.params,a.params)})),n["H"](a,(function(t){return e[t.index]}))}var o="asc"===t;return e.sort((function(e,t){var a=(e.getValue()-t.getValue())*(o?1:-1);return 0===a?(e.dataIndex-t.dataIndex)*(o?-1:1):a}))}var z=a("41ef");function B(e){var t={};function a(e,a,o){var i=e;while(i&&i.depth>1)i=i.parentNode;var r=a.getColorFromPalette(i.name||i.dataIndex+"",t);return e.depth>1&&Object(n["C"])(r)&&(r=Object(z["c"])(r,(e.depth-1)/(o-1)*.5)),r}e.eachSeriesByType("sunburst",(function(e){var t=e.getData(),o=t.tree;o.eachNode((function(i){var r=i.getModel(),l=r.getModel("itemStyle").getItemStyle();l.fill||(l.fill=a(i,e,o.root.height));var s=t.ensureUniqueItemVisual(i.dataIndex,"style");Object(n["m"])(s,l)}))}))}var H=a("d3f4");function W(e){e.registerChartView(S),e.registerSeriesModel(k),e.registerLayout(Object(n["h"])(E,"sunburst")),e.registerProcessor(Object(n["h"])(H["a"],"sunburst")),e.registerVisual(B),O(e)}},acf6:function(e,t,a){"use strict";a.d(t,"a",(function(){return y}));var o=a("22b4"),n=a("7fae"),i=a("1830"),r=a("4f85"),l=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a.hasSymbolVisual=!0,a}return Object(n["a"])(t,e),t.prototype.getInitialData=function(e,t){return Object(i["a"])(null,this,{useEncodeDefaulter:!0})},t.prototype.getProgressive=function(){var e=this.option.progressive;return null==e?this.option.large?5e3:this.get("progressive"):e},t.prototype.getProgressiveThreshold=function(){var e=this.option.progressiveThreshold;return null==e?this.option.large?1e4:this.get("progressiveThreshold"):e},t.prototype.brushSelector=function(e,t,a){return a.point(t.getItemLayout(e))},t.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},t.type="series.scatter",t.dependencies=["grid","polar","geo","singleAxis","calendar"],t.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},t}(r["b"]),s=l,d=a("f706"),u=a("c965"),c=a("87c3"),h=a("e887"),p=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a}return Object(n["a"])(t,e),t.prototype.render=function(e,t,a){var o=e.getData(),n=this._updateSymbolDraw(o,e);n.updateData(o,{clipShape:this._getClipShape(e)}),this._finished=!0},t.prototype.incrementalPrepareRender=function(e,t,a){var o=e.getData(),n=this._updateSymbolDraw(o,e);n.incrementalPrepareUpdate(o),this._finished=!1},t.prototype.incrementalRender=function(e,t,a){this._symbolDraw.incrementalUpdate(e,t.getData(),{clipShape:this._getClipShape(t)}),this._finished=e.end===t.getData().count()},t.prototype.updateTransform=function(e,t,a){var o=e.getData();if(this.group.dirty(),!this._finished||o.count()>1e4)return{update:!0};var n=Object(c["a"])("").reset(e,t,a);n.progress&&n.progress({start:0,end:o.count(),count:o.count()},o),this._symbolDraw.updateLayout(o)},t.prototype.eachRendered=function(e){this._symbolDraw&&this._symbolDraw.eachRendered(e)},t.prototype._getClipShape=function(e){if(e.get("clip",!0)){var t=e.coordinateSystem;return t&&t.getArea&&t.getArea(.1)}},t.prototype._updateSymbolDraw=function(e,t){var a=this._symbolDraw,o=t.pipelineContext,n=o.large;return a&&n===this._isLargeDraw||(a&&a.remove(),a=this._symbolDraw=n?new u["a"]:new d["a"],this._isLargeDraw=n,this.group.removeAll()),this.group.add(a.group),a},t.prototype.remove=function(e,t){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},t.prototype.dispose=function(){},t.type="scatter",t}(h["a"]),g=p,f=a("8702");function y(e){Object(o["a"])(f["a"]),e.registerSeriesModel(s),e.registerChartView(g),e.registerLayout(Object(c["a"])("scatter"))}},c835b:function(e,t,a){"use strict";a.d(t,"a",(function(){return T}));var o=a("7fae"),n=a("d4d1"),i=a("2dc5"),r=a("deca"),l=a("c7a2"),s=a("7d6c"),d=a("7837"),u=a("6d8b"),c=a("80f0"),h=a("e887"),p=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a._layers=[],a}return Object(o["a"])(t,e),t.prototype.render=function(e,t,a){var o=e.getData(),l=this,h=this.group,p=e.getLayerSeries(),f=o.getLayout("layoutInfo"),y=f.rect,v=f.boundaryGap;function m(e){return e.name}h.x=0,h.y=y.y+v[0];var b=new c["a"](this._layersSeries||[],p,m,m),x=[];function w(t,a,u){var c=l._layers;if("remove"!==t){for(var f,y,v=[],m=[],b=p[a].indices,w=0;w<b.length;w++){var I=o.getItemLayout(b[w]),L=I.x,_=I.y0,O=I.y;v.push(L,_),m.push(L,_+O),f=o.getItemVisual(b[w],"style")}var M=o.getItemLayout(b[0]),N=e.getModel("label"),S=N.get("margin"),j=e.getModel("emphasis");if("add"===t){var D=x[a]=new i["a"];y=new n["a"]({shape:{points:v,stackedOnPoints:m,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),D.add(y),h.add(D),e.isAnimationEnabled()&&y.setClipPath(g(y.getBoundingRect(),e,(function(){y.removeClipPath()})))}else{D=c[u];y=D.childAt(0),h.add(D),x[a]=D,r["h"](y,{shape:{points:v,stackedOnPoints:m}},e),Object(r["g"])(y)}Object(d["g"])(y,Object(d["e"])(e),{labelDataIndex:b[w-1],defaultText:o.getName(b[w-1]),inheritColor:f.fill},{normal:{verticalAlign:"middle"}}),y.setTextConfig({position:null,local:!0});var T=y.getTextContent();T&&(T.x=M.x-S,T.y=M.y0+M.y/2),y.useStyle(f),o.setItemGraphicEl(a,y),Object(s["I"])(y,e),Object(s["J"])(y,j.get("focus"),j.get("blurScope"),j.get("disabled"))}else h.remove(c[a])}b.add(Object(u["c"])(w,this,"add")).update(Object(u["c"])(w,this,"update")).remove(Object(u["c"])(w,this,"remove")).execute(),this._layersSeries=p,this._layers=x},t.type="themeRiver",t}(h["a"]);function g(e,t,a){var o=new l["a"]({shape:{x:e.x-10,y:e.y-10,width:0,height:e.height+20}});return r["c"](o,{shape:{x:e.x-50,width:e.width+100,height:e.height+20}},t,a),o}var f=p,y=a("4f85"),v=a("b1d4"),m=a("2f45"),b=a("b682"),x=a("e0d3"),w=a("c4a3"),I=a("217c"),L=2,_=function(e){function t(){var a=null!==e&&e.apply(this,arguments)||this;return a.type=t.type,a}return Object(o["a"])(t,e),t.prototype.init=function(t){e.prototype.init.apply(this,arguments),this.legendVisualProvider=new w["a"](u["c"](this.getData,this),u["c"](this.getRawData,this))},t.prototype.fixData=function(e){var t=e.length,a={},o=Object(x["j"])(e,(function(e){return a.hasOwnProperty(e[0]+"")||(a[e[0]+""]=-1),e[2]})),n=[];o.buckets.each((function(e,t){n.push({name:t,dataList:e})}));for(var i=n.length,r=0;r<i;++r){for(var l=n[r].name,s=0;s<n[r].dataList.length;++s){var d=n[r].dataList[s][0]+"";a[d]=r}for(var d in a)a.hasOwnProperty(d)&&a[d]!==r&&(a[d]=r,e[t]=[d,0,l],t++)}return e},t.prototype.getInitialData=function(e,t){for(var a=this.getReferringComponents("singleAxis",x["b"]).models[0],o=a.get("type"),n=u["n"](e.data,(function(e){return void 0!==e[2]})),i=this.fixData(n||[]),r=[],l=this.nameMap=u["f"](),s=0,d=0;d<i.length;++d)r.push(i[d][L]),l.get(i[d][L])||(l.set(i[d][L],s),s++);var c=Object(v["a"])(i,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:Object(m["a"])(o)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,h=new b["a"](c,this);return h.initData(i),h},t.prototype.getLayerSeries=function(){for(var e=this.getData(),t=e.count(),a=[],o=0;o<t;++o)a[o]=o;var n=e.mapDimension("single"),i=Object(x["j"])(a,(function(t){return e.get("name",t)})),r=[];return i.buckets.each((function(t,a){t.sort((function(t,a){return e.get(n,t)-e.get(n,a)})),r.push({name:a,indices:t})})),r},t.prototype.getAxisTooltipData=function(e,t,a){u["t"](e)||(e=e?[e]:[]);for(var o,n=this.getData(),i=this.getLayerSeries(),r=[],l=i.length,s=0;s<l;++s){for(var d=Number.MAX_VALUE,c=-1,h=i[s].indices.length,p=0;p<h;++p){var g=n.get(e[0],i[s].indices[p]),f=Math.abs(g-t);f<=d&&(o=g,d=f,c=i[s].indices[p])}r.push(c)}return{dataIndices:r,nestestValue:o}},t.prototype.formatTooltip=function(e,t,a){var o=this.getData(),n=o.getName(e),i=o.get(o.mapDimension("value"),e);return Object(I["c"])("nameValue",{name:n,value:i})},t.type="series.themeRiver",t.dependencies=["singleAxis"],t.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},t}(y["b"]),O=_,M=a("3842");function N(e,t){e.eachSeriesByType("themeRiver",(function(e){var t=e.getData(),a=e.coordinateSystem,o={},n=a.getRect();o.rect=n;var i=e.get("boundaryGap"),r=a.getAxis();if(o.boundaryGap=i,"horizontal"===r.orient){i[0]=M["o"](i[0],n.height),i[1]=M["o"](i[1],n.height);var l=n.height-i[0]-i[1];S(t,e,l)}else{i[0]=M["o"](i[0],n.width),i[1]=M["o"](i[1],n.width);var s=n.width-i[0]-i[1];S(t,e,s)}t.setLayout("layoutInfo",o)}))}function S(e,t,a){if(e.count())for(var o,n=t.coordinateSystem,i=t.getLayerSeries(),r=e.mapDimension("single"),l=e.mapDimension("value"),s=u["H"](i,(function(t){return u["H"](t.indices,(function(t){var a=n.dataToPoint(e.get(r,t));return a[1]=e.get(l,t),a}))})),d=j(s),c=d.y0,h=a/d.max,p=i.length,g=i[0].indices.length,f=0;f<g;++f){o=c[f]*h,e.setItemLayout(i[0].indices[f],{layerIndex:0,x:s[0][f][0],y0:o,y:s[0][f][1]*h});for(var y=1;y<p;++y)o+=s[y-1][f][1]*h,e.setItemLayout(i[y].indices[f],{layerIndex:y,x:s[y][f][0],y0:o,y:s[y][f][1]*h})}}function j(e){for(var t=e.length,a=e[0].length,o=[],n=[],i=0,r=0;r<a;++r){for(var l=0,s=0;s<t;++s)l+=e[s][r][1];l>i&&(i=l),o.push(l)}for(var d=0;d<a;++d)n[d]=(i-o[d])/2;i=0;for(var u=0;u<a;++u){var c=o[u]+n[u];c>i&&(i=c)}return{y0:n,max:i}}var D=a("d3f4");function T(e){e.registerChartView(f),e.registerSeriesModel(O),e.registerLayout(N),e.registerProcessor(Object(D["a"])("themeRiver"))}}}]);