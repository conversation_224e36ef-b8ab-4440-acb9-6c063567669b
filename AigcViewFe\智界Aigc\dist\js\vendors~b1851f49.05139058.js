(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~b1851f49"],{2401:function(n,t){(function(){"use strict";var n=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=function(n){var t=typeof n;return null===n?"null":"object"===t&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":t},e=function(n){return function(e){return t(e)===n}},r=function(n){return function(t){return typeof t===n}},o=e("string"),i=e("object"),u=e("array"),a=r("boolean"),c=r("function"),s=r("number"),f=function(){},l=function(n){return function(){return n}},d=function(n){return n},m=function(n,t){return n===t},g=function(n){return function(t){return!n(t)}},v=l(!1),p=l(!0),h=function(){return y},y=function(){var n=function(n){return n()},t=d,e={fold:function(n,t){return n()},isSome:v,isNone:p,getOr:t,getOrThunk:n,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:l(null),getOrUndefined:l(void 0),or:t,orThunk:n,map:h,each:f,bind:h,exists:v,forall:p,filter:function(){return h()},toArray:function(){return[]},toString:l("none()")};return e}(),b=function(n){var t=l(n),e=function(){return o},r=function(t){return t(n)},o={fold:function(t,e){return e(n)},isSome:p,isNone:v,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:e,orThunk:e,map:function(t){return b(t(n))},each:function(t){t(n)},bind:r,exists:r,forall:r,filter:function(t){return t(n)?o:y},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},k=function(n){return null===n||void 0===n?y:b(n)},C={some:b,none:h,from:k},S=Array.prototype.slice,x=Array.prototype.push,O=function(n,t){for(var e=n.length,r=new Array(e),o=0;o<e;o++){var i=n[o];r[o]=t(i,o)}return r},w=function(n,t){for(var e=0,r=n.length;e<r;e++){var o=n[e];t(o,e)}},N=function(n,t){for(var e=[],r=0,o=n.length;r<o;r++){var i=n[r];t(i,r)&&e.push(i)}return e},T=function(n,t){if(0===n.length)return[];for(var e=t(n[0]),r=[],o=[],i=0,u=n.length;i<u;i++){var a=n[i],c=t(a);c!==e&&(r.push(o),o=[]),e=c,o.push(a)}return 0!==o.length&&r.push(o),r},A=function(n,t,e){return w(n,(function(n,r){e=t(e,n,r)})),e},L=function(n,t,e){for(var r=0,o=n.length;r<o;r++){var i=n[r];if(t(i,r))return C.some(i);if(e(i,r))break}return C.none()},D=function(n,t){return L(n,t,v)},P=function(n){for(var t=[],e=0,r=n.length;e<r;++e){if(!u(n[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+n);x.apply(t,n[e])}return t},R=function(n,t){return P(O(n,t))},E=function(n){var t=S.call(n,0);return t.reverse(),t},B=function(n,t){return t>=0&&t<n.length?C.some(n[t]):C.none()},M=function(n){return B(n,0)},I=function(n){return B(n,n.length-1)},_=function(n,t){for(var e=0;e<n.length;e++){var r=t(n[e],e);if(r.isSome())return r}return C.none()},U=function(){return U=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++)for(var o in t=arguments[e],t)Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o]);return n},U.apply(this,arguments)};function F(n,t,e){if(e||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return n.concat(r||Array.prototype.slice.call(t))}var j=function(n){var t,e=!1;return function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return e||(e=!0,t=n.apply(null,r)),t}},$=function(n,t,e,r){var o=n.isiOS()&&!0===/ipad/i.test(e),i=n.isiOS()&&!o,u=n.isiOS()||n.isAndroid(),a=u||r("(pointer:coarse)"),c=o||!i&&u&&r("(min-device-width:768px)"),s=i||u&&!c,f=t.isSafari()&&n.isiOS()&&!1===/safari/i.test(e),d=!s&&!c&&!f;return{isiPad:l(o),isiPhone:l(i),isTablet:l(c),isPhone:l(s),isTouch:l(a),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:l(f),isDesktop:l(d)}},K=function(n,t){for(var e=0;e<n.length;e++){var r=n[e];if(r.test(t))return r}},z=function(n,t){var e=K(n,t);if(!e)return{major:0,minor:0};var r=function(n){return Number(t.replace(e,"$"+n))};return W(r(1),r(2))},H=function(n,t){var e=String(t).toLowerCase();return 0===n.length?V():z(n,e)},V=function(){return W(0,0)},W=function(n,t){return{major:n,minor:t}},q={nu:W,detect:H,unknown:V},Q=function(n,t){return _(t.brands,(function(t){var e=t.brand.toLowerCase();return D(n,(function(n){var t;return e===(null===(t=n.brand)||void 0===t?void 0:t.toLowerCase())})).map((function(n){return{current:n.name,version:q.nu(parseInt(t.version,10),0)}}))}))},X=function(n,t){var e=String(t).toLowerCase();return D(n,(function(n){return n.search(e)}))},J=function(n,t){return X(n,t).map((function(n){var e=q.detect(n.versionRegexes,t);return{current:n.name,version:e}}))},G=function(n,t){return X(n,t).map((function(n){var e=q.detect(n.versionRegexes,t);return{current:n.name,version:e}}))},Y=function(n,t){return-1!==n.indexOf(t)},Z=function(n){return function(t){return t.replace(n,"")}},nn=Z(/^\s+|\s+$/g),tn=function(n){return n.length>0},en=function(n){return!tn(n)},rn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,on=function(n){return function(t){return Y(t,n)}},un=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Y(n,"edge/")&&Y(n,"chrome")&&Y(n,"safari")&&Y(n,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,rn],search:function(n){return Y(n,"chrome")&&!Y(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Y(n,"msie")||Y(n,"trident")}},{name:"Opera",versionRegexes:[rn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:on("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:on("firefox")},{name:"Safari",versionRegexes:[rn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Y(n,"safari")||Y(n,"mobile/"))&&Y(n,"applewebkit")}}],an=[{name:"Windows",search:on("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Y(n,"iphone")||Y(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:on("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:on("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:on("linux"),versionRegexes:[]},{name:"Solaris",search:on("sunos"),versionRegexes:[]},{name:"FreeBSD",search:on("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:on("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],cn={browsers:l(un),oses:l(an)},sn="Edge",fn="Chrome",ln="IE",dn="Opera",mn="Firefox",gn="Safari",vn=function(){return pn({current:void 0,version:q.unknown()})},pn=function(n){var t=n.current,e=n.version,r=function(n){return function(){return t===n}};return{current:t,version:e,isEdge:r(sn),isChrome:r(fn),isIE:r(ln),isOpera:r(dn),isFirefox:r(mn),isSafari:r(gn)}},hn={unknown:vn,nu:pn,edge:l(sn),chrome:l(fn),ie:l(ln),opera:l(dn),firefox:l(mn),safari:l(gn)},yn="Windows",bn="iOS",kn="Android",Cn="Linux",Sn="OSX",xn="Solaris",On="FreeBSD",wn="ChromeOS",Nn=function(){return Tn({current:void 0,version:q.unknown()})},Tn=function(n){var t=n.current,e=n.version,r=function(n){return function(){return t===n}};return{current:t,version:e,isWindows:r(yn),isiOS:r(bn),isAndroid:r(kn),isOSX:r(Sn),isLinux:r(Cn),isSolaris:r(xn),isFreeBSD:r(On),isChromeOS:r(wn)}},An={unknown:Nn,nu:Tn,windows:l(yn),ios:l(bn),android:l(kn),linux:l(Cn),osx:l(Sn),solaris:l(xn),freebsd:l(On),chromeos:l(wn)},Ln=function(n,t,e){var r=cn.browsers(),o=cn.oses(),i=t.bind((function(n){return Q(r,n)})).orThunk((function(){return J(r,n)})).fold(hn.unknown,hn.nu),u=G(o,n).fold(An.unknown,An.nu),a=$(u,i,n,e);return{browser:i,os:u,deviceType:a}},Dn={detect:Ln},Pn=function(n){return window.matchMedia(n).matches},Rn=j((function(){return Dn.detect(navigator.userAgent,C.from(navigator.userAgentData),Pn)})),En=function(){return Rn()},Bn=function(n,t,e){return 0!==(n.compareDocumentPosition(t)&e)},Mn=function(n,t){return Bn(n,t,Node.DOCUMENT_POSITION_CONTAINED_BY)},In=1,_n=function(n,t){var e=t||document,r=e.createElement("div");if(r.innerHTML=n,!r.hasChildNodes()||r.childNodes.length>1)throw new Error("HTML must have a single root node");return jn(r.childNodes[0])},Un=function(n,t){var e=t||document,r=e.createElement(n);return jn(r)},Fn=function(n,t){var e=t||document,r=e.createTextNode(n);return jn(r)},jn=function(n){if(null===n||void 0===n)throw new Error("Node cannot be null or undefined");return{dom:n}},$n=function(n,t,e){return C.from(n.dom.elementFromPoint(t,e)).map(jn)},Kn={fromHtml:_n,fromTag:Un,fromText:Fn,fromDom:jn,fromPoint:$n},zn=function(n,t){var e=n.dom;if(e.nodeType!==In)return!1;var r=e;if(void 0!==r.matches)return r.matches(t);if(void 0!==r.msMatchesSelector)return r.msMatchesSelector(t);if(void 0!==r.webkitMatchesSelector)return r.webkitMatchesSelector(t);if(void 0!==r.mozMatchesSelector)return r.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},Hn=function(n,t){return n.dom===t.dom},Vn=function(n,t){var e=n.dom,r=t.dom;return e!==r&&e.contains(r)},Wn=function(n,t){return Mn(n.dom,t.dom)},qn=function(n,t){return En().browser.isIE()?Wn(n,t):Vn(n,t)},Qn=zn,Xn=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),Jn=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),Gn=tinymce.util.Tools.resolve("tinymce.util.VK"),Yn=Object.keys,Zn=function(n,t){for(var e=Yn(n),r=0,o=e.length;r<o;r++){var i=e[r],u=n[i];t(u,i)}},nt=function(n){return function(t,e){n[e]=t}},tt=function(n,t,e,r){var o={};return Zn(n,(function(n,o){(t(n,o)?e:r)(n,o)})),o},et=function(n,t){var e={};return tt(n,t,nt(e),f),e};"undefined"!==typeof window?window:Function("return this;")();var rt=function(n){var t=n.dom.nodeName;return t.toLowerCase()},ot=function(n){return n.dom.nodeType},it=function(n){return function(t){return ot(t)===n}},ut=it(In),at=function(n){return function(t){return ut(t)&&rt(t)===n}},ct=function(n,t,e){if(!(o(e)||a(e)||s(e)))throw new Error("Attribute value was not simple");n.setAttribute(t,e+"")},st=function(n,t){var e=n.dom;Zn(t,(function(n,t){ct(e,t,n)}))},ft=function(n){return A(n.dom.attributes,(function(n,t){return n[t.name]=t.value,n}),{})},lt=function(n){return C.from(n.dom.parentNode).map(Kn.fromDom)},dt=function(n){return O(n.dom.childNodes,Kn.fromDom)},mt=function(n,t){var e=n.dom.childNodes;return C.from(e[t]).map(Kn.fromDom)},gt=function(n){return mt(n,0)},vt=function(n){return mt(n,n.dom.childNodes.length-1)},pt=function(n,t){var e=lt(n);e.each((function(e){e.dom.insertBefore(t.dom,n.dom)}))},ht=function(n,t){n.dom.appendChild(t.dom)},yt=function(n,t){w(t,(function(t){pt(n,t)}))},bt=function(n,t){w(t,(function(t){ht(n,t)}))},kt=function(n){var t=n.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Ct=function(n,t){return Kn.fromDom(n.dom.cloneNode(t))},St=function(n){return Ct(n,!0)},xt=function(n,t){var e=Kn.fromTag(t),r=ft(n);return st(e,r),e},Ot=function(n,t){var e=xt(n,t);pt(n,e);var r=dt(n);return bt(e,r),kt(n),e},wt=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Nt=tinymce.util.Tools.resolve("tinymce.util.Tools"),Tt=function(n){return function(t){return t&&t.nodeName.toLowerCase()===n}},At=function(n){return function(t){return t&&n.test(t.nodeName)}},Lt=function(n){return n&&3===n.nodeType},Dt=At(/^(OL|UL|DL)$/),Pt=At(/^(OL|UL)$/),Rt=Tt("ol"),Et=At(/^(LI|DT|DD)$/),Bt=At(/^(DT|DD)$/),Mt=At(/^(TH|TD)$/),It=Tt("br"),_t=function(n){return n.parentNode.firstChild===n},Ut=function(n,t){return t&&!!n.schema.getTextBlockElements()[t.nodeName]},Ft=function(n,t){return n&&n.nodeName in t},jt=function(n,t){return!!It(t)&&(n.isBlock(t.nextSibling)&&!It(t.previousSibling))},$t=function(n,t,e){var r=n.isEmpty(t);return!(e&&n.select("span[data-mce-type=bookmark]",t).length>0)&&r},Kt=function(n,t){return n.isChildOf(t,n.getRoot())},zt=function(n){return n.getParam("lists_indent_on_tab",!0)},Ht=function(n){var t=n.getParam("forced_root_block","p");return!1===t?"":!0===t?"p":t},Vt=function(n){return n.getParam("forced_root_block_attrs",{})},Wt=function(n,t){var e,r,o,i=n.dom,u=n.schema.getBlockElements(),a=i.createFragment(),c=Ht(n);if(c&&(r=i.create(c),r.tagName===c.toUpperCase()&&i.setAttribs(r,Vt(n)),Ft(t.firstChild,u)||a.appendChild(r)),t)while(e=t.firstChild){var s=e.nodeName;o||"SPAN"===s&&"bookmark"===e.getAttribute("data-mce-type")||(o=!0),Ft(e,u)?(a.appendChild(e),r=null):c?(r||(r=i.create(c),a.appendChild(r)),r.appendChild(e)):a.appendChild(e)}return c?o||r.appendChild(i.create("br",{"data-mce-bogus":"1"})):a.appendChild(i.create("br")),a},qt=wt.DOM,Qt=function(n,t,e){var r=function(n){Nt.each(o,(function(t){n.parentNode.insertBefore(t,e.parentNode)})),qt.remove(n)},o=qt.select('span[data-mce-type="bookmark"]',t),i=Wt(n,e),u=qt.createRng();u.setStartAfter(e),u.setEndAfter(t);for(var a=u.extractContents(),c=a.firstChild;c;c=c.firstChild)if("LI"===c.nodeName&&n.dom.isEmpty(c)){qt.remove(c);break}n.dom.isEmpty(a)||qt.insertAfter(a,t),qt.insertAfter(i,t),$t(n.dom,e.parentNode)&&r(e.parentNode),qt.remove(e),$t(n.dom,t)&&qt.remove(t)},Xt=at("dd"),Jt=at("dt"),Gt=function(n,t){Xt(t)?Ot(t,"dt"):Jt(t)&&lt(t).each((function(e){return Qt(n,e.dom,t.dom)}))},Yt=function(n){Jt(n)&&Ot(n,"dd")},Zt=function(n,t,e){w(e,"Indent"===t?Yt:function(t){return Gt(n,t)})},ne=function(n,t){if(Lt(n))return{container:n,offset:t};var e=Xn.getNode(n,t);return Lt(e)?{container:e,offset:t>=n.childNodes.length?e.data.length:0}:e.previousSibling&&Lt(e.previousSibling)?{container:e.previousSibling,offset:e.previousSibling.data.length}:e.nextSibling&&Lt(e.nextSibling)?{container:e.nextSibling,offset:0}:{container:n,offset:t}},te=function(n){var t=n.cloneRange(),e=ne(n.startContainer,n.startOffset);t.setStart(e.container,e.offset);var r=ne(n.endContainer,n.endOffset);return t.setEnd(r.container,r.offset),t},ee=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),re=function(n,t){var e=t||n.selection.getStart(!0);return n.dom.getParent(e,"OL,UL,DL",fe(n,e))},oe=function(n,t){return n&&1===t.length&&t[0]===n},ie=function(n){return N(n.querySelectorAll("ol,ul,dl"),Dt)},ue=function(n){var t=re(n),e=n.selection.getSelectedBlocks();return oe(t,e)?ie(t):N(e,(function(n){return Dt(n)&&t!==n}))},ae=function(n,t){var e=Nt.map(t,(function(t){var e=n.dom.getParent(t,"li,dd,dt",fe(n,t));return e||t}));return ee.unique(e)},ce=function(n){var t=n.selection.getSelectedBlocks();return N(ae(n,t),Et)},se=function(n){return N(ce(n),Bt)},fe=function(n,t){var e=n.dom.getParents(t,"TD,TH");return e.length>0?e[0]:n.getBody()},le=function(n,t){var e=n.dom.getParents(t,"ol,ul",fe(n,t));return I(e)},de=function(n){var t=le(n,n.selection.getStart()),e=N(n.selection.getSelectedBlocks(),Pt);return t.toArray().concat(e)},me=function(n){var t=de(n);return ge(n,t)},ge=function(n,t){var e=O(t,(function(t){return le(n,t).getOr(t)}));return ee.unique(e)},ve=function(n,t,e){return void 0===e&&(e=m),n.exists((function(n){return e(n,t)}))},pe=function(n,t,e){return n.isSome()&&t.isSome()?C.some(e(n.getOrDie(),t.getOrDie())):C.none()},he=function(n,t){var e=t||document,r=e.createDocumentFragment();return w(n,(function(n){r.appendChild(n.dom)})),Kn.fromDom(r)},ye=function(n,t,e){return n.fire("ListMutation",{action:t,element:e})},be=function(n){return void 0!==n.style&&c(n.style.getPropertyValue)},ke=function(n,t,e){if(!o(e))throw new Error("CSS value must be a string: "+e);be(n)&&n.style.setProperty(t,e)},Ce=function(n,t,e){var r=n.dom;ke(r,t,e)},Se=function(n,t){ht(n.item,t.list)},xe=function(n){for(var t=1;t<n.length;t++)Se(n[t-1],n[t])},Oe=function(n,t){pe(I(n),M(t),Se)},we=function(n,t){var e={list:Kn.fromTag(t,n),item:Kn.fromTag("li",n)};return ht(e.list,e.item),e},Ne=function(n,t,e){for(var r=[],o=0;o<e;o++)r.push(we(n,t.listType));return r},Te=function(n,t){for(var e=0;e<n.length-1;e++)Ce(n[e].item,"list-style-type","none");I(n).each((function(n){st(n.list,t.listAttributes),st(n.item,t.itemAttributes),bt(n.item,t.content)}))},Ae=function(n,t){rt(n.list)!==t.listType&&(n.list=Ot(n.list,t.listType)),st(n.list,t.listAttributes)},Le=function(n,t,e){var r=Kn.fromTag("li",n);return st(r,t),bt(r,e),r},De=function(n,t){ht(n.list,t),n.item=t},Pe=function(n,t,e){var r=t.slice(0,e.depth);return I(r).each((function(t){var r=Le(n,e.itemAttributes,e.content);De(t,r),Ae(t,e)})),r},Re=function(n,t,e){var r=Ne(n,e,e.depth-t.length);return xe(r),Te(r,e),Oe(t,r),t.concat(r)},Ee=function(n,t){var e=A(t,(function(t,e){return e.depth>t.length?Re(n,t,e):Pe(n,t,e)}),[]);return M(e).map((function(n){return n.list}))},Be=function(n){return Qn(n,"OL,UL")},Me=function(n){return gt(n).exists(Be)},Ie=function(n){return vt(n).exists(Be)},_e=function(n){return n.depth>0},Ue=function(n){return n.isSelected},Fe=function(n){var t=dt(n),e=Ie(n)?t.slice(0,-1):t;return O(e,St)},je=function(n,t,e){return lt(n).filter(ut).map((function(r){return{depth:t,dirty:!1,isSelected:e,content:Fe(n),itemAttributes:ft(n),listAttributes:ft(r),listType:rt(r)}}))},$e=function(n,t){switch(n){case"Indent":t.depth++;break;case"Outdent":t.depth--;break;case"Flatten":t.depth=0}t.dirty=!0},Ke=function(n,t){n.listType=t.listType,n.listAttributes=U({},t.listAttributes)},ze=function(n){n.listAttributes=et(n.listAttributes,(function(n,t){return"start"!==t}))},He=function(n,t){var e=n[t].depth,r=function(n){return n.depth===e&&!n.dirty},o=function(n){return n.depth<e};return L(E(n.slice(0,t)),r,o).orThunk((function(){return L(n.slice(t+1),r,o)}))},Ve=function(n){return w(n,(function(t,e){He(n,e).fold((function(){t.dirty&&ze(t)}),(function(n){return Ke(t,n)}))})),n},We=function(n){var t=n,e=function(){return t},r=function(n){t=n};return{get:e,set:r}},qe=function(n,t,e,r){return gt(r).filter(Be).fold((function(){t.each((function(n){Hn(n.start,r)&&e.set(!0)}));var o=je(r,n,e.get());t.each((function(n){Hn(n.end,r)&&e.set(!1)}));var i=vt(r).filter(Be).map((function(r){return Qe(n,t,e,r)})).getOr([]);return o.toArray().concat(i)}),(function(r){return Qe(n,t,e,r)}))},Qe=function(n,t,e,r){return R(dt(r),(function(r){var o=Be(r)?Qe:qe,i=n+1;return o(i,t,e,r)}))},Xe=function(n,t){var e=We(!1),r=0;return O(n,(function(n){return{sourceList:n,entries:Qe(r,t,e,n)}}))},Je=function(n,t){var e=Ve(t);return O(e,(function(t){var e=he(t.content);return Kn.fromDom(Wt(n,e.dom))}))},Ge=function(n,t){var e=Ve(t);return Ee(n.contentDocument,e).toArray()},Ye=function(n,t){return R(T(t,_e),(function(t){var e=M(t).exists(_e);return e?Ge(n,t):Je(n,t)}))},Ze=function(n,t){w(N(n,Ue),(function(n){return $e(t,n)}))},nr=function(n){var t=O(ce(n),Kn.fromDom);return pe(D(t,g(Me)),D(E(t),g(Me)),(function(n,t){return{start:n,end:t}}))},tr=function(n,t,e){var r=Xe(t,nr(n));w(r,(function(t){Ze(t.entries,e);var r=Ye(n,t.entries);w(r,(function(t){ye(n,"Indent"===e?"IndentList":"OutdentList",t.dom)})),yt(t.sourceList,r),kt(t.sourceList)}))},er=function(n,t){var e=O(me(n),Kn.fromDom),r=O(se(n),Kn.fromDom),o=!1;if(e.length||r.length){var i=n.selection.getBookmark();tr(n,e,t),Zt(n,t,r),n.selection.moveToBookmark(i),n.selection.setRng(te(n.selection.getRng())),n.nodeChanged(),o=!0}return o},rr=function(n){return er(n,"Indent")},or=function(n){return er(n,"Outdent")},ir=function(n){return er(n,"Flatten")},ur=tinymce.util.Tools.resolve("tinymce.dom.BookmarkManager"),ar=wt.DOM,cr=function(n){var t={},e=function(e){var r=n[e?"startContainer":"endContainer"],o=n[e?"startOffset":"endOffset"];if(1===r.nodeType){var i=ar.create("span",{"data-mce-type":"bookmark"});r.hasChildNodes()?(o=Math.min(o,r.childNodes.length-1),e?r.insertBefore(i,r.childNodes[o]):ar.insertAfter(i,r.childNodes[o])):r.appendChild(i),r=i,o=0}t[e?"startContainer":"endContainer"]=r,t[e?"startOffset":"endOffset"]=o};return e(!0),n.collapsed||e(),t},sr=function(n){var t=function(t){var e,r=function(n){var t=n.parentNode.firstChild,e=0;while(t){if(t===n)return e;1===t.nodeType&&"bookmark"===t.getAttribute("data-mce-type")||e++,t=t.nextSibling}return-1},o=e=n[t?"startContainer":"endContainer"],i=n[t?"startOffset":"endOffset"];o&&(1===o.nodeType&&(i=r(o),o=o.parentNode,ar.remove(e),!o.hasChildNodes()&&ar.isBlock(o)&&o.appendChild(ar.create("br"))),n[t?"startContainer":"endContainer"]=o,n[t?"startOffset":"endOffset"]=i)};t(!0),t();var e=ar.createRng();return e.setStart(n.startContainer,n.startOffset),n.endContainer&&e.setEnd(n.endContainer,n.endOffset),te(e)},fr=function(n){switch(n){case"UL":return"ToggleUlList";case"OL":return"ToggleOlList";case"DL":return"ToggleDLList"}},lr=function(n){return/\btox\-/.test(n.className)},dr=function(n,t,e){var r=function(n){var r=L(n.parents,Dt,Mt).filter((function(n){return n.nodeName===t&&!lr(n)})).isSome();e(r)},o=n.dom.getParents(n.selection.getNode());return r({parents:o}),n.on("NodeChange",r),function(){return n.off("NodeChange",r)}},mr=function(n,t,e){var r=e["list-style-type"]?e["list-style-type"]:null;n.setStyle(t,"list-style-type",r)},gr=function(n,t){Nt.each(t,(function(t,e){n.setAttribute(e,t)}))},vr=function(n,t,e){gr(t,e["list-attributes"]),Nt.each(n.select("li",t),(function(n){gr(n,e["list-item-attributes"])}))},pr=function(n,t,e){mr(n,t,e),vr(n,t,e)},hr=function(n,t,e){Nt.each(e,(function(e){var r;return n.setStyle(t,(r={},r[e]="",r))}))},yr=function(n,t,e,r){var o=t[e?"startContainer":"endContainer"],i=t[e?"startOffset":"endOffset"];1===o.nodeType&&(o=o.childNodes[Math.min(i,o.childNodes.length-1)]||o),!e&&It(o.nextSibling)&&(o=o.nextSibling);while(o.parentNode!==r){if(Ut(n,o))return o;if(/^(TD|TH)$/.test(o.parentNode.nodeName))return o;o=o.parentNode}return o},br=function(n,t,e){for(var r,o=[],i=n.dom,u=yr(n,t,!0,e),a=yr(n,t,!1,e),c=[],s=u;s;s=s.nextSibling)if(c.push(s),s===a)break;return Nt.each(c,(function(t){if(Ut(n,t))return o.push(t),void(r=null);if(i.isBlock(t)||It(t))return It(t)&&i.remove(t),void(r=null);var u=t.nextSibling;ur.isBookmarkNode(t)&&(Dt(u)||Ut(n,u)||!u&&t.parentNode===e)?r=null:(r||(r=i.create("p"),t.parentNode.insertBefore(r,t),o.push(r)),r.appendChild(t))})),o},kr=function(n,t,e){var r=n.getStyle(t,"list-style-type"),o=e?e["list-style-type"]:"";return o=null===o?"":o,r===o},Cr=function(n,t,e){var r=n.selection.getRng(),o="LI",i=fe(n,n.selection.getStart(!0)),u=n.dom;if("false"!==u.getContentEditable(n.selection.getNode())){t=t.toUpperCase(),"DL"===t&&(o="DT");var a=cr(r),c=br(n,r,i);Nt.each(c,(function(r){var i,a=r.previousSibling,c=r.parentNode;Et(c)||(a&&Dt(a)&&a.nodeName===t&&kr(u,a,e)?(i=a,r=u.rename(r,o),a.appendChild(r)):(i=u.create(t),r.parentNode.insertBefore(i,r),i.appendChild(r),r=u.rename(r,o)),hr(u,r,["margin","margin-right","margin-bottom","margin-left","margin-top","padding","padding-right","padding-bottom","padding-left","padding-top"]),pr(u,i,e),Nr(n.dom,i))})),n.selection.setRng(sr(a))}},Sr=function(n,t){return n&&t&&Dt(n)&&n.nodeName===t.nodeName},xr=function(n,t,e){var r=n.getStyle(t,"list-style-type",!0),o=n.getStyle(e,"list-style-type",!0);return r===o},Or=function(n,t){return n.className===t.className},wr=function(n,t,e){return Sr(t,e)&&xr(n,t,e)&&Or(t,e)},Nr=function(n,t){var e,r;if(e=t.nextSibling,wr(n,t,e)){while(r=e.firstChild)t.appendChild(r);n.remove(e)}if(e=t.previousSibling,wr(n,t,e)){while(r=e.lastChild)t.insertBefore(r,t.firstChild);n.remove(e)}},Tr=function(n,t,e,r){if(t.nodeName!==e){var o=n.dom.rename(t,e);pr(n.dom,o,r),ye(n,fr(e),o)}else pr(n.dom,t,r),ye(n,fr(e),t)},Ar=function(n,t,e,r,o){var i=Dt(t);if(i&&t.nodeName===r&&!Lr(o))ir(n);else{Cr(n,r,o);var u=cr(n.selection.getRng()),a=i?F([t],e,!0):e;Nt.each(a,(function(t){Tr(n,t,r,o)})),n.selection.setRng(sr(u))}},Lr=function(n){return"list-style-type"in n},Dr=function(n,t,e,r){if(t!==n.getBody())if(t)if(t.nodeName!==e||Lr(r)||lr(t)){var o=cr(n.selection.getRng());pr(n.dom,t,r);var i=n.dom.rename(t,e);Nr(n.dom,i),n.selection.setRng(sr(o)),Cr(n,e,r),ye(n,fr(e),i)}else ir(n);else Cr(n,e,r),ye(n,fr(e),t)},Pr=function(n,t,e){var r=re(n),o=ue(n),u=i(e)?e:{};o.length>0?Ar(n,r,o,t,u):Dr(n,r,t,u)},Rr=wt.DOM,Er=function(n,t){var e=t.parentNode;if("LI"===e.nodeName&&e.firstChild===t){var r=e.previousSibling;r&&"LI"===r.nodeName?(r.appendChild(t),$t(n,e)&&Rr.remove(e)):Rr.setStyle(e,"listStyleType","none")}if(Dt(e)){r=e.previousSibling;r&&"LI"===r.nodeName&&r.appendChild(t)}},Br=function(n,t){var e=Nt.grep(n.select("ol,ul",t));Nt.each(e,(function(t){Er(n,t)}))},Mr=function(n,t,e,r){var o=t.startContainer,i=t.startOffset;if(Lt(o)&&(e?i<o.data.length:i>0))return o;var u=n.schema.getNonEmptyElements();1===o.nodeType&&(o=Xn.getNode(o,i));var a=new Jn(o,r);e&&jt(n.dom,o)&&a.next();while(o=a[e?"next":"prev2"]()){if("LI"===o.nodeName&&!o.hasChildNodes())return o;if(u[o.nodeName])return o;if(Lt(o)&&o.data.length>0)return o}},Ir=function(n,t){var e=t.childNodes;return 1===e.length&&!Dt(e[0])&&n.isBlock(e[0])},_r=function(n,t){Ir(n,t)&&n.remove(t.firstChild,!0)},Ur=function(n,t,e){var r,o=Ir(n,e)?e.firstChild:e;if(_r(n,t),!$t(n,t,!0))while(r=t.firstChild)o.appendChild(r)},Fr=function(n,t,e){var r,o=t.parentNode;if(Kt(n,t)&&Kt(n,e)){Dt(e.lastChild)&&(r=e.lastChild),o===e.lastChild&&It(o.previousSibling)&&n.remove(o.previousSibling);var i=e.lastChild;i&&It(i)&&t.hasChildNodes()&&n.remove(i),$t(n,e,!0)&&n.$(e).empty(),Ur(n,t,e),r&&e.appendChild(r);var u=qn(Kn.fromDom(e),Kn.fromDom(t)),a=u?n.getParents(t,Dt,e):[];n.remove(t),w(a,(function(t){$t(n,t)&&t!==n.getRoot()&&n.remove(t)}))}},jr=function(n,t,e){n.dom.$(e).empty(),Fr(n.dom,t,e),n.selection.setCursorLocation(e,0)},$r=function(n,t,e,r){var o=n.dom;if(o.isEmpty(r))jr(n,e,r);else{var i=cr(t);Fr(o,e,r),n.selection.setRng(sr(i))}},Kr=function(n,t,e,r){var o=cr(t);Fr(n.dom,e,r);var i=sr(o);n.selection.setRng(i)},zr=function(n,t){var e=n.dom,r=n.selection,o=r.getStart(),i=fe(n,o),u=e.getParent(r.getStart(),"LI",i);if(u){var a=u.parentNode;if(a===n.getBody()&&$t(e,a))return!0;var c=te(r.getRng()),s=e.getParent(Mr(n,c,t,i),"LI",i);if(s&&s!==u)return n.undoManager.transact((function(){t?$r(n,c,s,u):_t(u)?or(n):Kr(n,c,u,s)})),!0;if(!s&&!t&&0===c.startOffset&&0===c.endOffset)return n.undoManager.transact((function(){ir(n)})),!0}return!1},Hr=function(n,t,e){var r=n.getParent(t.parentNode,n.isBlock,e);n.remove(t),r&&n.isEmpty(r)&&n.remove(r)},Vr=function(n,t){var e=n.dom,r=n.selection.getStart(),o=fe(n,r),i=e.getParent(r,e.isBlock,o);if(i&&e.isEmpty(i)){var u=te(n.selection.getRng()),a=e.getParent(Mr(n,u,t,o),"LI",o);if(a)return n.undoManager.transact((function(){Hr(e,i,o),Nr(e,a.parentNode),n.selection.select(a,!0),n.selection.collapse(t)})),!0}return!1},Wr=function(n,t){return zr(n,t)||Vr(n,t)},qr=function(n){var t=n.selection.getStart(),e=fe(n,t),r=n.dom.getParent(t,"LI,DT,DD",e);return!!(r||ce(n).length>0)&&(n.undoManager.transact((function(){n.execCommand("Delete"),Br(n.dom,n.getBody())})),!0)},Qr=function(n,t){return n.selection.isCollapsed()?Wr(n,t):qr(n)},Xr=function(n){n.on("keydown",(function(t){t.keyCode===Gn.BACKSPACE?Qr(n,!1)&&t.preventDefault():t.keyCode===Gn.DELETE&&Qr(n,!0)&&t.preventDefault()}))},Jr=function(n){return{backspaceDelete:function(t){Qr(n,t)}}},Gr=function(n,t){var e=re(n);n.undoManager.transact((function(){i(t.styles)&&n.dom.setStyles(e,t.styles),i(t.attrs)&&Zn(t.attrs,(function(t,r){return n.dom.setAttrib(e,r,t)}))}))},Yr=function(n){var t=E(nn(n).split("")),e=O(t,(function(n,t){var e=n.toUpperCase().charCodeAt(0)-"A".charCodeAt(0)+1;return Math.pow(26,t)*e}));return A(e,(function(n,t){return n+t}),0)},Zr=function(n){if(n--,n<0)return"";var t=n%26,e=Math.floor(n/26),r=Zr(e),o=String.fromCharCode("A".charCodeAt(0)+t);return r+o},no=function(n){return/^[A-Z]+$/.test(n)},to=function(n){return/^[a-z]+$/.test(n)},eo=function(n){return/^[0-9]+$/.test(n)},ro=function(n){return eo(n)?2:no(n)?0:to(n)?1:en(n)?3:4},oo=function(n){switch(ro(n)){case 2:return C.some({listStyleType:C.none(),start:n});case 0:return C.some({listStyleType:C.some("upper-alpha"),start:Yr(n).toString()});case 1:return C.some({listStyleType:C.some("lower-alpha"),start:Yr(n).toString()});case 3:return C.some({listStyleType:C.none(),start:""});case 4:return C.none()}},io=function(n){var t=parseInt(n.start,10);return ve(n.listStyleType,"upper-alpha")?Zr(t):ve(n.listStyleType,"lower-alpha")?Zr(t).toLowerCase():n.start},uo=function(n){var t=re(n);Rt(t)&&n.windowManager.open({title:"List Properties",body:{type:"panel",items:[{type:"input",name:"start",label:"Start list at number",inputMode:"numeric"}]},initialData:{start:io({start:n.dom.getAttrib(t,"start","1"),listStyleType:C.some(n.dom.getStyle(t,"list-style-type"))})},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:function(t){var e=t.getData();oo(e.start).each((function(t){n.execCommand("mceListUpdate",!1,{attrs:{start:"1"===t.start?"":t.start},styles:{"list-style-type":t.listStyleType.getOr("")}})})),t.close()}})},ao=function(n,t){return function(){var e=re(n);return e&&e.nodeName===t}},co=function(n){n.addCommand("mceListProps",(function(){uo(n)}))},so=function(n){n.on("BeforeExecCommand",(function(t){var e=t.command.toLowerCase();"indent"===e?rr(n):"outdent"===e&&or(n)})),n.addCommand("InsertUnorderedList",(function(t,e){Pr(n,"UL",e)})),n.addCommand("InsertOrderedList",(function(t,e){Pr(n,"OL",e)})),n.addCommand("InsertDefinitionList",(function(t,e){Pr(n,"DL",e)})),n.addCommand("RemoveList",(function(){ir(n)})),co(n),n.addCommand("mceListUpdate",(function(t,e){i(e)&&Gr(n,e)})),n.addQueryStateHandler("InsertUnorderedList",ao(n,"UL")),n.addQueryStateHandler("InsertOrderedList",ao(n,"OL")),n.addQueryStateHandler("InsertDefinitionList",ao(n,"DL"))},fo=function(n){n.on("keydown",(function(t){t.keyCode!==Gn.TAB||Gn.metaKeyPressed(t)||n.undoManager.transact((function(){(t.shiftKey?or(n):rr(n))&&t.preventDefault()}))}))},lo=function(n){zt(n)&&fo(n),Xr(n)},mo=function(n){var t=function(t){return function(){return n.execCommand(t)}};n.hasPlugin("advlist")||(n.ui.registry.addToggleButton("numlist",{icon:"ordered-list",active:!1,tooltip:"Numbered list",onAction:t("InsertOrderedList"),onSetup:function(t){return dr(n,"OL",t.setActive)}}),n.ui.registry.addToggleButton("bullist",{icon:"unordered-list",active:!1,tooltip:"Bullet list",onAction:t("InsertUnorderedList"),onSetup:function(t){return dr(n,"UL",t.setActive)}}))},go=function(n){var t={text:"List properties...",icon:"ordered-list",onAction:function(){return n.execCommand("mceListProps")},onSetup:function(t){return dr(n,"OL",(function(n){return t.setDisabled(!n)}))}};n.ui.registry.addMenuItem("listprops",t),n.ui.registry.addContextMenu("lists",{update:function(t){var e=re(n,t);return Rt(e)?["listprops"]:[]}})};function vo(){n.add("lists",(function(n){return!1===n.hasPlugin("rtc",!0)?(lo(n),so(n)):co(n),mo(n),go(n),Jr(n)}))}vo()})()},4237:function(n,t,e){e("be7f")},"84ec":function(n,t,e){e("2401")},be7f:function(n,t){(function(){"use strict";var n=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=tinymce.util.Tools.resolve("tinymce.util.VK"),e=function(n){var t=typeof n;return null===n?"null":"object"===t&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":t},r=function(n){return function(t){return e(t)===n}},o=function(n){return function(t){return typeof t===n}},i=function(n){return function(t){return n===t}},u=r("string"),a=r("array"),c=i(null),s=o("boolean"),f=o("function"),l=function(){},d=function(n){return function(){return n}},m=function(n){return n},g=function(n,t){return n===t},v=d(!1),p=d(!0),h=function(){return y},y=function(){var n=function(n){return n()},t=m,e={fold:function(n,t){return n()},isSome:v,isNone:p,getOr:t,getOrThunk:n,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:d(null),getOrUndefined:d(void 0),or:t,orThunk:n,map:h,each:l,bind:h,exists:v,forall:p,filter:function(){return h()},toArray:function(){return[]},toString:d("none()")};return e}(),b=function(n){var t=d(n),e=function(){return o},r=function(t){return t(n)},o={fold:function(t,e){return e(n)},isSome:p,isNone:v,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:e,orThunk:e,map:function(t){return b(t(n))},each:function(t){t(n)},bind:r,exists:r,forall:r,filter:function(t){return t(n)?o:y},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},k=function(n){return null===n||void 0===n?y:b(n)},C={some:b,none:h,from:k},S=Array.prototype.indexOf,x=Array.prototype.push,O=function(n,t){return S.call(n,t)},w=function(n,t){return O(n,t)>-1},N=function(n,t){for(var e=n.length,r=new Array(e),o=0;o<e;o++){var i=n[o];r[o]=t(i,o)}return r},T=function(n,t){for(var e=0,r=n.length;e<r;e++){var o=n[e];t(o,e)}},A=function(n,t,e){return T(n,(function(n,r){e=t(e,n,r)})),e},L=function(n){for(var t=[],e=0,r=n.length;e<r;++e){if(!a(n[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+n);x.apply(t,n[e])}return t},D=function(n,t){return L(N(n,t))},P=function(n,t){for(var e=0;e<n.length;e++){var r=t(n[e],e);if(r.isSome())return r}return C.none()},R=function(n,t,e){return void 0===e&&(e=g),n.exists((function(n){return e(n,t)}))},E=function(n){for(var t=[],e=function(n){t.push(n)},r=0;r<n.length;r++)n[r].each(e);return t},B=function(n,t){return n?C.some(t):C.none()},M=function(n){var t=n.getParam("link_assume_external_targets",!1);return s(t)&&t?1:!u(t)||"http"!==t&&"https"!==t?0:t},I=function(n){return n.getParam("link_context_toolbar",!1,"boolean")},_=function(n){return n.getParam("link_list")},U=function(n){return n.getParam("default_link_target")},F=function(n){return n.getParam("target_list",!0)},j=function(n){return n.getParam("rel_list",[],"array")},$=function(n){return n.getParam("link_class_list",[],"array")},K=function(n){return n.getParam("link_title",!0,"boolean")},z=function(n){return n.getParam("allow_unsafe_link_target",!1,"boolean")},H=function(n){return n.getParam("link_quicklink",!1,"boolean")},V=function(n){return n.getParam("link_default_protocol","http","string")},W=tinymce.util.Tools.resolve("tinymce.util.Tools"),q=function(n){return u(n.value)?n.value:""},Q=function(n){return u(n.text)?n.text:u(n.title)?n.title:""},X=function(n,t){var e=[];return W.each(n,(function(n){var r=Q(n);if(void 0!==n.menu){var o=X(n.menu,t);e.push({text:r,items:o})}else{var i=t(n);e.push({text:r,value:i})}})),e},J=function(n){return void 0===n&&(n=q),function(t){return C.from(t).map((function(t){return X(t,n)}))}},G=function(n){return J(q)(n)},Y=function(n,t){return function(e){return{name:n,type:"listbox",label:t,items:e}}},Z={sanitize:G,sanitizeWith:J,createUi:Y,getValue:q},nn=function(){return nn=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++)for(var o in t=arguments[e],t)Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o]);return n},nn.apply(this,arguments)},tn=Object.keys,en=Object.hasOwnProperty,rn=function(n,t){for(var e=tn(n),r=0,o=e.length;r<o;r++){var i=e[r],u=n[i];t(u,i)}},on=function(n){return function(t,e){n[e]=t}},un=function(n,t,e,r){var o={};return rn(n,(function(n,o){(t(n,o)?e:r)(n,o)})),o},an=function(n,t){var e={};return un(n,t,on(e),l),e},cn=function(n,t){return en.call(n,t)},sn=function(n,t){return cn(n,t)&&void 0!==n[t]&&null!==n[t]},fn=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),ln=tinymce.util.Tools.resolve("tinymce.util.URI"),dn=function(n){return n&&"a"===n.nodeName.toLowerCase()},mn=function(n){return dn(n)&&!!pn(n)},gn=function(n,t){if(n.collapsed)return[];var e=n.cloneContents(),r=new fn(e.firstChild,e),o=[],i=e.firstChild;do{t(i)&&o.push(i)}while(i=r.next());return o},vn=function(n){return/^\w+:/i.test(n)},pn=function(n){var t=n.getAttribute("data-mce-href");return t||n.getAttribute("href")},hn=function(n,t){var e=["noopener"],r=n?n.split(/\s+/):[],o=function(n){return W.trim(n.sort().join(" "))},i=function(n){return n=u(n),n.length>0?n.concat(e):e},u=function(n){return n.filter((function(n){return-1===W.inArray(e,n)}))},a=t?i(r):u(r);return a.length>0?o(a):""},yn=function(n){return n.replace(/\uFEFF/g,"")},bn=function(n,t){return t=t||n.selection.getNode(),On(t)?n.dom.select("a[href]",t)[0]:n.dom.getParent(t,"a[href]")},kn=function(n,t){var e=t?t.innerText||t.textContent:n.getContent({format:"text"});return yn(e)},Cn=function(n){return W.grep(n,mn).length>0},Sn=function(n){return gn(n,mn).length>0},xn=function(n){var t=n.schema.getTextInlineElements(),e=function(n){return 1===n.nodeType&&!dn(n)&&!cn(t,n.nodeName.toLowerCase())},r=gn(n.selection.getRng(),e);return 0===r.length},On=function(n){return n&&"FIGURE"===n.nodeName&&/\bimage\b/i.test(n.className)},wn=function(n){var t=["title","rel","class","target"];return A(t,(function(t,e){return n[e].each((function(n){t[e]=n.length>0?n:null})),t}),{href:n.href})},Nn=function(n,t){return"http"!==t&&"https"!==t||vn(n)?n:t+"://"+n},Tn=function(n,t){var e=nn({},t);if(!(j(n).length>0)&&!1===z(n)){var r=hn(e.rel,"_blank"===e.target);e.rel=r||null}return C.from(e.target).isNone()&&!1===F(n)&&(e.target=U(n)),e.href=Nn(e.href,M(n)),e},An=function(n,t,e,r){e.each((function(n){cn(t,"innerText")?t.innerText=n:t.textContent=n})),n.dom.setAttribs(t,r),n.selection.select(t)},Ln=function(n,t,e,r){On(t)?Un(n,t,r):e.fold((function(){n.execCommand("mceInsertLink",!1,r)}),(function(t){n.insertContent(n.dom.createHTML("a",r,n.dom.encode(t)))}))},Dn=function(n,t,e){var r=n.selection.getNode(),o=bn(n,r),i=Tn(n,wn(e));n.undoManager.transact((function(){e.href===t.href&&t.attach(),o?(n.focus(),An(n,o,e.text,i)):Ln(n,r,e.text,i)}))},Pn=function(n){var t=n.dom,e=n.selection,r=e.getBookmark(),o=e.getRng().cloneRange(),i=t.getParent(o.startContainer,"a[href]",n.getBody()),u=t.getParent(o.endContainer,"a[href]",n.getBody());i&&o.setStartBefore(i),u&&o.setEndAfter(u),e.setRng(o),n.execCommand("unlink"),e.moveToBookmark(r)},Rn=function(n){n.undoManager.transact((function(){var t=n.selection.getNode();On(t)?_n(n,t):Pn(n),n.focus()}))},En=function(n){var t=n.class,e=n.href,r=n.rel,o=n.target,i=n.text,u=n.title;return an({class:t.getOrNull(),href:e,rel:r.getOrNull(),target:o.getOrNull(),text:i.getOrNull(),title:u.getOrNull()},(function(n,t){return!1===c(n)}))},Bn=function(n,t){var e=t.href;return nn(nn({},t),{href:ln.isDomSafe(e,"a",n.settings)?e:""})},Mn=function(n,t,e){var r=Bn(n,e);n.hasPlugin("rtc",!0)?n.execCommand("createlink",!1,En(r)):Dn(n,t,r)},In=function(n){n.hasPlugin("rtc",!0)?n.execCommand("unlink"):Rn(n)},_n=function(n,t){var e=n.dom.select("img",t)[0];if(e){var r=n.dom.getParents(e,"a[href]",t)[0];r&&(r.parentNode.insertBefore(e,r),n.dom.remove(r))}},Un=function(n,t,e){var r=n.dom.select("img",t)[0];if(r){var o=n.dom.create("a",e);r.parentNode.insertBefore(o,r),o.appendChild(r)}},Fn=function(n){return sn(n,"items")},jn=function(n,t){return P(t,(function(t){return Fn(t)?jn(n,t.items):B(t.value===n,t)}))},$n=function(n,t,e,r){var o=r[t],i=n.length>0;return void 0!==o?jn(o,e).map((function(t){return{url:{value:t.value,meta:{text:i?n:t.text,attach:l}},text:i?n:t.text}})):C.none()},Kn=function(n,t){return"link"===t?n.link:"anchor"===t?n.anchor:C.none()},zn=function(n,t){var e={text:n.text,title:n.title},r=function(n){return B(e.title.length<=0,C.from(n.meta.title).getOr(""))},o=function(n){return B(e.text.length<=0,C.from(n.meta.text).getOr(n.value))},i=function(n){var t=o(n.url),e=r(n.url);return t.isSome()||e.isSome()?C.some(nn(nn({},t.map((function(n){return{text:n}})).getOr({})),e.map((function(n){return{title:n}})).getOr({}))):C.none()},u=function(n,r){var o=Kn(t,r.name).getOr([]);return $n(e.text,r.name,o,n)},a=function(n,t){var r=t.name;return"url"===r?i(n()):w(["anchor","link"],r)?u(n(),t):"text"===r||"title"===r?(e[r]=n()[r],C.none()):C.none()};return{onChange:a}},Hn={init:zn,getDelta:$n},Vn=tinymce.util.Tools.resolve("tinymce.util.Delay"),Wn=tinymce.util.Tools.resolve("tinymce.util.Promise"),qn=function(n,t,e){var r=n.selection.getRng();Vn.setEditorTimeout(n,(function(){n.windowManager.confirm(t,(function(t){n.selection.setRng(r),e(t)}))}))},Qn=function(n){var t=n.href,e=t.indexOf("@")>0&&-1===t.indexOf("/")&&-1===t.indexOf("mailto:");return e?C.some({message:"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",preprocess:function(n){return nn(nn({},n),{href:"mailto:"+t})}}):C.none()},Xn=function(n,t){return function(e){var r=e.href,o=1===n&&!vn(r)||0===n&&/^\s*www(\.|\d\.)/i.test(r);return o?C.some({message:"The URL you entered seems to be an external link. Do you want to add the required "+t+":// prefix?",preprocess:function(n){return nn(nn({},n),{href:t+"://"+r})}}):C.none()}},Jn=function(n,t){return P([Qn,Xn(M(n),V(n))],(function(n){return n(t)})).fold((function(){return Wn.resolve(t)}),(function(e){return new Wn((function(r){qn(n,e.message,(function(n){r(n?e.preprocess(t):t)}))}))}))},Gn={preprocess:Jn},Yn=function(n){var t=n.dom.select("a:not([href])"),e=D(t,(function(n){var t=n.name||n.id;return t?[{text:t,value:"#"+t}]:[]}));return e.length>0?C.some([{text:"None",value:""}].concat(e)):C.none()},Zn={getAnchors:Yn},nt=function(n){var t=$(n);return t.length>0?Z.sanitize(t):C.none()},tt={getClasses:nt},et=tinymce.util.Tools.resolve("tinymce.util.XHR"),rt=function(n){try{return C.some(JSON.parse(n))}catch(t){return C.none()}},ot=function(n){var t=function(t){return n.convertURL(t.value||t.url,"href")},e=_(n);return new Wn((function(n){u(e)?et.send({url:e,success:function(t){return n(rt(t))},error:function(t){return n(C.none())}}):f(e)?e((function(t){return n(C.some(t))})):n(C.from(e))})).then((function(n){return n.bind(Z.sanitizeWith(t)).map((function(n){if(n.length>0){var t=[{text:"None",value:""}];return t.concat(n)}return n}))}))},it={getLinks:ot},ut=function(n,t){var e=j(n);if(e.length>0){var r=R(t,"_blank"),o=!1===z(n),i=function(n){return hn(Z.getValue(n),r)},u=o?Z.sanitizeWith(i):Z.sanitize;return u(e)}return C.none()},at={getRels:ut},ct=[{text:"Current window",value:""},{text:"New window",value:"_blank"}],st=function(n){var t=F(n);return a(t)?Z.sanitize(t).orThunk((function(){return C.some(ct)})):!1===t?C.none():C.some(ct)},ft={getTargets:st},lt=function(n,t,e){var r=n.getAttrib(t,e);return null!==r&&r.length>0?C.some(r):C.none()},dt=function(n,t){var e=n.dom,r=xn(n),o=r?C.some(kn(n.selection,t)):C.none(),i=t?C.some(e.getAttrib(t,"href")):C.none(),u=t?C.from(e.getAttrib(t,"target")):C.none(),a=lt(e,t,"rel"),c=lt(e,t,"class"),s=lt(e,t,"title");return{url:i,text:o,title:s,target:u,rel:a,linkClass:c}},mt=function(n,t){return it.getLinks(n).then((function(e){var r=dt(n,t);return{anchor:r,catalogs:{targets:ft.getTargets(n),rels:at.getRels(n,r.target),classes:tt.getClasses(n),anchor:Zn.getAnchors(n),link:e},optNode:C.from(t),flags:{titleEnabled:K(n)}}}))},gt={collect:mt},vt=function(n,t){return function(e){var r=e.getData();if(!r.url.value)return In(n),void e.close();var o=function(n){return C.from(r[n]).filter((function(e){return!R(t.anchor[n],e)}))},i={href:r.url.value,text:o("text"),target:o("target"),rel:o("rel"),class:o("linkClass"),title:o("title")},u={href:r.url.value,attach:void 0!==r.url.meta&&r.url.meta.attach?r.url.meta.attach:l};Gn.preprocess(n,i).then((function(t){Mn(n,u,t)})),e.close()}},pt=function(n){var t=bn(n);return gt.collect(n,t)},ht=function(n,t){var e=n.anchor,r=e.url.getOr("");return{url:{value:r,meta:{original:{value:r}}},text:e.text.getOr(""),title:e.title.getOr(""),anchor:r,link:r,rel:e.rel.getOr(""),target:e.target.or(t).getOr(""),linkClass:e.linkClass.getOr("")}},yt=function(n,t,e){var r=[{name:"url",type:"urlinput",filetype:"file",label:"URL"}],o=n.anchor.text.map((function(){return{name:"text",type:"input",label:"Text to display"}})).toArray(),i=n.flags.titleEnabled?[{name:"title",type:"input",label:"Title"}]:[],u=C.from(U(e)),a=ht(n,u),c=n.catalogs,s=Hn.init(a,c),f={type:"panel",items:L([r,o,i,E([c.anchor.map(Z.createUi("anchor","Anchors")),c.rels.map(Z.createUi("rel","Rel")),c.targets.map(Z.createUi("target","Open link in...")),c.link.map(Z.createUi("link","Link list")),c.classes.map(Z.createUi("linkClass","Class"))])])};return{title:"Insert/Edit Link",size:"normal",body:f,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:a,onChange:function(n,t){var e=t.name;s.onChange(n.getData,{name:e}).each((function(t){n.setData(t)}))},onSubmit:t}},bt=function(n){var t=pt(n);t.then((function(t){var e=vt(n,t);return yt(t,e,n)})).then((function(t){n.windowManager.open(t)}))},kt=function(n,t){document.body.appendChild(n),n.dispatchEvent(t),document.body.removeChild(n)},Ct=function(n){var t=document.createElement("a");t.target="_blank",t.href=n,t.rel="noreferrer noopener";var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),kt(t,e)},St=function(n,t){return n.dom.getParent(t,"a[href]")},xt=function(n){return St(n,n.selection.getStart())},Ot=function(n){return!0===n.altKey&&!1===n.shiftKey&&!1===n.ctrlKey&&!1===n.metaKey},wt=function(n,t){if(t){var e=pn(t);if(/^#/.test(e)){var r=n.$(e);r.length&&n.selection.scrollIntoView(r[0],!0)}else Ct(t.href)}},Nt=function(n){return function(){bt(n)}},Tt=function(n){return function(){wt(n,xt(n))}},At=function(n){n.on("click",(function(e){var r=St(n,e.target);r&&t.metaKeyPressed(e)&&(e.preventDefault(),wt(n,r))})),n.on("keydown",(function(t){var e=xt(n);e&&13===t.keyCode&&Ot(t)&&(t.preventDefault(),wt(n,e))}))},Lt=function(n,t){return n.on("NodeChange",t),function(){return n.off("NodeChange",t)}},Dt=function(n){return function(t){var e=function(){return t.setActive(!n.mode.isReadOnly()&&null!==bn(n,n.selection.getNode()))};return e(),Lt(n,e)}},Pt=function(n){return function(t){var e=function(){return t.setDisabled(null===bn(n,n.selection.getNode()))};return e(),Lt(n,e)}},Rt=function(n){return function(t){var e=function(t){return Cn(t)||Sn(n.selection.getRng())},r=n.dom.getParents(n.selection.getStart());return t.setDisabled(!e(r)),Lt(n,(function(n){return t.setDisabled(!e(n.parents))}))}},Et=function(n){n.addCommand("mceLink",(function(){H(n)?n.fire("contexttoolbar-show",{toolbarKey:"quicklink"}):Nt(n)()}))},Bt=function(n){n.addShortcut("Meta+K","",(function(){n.execCommand("mceLink")}))},Mt=function(n){n.ui.registry.addToggleButton("link",{icon:"link",tooltip:"Insert/edit link",onAction:Nt(n),onSetup:Dt(n)}),n.ui.registry.addButton("openlink",{icon:"new-tab",tooltip:"Open link",onAction:Tt(n),onSetup:Pt(n)}),n.ui.registry.addButton("unlink",{icon:"unlink",tooltip:"Remove link",onAction:function(){return In(n)},onSetup:Rt(n)})},It=function(n){n.ui.registry.addMenuItem("openlink",{text:"Open link",icon:"new-tab",onAction:Tt(n),onSetup:Pt(n)}),n.ui.registry.addMenuItem("link",{icon:"link",text:"Link...",shortcut:"Meta+K",onAction:Nt(n)}),n.ui.registry.addMenuItem("unlink",{icon:"unlink",text:"Remove link",onAction:function(){return In(n)},onSetup:Rt(n)})},_t=function(n){var t="link unlink openlink",e="link";n.ui.registry.addContextMenu("link",{update:function(r){return Cn(n.dom.getParents(r,"a"))?t:e}})},Ut=function(n){var t=function(n){n.selection.collapse(!1)},e=function(t){var e=n.selection.getNode();return t.setDisabled(!bn(n,e)),l},r=function(t){var e=bn(n),r=xn(n);if(!e&&r){var o=kn(n.selection,e);return C.some(o.length>0?o:t)}return C.none()};n.ui.registry.addContextForm("quicklink",{launch:{type:"contextformtogglebutton",icon:"link",tooltip:"Link",onSetup:Dt(n)},label:"Link",predicate:function(t){return!!bn(n,t)&&I(n)},initValue:function(){var t=bn(n);return t?pn(t):""},commands:[{type:"contextformtogglebutton",icon:"link",tooltip:"Link",primary:!0,onSetup:function(t){var e=n.selection.getNode();return t.setActive(!!bn(n,e)),Dt(n)(t)},onAction:function(e){var o=e.getValue(),i=r(o),u={href:o,attach:l};Mn(n,u,{href:o,text:i,title:C.none(),rel:C.none(),target:C.none(),class:C.none()}),t(n),e.hide()}},{type:"contextformbutton",icon:"unlink",tooltip:"Remove link",onSetup:e,onAction:function(t){In(n),t.hide()}},{type:"contextformbutton",icon:"new-tab",tooltip:"Open link",onSetup:e,onAction:function(t){Tt(n)(),t.hide()}}]})};function Ft(){n.add("link",(function(n){Mt(n),It(n),_t(n),Ut(n),At(n),Et(n),Bt(n)}))}Ft()})()}}]);