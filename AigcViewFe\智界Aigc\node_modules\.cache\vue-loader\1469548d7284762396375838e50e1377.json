{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue?vue&type=template&id=2cb5f0cc&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue", "mtime": 1753848313296}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"credits-page\">\n  <div class=\"page-header\">\n    <h1 class=\"page-title\">账户管理</h1>\n    <p class=\"page-description\">管理您的账户余额、查看交易记录和充值</p>\n  </div>\n\n  <div class=\"credits-content\">\n    <!-- 余额概览 -->\n    <div class=\"balance-overview\">\n      <div class=\"balance-cards\">\n        <StatsCard\n          :value=\"balanceData.currentBalance\"\n          unit=\"元\"\n          label=\"当前余额\"\n          icon=\"anticon anticon-wallet\"\n          icon-color=\"#10b981\"\n          :trend=\"balanceTrend\"\n          :loading=\"loading\"\n          @click=\"handleQuickRecharge\"\n        />\n        \n        <StatsCard\n          :value=\"balanceData.totalRecharge\"\n          unit=\"元\"\n          label=\"累计充值\"\n          icon=\"anticon anticon-plus-circle\"\n          icon-color=\"#7c8aed\"\n          :loading=\"loading\"\n        />\n        \n        <StatsCard\n          :value=\"balanceData.totalConsumption\"\n          unit=\"元\"\n          label=\"累计消费\"\n          icon=\"anticon anticon-minus-circle\"\n          icon-color=\"#ef4444\"\n          :loading=\"loading\"\n        />\n        \n        <StatsCard\n          :value=\"balanceData.monthlyConsumption\"\n          unit=\"元\"\n          label=\"本月消费\"\n          icon=\"anticon anticon-bar-chart\"\n          icon-color=\"#f59e0b\"\n          :trend=\"monthlyTrend\"\n          :loading=\"loading\"\n        />\n      </div>\n\n      <!-- 快速充值 -->\n      <div class=\"quick-recharge\">\n        <h3 class=\"section-title\">快速充值</h3>\n        <div class=\"recharge-options\">\n          <div\n            v-for=\"option in rechargeOptions\"\n            :key=\"option.amount\"\n            class=\"recharge-option\"\n            :class=\"{ selected: selectedAmount === option.amount }\"\n            @click=\"selectRechargeAmount(option.amount)\"\n          >\n            <div class=\"option-amount\">¥{{ option.amount }}</div>\n          </div>\n        </div>\n        \n        <div class=\"custom-amount\">\n          <a-input-number\n            v-model=\"customAmount\"\n            :min=\"0.01\"\n            :max=\"10000\"\n            :step=\"0.01\"\n            placeholder=\"自定义金额（最低0.01元）\"\n            size=\"large\"\n            style=\"flex: 1\"\n            @change=\"onCustomAmountChange\"\n          />\n          <span class=\"currency\">元</span>\n        </div>\n\n        <!-- 充值金额显示和立即充值按钮 -->\n        <div class=\"recharge-action\">\n          <div class=\"recharge-amount-display\">\n            <span class=\"amount-label\">充值金额：</span>\n            <span class=\"amount-value\" :class=\"{ 'no-amount': finalRechargeAmount <= 0 }\">\n              {{ finalRechargeAmount > 0 ? `¥${finalRechargeAmount}` : '请选择充值金额' }}\n            </span>\n          </div>\n          <a-button\n            type=\"primary\"\n            size=\"large\"\n            :loading=\"rechargeLoading\"\n            :disabled=\"finalRechargeAmount <= 0\"\n            @click=\"handleRecharge\"\n          >\n            立即充值\n          </a-button>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 交易记录 -->\n    <DataTable\n      ref=\"transactionTable\"\n      title=\"交易记录\"\n      :data-source=\"transactionList\"\n      :columns=\"transactionColumns\"\n      :loading=\"transactionLoading\"\n      :pagination=\"pagination\"\n      :show-action-column=\"false\"\n      :type-options=\"transactionTypeOptions\"\n      :status-options=\"[]\"\n      :show-search=\"true\"\n      type-filter-placeholder=\"交易类型\"\n      status-filter-placeholder=\"交易状态\"\n      search-placeholder=\"搜索交易描述\"\n      :date-filter-placeholder=\"['交易时间', '交易时间']\"\n      @filter-change=\"handleFilterChange\"\n      @table-change=\"handleTableChange\"\n      @refresh=\"loadTransactionData\"\n    >\n      <!-- 自定义操作按钮 -->\n      <template #actions>\n        <a-button\n          @click=\"handleResetFilters\"\n          style=\"margin-right: 8px; background: linear-gradient(135deg, #64748b 0%, #475569 100%); border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3); color: white;\"\n        >\n          <a-icon type=\"reload\" style=\"margin-right: 6px;\" />\n          重置\n        </a-button>\n        <a-button\n          type=\"primary\"\n          @click=\"handleExportTransactions\"\n          style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\"\n        >\n          <a-icon type=\"download\" style=\"margin-right: 6px;\" />\n          导出交易记录\n        </a-button>\n      </template>\n    </DataTable>\n  </div>\n\n  <!-- 充值确认模态框 -->\n  <a-modal\n    v-model=\"showRechargeModal\"\n    title=\"确认充值\"\n    :footer=\"null\"\n    width=\"500px\"\n  >\n    <div class=\"recharge-confirm\">\n      <div class=\"confirm-info\">\n        <div class=\"info-row\">\n          <span class=\"info-label\">充值金额：</span>\n          <span class=\"info-value\">¥{{ finalRechargeAmount }}</span>\n        </div>\n        <div class=\"info-row total\">\n          <span class=\"info-label\">到账金额：</span>\n          <span class=\"info-value\">¥{{ finalRechargeAmount }}</span>\n        </div>\n      </div>\n\n      <div class=\"payment-methods\">\n        <h4>支付方式</h4>\n        <a-radio-group v-model=\"selectedPaymentMethod\" size=\"large\">\n          <a-radio-button value=\"alipay-page\">\n            <i class=\"anticon anticon-alipay\"></i>\n            支付宝网页\n          </a-radio-button>\n        </a-radio-group>\n      </div>\n\n      <div class=\"modal-actions\">\n        <a-button @click=\"showRechargeModal = false\">取消</a-button>\n        <a-button \n          type=\"primary\" \n          :loading=\"paymentLoading\"\n          @click=\"handleConfirmRecharge\"\n        >\n          确认支付\n        </a-button>\n      </div>\n    </div>\n  </a-modal>\n\n  <!-- 交易详情模态框 -->\n  <a-modal\n    v-model=\"showTransactionDetail\"\n    title=\"交易详情\"\n    :footer=\"null\"\n    width=\"600px\"\n  >\n    <div class=\"transaction-detail\" v-if=\"selectedTransaction\">\n      <div class=\"detail-header\">\n        <div class=\"transaction-type\" :class=\"getTransactionTypeClass(selectedTransaction.transactionType)\">\n          <i :class=\"getTransactionTypeIcon(selectedTransaction.transactionType)\"></i>\n          {{ getTransactionTypeText(selectedTransaction.transactionType) }}\n        </div>\n        <div class=\"transaction-amount\" :class=\"getAmountClass(selectedTransaction.transactionType)\">\n          {{ formatAmount(selectedTransaction.amount, selectedTransaction.transactionType) }}\n        </div>\n      </div>\n\n      <div class=\"detail-content\">\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">交易单号：</span>\n          <span class=\"detail-value\">{{ selectedTransaction.id }}</span>\n        </div>\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">交易时间：</span>\n          <span class=\"detail-value\">{{ formatDateTime(selectedTransaction.transactionTime) }}</span>\n        </div>\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">交易描述：</span>\n          <span class=\"detail-value\">{{ selectedTransaction.description }}</span>\n        </div>\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">交易前余额：</span>\n          <span class=\"detail-value\">¥{{ formatNumber(selectedTransaction.balanceBefore) }}</span>\n        </div>\n        <div class=\"detail-row\">\n          <span class=\"detail-label\">交易后余额：</span>\n          <span class=\"detail-value\">¥{{ formatNumber(selectedTransaction.balanceAfter) }}</span>\n        </div>\n        <div class=\"detail-row\" v-if=\"selectedTransaction.relatedOrderId\">\n          <span class=\"detail-label\">关联订单：</span>\n          <span class=\"detail-value\">{{ selectedTransaction.relatedOrderId }}</span>\n        </div>\n      </div>\n    </div>\n  </a-modal>\n</div>\n", null]}