(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~939c57f2"],{"07d1":function(n,e,t){t("94ce")},"09cf":function(n,e){(function(){"use strict";var n=tinymce.util.Tools.resolve("tinymce.PluginManager");function e(){n.add("textcolor",(function(){}))}e()})()},"34de":function(n,e,t){t("09cf")},3699:function(n,e){(function(){"use strict";var n=tinymce.util.Tools.resolve("tinymce.PluginManager"),e=function(){return e=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t],e)Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n},e.apply(this,arguments)},t=function(n){var e=typeof n;return null===n?"null":"object"===e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"===e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e},r=function(n){return function(e){return t(e)===n}},o=r("string"),u=r("object"),i=r("array"),c=function(n){return null===n||void 0===n},a=function(n){return!c(n)},l=function(){},f=function(n){return function(){return n}},s=function(n){return n},m=f(!1),d=f(!0),g=function(){return p},p=function(){var n=function(n){return n()},e=s,t={fold:function(n,e){return n()},isSome:m,isNone:d,getOr:e,getOrThunk:n,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:f(null),getOrUndefined:f(void 0),or:e,orThunk:n,map:g,each:l,bind:g,exists:m,forall:d,filter:function(){return g()},toArray:function(){return[]},toString:f("none()")};return t}(),v=function(n){var e=f(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},isSome:d,isNone:m,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return v(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:p},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},h=function(n){return null===n||void 0===n?p:v(n)},b={some:v,none:g,from:h},w=Array.prototype.push,y=function(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];e(o,t)}},C=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!i(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);w.apply(e,n[t])}return e},T=function(n){var e=n,t=function(){return e},r=function(n){e=n};return{get:t,set:r}},S=Object.keys,x=Object.hasOwnProperty,R=function(n,e){for(var t=S(n),r=0,o=t.length;r<o;r++){var u=t[r],i=n[u];e(i,u)}},A=function(n,e){return D(n,e)?b.from(n[e]):b.none()},D=function(n,e){return x.call(n,e)},O=function(n){return n.getParam("media_scripts")},E=function(n){return n.getParam("audio_template_callback")},k=function(n){return n.getParam("video_template_callback")},M=function(n){return n.getParam("media_live_embeds",!0)},I=function(n){return n.getParam("media_filter_html",!0)},N=function(n){return n.getParam("media_url_resolver")},B=function(n){return n.getParam("media_alt_source",!0)},P=function(n){return n.getParam("media_poster",!0)},j=function(n){return n.getParam("media_dimensions",!0)},L=tinymce.util.Tools.resolve("tinymce.util.Tools"),_=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),z=tinymce.util.Tools.resolve("tinymce.html.SaxParser"),W=function(n,e){if(n)for(var t=0;t<n.length;t++)if(-1!==e.indexOf(n[t].filter))return n[t]},F=_.DOM,U=function(n){return n.replace(/px$/,"")},H=function(n){var e=n.map.style,t=e?F.parseStyle(e):{};return{type:"ephox-embed-iri",source:n.map["data-ephox-embed-iri"],altsource:"",poster:"",width:A(t,"max-width").map(U).getOr(""),height:A(t,"max-height").map(U).getOr("")}},$=function(n,e){var t=T(!1),r={};return z({validate:!1,allow_conditional_comments:!0,start:function(e,o){if(t.get());else if(D(o.map,"data-ephox-embed-iri"))t.set(!0),r=H(o);else{if(r.source||"param"!==e||(r.source=o.map.movie),"iframe"!==e&&"object"!==e&&"embed"!==e&&"video"!==e&&"audio"!==e||(r.type||(r.type=e),r=L.extend(o.map,r)),"script"===e){var u=W(n,o.map.src);if(!u)return;r={type:"script",source:o.map.src,width:String(u.width),height:String(u.height)}}"source"===e&&(r.source?r.altsource||(r.altsource=o.map.src):r.source=o.map.src),"img"!==e||r.poster||(r.poster=o.map.src)}}}).parse(e),r.source=r.source||r.src||r.data,r.altsource=r.altsource||"",r.poster=r.poster||"",r},V=function(n){var e={mp3:"audio/mpeg",m4a:"audio/x-m4a",wav:"audio/wav",mp4:"video/mp4",webm:"video/webm",ogg:"video/ogg",swf:"application/x-shockwave-flash"},t=n.toLowerCase().split(".").pop(),r=e[t];return r||""},q=tinymce.util.Tools.resolve("tinymce.html.Schema"),K=tinymce.util.Tools.resolve("tinymce.html.Writer"),X=_.DOM,G=function(n){return/^[0-9.]+$/.test(n)?n+"px":n},Y=function(n,e){R(e,(function(e,t){var r=""+e;if(n.map[t]){var o=n.length;while(o--){var u=n[o];u.name===t&&(r?(n.map[t]=r,u.value=r):(delete n.map[t],n.splice(o,1)))}}else r&&(n.push({name:t,value:r}),n.map[t]=r)}))},J=function(n,e){var t=e.map.style,r=t?X.parseStyle(t):{};r["max-width"]=G(n.width),r["max-height"]=G(n.height),Y(e,{style:X.serializeStyle(r)})},Q=["source","altsource"],Z=function(n,e,t){var r,o=K(),u=T(!1),i=0;return z({validate:!1,allow_conditional_comments:!0,comment:function(n){o.comment(n)},cdata:function(n){o.cdata(n)},text:function(n,e){o.text(n,e)},start:function(n,c,a){if(u.get());else if(D(c.map,"data-ephox-embed-iri"))u.set(!0),J(e,c);else{switch(n){case"video":case"object":case"embed":case"img":case"iframe":void 0!==e.height&&void 0!==e.width&&Y(c,{width:e.width,height:e.height});break}if(t)switch(n){case"video":Y(c,{poster:e.poster,src:""}),e.altsource&&Y(c,{src:""});break;case"iframe":Y(c,{src:e.source});break;case"source":if(i<2&&(Y(c,{src:e[Q[i]],type:e[Q[i]+"mime"]}),!e[Q[i]]))return;i++;break;case"img":if(!e.poster)return;r=!0;break}}o.start(n,c,a)},end:function(n){if(!u.get()){if("video"===n&&t)for(var c=0;c<2;c++)if(e[Q[c]]){var a=[];a.map={},i<=c&&(Y(a,{src:e[Q[c]],type:e[Q[c]+"mime"]}),o.start("source",a,!0))}if(e.poster&&"object"===n&&t&&!r){var l=[];l.map={},Y(l,{src:e.poster,width:e.width,height:e.height}),o.start("img",l,!0)}}o.end(n)}},q({})).parse(n),o.getContent()},nn=[{regex:/youtu\.be\/([\w\-_\?&=.]+)/i,type:"iframe",w:560,h:314,url:"www.youtube.com/embed/$1",allowFullscreen:!0},{regex:/youtube\.com(.+)v=([^&]+)(&([a-z0-9&=\-_]+))?/i,type:"iframe",w:560,h:314,url:"www.youtube.com/embed/$2?$4",allowFullscreen:!0},{regex:/youtube.com\/embed\/([a-z0-9\?&=\-_]+)/i,type:"iframe",w:560,h:314,url:"www.youtube.com/embed/$1",allowFullscreen:!0},{regex:/vimeo\.com\/([0-9]+)/,type:"iframe",w:425,h:350,url:"player.vimeo.com/video/$1?title=0&byline=0&portrait=0&color=8dc7dc",allowFullscreen:!0},{regex:/vimeo\.com\/(.*)\/([0-9]+)/,type:"iframe",w:425,h:350,url:"player.vimeo.com/video/$2?title=0&amp;byline=0",allowFullscreen:!0},{regex:/maps\.google\.([a-z]{2,3})\/maps\/(.+)msid=(.+)/,type:"iframe",w:425,h:350,url:'maps.google.com/maps/ms?msid=$2&output=embed"',allowFullscreen:!1},{regex:/dailymotion\.com\/video\/([^_]+)/,type:"iframe",w:480,h:270,url:"www.dailymotion.com/embed/video/$1",allowFullscreen:!0},{regex:/dai\.ly\/([^_]+)/,type:"iframe",w:480,h:270,url:"www.dailymotion.com/embed/video/$1",allowFullscreen:!0}],en=function(n){var e=n.match(/^(https?:\/\/|www\.)(.+)$/i);return e&&e.length>1?"www."===e[1]?"https://":e[1]:"https://"},tn=function(n,e){for(var t=en(e),r=n.regex.exec(e),o=t+n.url,u=function(n){o=o.replace("$"+n,(function(){return r[n]?r[n]:""}))},i=0;i<r.length;i++)u(i);return o.replace(/\?$/,"")},rn=function(n){var e=nn.filter((function(e){return e.regex.test(n)}));return e.length>0?L.extend({},e[0],{url:tn(e[0],n)}):null},on=function(n){var e=n.allowfullscreen?' allowFullscreen="1"':"";return'<iframe src="'+n.source+'" width="'+n.width+'" height="'+n.height+'"'+e+"></iframe>"},un=function(n){var e='<object data="'+n.source+'" width="'+n.width+'" height="'+n.height+'" type="application/x-shockwave-flash">';return n.poster&&(e+='<img src="'+n.poster+'" width="'+n.width+'" height="'+n.height+'" />'),e+="</object>",e},cn=function(n,e){return e?e(n):'<audio controls="controls" src="'+n.source+'">'+(n.altsource?'\n<source src="'+n.altsource+'"'+(n.altsourcemime?' type="'+n.altsourcemime+'"':"")+" />\n":"")+"</audio>"},an=function(n,e){return e?e(n):'<video width="'+n.width+'" height="'+n.height+'"'+(n.poster?' poster="'+n.poster+'"':"")+' controls="controls">\n<source src="'+n.source+'"'+(n.sourcemime?' type="'+n.sourcemime+'"':"")+" />\n"+(n.altsource?'<source src="'+n.altsource+'"'+(n.altsourcemime?' type="'+n.altsourcemime+'"':"")+" />\n":"")+"</video>"},ln=function(n){return'<script src="'+n.source+'"><\/script>'},fn=function(n,e){var t=L.extend({},e);if(!t.source&&(L.extend(t,$(O(n),t.embed)),!t.source))return"";t.altsource||(t.altsource=""),t.poster||(t.poster=""),t.source=n.convertURL(t.source,"source"),t.altsource=n.convertURL(t.altsource,"source"),t.sourcemime=V(t.source),t.altsourcemime=V(t.altsource),t.poster=n.convertURL(t.poster,"poster");var r=rn(t.source);if(r&&(t.source=r.url,t.type=r.type,t.allowfullscreen=r.allowFullscreen,t.width=t.width||String(r.w),t.height=t.height||String(r.h)),t.embed)return Z(t.embed,t,!0);var o=W(O(n),t.source);o&&(t.type="script",t.width=String(o.width),t.height=String(o.height));var u=E(n),i=k(n);return t.width=t.width||"300",t.height=t.height||"150",L.each(t,(function(e,r){t[r]=n.dom.encode(""+e)})),"iframe"===t.type?on(t):"application/x-shockwave-flash"===t.sourcemime?un(t):-1!==t.sourcemime.indexOf("audio")?cn(t,u):"script"===t.type?ln(t):an(t,i)},sn=function(n){return n.hasAttribute("data-mce-object")||n.hasAttribute("data-ephox-embed-iri")},mn=function(n){n.on("click keyup touchend",(function(){var e=n.selection.getNode();e&&n.dom.hasClass(e,"mce-preview-object")&&n.dom.getAttrib(e,"data-mce-selected")&&e.setAttribute("data-mce-selected","2")})),n.on("ObjectSelected",(function(n){var e=n.target.getAttribute("data-mce-object");"script"===e&&n.preventDefault()})),n.on("ObjectResized",(function(n){var e=n.target;if(e.getAttribute("data-mce-object")){var t=e.getAttribute("data-mce-html");t&&(t=unescape(t),e.setAttribute("data-mce-html",escape(Z(t,{width:String(n.width),height:String(n.height)}))))}}))},dn=tinymce.util.Tools.resolve("tinymce.util.Promise"),gn={},pn=function(n,e,t){return new dn((function(r,o){var u=function(t){return t.html&&(gn[n.source]=t),r({url:n.source,html:t.html?t.html:e(n)})};gn[n.source]?u(gn[n.source]):t({url:n.source},u,o)}))},vn=function(n,e){return dn.resolve({html:e(n),url:n.source})},hn=function(n){return function(e){return fn(n,e)}},bn=function(n,e){var t=N(n);return t?pn(e,hn(n),t):vn(e,hn(n))},wn=function(n){return D(gn,n)},yn=function(n,e){return A(e,n).bind((function(n){return A(n,"meta")}))},Cn=function(n,e,t){return function(r){var o,i=function(){return A(n,r)},c=function(){return A(e,r)},a=function(n){return A(n,"value").bind((function(n){return n.length>0?b.some(n):b.none()}))},l=function(){return i().bind((function(n){return u(n)?a(n).orThunk(c):c().orThunk((function(){return b.from(n)}))}))},f=function(){return c().orThunk((function(){return i().bind((function(n){return u(n)?a(n):b.from(n)}))}))};return o={},o[r]=(r===t?l():f()).getOr(""),o}},Tn=function(n,e){var t={};return A(n,"dimensions").each((function(n){y(["width","height"],(function(r){A(e,r).orThunk((function(){return A(n,r)})).each((function(n){return t[r]=n}))}))})),t},Sn=function(n,t){var r=t?yn(t,n).getOr({}):{},o=Cn(n,r,t);return e(e(e(e(e({},o("source")),o("altsource")),o("poster")),o("embed")),Tn(n,r))},xn=function(n){var t=e(e({},n),{source:{value:A(n,"source").getOr("")},altsource:{value:A(n,"altsource").getOr("")},poster:{value:A(n,"poster").getOr("")}});return y(["width","height"],(function(e){A(n,e).each((function(n){var r=t.dimensions||{};r[e]=n,t.dimensions=r}))})),t},Rn=function(n){return function(e){var t=e&&e.msg?"Media embed handler error: "+e.msg:"Media embed handler threw unknown error.";n.notificationManager.open({type:"error",text:t})}},An=function(n,e){return $(O(n),e)},Dn=function(n){var t=n.selection.getNode(),r=sn(t)?n.serializer.serialize(t,{selection:!0}):"";return e({embed:r},$(O(n),r))},On=function(n,t){return function(r){if(o(r.url)&&r.url.trim().length>0){var u=r.html,i=An(t,u),c=e(e({},i),{source:r.url,embed:u});n.setData(xn(c))}}},En=function(n,e){for(var t=n.dom.select("*[data-mce-object]"),r=0;r<e.length;r++)for(var o=t.length-1;o>=0;o--)e[r]===t[o]&&t.splice(o,1);n.selection.select(t[0])},kn=function(n,e){var t=n.dom.select("*[data-mce-object]");n.insertContent(e),En(n,t),n.nodeChanged()},Mn=function(n,e,t){e.embed=Z(e.embed,e),e.embed&&(n.source===e.source||wn(e.source))?kn(t,e.embed):bn(t,e).then((function(n){kn(t,n.html)})).catch(Rn(t))},In=function(n){var t=Dn(n),r=T(t),o=xn(t),u=function(e,t){var r=Sn(t.getData(),"source");e.source!==r.source&&(On(h,n)({url:r.source,html:""}),bn(n,r).then(On(h,n)).catch(Rn(n)))},i=function(e){var t=Sn(e.getData()),r=An(n,t.embed);e.setData(xn(r))},c=function(t,r){var o=Sn(t.getData(),r),u=fn(n,o);t.setData(xn(e(e({},o),{embed:u})))},a=[{name:"source",type:"urlinput",filetype:"media",label:"Source"}],l=j(n)?[{type:"sizeinput",name:"dimensions",label:"Constrain proportions",constrain:!0}]:[],f={title:"General",name:"general",items:C([a,l])},s={type:"textarea",name:"embed",label:"Paste your embed code below:"},m={title:"Embed",items:[s]},d=[];B(n)&&d.push({name:"altsource",type:"urlinput",filetype:"media",label:"Alternative source URL"}),P(n)&&d.push({name:"poster",type:"urlinput",filetype:"image",label:"Media poster (Image URL)"});var g={title:"Advanced",name:"advanced",items:d},p=[f,m];d.length>0&&p.push(g);var v={type:"tabpanel",tabs:p},h=n.windowManager.open({title:"Insert/Edit Media",size:"normal",body:v,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:function(e){var t=Sn(e.getData());Mn(r.get(),t,n),e.close()},onChange:function(n,e){switch(e.name){case"source":u(r.get(),n);break;case"embed":i(n);break;case"dimensions":case"altsource":case"poster":c(n,e.name);break}r.set(Sn(n.getData()))},initialData:o})},Nn=function(n){var e=function(){In(n)};return{showDialog:e}},Bn=function(n){var e=function(){In(n)};n.addCommand("mceMedia",e)},Pn=tinymce.util.Tools.resolve("tinymce.html.Node"),jn=tinymce.util.Tools.resolve("tinymce.Env"),Ln=tinymce.util.Tools.resolve("tinymce.html.DomParser"),_n=function(n,e){if(!1===I(n))return e;var t,r=K();return z({validate:!1,allow_conditional_comments:!1,comment:function(n){t||r.comment(n)},cdata:function(n){t||r.cdata(n)},text:function(n,e){t||r.text(n,e)},start:function(e,o,u){if(t=!0,"script"!==e&&"noscript"!==e&&"svg"!==e){for(var i=o.length-1;i>=0;i--){var c=o[i].name;0===c.indexOf("on")&&(delete o.map[c],o.splice(i,1)),"style"===c&&(o[i].value=n.dom.serializeStyle(n.dom.parseStyle(o[i].value),e))}r.start(e,o,u),t=!1}},end:function(n){t||r.end(n)}},q({})).parse(e),r.getContent()},zn=function(n){var e=n.name;return"iframe"===e||"video"===e||"audio"===e},Wn=function(n,e,t,r){void 0===r&&(r=null);var o=n.attr(t);return a(o)?o:D(e,t)?null:r},Fn=function(n,e,t){var r="img"===e.name||"video"===n.name,o=r?"300":null,u="audio"===n.name?"30":"150",i=r?u:null;e.attr({width:Wn(n,t,"width",o),height:Wn(n,t,"height",i)})},Un=function(n,e,t,r){var o=Ln({forced_root_block:!1,validate:!1},n.schema).parse(r,{context:e});while(o.firstChild)t.append(o.firstChild)},Hn=function(n,e){var t=e.name,r=new Pn("img",1);return r.shortEnded=!0,Vn(n,e,r),Fn(e,r,{}),r.attr({style:e.attr("style"),src:jn.transparentSrc,"data-mce-object":t,class:"mce-object mce-object-"+t}),r},$n=function(n,e){var t=e.name,r=new Pn("span",1);r.attr({contentEditable:"false",style:e.attr("style"),"data-mce-object":t,class:"mce-preview-object mce-object-"+t}),Vn(n,e,r);var o=n.dom.parseStyle(e.attr("style")),u=new Pn(t,1);if(Fn(e,u,o),u.attr({src:e.attr("src"),style:e.attr("style"),class:e.attr("class")}),"iframe"===t)u.attr({allowfullscreen:e.attr("allowfullscreen"),frameborder:"0"});else{var i=["controls","crossorigin","currentTime","loop","muted","poster","preload"];y(i,(function(n){u.attr(n,e.attr(n))}));var c=r.attr("data-mce-html");a(c)&&Un(n,t,u,unescape(c))}var l=new Pn("span",1);return l.attr("class","mce-shim"),r.append(u),r.append(l),r},Vn=function(n,e,t){var r=e.attributes,o=r.length;while(o--){var u=r[o].name,i=r[o].value;"width"!==u&&"height"!==u&&"style"!==u&&("data"!==u&&"src"!==u||(i=n.convertURL(i,u)),t.attr("data-mce-p-"+u,i))}var c=e.firstChild&&e.firstChild.value;c&&(t.attr("data-mce-html",escape(_n(n,c))),t.firstChild=null)},qn=function(n){var e=n.attr("class");return e&&/\btiny-pageembed\b/.test(e)},Kn=function(n){while(n=n.parent)if(n.attr("data-ephox-embed-iri")||qn(n))return!0;return!1},Xn=function(n){return function(e){var t,r,o=e.length;while(o--)t=e[o],t.parent&&(t.parent.attr("data-mce-object")||("script"!==t.name||(r=W(O(n),t.attr("src")),r))&&(r&&(r.width&&t.attr("width",r.width.toString()),r.height&&t.attr("height",r.height.toString())),zn(t)&&M(n)&&jn.ceFalse?Kn(t)||t.replace($n(n,t)):Kn(t)||t.replace(Hn(n,t))))}},Gn=function(n){n.on("preInit",(function(){var e=n.schema.getSpecialElements();L.each("video audio iframe object".split(" "),(function(n){e[n]=new RegExp("</"+n+"[^>]*>","gi")}));var t=n.schema.getBoolAttrs();L.each("webkitallowfullscreen mozallowfullscreen allowfullscreen".split(" "),(function(n){t[n]={}})),n.parser.addNodeFilter("iframe,video,audio,object,embed,script",Xn(n)),n.serializer.addAttributeFilter("data-mce-object",(function(e,t){var r,o,u,i,c,a,l,f,s=e.length;while(s--)if(r=e[s],r.parent){l=r.attr(t),o=new Pn(l,1),"audio"!==l&&"script"!==l&&(f=r.attr("class"),f&&-1!==f.indexOf("mce-preview-object")?o.attr({width:r.firstChild.attr("width"),height:r.firstChild.attr("height")}):o.attr({width:r.attr("width"),height:r.attr("height")})),o.attr({style:r.attr("style")}),i=r.attributes,u=i.length;while(u--){var m=i[u].name;0===m.indexOf("data-mce-p-")&&o.attr(m.substr(11),i[u].value)}"script"===l&&o.attr("type","text/javascript"),c=r.attr("data-mce-html"),c&&(a=new Pn("#text",3),a.raw=!0,a.value=_n(n,unescape(c)),o.append(a)),r.replace(o)}}))})),n.on("SetContent",(function(){n.$("span.mce-preview-object").each((function(e,t){var r=n.$(t);0===r.find("span.mce-shim").length&&r.append('<span class="mce-shim"></span>')}))}))},Yn=function(n){n.on("ResolveName",(function(n){var e;1===n.target.nodeType&&(e=n.target.getAttribute("data-mce-object"))&&(n.name=e)}))},Jn=function(n){var e=function(){return n.execCommand("mceMedia")};n.ui.registry.addToggleButton("media",{tooltip:"Insert/edit media",icon:"embed",onAction:e,onSetup:function(e){var t=n.selection;return e.setActive(sn(t.getNode())),t.selectorChangedWithUnbind("img[data-mce-object],span[data-mce-object],div[data-ephox-embed-iri]",e.setActive).unbind}}),n.ui.registry.addMenuItem("media",{icon:"embed",text:"Media...",onAction:e})};function Qn(){n.add("media",(function(n){return Bn(n),Jn(n),Yn(n),Gn(n),mn(n),Nn(n)}))}Qn()})()},"3aea":function(n,e,t){t("3699")},9434:function(n,e,t){t("e8b0")},"94ce":function(n,e){(function(){"use strict";var n=function(n){var e=typeof n;return null===n?"null":"object"===e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"===e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e},e=function(e){return function(t){return n(t)===e}},t=function(n){return function(e){return typeof e===n}},r=function(n){return function(e){return n===e}},o=e("string"),u=e("object"),i=e("array"),c=r(null),a=t("boolean"),l=r(void 0),f=function(n){return null===n||void 0===n},s=function(n){return!f(n)},m=t("function"),d=t("number"),g=function(){},p=function(n,e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return n(e.apply(null,t))}},v=function(n,e){return function(t){return n(e(t))}},h=function(n){return function(){return n}},b=function(n){return n},w=function(n,e){return n===e};function y(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var o=e.concat(t);return n.apply(null,o)}}var C=function(n){return function(e){return!n(e)}},T=function(n){return function(){throw new Error(n)}},S=h(!1),x=h(!0),R=function(){return A},A=function(){var n=function(n){return n()},e=b,t={fold:function(n,e){return n()},isSome:S,isNone:x,getOr:e,getOrThunk:n,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:h(null),getOrUndefined:h(void 0),or:e,orThunk:n,map:R,each:g,bind:R,exists:S,forall:x,filter:function(){return R()},toArray:function(){return[]},toString:h("none()")};return t}(),D=function(n){var e=h(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},isSome:x,isNone:S,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return D(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:A},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},O=function(n){return null===n||void 0===n?A:D(n)},E={some:D,none:R,from:O},k=Array.prototype.slice,M=Array.prototype.indexOf,I=Array.prototype.push,N=function(n,e){return M.call(n,e)},B=function(n,e){return N(n,e)>-1},P=function(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t))return!0}return!1},j=function(n,e){for(var t=[],r=0;r<n;r++)t.push(e(r));return t},L=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o)}return r},_=function(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];e(o,t)}},z=function(n,e){for(var t=n.length-1;t>=0;t--){var r=n[t];e(r,t)}},W=function(n,e){for(var t=[],r=[],o=0,u=n.length;o<u;o++){var i=n[o],c=e(i,o)?t:r;c.push(i)}return{pass:t,fail:r}},F=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var u=n[r];e(u,r)&&t.push(u)}return t},U=function(n,e,t){return z(n,(function(n,r){t=e(t,n,r)})),t},H=function(n,e,t){return _(n,(function(n,r){t=e(t,n,r)})),t},$=function(n,e,t){for(var r=0,o=n.length;r<o;r++){var u=n[r];if(e(u,r))return E.some(u);if(t(u,r))break}return E.none()},V=function(n,e){return $(n,e,S)},q=function(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t))return E.some(t)}return E.none()},K=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!i(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);I.apply(e,n[t])}return e},X=function(n,e){return K(L(n,e))},G=function(n,e){for(var t=0,r=n.length;t<r;++t){var o=n[t];if(!0!==e(o,t))return!1}return!0},Y=function(n){var e=k.call(n,0);return e.reverse(),e},J=function(n,e){for(var t={},r=0,o=n.length;r<o;r++){var u=n[r];t[String(u)]=e(u,r)}return t},Q=function(n){return[n]},Z=function(n,e){var t=k.call(n,0);return t.sort(e),t},nn=function(n,e){return e>=0&&e<n.length?E.some(n[e]):E.none()},en=function(n){return nn(n,0)},tn=function(n){return nn(n,n.length-1)},rn=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return E.none()},on=function(){return on=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t],e)Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n},on.apply(this,arguments)};function un(n,e,t){if(t||2===arguments.length)for(var r,o=0,u=e.length;o<u;o++)!r&&o in e||(r||(r=Array.prototype.slice.call(e,0,o)),r[o]=e[o]);return n.concat(r||Array.prototype.slice.call(e))}var cn=function(n){var e,t=!1;return function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return t||(t=!0,e=n.apply(null,r)),e}},an=function(n,e,t,r){var o=n.isiOS()&&!0===/ipad/i.test(t),u=n.isiOS()&&!o,i=n.isiOS()||n.isAndroid(),c=i||r("(pointer:coarse)"),a=o||!u&&i&&r("(min-device-width:768px)"),l=u||i&&!a,f=e.isSafari()&&n.isiOS()&&!1===/safari/i.test(t),s=!l&&!a&&!f;return{isiPad:h(o),isiPhone:h(u),isTablet:h(a),isPhone:h(l),isTouch:h(c),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:h(f),isDesktop:h(s)}},ln=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}},fn=function(n,e){var t=ln(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return dn(r(1),r(2))},sn=function(n,e){var t=String(e).toLowerCase();return 0===n.length?mn():fn(n,t)},mn=function(){return dn(0,0)},dn=function(n,e){return{major:n,minor:e}},gn={nu:dn,detect:sn,unknown:mn},pn=function(n,e){return rn(e.brands,(function(e){var t=e.brand.toLowerCase();return V(n,(function(n){var e;return t===(null===(e=n.brand)||void 0===e?void 0:e.toLowerCase())})).map((function(n){return{current:n.name,version:gn.nu(parseInt(e.version,10),0)}}))}))},vn=function(n,e){var t=String(e).toLowerCase();return V(n,(function(n){return n.search(t)}))},hn=function(n,e){return vn(n,e).map((function(n){var t=gn.detect(n.versionRegexes,e);return{current:n.name,version:t}}))},bn=function(n,e){return vn(n,e).map((function(n){var t=gn.detect(n.versionRegexes,e);return{current:n.name,version:t}}))},wn=function(n,e){return n.substring(e)},yn=function(n,e,t){return""===e||n.length>=e.length&&n.substr(t,t+e.length)===e},Cn=function(n,e){return Sn(n,e)?wn(n,e.length):n},Tn=function(n,e){return-1!==n.indexOf(e)},Sn=function(n,e){return yn(n,e,0)},xn=function(n,e){return yn(n,e,n.length-e.length)},Rn=function(n){return function(e){return e.replace(n,"")}},An=Rn(/^\s+|\s+$/g),Dn=function(n){return n.length>0},On=function(n){return!Dn(n)},En=function(n){var e=parseFloat(n);return isNaN(e)?E.none():E.some(e)},kn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Mn=function(n){return function(e){return Tn(e,n)}},In=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Tn(n,"edge/")&&Tn(n,"chrome")&&Tn(n,"safari")&&Tn(n,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,kn],search:function(n){return Tn(n,"chrome")&&!Tn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Tn(n,"msie")||Tn(n,"trident")}},{name:"Opera",versionRegexes:[kn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Mn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Mn("firefox")},{name:"Safari",versionRegexes:[kn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Tn(n,"safari")||Tn(n,"mobile/"))&&Tn(n,"applewebkit")}}],Nn=[{name:"Windows",search:Mn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Tn(n,"iphone")||Tn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Mn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Mn("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Mn("linux"),versionRegexes:[]},{name:"Solaris",search:Mn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Mn("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Mn("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Bn={browsers:h(In),oses:h(Nn)},Pn="Edge",jn="Chrome",Ln="IE",_n="Opera",zn="Firefox",Wn="Safari",Fn=function(){return Un({current:void 0,version:gn.unknown()})},Un=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:r(Pn),isChrome:r(jn),isIE:r(Ln),isOpera:r(_n),isFirefox:r(zn),isSafari:r(Wn)}},Hn={unknown:Fn,nu:Un,edge:h(Pn),chrome:h(jn),ie:h(Ln),opera:h(_n),firefox:h(zn),safari:h(Wn)},$n="Windows",Vn="iOS",qn="Android",Kn="Linux",Xn="OSX",Gn="Solaris",Yn="FreeBSD",Jn="ChromeOS",Qn=function(){return Zn({current:void 0,version:gn.unknown()})},Zn=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:r($n),isiOS:r(Vn),isAndroid:r(qn),isOSX:r(Xn),isLinux:r(Kn),isSolaris:r(Gn),isFreeBSD:r(Yn),isChromeOS:r(Jn)}},ne={unknown:Qn,nu:Zn,windows:h($n),ios:h(Vn),android:h(qn),linux:h(Kn),osx:h(Xn),solaris:h(Gn),freebsd:h(Yn),chromeos:h(Jn)},ee=function(n,e,t){var r=Bn.browsers(),o=Bn.oses(),u=e.bind((function(n){return pn(r,n)})).orThunk((function(){return hn(r,n)})).fold(Hn.unknown,Hn.nu),i=bn(o,n).fold(ne.unknown,ne.nu),c=an(i,u,n,t);return{browser:u,os:i,deviceType:c}},te={detect:ee},re=function(n){return window.matchMedia(n).matches},oe=cn((function(){return te.detect(navigator.userAgent,E.from(navigator.userAgentData),re)})),ue=function(){return oe()},ie=function(n,e,t){return 0!==(n.compareDocumentPosition(e)&t)},ce=function(n,e){return ie(n,e,Node.DOCUMENT_POSITION_CONTAINED_BY)},ae=8,le=9,fe=11,se=1,me=3,de=function(n,e){var t=e||document,r=t.createElement("div");if(r.innerHTML=n,!r.hasChildNodes()||r.childNodes.length>1)throw new Error("HTML must have a single root node");return ve(r.childNodes[0])},ge=function(n,e){var t=e||document,r=t.createElement(n);return ve(r)},pe=function(n,e){var t=e||document,r=t.createTextNode(n);return ve(r)},ve=function(n){if(null===n||void 0===n)throw new Error("Node cannot be null or undefined");return{dom:n}},he=function(n,e,t){return E.from(n.dom.elementFromPoint(e,t)).map(ve)},be={fromHtml:de,fromTag:ge,fromText:pe,fromDom:ve,fromPoint:he},we=function(n,e){var t=n.dom;if(t.nodeType!==se)return!1;var r=t;if(void 0!==r.matches)return r.matches(e);if(void 0!==r.msMatchesSelector)return r.msMatchesSelector(e);if(void 0!==r.webkitMatchesSelector)return r.webkitMatchesSelector(e);if(void 0!==r.mozMatchesSelector)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},ye=function(n){return n.nodeType!==se&&n.nodeType!==le&&n.nodeType!==fe||0===n.childElementCount},Ce=function(n,e){var t=void 0===e?document:e.dom;return ye(t)?[]:L(t.querySelectorAll(n),be.fromDom)},Te=function(n,e){var t=void 0===e?document:e.dom;return ye(t)?E.none():E.from(t.querySelector(n)).map(be.fromDom)},Se=function(n,e){return n.dom===e.dom},xe=function(n,e){var t=n.dom,r=e.dom;return t!==r&&t.contains(r)},Re=function(n,e){return ce(n.dom,e.dom)},Ae=function(n,e){return ue().browser.isIE()?Re(n,e):xe(n,e)},De=we,Oe=Object.keys,Ee=Object.hasOwnProperty,ke=function(n,e){for(var t=Oe(n),r=0,o=t.length;r<o;r++){var u=t[r],i=n[u];e(i,u)}},Me=function(n,e){return Ie(n,(function(n,t){return{k:t,v:e(n,t)}}))},Ie=function(n,e){var t={};return ke(n,(function(n,r){var o=e(n,r);t[o.k]=o.v})),t},Ne=function(n){return function(e,t){n[t]=e}},Be=function(n,e,t,r){var o={};return ke(n,(function(n,o){(e(n,o)?t:r)(n,o)})),o},Pe=function(n,e){var t={};return Be(n,e,Ne(t),g),t},je=function(n,e){var t=[];return ke(n,(function(n,r){t.push(e(n,r))})),t},Le=function(n){return je(n,b)},_e=function(n){return Oe(n).length},ze=function(n,e){return We(n,e)?E.from(n[e]):E.none()},We=function(n,e){return Ee.call(n,e)},Fe=function(n,e){return We(n,e)&&void 0!==n[e]&&null!==n[e]},Ue=function(n){for(var e in n)if(Ee.call(n,e))return!1;return!0},He=["tfoot","thead","tbody","colgroup"],$e=function(n){return B(He,n)},Ve=function(n,e){return{rows:n,columns:e}},qe=function(n,e){return{row:n,column:e}},Ke=function(n,e,t){return{element:n,rowspan:e,colspan:t}},Xe=function(n,e,t,r){return{element:n,rowspan:e,colspan:t,isNew:r}},Ge=function(n,e,t,r,o,u){return{element:n,rowspan:e,colspan:t,row:r,column:o,isLocked:u}},Ye=function(n,e,t){return{element:n,cells:e,section:t}},Je=function(n,e,t,r){return{element:n,cells:e,section:t,isNew:r}},Qe=function(n,e,t){return{element:n,isNew:e,isLocked:t}},Ze=function(n,e,t,r){return{element:n,cells:e,section:t,isNew:r}},nt=function(n,e,t,r){return{startRow:n,startCol:e,finishRow:t,finishCol:r}},et=function(n,e,t){return{element:n,colspan:e,column:t}},tt=function(n,e){return{element:n,columns:e}};"undefined"!==typeof window?window:Function("return this;")();var rt=function(n){var e=n.dom.nodeName;return e.toLowerCase()},ot=function(n){return n.dom.nodeType},ut=function(n){return function(e){return ot(e)===n}},it=function(n){return ot(n)===ae||"#comment"===rt(n)},ct=ut(se),at=ut(me),lt=ut(le),ft=ut(fe),st=function(n){return function(e){return ct(e)&&rt(e)===n}},mt=function(n){return be.fromDom(n.dom.ownerDocument)},dt=function(n){return lt(n)?n:mt(n)},gt=function(n){return be.fromDom(dt(n).dom.defaultView)},pt=function(n){return E.from(n.dom.parentNode).map(be.fromDom)},vt=function(n){return E.from(n.dom.parentElement).map(be.fromDom)},ht=function(n,e){var t=m(e)?e:S,r=n.dom,o=[];while(null!==r.parentNode&&void 0!==r.parentNode){var u=r.parentNode,i=be.fromDom(u);if(o.push(i),!0===t(i))break;r=u}return o},bt=function(n){return E.from(n.dom.previousSibling).map(be.fromDom)},wt=function(n){return E.from(n.dom.nextSibling).map(be.fromDom)},yt=function(n){return L(n.dom.childNodes,be.fromDom)},Ct=function(n,e){var t=n.dom.childNodes;return E.from(t[e]).map(be.fromDom)},Tt=function(n){return Ct(n,0)},St=function(n){return ft(n)&&s(n.dom.host)},xt=m(Element.prototype.attachShadow)&&m(Node.prototype.getRootNode),Rt=h(xt),At=xt?function(n){return be.fromDom(n.dom.getRootNode())}:dt,Dt=function(n){var e=At(n);return St(e)?E.some(e):E.none()},Ot=function(n){return be.fromDom(n.dom.host)},Et=function(n){if(Rt()&&s(n.target)){var e=be.fromDom(n.target);if(ct(e)&&kt(e)&&n.composed&&n.composedPath){var t=n.composedPath();if(t)return en(t)}}return E.from(n.target)},kt=function(n){return s(n.dom.shadowRoot)},Mt=function(n){var e=at(n)?n.dom.parentNode:n.dom;if(void 0===e||null===e||null===e.ownerDocument)return!1;var t=e.ownerDocument;return Dt(be.fromDom(e)).fold((function(){return t.body.contains(e)}),v(Mt,Ot))},It=function(){return Nt(be.fromDom(document))},Nt=function(n){var e=n.dom.body;if(null===e||void 0===e)throw new Error("Body is not available yet");return be.fromDom(e)},Bt=function(n,e,t){return F(ht(n,t),e)},Pt=function(n,e){return F(yt(n),e)},jt=function(n,e){var t=[];return _(yt(n),(function(n){e(n)&&(t=t.concat([n])),t=t.concat(jt(n,e))})),t},Lt=function(n,e,t){return Bt(n,(function(n){return we(n,e)}),t)},_t=function(n,e){return Pt(n,(function(n){return we(n,e)}))},zt=function(n,e){return Ce(e,n)};function Wt(n,e,t,r,o){return n(t,r)?E.some(t):m(o)&&o(t)?E.none():e(t,r,o)}var Ft=function(n,e,t){var r=n.dom,o=m(t)?t:S;while(r.parentNode){r=r.parentNode;var u=be.fromDom(r);if(e(u))return E.some(u);if(o(u))break}return E.none()},Ut=function(n,e,t){var r=function(n,e){return e(n)};return Wt(r,Ft,n,e,t)},Ht=function(n,e){var t=function(n){return e(be.fromDom(n))},r=V(n.dom.childNodes,t);return r.map(be.fromDom)},$t=function(n,e){var t=function(n){for(var r=0;r<n.childNodes.length;r++){var o=be.fromDom(n.childNodes[r]);if(e(o))return E.some(o);var u=t(n.childNodes[r]);if(u.isSome())return u}return E.none()};return t(n.dom)},Vt=function(n,e,t){return Ft(n,(function(n){return we(n,e)}),t)},qt=function(n,e){return Ht(n,(function(n){return we(n,e)}))},Kt=function(n,e){return Te(e,n)},Xt=function(n,e,t){var r=function(n,e){return we(n,e)};return Wt(r,Vt,n,e,t)},Gt=function(n,e,t){if(!(o(t)||a(t)||d(t)))throw new Error("Attribute value was not simple");n.setAttribute(e,t+"")},Yt=function(n,e,t){Gt(n.dom,e,t)},Jt=function(n,e){var t=n.dom;ke(e,(function(n,e){Gt(t,e,n)}))},Qt=function(n,e){ke(e,(function(e,t){e.fold((function(){er(n,t)}),(function(e){Gt(n.dom,t,e)}))}))},Zt=function(n,e){var t=n.dom.getAttribute(e);return null===t?void 0:t},nr=function(n,e){return E.from(Zt(n,e))},er=function(n,e){n.dom.removeAttribute(e)},tr=function(n){return H(n.dom.attributes,(function(n,e){return n[e.name]=e.value,n}),{})},rr=function(n,e,t){return void 0===t&&(t=w),n.exists((function(n){return t(n,e)}))},or=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},ur=function(n,e,t){return n.isSome()&&e.isSome()?E.some(t(n.getOrDie(),e.getOrDie())):E.none()},ir=function(n,e){return void 0!==n&&null!==n?e(n):E.none()},cr=function(n){return n.bind(b)},ar=function(n,e){return n?E.some(e):E.none()},lr=function(n){return void 0!==n.style&&m(n.style.getPropertyValue)},fr=function(n,e,t){if(!o(t))throw new Error("CSS value must be a string: "+t);lr(n)&&n.style.setProperty(e,t)},sr=function(n,e){lr(n)&&n.style.removeProperty(e)},mr=function(n,e,t){var r=n.dom;fr(r,e,t)},dr=function(n,e){var t=n.dom;ke(e,(function(n,e){fr(t,e,n)}))},gr=function(n,e){var t=n.dom,r=window.getComputedStyle(t),o=r.getPropertyValue(e);return""!==o||Mt(n)?o:pr(t,e)},pr=function(n,e){return lr(n)?n.style.getPropertyValue(e):""},vr=function(n,e){var t=n.dom,r=pr(t,e);return E.from(r).filter((function(n){return n.length>0}))},hr=function(n,e){var t=n.dom;sr(t,e),rr(nr(n,"style").map(An),"")&&er(n,"style")},br=function(n,e){var t=n.dom,r=e.dom;lr(t)&&lr(r)&&(r.style.cssText=t.style.cssText)},wr=function(n,e,t){return void 0===t&&(t=0),nr(n,e).map((function(n){return parseInt(n,10)})).getOr(t)},yr=function(n,e){return wr(n,e,1)},Cr=function(n){return st("col")(n)?wr(n,"span",1)>1:yr(n,"colspan")>1},Tr=function(n){return yr(n,"rowspan")>1},Sr=function(n,e){return parseInt(gr(n,e),10)},xr=h(10),Rr=h(10),Ar=function(n,e){return Dr(n,e,x)},Dr=function(n,e,t){return X(yt(n),(function(n){return we(n,e)?t(n)?[n]:[]:Dr(n,e,t)}))},Or=function(n,e,t){if(void 0===t&&(t=S),t(e))return E.none();if(B(n,rt(e)))return E.some(e);var r=function(n){return we(n,"table")||t(n)};return Vt(e,n.join(","),r)},Er=function(n,e){return Or(["td","th"],n,e)},kr=function(n){return Ar(n,"th,td")},Mr=function(n){return we(n,"colgroup")?_t(n,"col"):X(Br(n),(function(n){return _t(n,"col")}))},Ir=function(n,e){return Xt(n,"table",e)},Nr=function(n){return Ar(n,"tr")},Br=function(n){return Ir(n).fold(h([]),(function(n){return _t(n,"colgroup")}))},Pr=function(n,e){return L(n,(function(n){if("colgroup"===rt(n)){var t=L(Mr(n),(function(n){var e=wr(n,"span",1);return Ke(n,1,e)}));return Ye(n,t,"colgroup")}t=L(kr(n),(function(n){var e=wr(n,"rowspan",1),t=wr(n,"colspan",1);return Ke(n,e,t)}));return Ye(n,t,e(n))}))},jr=function(n){return pt(n).map((function(n){var e=rt(n);return $e(e)?e:"tbody"})).getOr("tbody")},Lr=function(n){var e=Nr(n),t=Br(n),r=un(un([],t,!0),e,!0);return Pr(r,jr)},_r=function(n,e){return Pr(n,(function(){return e}))},zr=function(n,e,t){var r=n.cells,o=r.slice(0,e),u=r.slice(e),i=o.concat(t).concat(u);return Ur(n,i)},Wr=function(n,e,t){return zr(n,e,[t])},Fr=function(n,e,t){var r=n.cells;r[e]=t},Ur=function(n,e){return Ze(n.element,e,n.section,n.isNew)},Hr=function(n,e){var t=n.cells,r=L(t,e);return Ze(n.element,r,n.section,n.isNew)},$r=function(n,e){return n.cells[e]},Vr=function(n,e){return $r(n,e).element},qr=function(n){return n.cells.length},Kr=function(n){var e=W(n,(function(n){return"colgroup"===n.section}));return{rows:e.fail,cols:e.pass}},Xr=function(n,e,t){var r=L(n.cells,t);return Ze(e(n.element),r,n.section,!0)},Gr="data-snooker-locked-cols",Yr=function(n){return nr(n,Gr).bind((function(n){return E.from(n.match(/\d+/g))})).map((function(n){return J(n,x)}))},Jr=function(n){var e=H(Kr(n).rows,(function(n,e){return _(e.cells,(function(e,t){e.isLocked&&(n[t]=!0)})),n}),{}),t=je(e,(function(n,e){return parseInt(e,10)}));return Z(t)},Qr=function(n,e){return n+","+e},Zr=function(n,e,t){return E.from(n.access[Qr(e,t)])},no=function(n,e,t){var r=eo(n,(function(n){return t(e,n.element)}));return r.length>0?E.some(r[0]):E.none()},eo=function(n,e){var t=X(n.all,(function(n){return n.cells}));return F(t,e)},to=function(n){var e={},t=0;return _(n.cells,(function(n){var r=n.colspan;j(r,(function(o){var u=t+o;e[u]=et(n.element,r,u)})),t+=r})),e},ro=function(n){var e={},t=[],r=en(n).map((function(n){return n.element})).bind(Ir),o=r.bind(Yr).getOr({}),u=0,i=0,c=0,a=W(n,(function(n){return"colgroup"===n.section})),l=a.pass,f=a.fail;_(f,(function(n){var r=[];_(n.cells,(function(n){var t=0;while(void 0!==e[Qr(c,t)])t++;for(var u=Fe(o,t.toString()),a=Ge(n.element,n.rowspan,n.colspan,c,t,u),l=0;l<n.colspan;l++)for(var f=0;f<n.rowspan;f++){var s=c+f,m=t+l,d=Qr(s,m);e[d]=a,i=Math.max(i,m+1)}r.push(a)})),u++,t.push(Ye(n.element,r,n.section)),c++}));var s=tn(l).map((function(n){var e=to(n),t=tt(n.element,Le(e));return{colgroups:[t],columns:e}})).getOrThunk((function(){return{colgroups:[],columns:{}}})),m=s.columns,d=s.colgroups,g=Ve(u,i);return{grid:g,access:e,all:t,columns:m,colgroups:d}},oo=function(n){var e=Lr(n);return ro(e)},uo=function(n){return X(n.all,(function(n){return n.cells}))},io=function(n){return Le(n.columns)},co=function(n){return Oe(n.columns).length>0},ao=function(n,e){return E.from(n.columns[e])},lo={fromTable:oo,generate:ro,getAt:Zr,findItem:no,filterItems:eo,justCells:uo,justColumns:io,hasColumns:co,getColumnAt:ao},fo=function(n,e){var t=e.column,r=e.column+e.colspan-1,o=e.row,u=e.row+e.rowspan-1;return t<=n.finishCol&&r>=n.startCol&&o<=n.finishRow&&u>=n.startRow},so=function(n,e){return e.column>=n.startCol&&e.column+e.colspan-1<=n.finishCol&&e.row>=n.startRow&&e.row+e.rowspan-1<=n.finishRow},mo=function(n,e){for(var t=!0,r=y(so,e),o=e.startRow;o<=e.finishRow;o++)for(var u=e.startCol;u<=e.finishCol;u++)t=t&&lo.getAt(n,o,u).exists(r);return t?E.some(e):E.none()},go=function(n,e){return nt(Math.min(n.row,e.row),Math.min(n.column,e.column),Math.max(n.row+n.rowspan-1,e.row+e.rowspan-1),Math.max(n.column+n.colspan-1,e.column+e.colspan-1))},po=function(n,e,t){var r=lo.findItem(n,e,Se),o=lo.findItem(n,t,Se);return r.bind((function(n){return o.map((function(e){return go(n,e)}))}))},vo=function(n,e,t){return po(n,e,t).bind((function(e){return mo(n,e)}))},ho=function(n,e,t,r){return lo.findItem(n,e,Se).bind((function(e){var o=t>0?e.row+e.rowspan-1:e.row,u=r>0?e.column+e.colspan-1:e.column,i=lo.getAt(n,o+t,u+r);return i.map((function(n){return n.element}))}))},bo=function(n,e,t){return po(n,e,t).map((function(e){var t=lo.filterItems(n,y(fo,e));return L(t,(function(n){return n.element}))}))},wo=function(n,e){var t=function(n,e){return Ae(e,n)};return lo.findItem(n,e,t).map((function(n){return n.element}))},yo=function(n,e,t){return Ir(n).bind((function(r){var o=xo(r);return ho(o,n,e,t)}))},Co=function(n,e,t){var r=xo(n);return bo(r,e,t)},To=function(n,e,t,r,o){var u=xo(n),i=Se(n,t)?E.some(e):wo(u,e),c=Se(n,o)?E.some(r):wo(u,r);return i.bind((function(n){return c.bind((function(e){return bo(u,n,e)}))}))},So=function(n,e,t){var r=xo(n);return vo(r,e,t)},xo=lo.fromTable,Ro=function(n,e){var t=pt(n);t.each((function(t){t.dom.insertBefore(e.dom,n.dom)}))},Ao=function(n,e){var t=wt(n);t.fold((function(){var t=pt(n);t.each((function(n){Oo(n,e)}))}),(function(n){Ro(n,e)}))},Do=function(n,e){var t=Tt(n);t.fold((function(){Oo(n,e)}),(function(t){n.dom.insertBefore(e.dom,t.dom)}))},Oo=function(n,e){n.dom.appendChild(e.dom)},Eo=function(n,e,t){Ct(n,t).fold((function(){Oo(n,e)}),(function(n){Ro(n,e)}))},ko=function(n,e){Ro(n,e),Oo(e,n)},Mo=function(n,e){_(e,(function(e){Ro(n,e)}))},Io=function(n,e){_(e,(function(t,r){var o=0===r?n:e[r-1];Ao(o,t)}))},No=function(n,e){_(e,(function(e){Oo(n,e)}))},Bo=function(n){n.dom.textContent="",_(yt(n),(function(n){Po(n)}))},Po=function(n){var e=n.dom;null!==e.parentNode&&e.parentNode.removeChild(e)},jo=function(n){var e=yt(n);e.length>0&&Mo(n,e),Po(n)},Lo=function(n,e){var t=function(t){if(!n(t))throw new Error("Can only get "+e+" value of a "+e+" node");return r(t).getOr("")},r=function(e){return n(e)?E.from(e.dom.nodeValue):E.none()},o=function(t,r){if(!n(t))throw new Error("Can only set raw "+e+" value of a "+e+" node");t.dom.nodeValue=r};return{get:t,getOption:r,set:o}},_o=Lo(at,"text"),zo=function(n){return _o.get(n)},Wo=function(n){return _o.getOption(n)},Fo=function(n,e){return _o.set(n,e)},Uo=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function Ho(){var n=function(n){return be.fromDom(n.dom.cloneNode(!1))},e=function(n){return dt(n).dom},t=function(n){return!!ct(n)&&("body"===rt(n)||B(Uo,rt(n)))},r=function(n){return!!ct(n)&&B(["br","img","hr","input"],rt(n))},o=function(n){return ct(n)&&"false"===Zt(n,"contenteditable")},u=function(n,e){return n.dom.compareDocumentPosition(e.dom)},i=function(n,e){var t=tr(n);Jt(e,t)},c=function(n){var e=rt(n);return B(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],e)},a=function(n){return ct(n)?nr(n,"lang"):E.none()};return{up:h({selector:Vt,closest:Xt,predicate:Ft,all:ht}),down:h({selector:zt,predicate:jt}),styles:h({get:gr,getRaw:vr,set:mr,remove:hr}),attrs:h({get:Zt,set:Yt,remove:er,copyTo:i}),insert:h({before:Ro,after:Ao,afterAll:Io,append:Oo,appendAll:No,prepend:Do,wrap:ko}),remove:h({unwrap:jo,remove:Po}),create:h({nu:be.fromTag,clone:n,text:be.fromText}),query:h({comparePosition:u,prevSibling:bt,nextSibling:wt}),property:h({children:yt,name:rt,parent:pt,document:e,isText:at,isComment:it,isElement:ct,isSpecial:c,getLanguage:a,getText:zo,setText:Fo,isBoundary:t,isEmptyTag:r,isNonEditable:o}),eq:Se,is:De}}var $o=function(n,e,t,r){var o=t[0],u=t.slice(1);return r(n,e,o,u)},Vo=function(n,e,t){return t.length>0?$o(n,e,t,qo):E.none()},qo=function(n,e,t,r){var o=e(n,t);return U(r,(function(t,r){var o=e(n,r);return Ko(n,t,o)}),o)},Ko=function(n,e,t){return e.bind((function(e){return t.filter(y(n.eq,e))}))},Xo=function(n,e){return y(n.eq,e)},Go=function(n,e,t,r){void 0===r&&(r=S);var o=[e].concat(n.up().all(e)),u=[t].concat(n.up().all(t)),i=function(n){var e=q(n,r);return e.fold((function(){return n}),(function(e){return n.slice(0,e+1)}))},c=i(o),a=i(u),l=V(c,(function(e){return P(a,Xo(n,e))}));return{firstpath:c,secondpath:a,shared:l}},Yo=Vo,Jo=Go,Qo=Ho(),Zo=function(n,e){return Yo(Qo,(function(e,t){return n(t)}),e)},nu=function(n,e,t){return Jo(Qo,n,e,t)},eu=function(n){return Vt(n,"table")},tu=function(n,e,t){var r=function(n){return function(e){return void 0!==t&&t(e)||Se(e,n)}};return Se(n,e)?E.some({boxes:E.some([n]),start:n,finish:e}):eu(n).bind((function(o){return eu(e).bind((function(u){if(Se(o,u))return E.some({boxes:Co(o,n,e),start:n,finish:e});if(Ae(o,u)){var i=Lt(e,"td,th",r(o)),c=i.length>0?i[i.length-1]:e;return E.some({boxes:To(o,n,o,e,u),start:n,finish:c})}if(Ae(u,o)){i=Lt(n,"td,th",r(u));var a=i.length>0?i[i.length-1]:n;return E.some({boxes:To(u,n,o,e,u),start:n,finish:a})}return nu(n,e).shared.bind((function(i){return Xt(i,"table",t).bind((function(t){var i=Lt(e,"td,th",r(t)),c=i.length>0?i[i.length-1]:e,a=Lt(n,"td,th",r(t)),l=a.length>0?a[a.length-1]:n;return E.some({boxes:To(t,n,o,e,u),start:l,finish:c})}))}))}))}))},ru=function(n,e){var t=zt(n,e);return t.length>0?E.some(t):E.none()},ou=function(n,e){return V(n,(function(n){return we(n,e)}))},uu=function(n,e,t){return Kt(n,e).bind((function(e){return Kt(n,t).bind((function(n){return Zo(eu,[e,n]).map((function(t){return{first:e,last:n,table:t}}))}))}))},iu=function(n,e){return Vt(n,"table").bind((function(t){return Kt(t,e).bind((function(e){return tu(e,n).bind((function(n){return n.boxes.map((function(e){return{boxes:e,start:n.start,finish:n.finish}}))}))}))}))},cu=function(n,e,t,r,o){return ou(n,o).bind((function(n){return yo(n,e,t).bind((function(n){return iu(n,r)}))}))},au=function(n,e){return ru(n,e)},lu=function(n,e,t){return uu(n,e,t).bind((function(e){var t=function(e){return Se(n,e)},r="thead,tfoot,tbody,table",o=Vt(e.first,r,t),u=Vt(e.last,r,t);return o.bind((function(n){return u.bind((function(t){return Se(n,t)?So(e.table,e.first,e.last):E.none()}))}))}))},fu=function(n){if(!i(n))throw new Error("cases must be an array");if(0===n.length)throw new Error("there must be at least one case");var e=[],t={};return _(n,(function(r,o){var u=Oe(r);if(1!==u.length)throw new Error("one and only one name per case");var c=u[0],a=r[c];if(void 0!==t[c])throw new Error("duplicate key detected:"+c);if("cata"===c)throw new Error("cannot have a case named cata (sorry)");if(!i(a))throw new Error("case arguments must be an array");e.push(c),t[c]=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var u=t.length;if(u!==a.length)throw new Error("Wrong number of arguments to case "+c+". Expected "+a.length+" ("+a+"), got "+u);var i=function(n){var r=Oe(n);if(e.length!==r.length)throw new Error("Wrong number of arguments to match. Expected: "+e.join(",")+"\nActual: "+r.join(","));var o=G(e,(function(n){return B(r,n)}));if(!o)throw new Error("Not all branches were specified when using match. Specified: "+r.join(", ")+"\nRequired: "+e.join(", "));return n[c].apply(null,t)};return{fold:function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];if(e.length!==n.length)throw new Error("Wrong number of arguments to fold. Expected "+n.length+", got "+e.length);var u=e[o];return u.apply(null,t)},match:i,log:function(n){}}}})),t},su={generate:fu},mu=su.generate([{none:[]},{multiple:["elements"]},{single:["element"]}]),du=function(n,e,t,r){return n.fold(e,t,r)},gu=mu.none,pu=mu.multiple,vu=mu.single,hu=function(n,e,t){var r=function(){return au(n(),t).fold((function(){return e().fold(gu,vu)}),(function(n){return pu(n)}))};return{get:r}},bu=tinymce.util.Tools.resolve("tinymce.PluginManager"),wu=function(n,e){return be.fromDom(n.dom.cloneNode(e))},yu=function(n){return wu(n,!1)},Cu=function(n){return wu(n,!0)},Tu=function(n,e){var t=be.fromTag(e),r=tr(n);return Jt(t,r),t},Su=function(n,e){var t=Tu(n,e),r=yt(Cu(n));return No(t,r),t},xu=function(n,e){var t=Tu(n,e);Ro(n,t);var r=yt(n);return No(t,r),Po(n),t},Ru=function(n,e){var t=function(e,t){if(!d(t)&&!t.match(/^[0-9]+$/))throw new Error(n+".set accepts only positive integer values. Value was "+t);var r=e.dom;lr(r)&&(r.style[n]=t+"px")},r=function(t){var r=e(t);if(r<=0||null===r){var o=gr(t,n);return parseFloat(o)||0}return r},o=r,u=function(n,e){return H(e,(function(e,t){var r=gr(n,t),o=void 0===r?0:parseInt(r,10);return isNaN(o)?e:e+o}),0)},i=function(n,e,t){var r=u(n,t),o=e>r?e-r:0;return o};return{set:t,get:r,getOuter:o,aggregate:u,max:i}},Au=function(){var n=ue().browser;return n.isIE()||n.isEdge()},Du=function(n,e){return En(n).getOr(e)},Ou=function(n,e,t){return Du(gr(n,e),t)},Eu=function(n){return gr(n,"box-sizing")},ku=function(n,e,t,r){var o=Ou(n,"padding-"+t,0),u=Ou(n,"padding-"+r,0),i=Ou(n,"border-"+t+"-width",0),c=Ou(n,"border-"+r+"-width",0);return e-o-u-i-c},Mu=function(n,e){var t=n.dom,r=t.getBoundingClientRect().height||t.offsetHeight;return"border-box"===e?r:ku(n,r,"top","bottom")},Iu=function(n,e){var t=n.dom,r=t.getBoundingClientRect().width||t.offsetWidth;return"border-box"===e?r:ku(n,r,"left","right")},Nu=function(n){return Au()?Mu(n,Eu(n)):Ou(n,"height",n.dom.offsetHeight)},Bu=function(n){return Au()?Iu(n,Eu(n)):Ou(n,"width",n.dom.offsetWidth)},Pu=function(n){return Iu(n,"content-box")},ju=Ru("width",(function(n){return n.dom.offsetWidth})),Lu=function(n){return ju.get(n)},_u=function(n){return ju.getOuter(n)},zu=Pu,Wu=Bu,Fu=function(n,e){void 0===e&&(e=x);var t=n.grid,r=j(t.columns,b),o=j(t.rows,b);return L(r,(function(t){var r=function(){return X(o,(function(e){return lo.getAt(n,e,t).filter((function(n){return n.column===t})).toArray()}))},u=function(n){return 1===n.colspan&&e(n.element)},i=function(){return lo.getAt(n,0,t)};return Uu(r,u,i)}))},Uu=function(n,e,t){var r=n(),o=V(r,e),u=o.orThunk((function(){return E.from(r[0]).orThunk(t)}));return u.map((function(n){return n.element}))},Hu=function(n){var e=n.grid,t=j(e.rows,b),r=j(e.columns,b);return L(t,(function(e){var t=function(){return X(r,(function(t){return lo.getAt(n,e,t).filter((function(n){return n.row===e})).fold(h([]),(function(n){return[n]}))}))},o=function(n){return 1===n.rowspan},u=function(){return lo.getAt(n,e,0)};return Uu(t,o,u)}))},$u=function(n,e){if(e<0||e>=n.length-1)return E.none();var t=n[e].fold((function(){var t=Y(n.slice(0,e));return rn(t,(function(n,e){return n.map((function(n){return{value:n,delta:e+1}}))}))}),(function(n){return E.some({value:n,delta:0})})),r=n[e+1].fold((function(){var t=n.slice(e+1);return rn(t,(function(n,e){return n.map((function(n){return{value:n,delta:e+1}}))}))}),(function(n){return E.some({value:n,delta:1})}));return t.bind((function(n){return r.map((function(e){var t=e.delta+n.delta;return Math.abs(e.value-n.value)/t}))}))},Vu=function(n,e){return function(t){return"rtl"===qu(t)?e:n}},qu=function(n){return"rtl"===gr(n,"direction")?"rtl":"ltr"},Ku=Ru("height",(function(n){var e=n.dom;return Mt(n)?e.getBoundingClientRect().height:e.offsetHeight})),Xu=function(n){return Ku.get(n)},Gu=function(n){return Ku.getOuter(n)},Yu=Nu,Ju=function(n,e){var t=function(t,r){return Ju(n+t,e+r)};return{left:n,top:e,translate:t}},Qu=Ju,Zu=function(n){var e=n.getBoundingClientRect();return Qu(e.left,e.top)},ni=function(n,e){return void 0!==n?n:void 0!==e?e:0},ei=function(n){var e=n.dom.ownerDocument,t=e.body,r=e.defaultView,o=e.documentElement;if(t===n.dom)return Qu(t.offsetLeft,t.offsetTop);var u=ni(null===r||void 0===r?void 0:r.pageYOffset,o.scrollTop),i=ni(null===r||void 0===r?void 0:r.pageXOffset,o.scrollLeft),c=ni(o.clientTop,t.clientTop),a=ni(o.clientLeft,t.clientLeft);return ti(n).translate(i-a,u-c)},ti=function(n){var e=n.dom,t=e.ownerDocument,r=t.body;return r===e?Qu(r.offsetLeft,r.offsetTop):Mt(n)?Zu(e):Qu(0,0)},ri=function(n,e){return{row:n,y:e}},oi=function(n,e){return{col:n,x:e}},ui=function(n){var e=ei(n);return e.left+_u(n)},ii=function(n){return ei(n).left},ci=function(n,e){return oi(n,ii(e))},ai=function(n,e){return oi(n,ui(e))},li=function(n){return ei(n).top},fi=function(n,e){return ri(n,li(e))},si=function(n,e){return ri(n,li(e)+Gu(e))},mi=function(n,e,t){if(0===t.length)return[];var r=L(t.slice(1),(function(e,t){return e.map((function(e){return n(t,e)}))})),o=t[t.length-1].map((function(n){return e(t.length-1,n)}));return r.concat([o])},di=function(n){return-n},gi={delta:b,positions:function(n){return mi(fi,si,n)},edge:li},pi={delta:b,edge:ii,positions:function(n){return mi(ci,ai,n)}},vi={delta:di,edge:ui,positions:function(n){return mi(ai,ci,n)}},hi=Vu(pi,vi),bi={delta:function(n,e){return hi(e).delta(n,e)},positions:function(n,e){return hi(e).positions(n,e)},edge:function(n){return hi(n).edge(n)}},wi={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},yi=function(){var n="[0-9]+",e="[+-]?"+n,t="[eE]"+e,r="\\.",o=function(n){return"(?:"+n+")?"},u=["Infinity",n+r+o(n)+o(t),r+n+o(t),n+o(t)].join("|"),i="[+-]?(?:"+u+")";return new RegExp("^("+i+")(.*)$")}(),Ci=function(n,e){return P(e,(function(e){return P(wi[e],(function(e){return n===e}))}))},Ti=function(n,e){var t=E.from(yi.exec(n));return t.bind((function(n){var t=Number(n[1]),r=n[2];return Ci(r,e)?E.some({value:t,unit:r}):E.none()}))},Si=/(\d+(\.\d+)?)%/,xi=/(\d+(\.\d+)?)px|em/,Ri=st("col"),Ai=function(n,e,t){var r=vt(n).getOrThunk((function(){return Nt(mt(n))}));return e(n)/t(r)*100},Di=function(n,e){mr(n,"width",e+"px")},Oi=function(n,e){mr(n,"width",e+"%")},Ei=function(n,e){mr(n,"height",e+"px")},ki=function(n){return Yu(n)+"px"},Mi=function(n,e,t,r){var o=Ir(n).map((function(n){var r=t(n);return Math.floor(e/100*r)})).getOr(e);return r(n,o),o},Ii=function(n,e,t,r){var o=parseFloat(n);return xn(n,"%")&&"table"!==rt(e)?Mi(e,o,t,r):o},Ni=function(n){var e=ki(n);return e?Ii(e,n,Xu,Ei):Xu(n)},Bi=function(n,e,t){var r=t(n),o=yr(n,e);return r/o},Pi=function(n,e){return vr(n,e).orThunk((function(){return nr(n,e).map((function(n){return n+"px"}))}))},ji=function(n){return Pi(n,"width")},Li=function(n){return Pi(n,"height")},_i=function(n){return Ai(n,Lu,zu)},zi=function(n){return Ri(n)?Lu(n):Wu(n)},Wi=function(n){return Bi(n,"rowspan",Ni)},Fi=function(n){var e=ji(n);return e.bind((function(n){return Ti(n,["fixed","relative","empty"])}))},Ui=function(n,e,t){mr(n,"width",e+t)},Hi=function(n){return Lu(n)+"px"},$i=function(n){return Ai(n,Lu,zu)+"%"},Vi=function(n){return ji(n).exists((function(n){return Si.test(n)}))},qi=function(n){return ji(n).exists((function(n){return xi.test(n)}))},Ki=function(n){return ji(n).isNone()},Xi=h(Si),Gi=st("col"),Yi=function(n){return ji(n).getOrThunk((function(){return zi(n)+"px"}))},Ji=function(n){return Li(n).getOrThunk((function(){return Wi(n)+"px"}))},Qi=function(n){return L(lo.justColumns(n),(function(n){return E.from(n.element)}))},Zi=function(n){var e=ue().browser,t=e.isChrome()||e.isFirefox();return!Gi(n)||t},nc=function(n,e,t,r,o,u){return n.filter(r).fold((function(){return u($u(t,e))}),(function(n){return o(n)}))},ec=function(n,e,t,r){var o=Fu(n),u=lo.hasColumns(n)?Qi(n):o,i=[E.some(bi.edge(e))].concat(L(bi.positions(o,e),(function(n){return n.map((function(n){return n.x}))}))),c=C(Cr);return L(u,(function(n,e){return nc(n,e,i,c,(function(n){if(Zi(n))return t(n);var u=ir(o[e],b);return nc(u,e,i,c,(function(n){return r(E.some(Lu(n)))}),r)}),r)}))},tc=function(n){return n.map((function(n){return n+"px"})).getOr("")},rc=function(n,e){return ec(n,e,Yi,tc)},oc=function(n,e,t){return ec(n,e,_i,(function(n){return n.fold((function(){return t.minCellWidth()}),(function(n){return n/t.pixelWidth()*100}))}))},uc=function(n,e,t){return ec(n,e,zi,(function(n){return n.getOrThunk(t.minCellWidth)}))},ic=function(n,e,t,r,o){var u=Hu(n),i=[E.some(t.edge(e))].concat(L(t.positions(u,e),(function(n){return n.map((function(n){return n.y}))})));return L(u,(function(n,e){return nc(n,e,i,C(Tr),r,o)}))},cc=function(n,e,t){return ic(n,e,t,Wi,(function(n){return n.getOrThunk(Rr)}))},ac=function(n,e,t){return ic(n,e,t,Ji,tc)},lc=function(n,e){return function(){return Mt(n)?e(n):parseFloat(vr(n,"width").getOr("0"))}},fc=function(n){var e=lc(n,Lu),t=h(0),r=function(e,t){return uc(e,n,t)};return{width:e,pixelWidth:e,getWidths:r,getCellDelta:t,singleColumnWidth:h([0]),minCellWidth:t,setElementWidth:g,adjustTableWidth:g,isRelative:!0,label:"none"}},sc=function(n){var e=lc(n,(function(n){return parseFloat($i(n))})),t=lc(n,Lu),r=function(n){return n/t()*100},o=function(n,e){return[100-n]},u=function(){return xr()/t()*100},i=function(t){var r=e(),o=t/100*r,u=r+o;Oi(n,u)},c=function(e,t){return oc(e,n,t)};return{width:e,pixelWidth:t,getWidths:c,getCellDelta:r,singleColumnWidth:o,minCellWidth:u,setElementWidth:Oi,adjustTableWidth:i,isRelative:!0,label:"percent"}},mc=function(n){var e=lc(n,Lu),t=b,r=function(n,e){var t=Math.max(xr(),n+e);return[t-n]},o=function(t){var r=e()+t;Di(n,r)},u=function(e,t){return uc(e,n,t)};return{width:e,pixelWidth:e,getWidths:u,getCellDelta:t,singleColumnWidth:r,minCellWidth:xr,setElementWidth:Di,adjustTableWidth:o,isRelative:!1,label:"pixel"}},dc=function(n,e){var t=Xi().exec(e);return null!==t?sc(n):mc(n)},gc=function(n){var e=ji(n);return e.fold((function(){return fc(n)}),(function(e){return dc(n,e)}))},pc={getTableSize:gc,pixelSize:mc,percentageSize:sc,noneSize:fc},vc=function(n,e,t,r,o,u){return{minRow:n,minCol:e,maxRow:t,maxCol:r,allCells:o,selectedCells:u}},hc=function(n,e){var t=n.grid.columns,r=n.grid.rows,o=r,u=t,i=0,c=0,a=[],l=[];return ke(n.access,(function(n){if(a.push(n),e(n)){l.push(n);var t=n.row,r=t+n.rowspan-1,f=n.column,s=f+n.colspan-1;t<o?o=t:r>i&&(i=r),f<u?u=f:s>c&&(c=s)}})),vc(o,u,i,c,a,l)},bc=function(n,e,t){var r=n[t].element,o=be.fromTag("td");Oo(o,be.fromTag("br"));var u=e?Oo:Do;u(r,o)},wc=function(n,e,t,r){for(var o=e.grid.columns,u=e.grid.rows,i=0;i<u;i++)for(var c=!1,a=0;a<o;a++)if(!(i<t.minRow||i>t.maxRow||a<t.minCol||a>t.maxCol)){var l=lo.getAt(e,i,a).filter(r).isNone();l?bc(n,c,i):c=!0}},yc=function(n,e,t,r){ke(t.columns,(function(n){(n.column<e.minCol||n.column>e.maxCol)&&Po(n.element)}));var o=F(Ar(n,"tr"),(function(n){return 0===n.dom.childElementCount}));_(o,Po),e.minCol!==e.maxCol&&e.minRow!==e.maxRow||_(Ar(n,"th,td"),(function(n){er(n,"rowspan"),er(n,"colspan")})),er(n,Gr),er(n,"data-snooker-col-series");var u=pc.getTableSize(n);u.adjustTableWidth(r)},Cc=function(n,e,t,r){if(0===r.minCol&&e.grid.columns===r.maxCol+1)return 0;var o=uc(e,n,t),u=H(o,(function(n,e){return n+e}),0),i=H(o.slice(r.minCol,r.maxCol+1),(function(n,e){return n+e}),0),c=i/u*t.pixelWidth(),a=c-t.pixelWidth();return t.getCellDelta(a)},Tc=function(n,e){var t=function(n){return we(n.element,e)},r=Cu(n),o=Lr(r),u=pc.getTableSize(n),i=lo.generate(o),c=hc(i,t),a="th:not("+e+"),td:not("+e+")",l=Dr(r,"th,td",(function(n){return we(n,a)}));_(l,Po),wc(o,i,c,t);var f=lo.fromTable(n),s=Cc(n,f,u,c);return yc(r,c,i,s),r},Sc=" ",xc=function(n){return"img"===rt(n)?1:Wo(n).fold((function(){return yt(n).length}),(function(n){return n.length}))},Rc=function(n){return Wo(n).filter((function(n){return 0!==n.trim().length||n.indexOf(Sc)>-1})).isSome()},Ac=["img","br"],Dc=function(n){var e=Rc(n);return e||B(Ac,rt(n))},Oc=function(n){return $t(n,Dc)},Ec=function(n){return kc(n,Dc)},kc=function(n,e){var t=function(n){for(var r=yt(n),o=r.length-1;o>=0;o--){var u=r[o];if(e(u))return E.some(u);var i=t(u);if(i.isSome())return i}return E.none()};return t(n)},Mc={scope:["row","col"]},Ic=function(n){return function(){var e=be.fromTag("td",n.dom);return Oo(e,be.fromTag("br",n.dom)),e}},Nc=function(n){return function(){return be.fromTag("col",n.dom)}},Bc=function(n){return function(){return be.fromTag("colgroup",n.dom)}},Pc=function(n){return function(){return be.fromTag("tr",n.dom)}},jc=function(n,e,t){var r=Su(n,e);return ke(t,(function(n,e){null===n?er(r,e):Yt(r,e,n)})),r},Lc=function(n){return n},_c=function(n,e,t){var r=Oc(n);return r.map((function(r){var o=t.join(","),u=Lt(r,o,(function(e){return Se(e,n)}));return U(u,(function(n,e){var t=yu(e);return er(t,"contenteditable"),Oo(n,t),t}),e)})).getOr(e)},zc=function(n,e){ke(Mc,(function(t,r){return nr(n,r).filter((function(n){return B(t,n)})).each((function(n){return Yt(e,r,n)}))}))},Wc=function(n,e,t){var r=function(n,e){br(n.element,e),hr(e,"height"),1!==n.colspan&&hr(e,"width")},o=function(o){var u=be.fromTag(rt(o.element),e.dom),i=t.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),c=i.length>0?_c(o.element,u,i):u;return Oo(c,be.fromTag("br")),r(o,u),zc(o.element,u),n(o.element,u),u},u=function(t){var o=be.fromTag(rt(t.element),e.dom);return r(t,o),n(t.element,o),o};return{col:u,colgroup:Bc(e),row:Pc(e),cell:o,replace:jc,colGap:Nc(e),gap:Ic(e)}},Fc=function(n){return{col:Nc(n),colgroup:Bc(n),row:Pc(n),cell:Ic(n),replace:Lc,colGap:Nc(n),gap:Ic(n)}},Uc=function(n,e){var t=e||document,r=t.createElement("div");return r.innerHTML=n,yt(be.fromDom(r))},Hc=function(n){return L(n,be.fromDom)},$c=function(n){return n.nodeName.toLowerCase()},Vc=function(n){return be.fromDom(n.getBody())},qc=function(n){return n.getBoundingClientRect().width},Kc=function(n){return n.getBoundingClientRect().height},Xc=function(n){return function(e){return Se(e,Vc(n))}},Gc=function(n){return n?n.replace(/px$/,""):""},Yc=function(n){return/^\d+(\.\d+)?$/.test(n)?n+"px":n},Jc=function(n){er(n,"data-mce-style");var e=function(n){return er(n,"data-mce-style")};_(kr(n),e),_(Mr(n),e),_(Nr(n),e)},Qc=function(n,e){var t=n.dom.getStyle(e,"width")||n.dom.getAttrib(e,"width");return E.from(t).filter(Dn)},Zc=function(n){return/^(\d+(\.\d+)?)%$/.test(n)},na=function(n){return/^(\d+(\.\d+)?)px$/.test(n)},ea=function(n){return be.fromDom(n.selection.getStart())},ta=function(n){return be.fromDom(n.selection.getEnd())},ra=function(n){return du(n.get(),h([]),b,Q)},oa=function(n){var e=function(n,e){return nr(n,e).exists((function(n){return parseInt(n,10)>1}))},t=function(n){return e(n,"rowspan")||e(n,"colspan")},r=ra(n);return r.length>0&&G(r,t)?E.some(r):E.none()},ua=function(n,e,t){return du(e.get(),E.none,(function(e){return e.length<=1?E.none():lu(n,t.firstSelectedSelector,t.lastSelectedSelector).map((function(n){return{bounds:n,cells:e}}))}),E.none)},ia="data-mce-selected",ca="td["+ia+"],th["+ia+"]",aa="["+ia+"]",la="data-mce-first-selected",fa="td["+la+"],th["+la+"]",sa="data-mce-last-selected",ma="td["+sa+"],th["+sa+"]",da=aa,ga={selected:ia,selectedSelector:ca,firstSelected:la,firstSelectedSelector:fa,lastSelected:sa,lastSelectedSelector:ma},pa=function(n){return{element:n,mergable:E.none(),unmergable:E.none(),selection:[n]}},va=function(n,e,t){return{element:t,mergable:ua(e,n,ga),unmergable:oa(n),selection:ra(n)}},ha=function(n,e,t){return{element:n,clipboard:e,generators:t}},ba=function(n,e,t,r){return{selection:ra(n),clipboard:t,generators:r}},wa=function(n){return Ir(n).bind((function(n){return au(n,ga.firstSelectedSelector)})).fold(h(n),(function(n){return n[0]}))},ya=function(n){return function(e,t){var r=rt(e),o="col"===r||"colgroup"===r?wa(e):e;return Xt(o,n,t)}},Ca=ya("th,td,caption"),Ta=ya("th,td"),Sa=function(n){return ra(n)},xa=function(n,e){var t=Ta(n),r=t.bind((function(n){return Ir(n)})).map((function(n){return Nr(n)}));return ur(t,r,(function(n,t){return F(t,(function(t){return P(Hc(t.dom.cells),(function(t){return"1"===Zt(t,e)||Se(t,n)}))}))})).getOr([])},Ra=function(n){return Ir(n[0]).map((function(n){var e=Tc(n,da);return Jc(e),[e]}))},Aa=function(n,e){return L(e,(function(e){return n.selection.serializer.serialize(e.dom,{})})).join("")},Da=function(n){return L(n,(function(n){return n.dom.innerText})).join("")},Oa=function(n,e,t){n.on("BeforeGetContent",(function(t){var r=function(e){t.preventDefault(),Ra(e).each((function(e){t.content="text"===t.format?Da(e):Aa(n,e)}))};!0===t.selection&&du(e.get(),g,r,g)})),n.on("BeforeSetContent",(function(r){if(!0===r.selection&&!0===r.paste){var o=Sa(e);en(o).each((function(e){Ir(e).each((function(o){var u=F(Uc(r.content),(function(n){return"meta"!==rt(n)})),i=st("table");if(1===u.length&&i(u[0])){r.preventDefault();var c=be.fromDom(n.getDoc()),a=Fc(c),l=ha(e,u[0],a);t.pasteCells(o,l).each((function(){n.focus()}))}}))}))}}))},Ea=su.generate([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),ka=on({},Ea),Ma=function(n,e){return 0===n.length?ka.none():1===n.length?ka.only(0):0===e?ka.left(0,1):e===n.length-1?ka.right(e-1,e):e>0&&e<n.length-1?ka.middle(e-1,e,e+1):ka.none()},Ia=function(n,e,t,r,o){var u=n.slice(0),i=Ma(n,e),c=h(L(u,h(0))),a=function(n){return r.singleColumnWidth(u[n],t)},l=function(n,e){return o.calcLeftEdgeDeltas(u,n,e,t,r.minCellWidth(),r.isRelative)},f=function(n,e,i){return o.calcMiddleDeltas(u,n,e,i,t,r.minCellWidth(),r.isRelative)},s=function(n,e){return o.calcRightEdgeDeltas(u,n,e,t,r.minCellWidth(),r.isRelative)};return i.fold(c,a,l,f,s)},Na=function(n,e,t){for(var r=0,o=n;o<e;o++)r+=void 0!==t[o]?t[o]:0;return r},Ba=function(n,e){var t=lo.justCells(n);return L(t,(function(n){var t=Na(n.column,n.column+n.colspan,e);return{element:n.element,width:t,colspan:n.colspan}}))},Pa=function(n,e){var t=lo.justColumns(n);return L(t,(function(n,t){return{element:n.element,width:e[t],colspan:n.colspan}}))},ja=function(n,e){var t=lo.justCells(n);return L(t,(function(n){var t=Na(n.row,n.row+n.rowspan,e);return{element:n.element,height:t,rowspan:n.rowspan}}))},La=function(n,e){return L(n.all,(function(n,t){return{element:n.element,height:e[t]}}))},_a=function(n){return U(n,(function(n,e){return n+e}),0)},za=function(n,e){return lo.hasColumns(n)?Pa(n,e):Ba(n,e)},Wa=function(n,e,t){var r=za(n,e);_(r,(function(n){t.setElementWidth(n.element,n.width)}))},Fa=function(n,e,t,r,o){var u=lo.fromTable(n),i=o.getCellDelta(e),c=o.getWidths(u,o),a=t===u.grid.columns-1,l=r.clampTableDelta(c,t,i,o.minCellWidth(),a),f=Ia(c,t,l,o,r),s=L(f,(function(n,e){return n+c[e]}));Wa(u,s,o),r.resizeTable(o.adjustTableWidth,l,a)},Ua=function(n,e,t,r){var o=lo.fromTable(n),u=cc(o,n,r),i=L(u,(function(n,r){return t===r?Math.max(e+n,Rr()):n})),c=ja(o,i),a=La(o,i);_(a,(function(n){Ei(n.element,n.height)})),_(c,(function(n){Ei(n.element,n.height)}));var l=_a(i);Ei(n,l)},Ha=function(n,e,t,r,o){var u=lo.generate(e),i=r.getWidths(u,r),c=r.pixelWidth(),a=o.calcRedestributedWidths(i,c,t.pixelDelta,r.isRelative),l=a.newSizes,f=a.delta;Wa(u,l,r),r.adjustTableWidth(f)},$a=function(n,e,t,r){var o=lo.generate(e),u=r.getWidths(o,r);Wa(o,u,r)},Va=function(n){return L(n,h(0))},qa=function(n,e,t,r,o){return o(n.slice(0,e)).concat(r).concat(o(n.slice(t)))},Ka=function(n){return function(e,t,r,o){if(n(r)){var u=Math.max(o,e[t]-Math.abs(r)),i=Math.abs(u-e[t]);return r>=0?i:-i}return r}},Xa=Ka((function(n){return n<0})),Ga=Ka(x),Ya=function(){var n=function(n,e,t,r,o){var u=Xa(n,e,r,o);return qa(n,e,t+1,[u,0],Va)},e=function(n,e,t,r){var o=(100+t)/100,u=Math.max(r,(n[e]+t)/o);return L(n,(function(n,t){var r=t===e?u:n/o;return r-n}))},t=function(t,r,o,u,i,c){return c?e(t,r,u,i):n(t,r,o,u,i)},r=function(n,e,r,o,u,i,c){return t(n,r,o,u,i,c)},o=function(n,e){return n(e)},u=function(n,t,r,o,u,i){if(i)return e(n,r,o,u);var c=Xa(n,r,o,u);return Va(n.slice(0,r)).concat([c])},i=function(n,e,t,r){if(r){var o=e+t,u=o/e,i=L(n,(function(n){return n/u}));return{delta:100*u-100,newSizes:i}}return{delta:t,newSizes:n}};return{resizeTable:o,clampTableDelta:Xa,calcLeftEdgeDeltas:t,calcMiddleDeltas:r,calcRightEdgeDeltas:u,calcRedestributedWidths:i}},Ja=function(){var n=function(n,e,t,r,o){var u=r>=0?t:e,i=Ga(n,u,r,o);return qa(n,e,t+1,[i,-i],Va)},e=function(e,t,r,o,u,i){return n(e,r,o,u,i)},t=function(n,e,t){t&&n(e)},r=function(n,e,t,r,o,u){if(u)return Va(n);var i=r/n.length;return L(n,h(i))},o=function(n,e,t,r,o){if(o){if(t>=0)return t;var u=H(n,(function(n,e){return n+e-r}),0);return Math.max(-u,t)}return Xa(n,e,t,r)},u=function(n,e,t,r){return{delta:0,newSizes:n}};return{resizeTable:t,clampTableDelta:o,calcLeftEdgeDeltas:n,calcMiddleDeltas:e,calcRightEdgeDeltas:r,calcRedestributedWidths:u}},Qa=function(n,e){var t=E.from(n.dom.documentElement).map(be.fromDom).getOr(n);return{parent:h(t),view:h(n),origin:h(Qu(0,0)),isResizable:e}},Za=function(n,e,t){var r=function(){return ei(e)};return{parent:h(e),view:h(n),origin:r,isResizable:t}},nl=function(n,e,t){return{parent:h(e),view:h(n),origin:h(Qu(0,0)),isResizable:t}},el={only:Qa,detached:Za,body:nl},tl=su.generate([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),rl=function(n,e,t){var r=t.substring(0,t.length-n.length),o=parseFloat(r);return r===o.toString()?e(o):tl.invalid(t)},ol=function(n){return xn(n,"%")?rl("%",tl.percent,n):xn(n,"px")?rl("px",tl.pixels,n):tl.invalid(n)},ul=on(on({},tl),{from:ol}),il=function(n,e){return L(n,(function(n){var t=ul.from(n);return t.fold((function(){return n}),(function(n){var t=n/e*100;return t+"%"}),(function(n){return n+"%"}))}))},cl=function(n,e,t){var r=t/e;return L(n,(function(n){var e=ul.from(n);return e.fold((function(){return n}),(function(n){return n*r+"px"}),(function(n){return n/100*t+"px"}))}))},al=function(n,e){var t=n.fold((function(){return h("")}),(function(n){var t=n/e;return h(t+"px")}),(function(){var n=100/e;return h(n+"%")}));return j(e,t)},ll=function(n,e,t){return n.fold((function(){return e}),(function(n){return cl(e,t,n)}),(function(n){return il(e,t)}))},fl=function(n,e,t){var r=ul.from(t),o=G(n,(function(n){return"0px"===n}))?al(r,n.length):ll(r,n,e);return gl(o)},sl=function(n,e){return 0===n.length?e:U(n,(function(n,e){return ul.from(e).fold(h(0),b,b)+n}),0)},ml=function(n,e){var t=Math.floor(n);return{value:t+e,remainder:n-t}},dl=function(n,e){return ul.from(n).fold(h(n),(function(n){return n+e+"px"}),(function(n){return n+e+"%"}))},gl=function(n){if(0===n.length)return n;var e=U(n,(function(n,e){var t=ul.from(e).fold((function(){return{value:e,remainder:0}}),(function(n){return ml(n,"px")}),(function(n){return{value:n+"%",remainder:0}}));return{output:[t.value].concat(n.output),remainder:n.remainder+t.remainder}}),{output:[],remainder:0}),t=e.output;return t.slice(0,t.length-1).concat([dl(t[t.length-1],Math.round(e.remainder))])},pl=ul.from,vl=function(n,e,t){_(e,(function(e){var r=n.slice(e.column,e.colspan+e.column),o=sl(r,xr());mr(e.element,"width",o+t)}))},hl=function(n,e,t){_(e,(function(e,r){var o=sl([n[r]],xr());mr(e.element,"width",o+t)}))},bl=function(n,e,t,r){_(t,(function(e){var t=n.slice(e.row,e.rowspan+e.row),o=sl(t,Rr());mr(e.element,"height",o+r)})),_(e,(function(e,t){mr(e.element,"height",n[t])}))},wl=function(n){return pl(n).fold(h("px"),h("px"),h("%"))},yl=function(n,e,t){var r=lo.fromTable(n),o=r.all,u=lo.justCells(r),i=lo.justColumns(r);e.each((function(e){var t=wl(e),o=Lu(n),c=rc(r,n),a=fl(c,o,e);lo.hasColumns(r)?hl(a,i,t):vl(a,u,t),mr(n,"width",e)})),t.each((function(e){var t=wl(e),i=Xu(n),c=ac(r,n,gi),a=fl(c,i,e);bl(a,o,u,t),mr(n,"height",e)}))},Cl=Vi,Tl=qi,Sl=Ki,xl=function(n){var e=lo.fromTable(n);return e.grid},Rl=function(n){var e=[],t=function(n){if(void 0===n)throw new Error("Event bind error: undefined handler");e.push(n)},r=function(n){e=F(e,(function(e){return e!==n}))},o=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var o={};_(n,(function(n,e){o[n]=t[e]})),_(e,(function(n){n(o)}))};return{bind:t,unbind:r,trigger:o}},Al=function(n){var e=Me(n,(function(n){return{bind:n.bind,unbind:n.unbind}})),t=Me(n,(function(n){return n.trigger}));return{registry:e,trigger:t}},Dl=function(n,e){var t=null,r=function(){c(t)||(clearTimeout(t),t=null)},o=function(){for(var o=[],u=0;u<arguments.length;u++)o[u]=arguments[u];r(),t=setTimeout((function(){t=null,n.apply(null,o)}),e)};return{cancel:r,throttle:o}},Ol=function(n){return n.slice(0).sort()},El=function(n,e){throw new Error("All required keys ("+Ol(n).join(", ")+") were not specified. Specified keys were: "+Ol(e).join(", ")+".")},kl=function(n){throw new Error("Unsupported keys for object: "+Ol(n).join(", "))},Ml=function(n,e){if(!i(e))throw new Error("The "+n+" fields must be an array. Was: "+e+".");_(e,(function(e){if(!o(e))throw new Error("The value "+e+" in the "+n+" fields was not a string.")}))},Il=function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+Ol(n).join(", ")+") were not.")},Nl=function(n){var e=Ol(n),t=V(e,(function(n,t){return t<e.length-1&&n===e[t+1]}));t.each((function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+e.join(", ")+"].")}))},Bl=function(n,e){return Pl(n,e,{validate:m,label:"function"})},Pl=function(n,e,t){if(0===e.length)throw new Error("You must specify at least one required field.");return Ml("required",e),Nl(e),function(r){var o=Oe(r),u=G(e,(function(n){return B(o,n)}));u||El(e,o),n(e,o);var i=F(e,(function(n){return!t.validate(r[n],n)}));return i.length>0&&Il(i,t.label),r}},jl=function(n,e){var t=F(e,(function(e){return!B(n,e)}));t.length>0&&kl(t)},Ll=function(n){return Bl(jl,n)},_l=Ll(["compare","extract","mutate","sink"]),zl=Ll(["element","start","stop","destroy"]),Wl=Ll(["forceDrop","drop","move","delayDrop"]),Fl=function(){var n=E.none(),e=function(){n=E.none()},t=function(e,t){var r=n.map((function(n){return e.compare(n,t)}));return n=E.some(t),r},r=function(n,e){var r=e.extract(n);r.each((function(n){var r=t(e,n);r.each((function(n){o.trigger.move(n)}))}))},o=Al({move:Rl(["info"])});return{onEvent:r,reset:e,events:o.registry}},Ul=function(){var n=Al({move:Rl(["info"])});return{onEvent:g,reset:g,events:n.registry}},Hl=function(){var n=Ul(),e=Fl(),t=n,r=function(){t.reset(),t=e},o=function(){t.reset(),t=n},u=function(n,e){t.onEvent(n,e)},i=function(){return t===e};return{on:r,off:o,isOn:i,onEvent:u,events:e.events}},$l=function(n,e,t){var r=!1,o=Al({start:Rl([]),stop:Rl([])}),u=Hl(),i=function(){d.stop(),u.isOn()&&(u.off(),o.trigger.stop())},c=Dl(i,200),a=function(n){d.start(n),u.on(),o.trigger.start()},l=function(n){c.cancel(),u.onEvent(n,e)};u.events.move.bind((function(t){e.mutate(n,t.info)}));var f=function(){r=!0},s=function(){r=!1},m=function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];r&&n.apply(null,e)}},d=e.sink(Wl({forceDrop:i,drop:m(i),move:m(l),delayDrop:m(c.throttle)}),t),g=function(){d.destroy()};return{element:d.element,go:a,on:f,off:s,destroy:g,events:o.registry}},Vl=function(n,e,t,r,o,u,i){return{target:n,x:e,y:t,stop:r,prevent:o,kill:u,raw:i}},ql=function(n){var e=be.fromDom(Et(n).getOr(n.target)),t=function(){return n.stopPropagation()},r=function(){return n.preventDefault()},o=p(r,t);return Vl(e,n.clientX,n.clientY,t,r,o,n)},Kl=function(n,e){return function(t){n(t)&&e(ql(t))}},Xl=function(n,e,t,r,o){var u=Kl(t,r);return n.dom.addEventListener(e,u,o),{unbind:y(Yl,n,e,u,o)}},Gl=function(n,e,t,r){return Xl(n,e,t,r,!1)},Yl=function(n,e,t,r){n.dom.removeEventListener(e,t,r)},Jl=x,Ql=function(n,e,t){return Gl(n,e,Jl,t)},Zl=ql,nf=function(n,e){var t=Zt(n,e);return void 0===t||""===t?[]:t.split(" ")},ef=function(n,e,t){var r=nf(n,e),o=r.concat([t]);return Yt(n,e,o.join(" ")),!0},tf=function(n,e,t){var r=F(nf(n,e),(function(n){return n!==t}));return r.length>0?Yt(n,e,r.join(" ")):er(n,e),!1},rf=function(n){return void 0!==n.dom.classList},of=function(n){return nf(n,"class")},uf=function(n,e){return ef(n,"class",e)},cf=function(n,e){return tf(n,"class",e)},af=function(n,e){rf(n)?n.dom.classList.add(e):uf(n,e)},lf=function(n){var e=rf(n)?n.dom.classList:of(n);0===e.length&&er(n,"class")},ff=function(n,e){if(rf(n)){var t=n.dom.classList;t.remove(e)}else cf(n,e);lf(n)},sf=function(n,e){return rf(n)&&n.dom.classList.contains(e)},mf=function(n){var e=n.replace(/\./g,"-"),t=function(n){return e+"-"+n};return{resolve:t}},df=mf("ephox-dragster"),gf=df.resolve,pf=function(n){var e=on({layerClass:gf("blocker")},n),t=be.fromTag("div");Yt(t,"role","presentation"),dr(t,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),af(t,gf("blocker")),af(t,e.layerClass);var r=h(t),o=function(){Po(t)};return{element:r,destroy:o}},vf=function(n,e){return Qu(e.left-n.left,e.top-n.top)},hf=function(n){return E.some(Qu(n.x,n.y))},bf=function(n,e){n.mutate(e.left,e.top)},wf=function(n,e){var t=pf(e),r=Ql(t.element(),"mousedown",n.forceDrop),o=Ql(t.element(),"mouseup",n.drop),u=Ql(t.element(),"mousemove",n.move),i=Ql(t.element(),"mouseout",n.delayDrop),c=function(){t.destroy(),o.unbind(),u.unbind(),i.unbind(),r.unbind()},a=function(n){Oo(n,t.element())},l=function(){Po(t.element())};return zl({element:t.element,start:a,stop:l,destroy:c})},yf=_l({compare:vf,extract:hf,sink:wf,mutate:bf}),Cf=function(n,e){void 0===e&&(e={});var t=void 0!==e.mode?e.mode:yf;return $l(n,t,e)},Tf=function(n){return Xt(n,"[contenteditable]")},Sf=function(n,e){return void 0===e&&(e=!1),!ue().browser.isIE()&&Mt(n)?n.dom.isContentEditable:Tf(n).fold(h(e),(function(n){return"true"===xf(n)}))},xf=function(n){return n.dom.contentEditable},Rf=mf("ephox-snooker"),Af=Rf.resolve,Df=function(){var n=Al({drag:Rl(["xDelta","yDelta"])}),e=function(e,t){n.trigger.drag(e,t)};return{mutate:e,events:n.registry}},Of=function(){var n=Al({drag:Rl(["xDelta","yDelta","target"])}),e=E.none(),t=Df();t.events.drag.bind((function(t){e.each((function(e){n.trigger.drag(t.xDelta,t.yDelta,e)}))}));var r=function(n){e=E.some(n)},o=function(){return e};return{assign:r,get:o,mutate:t.mutate,events:n.registry}},Ef=function(n,e,t,r,o){var u=be.fromTag("div");return dr(u,{position:"absolute",left:e-r/2+"px",top:t+"px",height:o+"px",width:r+"px"}),Jt(u,{"data-column":n,role:"presentation"}),u},kf=function(n,e,t,r,o){var u=be.fromTag("div");return dr(u,{position:"absolute",left:e+"px",top:t-o/2+"px",height:o+"px",width:r+"px"}),Jt(u,{"data-row":n,role:"presentation"}),u},Mf=Af("resizer-bar"),If=Af("resizer-rows"),Nf=Af("resizer-cols"),Bf=7,Pf=function(n,e){return X(n.all,(function(n,t){return e(n.element)?[t]:[]}))},jf=function(n,e){var t=[];return j(n.grid.columns,(function(r){var o=lo.getColumnAt(n,r).map((function(n){return n.element}));o.forall(e)&&t.push(r)})),F(t,(function(t){var r=lo.filterItems(n,(function(n){return n.column===t}));return G(r,(function(n){return e(n.element)}))}))},Lf=function(n){var e=zt(n.parent(),"."+Mf);_(e,Po)},_f=function(n,e,t){var r=n.origin();_(e,(function(e){e.each((function(e){var o=t(r,e);af(o,Mf),Oo(n.parent(),o)}))}))},zf=function(n,e,t,r){_f(n,e,(function(n,e){var o=Ef(e.col,e.x-n.left,t.top-n.top,Bf,r);return af(o,Nf),o}))},Wf=function(n,e,t,r){_f(n,e,(function(n,e){var o=kf(e.row,t.left-n.left,e.y-n.top,r,Bf);return af(o,If),o}))},Ff=function(n,e,t,r,o){var u=ei(t),i=e.isResizable,c=r.length>0?gi.positions(r,t):[],a=c.length>0?Pf(n,i):[],l=F(c,(function(n,e){return P(a,(function(n){return e===n}))}));Wf(e,l,u,_u(t));var f=o.length>0?bi.positions(o,t):[],s=f.length>0?jf(n,i):[],m=F(f,(function(n,e){return P(s,(function(n){return e===n}))}));zf(e,m,u,Gu(t))},Uf=function(n,e){if(Lf(n),n.isResizable(e)){var t=lo.fromTable(e),r=Hu(t),o=Fu(t);Ff(t,n,e,r,o)}},Hf=function(n,e){var t=zt(n.parent(),"."+Mf);_(t,e)},$f=function(n){Hf(n,(function(n){mr(n,"display","none")}))},Vf=function(n){Hf(n,(function(n){mr(n,"display","block")}))},qf=function(n){return sf(n,If)},Kf=function(n){return sf(n,Nf)},Xf=Af("resizer-bar-dragging"),Gf=function(n){var e=Of(),t=Cf(e,{}),r=E.none(),o=function(n,e){return E.from(Zt(n,e))};e.events.drag.bind((function(n){o(n.target,"data-row").each((function(e){var t=Sr(n.target,"top");mr(n.target,"top",t+n.yDelta+"px")})),o(n.target,"data-column").each((function(e){var t=Sr(n.target,"left");mr(n.target,"left",t+n.xDelta+"px")}))}));var u=function(n,e){var t=Sr(n,e),r=wr(n,"data-initial-"+e,0);return t-r};t.events.stop.bind((function(){e.get().each((function(e){r.each((function(t){o(e,"data-row").each((function(n){var r=u(e,"top");er(e,"data-initial-top"),d.trigger.adjustHeight(t,r,parseInt(n,10))})),o(e,"data-column").each((function(n){var r=u(e,"left");er(e,"data-initial-left"),d.trigger.adjustWidth(t,r,parseInt(n,10))})),Uf(n,t)}))}))}));var i=function(r,o){d.trigger.startAdjust(),e.assign(r),Yt(r,"data-initial-"+o,Sr(r,o)),af(r,Xf),mr(r,"opacity","0.2"),t.go(n.parent())},c=Ql(n.parent(),"mousedown",(function(n){qf(n.target)&&i(n.target,"top"),Kf(n.target)&&i(n.target,"left")})),a=function(e){return Se(e,n.view())},l=function(n){return Xt(n,"table",a).filter(Sf)},f=Ql(n.view(),"mouseover",(function(e){l(e.target).fold((function(){Mt(e.target)&&Lf(n)}),(function(e){r=E.some(e),Uf(n,e)}))})),s=function(){c.unbind(),f.unbind(),t.destroy(),Lf(n)},m=function(e){Uf(n,e)},d=Al({adjustHeight:Rl(["table","delta","row"]),adjustWidth:Rl(["table","delta","column"]),startAdjust:Rl([])});return{destroy:s,refresh:m,on:t.on,off:t.off,hideBars:y($f,n),showBars:y(Vf,n),events:d.registry}},Yf=function(n,e,t){var r=gi,o=bi,u=Gf(n),i=Al({beforeResize:Rl(["table","type"]),afterResize:Rl(["table","type"]),startDrag:Rl([])});return u.events.adjustHeight.bind((function(n){var e=n.table;i.trigger.beforeResize(e,"row");var t=r.delta(n.delta,e);Ua(e,t,n.row,r),i.trigger.afterResize(e,"row")})),u.events.startAdjust.bind((function(n){i.trigger.startDrag()})),u.events.adjustWidth.bind((function(n){var r=n.table;i.trigger.beforeResize(r,"col");var u=o.delta(n.delta,r),c=t(r);Fa(r,u,n.column,e,c),i.trigger.afterResize(r,"col")})),{on:u.on,off:u.off,hideBars:u.hideBars,showBars:u.showBars,destroy:u.destroy,events:i.registry}},Jf={create:Yf},Qf=function(n,e){return n.fire("newrow",{node:e})},Zf=function(n,e){return n.fire("newcell",{node:e})},ns=function(n,e,t,r,o){n.fire("ObjectResizeStart",{target:e,width:t,height:r,origin:o})},es=function(n,e,t,r,o){n.fire("ObjectResized",{target:e,width:t,height:r,origin:o})},ts=function(n,e,t,r,o){n.fire("TableSelectionChange",{cells:e,start:t,finish:r,otherCells:o})},rs=function(n){n.fire("TableSelectionClear")},os=function(n,e,t){n.fire("TableModified",on(on({},t),{table:e}))},us={structure:!1,style:!0},is={structure:!0,style:!1},cs={structure:!0,style:!0},as="tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol",ls={"border-collapse":"collapse",width:"100%"},fs=j(5,(function(n){var e=n+1+"px";return{title:e,value:e}})),ss=L(["Solid","Dotted","Dashed","Double","Groove","Ridge","Inset","Outset","None","Hidden"],(function(n){return{title:n,value:n.toLowerCase()}})),ms=function(n){var e;if(Ns(n)){var t=n.dom,r=null!==(e=t.getParent(n.selection.getStart(),t.isBlock))&&void 0!==e?e:n.getBody(),o=zu(be.fromDom(r));return on(on({},ls),{width:o+"px"})}return Bs(n)?Pe(ls,(function(n,e){return"width"!==e})):ls},ds={border:"1"},gs="preservetable",ps=function(n){return n.getParam("table_sizing_mode","auto")},vs=function(n){return n.getParam("table_responsive_width")},hs=function(n){return n.getParam("table_border_widths",fs,"array")},bs=function(n){return n.getParam("table_border_styles",ss,"array")},ws=function(n){return n.getParam("table_default_attributes",ds,"object")},ys=function(n){return n.getParam("table_default_styles",ms(n),"object")},Cs=function(n){return n.getParam("table_resize_bars",!0,"boolean")},Ts=function(n){return n.getParam("table_tab_navigation",!0,"boolean")},Ss=function(n){return n.getParam("table_cell_advtab",!0,"boolean")},xs=function(n){return n.getParam("table_row_advtab",!0,"boolean")},Rs=function(n){return n.getParam("table_advtab",!0,"boolean")},As=function(n){return n.getParam("table_appearance_options",!0,"boolean")},Ds=function(n){return n.getParam("table_grid",!0,"boolean")},Os=function(n){return n.getParam("table_style_by_css",!1,"boolean")},Es=function(n){return n.getParam("table_cell_class_list",[],"array")},ks=function(n){return n.getParam("table_row_class_list",[],"array")},Ms=function(n){return n.getParam("table_class_list",[],"array")},Is=function(n){return"relative"===ps(n)||!0===vs(n)},Ns=function(n){return"fixed"===ps(n)||!1===vs(n)},Bs=function(n){return"responsive"===ps(n)},Ps=function(n){return n.getParam("table_toolbar",as)},js=function(n){return n.getParam("table_use_colgroups",!1,"boolean")},Ls=function(n){var e="section",t=n.getParam("table_header_type",e,"string"),r=["section","cells","sectionCells","auto"];return B(r,t)?t:e},_s=function(n){var e=["preservetable","resizetable"],t=n.getParam("table_column_resizing",gs,"string");return V(e,(function(n){return n===t})).getOr(gs)},zs=function(n){return"preservetable"===_s(n)},Ws=function(n){return"resizetable"===_s(n)},Fs=function(n){var e=n.getParam("table_clone_elements");return o(e)?E.some(e.split(/[ ,]/)):Array.isArray(e)?E.some(e):E.none()},Us=function(n){var e=n.getParam("object_resizing",!0);return o(e)?"table"===e:e},Hs=function(n){return n.getParam("table_background_color_map",[],"array")},$s=function(n){return n.getParam("table_border_color_map",[],"array")},Vs=function(n,e){return Is(n)?pc.percentageSize(e):Ns(n)?pc.pixelSize(e):pc.getTableSize(e)},qs=function(n){er(n,"width")},Ks=function(n){var e=$i(n);yl(n,E.some(e),E.none()),qs(n)},Xs=function(n){var e=Hi(n);yl(n,E.some(e),E.none()),qs(n)},Gs=function(n){hr(n,"width");var e=Mr(n),t=e.length>0?e:kr(n);_(t,(function(n){hr(n,"width"),qs(n)})),qs(n)},Ys=Ks,Js=Xs,Qs=Gs,Zs=function(n){var e=lo.fromTable(n);lo.hasColumns(e)||_(kr(n),(function(n){var e=gr(n,"width");mr(n,"width",e),er(n,"width")}))},nm=function(){var n=be.fromTag("div");return dr(n,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Oo(It(),n),n},em=function(n,e){return n.inline?el.body(Vc(n),nm(),e):el.only(be.fromDom(n.getDoc()),e)},tm=function(n,e){n.inline&&Po(e.parent())},rm="bar-",om=function(n){return"false"!==Zt(n,"data-mce-resize")},um=function(n){var e,t,r=E.none(),o=E.none(),u=E.none(),i=function(n){return"TABLE"===n.nodeName},c=function(){return o},a=function(){return u.getOr(el.only(be.fromDom(n.getBody()),om))},l=function(e){return Vs(n,e)},f=function(){return zs(n)?Ja():Ya()},s=function(n){return xl(n).columns},m=function(r,o,u){var i=xn(o,"e");if(""===t&&Ys(r),u!==e&&""!==t){mr(r,"width",t);var c=f(),a=l(r),m=zs(n)||i?s(r)-1:0;Fa(r,u-e,m,c,a)}else if(Zc(t)){var d=parseFloat(t.replace("%","")),g=u*d/e;mr(r,"width",g+"%")}na(t)&&Zs(r)},d=function(){o.each((function(n){n.destroy()})),u.each((function(e){tm(n,e)}))};return n.on("init",(function(){var e=em(n,om);if(u=E.some(e),Us(n)&&Cs(n)){var t=f(),i=Jf.create(e,t,l);i.on(),i.events.startDrag.bind((function(e){r=E.some(n.selection.getRng())})),i.events.beforeResize.bind((function(e){var t=e.table.dom;ns(n,t,qc(t),Kc(t),rm+e.type)})),i.events.afterResize.bind((function(e){var t=e.table,o=t.dom;Jc(t),r.each((function(e){n.selection.setRng(e),n.focus()})),es(n,o,qc(o),Kc(o),rm+e.type),n.undoManager.add()})),o=E.some(i)}})),n.on("ObjectResizeStart",(function(r){var o=r.target;if(i(o)){var u=be.fromDom(o);_(n.dom.select(".mce-clonedresizable"),(function(e){n.dom.addClass(e,"mce-"+_s(n)+"-columns")})),!Tl(u)&&Ns(n)?Js(u):!Cl(u)&&Is(n)&&Ys(u),Sl(u)&&Sn(r.origin,rm)&&Ys(u),e=r.width,t=Bs(n)?"":Qc(n,o).getOr("")}})),n.on("ObjectResized",(function(e){var t=e.target;if(i(t)){var r=be.fromDom(t),o=e.origin;Sn(o,"corner-")&&m(r,o,e.width),Jc(r),os(n,r.dom,us)}})),n.on("SwitchMode",(function(){c().each((function(e){n.mode.isReadOnly()?e.hideBars():e.showBars()}))})),{lazyResize:c,lazyWire:a,destroy:d}},im=function(n,e){return{element:n,offset:e}},cm=function(n,e,t){return n.property().isText(e)&&0===n.property().getText(e).trim().length||n.property().isComment(e)?t(e).bind((function(e){return cm(n,e,t).orThunk((function(){return E.some(e)}))})):E.none()},am=function(n,e){if(n.property().isText(e))return n.property().getText(e).length;var t=n.property().children(e);return t.length},lm=function(n,e){var t=cm(n,e,n.query().prevSibling).getOr(e);if(n.property().isText(t))return im(t,am(n,t));var r=n.property().children(t);return r.length>0?lm(n,r[r.length-1]):im(t,am(n,t))},fm=lm,sm=Ho(),mm=function(n){return fm(sm,n)},dm=function(n,e){var t=yr(n,"colspan");if(1===t){var r=Fi(n);r.each((function(t){var r=t.value/2;Ui(n,r,t.unit),Ui(e,r,t.unit)}))}},gm=st("th"),pm=function(n){return G(n,(function(n){return gm(n.element)}))},vm=function(n,e){return n&&e?"sectionCells":n?"section":"cells"},hm=function(n){var e="thead"===n.section,t=rr(bm(n.cells),"th");return e||t?{type:"header",subType:vm(e,t)}:"tfoot"===n.section?{type:"footer"}:{type:"body"}},bm=function(n){var e=F(n,(function(n){return gm(n.element)}));return 0===e.length?E.some("td"):e.length===n.length?E.some("th"):E.none()},wm=function(n){var e=L(n,(function(n){return hm(n).type})),t=B(e,"header"),r=B(e,"footer");if(t||r){var o=B(e,"body");return!t||o||r?t||o||!r?E.none():E.some("footer"):E.some("header")}return E.some("body")},ym=function(n){return rn(n.all,(function(n){var e=hm(n);return"header"===e.type?E.from(e.subType):E.none()}))},Cm=function(n,e,t){return Qe(t(n.element,e),!0,n.isLocked)},Tm=function(n,e){return n.section!==e?Ze(n.element,n.cells,e,n.isNew):n},Sm=function(){return{transformRow:Tm,transformCell:function(n,e,t){var r=t(n.element,e),o="td"!==rt(r)?xu(r,"td"):r;return Qe(o,n.isNew,n.isLocked)}}},xm=function(){return{transformRow:Tm,transformCell:Cm}},Rm=function(){return{transformRow:function(n,e){var t="thead"===e?"tbody":e;return Tm(n,t)},transformCell:Cm}},Am=function(){return{transformRow:b,transformCell:Cm}},Dm=function(n,e){var t=lo.fromTable(n),r=ym(t).getOr(e);switch(r){case"section":return Sm();case"sectionCells":return xm();case"cells":return Rm()}},Om={getTableSectionType:Dm,section:Sm,sectionCells:xm,cells:Rm,fallback:Am},Em=function(n,e,t,r){t===r?er(n,e):Yt(n,e,t)},km=function(n,e,t){tn(_t(n,e)).fold((function(){return Do(n,t)}),(function(n){return Ao(n,t)}))},Mm=function(n,e){var t=qt(n,e).getOrThunk((function(){var t=be.fromTag(e,mt(n).dom);return"thead"===e?km(n,"caption,colgroup",t):"colgroup"===e?km(n,"caption",t):Oo(n,t),t}));return Bo(t),t},Im=function(n,e){var t=[],r=[],o=function(n){return L(n,(function(n){n.isNew&&t.push(n.element);var e=n.element;return Bo(e),_(n.cells,(function(n){n.isNew&&r.push(n.element),Em(n.element,"colspan",n.colspan,1),Em(n.element,"rowspan",n.rowspan,1),Oo(e,n.element)})),e}))},u=function(n){return X(n,(function(n){return L(n.cells,(function(n){return Em(n.element,"span",n.colspan,1),n.element}))}))},i=function(e,t){var r=Mm(n,t),i="colgroup"===t?u:o,c=i(e);No(r,c)},c=function(e){qt(n,e).each(Po)},a=function(n,e){n.length>0?i(n,e):c(e)},l=[],f=[],s=[],m=[];return _(e,(function(n){switch(n.section){case"thead":l.push(n);break;case"tbody":f.push(n);break;case"tfoot":s.push(n);break;case"colgroup":m.push(n);break}})),a(m,"colgroup"),a(l,"thead"),a(f,"tbody"),a(s,"tfoot"),{newRows:t,newCells:r}},Nm=function(n){return L(n,(function(n){var e=yu(n.element);return _(n.cells,(function(n){var t=Cu(n.element);Em(t,"colspan",n.colspan,1),Em(t,"rowspan",n.rowspan,1),Oo(e,t)})),e}))},Bm=function(n,e){return L(n,(function(n){return $r(n,e)}))},Pm=function(n,e){return n[e]},jm=function(n,e){if(0===n.length)return 0;var t=n[0],r=q(n,(function(n){return!e(t.element,n.element)}));return r.getOr(n.length)},Lm=function(n,e,t,r){var o=Pm(n,e),u="colgroup"===o.section,i=jm(o.cells.slice(t),r),c=u?1:jm(Bm(n.slice(e),t),r);return{colspan:i,rowspan:c}},_m=function(n,e){var t=L(n,(function(n){return L(n.cells,S)})),r=function(n,e,r,o){for(var u=n;u<n+r;u++)for(var i=e;i<e+o;i++)t[u][i]=!0};return L(n,(function(o,u){var i=X(o.cells,(function(o,i){if(!1===t[u][i]){var c=Lm(n,u,i,e);return r(u,i,c.rowspan,c.colspan),[Xe(o.element,c.rowspan,c.colspan,o.isNew)]}return[]}));return Je(o.element,i,o.section,o.isNew)}))},zm=function(n,e,t){var r=[];_(n.colgroups,(function(o){for(var u=[],i=0;i<n.grid.columns;i++){var c=lo.getColumnAt(n,i).map((function(n){return Qe(n.element,t,!1)})).getOrThunk((function(){return Qe(e.colGap(),!0,!1)}));u.push(c)}r.push(Ze(o.element,u,"colgroup",t))}));for(var o=0;o<n.grid.rows;o++){for(var u=[],i=0;i<n.grid.columns;i++){var c=lo.getAt(n,o,i).map((function(n){return Qe(n.element,t,n.isLocked)})).getOrThunk((function(){return Qe(e.gap(),!0,!1)}));u.push(c)}var a=n.all[o],l=Ze(a.element,u,a.section,t);r.push(l)}return r},Wm=function(n,e){return zm(n,e,!1)},Fm=function(n){return _m(n,Se)},Um=function(n,e){return rn(n.all,(function(n){return V(n.cells,(function(n){return Se(e,n.element)}))}))},Hm=function(n,e,t){var r=L(e.selection,(function(e){return Er(e).bind((function(e){return Um(n,e)})).filter(t)})),o=or(r);return ar(o.length>0,o)},$m=function(n,e,t,r,o){return function(u,i,c,a,l){var f=lo.fromTable(i),s=E.from(null===l||void 0===l?void 0:l.section).getOrThunk(Om.fallback),m=e(f,c).map((function(e){var t=Wm(f,a),r=n(t,e,Se,o(a),s),u=Jr(r.grid),i=Fm(r.grid);return{info:e,grid:i,cursor:r.cursor,lockedColumns:u}}));return m.bind((function(n){var e=Im(i,n.grid),o=E.from(null===l||void 0===l?void 0:l.sizing).getOrThunk((function(){return pc.getTableSize(i)})),c=E.from(null===l||void 0===l?void 0:l.resize).getOrThunk(Ja);return t(i,n.grid,n.info,{sizing:o,resize:c,section:s}),r(i),Uf(u,i),er(i,Gr),n.lockedColumns.length>0&&Yt(i,Gr,n.lockedColumns.join(",")),E.some({cursor:n.cursor,newRows:e.newRows,newCells:e.newCells})}))}},Vm=function(n,e){return Er(e.element).bind((function(t){return Um(n,t).map((function(n){var t=on(on({},n),{generators:e.generators,clipboard:e.clipboard});return t}))}))},qm=function(n,e){return Hm(n,e,x).map((function(n){return{cells:n,generators:e.generators,clipboard:e.clipboard}}))},Km=function(n,e){return e.mergable},Xm=function(n,e){return e.unmergable},Gm=function(n,e){return Hm(n,e,x)},Ym=function(n,e){return Hm(n,e,(function(n){return!n.isLocked}))},Jm=function(n,e){return Um(n,e).exists((function(n){return!n.isLocked}))},Qm=function(n,e){return G(e,(function(e){return Jm(n,e)}))},Zm=function(n,e){return Km(n,e).filter((function(e){return Qm(n,e.cells)}))},nd=function(n,e){return Xm(n,e).filter((function(e){return Qm(n,e)}))},ed=function(n,e,t,r){var o=Kr(n).rows;if(0===o.length)return n;for(var u=e.startRow;u<=e.finishRow;u++)for(var i=e.startCol;i<=e.finishCol;i++){var c=o[u],a=$r(c,i).isLocked;Fr(c,i,Qe(r(),!1,a))}return n},td=function(n,e,t,r){for(var o=Kr(n).rows,u=!0,i=0;i<o.length;i++)for(var c=0;c<qr(o[0]);c++){var a=o[i],l=$r(a,c),f=l.element,s=t(f,e);!0===s&&!1===u?Fr(a,c,Qe(r(),!0,l.isLocked)):!0===s&&(u=!1)}return n},rd=function(n,e){return H(n,(function(n,t){return P(n,(function(n){return e(n.element,t.element)}))?n:n.concat([t])}),[])},od=function(n,e,t,r){return e>0&&e<n[0].cells.length&&_(n,(function(n){var o=n.cells[e-1],u=n.cells[e],i=t(u.element,o.element);i&&Fr(n,e,Qe(r(),!0,u.isLocked))})),n},ud=function(n,e,t,r){var o=Kr(n).rows;if(e>0&&e<o.length){var u=o[e-1].cells,i=rd(u,t);_(i,(function(n){for(var u=E.none(),i=e;i<o.length;i++)for(var c=function(e){var c=o[i],a=$r(c,e),l=t(a.element,n.element);l&&(u.isNone()&&(u=E.some(r())),u.each((function(n){Fr(c,e,Qe(n,!0,a.isLocked))})))},a=0;a<qr(o[0]);a++)c(a)}))}return n},id=function(n){var e=function(e){return id(n)},t=function(e){return id(n)},r=function(e){return id(e(n))},o=function(e){return id(n)},u=function(e){e(n)},i=function(e){return e(n)},c=function(e,t){return t(n)},a=function(e){return e(n)},l=function(e){return e(n)},f=function(){return E.some(n)};return{isValue:x,isError:S,getOr:h(n),getOrThunk:h(n),getOrDie:h(n),or:e,orThunk:t,fold:c,map:r,mapError:o,each:u,bind:i,exists:a,forall:l,toOptional:f}},cd=function(n){var e=function(n){return n()},t=function(){return T(String(n))()},r=b,o=function(n){return n()},u=function(e){return cd(n)},i=function(e){return cd(e(n))},c=function(e){return cd(n)},a=function(e,t){return e(n)};return{isValue:S,isError:x,getOr:b,getOrThunk:e,getOrDie:t,or:r,orThunk:o,fold:a,map:u,mapError:i,each:g,bind:c,exists:S,forall:x,toOptional:E.none}},ad=function(n,e){return n.fold((function(){return cd(e)}),id)},ld={value:id,error:cd,fromOption:ad},fd=function(n,e,t){if(n.row>=e.length||n.column>qr(e[0]))return ld.error("invalid start address out of table bounds, row: "+n.row+", column: "+n.column);var r=e.slice(n.row),o=r[0].cells.slice(n.column),u=qr(t[0]),i=t.length;return ld.value({rowDelta:r.length-i,colDelta:o.length-u})},sd=function(n,e){var t=qr(n[0]),r=qr(e[0]);return{rowDelta:0,colDelta:t-r}},md=function(n,e){var t=n.length,r=e.length;return{rowDelta:t-r,colDelta:0}},dd=function(n,e,t,r){var o="colgroup"===e.section?t.col:t.cell;return j(n,(function(n){return Qe(o(),!0,r(n))}))},gd=function(n,e,t,r){var o=n[n.length-1];return n.concat(j(e,(function(){var n="colgroup"===o.section?t.colgroup:t.row,e=Xr(o,n,b),u=dd(e.cells.length,e,t,(function(n){return We(r,n.toString())}));return Ur(e,u)})))},pd=function(n,e,t,r){return L(n,(function(n){var o=dd(e,n,t,S);return zr(n,r,o)}))},vd=function(n,e,t){return L(n,(function(n){return H(t,(function(t,r){var o=dd(1,n,e,x)[0];return Wr(t,r,o)}),n)}))},hd=function(n,e,t){var r=e.colDelta<0?pd:b,o=e.rowDelta<0?gd:b,u=Jr(n),i=qr(n[0]),c=P(u,(function(n){return n===i-1})),a=r(n,Math.abs(e.colDelta),t,c?i-1:i),l=Jr(a);return o(a,Math.abs(e.rowDelta),t,J(l,x))},bd=function(n,e,t,r){var o=$r(n[e],t),u=y(r,o.element),i=n[e];return n.length>1&&qr(i)>1&&(t>0&&u(Vr(i,t-1))||t<i.cells.length-1&&u(Vr(i,t+1))||e>0&&u(Vr(n[e-1],t))||e<n.length-1&&u(Vr(n[e+1],t)))},wd=function(n,e,t,r,o,u){for(var i=n.row,c=n.column,a=t.length,l=qr(t[0]),f=i+a,s=c+l+u.length,m=J(u,x),d=i;d<f;d++)for(var g=0,p=c;p<s;p++)if(m[p])g++;else{bd(e,d,p,o)&&td(e,Vr(e[d],p),o,r.cell);var v=p-c-g,h=$r(t[d-i],v),b=h.element,w=r.replace(b);Fr(e[d],p,Qe(w,!0,h.isLocked))}return e},yd=function(n,e,t){var r=qr(e[0]),o=Kr(e).cols.length+n.row,u=j(r-n.column,(function(e){return e+n.column})),i=V(u,(function(n){return G(t,(function(e){return e!==n}))})).getOr(r-1);return{row:o,column:i}},Cd=function(n,e,t){return F(t,(function(t){return t>=n.column&&t<=qr(e[0])+n.column}))},Td=function(n,e,t,r,o){var u=Jr(e),i=yd(n,e,u),c=Kr(t).rows,a=Cd(i,c,u),l=fd(i,e,c);return l.map((function(n){var t=on(on({},n),{colDelta:n.colDelta-a.length}),u=hd(e,t,r),l=Jr(u),f=Cd(i,c,l);return wd(i,u,c,r,o,f)}))},Sd=function(n,e,t,r,o){od(e,n,o,r.cell);var u=md(t,e),i=hd(t,u,r),c=md(e,i),a=hd(e,c,r);return L(a,(function(e,t){return zr(e,n,i[t].cells)}))},xd=function(n,e,t,r,o){ud(e,n,o,r.cell);var u=Jr(e),i=sd(e,t),c=on(on({},i),{colDelta:i.colDelta-u.length}),a=hd(e,c,r),l=Kr(a),f=l.cols,s=l.rows,m=Jr(a),d=sd(t,e),g=on(on({},d),{colDelta:d.colDelta+m.length}),p=vd(t,r,m),v=hd(p,g,r);return f.concat(s.slice(0,n)).concat(v).concat(s.slice(n,s.length))},Rd=function(n,e,t,r){return Xr(n,(function(n){return r(n,t)}),e)},Ad=function(n,e,t,r,o){var u=Kr(n),i=u.rows,c=u.cols,a=i.slice(0,e),l=i.slice(e),f=Rd(i[t],(function(n,t){var u=e>0&&e<i.length&&r(Vr(i[e-1],t),Vr(i[e],t)),c=u?$r(i[e],t):Qe(o(n.element,r),!0,n.isLocked);return c}),r,o);return c.concat(a).concat([f]).concat(l)},Dd=function(n,e,t,r,o,u,i){if("colgroup"!==t&&r)return $r(n,e);var c=$r(n,o);return Qe(i(c.element,u),!0,!1)},Od=function(n,e,t,r,o){return L(n,(function(n){var u=e>0&&e<qr(n)&&r(Vr(n,e-1),Vr(n,e)),i=Dd(n,e,n.section,u,t,r,o);return Wr(n,e,i)}))},Ed=function(n,e){return X(n,(function(n){var t=n.cells,r=U(e,(function(n,e){return e>=0&&e<n.length?n.slice(0,e).concat(n.slice(e+1)):n}),t);return r.length>0?[Ze(n.element,r,n.section,n.isNew)]:[]}))},kd=function(n,e,t){var r=Kr(n),o=r.rows,u=r.cols;return u.concat(o.slice(0,e)).concat(o.slice(t+1))},Md=function(n,e,t,r){return void 0!==Vr(n[e],t)&&e>0&&r(Vr(n[e-1],t),Vr(n[e],t))},Id=function(n,e,t){return e>0&&t(Vr(n,e-1),Vr(n,e))},Nd=function(n,e,t,r){return Md(n,e,t,r)||Id(n[e],t,r)},Bd=function(n,e){var t=G(e,b)&&pm(n.cells);return t?x:function(n,t,r){var o=rt(n.element);return!("th"===o&&e[r])}},Pd=function(n,e){var t=G(e,b)&&pm(n);return t?x:function(n,t,r){var o=rt(n.element);return!("th"===o&&e[t])}},jd=function(n,e,t,r){var o=function(n){return"row"===n?Tr(e):Cr(e)},u=function(n){return o(n)?n+"group":n};if(n)return gm(e)?u(t):null;if(r&&gm(e)){var i="row"===t?"col":"row";return u(i)}return null},Ld=function(n,e){return function(t,r,o){return E.some(jd(n,t.element,"col",e[o]))}},_d=function(n,e){return function(t,r){return E.some(jd(n,t.element,"row",e[r]))}},zd=function(n,e,t){return Qe(t(n.element,e),!0,n.isLocked)},Wd=function(n,e,t,r,o,u,i){var c=function(n){return P(e,(function(e){return t(n.element,e.element)}))};return L(n,(function(n,e){return Hr(n,(function(n,a){if(c(n)){var l=i(n,e,a)?o(n,t,r):n;return u(l,e,a).each((function(n){Qt(l.element,{scope:E.from(n)})})),l}return n}))}))},Fd=function(n,e,t){return X(n,(function(r,o){return Nd(n,o,e,t)?[]:[$r(r,e)]}))},Ud=function(n,e,t){var r=n[e];return X(r.cells,(function(r,o){return Nd(n,e,o,t)?[]:[r]}))},Hd=function(n,e,t,r,o){var u=Kr(n).rows,i=X(e,(function(n){return Fd(u,n,r)})),c=L(n,(function(n){return pm(n.cells)})),a=Pd(i,c),l=_d(t,c);return Wd(n,i,r,o,zd,l,a)},$d=function(n,e,t,r,o,u,i){var c=Kr(n),a=c.cols,l=c.rows,f=l[e[0]],s=X(e,(function(n){return Ud(l,n,o)})),m=L(f.cells,(function(n,e){return pm(Fd(l,e,o))})),d=un([],l,!0);_(e,(function(n){d[n]=i.transformRow(l[n],t)}));var g=a.concat(d),p=Bd(f,m),v=Ld(r,m);return Wd(g,s,o,u,i.transformCell,v,p)},Vd=function(n,e,t,r){var o=Kr(n).rows,u=L(e,(function(n){return $r(o[n.row],n.column)}));return Wd(n,u,t,r,zd,E.none,x)},qd=function(n){var e=function(n,e){var t=P(n,(function(n){return n.column===e.column}));return t?n:n.concat([e])};return H(n,e,[]).sort((function(n,e){return n.column-e.column}))},Kd=st("col"),Xd=st("colgroup"),Gd=function(n){return"tr"===rt(n)||Xd(n)},Yd=function(n){var e=wr(n,"colspan",1),t=wr(n,"rowspan",1);return{element:n,colspan:e,rowspan:t}},Jd=function(n,e){void 0===e&&(e=Yd);var t=function(e){return Kd(e.element)?n.col(e):n.cell(e)},r=function(e){return Xd(e.element)?n.colgroup(e):n.row(e)},o=function(n){if(Gd(n))return r({element:n});var o=t(e(n));return u=E.some({item:n,replacement:o}),o},u=E.none(),i=function(n,e){return u.fold((function(){return o(n)}),(function(t){return e(n,t.item)?t.replacement:o(n)}))};return{getOrInit:i}},Qd=function(n){return function(e){var t=[],r=function(n,e){return V(t,(function(t){return e(t.item,n)}))},o=function(r){var o="td"===n?{scope:null}:{},u=e.replace(r,n,o);return t.push({item:r,sub:u}),u},u=function(n,e){return Gd(n)||Kd(n)?n:r(n,e).fold((function(){return o(n)}),(function(t){return e(n,t.item)?t.sub:o(n)}))};return{replaceOrInit:u}}},Zd=function(n){return nr(n,"scope").map((function(n){return n.substr(0,3)}))},ng=function(n){var e=function(e){var t=Zd(e);return t.each((function(n){return Yt(e,"scope",n)})),function(){var r=n.cell({element:e,colspan:1,rowspan:1});return hr(r,"width"),hr(e,"width"),t.each((function(n){return Yt(r,"scope",n)})),r}},t=function(n){var e=function(){var e=or(L(n,Zd));if(0===e.length)return E.none();var t=e[0],r=["row","col"],o=P(e,(function(n){return n!==t&&B(r,n)}));return o?E.none():E.from(t)};return hr(n[0],"width"),e().fold((function(){return er(n[0],"scope")}),(function(e){return Yt(n[0],"scope",e+"group")})),h(n[0])};return{unmerge:e,merge:t}},eg={modification:Jd,transform:Qd,merging:ng},tg=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],rg=function(n,e){var t=n.property().name(e);return B(["ol","ul"],t)},og=function(n,e){var t=n.property().name(e);return B(tg,t)},ug=function(n,e){return B(["br","img","hr","input"],n.property().name(e))},ig=Ho(),cg=function(n){return og(ig,n)},ag=function(n){return rg(ig,n)},lg=function(n){return ug(ig,n)},fg=function(n){var e=function(n){return"br"===rt(n)},t=function(n){return G(n,(function(n){return e(n)||at(n)&&0===zo(n).trim().length}))},r=function(n){return"li"===rt(n)||Ft(n,ag).isSome()},o=function(n){return wt(n).map((function(n){return!!cg(n)||!!lg(n)&&"img"!==rt(n)})).getOr(!1)},u=function(n){return Ec(n).bind((function(t){var u=o(t);return pt(t).map((function(o){return!0===u||r(o)||e(t)||cg(o)&&!Se(n,o)?[]:[be.fromTag("br")]}))})).getOr([])},i=function(){var e=X(n,(function(n){var e=yt(n);return t(e)?[]:e.concat(u(n))}));return 0===e.length?[be.fromTag("br")]:e},c=i();Bo(n[0]),No(n[0],c)},sg=function(n){return Sf(n,!0)},mg=function(n){var e=kr(n);0===e.length&&Po(n)},dg=function(n,e){return{grid:n,cursor:e}},gg=function(n){return rn(n,(function(n){return rn(n.cells,(function(n){var e=n.element;return ar(sg(e),e)}))}))},pg=function(n,e,t){var r,o,u=Kr(n).rows;return E.from(null===(o=null===(r=u[e])||void 0===r?void 0:r.cells[t])||void 0===o?void 0:o.element).filter(sg).orThunk((function(){return gg(u)}))},vg=function(n,e,t){var r=pg(n,e,t);return dg(n,r)},hg=function(n){var e=function(n,e){var t=P(n,(function(n){return n.row===e.row}));return t?n:n.concat([e])};return H(n,e,[]).sort((function(n,e){return n.row-e.row}))},bg=function(n,e,t,r){var o=e[0].row,u=hg(e),i=U(u,(function(n,e){var u=Ad(n.grid,o,e.row+n.delta,t,r.getOrInit);return{grid:u,delta:n.delta+1}}),{grid:n,delta:0}).grid;return vg(i,o,e[0].column)},wg=function(n,e,t,r){var o=hg(e),u=o[o.length-1],i=u.row+u.rowspan,c=U(o,(function(n,e){return Ad(n,i,e.row,t,r.getOrInit)}),n);return vg(c,i,e[0].column)},yg=function(n,e,t,r){var o=e.details,u=qd(o),i=u[0].column,c=U(u,(function(n,e){var o=Od(n.grid,i,e.column+n.delta,t,r.getOrInit);return{grid:o,delta:n.delta+1}}),{grid:n,delta:0}).grid;return vg(c,o[0].row,i)},Cg=function(n,e,t,r){var o=e.details,u=o[o.length-1],i=u.column+u.colspan,c=qd(o),a=U(c,(function(n,e){return Od(n,i,e.column,t,r.getOrInit)}),n);return vg(a,o[0].row,i)},Tg=function(n,e,t,r){var o=qd(e),u=L(o,(function(n){return n.column})),i=Hd(n,u,!0,t,r.replaceOrInit);return vg(i,e[0].row,e[0].column)},Sg=function(n,e,t,r){var o=Vd(n,e,t,r.replaceOrInit);return vg(o,e[0].row,e[0].column)},xg=function(n,e,t,r){var o=qd(e),u=L(o,(function(n){return n.column})),i=Hd(n,u,!1,t,r.replaceOrInit);return vg(i,e[0].row,e[0].column)},Rg=function(n,e,t,r){var o=Vd(n,e,t,r.replaceOrInit);return vg(o,e[0].row,e[0].column)},Ag=function(n,e){return function(t,r,o,u,i){var c=hg(r),a=L(c,(function(n){return n.row})),l=$d(t,a,n,e,o,u.replaceOrInit,i);return vg(l,r[0].row,r[0].column)}},Dg=Ag("thead",!0),Og=Ag("tbody",!1),Eg=Ag("tfoot",!1),kg=function(n,e,t,r){var o=qd(e.details),u=Ed(n,L(o,(function(n){return n.column}))),i=u.length>0?u[0].cells.length-1:0;return vg(u,o[0].row,Math.min(o[0].column,i))},Mg=function(n,e,t,r){var o=hg(e),u=kd(n,o[0].row,o[o.length-1].row),i=u.length>0?u.length-1:0;return vg(u,Math.min(e[0].row,i),e[0].column)},Ig=function(n,e,t,r){var o=e.cells;fg(o);var u=ed(n,e.bounds,t,r.merge(o));return dg(u,E.from(o[0]))},Ng=function(n,e,t,r){var o=function(n,e){return td(n,e,t,r.unmerge(e))},u=U(e,o,n);return dg(u,E.from(e[0]))},Bg=function(n,e,t,r){var o=function(n,e){var t=lo.fromTable(n);return zm(t,e,!0)},u=o(e.clipboard,e.generators),i=qe(e.row,e.column),c=Td(i,n,u,e.generators,t);return c.fold((function(){return dg(n,E.some(e.element))}),(function(n){return vg(n,e.row,e.column)}))},Pg=function(n,e,t){var r=_r(n,t.section),o=lo.generate(r);return zm(o,e,!0)},jg=function(n,e,t,r){var o=Kr(n).rows,u=e.cells[0].column,i=o[e.cells[0].row],c=Pg(e.clipboard,e.generators,i),a=Sd(u,n,c,e.generators,t);return vg(a,e.cells[0].row,e.cells[0].column)},Lg=function(n,e,t,r){var o=Kr(n).rows,u=e.cells[e.cells.length-1].column+e.cells[e.cells.length-1].colspan,i=o[e.cells[0].row],c=Pg(e.clipboard,e.generators,i),a=Sd(u,n,c,e.generators,t);return vg(a,e.cells[0].row,e.cells[0].column)},_g=function(n,e,t,r){var o=Kr(n).rows,u=e.cells[0].row,i=o[u],c=Pg(e.clipboard,e.generators,i),a=xd(u,n,c,e.generators,t);return vg(a,e.cells[0].row,e.cells[0].column)},zg=function(n,e,t,r){var o=Kr(n).rows,u=e.cells[e.cells.length-1].row+e.cells[e.cells.length-1].rowspan,i=o[e.cells[0].row],c=Pg(e.clipboard,e.generators,i),a=xd(u,n,c,e.generators,t);return vg(a,e.cells[0].row,e.cells[0].column)},Wg=function(n,e){var t=lo.fromTable(n),r=Gm(t,e);return r.bind((function(n){var e=n[n.length-1],r=n[0].column,o=e.column+e.colspan,u=K(L(t.all,(function(n){return F(n.cells,(function(n){return n.column>=r&&n.column<o}))})));return bm(u)})).getOr("")},Fg=function(n,e){var t=lo.fromTable(n),r=Gm(t,e);return r.bind(bm).getOr("")},Ug=function(n,e){var t=lo.fromTable(n),r=Gm(t,e);return r.bind((function(n){var e=n[n.length-1],r=n[0].row,o=e.row+e.rowspan,u=t.all.slice(r,o);return wm(u)})).getOr("")},Hg=function(n,e,t,r){return $a(n,e,t,r.sizing)},$g=function(n,e,t,r){return Ha(n,e,t,r.sizing,r.resize)},Vg=function(n,e){return P(e,(function(n){return 0===n.column&&n.isLocked}))},qg=function(n,e){return P(e,(function(e){return e.column+e.colspan>=n.grid.columns&&e.isLocked}))},Kg=function(n,e){var t=Fu(n),r=qd(e);return H(r,(function(n,e){var r=t[e.column],o=r.map(_u).getOr(0);return n+o}),0)},Xg=function(n){return function(e,t){return Gm(e,t).filter((function(t){var r=n?Vg:qg;return!r(e,t)})).map((function(n){return{details:n,pixelDelta:Kg(e,n)}}))}},Gg=function(n,e){return Ym(n,e).map((function(e){return{details:e,pixelDelta:-Kg(n,e)}}))},Yg=function(n){return function(e,t){return qm(e,t).filter((function(t){var r=n?Vg:qg;return!r(e,t.cells)}))}},Jg=eg.transform("th"),Qg=eg.transform("td"),Zg=$m(bg,Gm,g,g,eg.modification),np=$m(wg,Gm,g,g,eg.modification),ep=$m(yg,Xg(!0),$g,g,eg.modification),tp=$m(Cg,Xg(!1),$g,g,eg.modification),rp=$m(kg,Gg,$g,mg,eg.modification),op=$m(Mg,Gm,g,mg,eg.modification),up=$m(Tg,Ym,g,g,Jg),ip=$m(xg,Ym,g,g,Qg),cp=$m(Dg,Ym,g,g,Jg),ap=$m(Og,Ym,g,g,Qg),lp=$m(Eg,Ym,g,g,Qg),fp=$m(Sg,Ym,g,g,Jg),sp=$m(Rg,Ym,g,g,Qg),mp=$m(Ig,Zm,Hg,g,eg.merging),dp=$m(Ng,nd,Hg,g,eg.merging),gp=$m(Bg,Vm,Hg,g,eg.modification),pp=$m(jg,Yg(!0),g,g,eg.modification),vp=$m(Lg,Yg(!1),g,g,eg.modification),hp=$m(_g,qm,g,g,eg.modification),bp=$m(zg,qm,g,g,eg.modification),wp=Wg,yp=Fg,Cp=Ug,Tp=function(n,e,t){var r=function(n){return"table"===rt(Vc(n))},o=function(e){return!1===r(n)||xl(e).rows>1},u=function(e){return!1===r(n)||xl(e).columns>1},i=Fs(n),c=Ws(n)?g:dm,a=function(e){switch(Ls(n)){case"section":return Om.section();case"sectionCells":return Om.sectionCells();case"cells":return Om.cells();default:return Om.getTableSectionType(e,"section")}},l=function(t,r){return r.cursor.fold((function(){var r=kr(t);return en(r).filter(Mt).map((function(r){e.clear(t);var o=n.dom.createRng();return o.selectNode(r.dom),n.selection.setRng(o),Yt(r,"data-mce-selected","1"),o}))}),(function(r){var o=mm(r),u=n.dom.createRng();return u.setStart(o.element.dom,o.offset),u.setEnd(o.element.dom,o.offset),n.selection.setRng(u),e.clear(t),E.some(u)}))},f=function(e,t,r,o,u){return function(c,f,s){void 0===s&&(s=!1),Jc(c);var m=o(),d=be.fromDom(n.getDoc()),g=Wc(r,d,i),p={sizing:Vs(n,c),resize:Ws(n)?Ya():Ja(),section:a(c)};return t(c)?e(m,c,f,g,p).bind((function(e){_(e.newRows,(function(e){Qf(n,e.dom)})),_(e.newCells,(function(e){Zf(n,e.dom)}));var t=l(c,e);return Mt(c)&&(Jc(c),s||os(n,c.dom,u)),t.map((function(n){return{rng:n,effect:u}}))})):E.none()}},s=f(op,o,g,t,is),m=f(rp,u,g,t,is),d=f(Zg,x,g,t,is),p=f(np,x,g,t,is),v=f(ep,x,c,t,is),h=f(tp,x,c,t,is),b=f(mp,x,g,t,is),w=f(dp,x,g,t,is),y=f(pp,x,g,t,is),C=f(vp,x,g,t,is),T=f(hp,x,g,t,is),S=f(bp,x,g,t,is),R=f(gp,x,g,t,cs),A=f(fp,x,g,t,is),D=f(sp,x,g,t,is),O=f(up,x,g,t,is),k=f(ip,x,g,t,is),M=f(cp,x,g,t,is),I=f(ap,x,g,t,is),N=f(lp,x,g,t,is),B=yp,P=wp,j=Cp;return{deleteRow:s,deleteColumn:m,insertRowsBefore:d,insertRowsAfter:p,insertColumnsBefore:v,insertColumnsAfter:h,mergeCells:b,unmergeCells:w,pasteColsBefore:y,pasteColsAfter:C,pasteRowsBefore:T,pasteRowsAfter:S,pasteCells:R,makeCellsHeader:A,unmakeCellsHeader:D,makeColumnsHeader:O,unmakeColumnsHeader:k,makeRowsHeader:M,makeRowsBody:I,makeRowsFooter:N,getTableRowType:j,getTableCellType:B,getTableColType:P}},Sp={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},xp=function(){return be.fromTag("th")},Rp=function(){return be.fromTag("td")},Ap=function(){return be.fromTag("col")},Dp=function(n,e,t,r){for(var o=be.fromTag("tr"),u=0;u<n;u++){var i=r<e||u<t?xp():Rp();u<t&&Yt(i,"scope","row"),r<e&&Yt(i,"scope","col"),Oo(i,be.fromTag("br")),Oo(o,i)}return o},Op=function(n){var e=be.fromTag("colgroup");return j(n,(function(){return Oo(e,Ap())})),e},Ep=function(n,e,t,r){return j(n,(function(n){return Dp(e,t,r,n)}))},kp=function(n,e,t,r,o,u){void 0===u&&(u=Sp);var i=be.fromTag("table"),c="cells"!==o;dr(i,u.styles),Jt(i,u.attributes),u.colGroups&&Oo(i,Op(e));var a=Math.min(n,t);if(c&&t>0){var l=be.fromTag("thead");Oo(i,l);var f="sectionCells"===o?a:0,s=Ep(t,e,f,r);No(l,s)}var m=be.fromTag("tbody");Oo(i,m);var d=c?n-a:n,g=c?0:t,p=Ep(d,e,g,r);return No(m,p),i},Mp=function(n){return n.dom.innerHTML},Ip=function(n){var e=be.fromTag("div"),t=be.fromDom(n.dom.cloneNode(!0));return Oo(e,t),Mp(e)},Np=function(n,e){n.selection.select(e.dom,!0),n.selection.collapse(!0)},Bp=function(n,e){Kt(e,"td,th").each(y(Np,n))},Pp=function(n,e){_(zt(e,"tr"),(function(e){Qf(n,e.dom),_(zt(e,"th,td"),(function(e){Zf(n,e.dom)}))}))},jp=function(n){return o(n)&&-1!==n.indexOf("%")},Lp=function(n,e,t,r,o){var u=ys(n),i={styles:u,attributes:ws(n),colGroups:js(n)};return n.undoManager.ignore((function(){var u=kp(t,e,o,r,Ls(n),i);Yt(u,"data-mce-id","__mce");var c=Ip(u);n.insertContent(c),n.addVisual()})),Kt(Vc(n),'table[data-mce-id="__mce"]').map((function(e){return Ns(n)?Js(e):Bs(n)?Qs(e):(Is(n)||jp(u.width))&&Ys(e),Jc(e),er(e,"data-mce-id"),Pp(n,e),Bp(n,e),e.dom})).getOr(null)},_p=function(n,e,t,r,o){void 0===r&&(r={});var u=function(n){return d(n)&&n>0};if(u(e)&&u(t)){var i=r.headerRows||0,c=r.headerColumns||0;return Lp(n,t,e,c,i)}return null},zp=function(n){return function(){return n().fold((function(){return[]}),(function(n){return L(n,(function(n){return n.dom}))}))}},Wp=function(n){return function(e){var t=e.length>0?E.some(Hc(e)):E.none();n(t)}},Fp=function(n){return function(e,t,r){void 0===r&&(r={});var o=_p(n,t,e,r,"Invalid values for insertTable - rows and columns values are required to insert a table.");return n.undoManager.add(),o}},Up=function(n,e,t,r){return{insertTable:Fp(n),setClipboardRows:Wp(e.setRows),getClipboardRows:zp(e.getRows),setClipboardCols:Wp(e.setColumns),getClipboardCols:zp(e.getColumns),resizeHandler:t,selectionTargets:r}},Hp=function(n,e,t){var r=wr(n,e,1);1===t||r<=1?er(n,e):Yt(n,e,Math.min(t,r))},$p=function(n,e,t){if(lo.hasColumns(n)){var r=F(lo.justColumns(n),(function(n){return n.column>=e&&n.column<t})),o=L(r,(function(n){var r=Cu(n.element);return Hp(r,"span",t-e),r})),u=be.fromTag("colgroup");return No(u,o),[u]}return[]},Vp=function(n,e,t){return L(n.all,(function(n){var r=F(n.cells,(function(n){return n.column>=e&&n.column<t})),o=L(r,(function(n){var r=Cu(n.element);return Hp(r,"colspan",t-e),r})),u=be.fromTag("tr");return No(u,o),u}))},qp=function(n,e){var t=lo.fromTable(n),r=Ym(t,e);return r.map((function(n){var e=n[n.length-1],r=n[0].column,o=e.column+e.colspan,u=$p(t,r,o),i=Vp(t,r,o);return un(un([],u,!0),i,!0)}))},Kp=function(n,e,t){var r=lo.fromTable(n),o=Gm(r,e);return o.bind((function(n){var e=zm(r,t,!1),o=Kr(e).rows,u=o.slice(n[0].row,n[n.length-1].row+n[n.length-1].rowspan),i=X(u,(function(n){var e=F(n.cells,(function(n){return!n.isLocked}));return e.length>0?[on(on({},n),{cells:e})]:[]})),c=Fm(i);return ar(c.length>0,c)})).map((function(n){return Nm(n)}))},Xp=tinymce.util.Tools.resolve("tinymce.util.Tools"),Gp=function(n,e,t){var r,o=n.select("td,th",e),u=function(e,r){for(var o=0;o<r.length;o++){var u=n.getStyle(r[o],t);if("undefined"===typeof e&&(e=u),e!==u)return""}return e};return u(r,o)},Yp=function(n,e,t){t&&n.formatter.apply("align"+t,{},e)},Jp=function(n,e,t){t&&n.formatter.apply("valign"+t,{},e)},Qp=function(n,e){Xp.each("left center right".split(" "),(function(t){n.formatter.remove("align"+t,{},e)}))},Zp=function(n,e){Xp.each("top middle bottom".split(" "),(function(t){n.formatter.remove("valign"+t,{},e)}))},nv=[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}],ev=function(n){return{value:n}},tv=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,rv=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,ov=function(n){return tv.test(n)||rv.test(n)},uv=function(n){return Cn(n,"#").toUpperCase()},iv=function(n){return ov(n)?E.some({value:uv(n)}):E.none()},cv=function(n){var e=n.toString(16);return(1===e.length?"0"+e:e).toUpperCase()},av=function(n){var e=cv(n.red)+cv(n.green)+cv(n.blue);return ev(e)},lv=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,fv=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,sv=function(n,e,t,r){return{red:n,green:e,blue:t,alpha:r}},mv=function(n,e,t,r){var o=parseInt(n,10),u=parseInt(e,10),i=parseInt(t,10),c=parseFloat(r);return sv(o,u,i,c)},dv=function(n){if("transparent"===n)return E.some(sv(0,0,0,0));var e=lv.exec(n);if(null!==e)return E.some(mv(e[1],e[2],e[3],"1"));var t=fv.exec(n);return null!==t?E.some(mv(t[1],t[2],t[3],t[4])):E.none()},gv=function(n){return iv(n).orThunk((function(){return dv(n).map(av)})).getOrThunk((function(){var e=document.createElement("canvas");e.height=1,e.width=1;var t=e.getContext("2d");t.clearRect(0,0,e.width,e.height),t.fillStyle="#FFFFFF",t.fillStyle=n,t.fillRect(0,0,1,1);var r=t.getImageData(0,0,1,1).data,o=r[0],u=r[1],i=r[2],c=r[3];return av(sv(o,u,i,c))}))},pv=function(n){var e=n,t=function(){return e},r=function(n){e=n};return{get:t,set:r}},vv=function(n){var e=pv(E.none()),t=function(){return e.get().each(n)},r=function(){t(),e.set(E.none())},o=function(){return e.get().isSome()},u=function(){return e.get()},i=function(n){t(),e.set(E.some(n))};return{clear:r,isSet:o,get:u,set:i}},hv=function(){return vv((function(n){return n.unbind()}))},bv=function(){var n=vv(g),e=function(e){return n.get().each(e)};return on(on({},n),{on:e})},wv=function(n,e,t,r){return function(o){var u=hv(),i=On(r),c=function(){var c=Sa(e),a=function(e){return n.formatter.match(t,{value:r},e.dom,i)};i?(o.setActive(!P(c,a)),u.set(n.formatter.formatChanged(t,(function(n){return o.setActive(!n)}),!0))):(o.setActive(G(c,a)),u.set(n.formatter.formatChanged(t,o.setActive,!1,{value:r})))};return n.initialized?c():n.on("init",c),u.clear}},yv=function(n){return Fe(n,"menu")},Cv=function(n){return L(n,(function(n){var e=n.text||n.title;return yv(n)?{text:e,items:Cv(n.menu)}:{text:e,value:n.value}}))},Tv=function(n,e,t,r,o){return L(t,(function(t){var u=t.text||t.title;return yv(t)?{type:"nestedmenuitem",text:u,getSubmenuItems:function(){return Tv(n,e,t.menu,r,o)}}:{text:u,type:"togglemenuitem",onAction:function(){return o(t.value)},onSetup:wv(n,e,r,t.value)}}))},Sv=function(n,e){return function(t){var r;n.execCommand("mceTableApplyCellStyle",!1,(r={},r[e]=t,r))}},xv=function(n){return X(n,(function(n){return yv(n)?[on(on({},n),{menu:xv(n.menu)})]:Dn(n.value)?[n]:[]}))},Rv=function(n,e,t,r,o){return function(u){return u(Tv(n,e,t,r,o))}},Av=function(n,e,t){var r=L(e,(function(n){return{text:n.title,value:"#"+gv(n.value).value,type:"choiceitem"}}));return[{type:"fancymenuitem",fancytype:"colorswatch",initData:{colors:r.length>0?r:void 0,allowCustomColors:!1},onAction:function(e){var r,o="remove"===e.value?"":e.value;n.execCommand("mceTableApplyCellStyle",!1,(r={},r[t]=o,r))}}]},Dv=function(n){return function(){var e=n.queryCommandValue("mceTableRowType"),t="header"===e?"body":"header";n.execCommand("mceTableRowType",!1,{type:t})}},Ov=function(n){return function(){var e=n.queryCommandValue("mceTableColType"),t="th"===e?"td":"th";n.execCommand("mceTableColType",!1,{type:t})}},Ev=function(n){var e=Cv(Es(n));return e.length>0?E.some({name:"class",type:"listbox",label:"Class",items:e}):E.none()},kv=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"listbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"listbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"listbox",label:"Horizontal align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"listbox",label:"Vertical align",items:nv}],Mv=function(n){return kv.concat(Ev(n).toArray())},Iv=function(n,e){var t=[{text:"Select...",value:""}],r=[{name:"borderstyle",type:"listbox",label:"Border style",items:t.concat(Cv(bs(n)))},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}],o={name:"borderwidth",type:"input",label:"Border width"},u="cell"===e?[o].concat(r):r;return{title:"Advanced",name:"advanced",items:u}},Nv=function(n){return function(e,t){var r=e.dom,o=function(e,o){n&&!o||r.setAttrib(t,e,o)},u=function(e,o){n&&!o||r.setStyle(t,e,o)},i=function(r,o){n&&!o||(""===o?e.formatter.remove(r,{value:null},t,!0):e.formatter.apply(r,{value:o},t))};return{setAttrib:o,setStyle:u,setFormat:i}}},Bv={normal:Nv(!1),ifTruthy:Nv(!0)},Pv=function(n){return function(e){return Sn(e,"rgb")?n.toHex(e):e}},jv=function(n,e){var t=be.fromDom(e);return{borderwidth:vr(t,"border-width").getOr(""),borderstyle:vr(t,"border-style").getOr(""),bordercolor:vr(t,"border-color").map(Pv(n)).getOr(""),backgroundcolor:vr(t,"background-color").map(Pv(n)).getOr("")}},Lv=function(n){var e=n[0],t=n.slice(1);return _(t,(function(n){_(Oe(e),(function(t){ke(n,(function(n,r){var o=e[t];""!==o&&t===r&&o!==n&&(e[t]="")}))}))})),e},_v=function(n,e,t,r){return V(n,(function(n){return!l(t.formatter.matchNode(r,e+n))})).getOr("")},zv=y(_v,["left","center","right"],"align"),Wv=y(_v,["top","middle","bottom"],"valign"),Fv=function(n,e){var t=ys(n),r=ws(n),o=function(n){return{borderstyle:ze(t,"border-style").getOr(""),bordercolor:Pv(n)(ze(t,"border-color").getOr("")),backgroundcolor:Pv(n)(ze(t,"background-color").getOr(""))}},u={height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,class:"",align:"",border:""},i=function(){var e=t["border-width"];return Os(n)&&e?{border:e}:ze(r,"border").fold((function(){return{}}),(function(n){return{border:n}}))},c=e?o(n.dom):{},a=function(){var n=ze(t,"border-spacing").or(ze(r,"cellspacing")).fold((function(){return{}}),(function(n){return{cellspacing:n}})),e=ze(t,"border-padding").or(ze(r,"cellpadding")).fold((function(){return{}}),(function(n){return{cellpadding:n}}));return on(on({},n),e)},l=on(on(on(on(on(on({},u),t),r),c),i()),a());return l},Uv=function(n){return Ir(be.fromDom(n)).map((function(e){var t={selection:Hc(n.cells)};return Cp(e,t)})).getOr("")},Hv=function(n,e,t){var r=function(e,t){var r=vr(be.fromDom(t),"border-width");return Os(n)&&r.isSome()?r.getOr(""):e.getAttrib(t,"border")||Gp(n.dom,t,"border-width")||Gp(n.dom,t,"border")},o=n.dom,u=Os(n)?o.getStyle(e,"border-spacing")||o.getAttrib(e,"cellspacing"):o.getAttrib(e,"cellspacing")||o.getStyle(e,"border-spacing"),i=Os(n)?Gp(o,e,"padding")||o.getAttrib(e,"cellpadding"):o.getAttrib(e,"cellpadding")||Gp(o,e,"padding");return on({width:o.getStyle(e,"width")||o.getAttrib(e,"width"),height:o.getStyle(e,"height")||o.getAttrib(e,"height"),cellspacing:u,cellpadding:i,border:r(o,e),caption:!!o.select("caption",e)[0],class:o.getAttrib(e,"class",""),align:zv(n,e)},t?jv(o,e):{})},$v=function(n,e,t){var r=n.dom;return on({height:r.getStyle(e,"height")||r.getAttrib(e,"height"),class:r.getAttrib(e,"class",""),type:Uv(e),align:zv(n,e)},t?jv(r,e):{})},Vv=function(n,e,t,r){var o=n.dom,u=r.getOr(e),i=function(n,e){return o.getStyle(n,e)||o.getAttrib(n,e)};return on({width:i(u,"width"),height:i(e,"height"),scope:o.getAttrib(e,"scope"),celltype:$c(e),class:o.getAttrib(e,"class",""),halign:zv(n,e),valign:Wv(n,e)},t?jv(o,e):{})},qv=function(n,e){var t=lo.fromTable(n),r=lo.justCells(t),o=F(r,(function(n){return P(e,(function(e){return Se(n.element,e)}))}));return L(o,(function(n){return{element:n.element.dom,column:lo.getColumnAt(t,n.column).map((function(n){return n.element.dom}))}}))},Kv=function(n,e,t){n.setAttrib("scope",t.scope),n.setAttrib("class",t.class),n.setStyle("height",Yc(t.height)),e.setStyle("width",Yc(t.width))},Xv=function(n,e){n.setFormat("tablecellbackgroundcolor",e.backgroundcolor),n.setFormat("tablecellbordercolor",e.bordercolor),n.setFormat("tablecellborderstyle",e.borderstyle),n.setFormat("tablecellborderwidth",Yc(e.borderwidth))},Gv=function(n,e,t){var r=1===e.length;_(e,(function(e){var o=e.element,u=r?Bv.normal(n,o):Bv.ifTruthy(n,o),i=e.column.map((function(e){return r?Bv.normal(n,e):Bv.ifTruthy(n,e)})).getOr(u);Kv(u,i,t),Ss(n)&&Xv(u,t),r&&(Qp(n,o),Zp(n,o)),t.halign&&Yp(n,o,t.halign),t.valign&&Jp(n,o,t.valign)}))},Yv=function(n,e){n.execCommand("mceTableCellType",!1,{type:e.celltype,no_events:!0})},Jv=function(n,e,t,r){var o=Pe(r,(function(n,e){return t[e]!==n}));_e(o)>0&&e.length>=1&&Ir(e[0]).each((function(t){var u=qv(t,e),i=_e(Pe(o,(function(n,e){return"scope"!==e&&"celltype"!==e})))>0,c=We(o,"celltype");(i||We(o,"scope"))&&Gv(n,u,r),c&&Yv(n,r),os(n,t.dom,{structure:c,style:i})}))},Qv=function(n,e,t,r){var o=r.getData();r.close(),n.undoManager.transact((function(){Jv(n,e,t,o),n.focus()}))},Zv=function(n,e){var t=Ir(e[0]).map((function(t){return L(qv(t,e),(function(e){return Vv(n,e.element,Ss(n),e.column)}))}));return Lv(t.getOrDie())},nh=function(n,e){var t=Sa(e);if(0!==t.length){var r=Zv(n,t),o={type:"tabpanel",tabs:[{title:"General",name:"general",items:Mv(n)},Iv(n,"cell")]},u={type:"panel",items:[{type:"grid",columns:2,items:Mv(n)}]};n.windowManager.open({title:"Cell Properties",size:"normal",body:Ss(n)?o:u,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:r,onSubmit:y(Qv,n,t,r)})}},eh=function(n){var e=Cv(ks(n));return e.length>0?E.some({name:"class",type:"listbox",label:"Class",items:e}):E.none()},th=[{type:"listbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],rh=function(n){return th.concat(eh(n).toArray())},oh=function(n,e){n.setAttrib("class",e.class),n.setStyle("height",Yc(e.height))},uh=function(n,e){n.setStyle("background-color",e.backgroundcolor),n.setStyle("border-color",e.bordercolor),n.setStyle("border-style",e.borderstyle)},ih=function(n,e,t,r){var o=1===e.length;_(e,(function(e){var u=o?Bv.normal(n,e):Bv.ifTruthy(n,e);oh(u,t),xs(n)&&uh(u,t),t.align!==r.align&&(Qp(n,e),Yp(n,e,t.align))}))},ch=function(n,e){n.execCommand("mceTableRowType",!1,{type:e.type,no_events:!0})},ah=function(n,e,t,r){var o=Pe(r,(function(n,e){return t[e]!==n}));if(_e(o)>0){var u=We(o,"type"),i=!u||_e(o)>1;i&&ih(n,e,r,t),u&&ch(n,r),Ir(be.fromDom(e[0])).each((function(e){return os(n,e.dom,{structure:u,style:i})}))}},lh=function(n,e,t,r){var o=r.getData();r.close(),n.undoManager.transact((function(){ah(n,e,t,o),n.focus()}))},fh=function(n){var e=xa(ea(n),ga.selected);if(0!==e.length){var t=L(e,(function(e){return $v(n,e.dom,xs(n))})),r=Lv(t),o={type:"tabpanel",tabs:[{title:"General",name:"general",items:rh(n)},Iv(n,"row")]},u={type:"panel",items:[{type:"grid",columns:2,items:rh(n)}]};n.windowManager.open({title:"Row Properties",size:"normal",body:xs(n)?o:u,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:r,onSubmit:y(lh,n,L(e,(function(n){return n.dom})),r)})}},sh=function(n,e,t){var r=t?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],o=[{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}],u=As(n)?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],i=[{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}],c=e.length>0?[{type:"listbox",name:"class",label:"Class",items:e}]:[];return r.concat(o).concat(u).concat(i).concat(c)},mh=function(n,e,t,r){if("TD"===e.tagName||"TH"===e.tagName)o(t)?n.setStyle(e,t,r):n.setStyle(e,t);else if(e.children)for(var u=0;u<e.children.length;u++)mh(n,e.children[u],t,r)},dh=function(n,e,t){var r=n.dom,o={},u={};if(o.class=t.class,u.height=Yc(t.height),r.getAttrib(e,"width")&&!Os(n)?o.width=Gc(t.width):u.width=Yc(t.width),Os(n)?(u["border-width"]=Yc(t.border),u["border-spacing"]=Yc(t.cellspacing)):(o.border=t.border,o.cellpadding=t.cellpadding,o.cellspacing=t.cellspacing),Os(n)&&e.children)for(var i=0;i<e.children.length;i++)mh(r,e.children[i],{"border-width":Yc(t.border),padding:Yc(t.cellpadding)}),Rs(n)&&mh(r,e.children[i],{"border-color":t.bordercolor});Rs(n)&&(u["background-color"]=t.backgroundcolor,u["border-color"]=t.bordercolor,u["border-style"]=t.borderstyle),o.style=r.serializeStyle(on(on({},ys(n)),u)),r.setAttribs(e,on(on({},ws(n)),o))},gh=function(n,e,t,r){var o=n.dom,u=r.getData(),i=Pe(u,(function(n,e){return t[e]!==n}));r.close(),""===u.class&&delete u.class,n.undoManager.transact((function(){if(!e){var t=parseInt(u.cols,10)||1,r=parseInt(u.rows,10)||1;e=Lp(n,t,r,0,0)}if(_e(i)>0){dh(n,e,u);var c=o.select("caption",e)[0];(c&&!u.caption||!c&&u.caption)&&n.execCommand("mceTableToggleCaption"),""===u.align?Qp(n,e):Yp(n,e,u.align)}if(n.focus(),n.addVisual(),_e(i)>0){var a=We(i,"caption"),l=!a||_e(i)>1;os(n,e,{structure:a,style:l})}}))},ph=function(n,e){var t,r=n.dom,o=Fv(n,Rs(n));!1===e?(t=r.getParent(n.selection.getStart(),"table",n.getBody()),t?o=Hv(n,t,Rs(n)):Rs(n)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor="")):(o.cols="1",o.rows="1",Rs(n)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor=""));var u=Cv(Ms(n));u.length>0&&o.class&&(o.class=o.class.replace(/\s*mce\-item\-table\s*/g,""));var i={type:"grid",columns:2,items:sh(n,u,e)},c=function(){return{type:"panel",items:[i]}},a=function(){return{type:"tabpanel",tabs:[{title:"General",name:"general",items:[i]},Iv(n,"table")]}},l=Rs(n)?a():c();n.windowManager.open({title:"Table Properties",size:"normal",body:l,onSubmit:y(gh,n,t,o),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o})},vh=function(n){return Ca(ea(n),Xc(n))},hh=function(n){return Ta(ea(n),Xc(n))},bh=function(n,e,t,r,i){var c=Xc(n),a=function(){return vh(n).each((function(e){Ir(e,c).filter(C(c)).each((function(e){var t=be.fromText("");if(Ao(e,t),Po(e),n.dom.isEmpty(n.getBody()))n.setContent(""),n.selection.setCursorLocation();else{var r=n.dom.createRng();r.setStart(t.dom,0),r.setEnd(t.dom,0),n.selection.setRng(r),n.nodeChanged()}}))}))},l=function(e){return vh(n).each((function(t){var r=Bs(n)||Ns(n)||Is(n);r||Ir(t,c).each((function(t){"relative"!==e||Cl(t)?"fixed"!==e||Tl(t)?"responsive"!==e||Sl(t)||Qs(t):Js(t):Ys(t),Jc(t),os(n,t.dom,is)}))}))},f=function(n){return Ir(n,c)},s=function(e){return hh(n).bind((function(n){return f(n).map((function(t){return e(t,n)}))}))},m=function(e,t){s((function(e){n.formatter.toggle("tableclass",{value:t},e.dom),os(n,e.dom,us)}))},d=function(e,t){s((function(e){var o=Sa(r),u=G(o,(function(e){return n.formatter.match("tablecellclass",{value:t},e.dom)})),i=u?n.formatter.remove:n.formatter.apply;_(o,(function(n){return i("tablecellclass",{value:t},n.dom)})),os(n,e.dom,us)}))},p=function(){vh(n).each((function(e){Ir(e,c).each((function(t){qt(t,"caption").fold((function(){var e=be.fromTag("caption");Oo(e,be.fromText("Caption")),Eo(t,e,0),n.selection.setCursorLocation(e.dom,0)}),(function(r){st("caption")(e)&&Te("td",t).each((function(e){return n.selection.setCursorLocation(e.dom,0)})),Po(r)})),os(n,t.dom,is)}))}))},v=function(e){n.focus()},h=function(n,e){return void 0===e&&(e=!1),s((function(t,o){var u=va(r,t,o);n(t,u,e).each(v)}))},b=function(){return s((function(e,t){var o=va(r,e,t),u=Wc(g,be.fromDom(n.getDoc()),E.none());return Kp(e,o,u)}))},w=function(){return s((function(n,e){var t=va(r,n,e);return qp(n,t)}))},T=function(e,t){return t().each((function(t){var o=L(t,(function(n){return Cu(n)}));s((function(t,u){var i=Fc(be.fromDom(n.getDoc())),c=ba(r,u,o,i);e(t,c).each(v)}))}))},S=function(n){return function(e,t){return ze(t,"type").each((function(e){h(n(e),t.no_events)}))}};ke({mceTableSplitCells:function(){return h(e.unmergeCells)},mceTableMergeCells:function(){return h(e.mergeCells)},mceTableInsertRowBefore:function(){return h(e.insertRowsBefore)},mceTableInsertRowAfter:function(){return h(e.insertRowsAfter)},mceTableInsertColBefore:function(){return h(e.insertColumnsBefore)},mceTableInsertColAfter:function(){return h(e.insertColumnsAfter)},mceTableDeleteCol:function(){return h(e.deleteColumn)},mceTableDeleteRow:function(){return h(e.deleteRow)},mceTableCutCol:function(){return w().each((function(n){i.setColumns(n),h(e.deleteColumn)}))},mceTableCutRow:function(){return b().each((function(n){i.setRows(n),h(e.deleteRow)}))},mceTableCopyCol:function(){return w().each((function(n){return i.setColumns(n)}))},mceTableCopyRow:function(){return b().each((function(n){return i.setRows(n)}))},mceTablePasteColBefore:function(){return T(e.pasteColsBefore,i.getColumns)},mceTablePasteColAfter:function(){return T(e.pasteColsAfter,i.getColumns)},mceTablePasteRowBefore:function(){return T(e.pasteRowsBefore,i.getRows)},mceTablePasteRowAfter:function(){return T(e.pasteRowsAfter,i.getRows)},mceTableDelete:a,mceTableCellToggleClass:d,mceTableToggleClass:m,mceTableToggleCaption:p,mceTableSizingMode:function(n,e){return l(e)},mceTableCellType:S((function(n){return"th"===n?e.makeCellsHeader:e.unmakeCellsHeader})),mceTableColType:S((function(n){return"th"===n?e.makeColumnsHeader:e.unmakeColumnsHeader})),mceTableRowType:S((function(n){switch(n){case"header":return e.makeRowsHeader;case"footer":return e.makeRowsFooter;default:return e.makeRowsBody}}))},(function(e,t){return n.addCommand(t,e)})),ke({mceTableProps:y(ph,n,!1),mceTableRowProps:y(fh,n),mceTableCellProps:y(nh,n,r)},(function(e,t){return n.addCommand(t,(function(){return e()}))})),n.addCommand("mceInsertTable",(function(e,t){u(t)&&Oe(t).length>0?_p(n,t.rows,t.columns,t.options,"Invalid values for mceInsertTable - rows and columns values are required to insert a table."):ph(n,!0)})),n.addCommand("mceTableApplyCellStyle",(function(e,t){var i=function(n){return"tablecell"+n.toLowerCase().replace("-","")};if(u(t)){var c=Sa(r);if(0!==c.length){var a=Pe(t,(function(e,t){return n.formatter.has(i(t))&&o(e)}));Ue(a)||(ke(a,(function(e,t){_(c,(function(r){Bv.normal(n,r.dom).setFormat(i(t),e)}))})),f(c[0]).each((function(e){return os(n,e.dom,us)})))}}}))},wh=function(n,e,t){var r=Xc(n),o=function(e){return Ta(ea(n)).bind((function(n){return Ir(n,r).map((function(r){var o=va(t,r,n);return e(r,o)}))})).getOr("")};ke({mceTableRowType:function(){return o(e.getTableRowType)},mceTableCellType:function(){return o(e.getTableCellType)},mceTableColType:function(){return o(e.getTableColType)}},(function(e,t){return n.addQueryValueHandler(t,e)}))},yh=function(){var n=bv(),e=bv();return{getRows:n.get,setRows:function(t){t.fold(n.clear,n.set),e.clear()},clearRows:n.clear,getColumns:e.get,setColumns:function(t){t.fold(e.clear,e.set),n.clear()},clearColumns:e.clear}},Ch={remove_similar:!0,inherit:!1},Th=on({selector:"td,th"},Ch),Sh={tablecellbackgroundcolor:on({styles:{backgroundColor:"%value"}},Th),tablecellverticalalign:on({styles:{"vertical-align":"%value"}},Th),tablecellbordercolor:on({styles:{borderColor:"%value"}},Th),tablecellclass:on({classes:["%value"]},Th),tableclass:on({selector:"table",classes:["%value"]},Ch),tablecellborderstyle:on({styles:{borderStyle:"%value"}},Th),tablecellborderwidth:on({styles:{borderWidth:"%value"}},Th)},xh=function(n){n.formatter.register(Sh)},Rh=su.generate([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),Ah=function(n){return void 0===n&&(n=void 0),Rh.none(n)},Dh=on(on({},Rh),{none:Ah}),Oh=function(n,e,t,r,o){void 0===o&&(o=x);var u=1===r;if(!u&&t<=0)return Dh.first(n[0]);if(u&&t>=n.length-1)return Dh.last(n[n.length-1]);var i=t+r,c=n[i];return o(c)?Dh.middle(e,c):Oh(n,e,i,r,o)},Eh=function(n,e){return Ir(n,e).bind((function(e){var t=kr(e),r=q(t,(function(e){return Se(n,e)}));return r.map((function(n){return{index:n,all:t}}))}))},kh=function(n,e,t){var r=Eh(n,t);return r.fold((function(){return Dh.none(n)}),(function(t){return Oh(t.all,n,t.index,1,e)}))},Mh=function(n,e,t){var r=Eh(n,t);return r.fold((function(){return Dh.none()}),(function(t){return Oh(t.all,n,t.index,-1,e)}))},Ih=function(n,e,t,r){return{start:n,soffset:e,finish:t,foffset:r}},Nh={create:Ih},Bh=su.generate([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Ph=function(n,e,t,r){return n.fold(e,t,r)},jh=function(n){return n.fold(b,b,b)},Lh=Bh.before,_h=Bh.on,zh=Bh.after,Wh={before:Lh,on:_h,after:zh,cata:Ph,getStart:jh},Fh=su.generate([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Uh=function(n){return Fh.exact(n.start,n.soffset,n.finish,n.foffset)},Hh=function(n){return n.match({domRange:function(n){return be.fromDom(n.startContainer)},relative:function(n,e){return Wh.getStart(n)},exact:function(n,e,t,r){return n}})},$h=Fh.domRange,Vh=Fh.relative,qh=Fh.exact,Kh=function(n){var e=Hh(n);return gt(e)},Xh=Nh.create,Gh={domRange:$h,relative:Vh,exact:qh,exactFromRange:Uh,getWin:Kh,range:Xh},Yh=function(n,e){var t=n.document.createRange();return t.selectNode(e.dom),t},Jh=function(n,e){var t=n.document.createRange();return Qh(t,e),t},Qh=function(n,e){return n.selectNodeContents(e.dom)},Zh=function(n,e){e.fold((function(e){n.setStartBefore(e.dom)}),(function(e,t){n.setStart(e.dom,t)}),(function(e){n.setStartAfter(e.dom)}))},nb=function(n,e){e.fold((function(e){n.setEndBefore(e.dom)}),(function(e,t){n.setEnd(e.dom,t)}),(function(e){n.setEndAfter(e.dom)}))},eb=function(n,e,t){var r=n.document.createRange();return Zh(r,e),nb(r,t),r},tb=function(n,e,t,r,o){var u=n.document.createRange();return u.setStart(e.dom,t),u.setEnd(r.dom,o),u},rb=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:n.width,height:n.height}},ob=function(n){var e=n.getClientRects(),t=e.length>0?e[0]:n.getBoundingClientRect();return t.width>0||t.height>0?E.some(t).map(rb):E.none()},ub=su.generate([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),ib=function(n,e,t){return e(be.fromDom(t.startContainer),t.startOffset,be.fromDom(t.endContainer),t.endOffset)},cb=function(n,e){return e.match({domRange:function(n){return{ltr:h(n),rtl:E.none}},relative:function(e,t){return{ltr:cn((function(){return eb(n,e,t)})),rtl:cn((function(){return E.some(eb(n,t,e))}))}},exact:function(e,t,r,o){return{ltr:cn((function(){return tb(n,e,t,r,o)})),rtl:cn((function(){return E.some(tb(n,r,o,e,t))}))}}})},ab=function(n,e){var t=e.ltr();if(t.collapsed){var r=e.rtl().filter((function(n){return!1===n.collapsed}));return r.map((function(n){return ub.rtl(be.fromDom(n.endContainer),n.endOffset,be.fromDom(n.startContainer),n.startOffset)})).getOrThunk((function(){return ib(n,ub.ltr,t)}))}return ib(n,ub.ltr,t)},lb=function(n,e){var t=cb(n,e);return ab(n,t)},fb=function(n,e){var t=lb(n,e);return t.match({ltr:function(e,t,r,o){var u=n.document.createRange();return u.setStart(e.dom,t),u.setEnd(r.dom,o),u},rtl:function(e,t,r,o){var u=n.document.createRange();return u.setStart(r.dom,o),u.setEnd(e.dom,t),u}})};ub.ltr,ub.rtl;var sb=function(n,e,t,r,o){if(0===o)return 0;if(e===r)return o-1;for(var u=r,i=1;i<o;i++){var c=n(i),a=Math.abs(e-c.left);if(t<=c.bottom){if(t<c.top||a>u)return i-1;u=a}}return 0},mb=function(n,e,t){return e>=n.left&&e<=n.right&&t>=n.top&&t<=n.bottom},db=function(n,e,t,r,o){var u=function(t){var r=n.dom.createRange();return r.setStart(e.dom,t),r.collapse(!0),r},i=function(n){var e=u(n);return e.getBoundingClientRect()},c=zo(e).length,a=sb(i,t,r,o.right,c);return u(a)},gb=function(n,e,t,r){var o=n.dom.createRange();o.selectNode(e.dom);var u=o.getClientRects(),i=rn(u,(function(n){return mb(n,t,r)?E.some(n):E.none()}));return i.map((function(o){return db(n,e,t,r,o)}))},pb=function(n,e,t,r){var o=n.dom.createRange(),u=yt(e);return rn(u,(function(e){return o.selectNode(e.dom),mb(o.getBoundingClientRect(),t,r)?vb(n,e,t,r):E.none()}))},vb=function(n,e,t,r){return at(e)?gb(n,e,t,r):pb(n,e,t,r)},hb=function(n,e,t,r){var o=n.dom.createRange();o.selectNode(e.dom);var u=o.getBoundingClientRect(),i=Math.max(u.left,Math.min(u.right,t)),c=Math.max(u.top,Math.min(u.bottom,r));return vb(n,e,i,c)},bb=!0,wb=!1,yb=function(n,e){return e-n.left<n.right-e?bb:wb},Cb=function(n,e,t){var r=n.dom.createRange();return r.selectNode(e.dom),r.collapse(t),r},Tb=function(n,e,t){var r=n.dom.createRange();r.selectNode(e.dom);var o=r.getBoundingClientRect(),u=yb(o,t),i=u===bb?Oc:Ec;return i(e).map((function(e){return Cb(n,e,u)}))},Sb=function(n,e,t){var r=e.dom.getBoundingClientRect(),o=yb(r,t);return E.some(Cb(n,e,o))},xb=function(n,e,t){var r=0===yt(e).length?Sb:Tb;return r(n,e,t)},Rb=function(n,e,t){var r,o;return E.from(null===(o=(r=n.dom).caretPositionFromPoint)||void 0===o?void 0:o.call(r,e,t)).bind((function(e){if(null===e.offsetNode)return E.none();var t=n.dom.createRange();return t.setStart(e.offsetNode,e.offset),t.collapse(),E.some(t)}))},Ab=function(n,e,t){var r,o;return E.from(null===(o=(r=n.dom).caretRangeFromPoint)||void 0===o?void 0:o.call(r,e,t))},Db=function(n,e,t,r){var o=n.dom.createRange();o.selectNode(e.dom);var u=o.getBoundingClientRect(),i=Math.max(u.left,Math.min(u.right,t)),c=Math.max(u.top,Math.min(u.bottom,r));return hb(n,e,i,c)},Ob=function(n,e,t){return be.fromPoint(n,e,t).bind((function(r){var o=function(){return xb(n,r,e)};return 0===yt(r).length?o():Db(n,r,e,t).orThunk(o)}))},Eb=function(){return document.caretPositionFromPoint?Rb:document.caretRangeFromPoint?Ab:Ob}(),kb=function(n,e,t){var r=be.fromDom(n.document);return Eb(r,e,t).map((function(n){return Nh.create(be.fromDom(n.startContainer),n.startOffset,be.fromDom(n.endContainer),n.endOffset)}))},Mb=function(n,e){var t=rt(n);return"input"===t?Wh.after(n):B(["br","img"],t)?0===e?Wh.before(n):Wh.after(n):Wh.on(n,e)},Ib=function(n,e){var t=n.fold(Wh.before,Mb,Wh.after),r=e.fold(Wh.before,Mb,Wh.after);return Gh.relative(t,r)},Nb=function(n,e,t,r){var o=Mb(n,e),u=Mb(t,r);return Gh.relative(o,u)},Bb=function(n){return n.match({domRange:function(n){var e=be.fromDom(n.startContainer),t=be.fromDom(n.endContainer);return Nb(e,n.startOffset,t,n.endOffset)},relative:Ib,exact:Nb})},Pb=function(n,e,t,r){var o=mt(n),u=o.dom.createRange();return u.setStart(n.dom,e),u.setEnd(t.dom,r),u},jb=function(n,e,t,r){var o=Pb(n,e,t,r),u=Se(n,t)&&e===r;return o.collapsed&&!u},Lb=function(n){return E.from(n.getSelection())},_b=function(n,e){Lb(n).each((function(n){n.removeAllRanges(),n.addRange(e)}))},zb=function(n,e,t,r,o){var u=tb(n,e,t,r,o);_b(n,u)},Wb=function(n,e,t,r,o,u){e.collapse(t.dom,r),e.extend(o.dom,u)},Fb=function(n,e){return lb(n,e).match({ltr:function(e,t,r,o){zb(n,e,t,r,o)},rtl:function(e,t,r,o){Lb(n).each((function(u){if(u.setBaseAndExtent)u.setBaseAndExtent(e.dom,t,r.dom,o);else if(u.extend)try{Wb(n,u,e,t,r,o)}catch(i){zb(n,r,o,e,t)}else zb(n,r,o,e,t)}))}})},Ub=function(n,e,t,r,o){var u=Nb(e,t,r,o);Fb(n,u)},Hb=function(n,e,t){var r=Ib(e,t);Fb(n,r)},$b=function(n){var e=Gh.getWin(n).dom,t=function(n,t,r,o){return tb(e,n,t,r,o)},r=Bb(n);return lb(e,r).match({ltr:t,rtl:t})},Vb=function(n){if(n.rangeCount>0){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return E.some(Nh.create(be.fromDom(e.startContainer),e.startOffset,be.fromDom(t.endContainer),t.endOffset))}return E.none()},qb=function(n){if(null===n.anchorNode||null===n.focusNode)return Vb(n);var e=be.fromDom(n.anchorNode),t=be.fromDom(n.focusNode);return jb(e,n.anchorOffset,t,n.focusOffset)?E.some(Nh.create(e,n.anchorOffset,t,n.focusOffset)):Vb(n)},Kb=function(n,e,t){void 0===t&&(t=!0);var r=t?Jh:Yh,o=r(n,e);_b(n,o)},Xb=function(n){return Lb(n).filter((function(n){return n.rangeCount>0})).bind(qb)},Gb=function(n){return Xb(n).map((function(n){return Gh.exact(n.start,n.soffset,n.finish,n.foffset)}))},Yb=function(n,e){var t=fb(n,e);return ob(t)},Jb=function(n,e,t){return kb(n,e,t)},Qb=function(n){Lb(n).each((function(n){return n.removeAllRanges()}))},Zb=tinymce.util.Tools.resolve("tinymce.util.VK"),nw=function(n,e,t){return rw(n,e,kh(t,Sf))},ew=function(n,e,t){return rw(n,e,Mh(t,Sf))},tw=function(n,e){var t=Gh.exact(e,0,e,0);return $b(t)},rw=function(n,e,t){return t.fold(E.none,E.none,(function(e,t){return Oc(t).map((function(e){return tw(n,e)}))}),(function(t){return n.execCommand("mceTableInsertRowAfter"),nw(n,e,t)}))},ow=["table","li","dl"],uw=function(n,e,t){if(n.keyCode===Zb.TAB){var r=Vc(e),o=function(n){var e=rt(n);return Se(n,r)||B(ow,e)},u=e.selection.getRng(),i=be.fromDom(n.shiftKey?u.startContainer:u.endContainer);Er(i,o).each((function(r){n.preventDefault(),Ir(r,o).each(t.clear),e.selection.collapse(n.shiftKey);var u=n.shiftKey?ew:nw,i=u(e,o,r);i.each((function(n){e.selection.setRng(n)}))}))}},iw=function(n,e){return{selection:n,kill:e}},cw={create:iw},aw=function(n,e,t,r){return{start:Wh.on(n,e),finish:Wh.on(t,r)}},lw={create:aw},fw=function(n,e){var t=fb(n,e);return Nh.create(be.fromDom(t.startContainer),t.startOffset,be.fromDom(t.endContainer),t.endOffset)},sw=lw.create,mw=function(n,e,t,r,o,u,i){return Se(t,o)&&r===u?E.none():Xt(t,"td,th",e).bind((function(t){return Xt(o,"td,th",e).bind((function(r){return dw(n,e,t,r,i)}))}))},dw=function(n,e,t,r,o){return Se(t,r)?E.none():tu(t,r,e).bind((function(e){var r=e.boxes.getOr([]);return r.length>1?(o(n,r,e.start,e.finish),E.some(cw.create(E.some(sw(t,0,t,xc(t))),!0))):E.none()}))},gw=function(n,e,t,r,o){var u=function(n){return o.clearBeforeUpdate(t),o.selectRange(t,n.boxes,n.start,n.finish),n.boxes};return cu(r,n,e,o.firstSelectedSelector,o.lastSelectedSelector).map(u)},pw=function(n,e){return{item:n,mode:e}},vw=function(n,e,t,r){return void 0===r&&(r=hw),n.property().parent(e).map((function(n){return pw(n,r)}))},hw=function(n,e,t,r){return void 0===r&&(r=bw),t.sibling(n,e).map((function(n){return pw(n,r)}))},bw=function(n,e,t,r){void 0===r&&(r=bw);var o=n.property().children(e),u=t.first(o);return u.map((function(n){return pw(n,r)}))},ww=[{current:vw,next:hw,fallback:E.none()},{current:hw,next:bw,fallback:E.some(vw)},{current:bw,next:bw,fallback:E.some(hw)}],yw=function(n,e,t,r,o){void 0===o&&(o=ww);var u=V(o,(function(n){return n.current===t}));return u.bind((function(t){return t.current(n,e,r,t.next).orThunk((function(){return t.fallback.bind((function(t){return yw(n,e,t,r)}))}))}))},Cw=function(){var n=function(n,e){return n.query().prevSibling(e)},e=function(n){return n.length>0?E.some(n[n.length-1]):E.none()};return{sibling:n,first:e}},Tw=function(){var n=function(n,e){return n.query().nextSibling(e)},e=function(n){return n.length>0?E.some(n[0]):E.none()};return{sibling:n,first:e}},Sw={left:Cw,right:Tw},xw=function(n,e,t,r,o,u){var i=yw(n,e,r,o);return i.bind((function(e){return u(e.item)?E.none():t(e.item)?E.some(e.item):xw(n,e.item,t,e.mode,o,u)}))},Rw=function(n,e,t,r){return xw(n,e,t,hw,Sw.left(),r)},Aw=function(n,e,t,r){return xw(n,e,t,hw,Sw.right(),r)},Dw=function(n){return function(e){return 0===n.property().children(e).length}},Ow=function(n,e,t){return kw(n,e,Dw(n),t)},Ew=function(n,e,t){return Mw(n,e,Dw(n),t)},kw=Rw,Mw=Aw,Iw=Ho(),Nw=function(n,e){return Ow(Iw,n,e)},Bw=function(n,e){return Ew(Iw,n,e)},Pw=function(n,e,t){return kw(Iw,n,e,t)},jw=function(n,e,t){return Mw(Iw,n,e,t)},Lw=function(n,e,t){return Ft(n,e,t).isSome()},_w=su.generate([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),zw=function(n,e,t){var r=n.getRect(e),o=n.getRect(t);return o.right>r.left&&o.left<r.right},Ww=function(n){return Xt(n,"tr")},Fw=function(n,e,t,r,o,u,i){return Xt(r,"td,th",i).bind((function(t){return Xt(e,"td,th",i).map((function(e){return Se(t,e)?Se(r,t)&&xc(t)===o?u(e):_w.none("in same cell"):Zo(Ww,[t,e]).fold((function(){return zw(n,e,t)?_w.success():u(e)}),(function(n){return u(e)}))}))})).getOr(_w.none("default"))},Uw=function(n,e,t,r,o){return n.fold(e,t,r,o)},Hw=on(on({},_w),{verify:Fw,cata:Uw}),$w=function(n,e,t,r){return{parent:n,children:e,element:t,index:r}},Vw=function(n){return pt(n).bind((function(e){var t=yt(e);return qw(t,n).map((function(r){return $w(e,t,n,r)}))}))},qw=function(n,e){return q(n,y(Se,e))},Kw=function(n){return"br"===rt(n)},Xw=function(n,e,t){return e(n,t).bind((function(n){return at(n)&&0===zo(n).trim().length?Xw(n,e,t):E.some(n)}))},Gw=function(n,e,t){return t.traverse(e).orThunk((function(){return Xw(e,t.gather,n)})).map(t.relative)},Yw=function(n,e){return Ct(n,e).filter(Kw).orThunk((function(){return Ct(n,e-1).filter(Kw)}))},Jw=function(n,e,t,r){return Yw(e,t).bind((function(e){return r.traverse(e).fold((function(){return Xw(e,r.gather,n).map(r.relative)}),(function(n){return Vw(n).map((function(n){return Wh.on(n.parent,n.index)}))}))}))},Qw=function(n,e,t,r){var o=Kw(e)?Gw(n,e,r):Jw(n,e,t,r);return o.map((function(n){return{start:n,finish:n}}))},Zw=function(n){return Hw.cata(n,(function(n){return E.none()}),(function(){return E.none()}),(function(n){return E.some(im(n,0))}),(function(n){return E.some(im(n,xc(n)))}))},ny=function(n,e){return{left:n.left,top:n.top+e,right:n.right,bottom:n.bottom+e}},ey=function(n,e){return{left:n.left,top:n.top-e,right:n.right,bottom:n.bottom-e}},ty=function(n,e,t){return{left:n.left+e,top:n.top+t,right:n.right+e,bottom:n.bottom+t}},ry=function(n){return n.top},oy=function(n){return n.bottom},uy=function(n,e,t){return t>=0&&t<xc(e)?n.getRangedRect(e,t,e,t+1):t>0?n.getRangedRect(e,t-1,e,t):E.none()},iy=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom}},cy=function(n,e){return E.some(n.getRect(e))},ay=function(n,e,t){return ct(e)?cy(n,e).map(iy):at(e)?uy(n,e,t).map(iy):E.none()},ly=function(n,e){return ct(e)?cy(n,e).map(iy):at(e)?n.getRangedRect(e,0,e,xc(e)).map(iy):E.none()},fy=5,sy=100,my=su.generate([{none:[]},{retry:["caret"]}]),dy=function(n,e){return n.left<e.left||Math.abs(e.right-n.left)<1||n.left>e.right},gy=function(n,e,t){return Ut(e,cg).fold(S,(function(e){return ly(n,e).exists((function(n){return dy(t,n)}))}))},py=function(n,e,t,r,o){var u=ny(o,fy);return Math.abs(t.bottom-r.bottom)<1||t.top>o.bottom?my.retry(u):t.top===o.bottom?my.retry(ny(o,1)):gy(n,e,o)?my.retry(ty(u,fy,0)):my.none()},vy=function(n,e,t,r,o){var u=ey(o,fy);return Math.abs(t.top-r.top)<1||t.bottom<o.top?my.retry(u):t.bottom===o.top?my.retry(ey(o,1)):gy(n,e,o)?my.retry(ty(u,fy,0)):my.none()},hy={point:ry,adjuster:vy,move:ey,gather:Nw},by={point:oy,adjuster:py,move:ny,gather:Bw},wy=function(n,e,t){return n.elementFromPoint(e,t).filter((function(n){return"table"===rt(n)})).isSome()},yy=function(n,e,t,r,o){return Cy(n,e,t,e.move(r,fy),o)},Cy=function(n,e,t,r,o){return 0===o?E.some(r):wy(n,r.left,e.point(r))?yy(n,e,t,r,o-1):n.situsFromPoint(r.left,e.point(r)).bind((function(u){return u.start.fold(E.none,(function(u){return ly(n,u).bind((function(i){return e.adjuster(n,u,i,t,r).fold(E.none,(function(r){return Cy(n,e,t,r,o-1)}))})).orThunk((function(){return E.some(r)}))}),E.none)}))},Ty=function(n,e){return n.situsFromPoint(e.left,e.bottom+fy)},Sy=function(n,e){return n.situsFromPoint(e.left,e.top-fy)},xy=function(n,e,t){return n.point(e)>t.getInnerHeight()?E.some(n.point(e)-t.getInnerHeight()):n.point(e)<0?E.some(-n.point(e)):E.none()},Ry=function(n,e,t){var r=n.move(t,fy),o=Cy(e,n,t,r,sy).getOr(r);return xy(n,o,e).fold((function(){return e.situsFromPoint(o.left,n.point(o))}),(function(t){return e.scrollBy(0,t),e.situsFromPoint(o.left,n.point(o)-t)}))},Ay={tryUp:y(Ry,hy),tryDown:y(Ry,by),ieTryUp:Sy,ieTryDown:Ty,getJumpSize:h(fy)},Dy=20,Oy=function(n,e,t){return n.getSelection().bind((function(r){return Qw(e,r.finish,r.foffset,t).fold((function(){return E.some(im(r.finish,r.foffset))}),(function(o){var u=n.fromSitus(o),i=Hw.verify(n,r.finish,r.foffset,u.finish,u.foffset,t.failure,e);return Zw(i)}))}))},Ey=function(n,e,t,r,o,u){return 0===u?E.none():Iy(n,e,t,r,o).bind((function(i){var c=n.fromSitus(i),a=Hw.verify(n,t,r,c.finish,c.foffset,o.failure,e);return Hw.cata(a,(function(){return E.none()}),(function(){return E.some(i)}),(function(i){return Se(t,i)&&0===r?ky(n,t,r,ey,o):Ey(n,e,i,0,o,u-1)}),(function(i){return Se(t,i)&&r===xc(i)?ky(n,t,r,ny,o):Ey(n,e,i,xc(i),o,u-1)}))}))},ky=function(n,e,t,r,o){return ay(n,e,t).bind((function(e){return My(n,o,r(e,Ay.getJumpSize()))}))},My=function(n,e,t){var r=ue().browser;return r.isChrome()||r.isSafari()||r.isFirefox()||r.isEdge()?e.otherRetry(n,t):r.isIE()?e.ieRetry(n,t):E.none()},Iy=function(n,e,t,r,o){return ay(n,t,r).bind((function(e){return My(n,o,e)}))},Ny=function(n,e,t){return Oy(n,e,t).bind((function(r){return Ey(n,e,r.element,r.offset,t,Dy).map(n.fromSitus)}))},By=function(n,e){return Lw(n,(function(n){return pt(n).exists((function(n){return Se(n,e)}))}))},Py=function(n,e,t,r,o){return Xt(r,"td,th",e).bind((function(r){return Xt(r,"table",e).bind((function(u){return By(o,u)?Ny(n,e,t).bind((function(n){return Xt(n.finish,"td,th",e).map((function(e){return{start:r,finish:e,range:n}}))})):E.none()}))}))},jy=function(n,e,t,r,o,u){return ue().browser.isIE()?E.none():u(r,e).orThunk((function(){return Py(n,e,t,r,o).map((function(n){var e=n.range;return cw.create(E.some(sw(e.start,e.soffset,e.finish,e.foffset)),!0)}))}))},Ly=function(n,e){return Xt(n,"tr",e).bind((function(n){return Xt(n,"table",e).bind((function(t){var r=zt(t,"tr");return Se(n,r[0])?Pw(t,(function(n){return Ec(n).isSome()}),e).map((function(n){var e=xc(n);return cw.create(E.some(sw(n,e,n,e)),!0)})):E.none()}))}))},_y=function(n,e){return Xt(n,"tr",e).bind((function(n){return Xt(n,"table",e).bind((function(t){var r=zt(t,"tr");return Se(n,r[r.length-1])?jw(t,(function(n){return Oc(n).isSome()}),e).map((function(n){return cw.create(E.some(sw(n,0,n,0)),!0)})):E.none()}))}))},zy=function(n,e,t,r,o,u,i){return Py(n,t,r,o,u).bind((function(n){return dw(e,t,n.start,n.finish,i)}))},Wy=function(n,e){return Xt(n,"td,th",e)},Fy=function(n,e,t,r){var o=bv(),u=o.clear,i=function(u){o.on((function(o){r.clearBeforeUpdate(e),Wy(u.target,t).each((function(i){tu(o,i,t).each((function(t){var o=t.boxes.getOr([]);if(1===o.length){var c=o[0],a="false"===xf(c),l=rr(Tf(u.target),c,Se);a&&l&&(r.selectRange(e,o,c,c),n.selectContents(c))}else o.length>1&&(r.selectRange(e,o,t.start,t.finish),n.selectContents(i))}))}))}))},c=function(n){r.clear(e),Wy(n.target,t).each(o.set)},a=function(n){i(n)},l=function(n){i(n),u()};return{clearstate:u,mousedown:c,mouseover:a,mouseup:l}},Uy={traverse:wt,gather:Bw,relative:Wh.before,otherRetry:Ay.tryDown,ieRetry:Ay.ieTryDown,failure:Hw.failedDown},Hy={traverse:bt,gather:Nw,relative:Wh.before,otherRetry:Ay.tryUp,ieRetry:Ay.ieTryUp,failure:Hw.failedUp},$y=function(n){return function(e){return e===n}},Vy=$y(38),qy=$y(40),Ky=function(n){return n>=37&&n<=40},Xy={isBackward:$y(37),isForward:$y(39)},Gy={isBackward:$y(39),isForward:$y(37)},Yy=function(n){var e=void 0!==n?n.dom:document,t=e.body.scrollLeft||e.documentElement.scrollLeft,r=e.body.scrollTop||e.documentElement.scrollTop;return Qu(t,r)},Jy=function(n,e,t){var r=void 0!==t?t.dom:document,o=r.defaultView;o&&o.scrollBy(n,e)},Qy=function(n){var e=function(e,t){return be.fromPoint(be.fromDom(n.document),e,t)},t=function(n){return n.dom.getBoundingClientRect()},r=function(e,t,r,o){var u=Gh.exact(e,t,r,o);return Yb(n,u)},o=function(){return Gb(n).map((function(e){return fw(n,e)}))},u=function(e){var t=Gh.relative(e.start,e.finish);return fw(n,t)},i=function(e,t){return Jb(n,e,t).map((function(n){return lw.create(n.start,n.soffset,n.finish,n.foffset)}))},c=function(){Qb(n)},a=function(e){void 0===e&&(e=!1),Gb(n).each((function(t){return t.fold((function(n){return n.collapse(e)}),(function(t,r){var o=e?t:r;Hb(n,o,o)}),(function(t,r,o,u){var i=e?t:o,c=e?r:u;Ub(n,i,c,i,c)}))}))},l=function(e){Kb(n,e,!1)},f=function(e){Kb(n,e)},s=function(e){Ub(n,e.start,e.soffset,e.finish,e.foffset)},m=function(e,t){Hb(n,e,t)},d=function(){return n.innerHeight},g=function(){var e=Yy(be.fromDom(n.document));return e.top},p=function(e,t){Jy(e,t,be.fromDom(n.document))};return{elementFromPoint:e,getRect:t,getRangedRect:r,getSelection:o,fromSitus:u,situsFromPoint:i,clearSelection:c,collapseSelection:a,setSelection:s,setRelativeSelection:m,selectNode:l,selectContents:f,getInnerHeight:d,getScrollY:g,scrollBy:p}},Zy=function(n,e){return{rows:n,cols:e}},nC=function(n,e,t,r){var o=Qy(n),u=Fy(o,e,t,r);return{clearstate:u.clearstate,mousedown:u.mousedown,mouseover:u.mouseover,mouseup:u.mouseup}},eC=function(n,e,t,r){var o=Qy(n),u=function(){return r.clear(e),E.none()},i=function(n,i,c,a,l,f){var s=n.raw,m=s.which,d=!0===s.shiftKey,g=ru(e,r.selectedSelector).fold((function(){return Ky(m)&&!d&&r.clearBeforeUpdate(e),qy(m)&&d?y(zy,o,e,t,Uy,a,i,r.selectRange):Vy(m)&&d?y(zy,o,e,t,Hy,a,i,r.selectRange):qy(m)?y(jy,o,t,Uy,a,i,_y):Vy(m)?y(jy,o,t,Hy,a,i,Ly):E.none}),(function(n){var t=function(t){return function(){var u=rn(t,(function(t){return gw(t.rows,t.cols,e,n,r)}));return u.fold((function(){return uu(e,r.firstSelectedSelector,r.lastSelectedSelector).map((function(n){var t=qy(m)||f.isForward(m)?Wh.after:Wh.before;return o.setRelativeSelection(Wh.on(n.first,0),t(n.table)),r.clear(e),cw.create(E.none(),!0)}))}),(function(n){return E.some(cw.create(E.none(),!0))}))}};return qy(m)&&d?t([Zy(1,0)]):Vy(m)&&d?t([Zy(-1,0)]):f.isBackward(m)&&d?t([Zy(0,-1),Zy(-1,0)]):f.isForward(m)&&d?t([Zy(0,1),Zy(1,0)]):Ky(m)&&!d?u:E.none}));return g()},c=function(n,o,u,i,c){return ru(e,r.selectedSelector).fold((function(){var a=n.raw,l=a.which,f=!0===a.shiftKey;return f&&Ky(l)?mw(e,t,o,u,i,c,r.selectRange):E.none()}),E.none)};return{keydown:i,keyup:c}},tC=function(n,e,t,r){var o=Qy(n);return function(n,u){r.clearBeforeUpdate(e),tu(n,u,t).each((function(n){var t=n.boxes.getOr([]);r.selectRange(e,t,n.start,n.finish),o.selectContents(u),o.collapseSelection()}))}},rC=function(n,e){_(e,(function(e){ff(n,e)}))},oC=function(n){return function(e){af(e,n)}},uC=function(n){return function(e){rC(e,n)}},iC=function(n){var e=oC(n.selected),t=uC([n.selected,n.lastSelected,n.firstSelected]),r=function(e){var r=zt(e,n.selectedSelector);_(r,t)},o=function(t,o,u,i){r(t),_(o,e),af(u,n.firstSelected),af(i,n.lastSelected)};return{clearBeforeUpdate:r,clear:r,selectRange:o,selectedSelector:n.selectedSelector,firstSelectedSelector:n.firstSelectedSelector,lastSelectedSelector:n.lastSelectedSelector}},cC=function(n,e,t){var r=function(e){er(e,n.selected),er(e,n.firstSelected),er(e,n.lastSelected)},o=function(e){Yt(e,n.selected,"1")},u=function(n){i(n),t()},i=function(e){var t=zt(e,n.selectedSelector+","+n.firstSelectedSelector+","+n.lastSelectedSelector);_(t,r)},c=function(t,r,i,c){u(t),_(r,o),Yt(i,n.firstSelected,"1"),Yt(c,n.lastSelected,"1"),e(r,i,c)};return{clearBeforeUpdate:i,clear:u,selectRange:c,selectedSelector:n.selectedSelector,firstSelectedSelector:n.firstSelectedSelector,lastSelectedSelector:n.lastSelectedSelector}},aC={byClass:iC,byAttr:cC},lC=function(n,e){var t=n.slice(0,e[e.length-1].row+1),r=Fm(t);return X(r,(function(n){var t=n.cells.slice(0,e[e.length-1].column+1);return L(t,(function(n){return n.element}))}))},fC=function(n,e){var t=n.slice(e[0].row+e[0].rowspan-1,n.length),r=Fm(t);return X(r,(function(n){var t=n.cells.slice(e[0].column+e[0].colspan-1,n.cells.length);return L(t,(function(n){return n.element}))}))},sC=function(n,e,t){var r=lo.fromTable(n),o=Gm(r,e);return o.map((function(n){var e=zm(r,t,!1),o=lC(e,n),u=fC(e,n);return{upOrLeftCells:o,downOrRightCells:u}}))},mC=tinymce.util.Tools.resolve("tinymce.Env"),dC=function(n){return!1===sf(be.fromDom(n.target),"ephox-snooker-resizer-bar")};function gC(n,e,t){var r=function(e,r,o){t.targets().each((function(t){var u=Ir(r);u.each((function(u){var i=Fs(n),c=Wc(g,be.fromDom(n.getDoc()),i),a=sC(u,t,c);ts(n,e,r,o,a)}))}))},o=function(){return rs(n)},u=aC.byAttr(ga,r,o);return n.on("init",(function(t){var r=n.getWin(),o=Vc(n),i=Xc(n),c=function(){var e=n.selection,t=be.fromDom(e.getStart()),r=be.fromDom(e.getEnd()),i=Zo(Ir,[t,r]);i.fold((function(){return u.clear(o)}),g)},a=nC(r,o,i,u),l=eC(r,o,i,u),f=tC(r,o,i,u),s=function(n){return!0===n.raw.shiftKey};n.on("TableSelectorChange",(function(n){return f(n.start,n.finish)}));var m=function(e,t){s(e)&&(t.kill&&e.kill(),t.selection.each((function(e){var t=Gh.relative(e.start,e.finish),o=fb(r,t);n.selection.setRng(o)})))},d=function(e){var t=Zl(e);if(t.raw.shiftKey&&Ky(t.raw.which)){var r=n.selection.getRng(),o=be.fromDom(r.startContainer),u=be.fromDom(r.endContainer);l.keyup(t,o,r.startOffset,u,r.endOffset).each((function(n){m(t,n)}))}},p=function(t){var r=Zl(t);e().each((function(n){return n.hideBars()}));var o=n.selection.getRng(),u=be.fromDom(o.startContainer),i=be.fromDom(o.endContainer),c=Vu(Xy,Gy)(be.fromDom(n.selection.getStart()));l.keydown(r,u,o.startOffset,i,o.endOffset,c).each((function(n){m(r,n)})),e().each((function(n){return n.showBars()}))},v=function(n){return 0===n.button},h=function(n){return void 0===n.buttons||(!(!mC.browser.isEdge()||0!==n.buttons)||0!==(1&n.buttons))},b=function(n){a.clearstate()},w=function(n){v(n)&&dC(n)&&a.mousedown(Zl(n))},y=function(n){h(n)&&dC(n)&&a.mouseover(Zl(n))},C=function(n){v(n)&&dC(n)&&a.mouseup(Zl(n))},T=function(){var n=pv(be.fromDom(o)),e=pv(0),t=function(t){var r=be.fromDom(t.target);if("td"===rt(r)||"th"===rt(r)){var o=n.get(),u=e.get();Se(o,r)&&t.timeStamp-u<300&&(t.preventDefault(),f(r,r))}n.set(r),e.set(t.timeStamp)};return{touchEnd:t}},S=T();n.on("dragstart",b),n.on("mousedown",w),n.on("mouseover",y),n.on("mouseup",C),n.on("touchend",S.touchEnd),n.on("keyup",d),n.on("keydown",p),n.on("NodeChange",c)})),{clear:u.clear}}var pC=function(n,e){return qt(n,e).isSome()},vC=function(n,e){var t=pv(E.none()),r=pv([]),o=E.none(),u=st("caption"),i=function(n){return o.forall((function(e){return!e[n]}))},c=function(){return Ca(ea(n),Xc(n))},a=function(){return Ca(ta(n),Xc(n))},l=function(){return c().bind((function(n){return cr(ur(Ir(n),a().bind(Ir),(function(t,r){return Se(t,r)?u(n)?E.some(pa(n)):E.some(va(e,t,n)):E.none()})))}))},f=function(n){var e=Ir(n.element);return e.map((function(e){var t=lo.fromTable(e),r=Gm(t,n).getOr([]),o=H(r,(function(n,e){return e.isLocked&&(n.onAny=!0,0===e.column?n.onFirst=!0:e.column+e.colspan>=t.grid.columns&&(n.onLast=!0)),n}),{onAny:!1,onFirst:!1,onLast:!1});return{mergeable:Zm(t,n).isSome(),unmergeable:nd(t,n).isSome(),locked:o}}))},s=function(){t.set(cn(l)()),o=t.get().bind(f),_(r.get(),(function(n){return n()}))},m=function(n){return n(),r.set(r.get().concat([n])),function(){r.set(F(r.get(),(function(e){return e!==n})))}},d=function(n,e){return m((function(){return t.get().fold((function(){n.setDisabled(!0)}),(function(t){n.setDisabled(e(t))}))}))},g=function(n,e,r){return m((function(){return t.get().fold((function(){n.setDisabled(!0),n.setActive(!1)}),(function(t){n.setDisabled(e(t)),n.setActive(r(t))}))}))},p=function(n){return o.exists((function(e){return e.locked[n]}))},v=function(n){return d(n,(function(n){return!1}))},h=function(n){return d(n,(function(n){return u(n.element)}))},b=function(n){return function(e){return d(e,(function(e){return u(e.element)||p(n)}))}},w=function(n){return function(e){return d(e,(function(e){return u(e.element)||n().isNone()}))}},y=function(n,e){return function(t){return d(t,(function(t){return u(t.element)||n().isNone()||p(e)}))}},C=function(n){return d(n,(function(n){return i("mergeable")}))},T=function(n){return d(n,(function(n){return i("unmergeable")}))},x=function(e){return g(e,S,(function(e){var t=Ir(e.element,Xc(n));return t.exists((function(n){return pC(n,"caption")}))}))},R=function(e,t){return function(r){return g(r,(function(n){return u(n.element)}),(function(){return n.queryCommandValue(e)===t}))}},A=R("mceTableRowType","header"),D=R("mceTableColType","th");return n.on("NodeChange ExecCommand TableSelectorChange",s),{onSetupTable:v,onSetupCellOrRow:h,onSetupColumn:b,onSetupPasteable:w,onSetupPasteableColumn:y,onSetupMergeable:C,onSetupUnmergeable:T,resetTargets:s,onSetupTableWithCaption:x,onSetupTableRowHeaders:A,onSetupTableColumnHeaders:D,targets:t.get}},hC=function(n,e,t,r){n.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(n){return n("inserttable | cell row column | advtablesort | tableprops deletetable")}});var o=function(e){return function(){return n.execCommand(e)}};n.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:o("mceTableProps"),icon:"table",onSetup:t.onSetupTable}),n.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:o("mceTableDelete"),icon:"table-delete-table",onSetup:t.onSetupTable}),n.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:o("mceTableCellProps"),icon:"table-cell-properties",onSetup:t.onSetupCellOrRow}),n.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:o("mceTableMergeCells"),icon:"table-merge-cells",onSetup:t.onSetupMergeable}),n.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:o("mceTableSplitCells"),icon:"table-split-cells",onSetup:t.onSetupUnmergeable}),n.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:o("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:t.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:o("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:t.onSetupCellOrRow}),n.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:o("mceTableDeleteRow"),icon:"table-delete-row",onSetup:t.onSetupCellOrRow}),n.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:o("mceTableRowProps"),icon:"table-row-properties",onSetup:t.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:o("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:t.onSetupColumn("onFirst")}),n.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:o("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:t.onSetupColumn("onLast")}),n.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:o("mceTableDeleteCol"),icon:"table-delete-column",onSetup:t.onSetupColumn("onAny")}),n.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",icon:"cut-row",onAction:o("mceTableCutRow"),onSetup:t.onSetupCellOrRow}),n.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",icon:"duplicate-row",onAction:o("mceTableCopyRow"),onSetup:t.onSetupCellOrRow}),n.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",icon:"paste-row-before",onAction:o("mceTablePasteRowBefore"),onSetup:t.onSetupPasteable(r.getRows)}),n.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",icon:"paste-row-after",onAction:o("mceTablePasteRowAfter"),onSetup:t.onSetupPasteable(r.getRows)}),n.ui.registry.addButton("tablecutcol",{tooltip:"Cut column",icon:"cut-column",onAction:o("mceTableCutCol"),onSetup:t.onSetupColumn("onAny")}),n.ui.registry.addButton("tablecopycol",{tooltip:"Copy column",icon:"duplicate-column",onAction:o("mceTableCopyCol"),onSetup:t.onSetupColumn("onAny")}),n.ui.registry.addButton("tablepastecolbefore",{tooltip:"Paste column before",icon:"paste-column-before",onAction:o("mceTablePasteColBefore"),onSetup:t.onSetupPasteableColumn(r.getColumns,"onFirst")}),n.ui.registry.addButton("tablepastecolafter",{tooltip:"Paste column after",icon:"paste-column-after",onAction:o("mceTablePasteColAfter"),onSetup:t.onSetupPasteableColumn(r.getColumns,"onLast")}),n.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:o("mceInsertTable"),icon:"table"});var u=xv(Ms(n));0!==u.length&&n.ui.registry.addMenuButton("tableclass",{icon:"table-classes",tooltip:"Table styles",fetch:Rv(n,e,u,"tableclass",(function(e){return n.execCommand("mceTableToggleClass",!1,e)})),onSetup:t.onSetupTable});var i=xv(Es(n));0!==i.length&&n.ui.registry.addMenuButton("tablecellclass",{icon:"table-cell-classes",tooltip:"Cell styles",fetch:Rv(n,e,i,"tablecellclass",(function(e){return n.execCommand("mceTableCellToggleClass",!1,e)})),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuButton("tablecellvalign",{icon:"vertical-align",tooltip:"Vertical align",fetch:Rv(n,e,nv,"tablecellverticalalign",Sv(n,"vertical-align")),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuButton("tablecellborderwidth",{icon:"border-width",tooltip:"Border width",fetch:Rv(n,e,hs(n),"tablecellborderwidth",Sv(n,"border-width")),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuButton("tablecellborderstyle",{icon:"border-style",tooltip:"Border style",fetch:Rv(n,e,bs(n),"tablecellborderstyle",Sv(n,"border-style")),onSetup:t.onSetupCellOrRow}),n.ui.registry.addToggleButton("tablecaption",{tooltip:"Table caption",onAction:o("mceTableToggleCaption"),icon:"table-caption",onSetup:t.onSetupTableWithCaption}),n.ui.registry.addMenuButton("tablecellbackgroundcolor",{icon:"cell-background-color",tooltip:"Background color",fetch:function(e){return e(Av(n,Hs(n),"background-color"))},onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuButton("tablecellbordercolor",{icon:"cell-border-color",tooltip:"Border color",fetch:function(e){return e(Av(n,$s(n),"border-color"))},onSetup:t.onSetupCellOrRow}),n.ui.registry.addToggleButton("tablerowheader",{tooltip:"Row header",icon:"table-top-header",onAction:Dv(n),onSetup:t.onSetupTableRowHeaders}),n.ui.registry.addToggleButton("tablecolheader",{tooltip:"Column header",icon:"table-left-header",onAction:Ov(n),onSetup:t.onSetupTableColumnHeaders})},bC=function(n){var e=function(e){return n.dom.is(e,"table")&&n.getBody().contains(e)},t=Ps(n);t.length>0&&n.ui.registry.addContextToolbar("table",{predicate:e,items:t,scope:"node",position:"node"})},wC=function(n,e,t,r){var o=function(e){return function(){return n.execCommand(e)}},u=function(e){n.execCommand("mceInsertTable",!1,{rows:e.numRows,columns:e.numColumns})},i={text:"Table properties",onSetup:t.onSetupTable,onAction:o("mceTableProps")},c={text:"Delete table",icon:"table-delete-table",onSetup:t.onSetupTable,onAction:o("mceTableDelete")};n.ui.registry.addMenuItem("tableinsertrowbefore",{text:"Insert row before",icon:"table-insert-row-above",onAction:o("mceTableInsertRowBefore"),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuItem("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",onAction:o("mceTableInsertRowAfter"),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuItem("tabledeleterow",{text:"Delete row",icon:"table-delete-row",onAction:o("mceTableDeleteRow"),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuItem("tablerowprops",{text:"Row properties",icon:"table-row-properties",onAction:o("mceTableRowProps"),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuItem("tablecutrow",{text:"Cut row",icon:"cut-row",onAction:o("mceTableCutRow"),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuItem("tablecopyrow",{text:"Copy row",icon:"duplicate-row",onAction:o("mceTableCopyRow"),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuItem("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",onAction:o("mceTablePasteRowBefore"),onSetup:t.onSetupPasteable(r.getRows)}),n.ui.registry.addMenuItem("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",onAction:o("mceTablePasteRowAfter"),onSetup:t.onSetupPasteable(r.getRows)});var a={type:"nestedmenuitem",text:"Row",getSubmenuItems:h("tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter")};n.ui.registry.addMenuItem("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",onAction:o("mceTableInsertColBefore"),onSetup:t.onSetupColumn("onFirst")}),n.ui.registry.addMenuItem("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",onAction:o("mceTableInsertColAfter"),onSetup:t.onSetupColumn("onLast")}),n.ui.registry.addMenuItem("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",onAction:o("mceTableDeleteCol"),onSetup:t.onSetupColumn("onAny")}),n.ui.registry.addMenuItem("tablecutcolumn",{text:"Cut column",icon:"cut-column",onAction:o("mceTableCutCol"),onSetup:t.onSetupColumn("onAny")}),n.ui.registry.addMenuItem("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",onAction:o("mceTableCopyCol"),onSetup:t.onSetupColumn("onAny")}),n.ui.registry.addMenuItem("tablepastecolumnbefore",{text:"Paste column before",icon:"paste-column-before",onAction:o("mceTablePasteColBefore"),onSetup:t.onSetupPasteableColumn(r.getColumns,"onFirst")}),n.ui.registry.addMenuItem("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",onAction:o("mceTablePasteColAfter"),onSetup:t.onSetupPasteableColumn(r.getColumns,"onLast")});var l={type:"nestedmenuitem",text:"Column",getSubmenuItems:h("tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter")};n.ui.registry.addMenuItem("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",onAction:o("mceTableCellProps"),onSetup:t.onSetupCellOrRow}),n.ui.registry.addMenuItem("tablemergecells",{text:"Merge cells",icon:"table-merge-cells",onAction:o("mceTableMergeCells"),onSetup:t.onSetupMergeable}),n.ui.registry.addMenuItem("tablesplitcells",{text:"Split cell",icon:"table-split-cells",onAction:o("mceTableSplitCells"),onSetup:t.onSetupUnmergeable});var f={type:"nestedmenuitem",text:"Cell",getSubmenuItems:h("tablecellprops tablemergecells tablesplitcells")};!1===Ds(n)?n.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:o("mceInsertTable")}):n.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:u}]}}),n.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:o("mceInsertTable")}),n.ui.registry.addMenuItem("tableprops",i),n.ui.registry.addMenuItem("deletetable",c),n.ui.registry.addNestedMenuItem("row",a),n.ui.registry.addNestedMenuItem("column",l),n.ui.registry.addNestedMenuItem("cell",f),n.ui.registry.addContextMenu("table",{update:function(){return t.resetTargets(),t.targets().fold(h(""),(function(n){return"caption"===rt(n.element)?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"}))}});var s=xv(Ms(n));0!==s.length&&n.ui.registry.addNestedMenuItem("tableclass",{icon:"table-classes",text:"Table styles",getSubmenuItems:function(){return Tv(n,e,s,"tableclass",(function(e){return n.execCommand("mceTableToggleClass",!1,e)}))},onSetup:t.onSetupTable});var m=xv(Es(n));0!==m.length&&n.ui.registry.addNestedMenuItem("tablecellclass",{icon:"table-cell-classes",text:"Cell styles",getSubmenuItems:function(){return Tv(n,e,m,"tablecellclass",(function(e){return n.execCommand("mceTableCellToggleClass",!1,e)}))},onSetup:t.onSetupCellOrRow}),n.ui.registry.addNestedMenuItem("tablecellvalign",{icon:"vertical-align",text:"Vertical align",getSubmenuItems:function(){return Tv(n,e,nv,"tablecellverticalalign",Sv(n,"vertical-align"))},onSetup:t.onSetupCellOrRow}),n.ui.registry.addNestedMenuItem("tablecellborderwidth",{icon:"border-width",text:"Border width",getSubmenuItems:function(){return Tv(n,e,hs(n),"tablecellborderwidth",Sv(n,"border-width"))},onSetup:t.onSetupCellOrRow}),n.ui.registry.addNestedMenuItem("tablecellborderstyle",{icon:"border-style",text:"Border style",getSubmenuItems:function(){return Tv(n,e,bs(n),"tablecellborderstyle",Sv(n,"border-style"))},onSetup:t.onSetupCellOrRow}),n.ui.registry.addToggleMenuItem("tablecaption",{icon:"table-caption",text:"Table caption",onAction:o("mceTableToggleCaption"),onSetup:t.onSetupTableWithCaption}),n.ui.registry.addNestedMenuItem("tablecellbackgroundcolor",{icon:"cell-background-color",text:"Background color",getSubmenuItems:function(){return Av(n,Hs(n),"background-color")},onSetup:t.onSetupCellOrRow}),n.ui.registry.addNestedMenuItem("tablecellbordercolor",{icon:"cell-border-color",text:"Border color",getSubmenuItems:function(){return Av(n,$s(n),"border-color")},onSetup:t.onSetupCellOrRow}),n.ui.registry.addToggleMenuItem("tablerowheader",{text:"Row header",icon:"table-top-header",onAction:Dv(n),onSetup:t.onSetupTableRowHeaders}),n.ui.registry.addToggleMenuItem("tablecolheader",{text:"Column header",icon:"table-left-header",onAction:Ov(n),onSetup:t.onSetupTableColumnHeaders})},yC=function(n){var e=hu((function(){return Vc(n)}),(function(){return Ta(ea(n),Xc(n))}),ga.selectedSelector),t=vC(n,e),r=um(n),o=gC(n,r.lazyResize,t),u=Tp(n,o,r.lazyWire),i=yh();return bh(n,u,o,e,i),wh(n,u,e),Oa(n,e,u),wC(n,e,t,i),hC(n,e,t,i),bC(n),n.on("PreInit",(function(){n.serializer.addTempAttr(ga.firstSelected),n.serializer.addTempAttr(ga.lastSelected),xh(n)})),Ts(n)&&n.on("keydown",(function(e){uw(e,n,o)})),n.on("remove",(function(){r.destroy()})),Up(n,i,r,t)};function CC(){bu.add("table",yC)}CC()})()},e8b0:function(n,e){(function(){"use strict";var n=tinymce.util.Tools.resolve("tinymce.PluginManager"),e=function(n){return n},t=function(){return t=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t],e)Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n},t.apply(this,arguments)},r="\ufeff",o=function(n){return n.replace(/\uFEFF/g,"")},u=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o)}return r},i="[!-#%-*,-\\/:;?@\\[-\\]_{}¡«·»¿;·՚-՟։֊־׀׃׆׳״؉؊،؍؛؞؟٪-٭۔܀-܍߷-߹࠰-࠾࡞।॥॰෴๏๚๛༄-༒༺-༽྅࿐-࿔࿙࿚၊-၏჻፡-፨᐀᙭᙮᚛᚜᛫-᛭᜵᜶។-៖៘-៚᠀-᠊᥄᥅᨞᨟᪠-᪦᪨-᪭᭚-᭠᯼-᯿᰻-᰿᱾᱿᳓‐-‧‰-⁃⁅-⁑⁓-⁞⁽⁾₍₎〈〉❨-❵⟅⟆⟦-⟯⦃-⦘⧘-⧛⧼⧽⳹-⳼⳾⳿⵰⸀-⸮⸰⸱、-〃〈-】〔-〟〰〽゠・꓾꓿꘍-꘏꙳꙾꛲-꛷꡴-꡷꣎꣏꣸-꣺꤮꤯꥟꧁-꧍꧞꧟꩜-꩟꫞꫟꯫﴾﴿︐-︙︰-﹒﹔-﹡﹣﹨﹪﹫！-＃％-＊，-／：；？＠［-］＿｛｝｟-･]",c={aletter:"[A-Za-zªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԧԱ-Ֆՙա-ևא-תװ-׳ؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘऄ-हऽॐक़-ॡॱ-ॷॹ-ॿঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-ళవ-హఽౘౙౠౡಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൠൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆༀཀ-ཇཉ-ཬྈ-ྌႠ-Ⴥა-ჺჼᄀ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏼᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛰᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰᠠ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤜᨀ-ᨖᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᯀ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᳩ-ᳬᳮ-ᳱᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⒶ-ⓩⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⴀ-ⴥⴰ-ⵥⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞⸯ々〻〼ㄅ-ㄭㄱ-ㆎㆠ-ㆺꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚗꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞎꞐꞑꞠ-ꞩꟺ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꨀ-ꨨꩀ-ꩂꩄ-ꩋꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꯀ-ꯢ가-힣ힰ-ퟆퟋ-ퟻﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚﾠ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ]",midnumlet:"[-'\\.‘’․﹒＇．]",midletter:"[:··״‧︓﹕：]",midnum:"[±+*/,;;։،؍٬߸⁄︐︔﹐﹔，；]",numeric:"[0-9٠-٩٫۰-۹߀-߉०-९০-৯੦-੯૦-૯୦-୯௦-௯౦-౯೦-೯൦-൯๐-๙໐-໙༠-༩၀-၉႐-႙០-៩᠐-᠙᥆-᥏᧐-᧙᪀-᪉᪐-᪙᭐-᭙᮰-᮹᱀-᱉᱐-᱙꘠-꘩꣐-꣙꤀-꤉꧐-꧙꩐-꩙꯰-꯹]",cr:"\\r",lf:"\\n",newline:"[\v\f\u2028\u2029]",extend:"[̀-ͯ҃-҉֑-ׇֽֿׁׂׅׄؐ-ًؚ-ٰٟۖ-ۜ۟-۪ۤۧۨ-ܑۭܰ-݊ަ-ް߫-߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ऀ-ःऺ-़ा-ॏ॑-ॗॢॣঁ-ঃ়া-ৄেৈো-্ৗৢৣਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑੰੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୢୣஂா-ூெ-ைொ-்ௗఁ-ఃా-ౄె-ైొ-్ౕౖౢౣಂಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣംഃാ-ൄെ-ൈൊ-്ൗൢൣංඃ්ා-ුූෘ-ෟෲෳัิ-ฺ็-๎ັິ-ູົຼ່-ໍ༹༘༙༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏႚ-ႝ፝-፟ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳា-៓៝᠋-᠍ᢩᤠ-ᤫᤰ-᤻ᦰ-ᧀᧈᧉᨗ-ᨛᩕ-ᩞ᩠-᩿᩼ᬀ-ᬄ᬴-᭄᭫-᭳ᮀ-ᮂᮡ-᯦᮪-᯳ᰤ-᰷᳐-᳔᳒-᳨᳭ᳲ᷀-ᷦ᷼-᷿‌‍⃐-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꙯-꙲꙼꙽꛰꛱ꠂ꠆ꠋꠣ-ꠧꢀꢁꢴ-꣄꣠-꣱ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀ꨩ-ꨶꩃꩌꩍꩻꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꯣ-ꯪ꯬꯭ﬞ︀-️︠-︦ﾞﾟ]",format:"[­؀-؃۝܏឴឵‎‏‪-‮⁠-⁤⁪-⁯\ufeff￹-￻]",katakana:"[〱-〵゛゜゠-ヺー-ヿㇰ-ㇿ㋐-㋾㌀-㍗ｦ-ﾝ]",extendnumlet:"[=_‿⁀⁔︳︴﹍-﹏＿∀-⋿<>]",punctuation:i},a={ALETTER:0,MIDNUMLET:1,MIDLETTER:2,MIDNUM:3,NUMERIC:4,CR:5,LF:6,NEWLINE:7,EXTEND:8,FORMAT:9,KATAKANA:10,EXTENDNUMLET:11,AT:12,OTHER:13},l=[new RegExp(c.aletter),new RegExp(c.midnumlet),new RegExp(c.midletter),new RegExp(c.midnum),new RegExp(c.numeric),new RegExp(c.cr),new RegExp(c.lf),new RegExp(c.newline),new RegExp(c.extend),new RegExp(c.format),new RegExp(c.katakana),new RegExp(c.extendnumlet),new RegExp("@")],f="",s=new RegExp("^"+c.punctuation+"$"),m=/^\s+$/,d=l,g=a.OTHER,p=function(n){for(var e=g,t=d.length,r=0;r<t;++r){var o=d[r];if(o&&o.test(n)){e=r;break}}return e},v=function(n){var e={};return function(t){if(e[t])return e[t];var r=n(t);return e[t]=r,r}},h=function(n){var e=v(p);return u(n,e)},b=function(n,e){var t=n[e],r=n[e+1];if(e<0||e>n.length-1&&0!==e)return!1;if(t===a.ALETTER&&r===a.ALETTER)return!1;var o=n[e+2];if(t===a.ALETTER&&(r===a.MIDLETTER||r===a.MIDNUMLET||r===a.AT)&&o===a.ALETTER)return!1;var u=n[e-1];return(t!==a.MIDLETTER&&t!==a.MIDNUMLET&&r!==a.AT||r!==a.ALETTER||u!==a.ALETTER)&&((t!==a.NUMERIC&&t!==a.ALETTER||r!==a.NUMERIC&&r!==a.ALETTER)&&((t!==a.MIDNUM&&t!==a.MIDNUMLET||r!==a.NUMERIC||u!==a.NUMERIC)&&((t!==a.NUMERIC||r!==a.MIDNUM&&r!==a.MIDNUMLET||o!==a.NUMERIC)&&(t!==a.EXTEND&&t!==a.FORMAT&&u!==a.EXTEND&&u!==a.FORMAT&&r!==a.EXTEND&&r!==a.FORMAT&&((t!==a.CR||r!==a.LF)&&(t===a.NEWLINE||t===a.CR||t===a.LF||(r===a.NEWLINE||r===a.CR||r===a.LF||(t!==a.KATAKANA||r!==a.KATAKANA)&&((r!==a.EXTENDNUMLET||t!==a.ALETTER&&t!==a.NUMERIC&&t!==a.KATAKANA&&t!==a.EXTENDNUMLET)&&((t!==a.EXTENDNUMLET||r!==a.ALETTER&&r!==a.NUMERIC&&r!==a.KATAKANA)&&t!==a.AT)))))))))},w=f,y=m,C=s,T=function(n){return"http"===n||"https"===n},S=function(n,e){var t;for(t=e;t<n.length;t++)if(y.test(n[t]))break;return t},x=function(n,e){var t=S(n,e+1),r=n.slice(e+1,t).join(w);return"://"===r.substr(0,3)?t:e},R=function(n,e,t,r){for(var o=[],u=[],i=0;i<t.length;++i)if(u.push(n[i]),b(t,i)){var c=e[i];if((r.includeWhitespace||!y.test(c))&&(r.includePunctuation||!C.test(c))){var a=i-u.length+1,l=i+1,f=e.slice(a,l).join(w);if(T(f)){var s=x(e,i),m=n.slice(l,s);Array.prototype.push.apply(u,m),i=s}o.push(u)}u=[]}return o},A=function(){return{includeWhitespace:!1,includePunctuation:!1}},D=function(n,e,o){o=t(t({},A()),o);for(var u=[],i=[],c=0;c<n.length;c++){var a=e(n[c]);a!==r&&(u.push(n[c]),i.push(a))}var l=h(i);return R(u,i,l,o)},O=D,E=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),k=function(n,e){var t=e.getBlockElements(),r=e.getShortEndedElements(),u=function(n){return t[n.nodeName]||r[n.nodeName]},i=[],c="",a=new E(n,n);while(n=a.next())3===n.nodeType?c+=o(n.data):u(n)&&c.length&&(i.push(c),c="");return c.length&&i.push(c),i},M=function(n){return n.replace(/\u200B/g,"")},I=function(n){return n.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"_").length},N=function(n,t){var r=M(k(n,t).join("\n"));return O(r.split(""),e).length},B=function(n,e){var t=k(n,e).join("");return I(t)},P=function(n,e){var t=k(n,e).join("").replace(/\s/g,"");return I(t)},j=function(n,e){return function(){return e(n.getBody(),n.schema)}},L=function(n,e){return function(){return e(n.selection.getRng().cloneContents(),n.schema)}},_=function(n){return j(n,N)},z=function(n){return{body:{getWordCount:_(n),getCharacterCount:j(n,B),getCharacterCountWithoutSpaces:j(n,P)},selection:{getWordCount:L(n,N),getCharacterCount:L(n,B),getCharacterCountWithoutSpaces:L(n,P)},getCount:_(n)}},W=function(n,e){n.windowManager.open({title:"Word Count",body:{type:"panel",items:[{type:"table",header:["Count","Document","Selection"],cells:[["Words",String(e.body.getWordCount()),String(e.selection.getWordCount())],["Characters (no spaces)",String(e.body.getCharacterCountWithoutSpaces()),String(e.selection.getCharacterCountWithoutSpaces())],["Characters",String(e.body.getCharacterCount()),String(e.selection.getCharacterCount())]]}]},buttons:[{type:"cancel",name:"close",text:"Close",primary:!0}]})},F=function(n,e){n.addCommand("mceWordCount",(function(){return W(n,e)}))},U=tinymce.util.Tools.resolve("tinymce.util.Delay"),H=function(n,e){n.fire("wordCountUpdate",{wordCount:{words:e.body.getWordCount(),characters:e.body.getCharacterCount(),charactersWithoutSpaces:e.body.getCharacterCountWithoutSpaces()}})},$=function(n,e){H(n,e)},V=function(n,e,t){var r=U.debounce((function(){return $(n,e)}),t);n.on("init",(function(){$(n,e),U.setEditorTimeout(n,(function(){n.on("SetContent BeforeAddUndo Undo Redo ViewUpdate keyup",r)}),0)}))},q=function(n){var e=function(){return n.execCommand("mceWordCount")};n.ui.registry.addButton("wordcount",{tooltip:"Word count",icon:"character-count",onAction:e}),n.ui.registry.addMenuItem("wordcount",{text:"Word count",icon:"character-count",onAction:e})};function K(e){void 0===e&&(e=300),n.add("wordcount",(function(n){var t=z(n);return F(n,t),q(n),V(n,t,e),t}))}K()})()}}]);