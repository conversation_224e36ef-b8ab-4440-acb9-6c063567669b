{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue?vue&type=style&index=0&id=605e5410&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue", "mtime": 1753846840911}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.credits-page {\n  padding: 2rem;\n}\n\n.page-header {\n  margin-bottom: 3rem;\n}\n\n.page-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #334155;\n  margin: 0 0 0.5rem 0;\n}\n\n.page-description {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n.credits-content {\n  display: flex;\n  flex-direction: column;\n  gap: 3rem;\n}\n\n/* 余额概览 */\n.balance-overview {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 3rem;\n}\n\n.balance-cards {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 2rem;\n}\n\n/* 快速充值 */\n.quick-recharge {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(124, 138, 237, 0.1);\n  height: fit-content;\n}\n\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #334155;\n  margin: 0 0 2rem 0;\n}\n\n.recharge-options {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.recharge-option {\n  padding: 1rem;\n  border: 2px solid rgba(124, 138, 237, 0.1);\n  border-radius: 12px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: rgba(124, 138, 237, 0.02);\n}\n\n.recharge-option:hover {\n  border-color: rgba(124, 138, 237, 0.3);\n  background: rgba(124, 138, 237, 0.05);\n}\n\n.recharge-option.selected {\n  border-color: #7c8aed;\n  background: rgba(124, 138, 237, 0.1);\n}\n\n.option-amount {\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #334155;\n  margin-bottom: 0.25rem;\n}\n\n\n\n.option-label {\n  font-size: 0.9rem;\n  color: #64748b;\n}\n\n.custom-amount {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n\n\n/* 充值确认模态框 */\n.recharge-confirm {\n  padding: 1rem 0;\n}\n\n.confirm-info {\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: rgba(124, 138, 237, 0.05);\n  border-radius: 12px;\n}\n\n.info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.info-row:last-child {\n  margin-bottom: 0;\n}\n\n.info-row.total {\n  padding-top: 1rem;\n  border-top: 1px solid rgba(124, 138, 237, 0.1);\n  font-weight: 600;\n}\n\n.info-label {\n  color: #64748b;\n}\n\n.info-value {\n  color: #334155;\n  font-weight: 500;\n}\n\n.info-value.bonus {\n  color: #ef4444;\n}\n\n.payment-methods {\n  margin-bottom: 2rem;\n}\n\n.payment-methods h4 {\n  margin-bottom: 1rem;\n  color: #334155;\n}\n\n.payment-option {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.payment-icon {\n  width: 20px;\n  height: 20px;\n  background-size: contain;\n  background-repeat: no-repeat;\n}\n\n.payment-icon.alipay {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzAwOUZFOCIvPgo8L3N2Zz4K');\n}\n\n.payment-icon.wechat {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzA5QjEzMiIvPgo8L3N2Zz4K');\n}\n\n.payment-icon.bank {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzY0NzQ4QiIvPgo8L3N2Zz4K');\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n}\n\n/* 交易详情模态框 */\n.transaction-detail {\n  padding: 1rem 0;\n}\n\n.detail-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.transaction-type {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.type-recharge {\n  background: rgba(16, 185, 129, 0.1);\n  color: #10b981;\n}\n\n.type-consume {\n  background: rgba(239, 68, 68, 0.1);\n  color: #ef4444;\n}\n\n.type-refund {\n  background: rgba(245, 158, 11, 0.1);\n  color: #f59e0b;\n}\n\n.type-reward {\n  background: rgba(124, 138, 237, 0.1);\n  color: #7c8aed;\n}\n\n.transaction-amount {\n  font-size: 1.5rem;\n  font-weight: 700;\n  font-family: 'Courier New', monospace;\n}\n\n.amount-positive {\n  color: #10b981;\n}\n\n.amount-negative {\n  color: #ef4444;\n}\n\n.detail-content {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.detail-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem 0;\n  border-bottom: 1px solid rgba(124, 138, 237, 0.05);\n}\n\n.detail-row:last-child {\n  border-bottom: none;\n}\n\n.detail-label {\n  color: #64748b;\n  font-weight: 500;\n}\n\n.detail-value {\n  color: #334155;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .balance-overview {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n  \n  .balance-cards {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n  .credits-page {\n    padding: 1rem;\n  }\n  \n  .balance-cards {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .recharge-options {\n    grid-template-columns: 1fr;\n  }\n  \n  .custom-amount {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .chart-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n}\n", {"version": 3, "sources": ["Credits.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk+BA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Credits.vue", "sourceRoot": "src/views/website/usercenter/views", "sourcesContent": ["<template>\n  <div class=\"credits-page\">\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">账户管理</h1>\n      <p class=\"page-description\">管理您的账户余额、查看交易记录和充值</p>\n    </div>\n\n    <div class=\"credits-content\">\n      <!-- 余额概览 -->\n      <div class=\"balance-overview\">\n        <div class=\"balance-cards\">\n          <StatsCard\n            :value=\"balanceData.currentBalance\"\n            unit=\"元\"\n            label=\"当前余额\"\n            icon=\"anticon anticon-wallet\"\n            icon-color=\"#10b981\"\n            :trend=\"balanceTrend\"\n            :loading=\"loading\"\n            @click=\"handleQuickRecharge\"\n          />\n          \n          <StatsCard\n            :value=\"balanceData.totalRecharge\"\n            unit=\"元\"\n            label=\"累计充值\"\n            icon=\"anticon anticon-plus-circle\"\n            icon-color=\"#7c8aed\"\n            :loading=\"loading\"\n          />\n          \n          <StatsCard\n            :value=\"balanceData.totalConsumption\"\n            unit=\"元\"\n            label=\"累计消费\"\n            icon=\"anticon anticon-minus-circle\"\n            icon-color=\"#ef4444\"\n            :loading=\"loading\"\n          />\n          \n          <StatsCard\n            :value=\"balanceData.monthlyConsumption\"\n            unit=\"元\"\n            label=\"本月消费\"\n            icon=\"anticon anticon-bar-chart\"\n            icon-color=\"#f59e0b\"\n            :trend=\"monthlyTrend\"\n            :loading=\"loading\"\n          />\n        </div>\n\n        <!-- 快速充值 -->\n        <div class=\"quick-recharge\">\n          <h3 class=\"section-title\">快速充值</h3>\n          <div class=\"recharge-options\">\n            <div \n              v-for=\"option in rechargeOptions\" \n              :key=\"option.amount\"\n              class=\"recharge-option\"\n              :class=\"{ selected: selectedAmount === option.amount }\"\n              @click=\"selectRechargeAmount(option.amount)\"\n            >\n              <div class=\"option-amount\">¥{{ option.amount }}</div>\n              <div class=\"option-label\">{{ option.label }}</div>\n            </div>\n          </div>\n          \n          <div class=\"custom-amount\">\n            <a-input-number\n              v-model=\"customAmount\"\n              :min=\"0.01\"\n              :max=\"10000\"\n              :step=\"0.01\"\n              placeholder=\"自定义金额（最低0.01元）\"\n              size=\"large\"\n              style=\"width: 200px\"\n              @change=\"handleCustomAmountChange\"\n            />\n            <a-button \n              type=\"primary\" \n              size=\"large\"\n              :loading=\"rechargeLoading\"\n              @click=\"handleRecharge\"\n            >\n              立即充值\n            </a-button>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 交易记录 -->\n      <DataTable\n        ref=\"transactionTable\"\n        title=\"交易记录\"\n        :data-source=\"transactionList\"\n        :columns=\"transactionColumns\"\n        :loading=\"transactionLoading\"\n        :pagination=\"pagination\"\n        :show-action-column=\"false\"\n        :type-options=\"transactionTypeOptions\"\n        :status-options=\"[]\"\n        :show-search=\"true\"\n        type-filter-placeholder=\"交易类型\"\n        status-filter-placeholder=\"交易状态\"\n        search-placeholder=\"搜索交易描述\"\n        :date-filter-placeholder=\"['交易时间', '交易时间']\"\n        @filter-change=\"handleFilterChange\"\n        @table-change=\"handleTableChange\"\n        @refresh=\"loadTransactionData\"\n      >\n        <!-- 自定义操作按钮 -->\n        <template #actions>\n          <a-button\n            @click=\"handleResetFilters\"\n            style=\"margin-right: 8px; background: linear-gradient(135deg, #64748b 0%, #475569 100%); border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3); color: white;\"\n          >\n            <a-icon type=\"reload\" style=\"margin-right: 6px;\" />\n            重置\n          </a-button>\n          <a-button\n            type=\"primary\"\n            @click=\"handleExportTransactions\"\n            style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\"\n          >\n            <a-icon type=\"download\" style=\"margin-right: 6px;\" />\n            导出交易记录\n          </a-button>\n        </template>\n      </DataTable>\n    </div>\n\n    <!-- 充值确认模态框 -->\n    <a-modal\n      v-model=\"showRechargeModal\"\n      title=\"确认充值\"\n      :footer=\"null\"\n      width=\"500px\"\n    >\n      <div class=\"recharge-confirm\">\n        <div class=\"confirm-info\">\n          <div class=\"info-row\">\n            <span class=\"info-label\">充值金额：</span>\n            <span class=\"info-value\">¥{{ finalRechargeAmount }}</span>\n          </div>\n          <div class=\"info-row total\">\n            <span class=\"info-label\">到账金额：</span>\n            <span class=\"info-value\">¥{{ finalRechargeAmount }}</span>\n          </div>\n        </div>\n\n        <div class=\"payment-methods\">\n          <h4>选择支付方式</h4>\n          <a-radio-group v-model=\"selectedPaymentMethod\">\n            <a-radio value=\"alipay-page\">\n              <span class=\"payment-option\">\n                <i class=\"payment-icon alipay\"></i>\n                支付宝网页支付\n              </span>\n            </a-radio>\n          </a-radio-group>\n        </div>\n\n        <div class=\"modal-actions\">\n          <a-button @click=\"showRechargeModal = false\">取消</a-button>\n          <a-button \n            type=\"primary\" \n            :loading=\"paymentLoading\"\n            @click=\"handleConfirmRecharge\"\n          >\n            确认支付\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n\n    <!-- 交易详情模态框 -->\n    <a-modal\n      v-model=\"showTransactionDetail\"\n      title=\"交易详情\"\n      :footer=\"null\"\n      width=\"600px\"\n    >\n      <div class=\"transaction-detail\" v-if=\"selectedTransaction\">\n        <div class=\"detail-header\">\n          <div class=\"transaction-type\" :class=\"getTransactionTypeClass(selectedTransaction.transactionType)\">\n            <i :class=\"getTransactionTypeIcon(selectedTransaction.transactionType)\"></i>\n            {{ getTransactionTypeText(selectedTransaction.transactionType) }}\n          </div>\n          <div class=\"transaction-amount\" :class=\"getAmountClass(selectedTransaction.transactionType)\">\n            {{ formatAmount(selectedTransaction.amount, selectedTransaction.transactionType) }}\n          </div>\n        </div>\n\n        <div class=\"detail-content\">\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易单号：</span>\n            <span class=\"detail-value\">{{ selectedTransaction.id }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易时间：</span>\n            <span class=\"detail-value\">{{ formatDateTime(selectedTransaction.transactionTime) }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易描述：</span>\n            <span class=\"detail-value\">{{ selectedTransaction.description }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易前余额：</span>\n            <span class=\"detail-value\">¥{{ formatNumber(selectedTransaction.balanceBefore) }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"detail-label\">交易后余额：</span>\n            <span class=\"detail-value\">¥{{ formatNumber(selectedTransaction.balanceAfter) }}</span>\n          </div>\n          <div class=\"detail-row\" v-if=\"selectedTransaction.relatedOrderId\">\n            <span class=\"detail-label\">关联订单：</span>\n            <span class=\"detail-value\">{{ selectedTransaction.relatedOrderId }}</span>\n          </div>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport StatsCard from '../components/StatsCard.vue'\nimport DataTable from '../components/DataTable.vue'\nimport {\n  getTransactionList,\n  getTransactionStats,\n  createRechargeOrder,\n  exportTransactions\n  // getRechargeOptions, // 不再需要，使用前端固定配置\n  // getConsumptionChart // 临时注释，等后端接口实现\n} from '@/api/usercenter'\nimport { authMixin } from '@/mixins/authMixin'\n\nexport default {\n  name: 'UserCenterCredits',\n  mixins: [authMixin],\n  components: {\n    StatsCard,\n    DataTable\n  },\n  data() {\n    return {\n      loading: true,\n      transactionLoading: false,\n      chartLoading: false,\n      rechargeLoading: false,\n      paymentLoading: false,\n      \n      // 余额数据\n      balanceData: {\n        currentBalance: 0,\n        totalRecharge: 0,\n        totalConsumption: 0,\n        monthlyConsumption: 0\n      },\n      \n      // 充值相关 - 固定配置\n      rechargeOptions: [\n        { amount: 50, label: '体验套餐' },\n        { amount: 100, label: '基础套餐' },\n        { amount: 300, label: '进阶套餐' },\n        { amount: 500, label: '专业套餐' },\n        { amount: 1000, label: '企业套餐' }\n      ],\n      selectedAmount: 0,\n      customAmount: null,\n      showRechargeModal: false,\n      selectedPaymentMethod: 'alipay-page', // 默认选择网页支付\n      \n\n      \n      // 交易记录\n      transactionList: [],\n      pagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      filters: {},\n\n      // 表格列配置\n      transactionColumns: [\n        {\n          title: '交易时间',\n          dataIndex: 'transactionTime',\n          key: 'transactionTime',\n          width: 180,\n          align: 'center',\n          customRender: (text) => {\n            if (!text) return '-'\n            try {\n              let date\n              if (typeof text === 'number') {\n                date = new Date(text)\n              } else {\n                date = new Date(text)\n              }\n              return date.toLocaleString('zh-CN', {\n                year: 'numeric',\n                month: '2-digit',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            } catch (error) {\n              return '-'\n            }\n          }\n        },\n        {\n          title: '交易类型',\n          dataIndex: 'transactionType',\n          key: 'transactionType',\n          width: 120,\n          align: 'center',\n          customRender: (text) => {\n            console.log('🔍 交易类型 customRender - 接收到的值:', text, '类型:', typeof text)\n            const typeMap = {\n              1: '消费',\n              2: '充值',\n              3: '退款',\n              4: '奖励',\n              5: '会员订阅',\n              '1': '消费',\n              '2': '充值',\n              '3': '退款',\n              '4': '奖励',\n              '5': '会员订阅'\n            }\n            const result = typeMap[text] || text || '-'\n            console.log('🔍 交易类型 customRender - 返回结果:', result)\n            return result\n          }\n        },\n        {\n          title: '订单号',\n          dataIndex: 'relatedOrderId',\n          key: 'relatedOrderId',\n          width: 200,\n          align: 'center',\n          customRender: (text) => {\n            return text || '-'\n          }\n        },\n        {\n          title: '交易描述',\n          dataIndex: 'description',\n          key: 'description',\n          align: 'center',\n          ellipsis: true\n        },\n        {\n          title: '交易金额',\n          dataIndex: 'amount',\n          key: 'amount',\n          width: 120,\n          align: 'center',\n          customRender: (text, record) => {\n            const amount = parseFloat(text || 0).toFixed(2)\n            const isPositive = [2, 3, 4].includes(Number(record.transactionType)) // 充值、退款、奖励为正\n            const prefix = isPositive ? '+' : '-'\n            const colorStyle = isPositive ? 'color: #52c41a; font-weight: 600;' : 'color: #ff4d4f; font-weight: 600;'\n            return <span style={colorStyle}>{prefix}¥{amount}</span>\n          }\n        },\n        {\n          title: '余额',\n          dataIndex: 'balanceAfter',\n          key: 'balanceAfter',\n          width: 120,\n          align: 'center',\n          customRender: (text) => {\n            const balance = parseFloat(text || 0).toFixed(2)\n            const balanceStyle = 'color: #1890ff; font-weight: 600;'\n            return <span style={balanceStyle}>¥{balance}</span>\n          }\n        },\n        {\n          title: '状态',\n          dataIndex: 'orderStatus',\n          key: 'orderStatus',\n          width: 100,\n          align: 'center',\n          customRender: (text) => {\n            // 后端查询条件已经限制为 order_status = 3（已完成）\n            // 所以这里的数据都应该是已完成的交易\n\n            // 如果有明确的状态字段，使用映射\n            if (text !== undefined && text !== null) {\n              const statusMap = {\n                1: { text: '待支付', style: 'color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                2: { text: '已支付', style: 'color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                3: { text: '已完成', style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                4: { text: '已取消', style: 'color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                5: { text: '已退款', style: 'color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '1': { text: '待支付', style: 'color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '2': { text: '已支付', style: 'color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '3': { text: '已完成', style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '4': { text: '已取消', style: 'color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },\n                '5': { text: '已退款', style: 'color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' }\n              }\n              const status = statusMap[text] || { text: '已完成', style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' }\n              return <span style={status.style}>{status.text}</span>\n            }\n\n            // 如果没有状态字段，默认为已完成\n            // 因为后端查询已经过滤为已完成的交易\n            const defaultStyle = 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'\n            return <span style={defaultStyle}>已完成</span>\n          }\n        }\n      ],\n\n      // 筛选选项\n      transactionTypeOptions: [\n        { value: 1, label: '消费' },\n        { value: 2, label: '充值' },\n        { value: 3, label: '退款' },\n        { value: 4, label: '奖励' },\n        { value: 5, label: '会员订阅' }\n      ],\n      // 🚫 取消状态筛选选项\n      // transactionStatusOptions: [\n      //   { value: 1, label: '待支付' },\n      //   { value: 3, label: '已完成' },\n      //   { value: 4, label: '已取消' },\n      //   { value: 5, label: '已退款' }\n      // ],\n\n      // 交易详情\n      showTransactionDetail: false,\n      selectedTransaction: null\n    }\n  },\n  computed: {\n    balanceTrend() {\n      // 根据实际数据计算趋势（暂时返回null，等待后端提供趋势数据）\n      // TODO: 后端需要提供上月对比数据来计算真实趋势\n      return null\n    },\n\n    monthlyTrend() {\n      // 根据实际数据计算月度趋势（暂时返回null，等待后端提供趋势数据）\n      // TODO: 后端需要提供月度对比数据来计算真实趋势\n      return null\n    },\n    \n    finalRechargeAmount() {\n      return this.selectedAmount || this.customAmount || 0\n    }\n  },\n  async mounted() {\n    await this.loadData()\n\n    // 检查是否是支付成功返回\n    this.checkPaymentSuccess()\n  },\n  methods: {\n    async loadData() {\n      try {\n        this.loading = true\n        \n        // 并行加载数据\n        await Promise.all([\n          this.loadBalanceData(),\n          this.loadTransactionData()\n        ])\n      } catch (error) {\n        console.error('加载账户管理数据失败:', error)\n        this.$message.error('加载数据失败，请刷新重试')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async loadBalanceData() {\n      try {\n        const response = await getTransactionStats()\n        if (response.success) {\n          // 修复：使用 result 字段并正确映射字段名\n          const stats = response.result || {}\n\n          // 🔧 修复字段映射问题\n          this.balanceData = {\n            currentBalance: stats.accountBalance || 0,        // 后端返回accountBalance，前端期望currentBalance\n            totalRecharge: stats.totalRecharge || 0,          // 字段名一致\n            totalConsumption: stats.totalConsumption || 0,    // 字段名一致\n            monthlyConsumption: stats.monthlyConsumption || 0, // 如果后端有这个字段\n            transactionCount: stats.transactionCount || 0     // 交易记录总数\n          }\n\n          console.log('🔍 Credits页面 - 余额数据映射结果:', this.balanceData)\n          console.log('🔍 Credits页面 - 后端原始数据:', stats)\n        }\n      } catch (error) {\n        console.error('加载余额数据失败:', error)\n      }\n    },\n    \n    async loadTransactionData() {\n      try {\n        this.transactionLoading = true\n\n        const params = {\n          current: this.pagination.current,\n          size: this.pagination.pageSize,\n          ...this.filters\n        }\n\n        // 🔧 处理日期范围筛选\n        if (this.filters.dateRange && this.filters.dateRange.length === 2) {\n          params.startDate = this.filters.dateRange[0].format('YYYY-MM-DD')\n          params.endDate = this.filters.dateRange[1].format('YYYY-MM-DD')\n          console.log('🔍 日期范围筛选 - 原始dateRange:', this.filters.dateRange)\n          console.log('🔍 日期范围筛选 - startDate:', params.startDate, 'endDate:', params.endDate)\n          // 移除dateRange，避免传递给后端\n          delete params.dateRange\n        }\n\n        console.log('🔍 Credits页面 - 最终查询参数:', params)\n\n        const response = await getTransactionList(params)\n        if (response.success) {\n          // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法\n          const records = (response.result && response.result.records) || []\n\n          // 调试：打印后端返回的数据结构\n          console.log('🔍 Credits页面 - 后端返回的完整响应:', response)\n          console.log('🔍 Credits页面 - 交易记录数组:', records)\n          if (records.length > 0) {\n            console.log('🔍 Credits页面 - 第一条记录详情:', records[0])\n            console.log('🔍 Credits页面 - 第一条记录的所有字段:', Object.keys(records[0]))\n            console.log('🔍 Credits页面 - transactionType字段值:', records[0].transactionType, '类型:', typeof records[0].transactionType)\n            console.log('🔍 Credits页面 - status字段值:', records[0].status, '类型:', typeof records[0].status)\n\n          // 检查所有可能的状态相关字段\n          console.log('🔍 Credits页面 - 检查状态相关字段:')\n          console.log('  - status:', records[0].status)\n          console.log('  - transactionStatus:', records[0].transactionStatus)\n          console.log('  - orderStatus:', records[0].orderStatus)\n          console.log('  - paymentStatus:', records[0].paymentStatus)\n          console.log('  - state:', records[0].state)\n          console.log('  - delFlag:', records[0].delFlag)\n          }\n\n          this.transactionList = records\n          this.pagination.total = (response.result && response.result.total) || 0\n        }\n      } catch (error) {\n        console.error('加载交易记录失败:', error)\n        this.transactionList = []\n      } finally {\n        this.transactionLoading = false\n      }\n    },\n\n\n    \n\n    \n    // 充值相关方法\n    selectRechargeAmount(amount) {\n      this.selectedAmount = amount\n      this.customAmount = null\n    },\n    \n    handleQuickRecharge() {\n      this.selectedAmount = 100\n      this.handleRecharge()\n    },\n    \n    handleRecharge() {\n      if (!this.finalRechargeAmount || this.finalRechargeAmount < 0.01) {\n        this.$message.warning('请选择或输入充值金额，最低0.01元')\n        return\n      }\n\n      this.showRechargeModal = true\n    },\n    \n    async handleConfirmRecharge() {\n      try {\n        this.paymentLoading = true\n\n        const orderData = {\n          amount: this.finalRechargeAmount,\n          paymentMethod: this.selectedPaymentMethod\n        }\n\n        const response = await createRechargeOrder(orderData)\n        if (response.success) {\n          const result = response.result\n\n          // 只支持支付宝网页支付\n          await this.handleAlipayPagePayment(result.orderId, result.amount)\n\n          this.showRechargeModal = false\n        } else {\n          this.$message.error(response.message || '创建充值订单失败')\n        }\n      } catch (error) {\n        console.error('创建充值订单失败:', error)\n        this.$message.error('充值失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付宝网页支付\n    async handleAlipayPagePayment(orderId, amount) {\n      try {\n        console.log('🔍 开始处理支付宝支付 - 订单号:', orderId, '金额:', amount)\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        // 调用支付宝支付接口\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        console.log('🔍 发送支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        console.log('🔍 支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const payForm = payResponse.result.payForm\n          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')\n\n          if (!payForm) {\n            this.$message.error('支付表单为空')\n            return\n          }\n\n          // 创建表单并提交到支付宝\n          const div = document.createElement('div')\n          div.innerHTML = payForm\n          document.body.appendChild(div)\n\n          const form = div.querySelector('form')\n          if (form) {\n            console.log('🔍 找到支付表单，准备提交')\n            form.submit()\n          } else {\n            console.error('🔍 未找到支付表单')\n            this.$message.error('支付表单创建失败')\n          }\n\n          // 清理DOM\n          setTimeout(() => {\n            if (document.body.contains(div)) {\n              document.body.removeChild(div)\n            }\n          }, 1000)\n\n        } else {\n          console.error('🔍 支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建支付订单失败')\n        }\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝支付失败:', error)\n        this.$message.error('支付宝支付失败，请重试')\n      }\n    },\n\n    // 处理支付宝扫码支付\n    async handleAlipayQrPayment(orderId, amount) {\n      try {\n        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', orderId, '金额:', amount)\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        // 调用支付宝扫码支付接口\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        console.log('🔍 发送扫码支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        console.log('🔍 扫码支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')\n\n          if (!qrCode) {\n            this.$message.error('支付二维码生成失败')\n            return\n          }\n\n          // 显示二维码支付弹窗\n          this.showQrCodeModal(qrCode, orderId, amount)\n\n        } else {\n          console.error('🔍 扫码支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建扫码支付订单失败')\n        }\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝扫码支付失败:', error)\n        this.$message.error('支付宝扫码支付失败，请重试')\n      }\n    },\n\n    // 显示二维码支付弹窗\n    showQrCodeModal(qrCode, orderId, amount) {\n      this.$modal.info({\n        title: '支付宝扫码支付',\n        width: 400,\n        content: h => h('div', {\n          style: {\n            textAlign: 'center',\n            padding: '20px'\n          }\n        }, [\n          h('div', {\n            style: {\n              marginBottom: '16px',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            }\n          }, `充值金额：¥${amount}`),\n          h('div', {\n            style: {\n              marginBottom: '16px',\n              color: '#666'\n            }\n          }, '请使用支付宝扫描下方二维码完成支付'),\n          h('div', {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              marginBottom: '16px'\n            }\n          }, [\n            h('img', {\n              src: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`,\n              style: {\n                width: '200px',\n                height: '200px',\n                border: '1px solid #d9d9d9'\n              }\n            })\n          ]),\n          h('div', {\n            style: {\n              color: '#999',\n              fontSize: '12px'\n            }\n          }, '支付完成后页面将自动跳转')\n        ]),\n        onOk: () => {\n          // 用户点击确定后，可以查询订单状态\n          this.checkOrderStatus(orderId)\n        }\n      })\n\n      // 定时查询订单状态\n      this.startOrderStatusPolling(orderId)\n    },\n\n    // 开始轮询订单状态\n    startOrderStatusPolling(orderId) {\n      const pollInterval = setInterval(async () => {\n        try {\n          const response = await this.$http.get(`/api/alipay/queryOrder?orderId=${orderId}`)\n          if (response.success && response.result.tradeStatus === 'TRADE_SUCCESS') {\n            clearInterval(pollInterval)\n            this.$modal.destroyAll()\n            this.$message.success('支付成功！正在更新账户余额...')\n            setTimeout(() => {\n              this.loadData()\n            }, 1000)\n          }\n        } catch (error) {\n          console.error('查询订单状态失败:', error)\n        }\n      }, 3000) // 每3秒查询一次\n\n      // 15分钟后停止轮询\n      setTimeout(() => {\n        clearInterval(pollInterval)\n      }, 15 * 60 * 1000)\n    },\n\n    // 检查订单状态\n    async checkOrderStatus(orderId) {\n      try {\n        const response = await this.$http.get(`/api/alipay/queryOrder?orderId=${orderId}`)\n        if (response.success && response.result.tradeStatus === 'TRADE_SUCCESS') {\n          this.$message.success('支付成功！正在更新账户余额...')\n          setTimeout(() => {\n            this.loadData()\n          }, 1000)\n        } else {\n          this.$message.info('订单尚未支付，请继续扫码支付')\n        }\n      } catch (error) {\n        console.error('查询订单状态失败:', error)\n        this.$message.error('查询订单状态失败')\n      }\n    },\n\n    // 检查支付成功状态\n    checkPaymentSuccess() {\n      const urlParams = new URLSearchParams(window.location.search)\n      const paymentSuccess = urlParams.get('paymentSuccess')\n      const orderId = urlParams.get('orderId')\n\n      if (paymentSuccess === 'true' && orderId) {\n        console.log('🎉 检测到支付成功返回 - 订单号:', orderId)\n\n        // 显示支付成功消息\n        this.$message.success('支付成功！正在更新账户余额...')\n\n        // 刷新数据\n        setTimeout(() => {\n          this.loadData()\n        }, 1000)\n\n        // 清理URL参数\n        const newUrl = window.location.pathname\n        window.history.replaceState({}, document.title, newUrl)\n      }\n    },\n\n    // 表格相关方法\n    handleFilterChange(filters) {\n      this.filters = filters\n      this.pagination.current = 1\n      this.loadTransactionData()\n    },\n\n    handleTableChange({ pagination }) {\n      this.pagination = { ...this.pagination, ...pagination }\n      this.loadTransactionData()\n    },\n\n    // 重置筛选条件\n    handleResetFilters() {\n      // 调用DataTable组件的重置方法\n      if (this.$refs.transactionTable) {\n        this.$refs.transactionTable.resetFilters()\n      }\n      // 重置本地筛选条件\n      this.filters = {}\n      this.pagination.current = 1\n      // 重新加载数据\n      this.loadTransactionData()\n      this.$message.success('筛选条件已重置')\n    },\n\n    // 导出交易记录\n    async handleExportTransactions() {\n      try {\n        this.$message.loading('正在导出交易记录...', 0)\n\n        // 使用当前的筛选条件导出\n        const params = {\n          transactionType: this.filters.type,\n          status: this.filters.status,\n          keyword: this.filters.keyword\n        }\n\n        // 处理日期范围\n        if (this.filters.dateRange && this.filters.dateRange.length === 2) {\n          params.startDate = this.filters.dateRange[0].format('YYYY-MM-DD')\n          params.endDate = this.filters.dateRange[1].format('YYYY-MM-DD')\n        }\n\n        console.log('🎯 导出交易记录参数:', params)\n\n        const response = await exportTransactions(params)\n        this.$message.destroy()\n\n        // 创建下载链接\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n\n        // 生成文件名\n        const now = new Date()\n        const timestamp = now.getFullYear() +\n          String(now.getMonth() + 1).padStart(2, '0') +\n          String(now.getDate()).padStart(2, '0') + '_' +\n          String(now.getHours()).padStart(2, '0') +\n          String(now.getMinutes()).padStart(2, '0')\n\n        link.download = `交易记录_${timestamp}.xlsx`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n        this.$message.success('交易记录导出成功！')\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('🎯 导出交易记录失败:', error)\n        this.$message.error('导出失败，请重试')\n      }\n    },\n    \n    handleViewTransactionDetail(transaction) {\n      this.selectedTransaction = transaction\n      this.showTransactionDetail = true\n    },\n    \n\n    \n    // 工具方法\n    getTransactionTypeClass(type) {\n      const classMap = {\n        1: 'type-consume',\n        2: 'type-recharge',\n        3: 'type-refund',\n        4: 'type-reward'\n      }\n      return classMap[type] || ''\n    },\n    \n    getTransactionTypeIcon(type) {\n      const iconMap = {\n        1: 'anticon anticon-minus-circle',\n        2: 'anticon anticon-plus-circle',\n        3: 'anticon anticon-undo',\n        4: 'anticon anticon-gift'\n      }\n      return iconMap[type] || 'anticon anticon-question-circle'\n    },\n    \n    getTransactionTypeText(type) {\n      const textMap = {\n        1: '消费',\n        2: '充值',\n        3: '退款',\n        4: '奖励'\n      }\n      return textMap[type] || '未知'\n    },\n    \n    getAmountClass(type) {\n      return {\n        'amount-positive': [2, 3, 4].includes(type), // 充值、退款、奖励为正\n        'amount-negative': type === 1 // 消费为负\n      }\n    },\n    \n    formatAmount(amount, type) {\n      const prefix = [2, 3, 4].includes(type) ? '+' : '-'\n      return `${prefix}¥${this.formatNumber(Math.abs(amount))}`\n    },\n    \n    formatNumber(number) {\n      if (!number) return '0.00'\n      return parseFloat(number).toFixed(2)\n    },\n    \n    formatDateTime(dateString) {\n      if (!dateString) return '-'\n      \n      try {\n        const date = new Date(dateString)\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      } catch (error) {\n        return '-'\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.credits-page {\n  padding: 2rem;\n}\n\n.page-header {\n  margin-bottom: 3rem;\n}\n\n.page-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #334155;\n  margin: 0 0 0.5rem 0;\n}\n\n.page-description {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n.credits-content {\n  display: flex;\n  flex-direction: column;\n  gap: 3rem;\n}\n\n/* 余额概览 */\n.balance-overview {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 3rem;\n}\n\n.balance-cards {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 2rem;\n}\n\n/* 快速充值 */\n.quick-recharge {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(124, 138, 237, 0.1);\n  height: fit-content;\n}\n\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #334155;\n  margin: 0 0 2rem 0;\n}\n\n.recharge-options {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.recharge-option {\n  padding: 1rem;\n  border: 2px solid rgba(124, 138, 237, 0.1);\n  border-radius: 12px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: rgba(124, 138, 237, 0.02);\n}\n\n.recharge-option:hover {\n  border-color: rgba(124, 138, 237, 0.3);\n  background: rgba(124, 138, 237, 0.05);\n}\n\n.recharge-option.selected {\n  border-color: #7c8aed;\n  background: rgba(124, 138, 237, 0.1);\n}\n\n.option-amount {\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #334155;\n  margin-bottom: 0.25rem;\n}\n\n\n\n.option-label {\n  font-size: 0.9rem;\n  color: #64748b;\n}\n\n.custom-amount {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n\n\n/* 充值确认模态框 */\n.recharge-confirm {\n  padding: 1rem 0;\n}\n\n.confirm-info {\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: rgba(124, 138, 237, 0.05);\n  border-radius: 12px;\n}\n\n.info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.info-row:last-child {\n  margin-bottom: 0;\n}\n\n.info-row.total {\n  padding-top: 1rem;\n  border-top: 1px solid rgba(124, 138, 237, 0.1);\n  font-weight: 600;\n}\n\n.info-label {\n  color: #64748b;\n}\n\n.info-value {\n  color: #334155;\n  font-weight: 500;\n}\n\n.info-value.bonus {\n  color: #ef4444;\n}\n\n.payment-methods {\n  margin-bottom: 2rem;\n}\n\n.payment-methods h4 {\n  margin-bottom: 1rem;\n  color: #334155;\n}\n\n.payment-option {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.payment-icon {\n  width: 20px;\n  height: 20px;\n  background-size: contain;\n  background-repeat: no-repeat;\n}\n\n.payment-icon.alipay {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzAwOUZFOCIvPgo8L3N2Zz4K');\n}\n\n.payment-icon.wechat {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzA5QjEzMiIvPgo8L3N2Zz4K');\n}\n\n.payment-icon.bank {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzY0NzQ4QiIvPgo8L3N2Zz4K');\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n}\n\n/* 交易详情模态框 */\n.transaction-detail {\n  padding: 1rem 0;\n}\n\n.detail-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.transaction-type {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.type-recharge {\n  background: rgba(16, 185, 129, 0.1);\n  color: #10b981;\n}\n\n.type-consume {\n  background: rgba(239, 68, 68, 0.1);\n  color: #ef4444;\n}\n\n.type-refund {\n  background: rgba(245, 158, 11, 0.1);\n  color: #f59e0b;\n}\n\n.type-reward {\n  background: rgba(124, 138, 237, 0.1);\n  color: #7c8aed;\n}\n\n.transaction-amount {\n  font-size: 1.5rem;\n  font-weight: 700;\n  font-family: 'Courier New', monospace;\n}\n\n.amount-positive {\n  color: #10b981;\n}\n\n.amount-negative {\n  color: #ef4444;\n}\n\n.detail-content {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.detail-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem 0;\n  border-bottom: 1px solid rgba(124, 138, 237, 0.05);\n}\n\n.detail-row:last-child {\n  border-bottom: none;\n}\n\n.detail-label {\n  color: #64748b;\n  font-weight: 500;\n}\n\n.detail-value {\n  color: #334155;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .balance-overview {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n  \n  .balance-cards {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n  .credits-page {\n    padding: 1rem;\n  }\n  \n  .balance-cards {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .recharge-options {\n    grid-template-columns: 1fr;\n  }\n  \n  .custom-amount {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .chart-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n}\n</style>\n"]}]}