(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~8a52df3b"],{e562:function(e,t,n){(function(t){(function(){"use strict";var n=function(e){if(null===e)return"null";if(void 0===e)return"undefined";var t=typeof e;return"object"===t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t},r=function(e){return-1!==["undefined","boolean","number","string","function","xml","null"].indexOf(e)},o=function(e,t){var n=Array.prototype.slice.call(e);return n.sort(t)},i=function(e,t){return a((function(n,r){return e.eq(t(n),t(r))}))},a=function(e){return{eq:e}},u=a((function(e,t){return e===t})),c=u,s=function(e){return a((function(t,n){if(t.length!==n.length)return!1;for(var r=t.length,o=0;o<r;o++)if(!e.eq(t[o],n[o]))return!1;return!0}))},f=function(e,t){return i(s(e),(function(e){return o(e,t)}))},l=function(e){return a((function(t,n){var r=Object.keys(t),o=Object.keys(n);if(!f(c).eq(r,o))return!1;for(var i=r.length,a=0;a<i;a++){var u=r[a];if(!e.eq(t[u],n[u]))return!1}return!0}))},d=a((function(e,t){if(e===t)return!0;var o=n(e),i=n(t);return o===i&&(r(o)?e===t:"array"===o?s(d).eq(e,t):"object"===o&&l(d).eq(e,t))})),m=function(e){var t=typeof e;return null===e?"null":"object"===t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t},g=function(e){return function(t){return m(t)===e}},p=function(e){return function(t){return typeof t===e}},h=function(e){return function(t){return e===t}},v=g("string"),b=g("object"),y=g("array"),C=h(null),w=p("boolean"),x=h(void 0),S=function(e){return null===e||void 0===e},k=function(e){return!S(e)},N=p("function"),E=p("number"),_=function(){},A=function(e,t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return e(t.apply(null,n))}},R=function(e,t){return function(n){return e(t(n))}},D=function(e){return function(){return e}},T=function(e){return e},O=function(e,t){return e===t};function B(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o=t.concat(n);return e.apply(null,o)}}var P=function(e){return function(t){return!e(t)}},L=function(e){return function(){throw new Error(e)}},I=function(e){return e()},M=function(e){e()},F=D(!1),U=D(!0),z=function(){return H},H=function(){var e=function(e){return e()},t=T,n={fold:function(e,t){return e()},isSome:F,isNone:U,getOr:t,getOrThunk:e,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:D(null),getOrUndefined:D(void 0),or:t,orThunk:e,map:z,each:_,bind:z,exists:F,forall:U,filter:function(){return z()},toArray:function(){return[]},toString:D("none()")};return n}(),j=function(e){var t=D(e),n=function(){return o},r=function(t){return t(e)},o={fold:function(t,n){return n(e)},isSome:U,isNone:F,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(t){return j(t(e))},each:function(t){t(e)},bind:r,exists:r,forall:r,filter:function(t){return t(e)?o:H},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return o},V=function(e){return null===e||void 0===e?H:j(e)},q={some:j,none:z,from:V},$=Array.prototype.slice,W=Array.prototype.indexOf,K=Array.prototype.push,X=function(e,t){return W.call(e,t)},Y=function(e,t){var n=X(e,t);return-1===n?q.none():q.some(n)},G=function(e,t){return X(e,t)>-1},J=function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n))return!0}return!1},Q=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o)}return r},Z=function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];t(o,n)}},ee=function(e,t){for(var n=e.length-1;n>=0;n--){var r=e[n];t(r,n)}},te=function(e,t){for(var n=[],r=[],o=0,i=e.length;o<i;o++){var a=e[o],u=t(a,o)?n:r;u.push(a)}return{pass:n,fail:r}},ne=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r)&&n.push(i)}return n},re=function(e,t,n){return ee(e,(function(e,r){n=t(n,e,r)})),n},oe=function(e,t,n){return Z(e,(function(e,r){n=t(n,e,r)})),n},ie=function(e,t,n){for(var r=0,o=e.length;r<o;r++){var i=e[r];if(t(i,r))return q.some(i);if(n(i,r))break}return q.none()},ae=function(e,t){return ie(e,t,F)},ue=function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n))return q.some(n)}return q.none()},ce=function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!y(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);K.apply(t,e[n])}return t},se=function(e,t){return ce(Q(e,t))},fe=function(e,t){for(var n=0,r=e.length;n<r;++n){var o=e[n];if(!0!==t(o,n))return!1}return!0},le=function(e){var t=$.call(e,0);return t.reverse(),t},de=function(e,t){return ne(e,(function(e){return!G(t,e)}))},me=function(e,t){for(var n={},r=0,o=e.length;r<o;r++){var i=e[r];n[String(i)]=t(i,r)}return n},ge=function(e,t){var n=$.call(e,0);return n.sort(t),n},pe=function(e,t){return t>=0&&t<e.length?q.some(e[t]):q.none()},he=function(e){return pe(e,0)},ve=function(e){return pe(e,e.length-1)},be=N(Array.from)?Array.from:function(e){return $.call(e)},ye=function(e,t){for(var n=0;n<e.length;n++){var r=t(e[n],n);if(r.isSome())return r}return q.none()},Ce=Object.keys,we=Object.hasOwnProperty,xe=function(e,t){for(var n=Ce(e),r=0,o=n.length;r<o;r++){var i=n[r],a=e[i];t(a,i)}},Se=function(e,t){return ke(e,(function(e,n){return{k:n,v:t(e,n)}}))},ke=function(e,t){var n={};return xe(e,(function(e,r){var o=t(e,r);n[o.k]=o.v})),n},Ne=function(e){return function(t,n){e[n]=t}},Ee=function(e,t,n,r){var o={};return xe(e,(function(e,o){(t(e,o)?n:r)(e,o)})),o},_e=function(e,t){var n={},r={};return Ee(e,t,Ne(n),Ne(r)),{t:n,f:r}},Ae=function(e,t){var n={};return Ee(e,t,Ne(n),_),n},Re=function(e,t){var n=[];return xe(e,(function(e,r){n.push(t(e,r))})),n},De=function(e){return Re(e,T)},Te=function(e,t){return Oe(e,t)?q.from(e[t]):q.none()},Oe=function(e,t){return we.call(e,t)},Be=function(e,t){return Oe(e,t)&&void 0!==e[t]&&null!==e[t]},Pe=function(e,t,n){return void 0===n&&(n=d),l(n).eq(e,t)},Le=Array.isArray,Ie=function(e){if(Le(e))return e;for(var t=[],n=0,r=e.length;n<r;n++)t[n]=e[n];return t},Me=function(e,t,n){var r,o;if(!e)return!1;if(n=n||e,void 0!==e.length){for(r=0,o=e.length;r<o;r++)if(!1===t.call(n,e[r],r,e))return!1}else for(r in e)if(Oe(e,r)&&!1===t.call(n,e[r],r,e))return!1;return!0},Fe=function(e,t){var n=[];return Me(e,(function(r,o){n.push(t(r,o,e))})),n},Ue=function(e,t){var n=[];return Me(e,(function(r,o){t&&!t(r,o,e)||n.push(r)})),n},ze=function(e,t){if(e)for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},He=function(e,t,n,r){for(var o=x(n)?e[0]:n,i=0;i<e.length;i++)o=t.call(r,o,e[i],i);return o},je=function(e,t,n){var r,o;for(r=0,o=e.length;r<o;r++)if(t.call(n,e[r],r,e))return r;return-1},Ve=function(e){return e[e.length-1]},qe=function(){return qe=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},qe.apply(this,arguments)};function $e(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}function We(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}var Ke=function(e){var t,n=!1;return function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return n||(n=!0,t=e.apply(null,r)),t}},Xe=function(e,t,n,r){var o=e.isiOS()&&!0===/ipad/i.test(n),i=e.isiOS()&&!o,a=e.isiOS()||e.isAndroid(),u=a||r("(pointer:coarse)"),c=o||!i&&a&&r("(min-device-width:768px)"),s=i||a&&!c,f=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),l=!s&&!c&&!f;return{isiPad:D(o),isiPhone:D(i),isTablet:D(c),isPhone:D(s),isTouch:D(u),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:D(f),isDesktop:D(l)}},Ye=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}},Ge=function(e,t){var n=Ye(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return Ze(r(1),r(2))},Je=function(e,t){var n=String(t).toLowerCase();return 0===e.length?Qe():Ge(e,n)},Qe=function(){return Ze(0,0)},Ze=function(e,t){return{major:e,minor:t}},et={nu:Ze,detect:Je,unknown:Qe},tt=function(e,t){return ye(t.brands,(function(t){var n=t.brand.toLowerCase();return ae(e,(function(e){var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((function(e){return{current:e.name,version:et.nu(parseInt(t.version,10),0)}}))}))},nt=function(e,t){var n=String(t).toLowerCase();return ae(e,(function(e){return e.search(n)}))},rt=function(e,t){return nt(e,t).map((function(e){var n=et.detect(e.versionRegexes,t);return{current:e.name,version:n}}))},ot=function(e,t){return nt(e,t).map((function(e){var n=et.detect(e.versionRegexes,t);return{current:e.name,version:n}}))},it=function(e,t){return e.substring(t)},at=function(e,t,n){return""===t||e.length>=t.length&&e.substr(n,n+t.length)===t},ut=function(e,t){return st(e,t)?it(e,t.length):e},ct=function(e,t){return-1!==e.indexOf(t)},st=function(e,t){return at(e,t,0)},ft=function(e){return function(t){return t.replace(e,"")}},lt=ft(/^\s+|\s+$/g),dt=ft(/^\s+/g),mt=ft(/\s+$/g),gt=function(e){return e.length>0},pt=function(e){return!gt(e)},ht=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,vt=function(e){return function(t){return ct(t,e)}},bt=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return ct(e,"edge/")&&ct(e,"chrome")&&ct(e,"safari")&&ct(e,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ht],search:function(e){return ct(e,"chrome")&&!ct(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return ct(e,"msie")||ct(e,"trident")}},{name:"Opera",versionRegexes:[ht,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:vt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:vt("firefox")},{name:"Safari",versionRegexes:[ht,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(ct(e,"safari")||ct(e,"mobile/"))&&ct(e,"applewebkit")}}],yt=[{name:"Windows",search:vt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return ct(e,"iphone")||ct(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:vt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:vt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:vt("linux"),versionRegexes:[]},{name:"Solaris",search:vt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:vt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:vt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Ct={browsers:D(bt),oses:D(yt)},wt="Edge",xt="Chrome",St="IE",kt="Opera",Nt="Firefox",Et="Safari",_t=function(){return At({current:void 0,version:et.unknown()})},At=function(e){var t=e.current,n=e.version,r=function(e){return function(){return t===e}};return{current:t,version:n,isEdge:r(wt),isChrome:r(xt),isIE:r(St),isOpera:r(kt),isFirefox:r(Nt),isSafari:r(Et)}},Rt={unknown:_t,nu:At,edge:D(wt),chrome:D(xt),ie:D(St),opera:D(kt),firefox:D(Nt),safari:D(Et)},Dt="Windows",Tt="iOS",Ot="Android",Bt="Linux",Pt="OSX",Lt="Solaris",It="FreeBSD",Mt="ChromeOS",Ft=function(){return Ut({current:void 0,version:et.unknown()})},Ut=function(e){var t=e.current,n=e.version,r=function(e){return function(){return t===e}};return{current:t,version:n,isWindows:r(Dt),isiOS:r(Tt),isAndroid:r(Ot),isOSX:r(Pt),isLinux:r(Bt),isSolaris:r(Lt),isFreeBSD:r(It),isChromeOS:r(Mt)}},zt={unknown:Ft,nu:Ut,windows:D(Dt),ios:D(Tt),android:D(Ot),linux:D(Bt),osx:D(Pt),solaris:D(Lt),freebsd:D(It),chromeos:D(Mt)},Ht=function(e,t,n){var r=Ct.browsers(),o=Ct.oses(),i=t.bind((function(e){return tt(r,e)})).orThunk((function(){return rt(r,e)})).fold(Rt.unknown,Rt.nu),a=ot(o,e).fold(zt.unknown,zt.nu),u=Xe(a,i,e,n);return{browser:i,os:a,deviceType:u}},jt={detect:Ht},Vt=function(e){return window.matchMedia(e).matches},qt=Ke((function(){return jt.detect(navigator.userAgent,q.from(navigator.userAgentData),Vt)})),$t=function(){return qt()},Wt=navigator.userAgent,Kt=$t(),Xt=Kt.browser,Yt=Kt.os,Gt=Kt.deviceType,Jt=/WebKit/.test(Wt)&&!Xt.isEdge(),Qt="FormData"in window&&"FileReader"in window&&"URL"in window&&!!URL.createObjectURL,Zt=-1!==Wt.indexOf("Windows Phone"),en={opera:Xt.isOpera(),webkit:Jt,ie:!(!Xt.isIE()&&!Xt.isEdge())&&Xt.version.major,gecko:Xt.isFirefox(),mac:Yt.isOSX()||Yt.isiOS(),iOS:Gt.isiPad()||Gt.isiPhone(),android:Yt.isAndroid(),contentEditable:!0,transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",caretAfter:!0,range:window.getSelection&&"Range"in window,documentMode:Xt.isIE()?document.documentMode||7:10,fileApi:Qt,ceFalse:!0,cacheSuffix:null,container:null,experimentalShadowDom:!1,canHaveCSP:!Xt.isIE(),desktop:Gt.isDesktop(),windowsPhone:Zt,browser:{current:Xt.current,version:Xt.version,isChrome:Xt.isChrome,isEdge:Xt.isEdge,isFirefox:Xt.isFirefox,isIE:Xt.isIE,isOpera:Xt.isOpera,isSafari:Xt.isSafari},os:{current:Yt.current,version:Yt.version,isAndroid:Yt.isAndroid,isChromeOS:Yt.isChromeOS,isFreeBSD:Yt.isFreeBSD,isiOS:Yt.isiOS,isLinux:Yt.isLinux,isOSX:Yt.isOSX,isSolaris:Yt.isSolaris,isWindows:Yt.isWindows},deviceType:{isDesktop:Gt.isDesktop,isiPad:Gt.isiPad,isiPhone:Gt.isiPhone,isPhone:Gt.isPhone,isTablet:Gt.isTablet,isTouch:Gt.isTouch,isWebView:Gt.isWebView}},tn=/^\s*|\s*$/g,nn=function(e){return null===e||void 0===e?"":(""+e).replace(tn,"")},rn=function(e,t){return t?!("array"!==t||!Le(e))||typeof e===t:void 0!==e},on=function(e,t,n){var r;e=e||[],t=t||",","string"===typeof e&&(e=e.split(t)),n=n||{},r=e.length;while(r--)n[e[r]]={};return n},an=Oe,un=function(e,t,n){var r,o,i,a=this,u=0;e=/^((static) )?([\w.]+)(:([\w.]+))?/.exec(e);var c=e[3].match(/(^|\.)(\w+)$/i)[2],s=a.createNS(e[3].replace(/\.\w+$/,""),n);if(!s[c]){if("static"===e[2])return s[c]=t,void(this.onCreate&&this.onCreate(e[2],e[3],s[c]));t[c]||(t[c]=function(){},u=1),s[c]=t[c],a.extend(s[c].prototype,t),e[5]&&(r=a.resolve(e[5]).prototype,o=e[5].match(/\.(\w+)$/i)[1],i=s[c],s[c]=u?function(){return r[o].apply(this,arguments)}:function(){return this.parent=r[o],i.apply(this,arguments)},s[c].prototype[c]=s[c],a.each(r,(function(e,t){s[c].prototype[t]=r[t]})),a.each(t,(function(e,t){r[t]?s[c].prototype[t]=function(){return this.parent=r[t],e.apply(this,arguments)}:t!==c&&(s[c].prototype[t]=e)}))),a.each(t.static,(function(e,t){s[c][t]=e}))}},cn=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=0;r<t.length;r++){var o=t[r];for(var i in o)if(Oe(o,i)){var a=o[i];void 0!==a&&(e[i]=a)}}return e},sn=function(e,t,n,r){r=r||this,e&&(n&&(e=e[n]),Me(e,(function(e,o){if(!1===t.call(r,e,o,n))return!1;sn(e,t,n,r)})))},fn=function(e,t){var n,r;for(t=t||window,e=e.split("."),n=0;n<e.length;n++)r=e[n],t[r]||(t[r]={}),t=t[r];return t},ln=function(e,t){var n,r;for(t=t||window,e=e.split("."),n=0,r=e.length;n<r;n++)if(t=t[e[n]],!t)break;return t},dn=function(e,t){return!e||rn(e,"array")?e:Fe(e.split(t||","),nn)},mn=function(e){var t=en.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e},gn={trim:nn,isArray:Le,is:rn,toArray:Ie,makeMap:on,each:Me,map:Fe,grep:Ue,inArray:ze,hasOwn:an,extend:cn,create:un,walk:sn,createNS:fn,resolve:ln,explode:dn,_addCacheSuffix:mn},pn=function(e,t){var n=t||document,r=n.createElement("div");if(r.innerHTML=e,!r.hasChildNodes()||r.childNodes.length>1)throw new Error("HTML must have a single root node");return bn(r.childNodes[0])},hn=function(e,t){var n=t||document,r=n.createElement(e);return bn(r)},vn=function(e,t){var n=t||document,r=n.createTextNode(e);return bn(r)},bn=function(e){if(null===e||void 0===e)throw new Error("Node cannot be null or undefined");return{dom:e}},yn=function(e,t,n){return q.from(e.dom.elementFromPoint(t,n)).map(bn)},Cn={fromHtml:pn,fromTag:hn,fromText:vn,fromDom:bn,fromPoint:yn},wn=function(e,t){var n=[],r=function(e){return n.push(e),t(e)},o=t(e);do{o=o.bind(r)}while(o.isSome());return n},xn=function(e,t,n){return 0!==(e.compareDocumentPosition(t)&n)},Sn=function(e,t){return xn(e,t,Node.DOCUMENT_POSITION_CONTAINED_BY)},kn=8,Nn=9,En=11,_n=1,An=3,Rn=function(e,t){var n=e.dom;if(n.nodeType!==_n)return!1;var r=n;if(void 0!==r.matches)return r.matches(t);if(void 0!==r.msMatchesSelector)return r.msMatchesSelector(t);if(void 0!==r.webkitMatchesSelector)return r.webkitMatchesSelector(t);if(void 0!==r.mozMatchesSelector)return r.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},Dn=function(e){return e.nodeType!==_n&&e.nodeType!==Nn&&e.nodeType!==En||0===e.childElementCount},Tn=function(e,t){var n=void 0===t?document:t.dom;return Dn(n)?[]:Q(n.querySelectorAll(e),Cn.fromDom)},On=function(e,t){var n=void 0===t?document:t.dom;return Dn(n)?q.none():q.from(n.querySelector(e)).map(Cn.fromDom)},Bn=function(e,t){return e.dom===t.dom},Pn=function(e,t){var n=e.dom,r=t.dom;return n!==r&&n.contains(r)},Ln=function(e,t){return Sn(e.dom,t.dom)},In=function(e,t){return $t().browser.isIE()?Ln(e,t):Pn(e,t)};"undefined"!==typeof window?window:Function("return this;")();var Mn=function(e){var t=e.dom.nodeName;return t.toLowerCase()},Fn=function(e){return e.dom.nodeType},Un=function(e){return function(t){return Fn(t)===e}},zn=function(e){return Fn(e)===kn||"#comment"===Mn(e)},Hn=Un(_n),jn=Un(An),Vn=Un(Nn),qn=Un(En),$n=function(e){return function(t){return Hn(t)&&Mn(t)===e}},Wn=function(e){return Cn.fromDom(e.dom.ownerDocument)},Kn=function(e){return Vn(e)?e:Wn(e)},Xn=function(e){return Cn.fromDom(Kn(e).dom.documentElement)},Yn=function(e){return Cn.fromDom(Kn(e).dom.defaultView)},Gn=function(e){return q.from(e.dom.parentNode).map(Cn.fromDom)},Jn=function(e,t){var n=N(t)?t:F,r=e.dom,o=[];while(null!==r.parentNode&&void 0!==r.parentNode){var i=r.parentNode,a=Cn.fromDom(i);if(o.push(a),!0===n(a))break;r=i}return o},Qn=function(e){var t=function(t){return ne(t,(function(t){return!Bn(e,t)}))};return Gn(e).map(rr).map(t).getOr([])},Zn=function(e){return q.from(e.dom.previousSibling).map(Cn.fromDom)},er=function(e){return q.from(e.dom.nextSibling).map(Cn.fromDom)},tr=function(e){return le(wn(e,Zn))},nr=function(e){return wn(e,er)},rr=function(e){return Q(e.dom.childNodes,Cn.fromDom)},or=function(e,t){var n=e.dom.childNodes;return q.from(n[t]).map(Cn.fromDom)},ir=function(e){return or(e,0)},ar=function(e){return or(e,e.dom.childNodes.length-1)},ur=function(e){return e.dom.childNodes.length},cr=function(e){var t=e.dom.head;if(null===t||void 0===t)throw new Error("Head is not available yet");return Cn.fromDom(t)},sr=function(e){return qn(e)&&k(e.dom.host)},fr=N(Element.prototype.attachShadow)&&N(Node.prototype.getRootNode),lr=D(fr),dr=fr?function(e){return Cn.fromDom(e.dom.getRootNode())}:Kn,mr=function(e){return sr(e)?e:cr(Kn(e))},gr=function(e){var t=dr(e);return sr(t)?q.some(t):q.none()},pr=function(e){return Cn.fromDom(e.dom.host)},hr=function(e){if(lr()&&k(e.target)){var t=Cn.fromDom(e.target);if(Hn(t)&&vr(t)&&e.composed&&e.composedPath){var n=e.composedPath();if(n)return he(n)}}return q.from(e.target)},vr=function(e){return k(e.dom.shadowRoot)},br=function(e,t){var n=Gn(e);n.each((function(n){n.dom.insertBefore(t.dom,e.dom)}))},yr=function(e,t){var n=er(e);n.fold((function(){var n=Gn(e);n.each((function(e){wr(e,t)}))}),(function(e){br(e,t)}))},Cr=function(e,t){var n=ir(e);n.fold((function(){wr(e,t)}),(function(n){e.dom.insertBefore(t.dom,n.dom)}))},wr=function(e,t){e.dom.appendChild(t.dom)},xr=function(e,t){br(e,t),wr(t,e)},Sr=function(e,t){Z(t,(function(t){br(e,t)}))},kr=function(e,t){Z(t,(function(t){wr(e,t)}))},Nr=function(e){e.dom.textContent="",Z(rr(e),(function(e){Er(e)}))},Er=function(e){var t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},_r=function(e){var t=rr(e);t.length>0&&Sr(e,t),Er(e)},Ar=function(e){var t=jn(e)?e.dom.parentNode:e.dom;if(void 0===t||null===t||null===t.ownerDocument)return!1;var n=t.ownerDocument;return gr(Cn.fromDom(t)).fold((function(){return n.body.contains(t)}),R(Ar,pr))},Rr=function(e,t){var n=function(n,r){return Rr(e+n,t+r)};return{left:e,top:t,translate:n}},Dr=Rr,Tr=function(e){var t=e.getBoundingClientRect();return Dr(t.left,t.top)},Or=function(e,t){return void 0!==e?e:void 0!==t?t:0},Br=function(e){var t=e.dom.ownerDocument,n=t.body,r=t.defaultView,o=t.documentElement;if(n===e.dom)return Dr(n.offsetLeft,n.offsetTop);var i=Or(null===r||void 0===r?void 0:r.pageYOffset,o.scrollTop),a=Or(null===r||void 0===r?void 0:r.pageXOffset,o.scrollLeft),u=Or(o.clientTop,n.clientTop),c=Or(o.clientLeft,n.clientLeft);return Pr(e).translate(a-c,i-u)},Pr=function(e){var t=e.dom,n=t.ownerDocument,r=n.body;return r===t?Dr(r.offsetLeft,r.offsetTop):Ar(e)?Tr(t):Dr(0,0)},Lr=function(e){var t=void 0!==e?e.dom:document,n=t.body.scrollLeft||t.documentElement.scrollLeft,r=t.body.scrollTop||t.documentElement.scrollTop;return Dr(n,r)},Ir=function(e,t,n){var r=void 0!==n?n.dom:document,o=r.defaultView;o&&o.scrollTo(e,t)},Mr=function(e,t){var n=$t().browser.isSafari();n&&N(e.dom.scrollIntoViewIfNeeded)?e.dom.scrollIntoViewIfNeeded(!1):e.dom.scrollIntoView(t)},Fr=function(e){var t=void 0===e?window:e;return $t().browser.isFirefox()?q.none():q.from(t["visualViewport"])},Ur=function(e,t,n,r){return{x:e,y:t,width:n,height:r,right:e+n,bottom:t+r}},zr=function(e){var t=void 0===e?window:e,n=t.document,r=Lr(Cn.fromDom(n));return Fr(t).fold((function(){var e=t.document.documentElement,n=e.clientWidth,o=e.clientHeight;return Ur(r.left,r.top,n,o)}),(function(e){return Ur(Math.max(e.pageLeft,r.left),Math.max(e.pageTop,r.top),e.width,e.height)}))},Hr=function(e){return function(t){return!!t&&t.nodeType===e}},jr=function(e){return!!e&&!Object.getPrototypeOf(e)},Vr=Hr(1),qr=function(e){var t=e.map((function(e){return e.toLowerCase()}));return function(e){if(e&&e.nodeName){var n=e.nodeName.toLowerCase();return G(t,n)}return!1}},$r=function(e,t){var n=t.toLowerCase().split(" ");return function(t){if(Vr(t))for(var r=0;r<n.length;r++){var o=t.ownerDocument.defaultView.getComputedStyle(t,null),i=o?o.getPropertyValue(e):null;if(i===n[r])return!0}return!1}},Wr=function(e){return function(t){return Vr(t)&&t.hasAttribute(e)}},Kr=function(e,t){return function(n){return Vr(n)&&n.getAttribute(e)===t}},Xr=function(e){return Vr(e)&&e.hasAttribute("data-mce-bogus")},Yr=function(e){return Vr(e)&&"all"===e.getAttribute("data-mce-bogus")},Gr=function(e){return Vr(e)&&"TABLE"===e.tagName},Jr=function(e){return function(t){if(Vr(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1}},Qr=qr(["textarea","input"]),Zr=Hr(3),eo=Hr(8),to=Hr(9),no=Hr(11),ro=qr(["br"]),oo=qr(["img"]),io=Jr("true"),ao=Jr("false"),uo=qr(["td","th"]),co=qr(["video","audio","object","embed"]),so=function(e,t,n){return void 0===n&&(n=O),e.exists((function(e){return n(e,t)}))},fo=function(e){for(var t=[],n=function(e){t.push(e)},r=0;r<e.length;r++)e[r].each(n);return t},lo=function(e,t,n){return e.isSome()&&t.isSome()?q.some(n(e.getOrDie(),t.getOrDie())):q.none()},mo=function(e,t,n,r){return e.isSome()&&t.isSome()&&n.isSome()?q.some(r(e.getOrDie(),t.getOrDie(),n.getOrDie())):q.none()},go=function(e,t){return e?q.some(t):q.none()},po=function(e){return void 0!==e.style&&N(e.style.getPropertyValue)},ho=function(e,t,n){if(!(v(n)||w(n)||E(n)))throw new Error("Attribute value was not simple");e.setAttribute(t,n+"")},vo=function(e,t,n){ho(e.dom,t,n)},bo=function(e,t){var n=e.dom;xe(t,(function(e,t){ho(n,t,e)}))},yo=function(e,t){var n=e.dom.getAttribute(t);return null===n?void 0:n},Co=function(e,t){return q.from(yo(e,t))},wo=function(e,t){var n=e.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},xo=function(e,t){e.dom.removeAttribute(t)},So=function(e){return oe(e.dom.attributes,(function(e,t){return e[t.name]=t.value,e}),{})},ko=function(e,t,n){if(!v(n))throw new Error("CSS value must be a string: "+n);po(e)&&e.style.setProperty(t,n)},No=function(e,t){var n=e.dom;xe(t,(function(e,t){ko(n,t,e)}))},Eo=function(e,t){var n=e.dom,r=window.getComputedStyle(n),o=r.getPropertyValue(t);return""!==o||Ar(e)?o:_o(n,t)},_o=function(e,t){return po(e)?e.style.getPropertyValue(t):""},Ao=function(e,t){var n=e.dom,r=_o(n,t);return q.from(r).filter((function(e){return e.length>0}))},Ro=function(e){var t={},n=e.dom;if(po(n))for(var r=0;r<n.style.length;r++){var o=n.style.item(r);t[o]=n.style[o]}return t},Do=function(e){return e.dom.offsetWidth},To=$t().browser,Oo=function(e){return ae(e,Hn)},Bo=function(e){return To.isFirefox()&&"table"===Mn(e)?Oo(rr(e)).filter((function(e){return"caption"===Mn(e)})).bind((function(e){return Oo(nr(e)).map((function(t){var n=t.dom.offsetTop,r=e.dom.offsetTop,o=e.dom.offsetHeight;return n<=r?-o:0}))})).getOr(0):0},Po=function(e,t){return e.children&&G(e.children,t)},Lo=function(e,t,n){var r=0,o=0,i=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===Eo(Cn.fromDom(e),"position")){var a=t.getBoundingClientRect();return r=a.left+(i.documentElement.scrollLeft||e.scrollLeft)-i.documentElement.clientLeft,o=a.top+(i.documentElement.scrollTop||e.scrollTop)-i.documentElement.clientTop,{x:r,y:o}}var u=t;while(u&&u!==n&&u.nodeType&&!Po(u,n)){var c=u;r+=c.offsetLeft||0,o+=c.offsetTop||0,u=c.offsetParent}u=t.parentNode;while(u&&u!==n&&u.nodeType&&!Po(u,n))r-=u.scrollLeft||0,o-=u.scrollTop||0,u=u.parentNode;o+=Bo(Cn.fromDom(t))}return{x:r,y:o}},Io={},Mo={exports:Io};(function(e,n,r,o){(function(t,o){"object"===typeof n&&"undefined"!==typeof r?r.exports=o():"function"===typeof e&&e.amd?e(o):(t="undefined"!==typeof globalThis?globalThis:t||self,t.EphoxContactWrapper=o())})(this,(function(){var e="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof t?t:"undefined"!==typeof self?self:{},n={exports:{}};(function(t){(function(e){var n=setTimeout;function r(){}function o(e,t){return function(){e.apply(t,arguments)}}function i(e){if("object"!==typeof this)throw new TypeError("Promises must be constructed via new");if("function"!==typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],l(e,this)}function a(e,t){while(3===e._state)e=e._value;0!==e._state?(e._handled=!0,i._immediateFn((function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var r;try{r=n(e._value)}catch(o){return void c(t.promise,o)}u(t.promise,r)}else(1===e._state?u:c)(t.promise,e._value)}))):e._deferreds.push(t)}function u(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"===typeof t||"function"===typeof t)){var n=t.then;if(t instanceof i)return e._state=3,e._value=t,void s(e);if("function"===typeof n)return void l(o(n,t),e)}e._state=1,e._value=t,s(e)}catch(r){c(e,r)}}function c(e,t){e._state=2,e._value=t,s(e)}function s(e){2===e._state&&0===e._deferreds.length&&i._immediateFn((function(){e._handled||i._unhandledRejectionFn(e._value)}));for(var t=0,n=e._deferreds.length;t<n;t++)a(e,e._deferreds[t]);e._deferreds=null}function f(e,t,n){this.onFulfilled="function"===typeof e?e:null,this.onRejected="function"===typeof t?t:null,this.promise=n}function l(e,t){var n=!1;try{e((function(e){n||(n=!0,u(t,e))}),(function(e){n||(n=!0,c(t,e))}))}catch(r){if(n)return;n=!0,c(t,r)}}i.prototype["catch"]=function(e){return this.then(null,e)},i.prototype.then=function(e,t){var n=new this.constructor(r);return a(this,new f(e,t,n)),n},i.all=function(e){var t=Array.prototype.slice.call(e);return new i((function(e,n){if(0===t.length)return e([]);var r=t.length;function o(i,a){try{if(a&&("object"===typeof a||"function"===typeof a)){var u=a.then;if("function"===typeof u)return void u.call(a,(function(e){o(i,e)}),n)}t[i]=a,0===--r&&e(t)}catch(c){n(c)}}for(var i=0;i<t.length;i++)o(i,t[i])}))},i.resolve=function(e){return e&&"object"===typeof e&&e.constructor===i?e:new i((function(t){t(e)}))},i.reject=function(e){return new i((function(t,n){n(e)}))},i.race=function(e){return new i((function(t,n){for(var r=0,o=e.length;r<o;r++)e[r].then(t,n)}))},i._immediateFn="function"===typeof setImmediate?function(e){setImmediate(e)}:function(e){n(e,0)},i._unhandledRejectionFn=function(e){"undefined"!==typeof console&&console},i._setImmediateFn=function(e){i._immediateFn=e},i._setUnhandledRejectionFn=function(e){i._unhandledRejectionFn=e},t.exports?t.exports=i:e.Promise||(e.Promise=i)})(e)})(n);var r=n.exports,o=function(){return"undefined"!==typeof window?window:Function("return this;")()}(),i={boltExport:o.Promise||r};return i}))})(void 0,Io,Mo);var Fo=Mo.exports.boltExport,Uo=function(e){var t=q.none(),n=[],r=function(e){return Uo((function(t){o((function(n){t(e(n))}))}))},o=function(e){a()?c(e):n.push(e)},i=function(e){a()||(t=q.some(e),u(n),n=[])},a=function(){return t.isSome()},u=function(e){Z(e,c)},c=function(e){t.each((function(t){setTimeout((function(){e(t)}),0)}))};return e(i),{get:o,map:r,isReady:a}},zo=function(e){return Uo((function(t){t(e)}))},Ho={nu:Uo,pure:zo},jo=function(e){setTimeout((function(){throw e}),0)},Vo=function(e){var t=function(t){e().then(t,jo)},n=function(t){return Vo((function(){return e().then(t)}))},r=function(t){return Vo((function(){return e().then((function(e){return t(e).toPromise()}))}))},o=function(t){return Vo((function(){return e().then((function(){return t.toPromise()}))}))},i=function(){return Ho.nu(t)},a=function(){var t=null;return Vo((function(){return null===t&&(t=e()),t}))},u=e;return{map:n,bind:r,anonBind:o,toLazy:i,toCached:a,toPromise:u,get:t}},qo=function(e){return Vo((function(){return new Fo(e)}))},$o=function(e){return Vo((function(){return Fo.resolve(e)}))},Wo={nu:qo,pure:$o},Ko=function(e,t){return t((function(t){var n=[],r=0,o=function(o){return function(i){n[o]=i,r++,r>=e.length&&t(n)}};0===e.length?t([]):Z(e,(function(e,t){e.get(o(t))}))}))},Xo=function(e){return Ko(e,Wo.nu)},Yo=function(e){var t=function(t){return Yo(e)},n=function(t){return Yo(e)},r=function(t){return Yo(t(e))},o=function(t){return Yo(e)},i=function(t){t(e)},a=function(t){return t(e)},u=function(t,n){return n(e)},c=function(t){return t(e)},s=function(t){return t(e)},f=function(){return q.some(e)};return{isValue:U,isError:F,getOr:D(e),getOrThunk:D(e),getOrDie:D(e),or:t,orThunk:n,fold:u,map:r,mapError:o,each:i,bind:a,exists:c,forall:s,toOptional:f}},Go=function(e){var t=function(e){return e()},n=function(){return L(String(e))()},r=T,o=function(e){return e()},i=function(t){return Go(e)},a=function(t){return Go(t(e))},u=function(t){return Go(e)},c=function(t,n){return t(e)};return{isValue:F,isError:U,getOr:T,getOrThunk:t,getOrDie:n,or:r,orThunk:o,fold:c,map:i,mapError:a,each:_,bind:u,exists:F,forall:U,toOptional:q.none}},Jo=function(e,t){return e.fold((function(){return Go(t)}),Yo)},Qo={value:Yo,error:Go,fromOption:Jo},Zo=function(e){if(!y(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");var t=[],n={};return Z(e,(function(r,o){var i=Ce(r);if(1!==i.length)throw new Error("one and only one name per case");var a=i[0],u=r[a];if(void 0!==n[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!y(u))throw new Error("case arguments must be an array");t.push(a),n[a]=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=n.length;if(i!==u.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+u.length+" ("+u+"), got "+i);var c=function(e){var r=Ce(e);if(t.length!==r.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+r.join(","));var o=fe(t,(function(e){return G(r,e)}));if(!o)throw new Error("Not all branches were specified when using match. Specified: "+r.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,n)};return{fold:function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);var i=t[o];return i.apply(null,n)},match:c,log:function(e){}}}})),n},ei={generate:Zo};ei.generate([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);var ti=function(e){return e.fold(T,T)};function ni(e,t,n,r,o){return e(n,r)?q.some(n):N(o)&&o(n)?q.none():t(n,r,o)}var ri,oi,ii,ai,ui,ci,si,fi,li,di,mi,gi,pi,hi,vi,bi,yi,Ci,wi,xi=function(e,t,n){var r=e.dom,o=N(n)?n:F;while(r.parentNode){r=r.parentNode;var i=Cn.fromDom(r);if(t(i))return q.some(i);if(o(i))break}return q.none()},Si=function(e,t,n){var r=function(e,t){return t(e)};return ni(r,xi,e,t,n)},ki=function(e,t){var n=e.dom;return n.parentNode?Ni(Cn.fromDom(n.parentNode),(function(n){return!Bn(e,n)&&t(n)})):q.none()},Ni=function(e,t){var n=function(e){return t(Cn.fromDom(e))},r=ae(e.dom.childNodes,n);return r.map(Cn.fromDom)},Ei=function(e,t,n){return xi(e,(function(e){return Rn(e,t)}),n)},_i=function(e,t){return On(t,e)},Ai=function(e,t,n){var r=function(e,t){return Rn(e,t)};return ni(r,Ei,e,t,n)},Ri=window.Promise?window.Promise:Fo,Di=function(e,t){for(var n=window.requestAnimationFrame,r=["ms","moz","webkit"],o=function(e){window.setTimeout(e,0)},i=0;i<r.length&&!n;i++)n=window[r[i]+"RequestAnimationFrame"];n||(n=o),n(e,t)},Ti=function(e,t){return"number"!==typeof t&&(t=0),setTimeout(e,t)},Oi=function(e,t){return"number"!==typeof t&&(t=1),setInterval(e,t)},Bi=function(e){return clearTimeout(e)},Pi=function(e){return clearInterval(e)},Li=function(e,t){var n,r=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];clearTimeout(n),n=Ti((function(){e.apply(this,r)}),t)};return r.stop=function(){clearTimeout(n)},r},Ii={requestAnimationFrame:function(e,t){ri?ri.then(e):ri=new Ri((function(e){t||(t=document.body),Di(e,t)})).then(e)},setTimeout:Ti,setInterval:Oi,setEditorTimeout:function(e,t,n){return Ti((function(){e.removed||t()}),n)},setEditorInterval:function(e,t,n){var r=Oi((function(){e.removed?clearInterval(r):t()}),n);return r},debounce:Li,throttle:Li,clearInterval:Pi,clearTimeout:Bi},Mi=function(e,t){void 0===t&&(t={});var n=0,r={},o=Cn.fromDom(e),i=Kn(o),a=t.maxLoadTime||5e3,u=function(e){t.referrerPolicy=e},c=function(e){wr(mr(o),e)},s=function(e){var t=mr(o);_i(t,"#"+e).each(Er)},f=function(e){return Te(r,e).getOrThunk((function(){return{id:"mce-u"+n++,passed:[],failed:[],count:0}}))},l=function(n,o,u){var s,l=gn._addCacheSuffix(n),d=f(l);r[l]=d,d.count++;var m=function(e,t){var n=e.length;while(n--)e[n]();d.status=t,d.passed=[],d.failed=[],s&&(s.onload=null,s.onerror=null,s=null)},g=function(){return m(d.passed,2)},p=function(){return m(d.failed,3)},h=function(e,t){e()||(Date.now()-y<a?Ii.setTimeout(t):p())},v=function(){h((function(){var t=e.styleSheets,n=t.length;while(n--){var r=t[n],o=r.ownerNode;if(o&&o.id===s.id)return g(),!0}return!1}),v)};if(o&&d.passed.push(o),u&&d.failed.push(u),1!==d.status)if(2!==d.status)if(3!==d.status){d.status=1;var b=Cn.fromTag("link",i.dom);bo(b,{rel:"stylesheet",type:"text/css",id:d.id});var y=Date.now();t.contentCssCors&&vo(b,"crossOrigin","anonymous"),t.referrerPolicy&&vo(b,"referrerpolicy",t.referrerPolicy),s=b.dom,s.onload=v,s.onerror=p,c(b),vo(b,"href",l)}else p();else g()},d=function(e){return Wo.nu((function(t){l(e,A(t,D(Qo.value(e))),A(t,D(Qo.error(e))))}))},m=function(e,t,n){Xo(Q(e,d)).get((function(e){var r=te(e,(function(e){return e.isValue()}));r.fail.length>0?n(r.fail.map(ti)):t(r.pass.map(ti))}))},g=function(e){var t=gn._addCacheSuffix(e);Te(r,t).each((function(e){var n=--e.count;0===n&&(delete r[t],s(e.id))}))},p=function(e){Z(e,(function(e){g(e)}))};return{load:l,loadAll:m,unload:g,unloadAll:p,_setReferrerPolicy:u}},Fi=function(){var e=new WeakMap,t=function(t,n){var r=dr(t),o=r.dom;return q.from(e.get(o)).getOrThunk((function(){var t=Mi(o,n);return e.set(o,t),t}))};return{forElement:t}},Ui=Fi(),zi=function(){function e(e,t){this.node=e,this.rootNode=t,this.current=this.current.bind(this),this.next=this.next.bind(this),this.prev=this.prev.bind(this),this.prev2=this.prev2.bind(this)}return e.prototype.current=function(){return this.node},e.prototype.next=function(e){return this.node=this.findSibling(this.node,"firstChild","nextSibling",e),this.node},e.prototype.prev=function(e){return this.node=this.findSibling(this.node,"lastChild","previousSibling",e),this.node},e.prototype.prev2=function(e){return this.node=this.findPreviousNode(this.node,"lastChild","previousSibling",e),this.node},e.prototype.findSibling=function(e,t,n,r){var o,i;if(e){if(!r&&e[t])return e[t];if(e!==this.rootNode){if(o=e[n],o)return o;for(i=e.parentNode;i&&i!==this.rootNode;i=i.parentNode)if(o=i[n],o)return o}}},e.prototype.findPreviousNode=function(e,t,n,r){var o,i,a;if(e){if(o=e[n],this.rootNode&&o===this.rootNode)return;if(o){if(!r)for(a=o[t];a;a=a[t])if(!a[t])return a;return o}if(i=e.parentNode,i&&i!==this.rootNode)return i}},e}(),Hi=["article","aside","details","div","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","p","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"],ji=["td","th"],Vi=["thead","tbody","tfoot"],qi=["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"],$i=["h1","h2","h3","h4","h5","h6"],Wi=["li","dd","dt"],Ki=["ul","ol","dl"],Xi=["pre","script","textarea","style"],Yi=function(e){var t;return function(n){return t=t||me(e,U),Oe(t,Mn(n))}},Gi=Yi($i),Ji=Yi(Hi),Qi=function(e){return"table"===Mn(e)},Zi=function(e){return Hn(e)&&!Ji(e)},ea=function(e){return Hn(e)&&"br"===Mn(e)},ta=Yi(qi),na=Yi(Ki),ra=Yi(Wi),oa=Yi(Vi),ia=Yi(ji),aa=Yi(Xi),ua=function(e,t,n){return Ei(e,t,n).isSome()},ca="\ufeff",sa=" ",fa=function(e){return e===ca},la=function(e){return e.replace(/\uFEFF/g,"")},da=ca,ma=fa,ga=la,pa=Vr,ha=Zr,va=function(e){return ha(e)&&(e=e.parentNode),pa(e)&&e.hasAttribute("data-mce-caret")},ba=function(e){return ha(e)&&ma(e.data)},ya=function(e){return va(e)||ba(e)},Ca=function(e){return e.firstChild!==e.lastChild||!ro(e.firstChild)},wa=function(e,t){var n=e.ownerDocument,r=n.createTextNode(da),o=e.parentNode;if(t){i=e.previousSibling;if(ha(i)){if(ya(i))return i;if(_a(i))return i.splitText(i.data.length-1)}o.insertBefore(r,e)}else{var i=e.nextSibling;if(ha(i)){if(ya(i))return i;if(Ea(i))return i.splitText(1),i}e.nextSibling?o.insertBefore(r,e.nextSibling):o.appendChild(r)}return r},xa=function(e){var t=e.container();return!!Zr(t)&&(t.data.charAt(e.offset())===da||e.isAtStart()&&ba(t.previousSibling))},Sa=function(e){var t=e.container();return!!Zr(t)&&(t.data.charAt(e.offset()-1)===da||e.isAtEnd()&&ba(t.nextSibling))},ka=function(){var e=document.createElement("br");return e.setAttribute("data-mce-bogus","1"),e},Na=function(e,t,n){var r=t.ownerDocument,o=r.createElement(e);o.setAttribute("data-mce-caret",n?"before":"after"),o.setAttribute("data-mce-bogus","all"),o.appendChild(ka());var i=t.parentNode;return n?i.insertBefore(o,t):t.nextSibling?i.insertBefore(o,t.nextSibling):i.appendChild(o),o},Ea=function(e){return ha(e)&&e.data[0]===da},_a=function(e){return ha(e)&&e.data[e.data.length-1]===da},Aa=function(e){var t=e.getElementsByTagName("br"),n=t[t.length-1];Xr(n)&&n.parentNode.removeChild(n)},Ra=function(e){return e&&e.hasAttribute("data-mce-caret")?(Aa(e),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("_moz_abspos"),e):null},Da=function(e){return va(e.startContainer)},Ta=io,Oa=ao,Ba=ro,Pa=Zr,La=qr(["script","style","textarea"]),Ia=qr(["img","input","textarea","hr","iframe","video","audio","object","embed"]),Ma=qr(["table"]),Fa=ya,Ua=function(e){return!Fa(e)&&(Pa(e)?!La(e.parentNode):Ia(e)||Ba(e)||Ma(e)||Ha(e))},za=function(e){return Vr(e)&&"true"===e.getAttribute("unselectable")},Ha=function(e){return!1===za(e)&&Oa(e)},ja=function(e,t){for(e=e.parentNode;e&&e!==t;e=e.parentNode){if(Ha(e))return!1;if(Ta(e))return!0}return!0},Va=function(e){return!!Ha(e)&&!0!==oe(be(e.getElementsByTagName("*")),(function(e,t){return e||Ta(t)}),!1)},qa=function(e){return Ia(e)||Va(e)},$a=function(e,t){return Ua(e)&&ja(e,t)},Wa=/^[ \t\r\n]*$/,Ka=function(e){return Wa.test(e)},Xa=function(e,t){var n=Cn.fromDom(t),r=Cn.fromDom(e);return ua(r,"pre,code",B(Bn,n))},Ya=function(e,t){return Zr(e)&&Ka(e.data)&&!1===Xa(e,t)},Ga=function(e){return Vr(e)&&"A"===e.nodeName&&!e.hasAttribute("href")&&(e.hasAttribute("name")||e.hasAttribute("id"))},Ja=function(e,t){return Ua(e)&&!1===Ya(e,t)||Ga(e)||Qa(e)},Qa=Wr("data-mce-bookmark"),Za=Wr("data-mce-bogus"),eu=Kr("data-mce-bogus","all"),tu=function(e,t){var n=0;if(Ja(e,e))return!1;var r=e.firstChild;if(!r)return!0;var o=new zi(r,e);do{if(t){if(eu(r)){r=o.next(!0);continue}if(Za(r)){r=o.next();continue}}if(ro(r))n++,r=o.next();else{if(Ja(r,e))return!1;r=o.next()}}while(r);return n<=1},nu=function(e,t){return void 0===t&&(t=!0),tu(e.dom,t)},ru=function(e){return"span"===e.nodeName.toLowerCase()},ou=function(e,t){return k(e)&&(Ja(e,t)||Zi(Cn.fromDom(e)))},iu=function(e,t){var n=new zi(e,t).prev(!1),r=new zi(e,t).next(!1),o=x(n)||ou(n,t),i=x(r)||ou(r,t);return o&&i},au=function(e){return ru(e)&&"bookmark"===e.getAttribute("data-mce-type")},uu=function(e,t){return Zr(e)&&e.data.length>0&&iu(e,t)},cu=function(e){return!!Vr(e)&&e.childNodes.length>0},su=function(e){return no(e)||to(e)},fu=function(e,t,n){var r=n||t;if(Vr(t)&&au(t))return t;for(var o=t.childNodes,i=o.length-1;i>=0;i--)fu(e,o[i],r);if(Vr(t)){var a=t.childNodes;1===a.length&&au(a[0])&&t.parentNode.insertBefore(a[0],t)}return su(t)||Ja(t,r)||cu(t)||uu(t,r)||e.remove(t),t},lu=gn.makeMap,du=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,mu=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,gu=/[<>&\"\']/g,pu=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,hu={128:"€",130:"‚",131:"ƒ",132:"„",133:"…",134:"†",135:"‡",136:"ˆ",137:"‰",138:"Š",139:"‹",140:"Œ",142:"Ž",145:"‘",146:"’",147:"“",148:"”",149:"•",150:"–",151:"—",152:"˜",153:"™",154:"š",155:"›",156:"œ",158:"ž",159:"Ÿ"},vu={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},bu={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"},yu=function(e){var t=Cn.fromTag("div").dom;return t.innerHTML=e,t.textContent||t.innerText||e},Cu=function(e,t){var n,r,o,i={};if(e){for(e=e.split(","),t=t||10,n=0;n<e.length;n+=2)r=String.fromCharCode(parseInt(e[n],t)),vu[r]||(o="&"+e[n+1]+";",i[r]=o,i[o]=r);return i}},wu=Cu("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32),xu=function(e,t){return e.replace(t?du:mu,(function(e){return vu[e]||e}))},Su=function(e){return(""+e).replace(gu,(function(e){return vu[e]||e}))},ku=function(e,t){return e.replace(t?du:mu,(function(e){return e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":vu[e]||"&#"+e.charCodeAt(0)+";"}))},Nu=function(e,t,n){return n=n||wu,e.replace(t?du:mu,(function(e){return vu[e]||n[e]||e}))},Eu=function(e,t){var n=Cu(t)||wu,r=function(e,t){return e.replace(t?du:mu,(function(e){return void 0!==vu[e]?vu[e]:void 0!==n[e]?n[e]:e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";"}))},o=function(e,t){return Nu(e,t,n)},i=lu(e.replace(/\+/g,","));return i.named&&i.numeric?r:i.named?t?o:Nu:i.numeric?ku:xu},_u=function(e){return e.replace(pu,(function(e,t){return t?(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10),t>65535?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):hu[t]||String.fromCharCode(t)):bu[e]||wu[e]||yu(e)}))},Au={encodeRaw:xu,encodeAllRaw:Su,encodeNumeric:ku,encodeNamed:Nu,getEncodeFunc:Eu,decode:_u},Ru={},Du={},Tu=gn.makeMap,Ou=gn.each,Bu=gn.extend,Pu=gn.explode,Lu=gn.inArray,Iu=function(e,t){return e=gn.trim(e),e?e.split(t||" "):[]},Mu=function(e,t){var n=Tu(e," ",Tu(e.toUpperCase()," "));return Bu(n,t)},Fu=function(e){return Mu("td th li dt dd figcaption caption details summary",e.getTextBlockElements())},Uu=function(e){var t,n,r,o,i,a,u={},c=function(e,n,r){var o,i,a,c=function(e,t){var n,r,o={};for(n=0,r=e.length;n<r;n++)o[e[n]]=t||{};return o};r=r||[],n=n||"","string"===typeof r&&(r=Iu(r));var s=Iu(e);o=s.length;while(o--)i=Iu([t,n].join(" ")),a={attributes:c(i),attributesOrder:i,children:c(r,Du)},u[s[o]]=a},s=function(e,t){var n,r,o,i,a=Iu(e);n=a.length;var c=Iu(t);while(n--)for(r=u[a[n]],o=0,i=c.length;o<i;o++)r.attributes[c[o]]={},r.attributesOrder.push(c[o])};return Ru[e]?Ru[e]:(t="id accesskey class dir lang style tabindex title role",n="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",r="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(t+=" contenteditable contextmenu draggable dropzone hidden spellcheck translate",n+=" article aside details dialog figure main header footer hgroup section nav",r+=" audio canvas command datalist mark meter output picture progress time wbr video ruby bdi keygen"),"html5-strict"!==e&&(t+=" xml:lang",a="acronym applet basefont big font strike tt",r=[r,a].join(" "),Ou(Iu(a),(function(e){c(e,"",r)})),i="center dir isindex noframes",n=[n,i].join(" "),o=[n,r].join(" "),Ou(Iu(i),(function(e){c(e,"",o)}))),o=o||[n,r].join(" "),c("html","manifest","head body"),c("head","","base command link meta noscript script style title"),c("title hr noscript br"),c("base","href target"),c("link","href rel media hreflang type sizes hreflang"),c("meta","name http-equiv content charset"),c("style","media type scoped"),c("script","src async defer type charset"),c("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",o),c("address dt dd div caption","",o),c("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",r),c("blockquote","cite",o),c("ol","reversed start type","li"),c("ul","","li"),c("li","value",o),c("dl","","dt dd"),c("a","href target rel media hreflang type",r),c("q","cite",r),c("ins del","cite datetime",o),c("img","src sizes srcset alt usemap ismap width height"),c("iframe","src name width height",o),c("embed","src type width height"),c("object","data type typemustmatch name usemap form width height",[o,"param"].join(" ")),c("param","name value"),c("map","name",[o,"area"].join(" ")),c("area","alt coords shape href target rel media hreflang type"),c("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),c("colgroup","span","col"),c("col","span"),c("tbody thead tfoot","","tr"),c("tr","","td th"),c("td","colspan rowspan headers",o),c("th","colspan rowspan headers scope abbr",o),c("form","accept-charset action autocomplete enctype method name novalidate target",o),c("fieldset","disabled form name",[o,"legend"].join(" ")),c("label","form for",r),c("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),c("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?o:r),c("select","disabled form multiple name required size","option optgroup"),c("optgroup","disabled label","option"),c("option","disabled label selected value"),c("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),c("menu","type label",[o,"li"].join(" ")),c("noscript","",o),"html4"!==e&&(c("wbr"),c("ruby","",[r,"rt rp"].join(" ")),c("figcaption","",o),c("mark rt rp summary bdi","",r),c("canvas","width height",o),c("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[o,"track source"].join(" ")),c("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[o,"track source"].join(" ")),c("picture","","img source"),c("source","src srcset type media sizes"),c("track","kind src srclang label default"),c("datalist","",[r,"option"].join(" ")),c("article section nav aside main header footer","",o),c("hgroup","","h1 h2 h3 h4 h5 h6"),c("figure","",[o,"figcaption"].join(" ")),c("time","datetime",r),c("dialog","open",o),c("command","type label icon disabled checked radiogroup command"),c("output","for form name",r),c("progress","value max",r),c("meter","value min max low high optimum",r),c("details","open",[o,"summary"].join(" ")),c("keygen","autofocus challenge disabled form keytype name")),"html5-strict"!==e&&(s("script","language xml:space"),s("style","xml:space"),s("object","declare classid code codebase codetype archive standby align border hspace vspace"),s("embed","align name hspace vspace"),s("param","valuetype type"),s("a","charset name rev shape coords"),s("br","clear"),s("applet","codebase archive code object alt name width height align hspace vspace"),s("img","name longdesc align border hspace vspace"),s("iframe","longdesc frameborder marginwidth marginheight scrolling align"),s("font basefont","size color face"),s("input","usemap align"),s("select"),s("textarea"),s("h1 h2 h3 h4 h5 h6 div p legend caption","align"),s("ul","type compact"),s("li","type"),s("ol dl menu dir","compact"),s("pre","width xml:space"),s("hr","align noshade size width"),s("isindex","prompt"),s("table","summary width frame rules cellspacing cellpadding align bgcolor"),s("col","width align char charoff valign"),s("colgroup","width align char charoff valign"),s("thead","align char charoff valign"),s("tr","align char charoff valign bgcolor"),s("th","axis align char charoff valign nowrap bgcolor width height"),s("form","accept"),s("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),s("tfoot","align char charoff valign"),s("tbody","align char charoff valign"),s("area","nohref"),s("body","background bgcolor text link vlink alink")),"html4"!==e&&(s("input button select textarea","autofocus"),s("input textarea","placeholder"),s("a","download"),s("link script img","crossorigin"),s("img","loading"),s("iframe","sandbox seamless allowfullscreen loading")),Ou(Iu("a form meter progress dfn"),(function(e){u[e]&&delete u[e].children[e]})),delete u.caption.children.table,delete u.script,Ru[e]=u,u)},zu=function(e,t){var n;return e&&(n={},"string"===typeof e&&(e={"*":e}),Ou(e,(function(e,r){n[r]=n[r.toUpperCase()]="map"===t?Tu(e,/[, ]/):Pu(e,/[, ]/)}))),n},Hu=function(e){var t={},n={},r=[],o={},i={},a=function(t,n,r){var o=e[t];return o?o=Tu(o,/[, ]/,Tu(o.toUpperCase(),/[, ]/)):(o=Ru[t],o||(o=Mu(n,r),Ru[t]=o)),o};e=e||{};var u=Uu(e.schema);!1===e.verify_html&&(e.valid_elements="*[*]");var c=zu(e.valid_styles),s=zu(e.invalid_styles,"map"),f=zu(e.valid_classes,"map"),l=a("whitespace_elements","pre script noscript style textarea video audio iframe object code"),d=a("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),m=a("short_ended_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),g=a("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls"),p="td th iframe video audio object script code",h=a("non_empty_elements",p+" pre",m),v=a("move_caret_before_on_enter_elements",p+" table",m),b=a("text_block_elements","h1 h2 h3 h4 h5 h6 p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),y=a("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary",b),C=a("text_inline_elements","span strong b em i font s strike u var cite dfn code mark q sup sub samp");Ou((e.special||"script noscript iframe noframes noembed title style textarea xmp").split(" "),(function(e){i[e]=new RegExp("</"+e+"[^>]*>","gi")}));var w=function(e){return new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$")},x=function(e){var n,o,i,a,u,c,s,f,l,d,m,g,p,h,v,b,y,C,x=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)])?$/,S=/^([!\-])?(\w+[\\:]:\w+|[^=:<]+)?(?:([=:<])(.*))?$/,k=/[*?+]/;if(e){var N=Iu(e,",");for(t["@"]&&(b=t["@"].attributes,y=t["@"].attributesOrder),n=0,o=N.length;n<o;n++)if(u=x.exec(N[n]),u){if(h=u[1],l=u[2],v=u[3],f=u[5],g={},p=[],c={attributes:g,attributesOrder:p},"#"===h&&(c.paddEmpty=!0),"-"===h&&(c.removeEmpty=!0),"!"===u[4]&&(c.removeEmptyAttrs=!0),b&&(xe(b,(function(e,t){g[t]=e})),p.push.apply(p,y)),f)for(f=Iu(f,"|"),i=0,a=f.length;i<a;i++)if(u=S.exec(f[i]),u){if(s={},m=u[1],d=u[2].replace(/[\\:]:/g,":"),h=u[3],C=u[4],"!"===m&&(c.attributesRequired=c.attributesRequired||[],c.attributesRequired.push(d),s.required=!0),"-"===m){delete g[d],p.splice(Lu(p,d),1);continue}h&&("="===h&&(c.attributesDefault=c.attributesDefault||[],c.attributesDefault.push({name:d,value:C}),s.defaultValue=C),":"===h&&(c.attributesForced=c.attributesForced||[],c.attributesForced.push({name:d,value:C}),s.forcedValue=C),"<"===h&&(s.validValues=Tu(C,"?"))),k.test(d)?(c.attributePatterns=c.attributePatterns||[],s.pattern=w(d),c.attributePatterns.push(s)):(g[d]||p.push(d),g[d]=s)}b||"@"!==l||(b=g,y=p),v&&(c.outputName=l,t[v]=c),k.test(l)?(c.pattern=w(l),r.push(c)):t[l]=c}}},S=function(e){t={},r=[],x(e),Ou(u,(function(e,t){n[t]=e.children}))},k=function(e){var r=/^(~)?(.+)$/;e&&(Ru.text_block_elements=Ru.block_elements=null,Ou(Iu(e,","),(function(e){var i=r.exec(e),a="~"===i[1],u=a?"span":"div",c=i[2];if(n[c]=n[u],o[c]=u,a||(y[c.toUpperCase()]={},y[c]={}),!t[c]){var s=t[u];s=Bu({},s),delete s.removeEmptyAttrs,delete s.removeEmpty,t[c]=s}Ou(n,(function(e,t){e[u]&&(n[t]=e=Bu({},n[t]),e[c]=e[u])}))})))},N=function(t){var r=/^([+\-]?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)\[([^\]]+)]$/;Ru[e.schema]=null,t&&Ou(Iu(t,","),(function(e){var t,o,i=r.exec(e);i&&(o=i[1],t=o?n[i[2]]:n[i[2]]={"#comment":{}},t=n[i[2]],Ou(Iu(i[3],"|"),(function(e){"-"===o?delete t[e]:t[e]={}})))}))},E=function(e){var n,o=t[e];if(o)return o;n=r.length;while(n--)if(o=r[n],o.pattern.test(e))return o};e.valid_elements?S(e.valid_elements):(Ou(u,(function(e,r){t[r]={attributes:e.attributes,attributesOrder:e.attributesOrder},n[r]=e.children})),"html5"!==e.schema&&Ou(Iu("strong/b em/i"),(function(e){var n=Iu(e,"/");t[n[1]].outputName=n[0]})),Ou(C,(function(n,r){t[r]&&(e.padd_empty_block_inline_children&&(t[r].paddInEmptyBlock=!0),t[r].removeEmpty=!0)})),Ou(Iu("ol ul blockquote a table tbody"),(function(e){t[e]&&(t[e].removeEmpty=!0)})),Ou(Iu("p h1 h2 h3 h4 h5 h6 th td pre div address caption li"),(function(e){t[e].paddEmpty=!0})),Ou(Iu("span"),(function(e){t[e].removeEmptyAttrs=!0}))),k(e.custom_elements),N(e.valid_children),x(e.extended_valid_elements),N("+ol[ul|ol],+ul[ul|ol]"),Ou({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},(function(e,n){t[n]&&(t[n].parentsRequired=Iu(e))})),e.invalid_elements&&Ou(Pu(e.invalid_elements),(function(e){t[e]&&delete t[e]})),E("span")||x("span[!data-mce-type|*]");var _=D(c),A=D(s),R=D(f),T=D(g),O=D(y),B=D(b),P=D(C),L=D(m),I=D(d),M=D(h),F=D(v),U=D(l),z=D(i),H=function(e,t){var r=n[e.toLowerCase()];return!(!r||!r[t.toLowerCase()])},j=function(e,t){var n,r,o=E(e);if(o){if(!t)return!0;if(o.attributes[t])return!0;if(n=o.attributePatterns,n){r=n.length;while(r--)if(n[r].pattern.test(e))return!0}}return!1},V=D(o);return{children:n,elements:t,getValidStyles:_,getValidClasses:R,getBlockElements:O,getInvalidStyles:A,getShortEndedElements:L,getTextBlockElements:B,getTextInlineElements:P,getBoolAttrs:T,getElementRule:E,getSelfClosingElements:I,getNonEmptyElements:M,getMoveCaretBeforeOnEnterElements:F,getWhiteSpaceElements:U,getSpecialElements:z,isValidChild:H,isValid:j,getCustomElements:V,addValidElements:x,setValidElements:S,addCustomElements:k,addValidChildren:N}},ju=function(e,t,n,r){var o=function(e){return e=parseInt(e,10).toString(16),e.length>1?e:"0"+e};return"#"+o(t)+o(n)+o(r)},Vu=function(e,t){var n,r,o,i=this,a=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,u=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,c=/\s*([^:]+):\s*([^;]+);?/g,s=/\s+$/,f={},l=ca;e=e||{},t&&(r=t.getValidStyles(),o=t.getInvalidStyles());var d=("\\\" \\' \\; \\: ; : "+l).split(" ");for(n=0;n<d.length;n++)f[d[n]]=l+n,f[l+n]=d[n];return{toHex:function(e){return e.replace(a,ju)},parse:function(t){var r,o,d,m,g={},p=e.url_converter,h=e.url_converter_scope||i,v=function(e,t,r){var o=g[e+"-top"+t];if(o){var i=g[e+"-right"+t];if(i){var a=g[e+"-bottom"+t];if(a){var u=g[e+"-left"+t];if(u){var c=[o,i,a,u];n=c.length-1;while(n--)if(c[n]!==c[n+1])break;n>-1&&r||(g[e+t]=-1===n?c[0]:c.join(" "),delete g[e+"-top"+t],delete g[e+"-right"+t],delete g[e+"-bottom"+t],delete g[e+"-left"+t])}}}}},b=function(e){var t,n=g[e];if(n){n=n.split(" "),t=n.length;while(t--)if(n[t]!==n[0])return!1;return g[e]=n[0],!0}},y=function(e,t,n,r){b(t)&&b(n)&&b(r)&&(g[e]=g[t]+" "+g[n]+" "+g[r],delete g[t],delete g[n],delete g[r])},C=function(e){return m=!0,f[e]},w=function(e,t){return m&&(e=e.replace(/\uFEFF[0-9]/g,(function(e){return f[e]}))),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e},x=function(e){return String.fromCharCode(parseInt(e.slice(1),16))},S=function(e){return e.replace(/\\[0-9a-f]+/gi,x)},k=function(t,n,r,o,i,a){if(i=i||a,i)return i=w(i),"'"+i.replace(/\'/g,"\\'")+"'";if(n=w(n||r||o),!e.allow_script_urls){var u=n.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(u))return"";if(!e.allow_svg_data_urls&&/^data:image\/svg/i.test(u))return""}return p&&(n=p.call(h,n,"style")),"url('"+n.replace(/\'/g,"\\'")+"')"};if(t){t=t.replace(/[\u0000-\u001F]/g,""),t=t.replace(/\\[\"\';:\uFEFF]/g,C).replace(/\"[^\"]+\"|\'[^\']+\'/g,(function(e){return e.replace(/[;:]/g,C)}));while(r=c.exec(t))if(c.lastIndex=r.index+r[0].length,o=r[1].replace(s,"").toLowerCase(),d=r[2].replace(s,""),o&&d){if(o=S(o),d=S(d),-1!==o.indexOf(l)||-1!==o.indexOf('"'))continue;if(!e.allow_script_urls&&("behavior"===o||/expression\s*\(|\/\*|\*\//.test(d)))continue;"font-weight"===o&&"700"===d?d="bold":"color"!==o&&"background-color"!==o||(d=d.toLowerCase()),d=d.replace(a,ju),d=d.replace(u,k),g[o]=m?w(d,!0):d}v("border","",!0),v("border","-width"),v("border","-color"),v("border","-style"),v("padding",""),v("margin",""),y("border","border-width","border-style","border-color"),"medium none"===g.border&&delete g.border,"none"===g["border-image"]&&delete g["border-image"]}return g},serialize:function(e,t){var n="",i=function(t){var o,i=r[t];if(i)for(var a=0,u=i.length;a<u;a++)t=i[a],o=e[t],o&&(n+=(n.length>0?" ":"")+t+": "+o+";")},a=function(e,t){var n=o["*"];return(!n||!n[e])&&(n=o[t],!(n&&n[e]))};return t&&r?(i("*"),i(t)):xe(e,(function(e,r){!e||o&&!a(r,t)||(n+=(n.length>0?" ":"")+r+": "+e+";")})),n}}},qu={keyLocation:!0,layerX:!0,layerY:!0,returnValue:!0,webkitMovementX:!0,webkitMovementY:!0,keyIdentifier:!0,mozPressure:!0},$u=function(e){return e instanceof Event||N(e.initEvent)},Wu=function(e){return e.isDefaultPrevented===U||e.isDefaultPrevented===F},Ku=function(e){return S(e.preventDefault)||$u(e)},Xu=function(e,t){var n=null!==t&&void 0!==t?t:{};for(var r in e)Oe(qu,r)||(n[r]=e[r]);return k(n.composedPath)&&(n.composedPath=function(){return e.composedPath()}),n},Yu=function(e,t,n,r){var o,i=Xu(t,r);return i.type=e,S(i.target)&&(i.target=null!==(o=i.srcElement)&&void 0!==o?o:n),Ku(t)&&(i.preventDefault=function(){i.defaultPrevented=!0,i.isDefaultPrevented=U,N(t.preventDefault)?t.preventDefault():$u(t)&&(t.returnValue=!1)},i.stopPropagation=function(){i.cancelBubble=!0,i.isPropagationStopped=U,N(t.stopPropagation)?t.stopPropagation():$u(t)&&(t.cancelBubble=!0)},i.stopImmediatePropagation=function(){i.isImmediatePropagationStopped=U,i.stopPropagation()},Wu(i)||(i.isDefaultPrevented=!0===i.defaultPrevented?U:F,i.isPropagationStopped=!0===i.cancelBubble?U:F,i.isImmediatePropagationStopped=F)),i},Gu="mce-data-",Ju=/^(?:mouse|contextmenu)|click/,Qu=function(e,t,n,r){e.addEventListener?e.addEventListener(t,n,r||!1):e.attachEvent&&e.attachEvent("on"+t,n)},Zu=function(e,t,n,r){e.removeEventListener?e.removeEventListener(t,n,r||!1):e.detachEvent&&e.detachEvent("on"+t,n)},ec=function(e){return k(e)&&Ju.test(e.type)},tc=function(e,t){var n=Yu(e.type,e,document,t);if(ec(e)&&x(e.pageX)&&!x(e.clientX)){var r=n.target.ownerDocument||document,o=r.documentElement,i=r.body,a=n;a.pageX=e.clientX+(o&&o.scrollLeft||i&&i.scrollLeft||0)-(o&&o.clientLeft||i&&i.clientLeft||0),a.pageY=e.clientY+(o&&o.scrollTop||i&&i.scrollTop||0)-(o&&o.clientTop||i&&i.clientTop||0)}return x(n.metaKey)&&(n.metaKey=!1),n},nc=function(e,t,n){var r=e.document,o={type:"ready"};if(n.domLoaded)t(o);else{var i=function(){return"complete"===r.readyState||"interactive"===r.readyState&&r.body},a=function(){Zu(e,"DOMContentLoaded",a),Zu(e,"load",a),n.domLoaded||(n.domLoaded=!0,t(o)),e=null};i()?a():Qu(e,"DOMContentLoaded",a),n.domLoaded||Qu(e,"load",a)}},rc=function(){function e(){this.domLoaded=!1,this.events={},this.count=1,this.expando=Gu+(+new Date).toString(32),this.hasMouseEnterLeave="onmouseenter"in document.documentElement,this.hasFocusIn="onfocusin"in document.documentElement,this.count=1}return e.prototype.bind=function(e,t,n,r){var o,i,a,u,c,s,f,l=this,d=window,m=function(e){l.executeHandlers(tc(e||d.event),o)};if(e&&3!==e.nodeType&&8!==e.nodeType){e[l.expando]?o=e[l.expando]:(o=l.count++,e[l.expando]=o,l.events[o]={}),r=r||e;var g=t.split(" ");a=g.length;while(a--)u=g[a],s=m,c=f=!1,"DOMContentLoaded"===u&&(u="ready"),l.domLoaded&&"ready"===u&&"complete"===e.readyState?n.call(r,tc({type:u})):(l.hasMouseEnterLeave||(c=l.mouseEnterLeave[u],c&&(s=function(e){var t=e.currentTarget,n=e.relatedTarget;if(n&&t.contains)n=t.contains(n);else while(n&&n!==t)n=n.parentNode;n||(e=tc(e||d.event),e.type="mouseout"===e.type?"mouseleave":"mouseenter",e.target=t,l.executeHandlers(e,o))})),l.hasFocusIn||"focusin"!==u&&"focusout"!==u||(f=!0,c="focusin"===u?"focus":"blur",s=function(e){e=tc(e||d.event),e.type="focus"===e.type?"focusin":"focusout",l.executeHandlers(e,o)}),i=l.events[o][u],i?"ready"===u&&l.domLoaded?n(tc({type:u})):i.push({func:n,scope:r}):(l.events[o][u]=i=[{func:n,scope:r}],i.fakeName=c,i.capture=f,i.nativeHandler=s,"ready"===u?nc(e,s,l):Qu(e,c||u,s,f)));return e=i=null,n}},e.prototype.unbind=function(e,t,n){var r,o,i,a,u;if(!e||3===e.nodeType||8===e.nodeType)return this;var c=e[this.expando];if(c){if(u=this.events[c],t){var s=t.split(" ");o=s.length;while(o--)if(a=s[o],r=u[a],r){if(n){i=r.length;while(i--)if(r[i].func===n){var f=r.nativeHandler,l=r.fakeName,d=r.capture;r=r.slice(0,i).concat(r.slice(i+1)),r.nativeHandler=f,r.fakeName=l,r.capture=d,u[a]=r}}n&&0!==r.length||(delete u[a],Zu(e,r.fakeName||a,r.nativeHandler,r.capture))}}else xe(u,(function(t,n){Zu(e,t.fakeName||n,t.nativeHandler,t.capture)})),u={};for(a in u)if(Oe(u,a))return this;delete this.events[c];try{delete e[this.expando]}catch(m){e[this.expando]=null}}return this},e.prototype.fire=function(e,t,n){var r;if(!e||3===e.nodeType||8===e.nodeType)return this;var o=tc({type:t,target:e},n);do{r=e[this.expando],r&&this.executeHandlers(o,r),e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow}while(e&&!o.isPropagationStopped());return this},e.prototype.clean=function(e){var t,n;if(!e||3===e.nodeType||8===e.nodeType)return this;if(e[this.expando]&&this.unbind(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName){this.unbind(e),n=e.getElementsByTagName("*"),t=n.length;while(t--)e=n[t],e[this.expando]&&this.unbind(e)}return this},e.prototype.destroy=function(){this.events={}},e.prototype.cancel=function(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1},e.prototype.executeHandlers=function(e,t){var n=this.events[t],r=n&&n[e.type];if(r)for(var o=0,i=r.length;o<i;o++){var a=r[o];if(a&&!1===a.func.call(a.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return}},e.Event=new e,e}(),oc="sizzle"+-new Date,ic=window.document,ac=0,uc=0,cc=jc(),sc=jc(),fc=jc(),lc=function(e,t){return e===t&&(mi=!0),0},dc="undefined",mc=1<<31,gc={}.hasOwnProperty,pc=[],hc=pc.pop,vc=pc.push,bc=pc.push,yc=pc.slice,Cc=pc.indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},wc="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",xc="[\\x20\\t\\r\\n\\f]",Sc="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",kc="\\["+xc+"*("+Sc+")(?:"+xc+"*([*^$|!~]?=)"+xc+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+Sc+"))|)"+xc+"*\\]",Nc=":("+Sc+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+kc+")*)|.*)\\)|)",Ec=new RegExp("^"+xc+"+|((?:^|[^\\\\])(?:\\\\.)*)"+xc+"+$","g"),_c=new RegExp("^"+xc+"*,"+xc+"*"),Ac=new RegExp("^"+xc+"*([>+~]|"+xc+")"+xc+"*"),Rc=new RegExp("="+xc+"*([^\\]'\"]*?)"+xc+"*\\]","g"),Dc=new RegExp(Nc),Tc=new RegExp("^"+Sc+"$"),Oc={ID:new RegExp("^#("+Sc+")"),CLASS:new RegExp("^\\.("+Sc+")"),TAG:new RegExp("^("+Sc+"|[*])"),ATTR:new RegExp("^"+kc),PSEUDO:new RegExp("^"+Nc),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+xc+"*(even|odd|(([+-]|)(\\d*)n|)"+xc+"*(?:([+-]|)"+xc+"*(\\d+)|))"+xc+"*\\)|)","i"),bool:new RegExp("^(?:"+wc+")$","i"),needsContext:new RegExp("^"+xc+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+xc+"*((?:-\\d)?\\d*)"+xc+"*\\)|)(?=[^-]|$)","i")},Bc=/^(?:input|select|textarea|button)$/i,Pc=/^h\d$/i,Lc=/^[^{]+\{\s*\[native \w/,Ic=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Mc=/[+~]/,Fc=/'|\\/g,Uc=new RegExp("\\\\([\\da-f]{1,6}"+xc+"?|("+xc+")|.)","ig"),zc=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)};try{bc.apply(pc=yc.call(ic.childNodes),ic.childNodes),pc[ic.childNodes.length].nodeType}catch(DH){bc={apply:pc.length?function(e,t){vc.apply(e,yc.call(t))}:function(e,t){var n=e.length,r=0;while(e[n++]=t[r++]);e.length=n-1}}}var Hc=function(e,t,n,r){var o,i,a,u,c,s,f,l,d,m;if((t?t.ownerDocument||t:ic)!==pi&&gi(t),t=t||pi,n=n||[],!e||"string"!==typeof e)return n;if(1!==(u=t.nodeType)&&9!==u)return[];if(vi&&!r){if(o=Ic.exec(e))if(a=o[1]){if(9===u){if(i=t.getElementById(a),!i||!i.parentNode)return n;if(i.id===a)return n.push(i),n}else if(t.ownerDocument&&(i=t.ownerDocument.getElementById(a))&&wi(t,i)&&i.id===a)return n.push(i),n}else{if(o[2])return bc.apply(n,t.getElementsByTagName(e)),n;if((a=o[3])&&oi.getElementsByClassName)return bc.apply(n,t.getElementsByClassName(a)),n}if(oi.qsa&&(!bi||!bi.test(e))){if(l=f=oc,d=t,m=9===u&&e,1===u&&"object"!==t.nodeName.toLowerCase()){s=ci(e),(f=t.getAttribute("id"))?l=f.replace(Fc,"\\$&"):t.setAttribute("id",l),l="[id='"+l+"'] ",c=s.length;while(c--)s[c]=l+Gc(s[c]);d=Mc.test(e)&&Xc(t.parentNode)||t,m=s.join(",")}if(m)try{return bc.apply(n,d.querySelectorAll(m)),n}catch(g){}finally{f||t.removeAttribute("id")}}}return fi(e.replace(Ec,"$1"),t,n,r)};function jc(){var e=[];function t(n,r){return e.push(n+" ")>ii.cacheLength&&delete t[e.shift()],t[n+" "]=r}return t}function Vc(e){return e[oc]=!0,e}function qc(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||mc)-(~e.sourceIndex||mc);if(r)return r;if(n)while(n=n.nextSibling)if(n===t)return-1;return e?1:-1}function $c(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function Wc(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function Kc(e){return Vc((function(t){return t=+t,Vc((function(n,r){var o,i=e([],n.length,t),a=i.length;while(a--)n[o=i[a]]&&(n[o]=!(r[o]=n[o]))}))}))}function Xc(e){return e&&typeof e.getElementsByTagName!==dc&&e}function Yc(){}function Gc(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function Jc(e,t,n){var r=t.dir,o=n&&"parentNode"===r,i=uc++;return t.first?function(t,n,i){while(t=t[r])if(1===t.nodeType||o)return e(t,n,i)}:function(t,n,a){var u,c,s=[ac,i];if(a){while(t=t[r])if((1===t.nodeType||o)&&e(t,n,a))return!0}else while(t=t[r])if(1===t.nodeType||o){if(c=t[oc]||(t[oc]={}),(u=c[r])&&u[0]===ac&&u[1]===i)return s[2]=u[2];if(c[r]=s,s[2]=e(t,n,a))return!0}}}function Qc(e){return e.length>1?function(t,n,r){var o=e.length;while(o--)if(!e[o](t,n,r))return!1;return!0}:e[0]}function Zc(e,t,n){for(var r=0,o=t.length;r<o;r++)Hc(e,t[r],n);return n}function es(e,t,n,r,o){for(var i,a=[],u=0,c=e.length,s=null!=t;u<c;u++)(i=e[u])&&(n&&!n(i,r,o)||(a.push(i),s&&t.push(u)));return a}function ts(e,t,n,r,o,i){return r&&!r[oc]&&(r=ts(r)),o&&!o[oc]&&(o=ts(o,i)),Vc((function(i,a,u,c){var s,f,l,d=[],m=[],g=a.length,p=i||Zc(t||"*",u.nodeType?[u]:u,[]),h=!e||!i&&t?p:es(p,d,e,u,c),v=n?o||(i?e:g||r)?[]:a:h;if(n&&n(h,v,u,c),r){s=es(v,m),r(s,[],u,c),f=s.length;while(f--)(l=s[f])&&(v[m[f]]=!(h[m[f]]=l))}if(i){if(o||e){if(o){s=[],f=v.length;while(f--)(l=v[f])&&s.push(h[f]=l);o(null,v=[],s,c)}f=v.length;while(f--)(l=v[f])&&(s=o?Cc.call(i,l):d[f])>-1&&(i[s]=!(a[s]=l))}}else v=es(v===a?v.splice(g,v.length):v),o?o(null,a,v,c):bc.apply(a,v)}))}function ns(e){for(var t,n,r,o=e.length,i=ii.relative[e[0].type],a=i||ii.relative[" "],u=i?1:0,c=Jc((function(e){return e===t}),a,!0),s=Jc((function(e){return Cc.call(t,e)>-1}),a,!0),f=[function(e,n,r){var o=!i&&(r||n!==li)||((t=n).nodeType?c(e,n,r):s(e,n,r));return t=null,o}];u<o;u++)if(n=ii.relative[e[u].type])f=[Jc(Qc(f),n)];else{if(n=ii.filter[e[u].type].apply(null,e[u].matches),n[oc]){for(r=++u;r<o;r++)if(ii.relative[e[r].type])break;return ts(u>1&&Qc(f),u>1&&Gc(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(Ec,"$1"),n,u<r&&ns(e.slice(u,r)),r<o&&ns(e=e.slice(r)),r<o&&Gc(e))}f.push(n)}return Qc(f)}function rs(e,t){var n=t.length>0,r=e.length>0,o=function(o,i,a,u,c){var s,f,l,d=0,m="0",g=o&&[],p=[],h=li,v=o||r&&ii.find.TAG("*",c),b=ac+=null==h?1:Math.random()||.1,y=v.length;for(c&&(li=i!==pi&&i);m!==y&&null!=(s=v[m]);m++){if(r&&s){f=0;while(l=e[f++])if(l(s,i,a)){u.push(s);break}c&&(ac=b)}n&&((s=!l&&s)&&d--,o&&g.push(s))}if(d+=m,n&&m!==d){f=0;while(l=t[f++])l(g,p,i,a);if(o){if(d>0)while(m--)g[m]||p[m]||(p[m]=hc.call(u));p=es(p)}bc.apply(u,p),c&&!o&&p.length>0&&d+t.length>1&&Hc.uniqueSort(u)}return c&&(ac=b,li=h),g};return n?Vc(o):o}oi=Hc.support={},ui=Hc.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},gi=Hc.setDocument=function(e){var t,n=e?e.ownerDocument||e:ic,r=n.defaultView;function o(e){try{return e.top}catch(t){}return null}return n!==pi&&9===n.nodeType&&n.documentElement?(pi=n,hi=n.documentElement,vi=!ui(n),r&&r!==o(r)&&(r.addEventListener?r.addEventListener("unload",(function(){gi()}),!1):r.attachEvent&&r.attachEvent("onunload",(function(){gi()}))),oi.attributes=!0,oi.getElementsByTagName=!0,oi.getElementsByClassName=Lc.test(n.getElementsByClassName),oi.getById=!0,ii.find.ID=function(e,t){if(typeof t.getElementById!==dc&&vi){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},ii.filter.ID=function(e){var t=e.replace(Uc,zc);return function(e){return e.getAttribute("id")===t}},ii.find.TAG=oi.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!==dc)return t.getElementsByTagName(e)}:function(e,t){var n,r=[],o=0,i=t.getElementsByTagName(e);if("*"===e){while(n=i[o++])1===n.nodeType&&r.push(n);return r}return i},ii.find.CLASS=oi.getElementsByClassName&&function(e,t){if(vi)return t.getElementsByClassName(e)},yi=[],bi=[],oi.disconnectedMatch=!0,bi=bi.length&&new RegExp(bi.join("|")),yi=yi.length&&new RegExp(yi.join("|")),t=Lc.test(hi.compareDocumentPosition),wi=t||Lc.test(hi.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)while(t=t.parentNode)if(t===e)return!0;return!1},lc=t?function(e,t){if(e===t)return mi=!0,0;var r=!e.compareDocumentPosition-!t.compareDocumentPosition;return r||(r=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&r||!oi.sortDetached&&t.compareDocumentPosition(e)===r?e===n||e.ownerDocument===ic&&wi(ic,e)?-1:t===n||t.ownerDocument===ic&&wi(ic,t)?1:di?Cc.call(di,e)-Cc.call(di,t):0:4&r?-1:1)}:function(e,t){if(e===t)return mi=!0,0;var r,o=0,i=e.parentNode,a=t.parentNode,u=[e],c=[t];if(!i||!a)return e===n?-1:t===n?1:i?-1:a?1:di?Cc.call(di,e)-Cc.call(di,t):0;if(i===a)return qc(e,t);r=e;while(r=r.parentNode)u.unshift(r);r=t;while(r=r.parentNode)c.unshift(r);while(u[o]===c[o])o++;return o?qc(u[o],c[o]):u[o]===ic?-1:c[o]===ic?1:0},n):pi},Hc.matches=function(e,t){return Hc(e,null,null,t)},Hc.matchesSelector=function(e,t){if((e.ownerDocument||e)!==pi&&gi(e),t=t.replace(Rc,"='$1']"),oi.matchesSelector&&vi&&(!yi||!yi.test(t))&&(!bi||!bi.test(t)))try{var n=Ci.call(e,t);if(n||oi.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(DH){}return Hc(t,pi,null,[e]).length>0},Hc.contains=function(e,t){return(e.ownerDocument||e)!==pi&&gi(e),wi(e,t)},Hc.attr=function(e,t){(e.ownerDocument||e)!==pi&&gi(e);var n=ii.attrHandle[t.toLowerCase()],r=n&&gc.call(ii.attrHandle,t.toLowerCase())?n(e,t,!vi):void 0;return void 0!==r?r:oi.attributes||!vi?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},Hc.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},Hc.uniqueSort=function(e){var t,n=[],r=0,o=0;if(mi=!oi.detectDuplicates,di=!oi.sortStable&&e.slice(0),e.sort(lc),mi){while(t=e[o++])t===e[o]&&(r=n.push(o));while(r--)e.splice(n[r],1)}return di=null,e},ai=Hc.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"===typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=ai(e)}else if(3===o||4===o)return e.nodeValue}else while(t=e[r++])n+=ai(t);return n},ii=Hc.selectors={cacheLength:50,createPseudo:Vc,match:Oc,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Uc,zc),e[3]=(e[3]||e[4]||e[5]||"").replace(Uc,zc),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||Hc.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&Hc.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Oc.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&Dc.test(n)&&(t=ci(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Uc,zc).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=cc[e+" "];return t||(t=new RegExp("(^|"+xc+")"+e+"("+xc+"|$)"))&&cc(e,(function(e){return t.test("string"===typeof e.className&&e.className||typeof e.getAttribute!==dc&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var o=Hc.attr(r,e);return null==o?"!="===t:!t||(o+="","="===t?o===n:"!="===t?o!==n:"^="===t?n&&0===o.indexOf(n):"*="===t?n&&o.indexOf(n)>-1:"$="===t?n&&o.slice(-n.length)===n:"~="===t?(" "+o+" ").indexOf(n)>-1:"|="===t&&(o===n||o.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,o){var i="nth"!==e.slice(0,3),a="last"!==e.slice(-4),u="of-type"===t;return 1===r&&0===o?function(e){return!!e.parentNode}:function(t,n,c){var s,f,l,d,m,g,p=i!==a?"nextSibling":"previousSibling",h=t.parentNode,v=u&&t.nodeName.toLowerCase(),b=!c&&!u;if(h){if(i){while(p){l=t;while(l=l[p])if(u?l.nodeName.toLowerCase()===v:1===l.nodeType)return!1;g=p="only"===e&&!g&&"nextSibling"}return!0}if(g=[a?h.firstChild:h.lastChild],a&&b){f=h[oc]||(h[oc]={}),s=f[e]||[],m=s[0]===ac&&s[1],d=s[0]===ac&&s[2],l=m&&h.childNodes[m];while(l=++m&&l&&l[p]||(d=m=0)||g.pop())if(1===l.nodeType&&++d&&l===t){f[e]=[ac,m,d];break}}else if(b&&(s=(t[oc]||(t[oc]={}))[e])&&s[0]===ac)d=s[1];else while(l=++m&&l&&l[p]||(d=m=0)||g.pop())if((u?l.nodeName.toLowerCase()===v:1===l.nodeType)&&++d&&(b&&((l[oc]||(l[oc]={}))[e]=[ac,d]),l===t))break;return d-=o,d===r||d%r===0&&d/r>=0}}},PSEUDO:function(e,t){var n,r=ii.pseudos[e]||ii.setFilters[e.toLowerCase()]||Hc.error("unsupported pseudo: "+e);return r[oc]?r(t):r.length>1?(n=[e,e,"",t],ii.setFilters.hasOwnProperty(e.toLowerCase())?Vc((function(e,n){var o,i=r(e,t),a=i.length;while(a--)o=Cc.call(e,i[a]),e[o]=!(n[o]=i[a])})):function(e){return r(e,0,n)}):r}},pseudos:{not:Vc((function(e){var t=[],n=[],r=si(e.replace(Ec,"$1"));return r[oc]?Vc((function(e,t,n,o){var i,a=r(e,null,o,[]),u=e.length;while(u--)(i=a[u])&&(e[u]=!(t[u]=i))})):function(e,o,i){return t[0]=e,r(t,null,i,n),t[0]=null,!n.pop()}})),has:Vc((function(e){return function(t){return Hc(e,t).length>0}})),contains:Vc((function(e){return e=e.replace(Uc,zc),function(t){return(t.textContent||t.innerText||ai(t)).indexOf(e)>-1}})),lang:Vc((function(e){return Tc.test(e||"")||Hc.error("unsupported lang: "+e),e=e.replace(Uc,zc).toLowerCase(),function(t){var n;do{if(n=vi?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(e){var t=window.location&&window.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===hi},focus:function(e){return e===pi.activeElement&&(!pi.hasFocus||pi.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!ii.pseudos.empty(e)},header:function(e){return Pc.test(e.nodeName)},input:function(e){return Bc.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:Kc((function(){return[0]})),last:Kc((function(e,t){return[t-1]})),eq:Kc((function(e,t,n){return[n<0?n+t:n]})),even:Kc((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:Kc((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:Kc((function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e})),gt:Kc((function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e}))}},ii.pseudos.nth=ii.pseudos.eq,Z(["radio","checkbox","file","password","image"],(function(e){ii.pseudos[e]=$c(e)})),Z(["submit","reset"],(function(e){ii.pseudos[e]=Wc(e)})),Yc.prototype=ii.filters=ii.pseudos,ii.setFilters=new Yc,ci=Hc.tokenize=function(e,t){var n,r,o,i,a,u,c,s=sc[e+" "];if(s)return t?0:s.slice(0);a=e,u=[],c=ii.preFilter;while(a){for(i in n&&!(r=_c.exec(a))||(r&&(a=a.slice(r[0].length)||a),u.push(o=[])),n=!1,(r=Ac.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace(Ec," ")}),a=a.slice(n.length)),ii.filter)ii.filter.hasOwnProperty(i)&&(!(r=Oc[i].exec(a))||c[i]&&!(r=c[i](r))||(n=r.shift(),o.push({value:n,type:i,matches:r}),a=a.slice(n.length)));if(!n)break}return t?a.length:a?Hc.error(e):sc(e,u).slice(0)},si=Hc.compile=function(e,t){var n,r=[],o=[],i=fc[e+" "];if(!i){t||(t=ci(e)),n=t.length;while(n--)i=ns(t[n]),i[oc]?r.push(i):o.push(i);i=fc(e,rs(o,r)),i.selector=e}return i},fi=Hc.select=function(e,t,n,r){var o,i,a,u,c,s="function"===typeof e&&e,f=!r&&ci(e=s.selector||e);if(n=n||[],1===f.length){if(i=f[0]=f[0].slice(0),i.length>2&&"ID"===(a=i[0]).type&&oi.getById&&9===t.nodeType&&vi&&ii.relative[i[1].type]){if(t=(ii.find.ID(a.matches[0].replace(Uc,zc),t)||[])[0],!t)return n;s&&(t=t.parentNode),e=e.slice(i.shift().value.length)}o=Oc.needsContext.test(e)?0:i.length;while(o--){if(a=i[o],ii.relative[u=a.type])break;if((c=ii.find[u])&&(r=c(a.matches[0].replace(Uc,zc),Mc.test(i[0].type)&&Xc(t.parentNode)||t))){if(i.splice(o,1),e=r.length&&Gc(i),!e)return bc.apply(n,r),n;break}}}return(s||si(e,f))(r,t,!vi,n,Mc.test(e)&&Xc(t.parentNode)||t),n},oi.sortStable=oc.split("").sort(lc).join("")===oc,oi.detectDuplicates=!!mi,gi(),oi.sortDetached=!0;var os=document,is=Array.prototype.push,as=Array.prototype.slice,us=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,cs=rc.Event,ss=gn.makeMap("children,contents,next,prev"),fs=function(e){return"undefined"!==typeof e},ls=function(e){return"string"===typeof e},ds=function(e){return e&&e===e.window},ms=function(e,t){t=t||os;var n,r=t.createElement("div"),o=t.createDocumentFragment();r.innerHTML=e;while(n=r.firstChild)o.appendChild(n);return o},gs=function(e,t,n,r){var o;if(ls(t))t=ms(t,Rs(e[0]));else if(t.length&&!t.nodeType){if(t=Bs.makeArray(t),r)for(o=t.length-1;o>=0;o--)gs(e,t[o],n,r);else for(o=0;o<t.length;o++)gs(e,t[o],n,r);return e}if(t.nodeType){o=e.length;while(o--)n.call(e[o],t)}return e},ps=function(e,t){return e&&t&&-1!==(" "+e.className+" ").indexOf(" "+t+" ")},hs=function(e,t,n){var r,o;return t=Bs(t)[0],e.each((function(){var e=this;n&&r===e.parentNode||(r=e.parentNode,o=t.cloneNode(!1),e.parentNode.insertBefore(o,e)),o.appendChild(e)})),e},vs=gn.makeMap("fillOpacity fontWeight lineHeight opacity orphans widows zIndex zoom"," "),bs=gn.makeMap("checked compact declare defer disabled ismap multiple nohref noshade nowrap readonly selected"," "),ys={for:"htmlFor",class:"className",readonly:"readOnly"},Cs={float:"cssFloat"},ws={},xs={},Ss=function(e,t){return new Bs.fn.init(e,t)},ks=function(e,t){var n;if(t.indexOf)return t.indexOf(e);n=t.length;while(n--)if(t[n]===e)return n;return-1},Ns=/^\s*|\s*$/g,Es=function(e){return null===e||void 0===e?"":(""+e).replace(Ns,"")},_s=function(e,t){var n,r,o,i;if(e)if(n=e.length,void 0===n){for(r in e)if(e.hasOwnProperty(r)&&(i=e[r],!1===t.call(i,r,i)))break}else for(o=0;o<n;o++)if(i=e[o],!1===t.call(i,o,i))break;return e},As=function(e,t){var n=[];return _s(e,(function(e,r){t(r,e)&&n.push(r)})),n},Rs=function(e){return e?9===e.nodeType?e:e.ownerDocument:os};Ss.fn=Ss.prototype={constructor:Ss,selector:"",context:null,length:0,init:function(e,t){var n,r,o=this;if(!e)return o;if(e.nodeType)return o.context=o[0]=e,o.length=1,o;if(t&&t.nodeType)o.context=t;else{if(t)return Bs(e).attr(t);o.context=t=document}if(ls(e)){if(o.selector=e,n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:us.exec(e),!n)return Bs(t).find(e);if(n[1]){r=ms(e,Rs(t)).firstChild;while(r)is.call(o,r),r=r.nextSibling}else{if(r=Rs(t).getElementById(n[2]),!r)return o;if(r.id!==n[2])return o.find(e);o.length=1,o[0]=r}}else this.add(e,!1);return o},toArray:function(){return gn.toArray(this)},add:function(e,t){var n,r,o=this;if(ls(e))return o.add(Bs(e));if(!1!==t)for(n=Bs.unique(o.toArray().concat(Bs.makeArray(e))),o.length=n.length,r=0;r<n.length;r++)o[r]=n[r];else is.apply(o,Bs.makeArray(e));return o},attr:function(e,t){var n,r=this;if("object"===typeof e)_s(e,(function(e,t){r.attr(e,t)}));else{if(!fs(t)){if(r[0]&&1===r[0].nodeType){if(n=ws[e],n&&n.get)return n.get(r[0],e);if(bs[e])return r.prop(e)?e:void 0;t=r[0].getAttribute(e,2),null===t&&(t=void 0)}return t}this.each((function(){var n;if(1===this.nodeType){if(n=ws[e],n&&n.set)return void n.set(this,t);null===t?this.removeAttribute(e,2):this.setAttribute(e,t,2)}}))}return r},removeAttr:function(e){return this.attr(e,null)},prop:function(e,t){var n=this;if(e=ys[e]||e,"object"===typeof e)_s(e,(function(e,t){n.prop(e,t)}));else{if(!fs(t))return n[0]&&n[0].nodeType&&e in n[0]?n[0][e]:t;this.each((function(){1===this.nodeType&&(this[e]=t)}))}return n},css:function(e,t){var n,r,o=this,i=function(e){return e.replace(/-(\D)/g,(function(e,t){return t.toUpperCase()}))},a=function(e){return e.replace(/[A-Z]/g,(function(e){return"-"+e}))};if("object"===typeof e)_s(e,(function(e,t){o.css(e,t)}));else if(fs(t))e=i(e),"number"!==typeof t||vs[e]||(t=t.toString()+"px"),o.each((function(){var n=this.style;if(r=xs[e],r&&r.set)r.set(this,t);else{try{this.style[Cs[e]||e]=t}catch(o){}null!==t&&""!==t||(n.removeProperty?n.removeProperty(a(e)):n.removeAttribute(e))}}));else{if(n=o[0],r=xs[e],r&&r.get)return r.get(n);if(!n.ownerDocument.defaultView)return n.currentStyle?n.currentStyle[i(e)]:"";try{return n.ownerDocument.defaultView.getComputedStyle(n,null).getPropertyValue(a(e))}catch(u){return}}return o},remove:function(){var e,t=this,n=this.length;while(n--)e=t[n],cs.clean(e),e.parentNode&&e.parentNode.removeChild(e);return this},empty:function(){var e,t=this,n=this.length;while(n--){e=t[n];while(e.firstChild)e.removeChild(e.firstChild)}return this},html:function(e){var t,n=this;if(fs(e)){t=n.length;try{while(t--)n[t].innerHTML=e}catch(r){Bs(n[t]).empty().append(e)}return n}return n[0]?n[0].innerHTML:""},text:function(e){var t,n=this;if(fs(e)){t=n.length;while(t--)"innerText"in n[t]?n[t].innerText=e:n[0].textContent=e;return n}return n[0]?n[0].innerText||n[0].textContent:""},append:function(){return gs(this,arguments,(function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.appendChild(e)}))},prepend:function(){return gs(this,arguments,(function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.insertBefore(e,this.firstChild)}),!0)},before:function(){var e=this;return e[0]&&e[0].parentNode?gs(e,arguments,(function(e){this.parentNode.insertBefore(e,this)})):e},after:function(){var e=this;return e[0]&&e[0].parentNode?gs(e,arguments,(function(e){this.parentNode.insertBefore(e,this.nextSibling)}),!0):e},appendTo:function(e){return Bs(e).append(this),this},prependTo:function(e){return Bs(e).prepend(this),this},replaceWith:function(e){return this.before(e).remove()},wrap:function(e){return hs(this,e)},wrapAll:function(e){return hs(this,e,!0)},wrapInner:function(e){return this.each((function(){Bs(this).contents().wrapAll(e)})),this},unwrap:function(){return this.parent().each((function(){Bs(this).replaceWith(this.childNodes)}))},clone:function(){var e=[];return this.each((function(){e.push(this.cloneNode(!0))})),Bs(e)},addClass:function(e){return this.toggleClass(e,!0)},removeClass:function(e){return this.toggleClass(e,!1)},toggleClass:function(e,t){var n=this;return"string"!==typeof e||(-1!==e.indexOf(" ")?_s(e.split(" "),(function(){n.toggleClass(this,t)})):n.each((function(n,r){var o=ps(r,e);if(o!==t){var i=r.className;o?r.className=Es((" "+i+" ").replace(" "+e+" "," ")):r.className+=i?" "+e:e}}))),n},hasClass:function(e){return ps(this[0],e)},each:function(e){return _s(this,e)},on:function(e,t){return this.each((function(){cs.bind(this,e,t)}))},off:function(e,t){return this.each((function(){cs.unbind(this,e,t)}))},trigger:function(e){return this.each((function(){"object"===typeof e?cs.fire(this,e.type,e):cs.fire(this,e)}))},show:function(){return this.css("display","")},hide:function(){return this.css("display","none")},slice:function(){return Bs(as.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},find:function(e){var t,n,r=[];for(t=0,n=this.length;t<n;t++)Bs.find(e,this[t],r);return Bs(r)},filter:function(e){return Bs("function"===typeof e?As(this.toArray(),(function(t,n){return e(n,t)})):Bs.filter(e,this.toArray()))},closest:function(e){var t=[];return e instanceof Bs&&(e=e[0]),this.each((function(n,r){while(r){if("string"===typeof e&&Bs(r).is(e)){t.push(r);break}if(r===e){t.push(r);break}r=r.parentNode}})),Bs(t)},offset:function(e){var t,n,r,o,i=0,a=0;return e?this.css(e):(t=this[0],t&&(n=t.ownerDocument,r=n.documentElement,t.getBoundingClientRect&&(o=t.getBoundingClientRect(),i=o.left+(r.scrollLeft||n.body.scrollLeft)-r.clientLeft,a=o.top+(r.scrollTop||n.body.scrollTop)-r.clientTop)),{left:i,top:a})},push:is,sort:Array.prototype.sort,splice:Array.prototype.splice},gn.extend(Ss,{extend:gn.extend,makeArray:function(e){return ds(e)||e.nodeType?[e]:gn.toArray(e)},inArray:ks,isArray:gn.isArray,each:_s,trim:Es,grep:As,find:Hc,expr:Hc.selectors,unique:Hc.uniqueSort,text:Hc.getText,contains:Hc.contains,filter:function(e,t,n){var r=t.length;n&&(e=":not("+e+")");while(r--)1!==t[r].nodeType&&t.splice(r,1);return t=1===t.length?Bs.find.matchesSelector(t[0],e)?[t[0]]:[]:Bs.find.matches(e,t),t}});var Ds=function(e,t,n){var r=[],o=e[t];"string"!==typeof n&&n instanceof Bs&&(n=n[0]);while(o&&9!==o.nodeType){if(void 0!==n){if(o===n)break;if("string"===typeof n&&Bs(o).is(n))break}1===o.nodeType&&r.push(o),o=o[t]}return r},Ts=function(e,t,n,r){var o=[];for(r instanceof Bs&&(r=r[0]);e;e=e[t])if(!n||e.nodeType===n){if(void 0!==r){if(e===r)break;if("string"===typeof r&&Bs(e).is(r))break}o.push(e)}return o},Os=function(e,t,n){for(e=e[t];e;e=e[t])if(e.nodeType===n)return e;return null};_s({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return Ds(e,"parentNode")},next:function(e){return Os(e,"nextSibling",1)},prev:function(e){return Os(e,"previousSibling",1)},children:function(e){return Ts(e.firstChild,"nextSibling",1)},contents:function(e){return gn.toArray(("iframe"===e.nodeName?e.contentDocument||e.contentWindow.document:e).childNodes)}},(function(e,t){Ss.fn[e]=function(n){var r=this,o=[];r.each((function(){var e=t.call(o,this,n,o);e&&(Bs.isArray(e)?o.push.apply(o,e):o.push(e))})),this.length>1&&(ss[e]||(o=Bs.unique(o)),0===e.indexOf("parents")&&(o=o.reverse()));var i=Bs(o);return n?i.filter(n):i}})),_s({parentsUntil:function(e,t){return Ds(e,"parentNode",t)},nextUntil:function(e,t){return Ts(e,"nextSibling",1,t).slice(1)},prevUntil:function(e,t){return Ts(e,"previousSibling",1,t).slice(1)}},(function(e,t){Ss.fn[e]=function(n,r){var o=this,i=[];o.each((function(){var e=t.call(i,this,n,i);e&&(Bs.isArray(e)?i.push.apply(i,e):i.push(e))})),this.length>1&&(i=Bs.unique(i),0!==e.indexOf("parents")&&"prevUntil"!==e||(i=i.reverse()));var a=Bs(i);return r?a.filter(r):a}})),Ss.fn.is=function(e){return!!e&&this.filter(e).length>0},Ss.fn.init.prototype=Ss.fn,Ss.overrideDefaults=function(e){var t,n=function(r,o){return t=t||e(),0===arguments.length&&(r=t.element),o||(o=t.context),new n.fn.init(r,o)};return Bs.extend(n,this),n},Ss.attrHooks=ws,Ss.cssHooks=xs;var Bs=Ss,Ps=gn.each,Ls=gn.grep,Is=en.ie,Ms=/^([a-z0-9],?)+$/i,Fs=function(e,t,n){var r=t.keep_values,o={set:function(e,r,o){t.url_converter&&null!==r&&(r=t.url_converter.call(t.url_converter_scope||n(),r,o,e[0])),e.attr("data-mce-"+o,r).attr(o,r)},get:function(e,t){return e.attr("data-mce-"+t)||e.attr(t)}},i={style:{set:function(t,n){null===n||"object"!==typeof n?(r&&t.attr("data-mce-style",n),null!==n&&"string"===typeof n?(t.removeAttr("style"),t.css(e.parse(n))):t.attr("style",n)):t.css(n)},get:function(t){var n=t.attr("data-mce-style")||t.attr("style");return n=e.serialize(e.parse(n),t[0].nodeName),n}}};return r&&(i.href=i.src=o),i},Us=function(e,t){var n=t.attr("style"),r=e.serialize(e.parse(n),t[0].nodeName);r||(r=null),t.attr("data-mce-style",r)},zs=function(e,t){var n,r,o=0;if(e)for(n=e.nodeType,e=e.previousSibling;e;e=e.previousSibling)r=e.nodeType,(!t||3!==r||r!==n&&e.nodeValue.length)&&(o++,n=r);return o},Hs=function(e,t){void 0===t&&(t={});var n={},r=window,o={},i=0,a=!0,u=!0,c=Ui.forElement(Cn.fromDom(e),{contentCssCors:t.contentCssCors,referrerPolicy:t.referrerPolicy}),s=[],f=t.schema?t.schema:Hu({}),l=Vu({url_converter:t.url_converter,url_converter_scope:t.url_converter_scope},t.schema),d=t.ownEvents?new rc:rc.Event,m=f.getBlockElements(),g=Bs.overrideDefaults((function(){return{context:e,element:Te.getRoot()}})),p=function(e){if("string"===typeof e)return!!m[e];if(e){var t=e.nodeType;if(t)return!(1!==t||!m[e.nodeName])}return!1},h=function(t){return t&&e&&v(t)?e.getElementById(t):t},b=function(e){return g("string"===typeof e?h(e):e)},y=function(e,t,n){var r,o,i=b(e);return i.length&&(r=Oe[t],o=r&&r.get?r.get(i,t):i.attr(t)),"undefined"===typeof o&&(o=n||""),o},C=function(e){var t=h(e);return t?t.attributes:[]},w=function(e,n,r){""===r&&(r=null);var o=b(e),i=o.attr(n);if(o.length){var a=Oe[n];a&&a.set?a.set(o,r,n):o.attr(n,r),i!==r&&t.onSetAttrib&&t.onSetAttrib({attrElm:o,attrName:n,attrValue:r})}},x=function(t,n){if(!Is||1!==t.nodeType||n)return t.cloneNode(n);var r=e.createElement(t.nodeName);return Ps(C(t),(function(e){w(r,e.nodeName,y(t,e.nodeName))})),r},k=function(){return t.root_element||e.body},N=function(e){var t=zr(e);return{x:t.x,y:t.y,w:t.width,h:t.height}},E=function(t,n){return Lo(e.body,h(t),n)},A=function(e,n,r){var o=v(n)?b(e).css(n,r):b(e).css(n);t.update_styles&&Us(l,o)},R=function(e,n){var r=b(e).css(n);t.update_styles&&Us(l,r)},T=function(e,t,n){var r=b(e);return n?r.css(t):(t=t.replace(/-(\D)/g,(function(e,t){return t.toUpperCase()})),"float"===t&&(t=en.browser.isIE()?"styleFloat":"cssFloat"),r[0]&&r[0].style?r[0].style[t]:void 0)},O=function(e){var t,n;return e=h(e),t=T(e,"width"),n=T(e,"height"),-1===t.indexOf("px")&&(t=0),-1===n.indexOf("px")&&(n=0),{w:parseInt(t,10)||e.offsetWidth||e.clientWidth,h:parseInt(n,10)||e.offsetHeight||e.clientHeight}},B=function(e){e=h(e);var t=E(e),n=O(e);return{x:t.x,y:t.y,w:n.w,h:n.h}},P=function(e,t){var n;if(!e)return!1;if(!Array.isArray(e)){if("*"===t)return 1===e.nodeType;if(Ms.test(t)){var r=t.toLowerCase().split(/,/),o=e.nodeName.toLowerCase();for(n=r.length-1;n>=0;n--)if(r[n]===o)return!0;return!1}if(e.nodeType&&1!==e.nodeType)return!1}var i=Array.isArray(e)?e:[e];return Hc(t,i[0].ownerDocument||i[0],null,i).length>0},L=function(e,t,n,r){var o,i=[],a=h(e);r=void 0===r,n=n||("BODY"!==k().nodeName?k().parentNode:null),gn.is(t,"string")&&(o=t,t="*"===t?function(e){return 1===e.nodeType}:function(e){return P(e,o)});while(a){if(a===n||S(a.nodeType)||to(a)||no(a))break;if(!t||"function"===typeof t&&t(a)){if(!r)return[a];i.push(a)}a=a.parentNode}return r?i:null},I=function(e,t,n){var r=L(e,t,n,!1);return r&&r.length>0?r[0]:null},M=function(e,t,n){var r=t;if(e)for("string"===typeof t&&(r=function(e){return P(e,t)}),e=e[n];e;e=e[n])if("function"===typeof r&&r(e))return e;return null},F=function(e,t){return M(e,t,"nextSibling")},U=function(e,t){return M(e,t,"previousSibling")},z=function(n,r){return Hc(n,h(r)||t.root_element||e,[])},H=function(e,t,n){var r,o="string"===typeof e?h(e):e;if(!o)return!1;if(gn.isArray(o)&&(o.length||0===o.length))return r=[],Ps(o,(function(e,o){e&&r.push(t.call(n,"string"===typeof e?h(e):e,o))})),r;var i=n||this;return t.call(i,o)},j=function(e,t){b(e).each((function(e,n){Ps(t,(function(e,t){w(n,t,e)}))}))},V=function(e,t){var n=b(e);Is?n.each((function(e,n){if(!1!==n.canHaveHTML){while(n.firstChild)n.removeChild(n.firstChild);try{n.innerHTML="<br>"+t,n.removeChild(n.firstChild)}catch(r){Bs("<div></div>").html("<br>"+t).contents().slice(1).appendTo(n)}return t}})):n.html(t)},q=function(t,n,r,o,i){return H(t,(function(t){var a="string"===typeof n?e.createElement(n):n;return j(a,r),o&&("string"!==typeof o&&o.nodeType?a.appendChild(o):"string"===typeof o&&V(a,o)),i?a:t.appendChild(a)}))},$=function(t,n,r){return q(e.createElement(t),t,n,r,!0)},W=Au.decode,K=Au.encodeAllRaw,X=function(e,t,n){var r,o="";for(r in o+="<"+e,t)Be(t,r)&&(o+=" "+r+'="'+K(t[r])+'"');return"undefined"!==typeof n?o+">"+n+"</"+e+">":o+" />"},Y=function(t){var n,r=e.createElement("div"),o=e.createDocumentFragment();o.appendChild(r),t&&(r.innerHTML=t);while(n=r.firstChild)o.appendChild(n);return o.removeChild(r),o},G=function(e,t){var n=b(e);return t?n.each((function(){var e;while(e=this.firstChild)3===e.nodeType&&0===e.data.length?this.removeChild(e):this.parentNode.insertBefore(e,this)})).remove():n.remove(),n.length>1?n.toArray():n[0]},J=function(e){return H(e,(function(e){var t,n=e.attributes;for(t=n.length-1;t>=0;t--)e.removeAttributeNode(n.item(t))}))},Q=function(e){return l.parse(e)},ee=function(e,t){return l.serialize(e,t)},te=function(t){var r,o;if(Te!==Hs.DOM&&e===document){if(n[t])return;n[t]=!0}o=e.getElementById("mceDefaultStyles"),o||(o=e.createElement("style"),o.id="mceDefaultStyles",o.type="text/css",r=e.getElementsByTagName("head")[0],r.firstChild?r.insertBefore(o,r.firstChild):r.appendChild(o)),o.styleSheet?o.styleSheet.cssText+=t:o.appendChild(e.createTextNode(t))},ne=function(e){e||(e=""),Z(e.split(","),(function(e){o[e]=!0,c.load(e,_)}))},re=function(e,t,n){b(e).toggleClass(t,n).each((function(){""===this.className&&Bs(this).attr("class",null)}))},oe=function(e,t){b(e).addClass(t)},ie=function(e,t){re(e,t,!1)},ae=function(e,t){return b(e).hasClass(t)},ue=function(e){b(e).show()},ce=function(e){b(e).hide()},se=function(e){return"none"===b(e).css("display")},fe=function(e){return(e||"mce_")+i++},le=function(e){var t="string"===typeof e?h(e):e;return Vr(t)?t.outerHTML:Bs("<div></div>").append(Bs(t).clone()).html()},de=function(e,t){b(e).each((function(){try{if("outerHTML"in this)return void(this.outerHTML=t)}catch(e){}G(Bs(this).html(t),!0)}))},me=function(e,t){var n=h(t);return H(e,(function(e){var t=n.parentNode,r=n.nextSibling;return r?t.insertBefore(e,r):t.appendChild(e),e}))},ge=function(e,t,n){return H(t,(function(t){return gn.is(t,"array")&&(e=e.cloneNode(!0)),n&&Ps(Ls(t.childNodes),(function(t){e.appendChild(t)})),t.parentNode.replaceChild(e,t)}))},pe=function(e,t){var n;return e.nodeName!==t.toUpperCase()&&(n=$(t),Ps(C(e),(function(t){w(n,t.nodeName,y(e,t.nodeName))})),ge(n,e,!0)),n||e},he=function(e,t){var n,r=e;while(r){n=t;while(n&&r!==n)n=n.parentNode;if(r===n)break;r=r.parentNode}return!r&&e.ownerDocument?e.ownerDocument.documentElement:r},ve=function(e){return l.toHex(gn.trim(e))},be=function(e){if(Vr(e)){var t="a"===e.nodeName.toLowerCase()&&!y(e,"href")&&y(e,"id");if(y(e,"name")||y(e,"data-mce-bookmark")||t)return!0}return!1},ye=function(e,t){var n,r,o=0;if(be(e))return!1;if(e=e.firstChild,e){var i=new zi(e,e.parentNode),a=f?f.getWhiteSpaceElements():{};t=t||(f?f.getNonEmptyElements():null);do{if(n=e.nodeType,Vr(e)){var u=e.getAttribute("data-mce-bogus");if(u){e=i.next("all"===u);continue}if(r=e.nodeName.toLowerCase(),t&&t[r]){if("br"===r){o++,e=i.next();continue}return!1}if(be(e))return!1}if(8===n)return!1;if(3===n&&!Ka(e.nodeValue))return!1;if(3===n&&e.parentNode&&a[e.parentNode.nodeName]&&Ka(e.nodeValue))return!1;e=i.next()}while(e)}return o<=1},Ce=function(){return e.createRange()},we=function(e,t,n){var r,o,i,a=Ce();if(e&&t)return a.setStart(e.parentNode,zs(e)),a.setEnd(t.parentNode,zs(t)),r=a.extractContents(),a=Ce(),a.setStart(t.parentNode,zs(t)+1),a.setEnd(e.parentNode,zs(e)+1),o=a.extractContents(),i=e.parentNode,i.insertBefore(fu(Te,r),e),n?i.insertBefore(n,e):i.insertBefore(t,e),i.insertBefore(fu(Te,o),e),G(e),n||t},Se=function(n,o,i,a){if(gn.isArray(n)){var u=n.length,c=[];while(u--)c[u]=Se(n[u],o,i,a);return c}!t.collect||n!==e&&n!==r||s.push([n,o,i,a]);var f=d.bind(n,o,i,a||Te);return f},ke=function(t,n,o){if(gn.isArray(t)){var i=t.length,a=[];while(i--)a[i]=ke(t[i],n,o);return a}if(s.length>0&&(t===e||t===r)){i=s.length;while(i--){var u=s[i];t!==u[0]||n&&n!==u[1]||o&&o!==u[2]||d.unbind(u[0],u[1],u[2])}}return d.unbind(t,n,o)},Ne=function(e,t,n){return d.fire(e,t,n)},Ee=function(e){if(e&&Vr(e)){var t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},_e=function(e){for(var t=k(),n=null;e&&e!==t;e=e.parentNode)if(n=Ee(e),null!==n)break;return n},Ae=function(){if(s.length>0){var e=s.length;while(e--){var t=s[e];d.unbind(t[0],t[1],t[2])}}xe(o,(function(e,t){c.unload(t),delete o[t]})),Hc.setDocument&&Hc.setDocument()},Re=function(e,t){if(Is){while(e){if(t===e)return!0;e=e.parentNode}return!1}return e===t||t.contains(e)},De=function(e){return"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset},Te={doc:e,settings:t,win:r,files:o,stdMode:a,boxModel:u,styleSheetLoader:c,boundEvents:s,styles:l,schema:f,events:d,isBlock:p,$:g,$$:b,root:null,clone:x,getRoot:k,getViewPort:N,getRect:B,getSize:O,getParent:I,getParents:L,get:h,getNext:F,getPrev:U,select:z,is:P,add:q,create:$,createHTML:X,createFragment:Y,remove:G,setStyle:A,getStyle:T,setStyles:R,removeAllAttribs:J,setAttrib:w,setAttribs:j,getAttrib:y,getPos:E,parseStyle:Q,serializeStyle:ee,addStyle:te,loadCSS:ne,addClass:oe,removeClass:ie,hasClass:ae,toggleClass:re,show:ue,hide:ce,isHidden:se,uniqueId:fe,setHTML:V,getOuterHTML:le,setOuterHTML:de,decode:W,encode:K,insertAfter:me,replace:ge,rename:pe,findCommonAncestor:he,toHex:ve,run:H,getAttribs:C,isEmpty:ye,createRng:Ce,nodeIndex:zs,split:we,bind:Se,unbind:ke,fire:Ne,getContentEditable:Ee,getContentEditableParent:_e,destroy:Ae,isChildOf:Re,dumpRng:De},Oe=Fs(l,t,D(Te));return Te};Hs.DOM=Hs(document),Hs.nodeIndex=zs;var js=Hs.DOM,Vs=gn.each,qs=gn.grep,$s=0,Ws=1,Ks=2,Xs=3,Ys=function(){function e(e){void 0===e&&(e={}),this.states={},this.queue=[],this.scriptLoadedCallbacks={},this.queueLoadedCallbacks=[],this.loading=0,this.settings=e}return e.prototype._setReferrerPolicy=function(e){this.settings.referrerPolicy=e},e.prototype.loadScript=function(e,t,n){var r,o=js,i=function(){o.remove(c),r&&(r.onerror=r.onload=r=null)},a=function(){i(),t()},u=function(){i(),N(n)?n():"undefined"!==typeof console&&console.log},c=o.uniqueId();r=document.createElement("script"),r.id=c,r.type="text/javascript",r.src=gn._addCacheSuffix(e),this.settings.referrerPolicy&&o.setAttrib(r,"referrerpolicy",this.settings.referrerPolicy),r.onload=a,r.onerror=u,(document.getElementsByTagName("head")[0]||document.body).appendChild(r)},e.prototype.isDone=function(e){return this.states[e]===Ks},e.prototype.markDone=function(e){this.states[e]=Ks},e.prototype.add=function(e,t,n,r){var o=this.states[e];this.queue.push(e),void 0===o&&(this.states[e]=$s),t&&(this.scriptLoadedCallbacks[e]||(this.scriptLoadedCallbacks[e]=[]),this.scriptLoadedCallbacks[e].push({success:t,failure:r,scope:n||this}))},e.prototype.load=function(e,t,n,r){return this.add(e,t,n,r)},e.prototype.remove=function(e){delete this.states[e],delete this.scriptLoadedCallbacks[e]},e.prototype.loadQueue=function(e,t,n){this.loadScripts(this.queue,e,t,n)},e.prototype.loadScripts=function(e,t,n,r){var o=this,i=[],a=function(e,t){Vs(o.scriptLoadedCallbacks[t],(function(t){N(t[e])&&t[e].call(t.scope)})),o.scriptLoadedCallbacks[t]=void 0};o.queueLoadedCallbacks.push({success:t,failure:r,scope:n||this});var u=function(){var t=qs(e);if(e.length=0,Vs(t,(function(e){o.states[e]!==Ks?o.states[e]!==Xs?o.states[e]!==Ws&&(o.states[e]=Ws,o.loading++,o.loadScript(e,(function(){o.states[e]=Ks,o.loading--,a("success",e),u()}),(function(){o.states[e]=Xs,o.loading--,i.push(e),a("failure",e),u()}))):a("failure",e):a("success",e)})),!o.loading){var n=o.queueLoadedCallbacks.slice(0);o.queueLoadedCallbacks.length=0,Vs(n,(function(e){0===i.length?N(e.success)&&e.success.call(e.scope):N(e.failure)&&e.failure.call(e.scope,i)}))}};u()},e.ScriptLoader=new e,e}(),Gs=function(e){var t=e,n=function(){return t},r=function(e){t=e};return{get:n,set:r}},Js=function(e){return b(e)&&Oe(e,"raw")},Qs=function(e){return y(e)&&e.length>1},Zs={},ef=Gs("en"),tf=function(){return Te(Zs,ef.get())},nf=function(){return Se(Zs,(function(e){return qe({},e)}))},rf=function(e){e&&ef.set(e)},of=function(){return ef.get()},af=function(e,t){var n=Zs[e];n||(Zs[e]=n={}),xe(t,(function(e,t){n[t.toLowerCase()]=e}))},uf=function(e){var t=tf().getOr({}),n=function(e){return N(e)?Object.prototype.toString.call(e):r(e)?"":""+e},r=function(e){return""===e||null===e||void 0===e},o=function(e){var r=n(e);return Te(t,r.toLowerCase()).map(n).getOr(r)},i=function(e){return e.replace(/{context:\w+}$/,"")};if(r(e))return"";if(Js(e))return n(e.raw);if(Qs(e)){var a=e.slice(1),u=o(e[0]).replace(/\{([0-9]+)\}/g,(function(e,t){return Oe(a,t)?n(a[t]):e}));return i(u)}return i(o(e))},cf=function(){return tf().bind((function(e){return Te(e,"_dir")})).exists((function(e){return"rtl"===e}))},sf=function(e){return Oe(Zs,e)},ff={getData:nf,setCode:rf,getCode:of,add:af,translate:uf,isRtl:cf,hasCode:sf},lf=function(){var e=[],t={},n={},r=[],o=function(e,t){var n=ne(r,(function(n){return n.name===e&&n.state===t}));Z(n,(function(e){return e.callback()}))},i=function(e){if(n[e])return n[e].instance},a=function(e){var t;return n[e]&&(t=n[e].dependencies),t||[]},u=function(e,n){!1!==lf.languageLoad&&g(e,(function(){var r=ff.getCode(),o=","+(n||"")+",";!r||n&&-1===o.indexOf(","+r+",")||Ys.ScriptLoader.add(t[e]+"/langs/"+r+".js")}),"loaded")},c=function(t,r,i){var a=r;return e.push(a),n[t]={instance:a,dependencies:i},o(t,"added"),a},s=function(e){delete t[e],delete n[e]},f=function(e,t){return"object"===typeof t?t:"string"===typeof e?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}},l=function(e,n){var r=t[e];Z(n,(function(e){Ys.ScriptLoader.add(r+"/"+e)}))},d=function(e,t,n,r){var o=a(e);Z(o,(function(e){var n=f(t,e);m(n.resource,n,void 0,void 0)})),n&&(r?n.call(r):n.call(Ys))},m=function(e,r,i,a,u){if(!t[e]){var c="string"===typeof r?r:r.prefix+r.resource+r.suffix;0!==c.indexOf("/")&&-1===c.indexOf("://")&&(c=lf.baseURL+"/"+c),t[e]=c.substring(0,c.lastIndexOf("/"));var s=function(){o(e,"loaded"),d(e,r,i,a)};n[e]?s():Ys.ScriptLoader.add(c,s,a,u)}},g=function(e,o,i){void 0===i&&(i="added"),Oe(n,e)&&"added"===i||Oe(t,e)&&"loaded"===i?o():r.push({name:e,state:i,callback:o})};return{items:e,urls:t,lookup:n,_listeners:r,get:i,dependencies:a,requireLangPack:u,add:c,remove:s,createUrl:f,addComponents:l,load:m,waitFor:g}};lf.languageLoad=!0,lf.baseURL="",lf.PluginManager=lf(),lf.ThemeManager=lf();var df=function(e){var t=Gs(q.none()),n=function(){return t.get().each(e)},r=function(){n(),t.set(q.none())},o=function(){return t.get().isSome()},i=function(){return t.get()},a=function(e){n(),t.set(q.some(e))};return{clear:r,isSet:o,get:i,set:a}},mf=function(){var e=df(_),t=function(t){return e.get().each(t)};return qe(qe({},e),{on:t})},gf=function(e,t){var n=null,r=function(){C(n)||(clearTimeout(n),n=null)},o=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];C(n)&&(n=setTimeout((function(){n=null,e.apply(null,r)}),t))};return{cancel:r,throttle:o}},pf=function(e,t){var n=null,r=function(){C(n)||(clearTimeout(n),n=null)},o=function(){for(var o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];r(),n=setTimeout((function(){n=null,e.apply(null,o)}),t)};return{cancel:r,throttle:o}},hf=function(e,t){var n=yo(e,t);return void 0===n||""===n?[]:n.split(" ")},vf=function(e,t,n){var r=hf(e,t),o=r.concat([n]);return vo(e,t,o.join(" ")),!0},bf=function(e,t,n){var r=ne(hf(e,t),(function(e){return e!==n}));return r.length>0?vo(e,t,r.join(" ")):xo(e,t),!1},yf=function(e){return void 0!==e.dom.classList},Cf=function(e){return hf(e,"class")},wf=function(e,t){return vf(e,"class",t)},xf=function(e,t){return bf(e,"class",t)},Sf=function(e,t){yf(e)?e.dom.classList.add(t):wf(e,t)},kf=function(e){var t=yf(e)?e.dom.classList:Cf(e);0===t.length&&xo(e,"class")},Nf=function(e,t){if(yf(e)){var n=e.dom.classList;n.remove(t)}else xf(e,t);kf(e)},Ef=function(e,t){return yf(e)&&e.dom.classList.contains(t)},_f=function(e,t){var n=[];return Z(rr(e),(function(e){t(e)&&(n=n.concat([e])),n=n.concat(_f(e,t))})),n},Af=function(e,t){return Tn(t,e)},Rf=D("mce-annotation"),Df=D("data-mce-annotation"),Tf=D("data-mce-annotation-uid"),Of=function(e,t){var n=e.selection.getRng(),r=Cn.fromDom(n.startContainer),o=Cn.fromDom(e.getBody()),i=t.fold((function(){return"."+Rf()}),(function(e){return"["+Df()+'="'+e+'"]'})),a=or(r,n.startOffset).getOr(r),u=Ai(a,i,(function(e){return Bn(e,o)})),c=function(e,t){return wo(e,t)?q.some(yo(e,t)):q.none()};return u.bind((function(t){return c(t,""+Tf()).bind((function(n){return c(t,""+Df()).map((function(t){var r=Pf(e,n);return{uid:n,name:t,elements:r}}))}))}))},Bf=function(e){return Hn(e)&&Ef(e,Rf())},Pf=function(e,t){var n=Cn.fromDom(e.getBody());return Af(n,"["+Tf()+'="'+t+'"]')},Lf=function(e,t){var n=Cn.fromDom(e.getBody()),r=Af(n,"["+Df()+'="'+t+'"]'),o={};return Z(r,(function(e){var t=yo(e,Tf()),n=Te(o,t).getOr([]);o[t]=n.concat([e])})),o},If=function(e,t){var n=Gs({}),r=function(){return{listeners:[],previous:mf()}},o=function(e,t){i(e,(function(e){return t(e),e}))},i=function(e,t){var o=n.get(),i=Te(o,e).getOrThunk(r),a=t(i);o[e]=a,n.set(o)},a=function(e,t,n){o(e,(function(r){Z(r.listeners,(function(r){return r(!0,e,{uid:t,nodes:Q(n,(function(e){return e.dom}))})}))}))},u=function(e){o(e,(function(t){Z(t.listeners,(function(t){return t(!1,e)}))}))},c=pf((function(){var t=n.get(),r=ge(Ce(t));Z(r,(function(t){i(t,(function(n){var r=n.previous.get();return Of(e,q.some(t)).fold((function(){r.isSome()&&(u(t),n.previous.clear())}),(function(e){var t=e.uid,o=e.name,i=e.elements;so(r,t)||(a(o,t,i),n.previous.set(t))})),{previous:n.previous,listeners:n.listeners}}))}))}),30);e.on("remove",(function(){c.cancel()})),e.on("NodeChange",(function(){c.throttle()}));var s=function(e,t){i(e,(function(e){return{previous:e.previous,listeners:e.listeners.concat([t])}}))};return{addListener:s}},Mf=function(e,t){var n=function(e){return q.from(e.attr(Df())).bind(t.lookup)};e.on("init",(function(){e.serializer.addNodeFilter("span",(function(e){Z(e,(function(e){n(e).each((function(t){!1===t.persistent&&e.unwrap()}))}))}))}))},Ff=function(){var e={},t=function(t,n){e[t]={name:t,settings:n}},n=function(t){return Te(e,t).map((function(e){return e.settings}))};return{register:t,lookup:n}},Uf=0,zf=function(e){var t=new Date,n=t.getTime(),r=Math.floor(1e9*Math.random());return Uf++,e+"_"+r+Uf+String(n)},Hf=function(e,t){Z(t,(function(t){Sf(e,t)}))},jf=function(e,t){var n=t||document,r=n.createElement("div");return r.innerHTML=e,rr(Cn.fromDom(r))},Vf=function(e){return Q(e,Cn.fromDom)},qf=function(e){return e.dom.innerHTML},$f=function(e,t){var n=Wn(e),r=n.dom,o=Cn.fromDom(r.createDocumentFragment()),i=jf(t,r);kr(o,i),Nr(e),wr(e,o)},Wf=function(e,t){return Cn.fromDom(e.dom.cloneNode(t))},Kf=function(e){return Wf(e,!1)},Xf=function(e){return Wf(e,!0)},Yf=function(e,t,n){void 0===n&&(n=F);var r=new zi(e,t),o=function(e){var t;do{t=r[e]()}while(t&&!Zr(t)&&!n(t));return q.from(t).filter(Zr)};return{current:function(){return q.from(r.current()).filter(Zr)},next:function(){return o("next")},prev:function(){return o("prev")},prev2:function(){return o("prev2")}}},Gf=function(e,t){var n=t||function(t){return e.isBlock(t)||ro(t)||ao(t)},r=function(e,t,n,o){if(Zr(e)){var i=o(e,t,e.data);if(-1!==i)return q.some({container:e,offset:i})}return n().bind((function(e){return r(e.container,e.offset,n,o)}))},o=function(e,t,o,i){var a=Yf(e,i,n);return r(e,t,(function(){return a.prev().map((function(e){return{container:e,offset:e.length}}))}),o).getOrNull()},i=function(e,t,o,i){var a=Yf(e,i,n);return r(e,t,(function(){return a.next().map((function(e){return{container:e,offset:0}}))}),o).getOrNull()};return{backwards:o,forwards:i}},Jf=Math.round,Qf=function(e){return e?{left:Jf(e.left),top:Jf(e.top),bottom:Jf(e.bottom),right:Jf(e.right),width:Jf(e.width),height:Jf(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0}},Zf=function(e,t){return e=Qf(e),t||(e.left=e.left+e.width),e.right=e.left,e.width=0,e},el=function(e,t){return e.left===t.left&&e.top===t.top&&e.bottom===t.bottom&&e.right===t.right},tl=function(e,t,n){return e>=0&&e<=Math.min(t.height,n.height)/2},nl=function(e,t){var n=Math.min(t.height/2,e.height/2);return e.bottom-n<t.top||!(e.top>t.bottom)&&tl(t.top-e.bottom,e,t)},rl=function(e,t){return e.top>t.bottom||!(e.bottom<t.top)&&tl(t.bottom-e.top,e,t)},ol=function(e,t,n){return t>=e.left&&t<=e.right&&n>=e.top&&n<=e.bottom},il=function(e,t,n){return Math.min(Math.max(e,t),n)},al=function(e){var t=e.startContainer,n=e.startOffset;return t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},ul=function(e,t){if(Vr(e)&&e.hasChildNodes()){var n=e.childNodes,r=il(t,0,n.length-1);return n[r]}return e},cl=function(e,t){return t<0&&Vr(e)&&e.hasChildNodes()?void 0:ul(e,t)},sl=new RegExp("[̀-ͯ҃-҇҈-҉֑-ֽֿׁ-ׂׄ-ׇׅؐ-ًؚ-ٰٟۖ-ۜ۟-ۤۧ-۪ۨ-ܑۭܰ-݊ަ-ް߫-߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ࣣ-ंऺ़ु-ै्॑-ॗॢ-ॣঁ়াু-ৄ্ৗৢ-ৣਁ-ਂ਼ੁ-ੂੇ-ੈੋ-੍ੑੰ-ੱੵઁ-ં઼ુ-ૅે-ૈ્ૢ-ૣଁ଼ାିୁ-ୄ୍ୖୗୢ-ୣஂாீ்ௗఀా-ీె-ైొ-్ౕ-ౖౢ-ౣಁ಼ಿೂೆೌ-್ೕ-ೖೢ-ೣഁാു-ൄ്ൗൢ-ൣ්ාි-ුූෟัิ-ฺ็-๎ັິ-ູົ-ຼ່-ໍ༘-ཱ༹༙༵༷-ཾྀ-྄྆-྇ྍ-ྗྙ-ྼ࿆ိ-ူဲ-့္-်ွ-ှၘ-ၙၞ-ၠၱ-ၴႂႅ-ႆႍႝ፝-፟ᜒ-᜔ᜲ-᜴ᝒ-ᝓᝲ-ᝳ឴-឵ិ-ួំ៉-៓៝᠋-᠍ᢩᤠ-ᤢᤧ-ᤨᤲ᤹-᤻ᨗ-ᨘᨛᩖᩘ-ᩞ᩠ᩢᩥ-ᩬᩳ-᩿᩼᪰-᪽᪾ᬀ-ᬃ᬴ᬶ-ᬺᬼᭂ᭫-᭳ᮀ-ᮁᮢ-ᮥᮨ-ᮩ᮫-ᮭ᯦ᯨ-ᯩᯭᯯ-ᯱᰬ-ᰳᰶ-᰷᳐-᳔᳒-᳢᳠-᳨᳭᳴᳸-᳹᷀-᷵᷼-᷿‌-‍⃐-⃜⃝-⃠⃡⃢-⃤⃥-⃰⳯-⵿⳱ⷠ-〪ⷿ-〭〮-゙〯-゚꙯꙰-꙲ꙴ-꙽ꚞ-ꚟ꛰-꛱ꠂ꠆ꠋꠥ-ꠦ꣄꣠-꣱ꤦ-꤭ꥇ-ꥑꦀ-ꦂ꦳ꦶ-ꦹꦼꧥꨩ-ꨮꨱ-ꨲꨵ-ꨶꩃꩌꩼꪰꪲ-ꪴꪷ-ꪸꪾ-꪿꫁ꫬ-ꫭ꫶ꯥꯨ꯭ﬞ︀-️︠-︯ﾞ-ﾟ]"),fl=function(e){return"string"===typeof e&&e.charCodeAt(0)>=768&&sl.test(e)},ll=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){for(var n=0;n<e.length;n++)if(e[n](t))return!0;return!1}},dl=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){for(var n=0;n<e.length;n++)if(!e[n](t))return!1;return!0}},ml=Vr,gl=Ua,pl=$r("display","block table"),hl=$r("float","left right"),vl=dl(ml,gl,P(hl)),bl=P($r("white-space","pre pre-line pre-wrap")),yl=Zr,Cl=ro,wl=Hs.nodeIndex,xl=cl,Sl=function(e){return"createRange"in e?e.createRange():Hs.DOM.createRng()},kl=function(e){return e&&/[\r\n\t ]/.test(e)},Nl=function(e){return!!e.setStart&&!!e.setEnd},El=function(e){var t=e.startContainer,n=e.startOffset;if(kl(e.toString())&&bl(t.parentNode)&&Zr(t)){var r=t.data;if(kl(r[n-1])||kl(r[n+1]))return!0}return!1},_l=function(e){var t=e.ownerDocument,n=Sl(t),r=t.createTextNode(sa),o=e.parentNode;o.insertBefore(r,e),n.setStart(r,0),n.setEnd(r,1);var i=Qf(n.getBoundingClientRect());return o.removeChild(r),i},Al=function(e){var t=e.startContainer,n=e.endContainer,r=e.startOffset,o=e.endOffset;if(t===n&&Zr(n)&&0===r&&1===o){var i=e.cloneRange();return i.setEndAfter(n),Dl(i)}return null},Rl=function(e){return 0===e.left&&0===e.right&&0===e.top&&0===e.bottom},Dl=function(e){var t,n=e.getClientRects();return t=n.length>0?Qf(n[0]):Qf(e.getBoundingClientRect()),!Nl(e)&&Cl(e)&&Rl(t)?_l(e):Rl(t)&&Nl(e)?Al(e):t},Tl=function(e,t){var n=Zf(e,t);return n.width=1,n.right=n.left+1,n},Ol=function(e){var t=[],n=function(e){0!==e.height&&(t.length>0&&el(e,t[t.length-1])||t.push(e))},r=function(e,r){var o=Sl(e.ownerDocument);if(r<e.data.length){if(fl(e.data[r]))return t;if(fl(e.data[r-1])&&(o.setStart(e,r),o.setEnd(e,r+1),!El(o)))return n(Tl(Dl(o),!1)),t}r>0&&(o.setStart(e,r-1),o.setEnd(e,r),El(o)||n(Tl(Dl(o),!1))),r<e.data.length&&(o.setStart(e,r),o.setEnd(e,r+1),El(o)||n(Tl(Dl(o),!0)))},o=e.container(),i=e.offset();if(yl(o))return r(o,i),t;if(ml(o))if(e.isAtEnd()){var a=xl(o,i);yl(a)&&r(a,a.data.length),vl(a)&&!Cl(a)&&n(Tl(Dl(a),!1))}else{a=xl(o,i);if(yl(a)&&r(a,0),vl(a)&&e.isAtEnd())return n(Tl(Dl(a),!1)),t;var u=xl(e.container(),e.offset()-1);vl(u)&&!Cl(u)&&(pl(u)||pl(a)||!vl(a))&&n(Tl(Dl(u),!1)),vl(a)&&n(Tl(Dl(a),!0))}return t},Bl=function(e,t,n){var r=function(){return yl(e),0===t},o=function(){return yl(e)?t>=e.data.length:t>=e.childNodes.length},i=function(){var n=Sl(e.ownerDocument);return n.setStart(e,t),n.setEnd(e,t),n},a=function(){return n||(n=Ol(Bl(e,t))),n},u=function(){return a().length>0},c=function(n){return n&&e===n.container()&&t===n.offset()},s=function(n){return xl(e,n?t-1:t)};return{container:D(e),offset:D(t),toRange:i,getClientRects:a,isVisible:u,isAtStart:r,isAtEnd:o,isEqual:c,getNode:s}};Bl.fromRangeStart=function(e){return Bl(e.startContainer,e.startOffset)},Bl.fromRangeEnd=function(e){return Bl(e.endContainer,e.endOffset)},Bl.after=function(e){return Bl(e.parentNode,wl(e)+1)},Bl.before=function(e){return Bl(e.parentNode,wl(e))},Bl.isAbove=function(e,t){return lo(he(t.getClientRects()),ve(e.getClientRects()),nl).getOr(!1)},Bl.isBelow=function(e,t){return lo(ve(t.getClientRects()),he(e.getClientRects()),rl).getOr(!1)},Bl.isAtStart=function(e){return!!e&&e.isAtStart()},Bl.isAtEnd=function(e){return!!e&&e.isAtEnd()},Bl.isTextPosition=function(e){return!!e&&Zr(e.container())},Bl.isElementPosition=function(e){return!1===Bl.isTextPosition(e)};var Pl,Ll=function(e,t){Zr(t)&&0===t.data.length&&e.remove(t)},Il=function(e,t,n){t.insertNode(n),Ll(e,n.previousSibling),Ll(e,n.nextSibling)},Ml=function(e,t,n){var r=q.from(n.firstChild),o=q.from(n.lastChild);t.insertNode(n),r.each((function(t){return Ll(e,t.previousSibling)})),o.each((function(t){return Ll(e,t.nextSibling)}))},Fl=function(e,t,n){no(n)?Ml(e,t,n):Il(e,t,n)},Ul=Zr,zl=Xr,Hl=Hs.nodeIndex,jl=function(e){var t=e.parentNode;return zl(t)?jl(t):t},Vl=function(e){return e?He(e.childNodes,(function(e,t){return zl(t)&&"BR"!==t.nodeName?e=e.concat(Vl(t)):e.push(t),e}),[]):[]},ql=function(e,t){while(e=e.previousSibling){if(!Ul(e))break;t+=e.data.length}return t},$l=function(e){return function(t){return e===t}},Wl=function(e){var t,n;t=Vl(jl(e)),n=je(t,$l(e),e),t=t.slice(0,n+1);var r=He(t,(function(e,n,r){return Ul(n)&&Ul(t[r-1])&&e++,e}),0);return t=Ue(t,qr([e.nodeName])),n=je(t,$l(e),e),n-r},Kl=function(e){var t;return t=Ul(e)?"text()":e.nodeName.toLowerCase(),t+"["+Wl(e)+"]"},Xl=function(e,t,n){var r=[];for(t=t.parentNode;t!==e;t=t.parentNode){if(n&&n(t))break;r.push(t)}return r},Yl=function(e,t){var n,r,o,i,a,u=[];return n=t.container(),r=t.offset(),Ul(n)?o=ql(n,r):(i=n.childNodes,r>=i.length?(o="after",r=i.length-1):o="before",n=i[r]),u.push(Kl(n)),a=Xl(e,n),a=Ue(a,P(Xr)),u=u.concat(Fe(a,(function(e){return Kl(e)}))),u.reverse().join("/")+","+o},Gl=function(e,t,n){var r=Vl(e);return r=Ue(r,(function(e,t){return!Ul(e)||!Ul(r[t-1])})),r=Ue(r,qr([t])),r[n]},Jl=function(e,t){var n,r=e,o=0;while(Ul(r)){if(n=r.data.length,t>=o&&t<=o+n){e=r,t-=o;break}if(!Ul(r.nextSibling)){e=r,t=n;break}o+=n,r=r.nextSibling}return Ul(e)&&t>e.data.length&&(t=e.data.length),Bl(e,t)},Ql=function(e,t){var n;if(!t)return null;var r=t.split(","),o=r[0].split("/");n=r.length>1?r[1]:"before";var i=He(o,(function(e,t){var n=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t);return n?("text()"===n[1]&&(n[1]="#text"),Gl(e,n[1],parseInt(n[2],10))):null}),e);return i?Ul(i)?Jl(i,parseInt(n,10)):(n="after"===n?Hl(i)+1:Hl(i),Bl(i.parentNode,n)):null},Zl=ao,ed=function(e,t,n){var r,o;for(o=e(t.data.slice(0,n)).length,r=t.previousSibling;r&&Zr(r);r=r.previousSibling)o+=e(r.data).length;return o},td=function(e,t,n,r,o){var i,a=r[o?"startContainer":"endContainer"],u=r[o?"startOffset":"endOffset"],c=[],s=0,f=e.getRoot();for(Zr(a)?c.push(n?ed(t,a,u):u):(i=a.childNodes,u>=i.length&&i.length&&(s=1,u=Math.max(0,i.length-1)),c.push(e.nodeIndex(i[u],n)+s));a&&a!==f;a=a.parentNode)c.push(e.nodeIndex(a,n));return c},nd=function(e,t,n,r){var o=t.dom,i={};return i.start=td(o,e,n,r,!0),t.isCollapsed()||(i.end=td(o,e,n,r,!1)),Da(r)&&(i.isFakeCaret=!0),i},rd=function(e,t,n){var r=0;return gn.each(e.select(t),(function(e){if("all"!==e.getAttribute("data-mce-bogus"))return e!==n&&void r++})),r},od=function(e,t){var n,r,o,i=t?"start":"end";n=e[i+"Container"],r=e[i+"Offset"],Vr(n)&&"TR"===n.nodeName&&(o=n.childNodes,n=o[Math.min(t?r:r-1,o.length-1)],n&&(r=t?0:n.childNodes.length,e["set"+(t?"Start":"End")](n,r)))},id=function(e){return od(e,!0),od(e,!1),e},ad=function(e,t){var n;if(Vr(e)&&(e=ul(e,t),Zl(e)))return e;if(ya(e)){if(Zr(e)&&va(e)&&(e=e.parentNode),n=e.previousSibling,Zl(n))return n;if(n=e.nextSibling,Zl(n))return n}},ud=function(e){return ad(e.startContainer,e.startOffset)||ad(e.endContainer,e.endOffset)},cd=function(e,t,n){var r=n.getNode(),o=r?r.nodeName:null,i=n.getRng();if(Zl(r)||"IMG"===o)return{name:o,index:rd(n.dom,o,r)};var a=ud(i);return a?(o=a.tagName,{name:o,index:rd(n.dom,o,a)}):nd(e,n,t,i)},sd=function(e){var t=e.getRng();return{start:Yl(e.dom.getRoot(),Bl.fromRangeStart(t)),end:Yl(e.dom.getRoot(),Bl.fromRangeEnd(t))}},fd=function(e){return{rng:e.getRng()}},ld=function(e,t,n){var r={"data-mce-type":"bookmark",id:t,style:"overflow:hidden;line-height:0px"};return n?e.create("span",r,"&#xFEFF;"):e.create("span",r)},dd=function(e,t){var n=e.dom,r=e.getRng(),o=n.uniqueId(),i=e.isCollapsed(),a=e.getNode(),u=a.nodeName;if("IMG"===u)return{name:u,index:rd(n,u,a)};var c=id(r.cloneRange());if(!i){c.collapse(!1);var s=ld(n,o+"_end",t);Fl(n,c,s)}r=id(r),r.collapse(!0);var f=ld(n,o+"_start",t);return Fl(n,r,f),e.moveToBookmark({id:o,keep:!0}),{id:o}},md=function(e,t,n){return 2===t?cd(ga,n,e):3===t?sd(e):t?fd(e):dd(e,!1)},gd=B(cd,T,!0),pd=Hs.DOM,hd="font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow",vd=function(e,t,n){var r=e.getParam(t,n);if(-1!==r.indexOf("=")){var o=e.getParam(t,"","hash");return Te(o,e.id).getOr(n)}return r},bd=function(e){return e.getParam("iframe_attrs",{})},yd=function(e){return e.getParam("doctype","<!DOCTYPE html>")},Cd=function(e){return e.getParam("document_base_url","")},wd=function(e){return vd(e,"body_id","tinymce")},xd=function(e){return vd(e,"body_class","")},Sd=function(e){return e.getParam("content_security_policy","")},kd=function(e){return e.getParam("br_in_pre",!0)},Nd=function(e){if(e.getParam("force_p_newlines",!1))return"p";var t=e.getParam("forced_root_block","p");return!1===t?"":!0===t?"p":t},Ed=function(e){return e.getParam("forced_root_block_attrs",{})},_d=function(e){return e.getParam("br_newline_selector",".mce-toc h2,figcaption,caption")},Ad=function(e){return e.getParam("no_newline_selector","")},Rd=function(e){return e.getParam("keep_styles",!0)},Dd=function(e){return e.getParam("end_container_on_empty_block",!1)},Td=function(e){return gn.explode(e.getParam("font_size_style_values","xx-small,x-small,small,medium,large,x-large,xx-large"))},Od=function(e){return gn.explode(e.getParam("font_size_classes",""))},Bd=function(e){return e.getParam("images_dataimg_filter",U,"function")},Pd=function(e){return e.getParam("automatic_uploads",!0,"boolean")},Ld=function(e){return e.getParam("images_reuse_filename",!1,"boolean")},Id=function(e){return e.getParam("images_replace_blob_uris",!0,"boolean")},Md=function(e){return e.getParam("icons","","string")},Fd=function(e){return e.getParam("icons_url","","string")},Ud=function(e){return e.getParam("images_upload_url","","string")},zd=function(e){return e.getParam("images_upload_base_path","","string")},Hd=function(e){return e.getParam("images_upload_credentials",!1,"boolean")},jd=function(e){return e.getParam("images_upload_handler",null,"function")},Vd=function(e){return e.getParam("content_css_cors",!1,"boolean")},qd=function(e){return e.getParam("referrer_policy","","string")},$d=function(e){return e.getParam("language","en","string")},Wd=function(e){return e.getParam("language_url","","string")},Kd=function(e){return e.getParam("indent_use_margin",!1)},Xd=function(e){return e.getParam("indentation","40px","string")},Yd=function(e){var t=e.getParam("content_css");return v(t)?Q(t.split(","),lt):y(t)?t:!1===t||e.inline?[]:["default"]},Gd=function(e){var t=e.getParam("font_css",[]);return y(t)?t:Q(t.split(","),lt)},Jd=function(e){return e.getParam("directionality",ff.isRtl()?"rtl":void 0)},Qd=function(e){return e.getParam("inline_boundaries_selector","a[href],code,.mce-annotation","string")},Zd=function(e){var t=e.getParam("object_resizing");return!1!==t&&!en.iOS&&(v(t)?t:"table,img,figure.image,div,video,iframe")},em=function(e){return e.getParam("resize_img_proportional",!0,"boolean")},tm=function(e){return e.getParam("placeholder",pd.getAttrib(e.getElement(),"placeholder"),"string")},nm=function(e){return e.getParam("event_root")},rm=function(e){return e.getParam("service_message")},om=function(e){return e.getParam("theme")},im=function(e){return e.getParam("validate")},am=function(e){return!1!==e.getParam("inline_boundaries")},um=function(e){return e.getParam("formats")},cm=function(e){var t=e.getParam("preview_styles",hd);return v(t)?t:""},sm=function(e){return e.getParam("format_empty_lines",!1,"boolean")},fm=function(e){return e.getParam("custom_ui_selector","","string")},lm=function(e){return e.getParam("theme_url")},dm=function(e){return e.getParam("inline")},mm=function(e){return e.getParam("hidden_input")},gm=function(e){return e.getParam("submit_patch")},pm=function(e){return"xml"===e.getParam("encoding")},hm=function(e){return e.getParam("add_form_submit_trigger")},vm=function(e){return e.getParam("add_unload_trigger")},bm=function(e){return""!==Nd(e)},ym=function(e){return e.getParam("custom_undo_redo_levels",0,"number")},Cm=function(e){return e.getParam("disable_nodechange")},wm=function(e){return e.getParam("readonly")},xm=function(e){return e.getParam("content_css_cors")},Sm=function(e){return e.getParam("plugins","","string")},km=function(e){return e.getParam("external_plugins")},Nm=function(e){return e.getParam("block_unsupported_drop",!0,"boolean")},Em=function(e){return e.getParam("visual",!0,"boolean")},_m=function(e){return e.getParam("visual_table_class","mce-item-table","string")},Am=function(e){return e.getParam("visual_anchor_class","mce-item-anchor","string")},Rm=function(e){return e.getParam("iframe_aria_text","Rich Text Area. Press ALT-0 for help.","string")},Dm=Vr,Tm=Zr,Om=function(e){var t=e.parentNode;t&&t.removeChild(e)},Bm=function(e){var t=ga(e);return{count:e.length-t.length,text:t}},Pm=function(e){var t;while(-1!==(t=e.data.lastIndexOf(da)))e.deleteData(t,1)},Lm=function(e,t){return Hm(e),t},Im=function(e,t){var n=Bm(e.data.substr(0,t.offset())),r=Bm(e.data.substr(t.offset())),o=n.text+r.text;return o.length>0?(Pm(e),Bl(e,t.offset()-n.count)):t},Mm=function(e,t){var n=t.container(),r=Y(be(n.childNodes),e).map((function(e){return e<t.offset()?Bl(n,t.offset()-1):t})).getOr(t);return Hm(e),r},Fm=function(e,t){return Tm(e)&&t.container()===e?Im(e,t):Lm(e,t)},Um=function(e,t){return t.container()===e.parentNode?Mm(e,t):Lm(e,t)},zm=function(e,t){return Bl.isTextPosition(t)?Fm(e,t):Um(e,t)},Hm=function(e){Dm(e)&&ya(e)&&(Ca(e)?e.removeAttribute("data-mce-caret"):Om(e)),Tm(e)&&(Pm(e),0===e.data.length&&Om(e))},jm=$t().browser,Vm=ao,qm=co,$m=uo,Wm="*[contentEditable=false],video,audio,embed,object",Km=function(e,t,n){var r,o,i=Zf(t.getBoundingClientRect(),n);if("BODY"===e.tagName){var a=e.ownerDocument.documentElement;r=e.scrollLeft||a.scrollLeft,o=e.scrollTop||a.scrollTop}else{var u=e.getBoundingClientRect();r=e.scrollLeft-u.left,o=e.scrollTop-u.top}i.left+=r,i.right+=r,i.top+=o,i.bottom+=o,i.width=1;var c=t.offsetWidth-t.clientWidth;return c>0&&(n&&(c*=-1),i.left+=c,i.right+=c),i},Xm=function(e){for(var t=Af(Cn.fromDom(e),Wm),n=0;n<t.length;n++){var r=t[n].dom,o=r.previousSibling;if(_a(o)){var i=o.data;1===i.length?o.parentNode.removeChild(o):o.deleteData(i.length-1,1)}if(o=r.nextSibling,Ea(o)){i=o.data;1===i.length?o.parentNode.removeChild(o):o.deleteData(0,1)}}},Ym=function(e,t,n,r){var o,i,a=mf(),u=Nd(e),c=u.length>0?u:"p",s=function(e,r){var o;if(f(),$m(r))return null;if(!n(r))return i=wa(r,e),o=r.ownerDocument.createRange(),Jm(i.nextSibling)?(o.setStart(i,0),o.setEnd(i,0)):(o.setStart(i,1),o.setEnd(i,1)),o;i=Na(c,r,e);var u=Km(t,r,e);Bs(i).css("top",u.top);var s=Bs('<div class="mce-visual-caret" data-mce-bogus="all"></div>').css(qe({},u)).appendTo(t)[0];return a.set({caret:s,element:r,before:e}),e&&Bs(s).addClass("mce-visual-caret-before"),l(),o=r.ownerDocument.createRange(),o.setStart(i,0),o.setEnd(i,0),o},f=function(){Xm(t),i&&(Hm(i),i=null),a.on((function(e){Bs(e.caret).remove(),a.clear()})),o&&(Ii.clearInterval(o),o=void 0)},l=function(){o=Ii.setInterval((function(){r()?Bs("div.mce-visual-caret",t).toggleClass("mce-visual-caret-hidden"):Bs("div.mce-visual-caret",t).addClass("mce-visual-caret-hidden")}),500)},d=function(){a.on((function(e){var n=Km(t,e.element,e.before);Bs(e.caret).css(qe({},n))}))},m=function(){return Ii.clearInterval(o)},g=function(){return".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}"};return{show:s,hide:f,getCss:g,reposition:d,destroy:m}},Gm=function(){return jm.isIE()||jm.isEdge()||jm.isFirefox()},Jm=function(e){return Vm(e)||qm(e)},Qm=function(e){return Jm(e)||Gr(e)&&Gm()},Zm=ao,eg=co,tg=$r("display","block table table-cell table-caption list-item"),ng=ya,rg=va,og=Vr,ig=Ua,ag=function(e){return e>0},ug=function(e){return e<0},cg=function(e,t){var n;while(n=e(t))if(!rg(n))return n;return null},sg=function(e,t,n,r,o){var i=new zi(e,r),a=Zm(e)||rg(e);if(ug(t)){if(a&&(e=cg(i.prev.bind(i),!0),n(e)))return e;while(e=cg(i.prev.bind(i),o))if(n(e))return e}if(ag(t)){if(a&&(e=cg(i.next.bind(i),!0),n(e)))return e;while(e=cg(i.next.bind(i),o))if(n(e))return e}return null},fg=function(e,t){while(e&&e!==t){if(tg(e))return e;e=e.parentNode}return null},lg=function(e,t,n){return fg(e.container(),n)===fg(t.container(),n)},dg=function(e,t){if(!t)return null;var n=t.container(),r=t.offset();return og(n)?n.childNodes[r+e]:null},mg=function(e,t){var n=t.ownerDocument.createRange();return e?(n.setStartBefore(t),n.setEndBefore(t)):(n.setStartAfter(t),n.setEndAfter(t)),n},gg=function(e,t,n){return fg(t,e)===fg(n,e)},pg=function(e,t,n){var r=e?"previousSibling":"nextSibling";while(n&&n!==t){var o=n[r];if(ng(o)&&(o=o[r]),Zm(o)||eg(o)){if(gg(t,o,n))return o;break}if(ig(o))break;n=n.parentNode}return null},hg=B(mg,!0),vg=B(mg,!1),bg=function(e,t,n){var r,o=B(pg,!0,t),i=B(pg,!1,t),a=n.startContainer,u=n.startOffset;if(va(a)){og(a)||(a=a.parentNode);var c=a.getAttribute("data-mce-caret");if("before"===c&&(r=a.nextSibling,Qm(r)))return hg(r);if("after"===c&&(r=a.previousSibling,Qm(r)))return vg(r)}if(!n.collapsed)return n;if(Zr(a)){if(ng(a)){if(1===e){if(r=i(a),r)return hg(r);if(r=o(a),r)return vg(r)}if(-1===e){if(r=o(a),r)return vg(r);if(r=i(a),r)return hg(r)}return n}if(_a(a)&&u>=a.data.length-1)return 1===e&&(r=i(a),r)?hg(r):n;if(Ea(a)&&u<=1)return-1===e&&(r=o(a),r)?vg(r):n;if(u===a.data.length)return r=i(a),r?hg(r):n;if(0===u)return r=o(a),r?vg(r):n}return n},yg=function(e,t){return q.from(dg(e?0:-1,t)).filter(Zm)},Cg=function(e,t,n){var r=bg(e,t,n);return-1===e?Bl.fromRangeStart(r):Bl.fromRangeEnd(r)},wg=function(e){return q.from(e.getNode()).map(Cn.fromDom)},xg=function(e){return q.from(e.getNode(!0)).map(Cn.fromDom)},Sg=function(e,t){while(t=e(t))if(t.isVisible())return t;return t},kg=function(e,t){var n=lg(e,t);return!(n||!ro(e.getNode()))||n};(function(e){e[e["Backwards"]=-1]="Backwards",e[e["Forwards"]=1]="Forwards"})(Pl||(Pl={}));var Ng=ao,Eg=Zr,_g=Vr,Ag=ro,Rg=Ua,Dg=qa,Tg=$a,Og=function(e,t){var n=[];while(e&&e!==t)n.push(e),e=e.parentNode;return n},Bg=function(e,t){return e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null},Pg=function(e,t){if(ag(e)){if(Rg(t.previousSibling)&&!Eg(t.previousSibling))return Bl.before(t);if(Eg(t))return Bl(t,0)}if(ug(e)){if(Rg(t.nextSibling)&&!Eg(t.nextSibling))return Bl.after(t);if(Eg(t))return Bl(t,t.data.length)}return ug(e)?Ag(t)?Bl.before(t):Bl.after(t):Bl.before(t)},Lg=function(e,t){var n=t.nextSibling;return n&&Rg(n)?Eg(n)?Bl(n,0):Bl.before(n):Ig(Pl.Forwards,Bl.after(t),e)},Ig=function(e,t,n){var r,o,i,a;if(!_g(n)||!t)return null;if(t.isEqual(Bl.after(n))&&n.lastChild){if(a=Bl.after(n.lastChild),ug(e)&&Rg(n.lastChild)&&_g(n.lastChild))return Ag(n.lastChild)?Bl.before(n.lastChild):a}else a=t;var u=a.container(),c=a.offset();if(Eg(u)){if(ug(e)&&c>0)return Bl(u,--c);if(ag(e)&&c<u.length)return Bl(u,++c);r=u}else{if(ug(e)&&c>0&&(o=Bg(u,c-1),Rg(o)))return!Dg(o)&&(i=sg(o,e,Tg,o),i)?Eg(i)?Bl(i,i.data.length):Bl.after(i):Eg(o)?Bl(o,o.data.length):Bl.before(o);if(ag(e)&&c<u.childNodes.length&&(o=Bg(u,c),Rg(o)))return Ag(o)?Lg(n,o):!Dg(o)&&(i=sg(o,e,Tg,o),i)?Eg(i)?Bl(i,0):Bl.before(i):Eg(o)?Bl(o,0):Bl.after(o);r=o||a.getNode()}if((ag(e)&&a.isAtEnd()||ug(e)&&a.isAtStart())&&(r=sg(r,e,U,n,!0),Tg(r,n)))return Pg(e,r);o=sg(r,e,Tg,n);var s=Ve(ne(Og(u,n),Ng));return!s||o&&s.contains(o)?o?Pg(e,o):null:(a=ag(e)?Bl.after(s):Bl.before(s),a)},Mg=function(e){return{next:function(t){return Ig(Pl.Forwards,t,e)},prev:function(t){return Ig(Pl.Backwards,t,e)}}},Fg=function(e,t,n){var r=e?Bl.before(n):Bl.after(n);return $g(e,t,r)},Ug=function(e){return ro(e)?Bl.before(e):Bl.after(e)},zg=function(e){return Bl.isTextPosition(e)?0===e.offset():Ua(e.getNode())},Hg=function(e){if(Bl.isTextPosition(e)){var t=e.container();return e.offset()===t.data.length}return Ua(e.getNode(!0))},jg=function(e,t){return!Bl.isTextPosition(e)&&!Bl.isTextPosition(t)&&e.getNode()===t.getNode(!0)},Vg=function(e){return!Bl.isTextPosition(e)&&ro(e.getNode())},qg=function(e,t,n){return e?!jg(t,n)&&!Vg(t)&&Hg(t)&&zg(n):!jg(n,t)&&zg(t)&&Hg(n)},$g=function(e,t,n){var r=Mg(t);return q.from(e?r.next(n):r.prev(n))},Wg=function(e,t,n){return $g(e,t,n).bind((function(r){return lg(n,r,t)&&qg(e,n,r)?$g(e,t,r):q.some(r)}))},Kg=function(e,t,n,r){return Wg(e,t,n).bind((function(n){return r(n)?Kg(e,t,n,r):q.some(n)}))},Xg=function(e,t){var n=e?t.firstChild:t.lastChild;return Zr(n)?q.some(Bl(n,e?0:n.data.length)):n?Ua(n)?q.some(e?Bl.before(n):Ug(n)):Fg(e,t,n):q.none()},Yg=B($g,!0),Gg=B($g,!1),Jg=B(Xg,!0),Qg=B(Xg,!1),Zg="_mce_caret",ep=function(e){return Vr(e)&&e.id===Zg},tp=function(e,t){while(t&&t!==e){if(t.id===Zg)return t;t=t.parentNode}return null},np=function(e){return v(e.start)},rp=function(e){return Oe(e,"rng")},op=function(e){return Oe(e,"id")},ip=function(e){return Oe(e,"name")},ap=function(e){return gn.isArray(e.start)},up=function(e,t){return Vr(t)&&e.isBlock(t)&&!t.innerHTML&&!en.ie&&(t.innerHTML='<br data-mce-bogus="1" />'),t},cp=function(e,t){var n,r=e.createRng();return n=Ql(e.getRoot(),t.start),r.setStart(n.container(),n.offset()),n=Ql(e.getRoot(),t.end),r.setEnd(n.container(),n.offset()),r},sp=function(e,t){var n=e.ownerDocument.createTextNode(da);e.appendChild(n),t.setStart(n,0),t.setEnd(n,0)},fp=function(e){return!1===e.hasChildNodes()},lp=function(e,t){return Qg(e).fold(F,(function(e){return t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0}))},dp=function(e,t,n){return!(!fp(t)||!tp(e,t))&&(sp(t,n),!0)},mp=function(e,t,n,r){var o,i,a,u,c=n[t?"start":"end"],s=e.getRoot();if(c){for(a=c[0],i=s,o=c.length-1;o>=1;o--){if(u=i.childNodes,dp(s,i,r))return!0;if(c[o]>u.length-1)return!!dp(s,i,r)||lp(i,r);i=u[c[o]]}3===i.nodeType&&(a=Math.min(c[0],i.nodeValue.length)),1===i.nodeType&&(a=Math.min(c[0],i.childNodes.length)),t?r.setStart(i,a):r.setEnd(i,a)}return!0},gp=function(e){return Zr(e)&&e.data.length>0},pp=function(e,t,n){var r,o,i,a,u,c,s=e.get(n.id+"_"+t),f=n.keep;if(s){if(r=s.parentNode,"start"===t?(f?s.hasChildNodes()?(r=s.firstChild,o=1):gp(s.nextSibling)?(r=s.nextSibling,o=0):gp(s.previousSibling)?(r=s.previousSibling,o=s.previousSibling.data.length):(r=s.parentNode,o=e.nodeIndex(s)+1):o=e.nodeIndex(s),u=r,c=o):(f?s.hasChildNodes()?(r=s.firstChild,o=1):gp(s.previousSibling)?(r=s.previousSibling,o=s.previousSibling.data.length):(r=s.parentNode,o=e.nodeIndex(s)):o=e.nodeIndex(s),u=r,c=o),!f){a=s.previousSibling,i=s.nextSibling,gn.each(gn.grep(s.childNodes),(function(e){Zr(e)&&(e.nodeValue=e.nodeValue.replace(/\uFEFF/g,""))}));while(s=e.get(n.id+"_"+t))e.remove(s,!0);a&&i&&a.nodeType===i.nodeType&&Zr(a)&&!en.opera&&(o=a.nodeValue.length,a.appendData(i.nodeValue),e.remove(i),u=a,c=o)}return q.some(Bl(u,c))}return q.none()},hp=function(e,t){var n=e.createRng();return mp(e,!0,t,n)&&mp(e,!1,t,n)?q.some(n):q.none()},vp=function(e,t){var n=pp(e,"start",t),r=pp(e,"end",t);return lo(n,r.or(n),(function(t,n){var r=e.createRng();return r.setStart(up(e,t.container()),t.offset()),r.setEnd(up(e,n.container()),n.offset()),r}))},bp=function(e,t){return q.from(e.select(t.name)[t.index]).map((function(t){var n=e.createRng();return n.selectNode(t),n}))},yp=function(e,t){var n=e.dom;if(t){if(ap(t))return hp(n,t);if(np(t))return q.some(cp(n,t));if(op(t))return vp(n,t);if(ip(t))return bp(n,t);if(rp(t))return q.some(t.rng)}return q.none()},Cp=function(e,t,n){return md(e,t,n)},wp=function(e,t){yp(e,t).each((function(t){e.setRng(t)}))},xp=function(e){return Vr(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")},Sp=function(e){return function(t){return e===t}},kp=Sp(sa),Np=function(e){return""!==e&&-1!==" \f\n\r\t\v".indexOf(e)},Ep=function(e){return!Np(e)&&!kp(e)},_p=function(e){return!!e.nodeType},Ap=function(e){return e&&/^(IMG)$/.test(e.nodeName)},Rp=function(e,t,n){var r=n.startOffset,o=n.startContainer;if((o!==n.endContainer||!Ap(o.childNodes[r]))&&Vr(o)){var i=o.childNodes,a=void 0;r<i.length?(o=i[r],a=new zi(o,e.getParent(o,e.isBlock))):(o=i[i.length-1],a=new zi(o,e.getParent(o,e.isBlock)),a.next(!0));for(var u=a.current();u;u=a.next())if(Zr(u)&&!Bp(u))return n.setStart(u,0),void t.setRng(n)}},Dp=function(e,t,n){if(e){var r=t?"nextSibling":"previousSibling";for(e=n?e:e[r];e;e=e[r])if(Vr(e)||!Bp(e))return e}},Tp=function(e,t){return _p(t)&&(t=t.nodeName),!!e.schema.getTextBlockElements()[t.toLowerCase()]},Op=function(e,t,n){return e.schema.isValidChild(t,n)},Bp=function(e,t){if(void 0===t&&(t=!1),k(e)&&Zr(e)){var n=t?e.data.replace(/ /g," "):e.data;return Ka(n)}return!1},Pp=function(e){return k(e)&&Zr(e)&&0===e.length},Lp=function(e,t){return N(e)?e=e(t):k(t)&&(e=e.replace(/%(\w+)/g,(function(e,n){return t[n]||e}))),e},Ip=function(e,t){return e=e||"",t=t||"",e=""+(e.nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()},Mp=function(e,t,n){return"color"!==n&&"backgroundColor"!==n||(t=e.toHex(t)),"fontWeight"===n&&700===t&&(t="bold"),"fontFamily"===n&&(t=t.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),""+t},Fp=function(e,t,n){return Mp(e,e.getStyle(t,n),n)},Up=function(e,t){var n;return e.getParent(t,(function(t){return n=e.getStyle(t,"text-decoration"),n&&"none"!==n})),n},zp=function(e,t,n){return e.getParents(t,n,e.getRoot())},Hp=function(e,t){var n=function(e){var t=function(e){return e.length>1&&"%"===e.charAt(0)};return J(["styles","attributes"],(function(n){return Te(e,n).exists((function(e){var n=y(e)?e:De(e);return J(n,t)}))}))};return J(e.formatter.get(t),n)},jp=function(e,t,n){var r=["inline","block","selector","attributes","styles","classes"],o=function(e){return Ae(e,(function(e,t){return J(r,(function(e){return e===t}))}))};return J(e.formatter.get(t),(function(t){var r=o(t);return J(e.formatter.get(n),(function(e){var t=o(e);return Pe(r,t)}))}))},Vp=function(e){return Be(e,"block")},qp=function(e){return Be(e,"selector")},$p=function(e){return Be(e,"inline")},Wp=function(e){return qp(e)&&$p(e)&&so(Te(e,"mixed"),!0)},Kp=function(e){return qp(e)&&!1!==e.expand&&!$p(e)},Xp=xp,Yp=zp,Gp=Bp,Jp=Tp,Qp=function(e){return ro(e)&&e.getAttribute("data-mce-bogus")&&!e.nextSibling},Zp=function(e,t){var n=t;while(n){if(Vr(n)&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},eh=function(e,t,n,r){for(var o=t.data,i=n;e?i>=0:i<o.length;e?i--:i++)if(r(o.charAt(i)))return e?i+1:i;return-1},th=function(e,t,n){return eh(e,t,n,(function(e){return kp(e)||Np(e)}))},nh=function(e,t,n){return eh(e,t,n,Ep)},rh=function(e,t,n,r,o,i){var a,u=e.getParent(n,e.isBlock)||t,c=function(t,n,r){var i=Gf(e),c=o?i.backwards:i.forwards;return q.from(c(t,n,(function(e,t){return Xp(e.parentNode)?-1:(a=e,r(o,e,t))}),u))},s=c(n,r,th);return s.bind((function(e){return i?c(e.container,e.offset+(o?-1:0),nh):q.some(e)})).orThunk((function(){return a?q.some({container:a,offset:o?0:a.length}):q.none()}))},oh=function(e,t,n,r,o){Zr(r)&&pt(r.data)&&r[o]&&(r=r[o]);for(var i=Yp(e,r),a=0;a<i.length;a++)for(var u=0;u<t.length;u++){var c=t[u];if((!k(c.collapsed)||c.collapsed===n.collapsed)&&(qp(c)&&e.is(i[a],c.selector)))return i[a]}return r},ih=function(e,t,n,r){var o=n,i=e.dom,a=i.getRoot(),u=t[0];if(Vp(u)&&(o=u.wrapper?null:i.getParent(n,u.block,a)),!o){var c=i.getParent(n,"LI,TD,TH");o=i.getParent(Zr(n)?n.parentNode:n,(function(t){return t!==a&&Jp(e,t)}),c)}if(o&&Vp(u)&&u.wrapper&&(o=Yp(i,o,"ul,ol").reverse()[0]||o),!o){o=n;while(o[r]&&!i.isBlock(o[r]))if(o=o[r],Ip(o,"br"))break}return o||n},ah=function(e,t,n,r){var o=n.parentNode;return!k(n[r])&&(!(o!==t&&!S(o)&&!e.isBlock(o))||ah(e,t,o,r))},uh=function(e,t,n,r,o){var i=n,a=o?"previousSibling":"nextSibling",u=e.getRoot();if(Zr(n)&&!Gp(n)&&(o?r>0:r<n.data.length))return n;while(1){if(!t[0].block_expand&&e.isBlock(i))return i;for(var c=i[a];c;c=c[a]){var s=Zr(c)&&!ah(e,u,c,a);if(!Xp(c)&&!Qp(c)&&!Gp(c,s))return i}if(i===u||i.parentNode===u){n=i;break}i=i.parentNode}return n},ch=function(e){return Xp(e.parentNode)||Xp(e)},sh=function(e,t,n,r){void 0===r&&(r=!1);var o=t.startContainer,i=t.startOffset,a=t.endContainer,u=t.endOffset,c=e.dom,s=n[0];if(Vr(o)&&o.hasChildNodes()&&(o=ul(o,i),Zr(o)&&(i=0)),Vr(a)&&a.hasChildNodes()&&(a=ul(a,t.collapsed?u:u-1),Zr(a)&&(u=a.nodeValue.length)),o=Zp(c,o),a=Zp(c,a),ch(o)&&(o=Xp(o)?o:o.parentNode,o=t.collapsed?o.previousSibling||o:o.nextSibling||o,Zr(o)&&(i=t.collapsed?o.length:0)),ch(a)&&(a=Xp(a)?a:a.parentNode,a=t.collapsed?a.nextSibling||a:a.previousSibling||a,Zr(a)&&(u=t.collapsed?0:a.length)),t.collapsed){var f=rh(c,e.getBody(),o,i,!0,r);f.each((function(e){var t=e.container,n=e.offset;o=t,i=n}));var l=rh(c,e.getBody(),a,u,!1,r);l.each((function(e){var t=e.container,n=e.offset;a=t,u=n}))}return($p(s)||s.block_expand)&&($p(s)&&Zr(o)&&0!==i||(o=uh(c,n,o,i,!0)),$p(s)&&Zr(a)&&u!==a.nodeValue.length||(a=uh(c,n,a,u,!1))),Kp(s)&&(o=oh(c,n,t,o,"previousSibling"),a=oh(c,n,t,a,"nextSibling")),(Vp(s)||qp(s))&&(o=ih(e,n,o,"previousSibling"),a=ih(e,n,a,"nextSibling"),Vp(s)&&(c.isBlock(o)||(o=uh(c,n,o,i,!0)),c.isBlock(a)||(a=uh(c,n,a,u,!1)))),Vr(o)&&(i=c.nodeIndex(o),o=o.parentNode),Vr(a)&&(u=c.nodeIndex(a)+1,a=a.parentNode),{startContainer:o,startOffset:i,endContainer:a,endOffset:u}},fh=function(e,t,n){var r=t.startOffset,o=ul(t.startContainer,r),i=t.endOffset,a=ul(t.endContainer,i-1),u=function(e){var t=e[0];Zr(t)&&t===o&&r>=t.data.length&&e.splice(0,1);var n=e[e.length-1];return 0===i&&e.length>0&&n===a&&Zr(n)&&e.splice(e.length-1,1),e},c=function(e,t,n){for(var r=[];e&&e!==n;e=e[t])r.push(e);return r},s=function(t,n){return e.getParent(t,(function(e){return e.parentNode===n}),n)},f=function(e,t,r){for(var o=r?"nextSibling":"previousSibling",i=e,a=i.parentNode;i&&i!==t;i=a){a=i.parentNode;var s=c(i===e?i:i[o],o);s.length&&(r||s.reverse(),n(u(s)))}};if(o===a)return n(u([o]));var l=e.findCommonAncestor(o,a);if(e.isChildOf(o,a))return f(o,l,!0);if(e.isChildOf(a,o))return f(a,l);var d=s(o,l)||o,m=s(a,l)||a;f(o,d,!0);var g=c(d===o?d:d.nextSibling,"nextSibling",m===a?m.nextSibling:m);g.length&&n(u(g)),f(a,m)},lh=function(e){var t=[];if(e)for(var n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},dh=function(e){return se(e,(function(e){var t=al(e);return t?[Cn.fromDom(t)]:[]}))},mh=function(e){return lh(e).length>1},gh=function(e){return ne(dh(e),ia)},ph=function(e){return Af(e,"td[data-mce-selected],th[data-mce-selected]")},hh=function(e,t){var n=ph(t);return n.length>0?n:gh(e)},vh=function(e){return hh(lh(e.selection.getSel()),Cn.fromDom(e.getBody()))},bh=function(e,t){return Ei(e,"table",t)},yh=function(e){var t=e.startContainer,n=e.startOffset;return Zr(t)?0===n?q.some(Cn.fromDom(t)):q.none():q.from(t.childNodes[n]).map(Cn.fromDom)},Ch=function(e){var t=e.endContainer,n=e.endOffset;return Zr(t)?n===t.data.length?q.some(Cn.fromDom(t)):q.none():q.from(t.childNodes[n-1]).map(Cn.fromDom)},wh=function(e){return ir(e).fold(D([e]),(function(t){return[e].concat(wh(t))}))},xh=function(e){return ar(e).fold(D([e]),(function(t){return"br"===Mn(t)?Zn(t).map((function(t){return[e].concat(xh(t))})).getOr([]):[e].concat(xh(t))}))},Sh=function(e,t){return lo(yh(t),Ch(t),(function(t,n){var r=ae(wh(e),B(Bn,t)),o=ae(xh(e),B(Bn,n));return r.isSome()&&o.isSome()})).getOr(!1)},kh=function(e,t,n,r){var o=n,i=new zi(n,o),a=Ae(e.schema.getMoveCaretBeforeOnEnterElements(),(function(e,t){return!G(["td","th","table"],t.toLowerCase())}));do{if(Zr(n)&&0!==gn.trim(n.nodeValue).length)return void(r?t.setStart(n,0):t.setEnd(n,n.nodeValue.length));if(a[n.nodeName])return void(r?t.setStartBefore(n):"BR"===n.nodeName?t.setEndBefore(n):t.setEndAfter(n))}while(n=r?i.next():i.prev());"BODY"===o.nodeName&&(r?t.setStart(o,0):t.setEnd(o,o.childNodes.length))},Nh=function(e){var t=e.selection.getSel();return t&&t.rangeCount>0},Eh=function(e,t){var n=vh(e);n.length>0?Z(n,(function(n){var r=n.dom,o=e.dom.createRng();o.setStartBefore(r),o.setEndAfter(r),t(o,!0)})):t(e.selection.getRng(),!1)},_h=function(e,t,n){var r=dd(e,t);n(r),e.moveToBookmark(r)},Ah=function(e,t){var n=function(n){if(!e(n))throw new Error("Can only get "+t+" value of a "+t+" node");return r(n).getOr("")},r=function(t){return e(t)?q.from(t.dom.nodeValue):q.none()},o=function(n,r){if(!e(n))throw new Error("Can only set raw "+t+" value of a "+t+" node");n.dom.nodeValue=r};return{get:n,getOption:r,set:o}},Rh=Ah(jn,"text"),Dh=function(e){return Rh.get(e)},Th=function(e){return jn(e)&&Dh(e)===da},Oh=function(e,t,n,r){return Gn(t).fold((function(){return"skipping"}),(function(o){return"br"===r||Th(t)?"valid":Bf(t)?"existing":ep(t.dom)?"caret":Op(e,n,r)&&Op(e,Mn(o),n)?"valid":"invalid-child"}))},Bh=function(e,t){var n=sh(e,t,[{inline:"span"}]);t.setStart(n.startContainer,n.startOffset),t.setEnd(n.endContainer,n.endOffset),e.selection.setRng(t)},Ph=function(e,t,n,r){var o=t.uid,i=void 0===o?zf("mce-annotation"):o,a=$e(t,["uid"]),u=Cn.fromTag("span",e);Sf(u,Rf()),vo(u,""+Tf(),i),vo(u,""+Df(),n);var c=r(i,a),s=c.attributes,f=void 0===s?{}:s,l=c.classes,d=void 0===l?[]:l;return bo(u,f),Hf(u,d),u},Lh=function(e,t,n,r,o){var i=[],a=Ph(e.getDoc(),o,n,r),u=mf(),c=function(){u.clear()},s=function(){return u.get().getOrThunk((function(){var e=Kf(a);return i.push(e),u.set(e),e}))},f=function(e){Z(e,l)},l=function(t){var n=Oh(e,t,"span",Mn(t));switch(n){case"invalid-child":c();var r=rr(t);f(r),c();break;case"valid":var o=s();xr(t,o);break}},d=function(e){var t=Q(e,Cn.fromDom);f(t)};return fh(e.dom,t,(function(e){c(),d(e)})),i},Ih=function(e,t,n,r){e.undoManager.transact((function(){var o=e.selection,i=o.getRng(),a=vh(e).length>0;if(i.collapsed&&!a&&Bh(e,i),o.getRng().collapsed&&!a){var u=Ph(e.getDoc(),r,t,n.decorate);$f(u,sa),o.getRng().insertNode(u.dom),o.select(u.dom)}else _h(o,!1,(function(){Eh(e,(function(o){Lh(e,o,t,n.decorate,r)}))}))}))},Mh=function(e){var t=Ff();Mf(e,t);var n=If(e);return{register:function(e,n){t.register(e,n)},annotate:function(n,r){t.lookup(n).each((function(t){Ih(e,n,t,r)}))},annotationChanged:function(e,t){n.addListener(e,t)},remove:function(t){Of(e,q.some(t)).each((function(e){var t=e.elements;Z(t,_r)}))},getAll:function(t){var n=Lf(e,t);return Se(n,(function(e){return Q(e,(function(e){return e.dom}))}))}}},Fh=function(e){return{getBookmark:B(Cp,e),moveToBookmark:B(wp,e)}};Fh.isBookmarkNode=xp;var Uh=function(e,t){while(t&&t!==e){if(io(t)||ao(t))return t;t=t.parentNode}return null},zh=function(e,t,n){if(n.collapsed)return!1;if(en.browser.isIE()&&n.startOffset===n.endOffset-1&&n.startContainer===n.endContainer){var r=n.startContainer.childNodes[n.startOffset];if(Vr(r))return J(r.getClientRects(),(function(n){return ol(n,e,t)}))}return J(n.getClientRects(),(function(n){return ol(n,e,t)}))},Hh=function(e,t){return e.fire("PreProcess",t)},jh=function(e,t){return e.fire("PostProcess",t)},Vh=function(e){return e.fire("remove")},qh=function(e){return e.fire("detach")},$h=function(e,t){return e.fire("SwitchMode",{mode:t})},Wh=function(e,t,n,r,o){e.fire("ObjectResizeStart",{target:t,width:n,height:r,origin:o})},Kh=function(e,t,n,r,o){e.fire("ObjectResized",{target:t,width:n,height:r,origin:o})},Xh=function(e){return e.fire("PreInit")},Yh=function(e){return e.fire("PostRender")},Gh=function(e){return e.fire("Init")},Jh=function(e,t){return e.fire("PlaceholderToggle",{state:t})},Qh=function(e,t,n){return e.fire(t,n)},Zh=function(e,t,n,r){return e.fire("FormatApply",{format:t,node:n,vars:r})},ev=function(e,t,n,r){return e.fire("FormatRemove",{format:t,node:n,vars:r})},tv={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,ESC:27,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,modifierPressed:function(e){return e.shiftKey||e.ctrlKey||e.altKey||tv.metaKeyPressed(e)},metaKeyPressed:function(e){return en.mac?e.metaKey:e.ctrlKey&&!e.altKey}},nv=ao,rv=function(e,t){var n,r,o,i,a,u,c,s,f,l,d,m,g,p,h,v,b,y="data-mce-selected",C=t.dom,w=gn.each,x=t.getDoc(),S=document,N=Math.abs,E=Math.round,_=t.getBody(),A={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]},R=function(e){return e&&("IMG"===e.nodeName||t.dom.is(e,"figure.image"))},D=function(e){return co(e)||C.hasClass(e,"mce-preview-object")},T=function(e,t){if("longpress"===e.type||0===e.type.indexOf("touch")){var n=e.touches[0];return R(e.target)&&!zh(n.clientX,n.clientY,t)}return R(e.target)&&!zh(e.clientX,e.clientY,t)},O=function(e){var n=e.target;T(e,t.selection.getRng())&&!e.isDefaultPrevented()&&t.selection.select(n)},B=function(e){return C.is(e,"figure.image")?[e.querySelector("img")]:C.hasClass(e,"mce-preview-object")&&k(e.firstElementChild)?[e,e.firstElementChild]:[e]},P=function(e){var n=Zd(t);return!!n&&("false"!==e.getAttribute("data-mce-resize")&&(e!==t.getBody()&&(C.hasClass(e,"mce-preview-object")?Rn(Cn.fromDom(e.firstElementChild),n):Rn(Cn.fromDom(e),n))))},L=function(e){return D(e)?C.create("img",{src:en.transparentSrc}):e.cloneNode(!0)},I=function(e,n,r){if(k(r)){var o=B(e);Z(o,(function(e){e.style[n]||!t.schema.isValid(e.nodeName.toLowerCase(),n)?C.setStyle(e,n,r):C.setAttrib(e,n,""+r)}))}},M=function(e,t,n){I(e,"width",t),I(e,"height",n)},F=function(e){var a,y,w,x,S;a=e.screenX-u,y=e.screenY-c,p=a*i[2]+l,h=y*i[3]+d,p=p<5?5:p,h=h<5?5:h,w=(R(n)||D(n))&&!1!==em(t)?!tv.modifierPressed(e):tv.modifierPressed(e),w&&(N(a)>N(y)?(h=E(p*m),p=E(h/m)):(p=E(h/m),h=E(p*m))),M(r,p,h),x=i.startPos.x+a,S=i.startPos.y+y,x=x>0?x:0,S=S>0?S:0,C.setStyles(o,{left:x,top:S,display:"block"}),o.innerHTML=p+" &times; "+h,i[2]<0&&r.clientWidth<=p&&C.setStyle(r,"left",s+(l-p)),i[3]<0&&r.clientHeight<=h&&C.setStyle(r,"top",f+(d-h)),a=_.scrollWidth-v,y=_.scrollHeight-b,a+y!==0&&C.setStyles(o,{left:x-a,top:S-y}),g||(Wh(t,n,l,d,"corner-"+i.name),g=!0)},U=function(){var e=g;g=!1,e&&(I(n,"width",p),I(n,"height",h)),C.unbind(x,"mousemove",F),C.unbind(x,"mouseup",U),S!==x&&(C.unbind(S,"mousemove",F),C.unbind(S,"mouseup",U)),C.remove(r),C.remove(o),C.remove(a),z(n),e&&(Kh(t,n,p,h,"corner-"+i.name),C.setAttrib(n,"style",C.getAttrib(n,"style"))),t.nodeChanged()},z=function(e){q();var s=C.getPos(e,_),f=s.x,g=s.y,k=e.getBoundingClientRect(),N=k.width||k.right-k.left,E=k.height||k.bottom-k.top;n!==e&&(H(),n=e,p=h=0);var R=t.fire("ObjectSelected",{target:e}),D=C.getAttrib(n,y,"1");P(e)&&!R.isDefaultPrevented()?w(A,(function(e,t){var s,p=function(s){var p=B(n)[0];u=s.screenX,c=s.screenY,l=p.clientWidth,d=p.clientHeight,m=d/l,i=e,i.name=t,i.startPos={x:N*e[0]+f,y:E*e[1]+g},v=_.scrollWidth,b=_.scrollHeight,a=C.add(_,"div",{class:"mce-resize-backdrop","data-mce-bogus":"all"}),C.setStyles(a,{position:"fixed",left:"0",top:"0",width:"100%",height:"100%"}),r=L(n),C.addClass(r,"mce-clonedresizable"),C.setAttrib(r,"data-mce-bogus","all"),r.contentEditable="false",C.setStyles(r,{left:f,top:g,margin:0}),M(r,N,E),r.removeAttribute(y),_.appendChild(r),C.bind(x,"mousemove",F),C.bind(x,"mouseup",U),S!==x&&(C.bind(S,"mousemove",F),C.bind(S,"mouseup",U)),o=C.add(_,"div",{class:"mce-resize-helper","data-mce-bogus":"all"},l+" &times; "+d)};s=C.get("mceResizeHandle"+t),s&&C.remove(s),s=C.add(_,"div",{id:"mceResizeHandle"+t,"data-mce-bogus":"all",class:"mce-resizehandle",unselectable:!0,style:"cursor:"+t+"-resize; margin:0; padding:0"}),11===en.ie&&(s.contentEditable=!1),C.bind(s,"mousedown",(function(e){e.stopImmediatePropagation(),e.preventDefault(),p(e)})),e.elm=s,C.setStyles(s,{left:N*e[0]+f-s.offsetWidth/2,top:E*e[1]+g-s.offsetHeight/2})})):H(),C.getAttrib(n,y)||n.setAttribute(y,D)},H=function(){q(),n&&n.removeAttribute(y),xe(A,(function(e,t){var n=C.get("mceResizeHandle"+t);n&&(C.unbind(n),C.remove(n))}))},j=function(n){var r,o,i=function(e,t){if(e)do{if(e===t)return!0}while(e=e.parentNode)};g||t.removed||(w(C.select("img[data-mce-selected],hr[data-mce-selected]"),(function(e){e.removeAttribute(y)})),o="mousedown"===n.type?n.target:e.getNode(),o=C.$(o).closest("table,img,figure.image,hr,video,span.mce-preview-object")[0],i(o,_)&&($(),r=e.getStart(!0),i(r,o)&&i(e.getEnd(!0),o))?z(o):H())},V=function(e){return nv(Uh(t.getBody(),e))},q=function(){xe(A,(function(e){e.elm&&(C.unbind(e.elm),delete e.elm)}))},$=function(){try{t.getDoc().execCommand("enableObjectResizing",!1,"false")}catch(e){}};t.on("init",(function(){if($(),en.browser.isIE()||en.browser.isEdge()){t.on("mousedown click",(function(e){var n=e.target,r=n.nodeName;g||!/^(TABLE|IMG|HR)$/.test(r)||V(n)||(2!==e.button&&t.selection.select(n,"TABLE"===r),"mousedown"===e.type&&t.nodeChanged())}));var e=function(e){var n=function(e){Ii.setEditorTimeout(t,(function(){return t.selection.select(e)}))};if(V(e.target)||co(e.target))return e.preventDefault(),void n(e.target);/^(TABLE|IMG|HR)$/.test(e.target.nodeName)&&(e.preventDefault(),"IMG"===e.target.tagName&&n(e.target))};C.bind(_,"mscontrolselect",e),t.on("remove",(function(){return C.unbind(_,"mscontrolselect",e)}))}var r=Ii.throttle((function(e){t.composing||j(e)}));t.on("NodeChange ResizeEditor ResizeWindow ResizeContent drop",r),t.on("keyup compositionend",(function(e){n&&"TABLE"===n.nodeName&&r(e)})),t.on("hide blur",H),t.on("contextmenu longpress",O,!0)})),t.on("remove",q);var W=function(){n=r=a=null};return{isResizable:P,showResizeRect:z,hideResizeRect:H,updateResizeRect:j,destroy:W}},ov=function(e){return io(e)||ao(e)},iv=function(e,t,n){while(e&&e!==t){if(n(e))return e;e=e.parentNode}return null},av=function(e,t,n){var r,o=n.elementFromPoint(e,t),i=n.body.createTextRange();if(o&&"HTML"!==o.tagName||(o=n.body),i.moveToElementText(o),r=gn.toArray(i.getClientRects()),r=r.sort((function(e,n){return e=Math.abs(Math.max(e.top-t,e.bottom-t)),n=Math.abs(Math.max(n.top-t,n.bottom-t)),e-n})),r.length>0){t=(r[0].bottom+r[0].top)/2;try{return i.moveToPoint(e,t),i.collapse(!0),i}catch(a){}}return null},uv=function(e,t){var n=e&&e.parentElement?e.parentElement():null;return ao(iv(n,t,ov))?null:e},cv=function(e,t,n){var r,o,i=n;if(i.caretPositionFromPoint)o=i.caretPositionFromPoint(e,t),o&&(r=n.createRange(),r.setStart(o.offsetNode,o.offset),r.collapse(!0));else if(i.caretRangeFromPoint)r=i.caretRangeFromPoint(e,t);else if(i.body.createTextRange){r=i.body.createTextRange();try{r.moveToPoint(e,t),r.collapse(!0)}catch(a){r=av(e,t,n)}return uv(r,n.body)}return r},sv=function(e,t){return e&&t&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset},fv=function(e,t,n){while(e&&e!==t){if(n(e))return e;e=e.parentNode}return null},lv=function(e,t,n){return null!==fv(e,t,n)},dv=function(e,t,n){return lv(e,t,(function(e){return e.nodeName===n}))},mv=function(e){return e&&"TABLE"===e.nodeName},gv=function(e){return e&&/^(TD|TH|CAPTION)$/.test(e.nodeName)},pv=function(e,t){return ya(e)&&!1===lv(e,t,ep)},hv=function(e,t,n){var r=new zi(t,e.getParent(t.parentNode,e.isBlock)||e.getRoot());while(t=r[n?"prev":"next"]())if(ro(t))return!0},vv=function(e,t){return e.previousSibling&&e.previousSibling.nodeName===t},bv=function(e,t){while(t&&t!==e){if(ao(t))return!0;t=t.parentNode}return!1},yv=function(e,t,n,r,o){var i,a,u=e.getRoot(),c=e.schema.getNonEmptyElements(),s=e.getParent(o.parentNode,e.isBlock)||u;if(r&&ro(o)&&t&&e.isEmpty(s))return q.some(Bl(o.parentNode,e.nodeIndex(o)));var f=new zi(o,s);while(a=f[r?"prev":"next"]()){if("false"===e.getContentEditableParent(a)||pv(a,u))return q.none();if(Zr(a)&&a.nodeValue.length>0)return!1===dv(a,u,"A")?q.some(Bl(a,r?a.nodeValue.length:0)):q.none();if(e.isBlock(a)||c[a.nodeName.toLowerCase()])return q.none();i=a}return n&&i?q.some(Bl(i,0)):q.none()},Cv=function(e,t,n,r){var o,i,a,u,c=e.getRoot(),s=!1;o=r[(n?"start":"end")+"Container"],i=r[(n?"start":"end")+"Offset"];var f=Vr(o)&&i===o.childNodes.length,l=e.schema.getNonEmptyElements();if(u=n,ya(o))return q.none();if(Vr(o)&&i>o.childNodes.length-1&&(u=!1),to(o)&&(o=c,i=0),o===c){if(u&&(a=o.childNodes[i>0?i-1:0],a)){if(ya(a))return q.none();if(l[a.nodeName]||mv(a))return q.none()}if(o.hasChildNodes()){if(i=Math.min(!u&&i>0?i-1:i,o.childNodes.length-1),o=o.childNodes[i],i=Zr(o)&&f?o.data.length:0,!t&&o===c.lastChild&&mv(o))return q.none();if(bv(c,o)||ya(o))return q.none();if(o.hasChildNodes()&&!1===mv(o)){a=o;var d=new zi(o,c);do{if(ao(a)||ya(a)){s=!1;break}if(Zr(a)&&a.nodeValue.length>0){i=u?0:a.nodeValue.length,o=a,s=!0;break}if(l[a.nodeName.toLowerCase()]&&!gv(a)){i=e.nodeIndex(a),o=a.parentNode,u||i++,s=!0;break}}while(a=u?d.next():d.prev())}}}return t&&(Zr(o)&&0===i&&yv(e,f,t,!0,o).each((function(e){o=e.container(),i=e.offset(),s=!0})),Vr(o)&&(a=o.childNodes[i],a||(a=o.childNodes[i-1]),!a||!ro(a)||vv(a,"A")||hv(e,a,!1)||hv(e,a,!0)||yv(e,f,t,!0,a).each((function(e){o=e.container(),i=e.offset(),s=!0})))),u&&!t&&Zr(o)&&i===o.nodeValue.length&&yv(e,f,t,!1,o).each((function(e){o=e.container(),i=e.offset(),s=!0})),s?q.some(Bl(o,i)):q.none()},wv=function(e,t){var n=t.collapsed,r=t.cloneRange(),o=Bl.fromRangeStart(t);return Cv(e,n,!0,r).each((function(e){n&&Bl.isAbove(o,e)||r.setStart(e.container(),e.offset())})),n||Cv(e,n,!1,r).each((function(e){r.setEnd(e.container(),e.offset())})),n&&r.collapse(!0),sv(t,r)?q.none():q.some(r)},xv=function(e,t){return e.splitText(t)},Sv=function(e){var t=e.startContainer,n=e.startOffset,r=e.endContainer,o=e.endOffset;return t===r&&Zr(t)?n>0&&n<t.nodeValue.length&&(r=xv(t,n),t=r.previousSibling,o>n?(o-=n,t=r=xv(r,o).previousSibling,o=r.nodeValue.length,n=0):o=0):(Zr(t)&&n>0&&n<t.nodeValue.length&&(t=xv(t,n),n=0),Zr(r)&&o>0&&o<r.nodeValue.length&&(r=xv(r,o).previousSibling,o=r.nodeValue.length)),{startContainer:t,startOffset:n,endContainer:r,endOffset:o}},kv=function(e){var t=function(t,n){return fh(e,t,n)},n=Sv,r=function(t){return wv(e,t).fold(F,(function(e){return t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0}))};return{walk:t,split:n,normalize:r}};kv.compareRanges=sv,kv.getCaretRangeFromPoint=cv,kv.getSelectedNode=al,kv.getNode=ul;var Nv,Ev=function(e,t){var n=function(t,n){if(!E(n)&&!n.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+n);var r=t.dom;po(r)&&(r.style[e]=n+"px")},r=function(n){var r=t(n);if(r<=0||null===r){var o=Eo(n,e);return parseFloat(o)||0}return r},o=r,i=function(e,t){return oe(t,(function(t,n){var r=Eo(e,n),o=void 0===r?0:parseInt(r,10);return isNaN(o)?t:t+o}),0)},a=function(e,t,n){var r=i(e,n),o=t>r?t-r:0;return o};return{set:n,get:r,getOuter:o,aggregate:i,max:a}},_v=Ev("height",(function(e){var t=e.dom;return Ar(e)?t.getBoundingClientRect().height:t.offsetHeight})),Av=function(e){return _v.get(e)},Rv=function(e,t){var n=e.view(t);return n.fold(D([]),(function(t){var n=e.owner(t),r=Rv(e,n);return[t].concat(r)}))},Dv=function(e,t){var n=t.owner(e);return Rv(t,n)},Tv=function(e){var t,n=e.dom===document?q.none():q.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement);return n.map(Cn.fromDom)},Ov=function(e){return Kn(e)},Bv=Object.freeze({__proto__:null,view:Tv,owner:Ov}),Pv=function(e){var t=Cn.fromDom(document),n=Lr(t),r=Dv(e,Bv),o=Pr(e),i=re(r,(function(e,t){var n=Pr(t);return{left:e.left+n.left,top:e.top+n.top}}),{left:0,top:0});return Dr(i.left+o.left+n.left,i.top+o.top+n.top)},Lv=function(e){return"textarea"===Mn(e)},Iv=function(e,t){var n=e.fire("ScrollIntoView",t);return n.isDefaultPrevented()},Mv=function(e,t){e.fire("AfterScrollIntoView",t)},Fv=function(e,t){var n=rr(e);if(0===n.length||Lv(e))return{element:e,offset:t};if(t<n.length&&!Lv(n[t]))return{element:n[t],offset:0};var r=n[n.length-1];return Lv(r)?{element:e,offset:t}:"img"===Mn(r)?{element:r,offset:1}:jn(r)?{element:r,offset:Dh(r).length}:{element:r,offset:rr(r).length}},Uv=function(e,t){var n=Br(e),r=Av(e);return{element:e,bottom:n.top+r,height:r,pos:n,cleanup:t}},zv=function(e,t){var n=Fv(e,t),r=Cn.fromHtml('<span data-mce-bogus="all" style="display: inline-block;">'+da+"</span>");return br(n.element,r),Uv(r,(function(){return Er(r)}))},Hv=function(e){return Uv(Cn.fromDom(e),_)},jv=function(e,t,n,r){Wv(e,(function(o,i){return qv(e,t,n,r)}),n)},Vv=function(e,t,n,r,o){var i={elm:r.element.dom,alignToTop:o};if(!Iv(e,i)){var a=Lr(t).top;n(t,a,r,o),Mv(e,i)}},qv=function(e,t,n,r){var o=Cn.fromDom(e.getBody()),i=Cn.fromDom(e.getDoc());Do(o);var a=zv(Cn.fromDom(n.startContainer),n.startOffset);Vv(e,i,t,a,r),a.cleanup()},$v=function(e,t,n,r){var o=Cn.fromDom(e.getDoc());Vv(e,o,n,Hv(t),r)},Wv=function(e,t,n){var r=n.startContainer,o=n.startOffset,i=n.endContainer,a=n.endOffset;t(Cn.fromDom(r),Cn.fromDom(i));var u=e.dom.createRng();u.setStart(r,o),u.setEnd(i,a),e.selection.setRng(n)},Kv=function(e,t,n,r){var o=e.pos;if(n)Ir(o.left,o.top,r);else{var i=o.top-t+e.height;Ir(o.left,i,r)}},Xv=function(e,t,n,r,o){var i=n+t,a=r.pos.top,u=r.bottom,c=u-a>=n;if(a<t)Kv(r,n,!1!==o,e);else if(a>i){var s=c?!1!==o:!0===o;Kv(r,n,s,e)}else u>i&&!c&&Kv(r,n,!0===o,e)},Yv=function(e,t,n,r){var o=e.dom.defaultView.innerHeight;Xv(e,t,o,n,r)},Gv=function(e,t,n,r){var o=e.dom.defaultView.innerHeight;Xv(e,t,o,n,r);var i=Pv(n.element),a=zr(window);i.top<a.y?Mr(n.element,!1!==r):i.top>a.bottom&&Mr(n.element,!0===r)},Jv=function(e,t,n){return jv(e,Yv,t,n)},Qv=function(e,t,n){return $v(e,t,Yv,n)},Zv=function(e,t,n){return jv(e,Gv,t,n)},eb=function(e,t,n){return $v(e,t,Gv,n)},tb=function(e,t,n){var r=e.inline?Qv:eb;r(e,t,n)},nb=function(e,t,n){var r=e.inline?Jv:Zv;r(e,t,n)},rb=function(){return Cn.fromDom(document)},ob=function(e){return e.dom.focus()},ib=function(e){var t=dr(e).dom;return e.dom===t.activeElement},ab=function(e){return void 0===e&&(e=rb()),q.from(e.dom.activeElement).map(Cn.fromDom)},ub=function(e){return ab(dr(e)).filter((function(t){return e.dom.contains(t.dom)}))},cb=function(e,t,n,r){return{start:e,soffset:t,finish:n,foffset:r}},sb={create:cb},fb=ei.generate([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),lb=function(e,t,n,r){return e.fold(t,n,r)},db=function(e){return e.fold(T,T,T)},mb=fb.before,gb=fb.on,pb=fb.after,hb={before:mb,on:gb,after:pb,cata:lb,getStart:db},vb=ei.generate([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),bb=function(e){return vb.exact(e.start,e.soffset,e.finish,e.foffset)},yb=function(e){return e.match({domRange:function(e){return Cn.fromDom(e.startContainer)},relative:function(e,t){return hb.getStart(e)},exact:function(e,t,n,r){return e}})},Cb=vb.domRange,wb=vb.relative,xb=vb.exact,Sb=function(e){var t=yb(e);return Yn(t)},kb=sb.create,Nb={domRange:Cb,relative:wb,exact:xb,exactFromRange:bb,getWin:Sb,range:kb},Eb=$t().browser,_b=function(e,t){var n=jn(t)?Dh(t).length:rr(t).length+1;return e>n?n:e<0?0:e},Ab=function(e){return Nb.range(e.start,_b(e.soffset,e.start),e.finish,_b(e.foffset,e.finish))},Rb=function(e,t){return!jr(t.dom)&&(In(e,t)||Bn(e,t))},Db=function(e){return function(t){return Rb(e,t.start)&&Rb(e,t.finish)}},Tb=function(e){return!0===e.inline||Eb.isIE()},Ob=function(e){return Nb.range(Cn.fromDom(e.startContainer),e.startOffset,Cn.fromDom(e.endContainer),e.endOffset)},Bb=function(e){var t=e.getSelection(),n=t&&0!==t.rangeCount?q.from(t.getRangeAt(0)):q.none();return n.map(Ob)},Pb=function(e){var t=Yn(e);return Bb(t.dom).filter(Db(e))},Lb=function(e,t){return q.from(t).filter(Db(e)).map(Ab)},Ib=function(e){var t=document.createRange();try{return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),q.some(t)}catch(n){return q.none()}},Mb=function(e){var t=Tb(e)?Pb(Cn.fromDom(e.getBody())):q.none();e.bookmark=t.isSome()?t:e.bookmark},Fb=function(e,t){var n=Cn.fromDom(e.getBody()),r=Tb(e)?q.from(t):q.none(),o=r.map(Ob).filter(Db(n));e.bookmark=o.isSome()?o:e.bookmark},Ub=function(e){var t=e.bookmark?e.bookmark:q.none();return t.bind((function(t){return Lb(Cn.fromDom(e.getBody()),t)})).bind(Ib)},zb=function(e){Ub(e).each((function(t){return e.selection.setRng(t)}))},Hb=function(e){var t=e.className.toString();return-1!==t.indexOf("tox-")||-1!==t.indexOf("mce-")},jb={isEditorUIElement:Hb},Vb=function(e){return"nodechange"===e.type&&e.selectionChange},qb=function(e,t){var n=function(){t.throttle()};Hs.DOM.bind(document,"mouseup",n),e.on("remove",(function(){Hs.DOM.unbind(document,"mouseup",n)}))},$b=function(e){e.on("focusout",(function(){Mb(e)}))},Wb=function(e,t){e.on("mouseup touchend",(function(e){t.throttle()}))},Kb=function(e,t){var n=$t().browser;n.isIE()?$b(e):Wb(e,t),e.on("keyup NodeChange",(function(t){Vb(t)||Mb(e)}))},Xb=function(e){var t=gf((function(){Mb(e)}),0);e.on("init",(function(){e.inline&&qb(e,t),Kb(e,t)})),e.on("remove",(function(){t.cancel()}))},Yb=Hs.DOM,Gb=function(e){return jb.isEditorUIElement(e)},Jb=function(e){var t=e.classList;return void 0!==t&&(t.contains("tox-edit-area")||t.contains("tox-edit-area__iframe")||t.contains("mce-content-body"))},Qb=function(e,t){var n=fm(e),r=Yb.getParent(t,(function(t){return Gb(t)||!!n&&e.dom.is(t,n)}));return null!==r},Zb=function(e){try{var t=dr(Cn.fromDom(e.getElement()));return ab(t).fold((function(){return document.body}),(function(e){return e.dom}))}catch(n){return document.body}},ey=function(e,t){var n=t.editor;Xb(n),n.on("focusin",(function(){var t=e.focusedEditor;t!==n&&(t&&t.fire("blur",{focusedEditor:n}),e.setActive(n),e.focusedEditor=n,n.fire("focus",{blurredEditor:t}),n.focus(!0))})),n.on("focusout",(function(){Ii.setEditorTimeout(n,(function(){var t=e.focusedEditor;Qb(n,Zb(n))||t!==n||(n.fire("blur",{focusedEditor:null}),e.focusedEditor=null)}))})),Nv||(Nv=function(t){var n=e.activeEditor;n&&hr(t).each((function(t){t.ownerDocument===document&&(t===document.body||Qb(n,t)||e.focusedEditor!==n||(n.fire("blur",{focusedEditor:null}),e.focusedEditor=null))}))},Yb.bind(document,"focusin",Nv))},ty=function(e,t){e.focusedEditor===t.editor&&(e.focusedEditor=null),e.activeEditor||(Yb.unbind(document,"focusin",Nv),Nv=null)},ny=function(e){e.on("AddEditor",B(ey,e)),e.on("RemoveEditor",B(ty,e))},ry=function(e,t){return e.dom.getParent(t,(function(t){return"true"===e.dom.getContentEditable(t)}))},oy=function(e){return e.collapsed?q.from(ul(e.startContainer,e.startOffset)).map(Cn.fromDom):q.none()},iy=function(e,t){return oy(t).bind((function(t){return oa(t)?q.some(t):!1===In(e,t)?q.some(e):q.none()}))},ay=function(e,t){iy(Cn.fromDom(e.getBody()),t).bind((function(e){return Jg(e.dom)})).fold((function(){e.selection.normalize()}),(function(t){return e.selection.setRng(t.toRange())}))},uy=function(e){if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},cy=function(e){return ib(e)||ub(e).isSome()},sy=function(e){return e.iframeElement&&ib(Cn.fromDom(e.iframeElement))},fy=function(e){var t=e.getBody();return t&&cy(Cn.fromDom(t))},ly=function(e){var t=dr(Cn.fromDom(e.getElement()));return ab(t).filter((function(t){return!Jb(t.dom)&&Qb(e,t.dom)})).isSome()},dy=function(e){return e.inline?fy(e):sy(e)},my=function(e){return dy(e)||ly(e)},gy=function(e){var t=e.selection,n=e.getBody(),r=t.getRng();e.quirks.refreshContentEditable(),void 0!==e.bookmark&&!1===dy(e)&&Ub(e).each((function(t){e.selection.setRng(t),r=t}));var o=ry(e,t.getNode());if(e.$.contains(n,o))return uy(o),ay(e,r),void py(e);e.inline||(en.opera||uy(n),e.getWin().focus()),(en.gecko||e.inline)&&(uy(n),ay(e,r)),py(e)},py=function(e){return e.editorManager.setActive(e)},hy=function(e,t){e.removed||(t?py(e):gy(e))},vy=function(e,t,n,r,o){var i=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return q.from(i).map(Cn.fromDom).map((function(e){return r&&t.collapsed?e:or(e,o(e,a)).getOr(e)})).bind((function(e){return Hn(e)?q.some(e):Gn(e).filter(Hn)})).map((function(e){return e.dom})).getOr(e)},by=function(e,t,n){return vy(e,t,!0,n,(function(e,t){return Math.min(ur(e),t)}))},yy=function(e,t,n){return vy(e,t,!1,n,(function(e,t){return t>0?t-1:t}))},Cy=function(e,t){var n=e;while(e&&Zr(e)&&0===e.length)e=t?e.nextSibling:e.previousSibling;return e||n},wy=function(e,t){var n,r,o;if(!t)return e;r=t.startContainer,o=t.endContainer;var i=t.startOffset,a=t.endOffset;return n=t.commonAncestorContainer,!t.collapsed&&(r===o&&a-i<2&&r.hasChildNodes()&&(n=r.childNodes[i]),3===r.nodeType&&3===o.nodeType&&(r=r.length===i?Cy(r.nextSibling,!0):r.parentNode,o=0===a?Cy(o.previousSibling,!1):o.parentNode,r&&r===o))?r:n&&3===n.nodeType?n.parentNode:n},xy=function(e,t,n,r){var o,i=[],a=e.getRoot();if(n=e.getParent(n||by(a,t,t.collapsed),e.isBlock),r=e.getParent(r||yy(a,t,t.collapsed),e.isBlock),n&&n!==a&&i.push(n),n&&r&&n!==r){o=n;var u=new zi(n,a);while((o=u.next())&&o!==r)e.isBlock(o)&&i.push(o)}return r&&n!==r&&r!==a&&i.push(r),i},Sy=function(e,t,n){return q.from(t).map((function(t){var r=e.nodeIndex(t),o=e.createRng();return o.setStart(t.parentNode,r),o.setEnd(t.parentNode,r+1),n&&(kh(e,o,t,!0),kh(e,o,t,!1)),o}))},ky=function(e,t){return Q(t,(function(t){var n=e.fire("GetSelectionRange",{range:t});return n.range!==t?n.range:t}))},Ny={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Ey=function(e,t,n){var r=n?"lastChild":"firstChild",o=n?"prev":"next";if(e[r])return e[r];if(e!==t){var i=e[o];if(i)return i;for(var a=e.parent;a&&a!==t;a=a.parent)if(i=a[o],i)return i}},_y=function(e){if(!Ka(e.value))return!1;var t=e.parent;return!t||"span"===t.name&&!t.attr("style")||!/^[ ]+$/.test(e.value)},Ay=function(e){var t="a"===e.name&&!e.attr("href")&&e.attr("id");return e.attr("name")||e.attr("id")&&!e.firstChild||e.attr("data-mce-bookmark")||t},Ry=function(){function e(e,t){this.name=e,this.type=t,1===t&&(this.attributes=[],this.attributes.map={})}return e.create=function(t,n){var r=new e(t,Ny[t]||1);return n&&xe(n,(function(e,t){r.attr(t,e)})),r},e.prototype.replace=function(e){var t=this;return e.parent&&e.remove(),t.insert(e,t),t.remove(),t},e.prototype.attr=function(e,t){var n,r=this;if("string"!==typeof e)return void 0!==e&&null!==e&&xe(e,(function(e,t){r.attr(t,e)})),r;if(n=r.attributes){if(void 0!==t){if(null===t){if(e in n.map){delete n.map[e];var o=n.length;while(o--)if(n[o].name===e)return n.splice(o,1),r}return r}if(e in n.map){o=n.length;while(o--)if(n[o].name===e){n[o].value=t;break}}else n.push({name:e,value:t});return n.map[e]=t,r}return n.map[e]}},e.prototype.clone=function(){var t,n=this,r=new e(n.name,n.type);if(t=n.attributes){var o=[];o.map={};for(var i=0,a=t.length;i<a;i++){var u=t[i];"id"!==u.name&&(o[o.length]={name:u.name,value:u.value},o.map[u.name]=u.value)}r.attributes=o}return r.value=n.value,r.shortEnded=n.shortEnded,r},e.prototype.wrap=function(e){var t=this;return t.parent.insert(e,t),e.append(t),t},e.prototype.unwrap=function(){for(var e=this,t=e.firstChild;t;){var n=t.next;e.insert(t,e,!0),t=n}e.remove()},e.prototype.remove=function(){var e=this,t=e.parent,n=e.next,r=e.prev;return t&&(t.firstChild===e?(t.firstChild=n,n&&(n.prev=null)):r.next=n,t.lastChild===e?(t.lastChild=r,r&&(r.next=null)):n.prev=r,e.parent=e.next=e.prev=null),e},e.prototype.append=function(e){var t=this;e.parent&&e.remove();var n=t.lastChild;return n?(n.next=e,e.prev=n,t.lastChild=e):t.lastChild=t.firstChild=e,e.parent=t,e},e.prototype.insert=function(e,t,n){e.parent&&e.remove();var r=t.parent||this;return n?(t===r.firstChild?r.firstChild=e:t.prev.next=e,e.prev=t.prev,e.next=t,t.prev=e):(t===r.lastChild?r.lastChild=e:t.next.prev=e,e.next=t.next,e.prev=t,t.next=e),e.parent=r,e},e.prototype.getAll=function(e){for(var t=this,n=[],r=t.firstChild;r;r=Ey(r,t))r.name===e&&n.push(r);return n},e.prototype.children=function(){for(var e=this,t=[],n=e.firstChild;n;n=n.next)t.push(n);return t},e.prototype.empty=function(){var e=this;if(e.firstChild){for(var t=[],n=e.firstChild;n;n=Ey(n,e))t.push(n);var r=t.length;while(r--){n=t[r];n.parent=n.firstChild=n.lastChild=n.next=n.prev=null}}return e.firstChild=e.lastChild=null,e},e.prototype.isEmpty=function(e,t,n){void 0===t&&(t={});var r=this,o=r.firstChild;if(Ay(r))return!1;if(o)do{if(1===o.type){if(o.attr("data-mce-bogus"))continue;if(e[o.name])return!1;if(Ay(o))return!1}if(8===o.type)return!1;if(3===o.type&&!_y(o))return!1;if(3===o.type&&o.parent&&t[o.parent.name]&&Ka(o.value))return!1;if(n&&n(o))return!1}while(o=Ey(o,r));return!0},e.prototype.walk=function(e){return Ey(this,null,e)},e}(),Dy=gn.makeMap("NOSCRIPT STYLE SCRIPT XMP IFRAME NOEMBED NOFRAMES PLAINTEXT"," "),Ty=function(e){return v(e.nodeValue)&&ct(e.nodeValue,da)},Oy=function(e){return(0===e.length?"":Q(e,(function(e){return"["+e+"]"})).join(",")+",")+'[data-mce-bogus="all"]'},By=function(e,t){return t.querySelectorAll(Oy(e))},Py=function(e,t,n){return document.createTreeWalker(e,t,n,!1)},Ly=function(e){return Py(e,NodeFilter.SHOW_COMMENT,(function(e){return Ty(e)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}))},Iy=function(e){return Py(e,NodeFilter.SHOW_TEXT,(function(e){if(Ty(e)){var t=e.parentNode;return t&&Oe(Dy,t.nodeName)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}return NodeFilter.FILTER_SKIP}))},My=function(e){return null!==Ly(e).nextNode()},Fy=function(e){return null!==Iy(e).nextNode()},Uy=function(e,t){return null!==t.querySelector(Oy(e))},zy=function(e,t){Z(By(e,t),(function(t){var n=Cn.fromDom(t);"all"===yo(n,"data-mce-bogus")?Er(n):Z(e,(function(e){wo(n,e)&&xo(n,e)}))}))},Hy=function(e){var t=e.nextNode();while(null!==t)t.nodeValue=null,t=e.nextNode()},jy=A(Hy,Ly),Vy=A(Hy,Iy),qy=function(e,t){var n=[{condition:B(Uy,t),action:B(zy,t)},{condition:My,action:jy},{condition:Fy,action:Vy}],r=e,o=!1;return Z(n,(function(t){var n=t.condition,i=t.action;n(r)&&(o||(r=e.cloneNode(!0),o=!0),i(r))})),r},$y=function(e,t){var n=Nd(e),r=new RegExp("^(<"+n+"[^>]*>(&nbsp;|&#160;|\\s| |<br \\/>|)<\\/"+n+">[\r\n]*|<br \\/>[\r\n]*)$");return t.replace(r,"")},Wy=function(e,t){return qe(qe({},e),{format:t,get:!0,getInner:!0})},Ky=function(e,t,n,r){var o,i=Wy(t,n),a=t.no_events?i:e.fire("BeforeGetContent",i);return o="raw"===a.format?gn.trim(ga(qy(r,e.serializer.getTempAttrs()).innerHTML)):"text"===a.format?e.dom.isEmpty(r)?"":ga(r.innerText||r.textContent):"tree"===a.format?e.serializer.serialize(r,a):$y(e,e.serializer.serialize(r,a)),G(["text","tree"],a.format)||aa(Cn.fromDom(r))?a.content=o:a.content=gn.trim(o),a.no_events?a.content:e.fire("GetContent",a).content},Xy=function(e,t,n){return q.from(e.getBody()).fold(D("tree"===t.format?new Ry("body",11):""),(function(r){return Ky(e,t,n,r)}))},Yy=gn.each,Gy=function(e){var t=function(t,n){if(t.nodeName!==n.nodeName)return!1;var r=function(t){var n={};return Yy(e.getAttribs(t),(function(r){var o=r.nodeName.toLowerCase();0!==o.indexOf("_")&&"style"!==o&&0!==o.indexOf("data-")&&(n[o]=e.getAttrib(t,o))})),n},o=function(e,t){var n,r;for(r in e)if(Oe(e,r)){if(n=t[r],"undefined"===typeof n)return!1;if(e[r]!==n)return!1;delete t[r]}for(r in t)if(Oe(t,r))return!1;return!0};return!!o(r(t),r(n))&&(!!o(e.parseStyle(e.getAttrib(t,"style")),e.parseStyle(e.getAttrib(n,"style")))&&(!xp(t)&&!xp(n)))};return{compare:t}},Jy=gn.makeMap,Qy=function(e){var t=[];e=e||{};var n=e.indent,r=Jy(e.indent_before||""),o=Jy(e.indent_after||""),i=Au.getEncodeFunc(e.entity_encoding||"raw",e.entities),a="html"===e.element_format;return{start:function(e,u,c){var s,f,l,d;if(n&&r[e]&&t.length>0&&(d=t[t.length-1],d.length>0&&"\n"!==d&&t.push("\n")),t.push("<",e),u)for(s=0,f=u.length;s<f;s++)l=u[s],t.push(" ",l.name,'="',i(l.value,!0),'"');t[t.length]=!c||a?">":" />",c&&n&&o[e]&&t.length>0&&(d=t[t.length-1],d.length>0&&"\n"!==d&&t.push("\n"))},end:function(e){var r;t.push("</",e,">"),n&&o[e]&&t.length>0&&(r=t[t.length-1],r.length>0&&"\n"!==r&&t.push("\n"))},text:function(e,n){e.length>0&&(t[t.length]=n?e:i(e))},cdata:function(e){t.push("<![CDATA[",e,"]]>")},comment:function(e){t.push("\x3c!--",e,"--\x3e")},pi:function(e,r){r?t.push("<?",e," ",i(r),"?>"):t.push("<?",e,"?>"),n&&t.push("\n")},doctype:function(e){t.push("<!DOCTYPE",e,">",n?"\n":"")},reset:function(){t.length=0},getContent:function(){return t.join("").replace(/\n$/,"")}}},Zy=function(e,t){void 0===t&&(t=Hu());var n=Qy(e);e=e||{},e.validate=!("validate"in e)||e.validate;var r=function(r){var o=e.validate,i={3:function(e){n.text(e.value,e.raw)},8:function(e){n.comment(e.value)},7:function(e){n.pi(e.name,e.value)},10:function(e){n.doctype(e.value)},4:function(e){n.cdata(e.value)},11:function(e){if(e=e.firstChild)do{a(e)}while(e=e.next)}};n.reset();var a=function(e){var r=i[e.type];if(r)r(e);else{var u=e.name,c=e.shortEnded,s=e.attributes;if(o&&s&&s.length>1){var f=[];f.map={};var l=t.getElementRule(e.name);if(l){for(var d=0,m=l.attributesOrder.length;d<m;d++){var g=l.attributesOrder[d];if(g in s.map){var p=s.map[g];f.map[g]=p,f.push({name:g,value:p})}}for(d=0,m=s.length;d<m;d++){g=s[d].name;if(!(g in f.map)){p=s.map[g];f.map[g]=p,f.push({name:g,value:p})}}s=f}}if(n.start(e.name,s,c),!c){if(e=e.firstChild)do{a(e)}while(e=e.next);n.end(u)}}};return 1!==r.type||e.inner?i[11](r):a(r),n.getContent()};return{serialize:r}},eC=new Set;(function(){var e=["margin","margin-left","margin-right","margin-top","margin-bottom","padding","padding-left","padding-right","padding-top","padding-bottom","border","border-width","border-style","border-color","background","background-attachment","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","float","position","left","right","top","bottom","z-index","display","transform","width","max-width","min-width","height","max-height","min-height","overflow","overflow-x","overflow-y","text-overflow","vertical-align","transition","transition-delay","transition-duration","transition-property","transition-timing-function"];Z(e,(function(e){eC.add(e)}))})();var tC=["font","text-decoration","text-emphasis"],nC=function(e,t){return Ce(e.parseStyle(e.getAttrib(t,"style")))},rC=function(e){return eC.has(e)},oC=function(e,t){return fe(nC(e,t),(function(e){return!rC(e)}))},iC=function(e){return ne(e,(function(e){return J(tC,(function(t){return st(e,t)}))}))},aC=function(e,t,n){var r=nC(e,t),o=nC(e,n),i=function(r){var o=e.getStyle(t,r),i=e.getStyle(n,r);return gt(o)&&gt(i)&&o!==i};return J(r,(function(e){var t=function(t){return J(t,(function(t){return t===e}))};if(!t(o)&&t(tC)){var n=iC(o);return J(n,i)}return i(e)}))},uC=function(e,t,n){return q.from(n.container()).filter(Zr).exists((function(r){var o=e?0:-1;return t(r.data.charAt(n.offset()+o))}))},cC=B(uC,!0,Np),sC=B(uC,!1,Np),fC=function(e){var t=e.container();return Zr(t)&&(0===t.data.length||ma(t.data)&&Fh.isBookmarkNode(t.parentNode))},lC=function(e,t){return function(n){return q.from(dg(e?0:-1,n)).filter(t).isSome()}},dC=function(e){return oo(e)&&"block"===Eo(Cn.fromDom(e),"display")},mC=function(e){return ao(e)&&!Yr(e)},gC=lC(!0,dC),pC=lC(!1,dC),hC=lC(!0,co),vC=lC(!1,co),bC=lC(!0,Gr),yC=lC(!1,Gr),CC=lC(!0,mC),wC=lC(!1,mC),xC=function(e){var t=[],n=e.dom;while(n)t.push(Cn.fromDom(n)),n=n.lastChild;return t},SC=function(e){var t=Af(e,"br"),n=ne(xC(e).slice(-1),ea);t.length===n.length&&Z(n,Er)},kC=function(e){Nr(e),wr(e,Cn.fromHtml('<br data-mce-bogus="1">'))},NC=function(e){ar(e).each((function(t){Zn(t).each((function(n){Ji(e)&&ea(t)&&Ji(n)&&Er(t)}))}))},EC=function(e){return e.slice(0,-1)},_C=function(e,t,n){return In(t,e)?EC(Jn(e,(function(e){return n(e)||Bn(e,t)}))):[]},AC=function(e,t){return _C(e,t,F)},RC=function(e,t){return[e].concat(AC(e,t))},DC=function(e,t,n){return Kg(e,t,n,fC)},TC=function(e,t){return ae(RC(Cn.fromDom(t.container()),e),Ji)},OC=function(e,t,n){return DC(e,t.dom,n).forall((function(e){return TC(t,n).fold((function(){return!1===lg(e,n,t.dom)}),(function(r){return!1===lg(e,n,t.dom)&&In(r,Cn.fromDom(e.container()))}))}))},BC=function(e,t,n){return TC(t,n).fold((function(){return DC(e,t.dom,n).forall((function(e){return!1===lg(e,n,t.dom)}))}),(function(t){return DC(e,t.dom,n).isNone()}))},PC=B(BC,!1),LC=B(BC,!0),IC=B(OC,!1),MC=B(OC,!0),FC=function(e){return wg(e).exists(ea)},UC=function(e,t,n){var r=ne(RC(Cn.fromDom(n.container()),t),Ji),o=he(r).getOr(t);return $g(e,o.dom,n).filter(FC)},zC=function(e,t){return wg(t).exists(ea)||UC(!0,e,t).isSome()},HC=function(e,t){return xg(t).exists(ea)||UC(!1,e,t).isSome()},jC=B(UC,!1),VC=B(UC,!0),qC=function(e){return Bl.isTextPosition(e)&&!e.isAtStart()&&!e.isAtEnd()},$C=function(e,t){var n=ne(RC(Cn.fromDom(t.container()),e),Ji);return he(n).getOr(e)},WC=function(e,t){return qC(t)?sC(t):sC(t)||Gg($C(e,t).dom,t).exists(sC)},KC=function(e,t){return qC(t)?cC(t):cC(t)||Yg($C(e,t).dom,t).exists(cC)},XC=function(e){return G(["pre","pre-wrap"],e)},YC=function(e){return wg(e).bind((function(e){return Si(e,Hn)})).exists((function(e){return XC(Eo(e,"white-space"))}))},GC=function(e,t){return Gg(e.dom,t).isNone()},JC=function(e,t){return Yg(e.dom,t).isNone()},QC=function(e,t){return GC(e,t)||JC(e,t)||PC(e,t)||LC(e,t)||HC(e,t)||zC(e,t)},ZC=function(e,t){return!YC(t)&&(QC(e,t)||WC(e,t)||KC(e,t))},ew=function(e,t){return!YC(t)&&(PC(e,t)||IC(e,t)||HC(e,t)||WC(e,t))},tw=function(e){var t=e.container(),n=e.offset();return Zr(t)&&n<t.data.length?Bl(t,n+1):e},nw=function(e,t){return!YC(t)&&(LC(e,t)||MC(e,t)||zC(e,t)||KC(e,t))},rw=function(e,t){return ew(e,t)||nw(e,tw(t))},ow=function(e,t){return kp(e.charAt(t))},iw=function(e){var t=e.container();return Zr(t)&&ct(t.data,sa)},aw=function(e){var t=e.split("");return Q(t,(function(e,n){return kp(e)&&n>0&&n<t.length-1&&Ep(t[n-1])&&Ep(t[n+1])?" ":e})).join("")},uw=function(e,t){var n=t.data,r=Bl(t,0);return!(!ow(n,0)||rw(e,r))&&(t.data=" "+n.slice(1),!0)},cw=function(e){var t=e.data,n=aw(t);return n!==t&&(e.data=n,!0)},sw=function(e,t){var n=t.data,r=Bl(t,n.length-1);return!(!ow(n,n.length-1)||rw(e,r))&&(t.data=n.slice(0,-1)+" ",!0)},fw=function(e,t){return q.some(t).filter(iw).bind((function(t){var n=t.container(),r=uw(e,n)||cw(n)||sw(e,n);return r?q.some(t):q.none()}))},lw=function(e){var t=Cn.fromDom(e.getBody());e.selection.isCollapsed()&&fw(t,Bl.fromRangeStart(e.selection.getRng())).each((function(t){e.selection.setRng(t.toRange())}))},dw=function(e,t,n){var r=oe(e,(function(r,o){return Np(o)||kp(o)?r.previousCharIsSpace||""===r.str&&t||r.str.length===e.length-1&&n?{previousCharIsSpace:!1,str:r.str+sa}:{previousCharIsSpace:!0,str:r.str+" "}:{previousCharIsSpace:!1,str:r.str+o}}),{previousCharIsSpace:!1,str:""});return r.str},mw=function(e,t,n){if(0!==n){var r=Cn.fromDom(e),o=xi(r,Ji).getOr(r),i=e.data.slice(t,t+n),a=t+n>=e.data.length&&nw(o,Bl(e,e.data.length)),u=0===t&&ew(o,Bl(e,0));e.replaceData(t,n,dw(i,u,a))}},gw=function(e,t){var n=e.data.slice(t),r=n.length-dt(n).length;mw(e,t,r)},pw=function(e,t){var n=e.data.slice(0,t),r=n.length-mt(n).length;mw(e,t-r,r)},hw=function(e,t,n,r){void 0===r&&(r=!0);var o=mt(e.data).length,i=r?e:t,a=r?t:e;return r?i.appendData(a.data):i.insertData(0,a.data),Er(Cn.fromDom(a)),n&&gw(i,o),i},vw=function(e,t){var n=e.container(),r=e.offset();return!1===Bl.isTextPosition(e)&&n===t.parentNode&&r>Bl.before(t).offset()},bw=function(e,t){return vw(t,e)?Bl(t.container(),t.offset()-1):t},yw=function(e){return Zr(e)?Bl(e,0):Bl.before(e)},Cw=function(e){return Zr(e)?Bl(e,e.data.length):Bl.after(e)},ww=function(e){return Ua(e.previousSibling)?q.some(Cw(e.previousSibling)):e.previousSibling?Qg(e.previousSibling):q.none()},xw=function(e){return Ua(e.nextSibling)?q.some(yw(e.nextSibling)):e.nextSibling?Jg(e.nextSibling):q.none()},Sw=function(e,t){var n=Bl.before(t.previousSibling?t.previousSibling:t.parentNode);return Gg(e,n).fold((function(){return Yg(e,Bl.after(t))}),q.some)},kw=function(e,t){return Yg(e,Bl.after(t)).fold((function(){return Gg(e,Bl.before(t))}),q.some)},Nw=function(e,t){return ww(t).orThunk((function(){return xw(t)})).orThunk((function(){return Sw(e,t)}))},Ew=function(e,t){return xw(t).orThunk((function(){return ww(t)})).orThunk((function(){return kw(e,t)}))},_w=function(e,t,n){return e?Ew(t,n):Nw(t,n)},Aw=function(e,t,n){return _w(e,t,n).map(B(bw,n))},Rw=function(e,t,n){n.fold((function(){e.focus()}),(function(n){e.selection.setRng(n.toRange(),t)}))},Dw=function(e){return function(t){return t.dom===e}},Tw=function(e,t){return t&&Oe(e.schema.getBlockElements(),Mn(t))},Ow=function(e){if(nu(e)){var t=Cn.fromHtml('<br data-mce-bogus="1">');return Nr(e),wr(e,t),q.some(Bl.before(t.dom))}return q.none()},Bw=function(e,t,n){var r=Zn(e).filter(jn),o=er(e).filter(jn);return Er(e),mo(r,o,t,(function(e,t,r){var o=e.dom,i=t.dom,a=o.data.length;return hw(o,i,n),r.container()===i?Bl(o,a):r})).orThunk((function(){return n&&(r.each((function(e){return pw(e.dom,e.dom.length)})),o.each((function(e){return gw(e.dom,0)}))),t}))},Pw=function(e,t){return Oe(e.schema.getTextInlineElements(),Mn(t))},Lw=function(e,t,n,r){void 0===r&&(r=!0);var o=Aw(t,e.getBody(),n.dom),i=xi(n,B(Tw,e),Dw(e.getBody())),a=Bw(n,o,Pw(e,n));e.dom.isEmpty(e.getBody())?(e.setContent(""),e.selection.setCursorLocation()):i.bind(Ow).fold((function(){r&&Rw(e,t,a)}),(function(n){r&&Rw(e,t,q.some(n))}))},Iw=function(e){return function(t){return Bn(e,t)}},Mw=function(e){return Af(e,"td,th")},Fw=function(e,t){var n=function(e){return bh(Cn.fromDom(e),t)},r=n(e.startContainer),o=n(e.endContainer),i=r.isSome(),a=o.isSome(),u=lo(r,o,Bn).getOr(!1),c=!u&&i&&a;return{startTable:r,endTable:o,isStartInTable:i,isEndInTable:a,isSameTable:u,isMultiTable:c}},Uw=function(e,t){return{start:e,end:t}},zw=function(e,t,n){return{rng:e,table:t,cells:n}},Hw=ei.generate([{singleCellTable:["rng","cell"]},{fullTable:["table"]},{partialTable:["cells","outsideDetails"]},{multiTable:["startTableCells","endTableCells","betweenRng"]}]),jw=function(e,t){return Ai(Cn.fromDom(e),"td,th",t)},Vw=function(e){return!Bn(e.start,e.end)},qw=function(e,t){return bh(e.start,t).bind((function(n){return bh(e.end,t).bind((function(e){return go(Bn(n,e),n)}))}))},$w=function(e,t){return!Vw(e)&&qw(e,t).exists((function(e){var t=e.dom.rows;return 1===t.length&&1===t[0].cells.length}))},Ww=function(e,t){var n=jw(e.startContainer,t),r=jw(e.endContainer,t);return lo(n,r,Uw)},Kw=function(e){return function(t){return bh(t,e).bind((function(e){return ve(Mw(e)).map((function(e){return Uw(t,e)}))}))}},Xw=function(e){return function(t){return bh(t,e).bind((function(e){return he(Mw(e)).map((function(e){return Uw(e,t)}))}))}},Yw=function(e){return function(t){return qw(t,e).map((function(e){return zw(t,e,Mw(e))}))}},Gw=function(e,t,n,r){if(n.collapsed||!e.forall(Vw))return q.none();if(t.isSameTable){var o=e.bind(Yw(r));return q.some({start:o,end:o})}var i=jw(n.startContainer,r),a=jw(n.endContainer,r),u=i.bind(Kw(r)).bind(Yw(r)),c=a.bind(Xw(r)).bind(Yw(r));return q.some({start:u,end:c})},Jw=function(e,t){return ue(e,(function(e){return Bn(e,t)}))},Qw=function(e){return lo(Jw(e.cells,e.rng.start),Jw(e.cells,e.rng.end),(function(t,n){return e.cells.slice(t,n+1)}))},Zw=function(e,t,n){return e.exists((function(e){return $w(e,n)&&Sh(e.start,t)}))},ex=function(e,t){var n=t.startTable,r=t.endTable,o=e.cloneRange();return n.each((function(e){return o.setStartAfter(e.dom)})),r.each((function(e){return o.setEndBefore(e.dom)})),o},tx=function(e,t,n,r){return Gw(e,t,n,r).bind((function(e){var t=e.start,n=e.end;return t.or(n)})).bind((function(e){var r=t.isSameTable,o=Qw(e).getOr([]);if(r&&e.cells.length===o.length)return q.some(Hw.fullTable(e.table));if(o.length>0){if(r)return q.some(Hw.partialTable(o,q.none()));var i=ex(n,t);return q.some(Hw.partialTable(o,q.some(qe(qe({},t),{rng:i}))))}return q.none()}))},nx=function(e,t,n,r){return Gw(e,t,n,r).bind((function(e){var r=e.start,o=e.end,i=r.bind(Qw).getOr([]),a=o.bind(Qw).getOr([]);if(i.length>0&&a.length>0){var u=ex(n,t);return q.some(Hw.multiTable(i,a,u))}return q.none()}))},rx=function(e,t){var n=Iw(e),r=Ww(t,n),o=Fw(t,n);return Zw(r,t,n)?r.map((function(e){return Hw.singleCellTable(t,e.start)})):o.isMultiTable?nx(r,o,t,n):tx(r,o,t,n)},ox=function(e){var t=zn(e)?Zn(e):ar(e);return t.bind(ox).orThunk((function(){return q.some(e)}))},ix=function(e){return Z(e,(function(e){xo(e,"contenteditable"),kC(e)}))},ax=function(e,t){return q.from(e.dom.getParent(t,e.dom.isBlock)).map(Cn.fromDom)},ux=function(e,t,n){n.each((function(n){t?Er(n):(kC(n),e.selection.setCursorLocation(n.dom,0))}))},cx=function(e,t,n,r){var o=n.cloneRange();r?(o.setStart(n.startContainer,n.startOffset),o.setEndAfter(t.dom.lastChild)):(o.setStartBefore(t.dom.firstChild),o.setEnd(n.endContainer,n.endOffset)),dx(e,o,t,!1)},sx=function(e){var t=vh(e),n=Cn.fromDom(e.selection.getNode());uo(n.dom)&&nu(n)?e.selection.setCursorLocation(n.dom,0):e.selection.collapse(!0),t.length>1&&J(t,(function(e){return Bn(e,n)}))&&vo(n,"data-mce-selected","1")},fx=function(e,t,n){var r=e.selection.getRng(),o=n.bind((function(n){var o=n.rng,i=n.isStartInTable,a=ax(e,i?o.endContainer:o.startContainer);o.deleteContents(),ux(e,i,a.filter(nu));var u=i?t[0]:t[t.length-1];return cx(e,u,r,i),nu(u)?q.none():q.some(i?t.slice(1):t.slice(0,-1))})).getOr(t);return ix(o),sx(e),!0},lx=function(e,t,n,r){var o=e.selection.getRng(),i=t[0],a=n[n.length-1];cx(e,i,o,!0),cx(e,a,o,!1);var u=nu(i)?t:t.slice(1),c=nu(a)?n:n.slice(0,-1);return ix(u.concat(c)),r.deleteContents(),sx(e),!0},dx=function(e,t,n,r){void 0===r&&(r=!0),t.deleteContents();var o=ox(n).getOr(n),i=Cn.fromDom(e.dom.getParent(o.dom,e.dom.isBlock));if(nu(i)&&(kC(i),r&&e.selection.setCursorLocation(i.dom,0)),!Bn(n,i)){var a=so(Gn(i),n)?[]:Qn(i);Z(a.concat(rr(n)),(function(e){Bn(e,i)||In(e,i)||!nu(e)||Er(e)}))}return!0},mx=function(e,t){return Lw(e,!1,t),!0},gx=function(e,t,n){return rx(t,n).map((function(t){return t.fold(B(dx,e),B(mx,e),B(fx,e),B(lx,e))}))},px=function(e,t){return wx(e,t)},hx=function(e,t,n,r){return yx(t,r).fold((function(){return gx(e,t,n)}),(function(t){return px(e,t)})).getOr(!1)},vx=function(e,t,n){var r=Cn.fromDom(e.getBody()),o=e.selection.getRng();return 0!==n.length?fx(e,n,q.none()):hx(e,r,o,t)},bx=function(e,t){return ae(RC(t,e),ia)},yx=function(e,t){return ae(RC(t,e),$n("caption"))},Cx=function(e,t,n,r,o){return Wg(n,e.getBody(),o).bind((function(e){return bx(t,Cn.fromDom(e.getNode())).map((function(e){return!1===Bn(e,r)}))}))},wx=function(e,t){return kC(t),e.selection.setCursorLocation(t.dom,0),q.some(!0)},xx=function(e,t,n,r){return Jg(e.dom).bind((function(o){return Qg(e.dom).map((function(e){return t?n.isEqual(o)&&r.isEqual(e):n.isEqual(e)&&r.isEqual(o)}))})).getOr(!0)},Sx=function(e,t){return wx(e,t)},kx=function(e,t,n){return yx(e,Cn.fromDom(n.getNode())).map((function(e){return!1===Bn(e,t)}))},Nx=function(e,t,n,r,o){return Wg(n,e.getBody(),o).bind((function(i){return xx(r,n,o,i)?Sx(e,r):kx(t,r,i)})).or(q.some(!0))},Ex=function(e,t,n,r){var o=Bl.fromRangeStart(e.selection.getRng());return bx(n,r).bind((function(r){return nu(r)?wx(e,r):Cx(e,n,t,r,o)})).getOr(!1)},_x=function(e,t,n,r){var o=Bl.fromRangeStart(e.selection.getRng());return nu(r)?wx(e,r):Nx(e,n,t,r,o)},Ax=function(e,t){return e?bC(t):yC(t)},Rx=function(e,t){var n=Bl.fromRangeStart(e.selection.getRng());return Ax(t,n)||$g(t,e.getBody(),n).exists((function(e){return Ax(t,e)}))},Dx=function(e,t,n){var r=Cn.fromDom(e.getBody());return yx(r,n).fold((function(){return Ex(e,t,r,n)||Rx(e,t)}),(function(n){return _x(e,t,r,n).getOr(!1)}))},Tx=function(e,t){var n=Cn.fromDom(e.selection.getStart(!0)),r=vh(e);return e.selection.isCollapsed()&&0===r.length?Dx(e,t,n):vx(e,n,r)},Ox=function(e,t,n,r){var o=document.createRange();return o.setStart(e,t),o.setEnd(n,r),o},Bx=function(e){var t=Bl.fromRangeStart(e),n=Bl.fromRangeEnd(e),r=e.commonAncestorContainer;return $g(!1,r,n).map((function(o){return!lg(t,n,r)&&lg(t,o,r)?Ox(t.container(),t.offset(),o.container(),o.offset()):e})).getOr(e)},Px=function(e){return e.collapsed?e:Bx(e)},Lx=function(e){return e.firstChild&&e.firstChild===e.lastChild},Ix=function(e){return"br"===e.name||e.value===sa},Mx=function(e,t){var n=e.getBlockElements();return n[t.name]&&Lx(t)&&Ix(t.firstChild)},Fx=function(e,t){var n=e.getNonEmptyElements();return t&&(t.isEmpty(n)||Mx(e,t))},Ux=function(e,t){var n=t.firstChild,r=t.lastChild;return n&&"meta"===n.name&&(n=n.next),r&&"mce_marker"===r.attr("id")&&(r=r.prev),Fx(e,r)&&(r=r.prev),!(!n||n!==r)&&("ul"===n.name||"ol"===n.name)},zx=function(e){var t=e.firstChild,n=e.lastChild;return t&&"META"===t.nodeName&&t.parentNode.removeChild(t),n&&"mce_marker"===n.id&&n.parentNode.removeChild(n),e},Hx=function(e,t,n){var r=t.serialize(n),o=e.createFragment(r);return zx(o)},jx=function(e){return ne(e.childNodes,(function(e){return"LI"===e.nodeName}))},Vx=function(e){return e.data===sa||ro(e)},qx=function(e){return e&&e.firstChild&&e.firstChild===e.lastChild&&Vx(e.firstChild)},$x=function(e){return!e.firstChild||qx(e)},Wx=function(e){return e.length>0&&$x(e[e.length-1])?e.slice(0,-1):e},Kx=function(e,t){var n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},Xx=function(e,t){return!!Kx(e,t)},Yx=function(e,t){var n=t.cloneRange(),r=t.cloneRange();return n.setStartBefore(e),r.setEndAfter(e),[n.cloneContents(),r.cloneContents()]},Gx=function(e,t){var n=Bl.before(e),r=Mg(t),o=r.next(n);return o?o.toRange():null},Jx=function(e,t){var n=Bl.after(e),r=Mg(t),o=r.prev(n);return o?o.toRange():null},Qx=function(e,t,n,r){var o=Yx(e,r),i=e.parentNode;return i.insertBefore(o[0],e),gn.each(t,(function(t){i.insertBefore(t,e)})),i.insertBefore(o[1],e),i.removeChild(e),Jx(t[t.length-1],n)},Zx=function(e,t,n){var r=e.parentNode;return gn.each(t,(function(t){r.insertBefore(t,e)})),Gx(e,n)},eS=function(e,t,n,r){return r.insertAfter(t.reverse(),e),Jx(t[0],n)},tS=function(e,t,n,r){var o=Hx(t,e,r),i=Kx(t,n.startContainer),a=Wx(jx(o.firstChild)),u=1,c=2,s=t.getRoot(),f=function(e){var r=Bl.fromRangeStart(n),o=Mg(t.getRoot()),a=e===u?o.prev(r):o.next(r);return!a||Kx(t,a.getNode())!==i};return f(u)?Zx(i,a,s):f(c)?eS(i,a,s,t):Qx(i,a,s,n)},nS=function(e,t,n){var r=Cn.fromDom(e.getRoot());return n=ew(r,Bl.fromRangeStart(t))?n.replace(/^ /,"&nbsp;"):n.replace(/^&nbsp;/," "),n=nw(r,Bl.fromRangeEnd(t))?n.replace(/(&nbsp;| )(<br( \/)>)?$/,"&nbsp;"):n.replace(/&nbsp;(<br( \/)?>)?$/," "),n},rS=uo,oS=function(e,t,n){if(null!==n){var r=e.getParent(t.endContainer,rS);return n===r&&Sh(Cn.fromDom(n),t)}return!1},iS=function(e,t,n){if("all"===n.getAttribute("data-mce-bogus"))n.parentNode.insertBefore(e.dom.createFragment(t),n);else{var r=n.firstChild,o=n.lastChild;!r||r===o&&"BR"===r.nodeName?e.dom.setHTML(n,t):e.selection.setContent(t)}},aS=function(e,t){q.from(e.getParent(t,"td,th")).map(Cn.fromDom).each(NC)},uS=function(e,t){var n=e.schema.getTextInlineElements(),r=e.dom;if(t){var o=e.getBody(),i=Gy(r);gn.each(r.select("*[data-mce-fragment]"),(function(e){var t=k(n[e.nodeName.toLowerCase()]);if(t&&oC(r,e))for(var a=e.parentNode;k(a)&&a!==o;a=a.parentNode){var u=aC(r,e,a);if(u)break;if(i.compare(a,e)){r.remove(e,!0);break}}}))}},cS=function(e){var t=e;while(t=t.walk())1===t.type&&t.attr("data-mce-fragment","1")},sS=function(e){gn.each(e.getElementsByTagName("*"),(function(e){e.removeAttribute("data-mce-fragment")}))},fS=function(e){return!!e.getAttribute("data-mce-fragment")},lS=function(e,t){return t&&!e.schema.getShortEndedElements()[t.nodeName]},dS=function(e,t){var n,r=e.dom,o=e.selection;if(t){o.scrollIntoView(t);var i=Uh(e.getBody(),t);if("false"===r.getContentEditable(i))return r.remove(t),void o.select(i);var a=r.createRng(),u=t.previousSibling;if(Zr(u)){if(a.setStart(u,u.nodeValue.length),!en.ie){var c=t.nextSibling;Zr(c)&&(u.appendData(c.data),c.parentNode.removeChild(c))}}else a.setStartBefore(t),a.setEndBefore(t);var s=function(t){var n=Bl.fromRangeStart(t),r=Mg(e.getBody());if(n=r.next(n),n)return n.toRange()},f=r.getParent(t,r.isBlock);r.remove(t),f&&r.isEmpty(f)&&(e.$(f).empty(),a.setStart(f,0),a.setEnd(f,0),rS(f)||fS(f)||!(n=s(a))?r.add(f,r.create("br",{"data-mce-bogus":"1"})):(a=n,r.remove(f))),o.setRng(a)}},mS=function(e){var t=e.dom,n=Px(e.selection.getRng());e.selection.setRng(n);var r=t.getParent(n.startContainer,rS);oS(t,n,r)?dx(e,n,Cn.fromDom(r)):e.getDoc().execCommand("Delete",!1,null)},gS=function(e,t,n){var r,o,i,a=e.selection,u=e.dom;/^ | $/.test(t)&&(t=nS(u,a.getRng(),t));var c=e.parser,s=n.merge,f=Zy({validate:im(e)},e.schema),l='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;</span>',d=e.fire("BeforeSetContent",{content:t,format:"html",selection:!0,paste:n.paste});if(d.isDefaultPrevented())e.fire("SetContent",{content:d.content,format:"html",selection:!0,paste:n.paste});else{t=d.content,n.preserve_zwsp||(t=ga(t)),-1===t.indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,l),o=a.getRng();var m=o.startContainer||(o.parentElement?o.parentElement():null),g=e.getBody();m===g&&a.isCollapsed()&&u.isBlock(g.firstChild)&&lS(e,g.firstChild)&&u.isEmpty(g.firstChild)&&(o=u.createRng(),o.setStart(g.firstChild,0),o.setEnd(g.firstChild,0),a.setRng(o)),a.isCollapsed()||mS(e),r=a.getNode();var p={context:r.nodeName.toLowerCase(),data:n.data,insert:!0},h=c.parse(t,p);if(!0===n.paste&&Ux(e.schema,h)&&Xx(u,r))return o=tS(f,u,a.getRng(),h),a.setRng(o),void e.fire("SetContent",d);if(cS(h),i=h.lastChild,"mce_marker"===i.attr("id")){var v=i;for(i=i.prev;i;i=i.walk(!0))if(3===i.type||!u.isBlock(i.name)){e.schema.isValidChild(i.parent.name,"span")&&i.parent.insert(v,i,"br"===i.name);break}}if(e._selectionOverrides.showBlockCaretContainer(r),p.invalid){e.selection.setContent(l),r=a.getNode();var b=e.getBody();9===r.nodeType?r=i=b:i=r;while(i!==b)r=i,i=i.parentNode;t=r===b?b.innerHTML:u.getOuterHTML(r),t=f.serialize(c.parse(t.replace(/<span (id="mce_marker"|id=mce_marker).+?<\/span>/i,(function(){return f.serialize(h)})))),r===b?u.setHTML(b,t):u.setOuterHTML(r,t)}else t=f.serialize(h),iS(e,t,r);uS(e,s),dS(e,u.get("mce_marker")),sS(e.getBody()),aS(u,a.getStart()),e.fire("SetContent",d),e.addVisual()}},pS=function(e,t){t(e),e.firstChild&&pS(e.firstChild,t),e.next&&pS(e.next,t)},hS=function(e,t,n){var r={},o={},i=[];for(var a in n.firstChild&&pS(n.firstChild,(function(n){Z(e,(function(e){e.name===n.name&&(r[e.name]?r[e.name].nodes.push(n):r[e.name]={filter:e,nodes:[n]})})),Z(t,(function(e){"string"===typeof n.attr(e.name)&&(o[e.name]?o[e.name].nodes.push(n):o[e.name]={filter:e,nodes:[n]})}))})),r)Oe(r,a)&&i.push(r[a]);for(var u in o)Oe(o,u)&&i.push(o[u]);return i},vS=function(e,t,n){var r=hS(e,t,n);Z(r,(function(e){Z(e.filter.callbacks,(function(t){t(e.nodes,e.filter.name,{})}))}))},bS="html",yS=function(e){return e instanceof Ry},CS=function(e){dy(e)&&Jg(e.getBody()).each((function(t){var n=t.getNode(),r=Gr(n)?Jg(n).getOr(t):t;e.selection.setRng(r.toRange())}))},wS=function(e,t,n){e.dom.setHTML(e.getBody(),t),!0!==n&&CS(e)},xS=function(e,t,n,r){if(n=ga(n),0===n.length||/^\s+$/.test(n)){var o='<br data-mce-bogus="1">';"TABLE"===t.nodeName?n="<tr><td>"+o+"</td></tr>":/^(UL|OL)$/.test(t.nodeName)&&(n="<li>"+o+"</li>");var i=Nd(e);i&&e.schema.isValidChild(t.nodeName.toLowerCase(),i.toLowerCase())?(n=o,n=e.dom.createHTML(i,Ed(e),n)):n||(n='<br data-mce-bogus="1">'),wS(e,n,r.no_selection),e.fire("SetContent",r)}else"raw"!==r.format&&(n=Zy({validate:e.validate},e.schema).serialize(e.parser.parse(n,{isRootContent:!0,insert:!0}))),r.content=aa(Cn.fromDom(t))?n:gn.trim(n),wS(e,r.content,r.no_selection),r.no_events||e.fire("SetContent",r);return r.content},SS=function(e,t,n,r){vS(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);var o=Zy({validate:e.validate},e.schema).serialize(n);return r.content=ga(aa(Cn.fromDom(t))?o:gn.trim(o)),wS(e,r.content,r.no_selection),r.no_events||e.fire("SetContent",r),n},kS=function(e,t){return qe(qe({format:bS},e),{set:!0,content:yS(t)?"":t})},NS=function(e,t,n){var r=kS(n,t),o=n.no_events?r:e.fire("BeforeSetContent",r);return yS(t)||(t=o.content),q.from(e.getBody()).fold(D(t),(function(n){return yS(t)?SS(e,n,t,o):xS(e,n,t,o)}))},ES=function(e,t){return ki(e,t).isSome()},_S=function(e){return N(e)?e:F},AS=function(e,t,n){var r=e.dom,o=_S(n);while(r.parentNode){r=r.parentNode;var i=Cn.fromDom(r),a=t(i);if(a.isSome())return a;if(o(i))break}return q.none()},RS=function(e,t,n){var r=t(e),o=_S(n);return r.orThunk((function(){return o(e)?q.none():AS(e,t,o)}))},DS=Ip,TS=function(e,t,n){var r=e.formatter.get(n);if(r)for(var o=0;o<r.length;o++){var i=r[o];if(qp(i)&&!1===i.inherit&&e.dom.is(t,i.selector))return!0}return!1},OS=function(e,t,n,r,o){var i=e.dom.getRoot();return t!==i&&(t=e.dom.getParent(t,(function(t){return!!TS(e,t,n)||(t.parentNode===i||!!LS(e,t,n,r,!0))})),!!LS(e,t,n,r,o))},BS=function(e,t,n){return!!DS(t,n.inline)||(!!DS(t,n.block)||(n.selector?1===t.nodeType&&e.is(t,n.selector):void 0))},PS=function(e,t,n,r,o,i){var a=n[r];if(N(n.onmatch))return n.onmatch(t,n,r);if(a)if(x(a.length)){for(var u in a)if(Oe(a,u)){var c="attributes"===r?e.getAttrib(t,u):Fp(e,t,u),s=Lp(a[u],i),f=S(c)||pt(c);if(f&&S(s))continue;if(o&&f&&!n.exact)return!1;if((!o||n.exact)&&!DS(c,Mp(e,s,u)))return!1}}else for(var l=0;l<a.length;l++)if("attributes"===r?e.getAttrib(t,a[l]):Fp(e,t,a[l]))return!0;return!0},LS=function(e,t,n,r,o){var i=e.formatter.get(n),a=e.dom;if(i&&t)for(var u=0;u<i.length;u++){var c=i[u];if(BS(e.dom,t,c)&&PS(a,t,c,"attributes",o,r)&&PS(a,t,c,"styles",o,r)){var s=c.classes;if(s)for(var f=0;f<s.length;f++)if(!e.dom.hasClass(t,Lp(s[f],r)))return;return c}}},IS=function(e,t,n,r,o){if(r)return OS(e,r,t,n,o);if(r=e.selection.getNode(),OS(e,r,t,n,o))return!0;var i=e.selection.getStart();return!(i===r||!OS(e,i,t,n,o))},MS=function(e,t,n){var r=[],o={},i=e.selection.getStart();return e.dom.getParent(i,(function(i){for(var a=0;a<t.length;a++){var u=t[a];!o[u]&&LS(e,i,u,n)&&(o[u]=!0,r.push(u))}}),e.dom.getRoot()),r},FS=function(e,t){var n=function(t){return Bn(t,Cn.fromDom(e.getBody()))},r=function(t,n){return LS(e,t.dom,n)?q.some(n):q.none()};return q.from(e.selection.getStart(!0)).bind((function(e){return RS(Cn.fromDom(e),(function(e){return ye(t,(function(t){return r(e,t)}))}),n)})).getOrNull()},US=function(e,t){var n=e.formatter.get(t),r=e.dom;if(n)for(var o=e.selection.getStart(),i=zp(r,o),a=n.length-1;a>=0;a--){var u=n[a];if(!qp(u)||k(u.defaultBlock))return!0;for(var c=i.length-1;c>=0;c--)if(r.is(i[c],u.selector))return!0}return!1},zS=function(e,t,n){return oe(n,(function(n,r){var o=Hp(e,r);return e.formatter.matchNode(t,r,{},o)?n.concat([r]):n}),[])},HS=da,jS="_mce_caret",VS=function(e,t){return e.importNode(t,!0)},qS=function(e){var t=[];while(e){if(3===e.nodeType&&e.nodeValue!==HS||e.childNodes.length>1)return[];1===e.nodeType&&t.push(e),e=e.firstChild}return t},$S=function(e){return qS(e).length>0},WS=function(e){if(e){var t=new zi(e,e);for(e=t.current();e;e=t.next())if(Zr(e))return e}return null},KS=function(e){var t=Cn.fromTag("span");return bo(t,{id:jS,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&wr(t,Cn.fromText(HS)),t},XS=function(e){var t=WS(e);return t&&t.nodeValue.charAt(0)===HS&&t.deleteData(0,1),t},YS=function(e,t,n){void 0===n&&(n=!0);var r=e.dom,o=e.selection;if($S(t))Lw(e,!1,Cn.fromDom(t),n);else{var i=o.getRng(),a=r.getParent(t,r.isBlock),u=i.startContainer,c=i.startOffset,s=i.endContainer,f=i.endOffset,l=XS(t);r.remove(t,!0),u===l&&c>0&&i.setStart(l,c-1),s===l&&f>0&&i.setEnd(l,f-1),a&&r.isEmpty(a)&&kC(Cn.fromDom(a)),o.setRng(i)}},GS=function(e,t,n){void 0===n&&(n=!0);var r=e.dom,o=e.selection;if(t)YS(e,t,n);else if(t=tp(e.getBody(),o.getStart()),!t)while(t=r.get(jS))YS(e,t,!1)},JS=function(e,t,n){var r=e.dom,o=r.getParent(n,B(Tp,e));o&&r.isEmpty(o)?n.parentNode.replaceChild(t,n):(SC(Cn.fromDom(n)),r.isEmpty(n)?n.parentNode.replaceChild(t,n):r.insertAfter(t,n))},QS=function(e,t){return e.appendChild(t),t},ZS=function(e,t){var n=re(e,(function(e,t){return QS(e,t.cloneNode(!1))}),t);return QS(n,n.ownerDocument.createTextNode(HS))},ek=function(e,t,n,r,o,i){var a=e.formatter,u=e.dom,c=ne(Ce(a.get()),(function(e){return e!==r&&!ct(e,"removeformat")})),s=zS(e,n,c),f=ne(s,(function(t){return!jp(e,t,r)}));if(f.length>0){var l=n.cloneNode(!1);return u.add(t,l),a.remove(r,o,l,i),u.remove(l),q.some(l)}return q.none()},tk=function(e,t,n){var r,o,i=e.selection,a=i.getRng(),u=a.startOffset,c=a.startContainer,s=c.nodeValue;r=tp(e.getBody(),i.getStart()),r&&(o=WS(r));var f=/[^\s\u00a0\u00ad\u200b\ufeff]/;if(s&&u>0&&u<s.length&&f.test(s.charAt(u))&&f.test(s.charAt(u-1))){var l=i.getBookmark();a.collapse(!0);var d=sh(e,a,e.formatter.get(t));d=Sv(d),e.formatter.apply(t,n,d),i.moveToBookmark(l)}else r&&o.nodeValue===HS||(r=VS(e.getDoc(),KS(!0).dom),o=r.firstChild,a.insertNode(r),u=1),e.formatter.apply(t,n,r),i.setCursorLocation(o,u)},nk=function(e,t,n,r){var o,i,a,u=e.dom,c=e.selection,s=[],f=c.getRng(),l=f.startContainer,d=f.startOffset;i=l,3===l.nodeType&&(d!==l.nodeValue.length&&(o=!0),i=i.parentNode);while(i){if(LS(e,i,t,n,r)){a=i;break}i.nextSibling&&(o=!0),s.push(i),i=i.parentNode}if(a)if(o){var m=c.getBookmark();f.collapse(!0);var g=sh(e,f,e.formatter.get(t),!0);g=Sv(g),e.formatter.remove(t,n,g,r),c.moveToBookmark(m)}else{var p=tp(e.getBody(),a),h=KS(!1).dom;JS(e,h,null!==p?p:a);var v=ek(e,h,a,t,n,r),b=ZS(s.concat(v.toArray()),h);YS(e,p,!1),c.setCursorLocation(b,1),u.isEmpty(a)&&u.remove(a)}},rk=function(e,t){var n=e.selection,r=e.getBody();GS(e,null,!1),8!==t&&46!==t||!n.isCollapsed()||n.getStart().innerHTML!==HS||GS(e,tp(r,n.getStart())),37!==t&&39!==t||GS(e,tp(r,n.getStart()))},ok=function(e){e.on("mouseup keydown",(function(t){rk(e,t.keyCode)}))},ik=function(e,t){var n=KS(!1),r=ZS(t,n.dom);return br(Cn.fromDom(e),n),Er(Cn.fromDom(e)),Bl(r,0)},ak=function(e,t){var n=e.schema.getTextInlineElements();return Oe(n,Mn(t))&&!ep(t.dom)&&!Xr(t.dom)},uk=function(e){return ep(e.dom)&&$S(e.dom)},ck={},sk=Ue,fk=Me,lk=function(e,t){var n=ck[e];n||(ck[e]=[]),ck[e].push(t)},dk=function(e,t){fk(ck[e],(function(e){e(t)}))};lk("pre",(function(e){var t,n=e.selection.getRng(),r=function(e){return i(e.previousSibling)&&-1!==ze(t,e.previousSibling)},o=function(e,t){Bs(t).remove(),Bs(e).append("<br><br>").append(t.childNodes)},i=qr(["pre"]);n.collapsed||(t=e.selection.getSelectedBlocks(),fk(sk(sk(t,i),r),(function(e){o(e.previousSibling,e)})))}));var mk=gn.each,gk=function(e){return Vr(e)&&!xp(e)&&!ep(e)&&!Xr(e)},pk=function(e,t){for(var n=e;n;n=n[t]){if(Zr(n)&&gt(n.data))return e;if(Vr(n)&&!xp(n))return n}return e},hk=function(e,t,n){var r=Gy(e);if(t&&n&&(t=pk(t,"previousSibling"),n=pk(n,"nextSibling"),r.compare(t,n))){for(var o=t.nextSibling;o&&o!==n;){var i=o;o=o.nextSibling,t.appendChild(i)}return e.remove(n),gn.each(gn.grep(n.childNodes),(function(e){t.appendChild(e)})),t}return n},vk=function(e,t,n,r){if(r&&!1!==t.merge_siblings){var o=hk(e,Dp(r),r);hk(e,o,Dp(o,!0))}},bk=function(e,t,n){if(t.clear_child_styles){var r=t.links?"*:not(a)":"*";mk(e.select(r,n),(function(n){gk(n)&&mk(t.styles,(function(t,r){e.setStyle(n,r,"")}))}))}},yk=function(e,t,n){mk(e.childNodes,(function(e){gk(e)&&(t(e)&&n(e),e.hasChildNodes()&&yk(e,t,n))}))},Ck=function(e,t){"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)},wk=function(e,t){return function(n){return!(!n||!Fp(e,n,t))}},xk=function(e,t,n){return function(r){e.setStyle(r,t,n),""===r.getAttribute("style")&&r.removeAttribute("style"),Ck(e,r)}},Sk=ei.generate([{keep:[]},{rename:["name"]},{removed:[]}]),kk=/^(src|href|style)$/,Nk=gn.each,Ek=Ip,_k=function(e){return/^(TR|TH|TD)$/.test(e.nodeName)},Ak=function(e,t,n){return e.isChildOf(t,n)&&t!==n&&!e.isBlock(n)},Rk=function(e,t,n){var r=t[n?"startContainer":"endContainer"],o=t[n?"startOffset":"endOffset"];if(Vr(r)){var i=r.childNodes.length-1;!n&&o&&o--,r=r.childNodes[o>i?i:o]}return Zr(r)&&n&&o>=r.nodeValue.length&&(r=new zi(r,e.getBody()).next()||r),Zr(r)&&!n&&0===o&&(r=new zi(r,e.getBody()).prev()||r),r},Dk=function(e,t){var n=t?"firstChild":"lastChild";if(_k(e)&&e[n]){var r=e[n];return"TR"===e.nodeName&&r[n]||r}return e},Tk=function(e,t,n,r){var o=e.create(n,r);return t.parentNode.insertBefore(o,t),o.appendChild(t),o},Ok=function(e,t,n,r,o){var i=Cn.fromDom(t),a=Cn.fromDom(e.create(r,o)),u=n?nr(i):tr(i);return kr(a,u),n?(br(i,a),Cr(a,i)):(yr(i,a),wr(a,i)),a.dom},Bk=function(e,t,n){return!(!$p(n)||!Ek(t,n.inline))||(!(!Vp(n)||!Ek(t,n.block))||(qp(n)?Vr(t)&&e.is(t,n.selector):void 0))},Pk=function(e,t){return t.links&&"A"===e.nodeName},Lk=function(e,t,n,r){var o=Dp(t,n,r);return S(o)||"BR"===o.nodeName||e.isBlock(o)},Ik=function(e,t,n){var r,o=t.parentNode,i=e.dom,a=Nd(e);Vp(n)&&(a?o===i.getRoot()&&(n.list_block&&Ek(t,n.list_block)||Z(be(t.childNodes),(function(t){Op(e,a,t.nodeName.toLowerCase())?r?r.appendChild(t):(r=Tk(i,t,a),i.setAttribs(r,e.settings.forced_root_block_attrs)):r=null}))):i.isBlock(t)&&!i.isBlock(o)&&(Lk(i,t,!1)||Lk(i,t.firstChild,!0,!0)||t.insertBefore(i.create("br"),t.firstChild),Lk(i,t,!0)||Lk(i,t.lastChild,!1,!0)||t.appendChild(i.create("br")))),Wp(n)&&!Ek(n.inline,t)||i.remove(t,!0)},Mk=function(e,t,n,r,o){var i,a=e.dom;if(!Bk(a,r,t)&&!Pk(r,t))return Sk.keep();var u=r;if($p(t)&&"all"===t.remove&&y(t.preserve_attributes)){var c=ne(a.getAttribs(u),(function(e){return G(t.preserve_attributes,e.name.toLowerCase())}));if(a.removeAllAttribs(u),Z(c,(function(e){return a.setAttrib(u,e.name,e.value)})),c.length>0)return Sk.rename("span")}if("all"!==t.remove){Nk(t.styles,(function(e,r){e=Mp(a,Lp(e,n),r+""),E(r)&&(r=e,o=null),(t.remove_similar||!o||Ek(Fp(a,o,r),e))&&a.setStyle(u,r,""),i=!0})),i&&""===a.getAttrib(u,"style")&&(u.removeAttribute("style"),u.removeAttribute("data-mce-style")),Nk(t.attributes,(function(e,r){var i;if(e=Lp(e,n),E(r)&&(r=e,o=null),t.remove_similar||!o||Ek(a.getAttrib(o,r),e)){if("class"===r&&(e=a.getAttrib(u,r),e&&(i="",Z(e.split(/\s+/),(function(e){/mce\-\w+/.test(e)&&(i+=(i?" ":"")+e)})),i)))return void a.setAttrib(u,r,i);if(kk.test(r)&&u.removeAttribute("data-mce-"+r),"style"===r&&qr(["li"])(u)&&"none"===a.getStyle(u,"list-style-type"))return u.removeAttribute(r),void a.setStyle(u,"list-style-type","none");"class"===r&&u.removeAttribute("className"),u.removeAttribute(r)}})),Nk(t.classes,(function(e){e=Lp(e,n),o&&!a.hasClass(o,e)||a.removeClass(u,e)}));for(var s=a.getAttribs(u),f=0;f<s.length;f++){var l=s[f].nodeName;if(0!==l.indexOf("_")&&0!==l.indexOf("data-"))return Sk.keep()}}return"none"!==t.remove?(Ik(e,u,t),Sk.removed()):Sk.keep()},Fk=function(e,t,n,r,o){return Mk(e,t,n,r,o).fold(F,(function(t){return e.dom.rename(r,t),!0}),U)},Uk=function(e,t,n,r,o){var i;return Z(zp(e.dom,t.parentNode).reverse(),(function(t){if(!i&&"_start"!==t.id&&"_end"!==t.id){var a=LS(e,t,n,r,o);a&&!1!==a.split&&(i=t)}})),i},zk=function(e,t,n,r){return Mk(e,t,n,r,r).fold(D(r),(function(t){var n=e.dom.createFragment();return n.appendChild(r),e.dom.rename(r,t)}),D(null))},Hk=function(e,t,n,r,o,i,a,u){var c,s,f,l=e.dom;if(n){for(var d=n.parentNode,m=r.parentNode;m&&m!==d;m=m.parentNode){c=l.clone(m,!1);for(var g=0;g<t.length;g++)if(c=zk(e,t[g],u,c),null===c)break;c&&(s&&c.appendChild(s),f||(f=c),s=c)}!i||a.mixed&&l.isBlock(n)||(r=l.split(n,r)),s&&(o.parentNode.insertBefore(s,o),f.appendChild(o),$p(a)&&vk(l,a,u,s))}return r},jk=function(e,t,n,r,o){var i=e.formatter.get(t),a=i[0],u=!0,c=e.dom,s=e.selection,f=function(r){var u=Uk(e,r,t,n,o);return Hk(e,i,u,r,r,!0,a,n)},l=function(e){return xp(e)&&Vr(e)&&("_start"===e.id||"_end"===e.id)},d=function(t){return J(i,(function(r){return Fk(e,r,n,t,t)}))},m=function(t){var n=!0,r=!1;Vr(t)&&c.getContentEditable(t)&&(n=u,u="true"===c.getContentEditable(t),r=!0);var o=be(t.childNodes);if(u&&!r){var s=d(t),f=s||J(i,(function(e){return BS(c,t,e)})),l=t.parentNode;!f&&k(l)&&Kp(a)&&d(l)}if(a.deep&&o.length){for(var g=0;g<o.length;g++)m(o[g]);r&&(u=n)}var p=["underline","line-through","overline"];Z(p,(function(n){Vr(t)&&e.dom.getStyle(t,"text-decoration")===n&&t.parentNode&&Up(c,t.parentNode)===n&&Fk(e,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:n}},null,t)}))},g=function(e){var t=c.get(e?"_start":"_end"),n=t[e?"firstChild":"lastChild"];return l(n)&&(n=n[e?"firstChild":"lastChild"]),Zr(n)&&0===n.data.length&&(n=e?t.previousSibling||t.nextSibling:t.nextSibling||t.previousSibling),c.remove(t,!0),n},p=function(t){var n,r,o=sh(e,t,i,t.collapsed);if(a.split){if(o=Sv(o),n=Rk(e,o,!0),r=Rk(e,o),n!==r){if(n=Dk(n,!0),r=Dk(r,!1),Ak(c,n,r)){var u=q.from(n.firstChild).getOr(n);return f(Ok(c,u,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void g(!0)}if(Ak(c,r,n)){u=q.from(r.lastChild).getOr(r);return f(Ok(c,u,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void g(!1)}n=Tk(c,n,"span",{id:"_start","data-mce-type":"bookmark"}),r=Tk(c,r,"span",{id:"_end","data-mce-type":"bookmark"});var s=c.createRng();s.setStartAfter(n),s.setEndBefore(r),fh(c,s,(function(e){Z(e,(function(e){xp(e)||xp(e.parentNode)||f(e)}))})),f(n),f(r),n=g(!0),r=g()}else n=r=f(n);o.startContainer=n.parentNode?n.parentNode:n,o.startOffset=c.nodeIndex(n),o.endContainer=r.parentNode?r.parentNode:r,o.endOffset=c.nodeIndex(r)+1}fh(c,o,(function(e){Z(e,m)}))};if(r){if(_p(r)){var h=c.createRng();h.setStartBefore(r),h.setEndAfter(r),p(h)}else p(r);ev(e,t,r,n)}else if("false"!==c.getContentEditable(s.getNode()))s.isCollapsed()&&$p(a)&&!vh(e).length?nk(e,t,n,o):(_h(s,!0,(function(){Eh(e,p)})),$p(a)&&IS(e,t,n,s.getStart())&&Rp(c,s,s.getRng()),e.nodeChanged()),ev(e,t,r,n);else{r=s.getNode();for(var v=0;v<i.length;v++)if(i[v].ceFalseOverride&&Fk(e,i[v],n,r,r))break;ev(e,t,r,n)}},Vk=gn.each,qk=function(e,t,n,r){var o=function(t){if(1===t.nodeType&&t.parentNode&&1===t.parentNode.nodeType){var n=Up(e,t.parentNode);e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null)}};t.styles&&(t.styles.color||t.styles.textDecoration)&&(gn.walk(r,o,"childNodes"),o(r))},$k=function(e,t,n,r){t.styles&&t.styles.backgroundColor&&yk(r,wk(e,"fontSize"),xk(e,"backgroundColor",Lp(t.styles.backgroundColor,n)))},Wk=function(e,t,n,r){!$p(t)||"sub"!==t.inline&&"sup"!==t.inline||(yk(r,wk(e,"fontSize"),xk(e,"fontSize","")),e.remove(e.select("sup"===t.inline?"sub":"sup",r),!0))},Kk=function(e,t,n,r){Vk(t,(function(t){$p(t)&&Vk(e.dom.select(t.inline,r),(function(r){gk(r)&&Fk(e,t,n,r,t.exact?r:null)})),bk(e.dom,t,r)}))},Xk=function(e,t,n,r,o){LS(e,o.parentNode,n,r)&&Fk(e,t,r,o)||t.merge_with_parents&&e.dom.getParent(o.parentNode,(function(i){if(LS(e,i,n,r))return Fk(e,t,r,o),!0}))},Yk=gn.each,Gk=function(e){return Vr(e)&&!xp(e)&&!ep(e)&&!Xr(e)},Jk=function(e,t,n,r){if(sm(e)&&$p(t)){var o=Fu(e.schema),i=ES(Cn.fromDom(n),(function(e){return ep(e.dom)}));return Be(o,r)&&nu(Cn.fromDom(n.parentNode),!1)&&!i}return!1},Qk=function(e,t,n,r){var o=e.formatter.get(t),i=o[0],a=!r&&e.selection.isCollapsed(),u=e.dom,c=e.selection,s=function(e,t){if(void 0===t&&(t=i),N(t.onformat)&&t.onformat(e,t,n,r),Yk(t.styles,(function(t,r){u.setStyle(e,r,Lp(t,n))})),t.styles){var o=u.getAttrib(e,"style");o&&u.setAttrib(e,"data-mce-style",o)}Yk(t.attributes,(function(t,r){u.setAttrib(e,r,Lp(t,n))})),Yk(t.classes,(function(t){t=Lp(t,n),u.hasClass(e,t)||u.addClass(e,t)}))},f=function(e,t){var n=!1;return Yk(e,(function(e){return!!qp(e)&&(k(e.collapsed)&&e.collapsed!==a?void 0:u.is(t,e.selector)&&!ep(t)?(s(t,e),n=!0,!1):void 0)})),n},l=function(e){if(v(e)){var t=u.create(e);return s(t),t}return null},d=function(r,a,u){var c=[],d=!0,m=i.inline||i.block,g=l(m);fh(r,a,(function(a){var l,p=function(a){var h=!1,v=d,b=a.nodeName.toLowerCase(),y=a.parentNode,C=y.nodeName.toLowerCase();if(Vr(a)&&r.getContentEditable(a)&&(v=d,d="true"===r.getContentEditable(a),h=!0),ro(a)&&!Jk(e,i,a,C))return l=null,void(Vp(i)&&r.remove(a));if(Vp(i)&&i.wrapper&&LS(e,a,t,n))l=null;else{if(d&&!h&&Vp(i)&&!i.wrapper&&Tp(e,b)&&Op(e,C,m)){var w=r.rename(a,m);return s(w),c.push(w),void(l=null)}if(qp(i)){var x=f(o,a);if(!x&&k(y)&&Kp(i)&&(x=f(o,y)),!$p(i)||x)return void(l=null)}!d||h||!Op(e,m,b)||!Op(e,C,m)||!u&&Zr(a)&&ma(a.data)||ep(a)||$p(i)&&r.isBlock(a)?(l=null,Z(be(a.childNodes),p),h&&(d=v),l=null):(l||(l=r.clone(g,!1),a.parentNode.insertBefore(l,a),c.push(l)),l.appendChild(a))}};Z(a,p)})),!0===i.links&&Z(c,(function(e){var t=function(e){"A"===e.nodeName&&s(e,i),Z(be(e.childNodes),t)};t(e)})),Z(c,(function(a){var u=function(e){var t=0;return Z(e.childNodes,(function(e){Pp(e)||xp(e)||t++})),t},f=function(e){var t=ae(e.childNodes,Gk).filter((function(e){return BS(r,e,i)}));return t.map((function(t){var n=r.clone(t,!1);return s(n),r.replace(n,e,!0),r.remove(t,!0),n})).getOr(e)},l=u(a);!(c.length>1)&&r.isBlock(a)||0!==l?($p(i)||Vp(i)&&i.wrapper)&&(i.exact||1!==l||(a=f(a)),Kk(e,o,n,a),Xk(e,i,t,n,a),$k(r,i,n,a),qk(r,i,n,a),Wk(r,i,n,a),vk(r,i,n,a)):r.remove(a,!0)}))};if("false"!==u.getContentEditable(c.getNode())){if(i){if(r)if(_p(r)){if(!f(o,r)){var m=u.createRng();m.setStartBefore(r),m.setEndAfter(r),d(u,sh(e,m,o),!0)}}else d(u,r,!0);else if(a&&$p(i)&&!vh(e).length)tk(e,t,n);else{var g=c.getNode(),p=o[0];e.settings.forced_root_block||!p.defaultBlock||u.getParent(g,u.isBlock)||Qk(e,p.defaultBlock),c.setRng(Px(c.getRng())),_h(c,!0,(function(){Eh(e,(function(t,n){var r=n?t:sh(e,t,o);d(u,r,!1)}))})),Rp(u,c,c.getRng()),e.nodeChanged()}dk(t,e)}Zh(e,t,r,n)}else{r=c.getNode();for(var h=0,b=o.length;h<b;h++){var y=o[h];if(y.ceFalseOverride&&qp(y)&&u.is(r,y.selector)){s(r,y);break}}Zh(e,t,r,n)}},Zk=function(e){return Oe(e,"vars")},eN=function(e,t){e.set({}),t.on("NodeChange",(function(n){oN(t,n.element,e.get())})),t.on("FormatApply FormatRemove",(function(n){var r=q.from(n.node).map((function(e){return _p(e)?e:e.startContainer})).bind((function(e){return Vr(e)?q.some(e):q.from(e.parentElement)})).getOrThunk((function(){return tN(t)}));oN(t,r,e.get())}))},tN=function(e){return e.selection.getStart()},nN=function(e,t,n,r,o){var i=function(t){var i=e.formatter.matchNode(t,n,null!==o&&void 0!==o?o:{},r);return!x(i)},a=function(t){return!!TS(e,t,n)||!r&&k(e.formatter.matchNode(t,n,o,!0))};return ie(t,i,a)},rN=function(e,t){var n=null!==t&&void 0!==t?t:tN(e);return ne(zp(e.dom,n),(function(e){return Vr(e)&&!Xr(e)}))},oN=function(e,t,n){var r=rN(e,t);xe(n,(function(n,o){var i=function(n){var i=nN(e,r,o,n.similar,Zk(n)?n.vars:void 0),a=i.isSome();if(n.state.get()!==a){n.state.set(a);var u=i.getOr(t);Zk(n)?n.callback(a,{node:u,format:o,parents:r}):Z(n.callbacks,(function(e){return e(a,{node:u,format:o,parents:r})}))}};Z([n.withSimilar,n.withoutSimilar],i),Z(n.withVars,i)}))},iN=function(e,t,n,r,o,i){var a=t.get();Z(n.split(","),(function(t){var n=Te(a,t).getOrThunk((function(){var e={withSimilar:{state:Gs(!1),similar:!0,callbacks:[]},withoutSimilar:{state:Gs(!1),similar:!1,callbacks:[]},withVars:[]};return a[t]=e,e})),u=function(){var n=rN(e);return nN(e,n,t,o,i).isSome()};if(x(i)){var c=o?n.withSimilar:n.withoutSimilar;c.callbacks.push(r),1===c.callbacks.length&&c.state.set(u())}else n.withVars.push({state:Gs(u()),similar:o,vars:i,callback:r})})),t.set(a)},aN=function(e,t,n){var r=e.get();Z(t.split(","),(function(e){return Te(r,e).each((function(t){r[e]={withSimilar:qe(qe({},t.withSimilar),{callbacks:ne(t.withSimilar.callbacks,(function(e){return e!==n}))}),withoutSimilar:qe(qe({},t.withoutSimilar),{callbacks:ne(t.withoutSimilar.callbacks,(function(e){return e!==n}))}),withVars:ne(t.withVars,(function(e){return e.callback!==n}))}}))})),e.set(r)},uN=function(e,t,n,r,o,i){return null===t.get()&&eN(t,e),iN(e,t,n,r,o,i),{unbind:function(){return aN(t,n,r)}}},cN=function(e,t,n,r){var o=e.formatter.get(t);!IS(e,t,n,r)||"toggle"in o[0]&&!o[0].toggle?Qk(e,t,n,r):jk(e,t,n,r)},sN=function(e,t){var n=t||document,r=n.createDocumentFragment();return Z(e,(function(e){r.appendChild(e.dom)})),Cn.fromDom(r)},fN=function(e,t,n){return{element:e,width:t,rows:n}},lN=function(e,t){return{element:e,cells:t}},dN=function(e,t){return{x:e,y:t}},mN=function(e,t){var n=parseInt(yo(e,t),10);return isNaN(n)?1:n},gN=function(e,t,n,r,o){for(var i=mN(o,"rowspan"),a=mN(o,"colspan"),u=e.rows,c=n;c<n+i;c++){u[c]||(u[c]=lN(Xf(r),[]));for(var s=t;s<t+a;s++){var f=u[c].cells;f[s]=c===n&&s===t?o:Kf(o)}}},pN=function(e,t,n){var r=e.rows,o=r[n]?r[n].cells:[];return!!o[t]},hN=function(e,t,n){while(pN(e,t,n))t++;return t},vN=function(e){return oe(e,(function(e,t){return t.cells.length>e?t.cells.length:e}),0)},bN=function(e,t){for(var n=e.rows,r=0;r<n.length;r++)for(var o=n[r].cells,i=0;i<o.length;i++)if(Bn(o[i],t))return q.some(dN(i,r));return q.none()},yN=function(e,t,n,r,o){for(var i=[],a=e.rows,u=n;u<=o;u++){var c=a[u].cells,s=t<r?c.slice(t,r+1):c.slice(r,t+1);i.push(lN(a[u].element,s))}return i},CN=function(e,t,n){var r=t.x,o=t.y,i=n.x,a=n.y,u=o<a?yN(e,r,o,i,a):yN(e,r,a,i,o);return fN(e.element,vN(u),u)},wN=function(e,t){var n=Kf(e.element),r=Cn.fromTag("tbody");return kr(r,t),wr(n,r),n},xN=function(e){return Q(e.rows,(function(e){var t=Q(e.cells,(function(e){var t=Xf(e);return xo(t,"colspan"),xo(t,"rowspan"),t})),n=Kf(e.element);return kr(n,t),n}))},SN=function(e){var t=fN(Kf(e),0,[]);return Z(Af(e,"tr"),(function(e,n){Z(Af(e,"td,th"),(function(r,o){gN(t,hN(t,o,n),n,e,r)}))})),fN(t.element,vN(t.rows),t.rows)},kN=function(e){return wN(e,xN(e))},NN=function(e,t,n){return bN(e,t).bind((function(t){return bN(e,n).map((function(n){return CN(e,t,n)}))}))},EN=function(e){return ae(e,(function(e){return"ul"===Mn(e)||"ol"===Mn(e)}))},_N=function(e,t){return ae(e,(function(e){return"li"===Mn(e)&&Sh(e,t)})).fold(D([]),(function(t){return EN(e).map((function(e){var t=Cn.fromTag(Mn(e)),n=Ae(Ro(e),(function(e,t){return st(t,"list-style")}));return No(t,n),[Cn.fromTag("li"),t]})).getOr([])}))},AN=function(e,t){var n=oe(t,(function(e,t){return wr(t,e),t}),e);return t.length>0?sN([n]):n},RN=function(e){return ra(e)?Gn(e).filter(na).fold(D([]),(function(t){return[e,t]})):na(e)?[e]:[]},DN=function(e,t){var n=Cn.fromDom(t.commonAncestorContainer),r=RC(n,e),o=ne(r,(function(e){return Zi(e)||Gi(e)})),i=_N(r,t),a=o.concat(i.length?i:RN(n));return Q(a,Kf)},TN=function(){return sN([])},ON=function(e,t){return AN(Cn.fromDom(t.cloneContents()),DN(e,t))},BN=function(e,t){return Ei(t,"table",B(Bn,e))},PN=function(e,t){return BN(e,t[0]).bind((function(e){var n=t[0],r=t[t.length-1],o=SN(e);return NN(o,n,r).map((function(e){return sN([kN(e)])}))})).getOrThunk(TN)},LN=function(e,t){return t.length>0&&t[0].collapsed?TN():ON(e,t[0])},IN=function(e,t){var n=hh(t,e);return n.length>0?PN(e,n):LN(e,t)},MN=function(e){return e.replace(/^[ \f\n\r\t\v]+/,"")},FN=function(e,t){return t>=0&&t<e.length&&Np(e.charAt(t))},UN=function(e,t){var n=ga(e.innerText);return t?MN(n):n},zN=function(e){return e.map((function(e){return e.nodeName})).getOr("div").toLowerCase()},HN=function(e){return q.from(e.selection.getRng()).map((function(t){var n=q.from(e.dom.getParent(t.commonAncestorContainer,e.dom.isBlock)),r=e.getBody(),o=zN(n),i=en.browser.isIE()&&"pre"!==o,a=e.dom.add(r,o,{"data-mce-bogus":"all",style:"overflow: hidden; opacity: 0;"},t.cloneContents()),u=UN(a,i),c=ga(a.textContent);if(e.dom.remove(a),FN(c,0)||FN(c,c.length-1)){var s=n.getOr(r),f=UN(s,i),l=f.indexOf(u);if(-1===l)return u;var d=FN(f,l-1),m=FN(f,l+u.length);return(d?" ":"")+u+(m?" ":"")}return u})).getOr("")},jN=function(e,t){var n=e.selection.getRng(),r=e.dom.create("body"),o=e.selection.getSel(),i=ky(e,lh(o)),a=t.contextual?IN(Cn.fromDom(e.getBody()),i).dom:n.cloneContents();return a&&r.appendChild(a),e.selection.serializer.serialize(r,t)},VN=function(e,t){return qe(qe({},e),{format:t,get:!0,selection:!0})},qN=function(e,t,n){void 0===n&&(n={});var r=VN(n,t),o=e.fire("BeforeGetContent",r);if(o.isDefaultPrevented())return e.fire("GetContent",o),o.content;if("text"===o.format)return HN(e);o.getInner=!0;var i=jN(e,o);return"tree"===o.format?i:(o.content=e.selection.isCollapsed()?"":i,e.fire("GetContent",o),o.content)},$N=0,WN=1,KN=2,XN=function(e,t){var n=e.length+t.length+2,r=new Array(n),o=new Array(n),i=function(e,t,n){return{start:e,end:t,diag:n}},a=function(n,r,o,i,u){var s=c(n,r,o,i);if(null===s||s.start===r&&s.diag===r-i||s.end===n&&s.diag===n-o){var f=n,l=o;while(f<r||l<i)f<r&&l<i&&e[f]===t[l]?(u.push([$N,e[f]]),++f,++l):r-n>i-o?(u.push([KN,e[f]]),++f):(u.push([WN,t[l]]),++l)}else{a(n,s.start,o,s.start-s.diag,u);for(var d=s.start;d<s.end;++d)u.push([$N,e[d]]);a(s.end,r,s.end-s.diag,i,u)}},u=function(n,r,o,a){var u=n;while(u-r<a&&u<o&&e[u]===t[u-r])++u;return i(n,u,r)},c=function(n,i,a,c){var s=i-n,f=c-a;if(0===s||0===f)return null;var l,d,m,g,p,h=s-f,v=f+s,b=(v%2===0?v:v+1)/2;for(r[1+b]=n,o[1+b]=i+1,l=0;l<=b;++l){for(d=-l;d<=l;d+=2){m=d+b,d===-l||d!==l&&r[m-1]<r[m+1]?r[m]=r[m+1]:r[m]=r[m-1]+1,g=r[m],p=g-n+a-d;while(g<i&&p<c&&e[g]===t[p])r[m]=++g,++p;if(h%2!==0&&h-l<=d&&d<=h+l&&o[m-h]<=r[m])return u(o[m-h],d+n-a,i,c)}for(d=h-l;d<=h+l;d+=2){m=d+b-h,d===h-l||d!==h+l&&o[m+1]<=o[m-1]?o[m]=o[m+1]-1:o[m]=o[m-1],g=o[m]-1,p=g-n+a-d;while(g>=n&&p>=a&&e[g]===t[p])o[m]=g--,p--;if(h%2===0&&-l<=d&&d<=l&&o[m]<=r[m+h])return u(o[m],d+n-a,i,c)}}},s=[];return a(0,e.length,0,t.length,s),s},YN=function(e){return Vr(e)?e.outerHTML:Zr(e)?Au.encodeRaw(e.data,!1):eo(e)?"\x3c!--"+e.data+"--\x3e":""},GN=function(e){var t,n=document.createElement("div"),r=document.createDocumentFragment();e&&(n.innerHTML=e);while(t=n.firstChild)r.appendChild(t);return r},JN=function(e,t,n){var r=GN(t);if(e.hasChildNodes()&&n<e.childNodes.length){var o=e.childNodes[n];o.parentNode.insertBefore(r,o)}else e.appendChild(r)},QN=function(e,t){if(e.hasChildNodes()&&t<e.childNodes.length){var n=e.childNodes[t];n.parentNode.removeChild(n)}},ZN=function(e,t){var n=0;Z(e,(function(e){e[0]===$N?n++:e[0]===WN?(JN(t,e[1],n),n++):e[0]===KN&&QN(t,n)}))},eE=function(e,t){return ne(Q(be(e.childNodes),t?A(ga,YN):YN),(function(e){return e.length>0}))},tE=function(e,t){var n=Q(be(t.childNodes),YN);return ZN(XN(n,e),t),t},nE=Ke((function(){return document.implementation.createHTMLDocument("undo")})),rE=function(e){return null!==e.querySelector("iframe")},oE=function(e){return{type:"fragmented",fragments:e,content:"",bookmark:null,beforeBookmark:null}},iE=function(e){return{type:"complete",fragments:null,content:e,bookmark:null,beforeBookmark:null}},aE=function(e){var t=e.serializer.getTempAttrs(),n=qy(e.getBody(),t);return rE(n)?oE(eE(n,!0)):iE(ga(n.innerHTML))},uE=function(e,t,n){var r=n?t.beforeBookmark:t.bookmark;"fragmented"===t.type?tE(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw",no_selection:!k(r)||!ap(r)||!r.isFakeCaret}),e.selection.moveToBookmark(r)},cE=function(e){return"fragmented"===e.type?e.fragments.join(""):e.content},sE=function(e){var t=Cn.fromTag("body",nE());return $f(t,cE(e)),Z(Af(t,"*[data-mce-bogus]"),_r),qf(t)},fE=function(e,t){return cE(e)===cE(t)},lE=function(e,t){return sE(e)===sE(t)},dE=function(e,t){return!(!e||!t)&&(!!fE(e,t)||lE(e,t))},mE=function(e){return 0===e.get()},gE=function(e,t,n){mE(n)&&(e.typing=t)},pE=function(e,t){e.typing&&(gE(e,!1,t),e.add())},hE=function(e){e.typing&&(e.typing=!1,e.add())},vE=function(e,t,n){mE(t)&&n.set(gd(e.selection))},bE=function(e,t,n,r,o,i,a){var u=aE(e);if(i=i||{},i=gn.extend(i,u),!1===mE(r)||e.removed)return null;var c=t.data[n.get()];if(e.fire("BeforeAddUndo",{level:i,lastLevel:c,originalEvent:a}).isDefaultPrevented())return null;if(c&&dE(c,i))return null;t.data[n.get()]&&o.get().each((function(e){t.data[n.get()].beforeBookmark=e}));var s=ym(e);if(s&&t.data.length>s){for(var f=0;f<t.data.length-1;f++)t.data[f]=t.data[f+1];t.data.length--,n.set(t.data.length)}i.bookmark=gd(e.selection),n.get()<t.data.length-1&&(t.data.length=n.get()+1),t.data.push(i),n.set(t.data.length-1);var l={level:i,lastLevel:c,originalEvent:a};return n.get()>0?(e.setDirty(!0),e.fire("AddUndo",l),e.fire("change",l)):e.fire("AddUndo",l),i},yE=function(e,t,n){t.data=[],n.set(0),t.typing=!1,e.fire("ClearUndos")},CE=function(e,t,n,r,o){if(t.transact(r)){var i=t.data[n.get()].bookmark,a=t.data[n.get()-1];uE(e,a,!0),t.transact(o)&&(t.data[n.get()-1].beforeBookmark=i)}},wE=function(e,t,n){var r;return t.get()<n.length-1&&(t.set(t.get()+1),r=n[t.get()],uE(e,r,!1),e.setDirty(!0),e.fire("Redo",{level:r})),r},xE=function(e,t,n,r){var o;return t.typing&&(t.add(),t.typing=!1,gE(t,!1,n)),r.get()>0&&(r.set(r.get()-1),o=t.data[r.get()],uE(e,o,!0),e.setDirty(!0),e.fire("Undo",{level:o})),o},SE=function(e){e.clear(),e.add()},kE=function(e,t,n){return n.get()>0||t.typing&&t.data[0]&&!dE(aE(e),t.data[0])},NE=function(e,t){return t.get()<e.data.length-1&&!e.typing},EE=function(e,t,n){return pE(e,t),e.beforeChange(),e.ignore(n),e.add()},_E=function(e,t){try{e.set(e.get()+1),t()}finally{e.set(e.get()-1)}},AE=function(e,t){var n=e.dom,r=k(t)?t:e.getBody();x(e.hasVisual)&&(e.hasVisual=Em(e)),Z(n.select("table,a",r),(function(t){switch(t.nodeName){case"TABLE":var r=_m(e),o=n.getAttrib(t,"border");o&&"0"!==o||!e.hasVisual?n.removeClass(t,r):n.addClass(t,r);break;case"A":if(!n.getAttrib(t,"href")){var i=n.getAttrib(t,"name")||t.id,a=Am(e);i&&e.hasVisual?n.addClass(t,a):n.removeClass(t,a)}break}})),e.fire("VisualAid",{element:t,hasVisual:e.hasVisual})},RE=function(e){return{undoManager:{beforeChange:function(t,n){return vE(e,t,n)},add:function(t,n,r,o,i,a){return bE(e,t,n,r,o,i,a)},undo:function(t,n,r){return xE(e,t,n,r)},redo:function(t,n){return wE(e,t,n)},clear:function(t,n){return yE(e,t,n)},reset:function(e){return SE(e)},hasUndo:function(t,n){return kE(e,t,n)},hasRedo:function(e,t){return NE(e,t)},transact:function(e,t,n){return EE(e,t,n)},ignore:function(e,t){return _E(e,t)},extra:function(t,n,r,o){return CE(e,t,n,r,o)}},formatter:{match:function(t,n,r,o){return IS(e,t,n,r,o)},matchAll:function(t,n){return MS(e,t,n)},matchNode:function(t,n,r,o){return LS(e,t,n,r,o)},canApply:function(t){return US(e,t)},closest:function(t){return FS(e,t)},apply:function(t,n,r){return Qk(e,t,n,r)},remove:function(t,n,r,o){return jk(e,t,n,r,o)},toggle:function(t,n,r){return cN(e,t,n,r)},formatChanged:function(t,n,r,o,i){return uN(e,t,n,r,o,i)}},editor:{getContent:function(t,n){return Xy(e,t,n)},setContent:function(t,n){return NS(e,t,n)},insertContent:function(t,n){return gS(e,t,n)},addVisual:function(t){return AE(e,t)}},selection:{getContent:function(t,n){return qN(e,t,n)}},raw:{getModel:function(){return q.none()}}}},DE=function(e){var t=function(e){return b(e)?e:{}},n=e.undoManager,r=e.formatter,o=e.editor,i=e.selection,a=e.raw;return{undoManager:{beforeChange:n.beforeChange,add:n.add,undo:n.undo,redo:n.redo,clear:n.clear,reset:n.reset,hasUndo:n.hasUndo,hasRedo:n.hasRedo,transact:function(e,t,r){return n.transact(r)},ignore:function(e,t){return n.ignore(t)},extra:function(e,t,r,o){return n.extra(r,o)}},formatter:{match:function(e,n,o,i){return r.match(e,t(n),i)},matchAll:r.matchAll,matchNode:r.matchNode,canApply:function(e){return r.canApply(e)},closest:function(e){return r.closest(e)},apply:function(e,n,o){return r.apply(e,t(n))},remove:function(e,n,o,i){return r.remove(e,t(n))},toggle:function(e,n,o){return r.toggle(e,t(n))},formatChanged:function(e,t,n,o,i){return r.formatChanged(t,n,o,i)}},editor:{getContent:function(e,t){return o.getContent(e)},setContent:function(e,t){return o.setContent(e,t)},insertContent:function(e,t){return o.insertContent(e)},addVisual:o.addVisual},selection:{getContent:function(e,t){return i.getContent(t)}},raw:{getModel:function(){return q.some(a.getRawModel())}}}},TE=function(){var e=D(null),t=D("");return{undoManager:{beforeChange:_,add:e,undo:e,redo:e,clear:_,reset:_,hasUndo:F,hasRedo:F,transact:e,ignore:_,extra:_},formatter:{match:F,matchAll:D([]),matchNode:D(void 0),canApply:F,closest:t,apply:_,remove:_,toggle:_,formatChanged:D({unbind:_})},editor:{getContent:t,setContent:t,insertContent:_,addVisual:_},selection:{getContent:t},raw:{getModel:D(q.none())}}},OE=function(e){return Oe(e.plugins,"rtc")},BE=function(e){return Te(e.plugins,"rtc").bind((function(e){return q.from(e.setup)}))},PE=function(e){var t=e;return BE(e).fold((function(){return t.rtcInstance=RE(e),q.none()}),(function(e){return t.rtcInstance=TE(),q.some((function(){return e().then((function(e){return t.rtcInstance=DE(e),e.rtc.isRemote}))}))}))},LE=function(e){return e.rtcInstance?e.rtcInstance:RE(e)},IE=function(e){var t=e.rtcInstance;if(t)return t;throw new Error("Failed to get RTC instance not yet initialized.")},ME=function(e,t,n){IE(e).undoManager.beforeChange(t,n)},FE=function(e,t,n,r,o,i,a){return IE(e).undoManager.add(t,n,r,o,i,a)},UE=function(e,t,n,r){return IE(e).undoManager.undo(t,n,r)},zE=function(e,t,n){return IE(e).undoManager.redo(t,n)},HE=function(e,t,n){IE(e).undoManager.clear(t,n)},jE=function(e,t){IE(e).undoManager.reset(t)},VE=function(e,t,n){return IE(e).undoManager.hasUndo(t,n)},qE=function(e,t,n){return IE(e).undoManager.hasRedo(t,n)},$E=function(e,t,n,r){return IE(e).undoManager.transact(t,n,r)},WE=function(e,t,n){IE(e).undoManager.ignore(t,n)},KE=function(e,t,n,r,o){IE(e).undoManager.extra(t,n,r,o)},XE=function(e,t,n,r,o){return IE(e).formatter.match(t,n,r,o)},YE=function(e,t,n){return IE(e).formatter.matchAll(t,n)},GE=function(e,t,n,r,o){return IE(e).formatter.matchNode(t,n,r,o)},JE=function(e,t){return IE(e).formatter.canApply(t)},QE=function(e,t){return IE(e).formatter.closest(t)},ZE=function(e,t,n,r){IE(e).formatter.apply(t,n,r)},e_=function(e,t,n,r,o){IE(e).formatter.remove(t,n,r,o)},t_=function(e,t,n,r){IE(e).formatter.toggle(t,n,r)},n_=function(e,t,n,r,o,i){return IE(e).formatter.formatChanged(t,n,r,o,i)},r_=function(e,t,n){return LE(e).editor.getContent(t,n)},o_=function(e,t,n){return LE(e).editor.setContent(t,n)},i_=function(e,t,n){return LE(e).editor.insertContent(t,n)},a_=function(e,t,n){return IE(e).selection.getContent(t,n)},u_=function(e,t){return IE(e).editor.addVisual(t)},c_=function(e,t){void 0===t&&(t={});var n=t.format?t.format:"html";return a_(e,n,t)},s_=function(e){return 0===e.dom.length?(Er(e),q.none()):q.some(e)},f_=function(e,t){return e.filter((function(e){return Fh.isBookmarkNode(e.dom)})).bind(t?er:Zn)},l_=function(e,t,n,r){var o=e.dom,i=t.dom,a=r?o.length:i.length;r?(hw(o,i,!1,!r),n.setStart(i,a)):(hw(i,o,!1,!r),n.setEnd(i,a))},d_=function(e,t){Gn(e).each((function(n){var r=e.dom;t&&ew(n,Bl(r,0))?gw(r,0):!t&&nw(n,Bl(r,r.length))&&pw(r,r.length)}))},m_=function(e,t,n,r){e.bind((function(e){var o=r?pw:gw;return o(e.dom,r?e.dom.length:0),t.filter(jn).map((function(t){return l_(e,t,n,r)}))})).orThunk((function(){var e=f_(t,r).or(t).filter(jn);return e.map((function(e){return d_(e,r)}))}))},g_=function(e,t){var n=q.from(t.firstChild).map(Cn.fromDom),r=q.from(t.lastChild).map(Cn.fromDom);e.deleteContents(),e.insertNode(t);var o=n.bind(Zn).filter(jn).bind(s_),i=r.bind(er).filter(jn).bind(s_);m_(o,n,e,!0),m_(i,r,e,!1),e.collapse(!1)},p_=function(e,t){return qe(qe({format:"html"},e),{set:!0,selection:!0,content:t})},h_=function(e,t){if("raw"!==t.format){var n=e.selection.getRng(),r=e.dom.getParent(n.commonAncestorContainer,e.dom.isBlock),o=r?{context:r.nodeName.toLowerCase()}:{},i=e.parser.parse(t.content,qe(qe({isRootContent:!0,forced_root_block:!1},o),t));return Zy({validate:e.validate},e.schema).serialize(i)}return t.content},v_=function(e,t,n){void 0===n&&(n={});var r=p_(n,t),o=r;if(!r.no_events){var i=e.fire("BeforeSetContent",r);if(i.isDefaultPrevented())return void e.fire("SetContent",i);o=i}o.content=h_(e,o);var a=e.selection.getRng();g_(a,a.createContextualFragment(o.content)),e.selection.setRng(a),nb(e,a),o.no_events||e.fire("SetContent",o)},b_=function(e,t,n){if(e&&Oe(e,t)){var r=ne(e[t],(function(e){return e!==n}));0===r.length?delete e[t]:e[t]=r}};function y_(e,t){var n,r,o=function(t,n){return ae(n,(function(n){return e.is(n,t)}))},i=function(t){return e.getParents(t,null,e.getRoot())};return{selectorChangedWithUnbind:function(e,a){return n||(n={},r={},t.on("NodeChange",(function(e){var t=e.element,a=i(t),u={};gn.each(n,(function(e,t){o(t,a).each((function(n){r[t]||(Z(e,(function(e){e(!0,{node:n,selector:t,parents:a})})),r[t]=e),u[t]=e}))})),gn.each(r,(function(e,n){u[n]||(delete r[n],gn.each(e,(function(e){e(!1,{node:t,selector:n,parents:a})})))}))}))),n[e]||(n[e]=[]),n[e].push(a),o(e,i(t.selection.getStart())).each((function(){r[e]=n[e]})),{unbind:function(){b_(n,e,a),b_(r,e,a)}}}}}var C_=function(e){return!!e.select},w_=function(e){return!(!e||!e.ownerDocument)&&In(Cn.fromDom(e.ownerDocument),Cn.fromDom(e))},x_=function(e){return!!e&&(!!C_(e)||w_(e.startContainer)&&w_(e.endContainer))},S_=function(e,t,n,r){var o,i,a=y_(e,r).selectorChangedWithUnbind,u=function(t,n){var o=e.createRng();k(t)&&k(n)?(o.setStart(t,n),o.setEnd(t,n),y(o),h(!1)):(kh(e,o,r.getBody(),!0),y(o))},c=function(e){return c_(r,e)},s=function(e,t){return v_(r,e,t)},f=function(e){return by(r.getBody(),b(),e)},l=function(e){return yy(r.getBody(),b(),e)},d=function(e,t){return B.getBookmark(e,t)},m=function(e){return B.moveToBookmark(e)},g=function(t,n){return Sy(e,t,n).each(y),t},p=function(){var e=b(),t=v();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},h=function(e){var t=b();t.collapse(!!e),y(t)},v=function(){return t.getSelection?t.getSelection():t.document.selection},b=function(){var n,a,u,c=function(e,t,n){try{return t.compareBoundaryPoints(e,n)}catch(r){return-1}},s=t.document;if(void 0!==r.bookmark&&!1===dy(r)){var f=Ub(r);if(f.isSome())return f.map((function(e){return ky(r,[e])[0]})).getOr(s.createRange())}try{(n=v())&&!jr(n.anchorNode)&&(a=n.rangeCount>0?n.getRangeAt(0):n.createRange?n.createRange():s.createRange(),a=ky(r,[a])[0])}catch(l){}return a||(a=s.createRange?s.createRange():s.body.createTextRange()),a.setStart&&9===a.startContainer.nodeType&&a.collapsed&&(u=e.getRoot(),a.setStart(u,0),a.setEnd(u,0)),o&&i&&(0===c(a.START_TO_START,a,o)&&0===c(a.END_TO_END,a,o)?a=i:(o=null,i=null)),a},y=function(e,t){var n;if(x_(e)){var a=C_(e)?e:null;if(a){i=null;try{a.select()}catch(s){}}else{var u=v(),c=r.fire("SetSelectionRange",{range:e,forward:t});if(e=c.range,u){i=e;try{u.removeAllRanges(),u.addRange(e)}catch(s){}!1===t&&u.extend&&(u.collapse(e.endContainer,e.endOffset),u.extend(e.startContainer,e.startOffset)),o=u.rangeCount>0?u.getRangeAt(0):null}e.collapsed||e.startContainer!==e.endContainer||!u.setBaseAndExtent||en.ie||e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()&&(n=e.startContainer.childNodes[e.startOffset],n&&"IMG"===n.tagName&&(u.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),u.anchorNode===e.startContainer&&u.focusNode===e.endContainer||u.setBaseAndExtent(n,0,n,1))),r.fire("AfterSetSelectionRange",{range:e,forward:t})}}},C=function(t){return s(e.getOuterHTML(t)),t},w=function(){return wy(r.getBody(),b())},x=function(t,n){return xy(e,b(),t,n)},S=function(){var t=v(),n=null===t||void 0===t?void 0:t.anchorNode,r=null===t||void 0===t?void 0:t.focusNode;if(!t||!n||!r||jr(n)||jr(r))return!0;var o=e.createRng();o.setStart(n,t.anchorOffset),o.collapse(!0);var i=e.createRng();return i.setStart(r,t.focusOffset),i.collapse(!0),o.compareBoundaryPoints(o.START_TO_START,i)<=0},N=function(){var t=b(),n=v();if(!mh(n)&&Nh(r)){var o=wv(e,t);return o.each((function(e){y(e,S())})),o.getOr(t)}return t},E=function(e,t){return a(e,t),O},_=function(){var t,n=e.getRoot();while(n&&"BODY"!==n.nodeName){if(n.scrollHeight>n.clientHeight){t=n;break}n=n.parentNode}return t},A=function(e,t){k(e)?tb(r,e,t):nb(r,b(),t)},R=function(e,t){return y(cv(e,t,r.getDoc()))},D=function(){var e=b();return e.collapsed?Bl.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},T=function(){t=o=i=null,P.destroy()},O={bookmarkManager:null,controlSelection:null,dom:e,win:t,serializer:n,editor:r,collapse:h,setCursorLocation:u,getContent:c,setContent:s,getBookmark:d,moveToBookmark:m,select:g,isCollapsed:p,isForward:S,setNode:C,getNode:w,getSel:v,setRng:y,getRng:b,getStart:f,getEnd:l,getSelectedBlocks:x,normalize:N,selectorChanged:E,selectorChangedWithUnbind:a,getScrollContainer:_,scrollIntoView:A,placeCaretAt:R,getBoundingClientRect:D,destroy:T},B=Fh(O),P=rv(O,r);return O.bookmarkManager=B,O.controlSelection=P,O},k_=function(e,t){Z(t,(function(t){e.attr(t,null)}))},N_=function(e,t,n){e.addNodeFilter("font",(function(e){Z(e,(function(e){var r=t.parse(e.attr("style")),o=e.attr("color"),i=e.attr("face"),a=e.attr("size");o&&(r.color=o),i&&(r["font-family"]=i),a&&(r["font-size"]=n[parseInt(e.attr("size"),10)-1]),e.name="span",e.attr("style",t.serialize(r)),k_(e,["color","face","size"])}))}))},E_=function(e,t){e.addNodeFilter("strike",(function(e){Z(e,(function(e){var n=t.parse(e.attr("style"));n["text-decoration"]="line-through",e.name="span",e.attr("style",t.serialize(n))}))}))},__=function(e,t){var n=Vu();t.convert_fonts_to_spans&&N_(e,n,gn.explode(t.font_size_legacy_values)),E_(e,n)},A_=function(e,t){t.inline_styles&&__(e,t)},R_=function(e){return new Ri((function(t,n){var r=function(){n("Cannot convert "+e+" to Blob. Resource might not exist or is inaccessible.")};try{var o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){200===o.status?t(o.response):r()},o.onerror=r,o.send()}catch(i){r()}}))},D_=function(e){var t,n=decodeURIComponent(e).split(","),r=/data:([^;]+)/.exec(n[0]);return r&&(t=r[1]),{type:t,data:n[1]}},T_=function(e,t){var n;try{n=atob(t)}catch(DH){return q.none()}for(var r=new Uint8Array(n.length),o=0;o<r.length;o++)r[o]=n.charCodeAt(o);return q.some(new Blob([r],{type:e}))},O_=function(e){return new Ri((function(t){var n=D_(e),r=n.type,o=n.data;T_(r,o).fold((function(){return t(new Blob([]))}),t)}))},B_=function(e){return 0===e.indexOf("blob:")?R_(e):0===e.indexOf("data:")?O_(e):null},P_=function(e){return new Ri((function(t){var n=new FileReader;n.onloadend=function(){t(n.result)},n.readAsDataURL(e)}))},L_=0,I_=function(e){return(e||"blobid")+L_++},M_=function(e,t,n,r){var o,i;if(0===t.src.indexOf("blob:"))return i=e.getByUri(t.src),void(i?n({image:t,blobInfo:i}):B_(t.src).then((function(r){P_(r).then((function(a){o=D_(a).data,i=e.create(I_(),r,o),e.add(i),n({image:t,blobInfo:i})}))}),(function(e){r(e)})));var a=D_(t.src),u=a.data,c=a.type;o=u,i=e.getByData(o,c),i?n({image:t,blobInfo:i}):B_(t.src).then((function(r){i=e.create(I_(),r,o),e.add(i),n({image:t,blobInfo:i})}),(function(e){r(e)}))},F_=function(e){return e?be(e.getElementsByTagName("img")):[]},U_=function(e,t){var n={},r=function(r,o){o||(o=U);var i=ne(F_(r),(function(t){var n=t.src;return!!en.fileApi&&(!t.hasAttribute("data-mce-bogus")&&(!t.hasAttribute("data-mce-placeholder")&&(!(!n||n===en.transparentSrc)&&(0===n.indexOf("blob:")?!e.isUploaded(n)&&o(t):0===n.indexOf("data:")&&o(t)))))})),a=Q(i,(function(e){if(void 0!==n[e.src])return new Ri((function(t){n[e.src].then((function(n){if("string"===typeof n)return n;t({image:e,blobInfo:n.blobInfo})}))}));var r=new Ri((function(n,r){M_(t,e,n,r)})).then((function(e){return delete n[e.image.src],e})).catch((function(t){return delete n[e.src],t}));return n[e.src]=r,r}));return Ri.all(a)};return{findAll:r}},z_=function(e){var t,n=/data:[^;<"'\s]+;base64,([a-z0-9\+\/=\s]+)/gi,r=[],o={},i=zf("img"),a=0,u=0;while(t=n.exec(e)){var c=t[0],s=i+"_"+u++;o[s]=c,a<t.index&&r.push(e.substr(a,t.index-a)),r.push(s),a=t.index+c.length}var f=new RegExp(i+"_[0-9]+","g");return 0===a?{prefix:i,uris:o,html:e,re:f}:(a<e.length&&r.push(e.substr(a)),{prefix:i,uris:o,html:r.join(""),re:f})},H_=function(e,t){return e.replace(t.re,(function(e){return Te(t.uris,e).getOr(e)}))},j_=function(e){var t=/data:([^;]+);base64,([a-z0-9\+\/=\s]+)/i.exec(e);return t?q.some({type:t[1],data:decodeURIComponent(t[2])}):q.none()},V_=function(e,t,n,r){var o=e.padd_empty_with_br||t.insert;o&&n[r.name]?r.empty().append(new Ry("br",1)).shortEnded=!0:r.empty().append(new Ry("#text",3)).value=sa},q_=function(e){return $_(e,"#text")&&e.firstChild.value===sa},$_=function(e,t){return e&&e.firstChild&&e.firstChild===e.lastChild&&e.firstChild.name===t},W_=function(e,t){var n=e.getElementRule(t.name);return n&&n.paddEmpty},K_=function(e,t,n,r){return r.isEmpty(t,n,(function(t){return W_(e,t)}))},X_=function(e,t){return e&&(Oe(t,e.name)||"br"===e.name)},Y_=function(e){return k(e.attr("data-mce-bogus"))},G_=function(e){return e.attr("src")===en.transparentSrc||k(e.attr("data-mce-placeholder"))},J_=function(e,t){if(t.images_dataimg_filter){var n=new Image;return n.src=e.attr("src"),xe(e.attributes.map,(function(e,t){n.setAttribute(t,e)})),t.images_dataimg_filter(n)}return!0},Q_=function(e,t){var n=t.blob_cache,r=function(e){var r=e.attr("src");G_(e)||Y_(e)||j_(r).filter((function(){return J_(e,t)})).bind((function(e){var t=e.type,r=e.data;return q.from(n.getByData(r,t)).orThunk((function(){return T_(t,r).map((function(e){var t=n.create(I_(),e,r);return n.add(t),t}))}))})).each((function(t){e.attr("src",t.blobUri())}))};n&&e.addAttributeFilter("src",(function(e){return Z(e,r)}))},Z_=function(e,t){var n=e.schema;t.remove_trailing_brs&&e.addNodeFilter("br",(function(e,r,o){var i,a,u,c,s,f,l,d,m=e.length,g=gn.extend({},n.getBlockElements()),p=n.getNonEmptyElements(),h=n.getWhiteSpaceElements();for(g.body=1,i=0;i<m;i++)if(a=e[i],u=a.parent,g[a.parent.name]&&a===u.lastChild){s=a.prev;while(s){if(f=s.name,"span"!==f||"bookmark"!==s.attr("data-mce-type")){"br"===f&&(a=null);break}s=s.prev}a&&(a.remove(),K_(n,p,h,u)&&(l=n.getElementRule(u.name),l&&(l.removeEmpty?u.remove():l.paddEmpty&&V_(t,o,g,u))))}else{c=a;while(u&&u.firstChild===c&&u.lastChild===c){if(c=u,g[u.name])break;u=u.parent}c===u&&!0!==t.padd_empty_with_br&&(d=new Ry("#text",3),d.value=sa,a.replace(d))}})),e.addAttributeFilter("href",(function(e){var n=e.length,r=function(e){var t=e.split(" ").filter((function(e){return e.length>0}));return t.concat(["noopener"]).sort().join(" ")},o=function(e){var t=e?gn.trim(e):"";return/\b(noopener)\b/g.test(t)?t:r(t)};if(!t.allow_unsafe_link_target)while(n--){var i=e[n];"a"===i.name&&"_blank"===i.attr("target")&&i.attr("rel",o(i.attr("rel")))}})),t.allow_html_in_named_anchor||e.addAttributeFilter("id,name",(function(e){var t,n,r,o,i=e.length;while(i--)if(o=e[i],"a"===o.name&&o.firstChild&&!o.attr("href")){r=o.parent,t=o.lastChild;do{n=t.prev,r.insert(t,o),t=n}while(t)}})),t.fix_list_elements&&e.addNodeFilter("ul,ol",(function(e){var t,n,r=e.length;while(r--)if(t=e[r],n=t.parent,"ul"===n.name||"ol"===n.name)if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{var o=new Ry("li",1);o.attr("style","list-style-type: none"),t.wrap(o)}})),t.validate&&n.getValidClasses()&&e.addAttributeFilter("class",(function(e){var t=n.getValidClasses(),r=e.length;while(r--){for(var o=e[r],i=o.attr("class").split(" "),a="",u=0;u<i.length;u++){var c=i[u],s=!1,f=t["*"];f&&f[c]&&(s=!0),f=t[o.name],!s&&f&&f[c]&&(s=!0),s&&(a&&(a+=" "),a+=c)}a.length||(a=null),o.attr("class",a)}})),Q_(e,t)},eA=gn.each,tA=gn.trim,nA="source protocol authority userInfo user password host port relative path directory file query anchor".split(" "),rA={ftp:21,http:80,https:443,mailto:25},oA=["img","video"],iA=function(e,t){return k(e)?!e:!k(t)||!G(oA,t)},aA=function(e,t,n){return!e.allow_html_data_urls&&(/^data:image\//i.test(t)?iA(e.allow_svg_data_urls,n)&&/^data:image\/svg\+xml/i.test(t):/^data:/i.test(t))},uA=function(){function e(t,n){t=tA(t),this.settings=n||{};var r=this.settings.base_uri,o=this;if(/^([\w\-]+):([^\/]{2})/i.test(t)||/^\s*#/.test(t))o.source=t;else{var i=0===t.indexOf("//");if(0!==t.indexOf("/")||i||(t=(r&&r.protocol||"http")+"://mce_host"+t),!/^[\w\-]*:?\/\//.test(t)){var a=this.settings.base_uri?this.settings.base_uri.path:new e(document.location.href).directory;if(this.settings.base_uri&&""==this.settings.base_uri.protocol)t="//mce_host"+o.toAbsPath(a,t);else{var u=/([^#?]*)([#?]?.*)/.exec(t);t=(r&&r.protocol||"http")+"://mce_host"+o.toAbsPath(a,u[1])+u[2]}}t=t.replace(/@@/g,"(mce_at)");var c=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?(\[[a-zA-Z0-9:.%]+\]|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(t);eA(nA,(function(e,t){var n=c[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n})),r&&(o.protocol||(o.protocol=r.protocol),o.userInfo||(o.userInfo=r.userInfo),o.port||"mce_host"!==o.host||(o.port=r.port),o.host&&"mce_host"!==o.host||(o.host=r.host),o.source=""),i&&(o.protocol="")}}return e.parseDataUri=function(e){var t,n=decodeURIComponent(e).split(","),r=/data:([^;]+)/.exec(n[0]);return r&&(t=r[1]),{type:t,data:n[1]}},e.isDomSafe=function(e,t,n){if(void 0===n&&(n={}),n.allow_script_urls)return!0;var r=Au.decode(e).replace(/[\s\u0000-\u001F]+/g,"");try{r=decodeURIComponent(r)}catch(o){r=unescape(r)}return!/((java|vb)script|mhtml):/i.test(r)&&!aA(n,r,t)},e.getDocumentBaseUrl=function(e){var t;return t=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?e.href:e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),t},e.prototype.setPath=function(e){var t=/^(.*?)\/?(\w+)?$/.exec(e);this.path=t[0],this.directory=t[1],this.file=t[2],this.source="",this.getURI()},e.prototype.toRelative=function(t){var n;if("./"===t)return t;var r=new e(t,{base_uri:this});if("mce_host"!==r.host&&this.host!==r.host&&r.host||this.port!==r.port||this.protocol!==r.protocol&&""!==r.protocol)return r.getURI();var o=this.getURI(),i=r.getURI();return o===i||"/"===o.charAt(o.length-1)&&o.substr(0,o.length-1)===i?o:(n=this.toRelPath(this.path,r.path),r.query&&(n+="?"+r.query),r.anchor&&(n+="#"+r.anchor),n)},e.prototype.toAbsolute=function(t,n){var r=new e(t,{base_uri:this});return r.getURI(n&&this.isSameOrigin(r))},e.prototype.isSameOrigin=function(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;var t=rA[this.protocol];if(t&&(this.port||t)==(e.port||t))return!0}return!1},e.prototype.toRelPath=function(e,t){var n,r,o=0,i="",a=e.substring(0,e.lastIndexOf("/")).split("/"),u=t.split("/");if(a.length>=u.length)for(n=0,r=a.length;n<r;n++)if(n>=u.length||a[n]!==u[n]){o=n+1;break}if(a.length<u.length)for(n=0,r=u.length;n<r;n++)if(n>=a.length||a[n]!==u[n]){o=n+1;break}if(1===o)return t;for(n=0,r=a.length-(o-1);n<r;n++)i+="../";for(n=o-1,r=u.length;n<r;n++)i+=n!==o-1?"/"+u[n]:u[n];return i},e.prototype.toAbsPath=function(e,t){var n,r,o=0,i=[],a=/\/$/.test(t)?"/":"",u=e.split("/"),c=t.split("/");for(eA(u,(function(e){e&&i.push(e)})),u=i,n=c.length-1,i=[];n>=0;n--)0!==c[n].length&&"."!==c[n]&&(".."!==c[n]?o>0?o--:i.push(c[n]):o++);return n=u.length-o,r=n<=0?le(i).join("/"):u.slice(0,n).join("/")+"/"+le(i).join("/"),0!==r.indexOf("/")&&(r="/"+r),a&&r.lastIndexOf("/")!==r.length-1&&(r+=a),r},e.prototype.getURI=function(e){var t;return void 0===e&&(e=!1),this.source&&!e||(t="",e||(this.protocol?t+=this.protocol+"://":t+="//",this.userInfo&&(t+=this.userInfo+"@"),this.host&&(t+=this.host),this.port&&(t+=":"+this.port)),this.path&&(t+=this.path),this.query&&(t+="?"+this.query),this.anchor&&(t+="#"+this.anchor),this.source=t),this.source},e}(),cA=gn.makeMap("button,fieldset,form,iframe,img,image,input,object,output,select,textarea"),sA=function(e){return 0===e.indexOf("data-")||0===e.indexOf("aria-")},fA=Ke((function(){return document.implementation.createHTMLDocument("parser")})),lA=function(e,t,n){var r=/<([!?\/])?([A-Za-z0-9\-_:.]+)/g,o=/(?:\s(?:[^'">]+(?:"[^"]*"|'[^']*'))*[^"'>]*(?:"[^">]*|'[^'>]*)?|\s*|\/)>/g,i=e.getShortEndedElements(),a=1,u=n;while(0!==a){r.lastIndex=u;while(1){var c=r.exec(t);if(null===c)return u;if("!"===c[1]){u=st(c[2],"--")?mA(t,!1,c.index+"!--".length):mA(t,!0,c.index+1);break}o.lastIndex=r.lastIndex;var s=o.exec(t);if(!C(s)&&s.index===r.lastIndex){"/"===c[1]?a-=1:Oe(i,c[2])||(a+=1),u=r.lastIndex+s[0].length;break}}}return u},dA=function(e,t){return/^\s*\[if [\w\W]+\]>.*<!\[endif\](--!?)?>/.test(e.substr(t))},mA=function(e,t,n){void 0===n&&(n=0);var r=e.toLowerCase();if(-1!==r.indexOf("[if ",n)&&dA(r,n)){var o=r.indexOf("[endif]",n);return r.indexOf(">",o)}if(t){var i=r.indexOf(">",n);return-1!==i?i:r.length}var a=/--!?>/g;a.lastIndex=n;var u=a.exec(e);return u?u.index+u[0].length:r.length},gA=function(e,t){var n=e.exec(t);if(n){var r=n[1],o=n[2];return"string"===typeof r&&"data-mce-bogus"===r.toLowerCase()?o:null}return null},pA=function(e,t){void 0===t&&(t=Hu()),e=e||{};var n=fA(),r=n.createElement("form");!1!==e.fix_self_closing&&(e.fix_self_closing=!0);var o=e.comment?e.comment:_,i=e.cdata?e.cdata:_,a=e.text?e.text:_,u=e.start?e.start:_,c=e.end?e.end:_,s=e.pi?e.pi:_,f=e.doctype?e.doctype:_,l=function(l,d){void 0===d&&(d="html");var m,g,p,h,v,b,y,C,w,x,S,k,N,E,_,A,R,D,T,O,B=l.html,P=0,L=[],I=0,M=Au.decode,F=gn.makeMap("src,href,data,background,action,formaction,poster,xlink:href"),U="html"===d?0:1,z=function(e){var t,n;t=L.length;while(t--)if(L[t].name===e)break;if(t>=0){for(n=L.length-1;n>=t;n--)e=L[n],e.valid&&c(e.name);L.length=t}},H=function(e,t){return a(H_(e,l),t)},j=function(t){""!==t&&(">"===t.charAt(0)&&(t=" "+t),e.allow_conditional_comments||"[if"!==t.substr(0,3).toLowerCase()||(t=" "+t),o(H_(t,l)))},V=function(e){return H_(e,l)},q=function(e,t){var n=e||"",r=!st(n,"--"),o=mA(B,r,t);return e=B.substr(t,o-t),j(r?n+e:e),o+1},$=function(t,o,i,a,u){if(o=o.toLowerCase(),i=V(o in G?o:M(i||a||u||"")),J&&!C&&!1===sA(o)){var c=E[o];if(!c&&_){var s=_.length;while(s--)if(c=_[s],c.pattern.test(o))break;-1===s&&(c=null)}if(!c)return;if(c.validValues&&!(i in c.validValues))return}var f="name"===o||"id"===o;f&&t in cA&&(i in n||i in r)||F[o]&&!uA.isDomSafe(i,t,e)||C&&(o in F||0===o.indexOf("on"))||(h.map[o]=i,h.push({name:o,value:i}))},W=new RegExp("<(?:(?:!--([\\w\\W]*?)--!?>)|(?:!\\[CDATA\\[([\\w\\W]*?)\\]\\]>)|(?:![Dd][Oo][Cc][Tt][Yy][Pp][Ee]([\\w\\W]*?)>)|(?:!(--)?)|(?:\\?([^\\s\\/<>]+) ?([\\w\\W]*?)[?/]>)|(?:\\/([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)>)|(?:([A-Za-z][A-Za-z0-9\\-_:.]*)(\\s(?:[^'\">]+(?:\"[^\"]*\"|'[^']*'))*[^\"'>]*(?:\"[^\">]*|'[^'>]*)?|\\s*|\\/)>))","g"),K=/([\w:\-]+)(?:\s*=\s*(?:(?:\"((?:[^\"])*)\")|(?:\'((?:[^\'])*)\')|([^>\s]+)))?/g,X=t.getShortEndedElements(),Y=e.self_closing_elements||t.getSelfClosingElements(),G=t.getBoolAttrs(),J=e.validate,Q=e.remove_internals,Z=e.fix_self_closing,ee=t.getSpecialElements(),te=B+">";while(m=W.exec(te)){var ne=m[0];if(P<m.index&&H(M(B.substr(P,m.index-P))),g=m[7])g=g.toLowerCase(),":"===g.charAt(0)&&(g=g.substr(1)),z(g);else if(g=m[8]){if(m.index+ne.length>B.length){H(M(B.substr(m.index))),P=m.index+ne.length;continue}g=g.toLowerCase(),":"===g.charAt(0)&&(g=g.substr(1)),w=g in X,Z&&Y[g]&&L.length>0&&L[L.length-1].name===g&&z(g);var re=gA(K,m[9]);if(null!==re){if("all"===re){P=lA(t,B,W.lastIndex),W.lastIndex=P;continue}S=!1}if(!J||(x=t.getElementRule(g))){if(S=!0,J&&(E=x.attributes,_=x.attributePatterns),(N=m[9])?(C=-1!==N.indexOf("data-mce-type"),C&&Q&&(S=!1),h=[],h.map={},N.replace(K,(function(e,t,n,r,o){return $(g,t,n,r,o),""}))):(h=[],h.map={}),J&&!C){if(A=x.attributesRequired,R=x.attributesDefault,D=x.attributesForced,T=x.removeEmptyAttrs,T&&!h.length&&(S=!1),D){v=D.length;while(v--)k=D[v],y=k.name,O=k.value,"{$uid}"===O&&(O="mce_"+I++),h.map[y]=O,h.push({name:y,value:O})}if(R){v=R.length;while(v--)k=R[v],y=k.name,y in h.map||(O=k.value,"{$uid}"===O&&(O="mce_"+I++),h.map[y]=O,h.push({name:y,value:O}))}if(A){v=A.length;while(v--)if(A[v]in h.map)break;-1===v&&(S=!1)}if(k=h.map["data-mce-bogus"]){if("all"===k){P=lA(t,B,W.lastIndex),W.lastIndex=P;continue}S=!1}}S&&u(g,h,w)}else S=!1;if(p=ee[g]){p.lastIndex=P=m.index+ne.length,(m=p.exec(B))?(S&&(b=B.substr(P,m.index-P)),P=m.index+m[0].length):(b=B.substr(P),P=B.length),S&&(b.length>0&&H(b,!0),c(g)),W.lastIndex=P;continue}w||(N&&N.indexOf("/")===N.length-1?S&&c(g):L.push({name:g,valid:S}))}else if(g=m[1])j(g);else if(g=m[2]){var oe=1===U||e.preserve_cdata||L.length>0&&t.isValidChild(L[L.length-1].name,"#cdata");if(!oe){P=q("",m.index+2),W.lastIndex=P;continue}i(g)}else if(g=m[3])f(g);else{if((g=m[4])||"<!"===ne){P=q(g,m.index+ne.length),W.lastIndex=P;continue}if(g=m[5]){if(1!==U){P=q("?",m.index+2),W.lastIndex=P;continue}s(g,m[6])}}P=m.index+ne.length}for(P<B.length&&H(M(B.substr(P))),v=L.length-1;v>=0;v--)g=L[v],g.valid&&c(g.name)},d=function(e,t){void 0===t&&(t="html"),l(z_(e),t)};return{parse:d}};pA.findEndTag=lA;var hA=gn.makeMap,vA=gn.each,bA=gn.explode,yA=gn.extend,CA=function(e,t){void 0===t&&(t=Hu());var n={},r=[],o={},i={};e=e||{},e.validate=!("validate"in e)||e.validate,e.root_name=e.root_name||"body";var a=function(e){for(var n=hA("tr,td,th,tbody,thead,tfoot,table"),r=t.getNonEmptyElements(),o=t.getWhiteSpaceElements(),i=t.getTextBlockElements(),a=t.getSpecialElements(),c=function(e,n){if(void 0===n&&(n=e.parent),a[e.name])e.empty().remove();else{for(var r=e.children(),o=0,i=r;o<i.length;o++){var u=i[o];t.isValidChild(n.name,u.name)||c(u,n)}e.unwrap()}},s=0;s<e.length;s++){var f=e[s],l=void 0,d=void 0,m=void 0;if(f.parent&&!f.fixed)if(i[f.name]&&"li"===f.parent.name){var g=f.next;while(g){if(!i[g.name])break;g.name="li",g.fixed=!0,f.parent.insert(g,f.parent),g=g.next}f.unwrap()}else{var p=[f];for(l=f.parent;l&&!t.isValidChild(l.name,f.name)&&!n[l.name];l=l.parent)p.push(l);if(l&&p.length>1)if(t.isValidChild(l.name,f.name)){p.reverse(),d=u(p[0].clone());for(var h=d,v=0;v<p.length-1;v++){t.isValidChild(h.name,p[v].name)?(m=u(p[v].clone()),h.append(m)):m=h;for(var b=p[v].firstChild;b&&b!==p[v+1];){var y=b.next;m.append(b),b=y}h=m}K_(t,r,o,d)?l.insert(f,p[0],!0):(l.insert(d,p[0],!0),l.insert(f,d)),l=p[0],(K_(t,r,o,l)||$_(l,"br"))&&l.empty().remove()}else c(f);else if(f.parent){if("li"===f.name){g=f.prev;if(g&&("ul"===g.name||"ol"===g.name)){g.append(f);continue}if(g=f.next,g&&("ul"===g.name||"ol"===g.name)){g.insert(f,g.firstChild,!0);continue}f.wrap(u(new Ry("ul",1)));continue}t.isValidChild(f.parent.name,"div")&&t.isValidChild("div",f.name)?f.wrap(u(new Ry("div",1))):c(f)}}}},u=function(e){var t=e.name;if(t in n){var a=o[t];a?a.push(e):o[t]=[e]}var u=r.length;while(u--){var c=r[u].name;if(c in e.attributes.map){a=i[c];a?a.push(e):i[c]=[e]}}return e},c=function(e,t){vA(bA(e),(function(e){var r=n[e];r||(n[e]=r=[]),r.push(t)}))},s=function(){var e=[];for(var t in n)Oe(n,t)&&e.push({name:t,callbacks:n[t]});return e},f=function(e,t){vA(bA(e),(function(e){var n;for(n=0;n<r.length;n++)if(r[n].name===e)return void r[n].callbacks.push(t);r.push({name:e,callbacks:[t]})}))},l=function(){return[].concat(r)},d=function(u,c){var s,f,l,d,m,g,p,h,v=[],b=function(e){return!1===e?"":!0===e?"p":e};c=c||{},o={},i={};var y=yA(hA("script,style,head,html,body,title,meta,param"),t.getBlockElements()),C=Fu(t),w=t.getNonEmptyElements(),x=t.children,S=e.validate,N="forced_root_block"in c?c.forced_root_block:e.forced_root_block,E=b(N),_=t.getWhiteSpaceElements(),A=/^[ \t\r\n]+/,R=/[ \t\r\n]+$/,D=/[ \t\r\n]+/g,T=/^[ \t\r\n]+$/,O=Oe(_,c.context)||Oe(_,e.root_name),B=function(){var n=U.firstChild,r=null,o=function(e){e&&(n=e.firstChild,n&&3===n.type&&(n.value=n.value.replace(A,"")),n=e.lastChild,n&&3===n.type&&(n.value=n.value.replace(R,"")))};if(t.isValidChild(U.name,E.toLowerCase())){while(n){var i=n.next;3===n.type||1===n.type&&"p"!==n.name&&!y[n.name]&&!n.attr("data-mce-type")?(r||(r=P(E,1),r.attr(e.forced_root_block_attrs),U.insert(r,n)),r.append(n)):(o(r),r=null),n=i}o(r)}},P=function(e,t){var r,i=new Ry(e,t);return e in n&&(r=o[e],r?r.push(i):o[e]=[i]),i},L=function(e){for(var n=t.getBlockElements(),r=e.prev;r&&3===r.type;){var o=r.value.replace(R,"");if(o.length>0)return void(r.value=o);var i=r.next;if(i){if(3===i.type&&i.value.length){r=r.prev;continue}if(!n[i.name]&&"script"!==i.name&&"style"!==i.name){r=r.prev;continue}}var a=r.prev;r.remove(),r=a}},I=function(e){var t={};for(var n in e)"li"!==n&&"p"!==n&&(t[n]=e[n]);return t},M=function(e){var n=e;while(k(n)){if(n.name in C)return K_(t,w,_,n);n=n.parent}return!1},F=pA({validate:S,document:e.document,allow_html_data_urls:e.allow_html_data_urls,allow_svg_data_urls:e.allow_svg_data_urls,allow_script_urls:e.allow_script_urls,allow_conditional_comments:e.allow_conditional_comments,preserve_cdata:e.preserve_cdata,self_closing_elements:I(t.getSelfClosingElements()),cdata:function(e){h.append(P("#cdata",4)).value=e},text:function(e,t){var n;O||(e=e.replace(D," "),X_(h.lastChild,y)&&(e=e.replace(A,""))),0!==e.length&&(n=P("#text",3),n.raw=!!t,h.append(n).value=e)},comment:function(e){h.append(P("#comment",8)).value=e},pi:function(e,t){h.append(P(e,7)).value=t,L(h)},doctype:function(e){var t=h.append(P("#doctype",10));t.value=e,L(h)},start:function(e,n,o){var a=S?t.getElementRule(e):{};if(a){var u=P(a.outputName||e,1);u.attributes=n,u.shortEnded=o,h.append(u);var c=x[h.name];c&&x[u.name]&&!c[u.name]&&v.push(u);var s=r.length;while(s--){var f=r[s].name;f in n.map&&(g=i[f],g?g.push(u):i[f]=[u])}y[e]&&L(u),o||(h=u),!O&&_[e]&&(O=!0)}},end:function(n){var r,o,i,a=S?t.getElementRule(n):{};if(a){if(y[n]&&!O){if(r=h.firstChild,r&&3===r.type)if(o=r.value.replace(A,""),o.length>0)r.value=o,r=r.next;else{i=r.next,r.remove(),r=i;while(r&&3===r.type)o=r.value,i=r.next,(0===o.length||T.test(o))&&(r.remove(),r=i),r=i}if(r=h.lastChild,r&&3===r.type)if(o=r.value.replace(R,""),o.length>0)r.value=o,r=r.prev;else{i=r.prev,r.remove(),r=i;while(r&&3===r.type)o=r.value,i=r.prev,(0===o.length||T.test(o))&&(r.remove(),r=i),r=i}}O&&_[n]&&(O=!1);var u=K_(t,w,_,h),s=h.parent;a.paddInEmptyBlock&&u&&M(h)?V_(e,c,y,h):a.removeEmpty&&u?y[h.name]?h.empty().remove():h.unwrap():a.paddEmpty&&(q_(h)||u)&&V_(e,c,y,h),h=s}}},t),U=h=new Ry(c.context||e.root_name,11);if(F.parse(u,c.format),S&&v.length&&(c.context?c.invalid=!0:a(v)),E&&("body"===U.name||c.isRootContent)&&B(),!c.invalid){for(p in o)if(Oe(o,p)){g=n[p],s=o[p],d=s.length;while(d--)s[d].parent||s.splice(d,1);for(f=0,l=g.length;f<l;f++)g[f](s,p,c)}for(f=0,l=r.length;f<l;f++)if(g=r[f],g.name in i){s=i[g.name],d=s.length;while(d--)s[d].parent||s.splice(d,1);for(d=0,m=g.callbacks.length;d<m;d++)g.callbacks[d](s,g.name,c)}}return U},m={schema:t,addAttributeFilter:f,getAttributeFilters:l,addNodeFilter:c,getNodeFilters:s,filterNode:u,parse:d};return Z_(m,e),A_(m,e),m},wA=function(e,t,n){e.addAttributeFilter("data-mce-tabindex",(function(e,t){var n=e.length;while(n--){var r=e[n];r.attr("tabindex",r.attr("data-mce-tabindex")),r.attr(t,null)}})),e.addAttributeFilter("src,href,style",(function(e,r){var o="data-mce-"+r,i=t.url_converter,a=t.url_converter_scope,u=e.length;while(u--){var c=e[u],s=c.attr(o);void 0!==s?(c.attr(r,s.length>0?s:null),c.attr(o,null)):(s=c.attr(r),"style"===r?s=n.serializeStyle(n.parseStyle(s),c.name):i&&(s=i.call(a,s,r,c.name)),c.attr(r,s.length>0?s:null))}})),e.addAttributeFilter("class",(function(e){var t=e.length;while(t--){var n=e[t],r=n.attr("class");r&&(r=n.attr("class").replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),n.attr("class",r.length>0?r:null))}})),e.addAttributeFilter("data-mce-type",(function(e,t,n){var r=e.length;while(r--){var o=e[r];if("bookmark"===o.attr("data-mce-type")&&!n.cleanup){var i=q.from(o.firstChild).exists((function(e){return!ma(e.value)}));i?o.unwrap():o.remove()}}})),e.addNodeFilter("noscript",(function(e){var t=e.length;while(t--){var n=e[t].firstChild;n&&(n.value=Au.decode(n.value))}})),e.addNodeFilter("script,style",(function(e,n){var r=function(e){return e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"")},o=e.length;while(o--){var i=e[o],a=i.firstChild?i.firstChild.value:"";if("script"===n){var u=i.attr("type");u&&i.attr("type","mce-no/type"===u?null:u.replace(/^mce\-/,"")),"xhtml"===t.element_format&&a.length>0&&(i.firstChild.value="// <![CDATA[\n"+r(a)+"\n// ]]>")}else"xhtml"===t.element_format&&a.length>0&&(i.firstChild.value="\x3c!--\n"+r(a)+"\n--\x3e")}})),e.addNodeFilter("#comment",(function(e){var r=e.length;while(r--){var o=e[r];t.preserve_cdata&&0===o.value.indexOf("[CDATA[")?(o.name="#cdata",o.type=4,o.value=n.decode(o.value.replace(/^\[CDATA\[|\]\]$/g,""))):0===o.value.indexOf("mce:protected ")&&(o.name="#text",o.type=3,o.raw=!0,o.value=unescape(o.value).substr(14))}})),e.addNodeFilter("xml:namespace,input",(function(e,t){var n=e.length;while(n--){var r=e[n];7===r.type?r.remove():1===r.type&&("input"!==t||r.attr("type")||r.attr("type","text"))}})),e.addAttributeFilter("data-mce-type",(function(t){Z(t,(function(t){"format-caret"===t.attr("data-mce-type")&&(t.isEmpty(e.schema.getNonEmptyElements())?t.remove():t.unwrap())}))})),e.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-type,data-mce-resize,data-mce-placeholder",(function(e,t){var n=e.length;while(n--)e[n].attr(t,null)}))},xA=function(e){var t=function(e){return e&&"br"===e.name},n=e.lastChild;if(t(n)){var r=n.prev;t(r)&&(n.remove(),r.remove())}},SA=function(e,t,n){var r,o=e.dom,i=t.cloneNode(!0),a=document.implementation;if(a.createHTMLDocument){var u=a.createHTMLDocument("");gn.each("BODY"===i.nodeName?i.childNodes:[i],(function(e){u.body.appendChild(u.importNode(e,!0))})),i="BODY"!==i.nodeName?u.body.firstChild:u.body,r=o.doc,o.doc=u}return Hh(e,qe(qe({},n),{node:i})),r&&(o.doc=r),i},kA=function(e,t){return e&&e.hasEventListeners("PreProcess")&&!t.no_events},NA=function(e,t,n){return kA(e,n)?SA(e,t,n):t},EA=function(e,t,n){-1===gn.inArray(t,n)&&(e.addAttributeFilter(n,(function(e,t){var n=e.length;while(n--)e[n].attr(t,null)})),t.push(n))},_A=function(e,t,n){if(!t.no_events&&e){var r=jh(e,qe(qe({},t),{content:n}));return r.content}return n},AA=function(e,t,n){var r=ga(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection||aa(Cn.fromDom(t))?r:gn.trim(r)},RA=function(e,t,n){var r=n.selection?qe({forced_root_block:!1},n):n,o=e.parse(t,r);return xA(o),o},DA=function(e,t,n){var r=Zy(e,t);return r.serialize(n)},TA=function(e,t,n,r,o){var i=DA(t,n,r);return _A(e,o,i)},OA=function(e,t){var n=["data-mce-selected"],r=t&&t.dom?t.dom:Hs.DOM,o=t&&t.schema?t.schema:Hu(e);e.entity_encoding=e.entity_encoding||"named",e.remove_trailing_brs=!("remove_trailing_brs"in e)||e.remove_trailing_brs;var i=CA(e,o);wA(i,e,r);var a=function(n,a){void 0===a&&(a={});var u=qe({format:"html"},a),c=NA(t,n,u),s=AA(r,c,u),f=RA(i,s,u);return"tree"===u.format?f:TA(t,e,o,f,u)};return{schema:o,addNodeFilter:i.addNodeFilter,addAttributeFilter:i.addAttributeFilter,serialize:a,addRules:o.addValidElements,setRules:o.setValidElements,addTempAttr:B(EA,i,n),getTempAttrs:D(n),getNodeFilters:i.getNodeFilters,getAttributeFilters:i.getAttributeFilters}},BA=function(e,t){var n=OA(e,t);return{schema:n.schema,addNodeFilter:n.addNodeFilter,addAttributeFilter:n.addAttributeFilter,serialize:n.serialize,addRules:n.addRules,setRules:n.setRules,addTempAttr:n.addTempAttr,getTempAttrs:n.getTempAttrs,getNodeFilters:n.getNodeFilters,getAttributeFilters:n.getAttributeFilters}},PA="html",LA=function(e,t){void 0===t&&(t={});var n=t.format?t.format:PA;return r_(e,t,n)},IA=function(e,t,n){return void 0===n&&(n={}),o_(e,t,n)},MA=Hs.DOM,FA=function(e){MA.setStyle(e.id,"display",e.orgDisplay)},UA=function(e){return q.from(e).each((function(e){return e.destroy()}))},zA=function(e){e.contentAreaContainer=e.formElement=e.container=e.editorContainer=null,e.bodyElement=e.contentDocument=e.contentWindow=null,e.iframeElement=e.targetElm=null,e.selection&&(e.selection=e.selection.win=e.selection.dom=e.selection.dom.doc=null)},HA=function(e){var t=e.formElement;t&&(t._mceOldSubmit&&(t.submit=t._mceOldSubmit,t._mceOldSubmit=null),MA.unbind(t,"submit reset",e.formEventDelegate))},jA=function(e){if(!e.removed){var t=e._selectionOverrides,n=e.editorUpload,r=e.getBody(),o=e.getElement();r&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&o&&MA.remove(o.nextSibling),Vh(e),e.editorManager.remove(e),!e.inline&&r&&FA(e),qh(e),MA.remove(e.getContainer()),UA(t),UA(n),e.destroy()}},VA=function(e,t){var n=e.selection,r=e.dom;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),UA(n),UA(r)),HA(e),zA(e),e.destroyed=!0):e.remove())},qA=function(e,t){var n=b(e)&&b(t);return n?WA(e,t):t},$A=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var r={},o=0;o<t.length;o++){var i=t[o];for(var a in i)Oe(i,a)&&(r[a]=e(r[a],i[a]))}return r}},WA=$A(qA),KA="autoresize_on_init,content_editable_state,convert_fonts_to_spans,inline_styles,padd_empty_with_br,block_elements,boolean_attributes,editor_deselector,editor_selector,elements,file_browser_callback_types,filepicker_validator_handler,force_hex_style_colors,force_p_newlines,gecko_spellcheck,images_dataimg_filter,media_scripts,mode,move_caret_before_on_enter_elements,non_empty_elements,self_closing_elements,short_ended_elements,special,spellchecker_select_languages,spellchecker_whitelist,tab_focus,table_responsive_width,text_block_elements,text_inline_elements,toolbar_drawer,types,validate,whitespace_elements,paste_word_valid_elements,paste_retain_style_properties,paste_convert_word_fake_lists".split(","),XA="bbcode,colorpicker,contextmenu,fullpage,legacyoutput,spellchecker,textcolor".split(","),YA="imagetools,toc".split(","),GA=function(e){var t=ne(KA,(function(t){return Oe(e,t)})),n=e.forced_root_block;return!1!==n&&""!==n||t.push("forced_root_block (false only)"),ge(t)},JA=function(e){var t=gn.makeMap(e.plugins," "),n=function(e){return Oe(t,e)},r=We(We([],ne(XA,n),!0),se(YA,(function(e){return n(e)?[e+" (moving to premium)"]:[]})),!0);return ge(r)},QA=function(e,t){var n=GA(e),r=JA(t),o=r.length>0,i=n.length>0,a="mobile"===t.theme;if(o||i||a){var u="\n- ";o&&r.join(u),i&&n.join(u)}},ZA=function(e,t){return{sections:D(e),settings:D(t)}},eR=$t().deviceType,tR=eR.isTouch(),nR=eR.isPhone(),rR=eR.isTablet(),oR=["lists","autolink","autosave"],iR={table_grid:!1,object_resizing:!1,resize:!1},aR=function(e){var t=y(e)?e.join(" "):e,n=Q(v(t)?t.split(" "):[],lt);return ne(n,(function(e){return e.length>0}))},uR=function(e){return ne(e,B(G,oR))},cR=function(e,t){var n=_e(t,(function(t,n){return G(e,n)}));return ZA(n.t,n.f)},sR=function(e,t,n){void 0===n&&(n={});var r=e.sections(),o=Te(r,t).getOr({});return gn.extend({},n,o)},fR=function(e,t){return Oe(e.sections(),t)},lR=function(e,t,n){var r=e.sections();return fR(e,t)&&r[t].theme===n},dR=function(e,t){return fR(e,t)?e.sections()[t]:{}},mR=function(e,t){return Te(e,"toolbar_mode").orThunk((function(){return Te(e,"toolbar_drawer").map((function(e){return!1===e?"wrap":e}))})).getOr(t)},gR=function(e,t,n,r,o){var i={id:t,theme:"silver",toolbar_mode:mR(e,"floating"),plugins:"",document_base_url:n,add_form_submit_trigger:!0,submit_patch:!0,add_unload_trigger:!0,convert_urls:!0,relative_urls:!0,remove_script_host:!0,object_resizing:!0,doctype:"<!DOCTYPE html>",visual:!0,font_size_legacy_values:"xx-small,small,medium,large,x-large,xx-large,300%",forced_root_block:"p",hidden_input:!0,inline_styles:!0,convert_fonts_to_spans:!0,indent:!0,indent_before:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",indent_after:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",entity_encoding:"named",url_converter:o.convertURL,url_converter_scope:o};return qe(qe({},i),r?iR:{})},pR=function(e,t){var n={resize:!1,toolbar_mode:mR(e,"scrolling"),toolbar_sticky:!1},r={menubar:!1};return qe(qe(qe({},iR),n),t?r:{})},hR=function(e,t){var n=t.external_plugins?t.external_plugins:{};return e&&e.external_plugins?gn.extend({},e.external_plugins,n):n},vR=function(e,t){return[].concat(aR(e)).concat(aR(t))},bR=function(e,t,n,r){return e&&lR(t,"mobile","mobile")?uR(r):e&&fR(t,"mobile")?r:n},yR=function(e,t,n,r){var o=aR(n.forced_plugins),i=aR(r.plugins),a=dR(t,"mobile"),u=a.plugins?aR(a.plugins):i,c=bR(e,t,i,u),s=vR(o,c);if(en.browser.isIE()&&G(s,"rtc"))throw new Error("RTC plugin is not supported on IE 11.");return gn.extend(r,{plugins:s.join(" ")})},CR=function(e,t){return e&&fR(t,"mobile")},wR=function(e,t,n,r,o){var i=e?{mobile:pR(o.mobile||{},t)}:{},a=cR(["mobile"],WA(i,o)),u=gn.extend(n,r,a.settings(),CR(e,a)?sR(a,"mobile"):{},{validate:!0,external_plugins:hR(r,a.settings())});return yR(e,a,r,u)},xR=function(e,t,n,r,o){var i=gR(o,t,n,tR,e),a=wR(nR||rR,nR,i,r,o);return!1!==a.deprecation_warnings&&QA(o,a),a},SR=function(e,t,n){return q.from(t.settings[n]).filter(e)},kR=function(e){var t={};return"string"===typeof e?Z(e.indexOf("=")>0?e.split(/[;,](?![^=;,]*(?:[;,]|$))/):e.split(","),(function(e){var n=e.split("=");n.length>1?t[gn.trim(n[0])]=gn.trim(n[1]):t[gn.trim(n[0])]=gn.trim(n[0])})):t=e,t},NR=function(e){return function(t){return y(t)&&fe(t,e)}},ER=function(e,t,n,r){var o=t in e.settings?e.settings[t]:n;return"hash"===r?kR(o):"string"===r?SR(v,e,t).getOr(n):"number"===r?SR(E,e,t).getOr(n):"boolean"===r?SR(w,e,t).getOr(n):"object"===r?SR(b,e,t).getOr(n):"array"===r?SR(y,e,t).getOr(n):"string[]"===r?SR(NR(v),e,t).getOr(n):"function"===r?SR(N,e,t).getOr(n):o},_R=function(){var e={},t=function(t,n){e[t]=n},n=function(t){return e[t]?e[t]:{icons:{}}},r=function(t){return Oe(e,t)};return{add:t,get:n,has:r}},AR=_R(),RR=function(e,t){var n=t.dom;return n[e]},DR=function(e,t){return parseInt(Eo(t,e),10)},TR=B(RR,"clientWidth"),OR=B(RR,"clientHeight"),BR=B(DR,"margin-top"),PR=B(DR,"margin-left"),LR=function(e){return e.dom.getBoundingClientRect()},IR=function(e,t,n){var r=TR(e),o=OR(e);return t>=0&&n>=0&&t<=r&&n<=o},MR=function(e,t,n,r){var o=LR(t),i=e?o.left+t.dom.clientLeft+PR(t):0,a=e?o.top+t.dom.clientTop+BR(t):0,u=n-i,c=r-a;return{x:u,y:c}},FR=function(e,t,n){var r=Cn.fromDom(e.getBody()),o=e.inline?r:Xn(r),i=MR(e.inline,o,t,n);return IR(o,i.x,i.y)},UR=function(e){return q.from(e).map(Cn.fromDom)},zR=function(e){var t=e.inline?e.getBody():e.getContentAreaContainer();return UR(t).map(Ar).getOr(!1)},HR=function(){var e=function(){throw new Error("Theme did not provide a NotificationManager implementation.")};return{open:e,close:e,reposition:e,getArgs:e}},jR=function(e){var t=[],n=function(){var t=e.theme;return t&&t.getNotificationManagerImpl?t.getNotificationManagerImpl():HR()},r=function(){return q.from(t[0])},o=function(e,t){return e.type===t.type&&e.text===t.text&&!e.progressBar&&!e.timeout&&!t.progressBar&&!t.timeout},i=function(){t.length>0&&n().reposition(t)},a=function(e){t.push(e)},u=function(e){ue(t,(function(t){return t===e})).each((function(e){t.splice(e,1)}))},c=function(c,s){if(void 0===s&&(s=!0),!e.removed&&zR(e))return s&&e.fire("BeforeOpenNotification",{notification:c}),ae(t,(function(e){return o(n().getArgs(e),c)})).getOrThunk((function(){e.editorManager.setActive(e);var t=n().open(c,(function(){u(t),i(),r().fold((function(){return e.focus()}),(function(e){return ob(Cn.fromDom(e.getEl()))}))}));return a(t),i(),e.fire("OpenNotification",{notification:qe({},t)}),t}))},s=function(){r().each((function(e){n().close(e),u(e),i()}))},f=D(t),l=function(e){e.on("SkinLoaded",(function(){var t=rm(e);t&&c({text:t,type:"warning",timeout:0},!1),i()})),e.on("show ResizeEditor ResizeWindow NodeChange",(function(){Ii.requestAnimationFrame(i)})),e.on("remove",(function(){Z(t.slice(),(function(e){n().close(e)}))}))};return l(e),{open:c,close:s,getNotifications:f}},VR=lf.PluginManager,qR=lf.ThemeManager;function $R(){var e=function(){throw new Error("Theme did not provide a WindowManager implementation.")};return{open:e,openUrl:e,alert:e,confirm:e,close:e,getParams:e,setParams:e}}var WR=function(e){var t=[],n=function(){var t=e.theme;return t&&t.getWindowManagerImpl?t.getWindowManagerImpl():$R()},r=function(e,t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return t?t.apply(e,n):void 0}},o=function(t){e.fire("OpenWindow",{dialog:t})},i=function(t){e.fire("CloseWindow",{dialog:t})},a=function(e){t.push(e),o(e)},u=function(n){i(n),t=ne(t,(function(e){return e!==n})),0===t.length&&e.focus()},c=function(){return q.from(t[t.length-1])},s=function(t){e.editorManager.setActive(e),Mb(e);var n=t();return a(n),n},f=function(e,t){return s((function(){return n().open(e,t,u)}))},l=function(e){return s((function(){return n().openUrl(e,u)}))},d=function(e,t,o){var i=n();i.alert(e,r(o||i,t))},m=function(e,t,o){var i=n();i.confirm(e,r(o||i,t))},g=function(){c().each((function(e){n().close(e),u(e)}))};return e.on("remove",(function(){Z(t,(function(e){n().close(e)}))})),{open:f,openUrl:l,alert:d,confirm:m,close:g}},KR=function(e,t){e.notificationManager.open({type:"error",text:t})},XR=function(e,t){e._skinLoaded?KR(e,t):e.on("SkinLoaded",(function(){KR(e,t)}))},YR=function(e,t){XR(e,ff.translate(["Failed to upload image: {0}",t]))},GR=function(e,t,n){Qh(e,t,{message:n})},JR=function(e,t,n){return n?"Failed to load "+e+": "+n+" from url "+t:"Failed to load "+e+" url: "+t},QR=function(e,t,n){GR(e,"PluginLoadError",JR("plugin",t,n))},ZR=function(e,t,n){GR(e,"IconsLoadError",JR("icons",t,n))},eD=function(e,t,n){GR(e,"LanguageLoadError",JR("language",t,n))},tD=function(e,t,n){var r=ff.translate(["Failed to initialize plugin: {0}",t]);Qh(e,"PluginLoadError",{message:r}),nD(r,n),XR(e,r)},nD=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=window.console;r&&(r.error?r.error.apply(r,We([e],t,!1)):r.log.apply(r,We([e],t,!1)))},rD=function(e){return/^[a-z0-9\-]+$/i.test(e)},oD=function(e){return aD(e,Yd(e))},iD=function(e){return aD(e,Gd(e))},aD=function(e,t){var n=e.editorManager.baseURL+"/skins/content",r=e.editorManager.suffix,o="content"+r+".css",i=!0===e.inline;return Q(t,(function(t){return rD(t)&&!i?n+"/"+t+"/"+o:e.documentBaseURI.toAbsolute(t)}))},uD=function(e){e.contentCSS=e.contentCSS.concat(oD(e),iD(e))},cD=function(){var e=1,t=2,n={},r=function(e,t){return{status:e,resultUri:t}},o=function(e){return e in n},i=function(e){var t=n[e];return t?t.resultUri:null},a=function(t){return!!o(t)&&n[t].status===e},u=function(e){return!!o(e)&&n[e].status===t},c=function(t){n[t]=r(e,null)},s=function(e,o){n[e]=r(t,o)},f=function(e){delete n[e]},l=function(){n={}};return{hasBlobUri:o,getResultUri:i,isPending:a,isUploaded:u,markPending:c,markUploaded:s,removeFailed:f,destroy:l}},sD=0,fD=function(){var e=function(){return Math.round(4294967295*Math.random()).toString(36)},t=(new Date).getTime();return"s"+t.toString(36)+e()+e()+e()},lD=function(e){return e+sD+++fD()},dD=function(){var e=[],t=function(e){var t={"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png","image/apng":"apng","image/avif":"avif","image/svg+xml":"svg","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"};return t[e.toLowerCase()]||"dat"},n=function(e,t,n,o,i){if(v(e)){var a=e;return r({id:a,name:o,filename:i,blob:t,base64:n})}if(b(e))return r(e);throw new Error("Unknown input type")},r=function(e){if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");var n=e.id||lD("blobid"),r=e.name||n,o=e.blob;return{id:D(n),name:D(r),filename:D(e.filename||r+"."+t(o.type)),blob:D(o),base64:D(e.base64),blobUri:D(e.blobUri||URL.createObjectURL(o)),uri:D(e.uri)}},o=function(t){a(t.id())||e.push(t)},i=function(t){return ae(e,t).getOrUndefined()},a=function(e){return i((function(t){return t.id()===e}))},u=function(e){return i((function(t){return t.blobUri()===e}))},c=function(e,t){return i((function(n){return n.base64()===e&&n.blob().type===t}))},s=function(t){e=ne(e,(function(e){return e.blobUri()!==t||(URL.revokeObjectURL(e.blobUri()),!1)}))},f=function(){Z(e,(function(e){URL.revokeObjectURL(e.blobUri())})),e=[]};return{create:n,add:o,get:a,getByUri:u,getByData:c,findFirst:i,removeByUri:s,destroy:f}},mD=function(e,t){var n={},r=function(e,t){return e?e.replace(/\/$/,"")+"/"+t.replace(/^\//,""):t},o=function(e,n,o,i){var a=new XMLHttpRequest;a.open("POST",t.url),a.withCredentials=t.credentials,a.upload.onprogress=function(e){i(e.loaded/e.total*100)},a.onerror=function(){o("Image upload failed due to a XHR Transport error. Code: "+a.status)},a.onload=function(){if(a.status<200||a.status>=300)o("HTTP Error: "+a.status);else{var e=JSON.parse(a.responseText);e&&"string"===typeof e.location?n(r(t.basePath,e.location)):o("Invalid JSON: "+a.responseText)}};var u=new FormData;u.append("file",e.blob(),e.filename()),a.send(u)},i=function(){return new Ri((function(e){e([])}))},a=function(e,t){return{url:t,blobInfo:e,status:!0}},u=function(e,t,n){return{url:"",blobInfo:e,status:!1,error:{message:t,options:n}}},c=function(e,t){gn.each(n[e],(function(e){e(t)})),delete n[e]},s=function(t,n,r){return e.markPending(t.blobUri()),new Ri((function(o){var i,s;try{var f=function(){i&&(i.close(),s=_)},l=function(n){f(),e.markUploaded(t.blobUri(),n),c(t.blobUri(),a(t,n)),o(a(t,n))},d=function(n,r){var i=r||{};f(),e.removeFailed(t.blobUri()),c(t.blobUri(),u(t,n,i)),o(u(t,n,i))};s=function(e){e<0||e>100||q.from(i).orThunk((function(){return q.from(r).map(I)})).each((function(t){i=t,t.progressBar.value(e)}))},n(t,l,d,s)}catch(m){o(u(t,m.message,{}))}}))},f=function(e){return e===o},l=function(e){var t=e.blobUri();return new Ri((function(e){n[t]=n[t]||[],n[t].push(e)}))},d=function(n,r){return n=gn.grep(n,(function(t){return!e.isUploaded(t.blobUri())})),Ri.all(gn.map(n,(function(n){return e.isPending(n.blobUri())?l(n):s(n,t.handler,r)})))},m=function(e,n){return!t.url&&f(t.handler)?i():d(e,n)};return!1===N(t.handler)&&(t.handler=o),{upload:m}},gD=function(e){return function(){return e.notificationManager.open({text:e.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0})}},pD=function(e,t){return mD(t,{url:Ud(e),basePath:zd(e),credentials:Hd(e),handler:jd(e)})},hD=function(e){var t=cD(),n=pD(e,t);return{upload:function(t,r){return void 0===r&&(r=!0),n.upload(t,r?gD(e):void 0)}}},vD=function(e){var t=Gs(null);e.on("change AddUndo",(function(e){t.set(qe({},e.level))}));var n=function(){var n=e.undoManager.data;ve(n).filter((function(e){return!dE(t.get(),e)})).each((function(t){e.setDirty(!0),e.fire("change",{level:t,lastLevel:pe(n,n.length-2).getOrNull()})}))};return{fireIfChanged:n}},bD=function(e){var t,n,r=dD(),o=cD(),i=[],a=vD(e),u=function(t){return function(n){return e.selection?t(n):[]}},c=function(e){return e+(-1===e.indexOf("?")?"?":"&")+(new Date).getTime()},s=function(e,t,n){var r=0;do{r=e.indexOf(t,r),-1!==r&&(e=e.substring(0,r)+n+e.substr(r+t.length),r+=n.length-t.length+1)}while(-1!==r);return e},f=function(e,t,n){var r='src="'+n+'"'+(n===en.transparentSrc?' data-mce-placeholder="1"':"");return e=s(e,'src="'+t+'"',r),e=s(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"'),e},l=function(t,n){Z(e.undoManager.data,(function(e){"fragmented"===e.type?e.fragments=Q(e.fragments,(function(e){return f(e,t,n)})):e.content=f(e.content,t,n)}))},d=function(t,n){var r=e.convertURL(n,"src");l(t.src,n),e.$(t).attr({src:Ld(e)?c(n):n,"data-mce-src":r})},m=function(n){return t||(t=pD(e,o)),v().then(u((function(o){var i=Q(o,(function(e){return e.blobInfo}));return t.upload(i,gD(e)).then(u((function(t){var i=[],u=Q(t,(function(t,n){var a=o[n].blobInfo,u=o[n].image;return t.status&&Id(e)?(r.removeByUri(u.src),OE(e)||d(u,t.url)):t.error&&(t.error.options.remove&&(l(u.getAttribute("src"),en.transparentSrc),i.push(u)),YR(e,t.error.message)),{element:u,status:t.status,uploadUri:t.url,blobInfo:a}}));return u.length>0&&a.fireIfChanged(),i.length>0&&(OE(e)||e.undoManager.transact((function(){Z(i,(function(t){e.dom.remove(t),r.removeByUri(t.src)}))}))),n&&n(u),u})))})))},g=function(t){if(Pd(e))return m(t)},p=function(t){if(!1===fe(i,(function(e){return e(t)})))return!1;if(0===t.getAttribute("src").indexOf("data:")){var n=Bd(e);return n(t)}return!0},h=function(e){i.push(e)},v=function(){return n||(n=U_(o,r)),n.findAll(e.getBody(),p).then(u((function(t){return t=ne(t,(function(t){return"string"!==typeof t||(XR(e,t),!1)})),OE(e)||Z(t,(function(e){l(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")})),t})))},b=function(){r.destroy(),o.destroy(),n=t=null},y=function(t){return t.replace(/src="(blob:[^"]+)"/g,(function(t,n){var i=o.getResultUri(n);if(i)return'src="'+i+'"';var a=r.getByUri(n);if(a||(a=oe(e.editorManager.get(),(function(e,t){return e||t.editorUpload&&t.editorUpload.blobCache.getByUri(n)}),null)),a){var u=a.blob();return'src="data:'+u.type+";base64,"+a.base64()+'"'}return t}))};return e.on("SetContent",(function(){Pd(e)?g():v()})),e.on("RawSaveContent",(function(e){e.content=y(e.content)})),e.on("GetContent",(function(e){e.source_view||"raw"===e.format||"tree"===e.format||(e.content=y(e.content))})),e.on("PostRender",(function(){e.parser.addNodeFilter("img",(function(e){Z(e,(function(e){var t=e.attr("src");if(!r.getByUri(t)){var n=o.getResultUri(t);n&&e.attr("src",n)}}))}))})),{blobCache:r,addFilter:h,uploadImages:m,uploadImagesAuto:g,scanForImages:v,destroy:b}},yD=function(e){var t={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"left"},inherit:!1,preview:!1,defaultBlock:"div"},{selector:"img,table,audio,video",collapsed:!1,styles:{float:"left"},preview:"font-family font-size"}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"img,table,audio,video",collapsed:!1,styles:{float:"right"},preview:"font-family font-size"}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"justify"},inherit:!1,defaultBlock:"div",preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all",preserve_attributes:["class","style"]}],italic:[{inline:"em",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all",preserve_attributes:["class","style"]}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all",preserve_attributes:["class","style"]}],strikethrough:[{inline:"span",styles:{textDecoration:"line-through"},exact:!0},{inline:"strike",remove:"all",preserve_attributes:["class","style"]},{inline:"s",remove:"all",preserve_attributes:["class","style"]}],forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},lineheight:{selector:"h1,h2,h3,h4,h5,h6,p,li,td,th,div",defaultBlock:"p",styles:{lineHeight:"%value"}},fontsize_class:{inline:"span",attributes:{class:"%value"}},blockquote:{block:"blockquote",wrapper:!0,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:function(e,t,n){return Vr(e)&&e.hasAttribute("href")},onformat:function(t,n,r){gn.each(r,(function(n,r){e.setAttrib(t,r,n)}))}},lang:{inline:"span",clear_child_styles:!0,remove_similar:!0,attributes:{lang:"%value","data-mce-lang":function(e){var t;return null!==(t=null===e||void 0===e?void 0:e.customValue)&&void 0!==t?t:null}}},removeformat:[{selector:"b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return gn.each("p h1 h2 h3 h4 h5 h6 div address pre dt dd samp".split(/\s/),(function(e){t[e]={block:e,remove:"all"}})),t},CD=function(e){var t={},n=function(e){return k(e)?t[e]:t},r=function(e){return Oe(t,e)},o=function(e,n){e&&(v(e)?(y(n)||(n=[n]),Z(n,(function(e){x(e.deep)&&(e.deep=!qp(e)),x(e.split)&&(e.split=!qp(e)||$p(e)),x(e.remove)&&qp(e)&&!$p(e)&&(e.remove="none"),qp(e)&&$p(e)&&(e.mixed=!0,e.block_expand=!0),v(e.classes)&&(e.classes=e.classes.split(/\s+/))})),t[e]=n):xe(e,(function(e,t){o(t,e)})))},i=function(e){return e&&t[e]&&delete t[e],t};return o(yD(e.dom)),o(um(e)),{get:n,has:r,register:o,unregister:i}},wD=gn.each,xD=Hs.DOM,SD=function(e,t){var n,r,o,i=t&&t.schema||Hu({}),a=function(e,t){t.classes.length&&xD.addClass(e,t.classes.join(" ")),xD.setAttribs(e,t.attrs)},u=function(e){r="string"===typeof e?{name:e,classes:[],attrs:{}}:e;var t=xD.create(r.name);return a(t,r),t},c=function(e,t){var n="string"!==typeof e?e.nodeName.toLowerCase():e,r=i.getElementRule(n),o=r&&r.parentsRequired;return!(!o||!o.length)&&(t&&-1!==gn.inArray(o,t)?t:o[0])},s=function(e,t,n){var r,o,i=t.length>0&&t[0],a=i&&i.name,f=c(e,a);if(f)a===f?(o=t[0],t=t.slice(1)):o=f;else if(i)o=t[0],t=t.slice(1);else if(!n)return e;return o&&(r=u(o),r.appendChild(e)),n&&(r||(r=xD.create("div"),r.appendChild(e)),gn.each(n,(function(t){var n=u(t);r.insertBefore(n,e)}))),s(r,t,o&&o.siblings)};return e&&e.length?(r=e[0],n=u(r),o=xD.create("div"),o.appendChild(s(n,e.slice(1),r.siblings)),o):""},kD=function(e){var t,n={classes:[],attrs:{}};return e=n.selector=gn.trim(e),"*"!==e&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,(function(e,t,r,o,i){switch(t){case"#":n.attrs.id=r;break;case".":n.classes.push(r);break;case":":-1!==gn.inArray("checked disabled enabled read-only required".split(" "),r)&&(n.attrs[r]=r);break}if("["===o){var a=i.match(/([\w\-]+)(?:\=\"([^\"]+))?/);a&&(n.attrs[a[1]]=a[2])}return""}))),n.name=t||"div",n},ND=function(e){return e&&"string"===typeof e?(e=e.split(/\s*,\s*/)[0],e=e.replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),gn.map(e.split(/(?:>|\s+(?![^\[\]]+\]))/),(function(e){var t=gn.map(e.split(/(?:~\+|~|\+)/),kD),n=t.pop();return t.length&&(n.siblings=t),n})).reverse()):[]},ED=function(e,t){var n,r,o,i="",a=cm(e);if(""===a)return"";var u=function(e){return e.replace(/%(\w+)/g,"")};if("string"===typeof t){if(t=e.formatter.get(t),!t)return;t=t[0]}if("preview"in t){var c=Te(t,"preview");if(so(c,!1))return"";a=c.getOr(a)}n=t.block||t.inline||"span";var s=ND(t.selector);s.length?(s[0].name||(s[0].name=n),n=t.selector,r=SD(s,e)):r=SD([n],e);var f=xD.select(n,r)[0]||r.firstChild;return wD(t.styles,(function(e,t){var n=u(e);n&&xD.setStyle(f,t,n)})),wD(t.attributes,(function(e,t){var n=u(e);n&&xD.setAttrib(f,t,n)})),wD(t.classes,(function(e){var t=u(e);xD.hasClass(f,t)||xD.addClass(f,t)})),e.fire("PreviewFormats"),xD.setStyles(r,{position:"absolute",left:-65535}),e.getBody().appendChild(r),o=xD.getStyle(e.getBody(),"fontSize",!0),o=/px$/.test(o)?parseInt(o,10):0,wD(a.split(" "),(function(t){var n=xD.getStyle(f,t,!0);if(("background-color"!==t||!/transparent|rgba\s*\([^)]+,\s*0\)/.test(n)||(n=xD.getStyle(e.getBody(),t,!0),"#ffffff"!==xD.toHex(n).toLowerCase()))&&("color"!==t||"#000000"!==xD.toHex(n).toLowerCase())){if("font-size"===t&&/em|%$/.test(n)){if(0===o)return;var r=parseFloat(n)/(/%$/.test(n)?100:1);n=r*o+"px"}"border"===t&&n&&(i+="padding:0 2px;"),i+=t+":"+n+";"}})),e.fire("AfterPreviewFormats"),xD.remove(r),i},_D=function(e){e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(var t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])},AD=function(e){var t=CD(e),n=Gs(null);return _D(e),ok(e),{get:t.get,has:t.has,register:t.register,unregister:t.unregister,apply:function(t,n,r){ZE(e,t,n,r)},remove:function(t,n,r,o){e_(e,t,n,r,o)},toggle:function(t,n,r){t_(e,t,n,r)},match:function(t,n,r,o){return XE(e,t,n,r,o)},closest:function(t){return QE(e,t)},matchAll:function(t,n){return YE(e,t,n)},matchNode:function(t,n,r,o){return GE(e,t,n,r,o)},canApply:function(t){return JE(e,t)},formatChanged:function(t,r,o,i){return n_(e,n,t,r,o,i)},getCssText:B(ED,e)}},RD=function(e){switch(e.toLowerCase()){case"undo":case"redo":case"mcerepaint":case"mcefocus":return!0;default:return!1}},DD=function(e,t,n){var r=Gs(!1),o=function(e){gE(t,!1,n),t.add({},e)};e.on("init",(function(){t.add()})),e.on("BeforeExecCommand",(function(e){var r=e.command;RD(r)||(pE(t,n),t.beforeChange())})),e.on("ExecCommand",(function(e){var t=e.command;RD(t)||o(e)})),e.on("ObjectResizeStart cut",(function(){t.beforeChange()})),e.on("SaveContent ObjectResized blur",o),e.on("dragend",o),e.on("keyup",(function(n){var i=n.keyCode;n.isDefaultPrevented()||((i>=33&&i<=36||i>=37&&i<=40||45===i||n.ctrlKey)&&(o(),e.nodeChanged()),46!==i&&8!==i||e.nodeChanged(),r.get()&&t.typing&&!1===dE(aE(e),t.data[0])&&(!1===e.isDirty()&&(e.setDirty(!0),e.fire("change",{level:t.data[0],lastLevel:null})),e.fire("TypingUndo"),r.set(!1),e.nodeChanged()))})),e.on("keydown",(function(e){var i=e.keyCode;if(!e.isDefaultPrevented())if(i>=33&&i<=36||i>=37&&i<=40||45===i)t.typing&&o(e);else{var a=e.ctrlKey&&!e.altKey||e.metaKey;!(i<16||i>20)||224===i||91===i||t.typing||a||(t.beforeChange(),gE(t,!0,n),t.add({},e),r.set(!0))}})),e.on("mousedown",(function(e){t.typing&&o(e)}));var i=function(e){return"insertReplacementText"===e.inputType},a=function(e){return"insertText"===e.inputType&&null===e.data},u=function(e){return"insertFromPaste"===e.inputType||"insertFromDrop"===e.inputType};e.on("input",(function(e){e.inputType&&(i(e)||a(e)||u(e))&&o(e)})),e.on("AddUndo Undo Redo ClearUndos",(function(t){t.isDefaultPrevented()||e.nodeChanged()}))},TD=function(e){e.addShortcut("meta+z","","Undo"),e.addShortcut("meta+y,meta+shift+z","","Redo")},OD=function(e){var t=mf(),n=Gs(0),r=Gs(0),o={data:[],typing:!1,beforeChange:function(){ME(e,n,t)},add:function(i,a){return FE(e,o,r,n,t,i,a)},undo:function(){return UE(e,o,n,r)},redo:function(){return zE(e,r,o.data)},clear:function(){HE(e,o,r)},reset:function(){jE(e,o)},hasUndo:function(){return VE(e,o,r)},hasRedo:function(){return qE(e,o,r)},transact:function(t){return $E(e,o,n,t)},ignore:function(t){WE(e,n,t)},extra:function(t,n){KE(e,o,r,t,n)}};return OE(e)||DD(e,o,n),TD(e),o},BD=[9,27,tv.HOME,tv.END,19,20,44,144,145,33,34,45,16,17,18,91,92,93,tv.DOWN,tv.UP,tv.LEFT,tv.RIGHT].concat(en.browser.isFirefox()?[224]:[]),PD="data-mce-placeholder",LD=function(e){return"keydown"===e.type||"keyup"===e.type},ID=function(e){var t=e.keyCode;return t===tv.BACKSPACE||t===tv.DELETE},MD=function(e){if(LD(e)){var t=e.keyCode;return!ID(e)&&(tv.metaKeyPressed(e)||e.altKey||t>=112&&t<=123||G(BD,t))}return!1},FD=function(e){return LD(e)&&!(ID(e)||"keyup"===e.type&&229===e.keyCode)},UD=function(e,t,n){if(nu(Cn.fromDom(t),!1)){var r=""===n,o=t.firstElementChild;return!o||!e.getStyle(t.firstElementChild,"padding-left")&&!e.getStyle(t.firstElementChild,"padding-right")&&(r?!e.isBlock(o):n===o.nodeName.toLowerCase())}return!1},zD=function(e){var t=e.dom,n=Nd(e),r=tm(e),o=function(i,a){if(!MD(i)){var u=e.getBody(),c=!FD(i)&&UD(t,u,n),s=""!==t.getAttrib(u,PD);(s!==c||a)&&(t.setAttrib(u,PD,c?r:null),t.setAttrib(u,"aria-placeholder",c?r:null),Jh(e,c),e.on(c?"keydown":"keyup",o),e.off(c?"keyup":"keydown",o))}};r&&e.on("init",(function(t){o(t,!0),e.on("change SetContent ExecCommand",o),e.on("paste",(function(t){return Ii.setEditorTimeout(e,(function(){return o(t)}))}))}))},HD=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,jD=function(e){return HD.test(e)},VD=function(e,t){return Rn(Cn.fromDom(t),Qd(e))},qD=function(e){return"rtl"===Hs.DOM.getStyle(e,"direction",!0)||jD(e.textContent)},$D=function(e,t,n){return ne(Hs.DOM.getParents(n.container(),"*",t),e)},WD=function(e,t,n){var r=$D(e,t,n);return q.from(r[r.length-1])},KD=function(e,t,n){var r=fg(t,e),o=fg(n,e);return r&&r===o},XD=function(e){return xa(e)||Sa(e)},YD=function(e,t){if(!t)return t;var n=t.container(),r=t.offset();return e?ba(n)?Zr(n.nextSibling)?Bl(n.nextSibling,0):Bl.after(n):xa(t)?Bl(n,r+1):t:ba(n)?Zr(n.previousSibling)?Bl(n.previousSibling,n.previousSibling.data.length):Bl.before(n):Sa(t)?Bl(n,r-1):t},GD=B(YD,!0),JD=B(YD,!1),QD=function(e){return function(t){return Bn(e,Cn.fromDom(t.dom.parentNode))}},ZD=function(e){return ta(e)||ra(e)},eT=function(e,t){return In(e,t)?Si(t,ZD,QD(e)):q.none()},tT=function(e){var t=e.getBody(),n=t.firstChild&&e.dom.isBlock(t.firstChild)?t.firstChild:t;e.selection.setCursorLocation(n,0)},nT=function(e){e.dom.isEmpty(e.getBody())&&(e.setContent(""),tT(e))},rT=function(e,t,n){return lo(Jg(n),Qg(n),(function(r,o){var i=YD(!0,r),a=YD(!1,o),u=YD(!1,t);return e?Yg(n,u).exists((function(e){return e.isEqual(a)&&t.isEqual(i)})):Gg(n,u).exists((function(e){return e.isEqual(i)&&t.isEqual(a)}))})).getOr(!0)},oT=function(e,t){return{block:e,position:t}},iT=function(e,t){return{from:e,to:t}},aT=function(e,t){var n=Cn.fromDom(e),r=Cn.fromDom(t.container());return eT(n,r).map((function(e){return oT(e,t)}))},uT=function(e){return!1===Bn(e.from.block,e.to.block)},cT=function(e){return Gn(e.from.block).bind((function(t){return Gn(e.to.block).filter((function(e){return Bn(t,e)}))})).isSome()},sT=function(e){return!1===ao(e.from.block.dom)&&!1===ao(e.to.block.dom)},fT=function(e,t,n){return ro(n.position.getNode())&&!1===nu(n.block)?Xg(!1,n.block.dom).bind((function(r){return r.isEqual(n.position)?$g(t,e,r).bind((function(t){return aT(e,t)})):q.some(n)})).getOr(n):n},lT=function(e,t,n){var r=aT(e,Bl.fromRangeStart(n)),o=r.bind((function(n){return $g(t,e,n.position).bind((function(n){return aT(e,n).map((function(n){return fT(e,t,n)}))}))}));return lo(r,o,iT).filter((function(e){return uT(e)&&cT(e)&&sT(e)}))},dT=function(e,t,n){return n.collapsed?lT(e,t,n):q.none()},mT=function(e){var t=rr(e);return ue(t,Ji).fold(D(t),(function(e){return t.slice(0,e)}))},gT=function(e){var t=mT(e);return Z(t,Er),t},pT=function(e,t){var n=RC(t,e);return ae(n.reverse(),(function(e){return nu(e)})).each(Er)},hT=function(e){return 0===ne(tr(e),(function(e){return!nu(e)})).length},vT=function(e,t,n,r){if(nu(n))return kC(n),Jg(n.dom);hT(r)&&nu(t)&&br(r,Cn.fromTag("br"));var o=Gg(n.dom,Bl.before(r.dom));return Z(gT(t),(function(e){br(r,e)})),pT(e,t),o},bT=function(e,t,n){if(nu(n))return Er(n),nu(t)&&kC(t),Jg(t.dom);var r=Qg(n.dom);return Z(gT(t),(function(e){wr(n,e)})),pT(e,t),r},yT=function(e,t){var n=RC(t,e);return q.from(n[n.length-1])},CT=function(e,t){return In(t,e)?yT(t,e):q.none()},wT=function(e,t){Xg(e,t.dom).map((function(e){return e.getNode()})).map(Cn.fromDom).filter(ea).each(Er)},xT=function(e,t,n){return wT(!0,t),wT(!1,n),CT(t,n).fold(B(bT,e,t,n),B(vT,e,t,n))},ST=function(e,t,n,r){return t?xT(e,r,n):xT(e,n,r)},kT=function(e,t){var n=Cn.fromDom(e.getBody()),r=dT(n.dom,t,e.selection.getRng()).bind((function(e){return ST(n,t,e.from.block,e.to.block)}));return r.each((function(t){e.selection.setRng(t.toRange())})),r.isSome()},NT=function(e,t){var n=t.getRng();return lo(eT(e,Cn.fromDom(n.startContainer)),eT(e,Cn.fromDom(n.endContainer)),(function(r,o){return!1===Bn(r,o)&&(n.deleteContents(),ST(e,!0,r,o).each((function(e){t.setRng(e.toRange())})),!0)})).getOr(!1)},ET=function(e,t){var n=Cn.fromDom(t),r=B(Bn,e);return xi(n,ia,r).isSome()},_T=function(e,t){return ET(e,t.startContainer)||ET(e,t.endContainer)},AT=function(e,t){var n=Gg(e.dom,Bl.fromRangeStart(t)).isNone(),r=Yg(e.dom,Bl.fromRangeEnd(t)).isNone();return!_T(e,t)&&n&&r},RT=function(e){return e.setContent(""),e.selection.setCursorLocation(),!0},DT=function(e){var t=Cn.fromDom(e.getBody()),n=e.selection.getRng();return AT(t,n)?RT(e):NT(t,e.selection)},TT=function(e,t){return!e.selection.isCollapsed()&&DT(e)},OT=io,BT=ao,PT=function(e,t,n,r,o){return q.from(t._selectionOverrides.showCaret(e,n,r,o))},LT=function(e){var t=e.ownerDocument.createRange();return t.selectNode(e),t},IT=function(e,t){var n=e.fire("BeforeObjectSelected",{target:t});return n.isDefaultPrevented()?q.none():q.some(LT(t))},MT=function(e,t,n){var r=bg(1,e.getBody(),t),o=Bl.fromRangeStart(r),i=o.getNode();if(Jm(i))return PT(1,e,i,!o.isAtEnd(),!1);var a=o.getNode(!0);if(Jm(a))return PT(1,e,a,!1,!1);var u=e.dom.getParent(o.getNode(),(function(e){return BT(e)||OT(e)}));return Jm(u)?PT(1,e,u,!1,n):q.none()},FT=function(e,t,n){return t.collapsed?MT(e,t,n).getOr(t):t},UT=function(e){return CC(e)||hC(e)},zT=function(e){return wC(e)||vC(e)},HT=function(e,t){Zr(t)&&0===t.data.length&&e.remove(t)},jT=function(e,t,n,r,o,i){return PT(r,e,i.getNode(!o),o,!0).each((function(n){if(t.collapsed){var r=t.cloneRange();o?r.setEnd(n.startContainer,n.startOffset):r.setStart(n.endContainer,n.endOffset),r.deleteContents()}else t.deleteContents();e.selection.setRng(n)})),HT(e.dom,n),!0},VT=function(e,t){var n=e.selection.getRng();if(!Zr(n.commonAncestorContainer))return!1;var r=t?Pl.Forwards:Pl.Backwards,o=Mg(e.getBody()),i=B(Sg,t?o.next:o.prev),a=t?UT:zT,u=Cg(r,e.getBody(),n),c=YD(t,i(u));if(!c||!kg(u,c))return!1;if(a(c))return jT(e,n,u.getNode(),r,t,c);var s=i(c);return!!(s&&a(s)&&kg(c,s))&&jT(e,n,u.getNode(),r,t,s)},qT=function(e,t){return VT(e,t)},$T=function(e){return ia(Cn.fromDom(e))||ra(Cn.fromDom(e))},WT=ei.generate([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),KT=function(e,t){var n=t.getNode(!1===e),r=e?"after":"before";return Vr(n)&&n.getAttribute("data-mce-caret")===r},XT=function(e,t,n,r){var o=function(t){return Zi(Cn.fromDom(t))&&!lg(n,r,e)};return yg(!t,n).fold((function(){return yg(t,r).fold(F,o)}),o)},YT=function(e,t,n,r){var o=r.getNode(!1===t);return eT(Cn.fromDom(e),Cn.fromDom(n.getNode())).map((function(e){return nu(e)?WT.remove(e.dom):WT.moveToElement(o)})).orThunk((function(){return q.some(WT.moveToElement(o))}))},GT=function(e,t,n){return $g(t,e,n).bind((function(r){return $T(r.getNode())||XT(e,t,n,r)?q.none():t&&ao(r.getNode())||!1===t&&ao(r.getNode(!0))?YT(e,t,n,r):t&&wC(n)||!1===t&&CC(n)?q.some(WT.moveToPosition(r)):q.none()}))},JT=function(e,t){return e&&ao(t.nextSibling)?q.some(WT.moveToElement(t.nextSibling)):!1===e&&ao(t.previousSibling)?q.some(WT.moveToElement(t.previousSibling)):q.none()},QT=function(e,t,n){return n.fold((function(e){return q.some(WT.remove(e))}),(function(e){return q.some(WT.moveToElement(e))}),(function(n){return lg(t,n,e)?q.none():q.some(WT.moveToPosition(n))}))},ZT=function(e,t,n){return KT(t,n)?JT(t,n.getNode(!1===t)).fold((function(){return GT(e,t,n)}),q.some):GT(e,t,n).bind((function(t){return QT(e,n,t)}))},eO=function(e,t,n){var r=bg(t?1:-1,e,n),o=Bl.fromRangeStart(r),i=Cn.fromDom(e);return!1===t&&wC(o)?q.some(WT.remove(o.getNode(!0))):t&&CC(o)?q.some(WT.remove(o.getNode())):!1===t&&CC(o)&&HC(i,o)?jC(i,o).map((function(e){return WT.remove(e.getNode())})):t&&wC(o)&&zC(i,o)?VC(i,o).map((function(e){return WT.remove(e.getNode())})):ZT(e,t,o)},tO=function(e,t){return function(n){return e._selectionOverrides.hideFakeCaret(),Lw(e,t,Cn.fromDom(n)),!0}},nO=function(e,t){return function(n){var r=t?Bl.before(n):Bl.after(n);return e.selection.setRng(r.toRange()),!0}},rO=function(e){return function(t){return e.selection.setRng(t.toRange()),!0}},oO=function(e,t){return q.from(Uh(e.getBody(),t))},iO=function(e,t){var n=e.selection.getNode();return oO(e,n).filter(ao).fold((function(){return eO(e.getBody(),t,e.selection.getRng()).exists((function(n){return n.fold(tO(e,t),nO(e,t),rO(e))}))}),U)},aO=function(e){Z(Af(e,".mce-offscreen-selection"),Er)},uO=function(e,t){var n=e.selection.getNode();if(ao(n)&&!uo(n)){var r=oO(e,n.parentNode).filter(ao);return r.fold((function(){return aO(Cn.fromDom(e.getBody())),Lw(e,t,Cn.fromDom(e.selection.getNode())),nT(e),!0}),U)}return!1},cO=function(e){var t=e.dom,n=e.selection,r=Uh(e.getBody(),n.getNode());if(io(r)&&t.isBlock(r)&&t.isEmpty(r)){var o=t.create("br",{"data-mce-bogus":"1"});t.setHTML(r,""),r.appendChild(o),n.setRng(Bl.before(o).toRange())}return!0},sO=function(e,t){return e.selection.isCollapsed()?iO(e,t):uO(e,t)},fO=function(e,t){var n=Bl.fromRangeStart(e.selection.getRng());return $g(t,e.getBody(),n).filter((function(e){return t?gC(e):pC(e)})).bind((function(e){return q.from(dg(t?0:-1,e))})).exists((function(t){return e.selection.select(t),!0}))},lO=function(e,t){return!!e.selection.isCollapsed()&&fO(e,t)},dO=Zr,mO=function(e){return dO(e)&&e.data[0]===da},gO=function(e){return dO(e)&&e.data[e.data.length-1]===da},pO=function(e){return e.ownerDocument.createTextNode(da)},hO=function(e){if(dO(e.previousSibling))return gO(e.previousSibling)||e.previousSibling.appendData(da),e.previousSibling;if(dO(e))return mO(e)||e.insertData(0,da),e;var t=pO(e);return e.parentNode.insertBefore(t,e),t},vO=function(e){if(dO(e.nextSibling))return mO(e.nextSibling)||e.nextSibling.insertData(0,da),e.nextSibling;if(dO(e))return gO(e)||e.appendData(da),e;var t=pO(e);return e.nextSibling?e.parentNode.insertBefore(t,e.nextSibling):e.parentNode.appendChild(t),t},bO=function(e,t){return e?hO(t):vO(t)},yO=B(bO,!0),CO=B(bO,!1),wO=function(e,t){return Zr(e.container())?bO(t,e.container()):bO(t,e.getNode())},xO=function(e,t){var n=t.get();return n&&e.container()===n&&ba(n)},SO=function(e,t){return t.fold((function(t){Hm(e.get());var n=yO(t);return e.set(n),q.some(Bl(n,n.length-1))}),(function(t){return Jg(t).map((function(t){if(xO(t,e))return Bl(e.get(),1);Hm(e.get());var n=wO(t,!0);return e.set(n),Bl(n,1)}))}),(function(t){return Qg(t).map((function(t){if(xO(t,e))return Bl(e.get(),e.get().length-1);Hm(e.get());var n=wO(t,!1);return e.set(n),Bl(n,n.length-1)}))}),(function(t){Hm(e.get());var n=CO(t);return e.set(n),q.some(Bl(n,1))}))},kO=function(e,t){for(var n=0;n<e.length;n++){var r=e[n].apply(null,t);if(r.isSome())return r}return q.none()},NO=ei.generate([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),EO=function(e,t){var n=fg(t,e);return n||e},_O=function(e,t,n){var r=GD(n),o=EO(t,r.container());return WD(e,o,r).fold((function(){return Yg(o,r).bind(B(WD,e,o)).map((function(e){return NO.before(e)}))}),q.none)},AO=function(e,t){return null===tp(e,t)},RO=function(e,t,n){return WD(e,t,n).filter(B(AO,t))},DO=function(e,t,n){var r=JD(n);return RO(e,t,r).bind((function(e){var t=Gg(e,r);return t.isNone()?q.some(NO.start(e)):q.none()}))},TO=function(e,t,n){var r=GD(n);return RO(e,t,r).bind((function(e){var t=Yg(e,r);return t.isNone()?q.some(NO.end(e)):q.none()}))},OO=function(e,t,n){var r=JD(n),o=EO(t,r.container());return WD(e,o,r).fold((function(){return Gg(o,r).bind(B(WD,e,o)).map((function(e){return NO.after(e)}))}),q.none)},BO=function(e){return!1===qD(LO(e))},PO=function(e,t,n){var r=kO([_O,DO,TO,OO],[e,t,n]);return r.filter(BO)},LO=function(e){return e.fold(T,T,T,T)},IO=function(e){return e.fold(D("before"),D("start"),D("end"),D("after"))},MO=function(e){return e.fold(NO.before,NO.before,NO.after,NO.after)},FO=function(e){return e.fold(NO.start,NO.start,NO.end,NO.end)},UO=function(e,t){return IO(e)===IO(t)&&LO(e)===LO(t)},zO=function(e,t,n,r,o,i){return lo(WD(t,n,r),WD(t,n,o),(function(t,r){return t!==r&&KD(n,t,r)?NO.after(e?t:r):i})).getOr(i)},HO=function(e,t){return e.fold(U,(function(e){return!UO(e,t)}))},jO=function(e,t,n,r,o){var i=YD(e,o),a=$g(e,n,i).map(B(YD,e)),u=a.fold((function(){return r.map(MO)}),(function(o){return PO(t,n,o).map(B(zO,e,t,n,i,o)).filter(B(HO,r))}));return u.filter(BO)},VO=function(e,t){return e?t.fold(A(q.some,NO.start),q.none,A(q.some,NO.after),q.none):t.fold(q.none,A(q.some,NO.before),q.none,A(q.some,NO.end))},qO=function(e,t,n,r){var o=YD(e,r),i=PO(t,n,o);return PO(t,n,o).bind(B(VO,e)).orThunk((function(){return jO(e,t,n,i,r)}))};B(qO,!1),B(qO,!0);var $O,WO=function(e){return N(e.selection.getSel().modify)},KO=function(e,t,n){var r=e?1:-1;return t.setRng(Bl(n.container(),n.offset()+r).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0},XO=function(e,t){var n=t.selection.getRng(),r=e?Bl.fromRangeEnd(n):Bl.fromRangeStart(n);return!!WO(t)&&(e&&xa(r)?KO(!0,t.selection,r):!(e||!Sa(r))&&KO(!1,t.selection,r))};(function(e){e[e["Br"]=0]="Br",e[e["Block"]=1]="Block",e[e["Wrap"]=2]="Wrap",e[e["Eol"]=3]="Eol"})($O||($O={}));var YO,GO=function(e,t){return e===Pl.Backwards?le(t):t},JO=function(e,t,n){return e===Pl.Forwards?t.next(n):t.prev(n)},QO=function(e,t,n,r){return ro(r.getNode(t===Pl.Forwards))?$O.Br:!1===lg(n,r)?$O.Block:$O.Wrap},ZO=function(e,t,n,r){var o=Mg(n),i=r,a=[];while(i){var u=JO(t,o,i);if(!u)break;if(ro(u.getNode(!1)))return t===Pl.Forwards?{positions:GO(t,a).concat([u]),breakType:$O.Br,breakAt:q.some(u)}:{positions:GO(t,a),breakType:$O.Br,breakAt:q.some(u)};if(u.isVisible()){if(e(i,u)){var c=QO(n,t,i,u);return{positions:GO(t,a),breakType:c,breakAt:q.some(u)}}a.push(u),i=u}else i=u}return{positions:GO(t,a),breakType:$O.Eol,breakAt:q.none()}},eB=function(e,t,n,r){return t(n,r).breakAt.map((function(r){var o=t(n,r).positions;return e===Pl.Backwards?o.concat(r):[r].concat(o)})).getOr([])},tB=function(e,t){return oe(e,(function(e,n){return e.fold((function(){return q.some(n)}),(function(r){return lo(he(r.getClientRects()),he(n.getClientRects()),(function(e,o){var i=Math.abs(t-e.left),a=Math.abs(t-o.left);return a<=i?n:r})).or(e)}))}),q.none())},nB=function(e,t){return he(t.getClientRects()).bind((function(t){return tB(e,t.left)}))},rB=B(ZO,Bl.isAbove,-1),oB=B(ZO,Bl.isBelow,1),iB=B(eB,-1,rB),aB=B(eB,1,oB),uB=function(e,t){return rB(e,t).breakAt.isNone()},cB=function(e,t){return oB(e,t).breakAt.isNone()},sB=function(e){return Jg(e).map((function(t){return[t].concat(oB(e,t).positions)})).getOr([])},fB=function(e){return Qg(e).map((function(t){return rB(e,t).positions.concat(t)})).getOr([])},lB=function(e){var t=function(t){return Q(t,(function(t){var n=Qf(t);return n.node=e,n}))};if(Vr(e))return t(e.getClientRects());if(Zr(e)){var n=e.ownerDocument.createRange();return n.setStart(e,0),n.setEnd(e,e.data.length),t(n.getClientRects())}},dB=function(e){return se(e,lB)};(function(e){e[e["Up"]=-1]="Up",e[e["Down"]=1]="Down"})(YO||(YO={}));var mB,gB,pB=function(e,t,n,r){while(r=sg(r,e,$a,t))if(n(r))return},hB=function(e,t,n,r,o,i){var a=0,u=[],c=function(r){var i=dB([r]);-1===e&&(i=i.reverse());for(var c=0;c<i.length;c++){var f=i[c];if(!n(f,s)){if(u.length>0&&t(f,Ve(u))&&a++,f.line=a,o(f))return!0;u.push(f)}}},s=Ve(i.getClientRects());if(!s)return u;var f=i.getNode();return c(f),pB(e,r,c,f),u},vB=function(e,t){return t.line>e},bB=function(e,t){return t.line===e},yB=B(hB,YO.Up,nl,rl),CB=B(hB,YO.Down,rl,nl),wB=function(e,t,n,r){var o,i,a,u,c=Mg(t),s=[],f=0,l=function(e){return Ve(e.getClientRects())};1===e?(o=c.next,i=rl,a=nl,u=Bl.after(r)):(o=c.prev,i=nl,a=rl,u=Bl.before(r));var d=l(u);do{if(u.isVisible()){var m=l(u);if(!a(m,d)){s.length>0&&i(m,Ve(s))&&f++;var g=Qf(m);if(g.position=u,g.line=f,n(g))return s;s.push(g)}}}while(u=o(u));return s},xB=function(e){return function(t){return vB(e,t)}},SB=function(e){return function(t){return bB(e,t)}},kB=ao,NB=sg,EB=function(e,t){return Math.abs(e.left-t)},_B=function(e,t){return Math.abs(e.right-t)},AB=function(e,t){return e>=t.left&&e<=t.right},RB=function(e,t){return e>=t.top&&e<=t.bottom},DB=function(e){return Be(e,"node")},TB=function(e,t,n){return void 0===n&&(n=U),He(e,(function(e,r){if(AB(t,r))return n(r)?r:e;if(AB(t,e))return n(e)?e:r;var o=Math.min(EB(e,t),_B(e,t)),i=Math.min(EB(r,t),_B(r,t));return i===o&&DB(r)&&kB(r.node)||i<o?r:e}))},OB=function(e,t,n,r,o){var i=NB(r,e,$a,t,!o);do{if(!i||n(i))return}while(i=NB(i,e,$a,t))},BB=function(e,t,n){void 0===n&&(n=!0);var r=[],o=function(e,n){var o=ne(dB([n]),(function(n){return!e(n,t)}));return r=r.concat(o),0===o.length};return r.push(t),OB(YO.Up,e,B(o,nl),t.node,n),OB(YO.Down,e,B(o,rl),t.node,n),r},PB=function(e){return ne(be(e.getElementsByTagName("*")),Qm)},LB=function(e,t){return{node:e.node,before:EB(e,t)<_B(e,t)}},IB=function(e,t,n){var r=dB(PB(e)),o=ne(r,B(RB,n)),i=function(e){return!Gr(e.node)&&!co(e.node)},a=TB(o,t,i);if(a){var u=i(a);if(a=TB(BB(e,a,u),t,i),a&&Qm(a.node))return LB(a,t)}return null},MB=function(e,t){e.selection.setRng(t),nb(e,e.selection.getRng())},FB=function(e,t,n){return q.some(FT(e,t,n))},UB=function(e,t,n,r,o,i){var a=t===Pl.Forwards,u=Mg(e.getBody()),c=B(Sg,a?u.next:u.prev),s=a?r:o;if(!n.collapsed){var f=al(n);if(i(f))return PT(t,e,f,t===Pl.Backwards,!1)}var l=Cg(t,e.getBody(),n);if(s(l))return IT(e,l.getNode(!a));var d=YD(a,c(l)),m=Da(n);if(!d)return m?q.some(n):q.none();if(s(d))return PT(t,e,d.getNode(!a),a,!1);var g=c(d);return g&&s(g)&&kg(d,g)?PT(t,e,g.getNode(!a),a,!1):m?FB(e,d.toRange(),!1):q.none()},zB=function(e,t,n,r,o,i){var a=Cg(t,e.getBody(),n),u=Ve(a.getClientRects()),c=t===YO.Down;if(!u)return q.none();var s,f=c?CB:yB,l=f(e.getBody(),xB(1),a),d=ne(l,SB(1)),m=u.left,g=TB(d,m);if(g&&i(g.node)){var p=Math.abs(m-g.left),h=Math.abs(m-g.right);return PT(t,e,g.node,p<h,!1)}if(s=r(a)?a.getNode():o(a)?a.getNode(!0):al(n),s){var v=wB(t,e.getBody(),xB(1),s),b=TB(ne(v,SB(1)),m);if(b)return FB(e,b.position.toRange(),!1);if(b=Ve(ne(v,SB(0))),b)return FB(e,b.position.toRange(),!1)}return 0===d.length?HB(e,c).filter(c?o:r).map((function(t){return FT(e,t.toRange(),!1)})):q.none()},HB=function(e,t){var n=e.selection.getRng(),r=e.getBody();if(t){var o=Bl.fromRangeEnd(n),i=oB(r,o);return ve(i.positions)}o=Bl.fromRangeStart(n),i=rB(r,o);return he(i.positions)},jB=function(e,t,n){return HB(e,t).filter(n).exists((function(t){return e.selection.setRng(t.toRange()),!0}))},VB=function(e,t){var n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},qB=function(e,t){e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},$B=function(e,t,n){return SO(t,n).map((function(t){return VB(e,t),n}))},WB=function(e,t,n){var r=e.getBody(),o=Bl.fromRangeStart(e.selection.getRng()),i=B(VD,e),a=qO(n,i,r,o);return a.bind((function(n){return $B(e,t,n)}))},KB=function(e,t,n){var r=Q(Af(Cn.fromDom(t.getRoot()),'*[data-mce-selected="inline-boundary"]'),(function(e){return e.dom})),o=ne(r,e),i=ne(n,e);Z(de(o,i),B(qB,!1)),Z(de(i,o),B(qB,!0))},XB=function(e,t){if(e.selection.isCollapsed()&&!0!==e.composing&&t.get()){var n=Bl.fromRangeStart(e.selection.getRng());Bl.isTextPosition(n)&&!1===XD(n)&&(VB(e,zm(t.get(),n)),t.set(null))}},YB=function(e,t,n,r){if(t.selection.isCollapsed()){var o=ne(r,e);Z(o,(function(r){var o=Bl.fromRangeStart(t.selection.getRng());PO(e,t.getBody(),o).bind((function(e){return $B(t,n,e)}))}))}},GB=function(e,t,n){return!!am(e)&&WB(e,t,n).isSome()},JB=function(e,t,n){return!!am(t)&&XO(e,t)},QB=function(e){var t=Gs(null),n=B(VD,e);return e.on("NodeChange",(function(r){!am(e)||en.browser.isIE()&&r.initial||(KB(n,e.dom,r.parents),XB(e,t),YB(n,e,t,r.parents))})),t},ZB=B(JB,!0),eP=B(JB,!1),tP=function(e,t,n){if(am(e)){var r=HB(e,t).getOrThunk((function(){var n=e.selection.getRng();return t?Bl.fromRangeEnd(n):Bl.fromRangeStart(n)}));return PO(B(VD,e),e.getBody(),r).exists((function(t){var r=MO(t);return SO(n,r).exists((function(t){return VB(e,t),!0}))}))}return!1},nP=function(e,t){var n=document.createRange();return n.setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n},rP=function(e){return lo(Jg(e),Qg(e),(function(t,n){var r=YD(!0,t),o=YD(!1,n);return Yg(e,r).forall((function(e){return e.isEqual(o)}))})).getOr(!0)},oP=function(e,t){return function(n){return SO(t,n).exists((function(t){return VB(e,t),!0}))}},iP=function(e,t,n,r){var o=e.getBody(),i=B(VD,e);e.undoManager.ignore((function(){e.selection.setRng(nP(n,r)),e.execCommand("Delete"),PO(i,o,Bl.fromRangeStart(e.selection.getRng())).map(FO).map(oP(e,t))})),e.nodeChanged()},aP=function(e,t){var n=fg(t,e);return n||e},uP=function(e,t,n,r){var o=aP(e.getBody(),r.container()),i=B(VD,e),a=PO(i,o,r);return a.bind((function(e){return n?e.fold(D(q.some(FO(e))),q.none,D(q.some(MO(e))),q.none):e.fold(q.none,D(q.some(MO(e))),q.none,D(q.some(FO(e))))})).map(oP(e,t)).getOrThunk((function(){var u=Wg(n,o,r),c=u.bind((function(e){return PO(i,o,e)}));return lo(a,c,(function(){return WD(i,o,r).exists((function(t){return!!rP(t)&&(Lw(e,n,Cn.fromDom(t)),!0)}))})).orThunk((function(){return c.bind((function(o){return u.map((function(o){return n?iP(e,t,r,o):iP(e,t,o,r),!0}))}))})).getOr(!1)}))},cP=function(e,t,n){if(e.selection.isCollapsed()&&am(e)){var r=Bl.fromRangeStart(e.selection.getRng());return uP(e,t,n,r)}return!1},sP=function(e,t){var n=RC(t,e);return ue(n,Ji).fold(D(n),(function(e){return n.slice(0,e)}))},fP=function(e){return 1===ur(e)},lP=function(e,t,n,r){var o=B(ak,t),i=Q(ne(r,o),(function(e){return e.dom}));if(0===i.length)Lw(t,e,n);else{var a=ik(n.dom,i);t.selection.setRng(a.toRange())}},dP=function(e,t){var n=Cn.fromDom(e.getBody()),r=Cn.fromDom(e.selection.getStart()),o=ne(sP(n,r),fP);return ve(o).exists((function(n){var r=Bl.fromRangeStart(e.selection.getRng());return!(!rT(t,r,n.dom)||uk(n))&&(lP(t,e,n,o),!0)}))},mP=function(e,t){return!!e.selection.isCollapsed()&&dP(e,t)},gP=function(e,t,n){return e._selectionOverrides.hideFakeCaret(),Lw(e,t,Cn.fromDom(n)),!0},pP=function(e,t){var n=t?hC:vC,r=t?Pl.Forwards:Pl.Backwards,o=Cg(r,e.getBody(),e.selection.getRng());return n(o)?gP(e,t,o.getNode(!t)):q.from(YD(t,o)).filter((function(e){return n(e)&&kg(o,e)})).exists((function(n){return gP(e,t,n.getNode(!t))}))},hP=function(e,t){var n=e.selection.getNode();return!!co(n)&&gP(e,t,n)},vP=function(e,t){return e.selection.isCollapsed()?pP(e,t):hP(e,t)},bP=function(e){return Si(e,(function(e){return io(e.dom)||ao(e.dom)})).exists((function(e){return io(e.dom)}))},yP=function(e){var t=parseInt(e,10);return isNaN(t)?0:t},CP=function(e,t){var n=e||Qi(t)?"margin":"padding",r="rtl"===Eo(t,"direction")?"-right":"-left";return n+r},wP=function(e,t,n,r,o,i){var a=CP(n,Cn.fromDom(i));if("outdent"===t){var u=Math.max(0,yP(i.style[a])-r);e.setStyle(i,a,u?u+o:"")}else{u=yP(i.style[a])+r+o;e.setStyle(i,a,u)}},xP=function(e,t){return fe(t,(function(t){var n=CP(Kd(e),t),r=Ao(t,n).map(yP).getOr(0),o=e.dom.getContentEditable(t.dom);return"false"!==o&&r>0}))},SP=function(e){var t=EP(e);return!e.mode.isReadOnly()&&(t.length>1||xP(e,t))},kP=function(e){return na(e)||ra(e)},NP=function(e){return Gn(e).exists(kP)},EP=function(e){return ne(Vf(e.selection.getSelectedBlocks()),(function(e){return!kP(e)&&!NP(e)&&bP(e)}))},_P=function(e,t){var n=e.dom,r=e.selection,o=e.formatter,i=Xd(e),a=/[a-z%]+$/i.exec(i)[0],u=parseInt(i,10),c=Kd(e),s=Nd(e);e.queryCommandState("InsertUnorderedList")||e.queryCommandState("InsertOrderedList")||""!==s||n.getParent(r.getNode(),n.isBlock)||o.apply("div"),Z(EP(e),(function(e){wP(n,t,c,u,a,e.dom)}))},AP=function(e,t){if(e.selection.isCollapsed()&&SP(e)){var n=e.dom,r=e.selection.getRng(),o=Bl.fromRangeStart(r),i=n.getParent(r.startContainer,n.isBlock);if(null!==i&&PC(Cn.fromDom(i),o))return _P(e,"outdent"),!0}return!1},RP=function(e,t){e.getDoc().execCommand(t,!1,null)},DP=function(e,t){AP(e)||sO(e,!1)||qT(e,!1)||cP(e,t,!1)||kT(e,!1)||Tx(e)||lO(e,!1)||vP(e,!1)||TT(e)||mP(e,!1)||(RP(e,"Delete"),nT(e))},TP=function(e,t){sO(e,!0)||qT(e,!0)||cP(e,t,!0)||kT(e,!0)||Tx(e)||lO(e,!0)||vP(e,!0)||TT(e)||mP(e,!0)||RP(e,"ForwardDelete")},OP=function(e,t){e.addCommand("delete",(function(){DP(e,t)})),e.addCommand("forwardDelete",(function(){TP(e,t)}))},BP=5,PP=400,LP=function(e){return void 0===e.touches||1!==e.touches.length?q.none():q.some(e.touches[0])},IP=function(e,t){var n=Math.abs(e.clientX-t.x),r=Math.abs(e.clientY-t.y);return n>BP||r>BP},MP=function(e){var t=mf(),n=Gs(!1),r=pf((function(t){e.fire("longpress",qe(qe({},t),{type:"longpress"})),n.set(!0)}),PP);e.on("touchstart",(function(e){LP(e).each((function(o){r.cancel();var i={x:o.clientX,y:o.clientY,target:e.target};r.throttle(e),n.set(!1),t.set(i)}))}),!0),e.on("touchmove",(function(o){r.cancel(),LP(o).each((function(r){t.on((function(o){IP(r,o)&&(t.clear(),n.set(!1),e.fire("longpresscancel"))}))}))}),!0),e.on("touchend touchcancel",(function(o){r.cancel(),"touchcancel"!==o.type&&t.get().filter((function(e){return e.target.isEqualNode(o.target)})).each((function(){n.get()?o.preventDefault():e.fire("tap",qe(qe({},o),{type:"tap"}))}))}),!0)},FP=function(e,t){return Oe(e,t.nodeName)},UP=function(e,t){return!!Zr(t)||!!Vr(t)&&(!FP(e,t)&&!xp(t))},zP=function(e,t,n){return J(AC(Cn.fromDom(n),Cn.fromDom(t)),(function(t){return FP(e,t.dom)}))},HP=function(e,t){if(Zr(t)){if(0===t.nodeValue.length)return!0;if(/^\s+$/.test(t.nodeValue)&&(!t.nextSibling||FP(e,t.nextSibling)))return!0}return!1},jP=function(e){var t,n,r,o=e.dom,i=e.selection,a=e.schema,u=a.getBlockElements(),c=i.getStart(),s=e.getBody(),f=Nd(e);if(c&&Vr(c)&&f){var l=s.nodeName.toLowerCase();if(a.isValidChild(l,f.toLowerCase())&&!zP(u,s,c)){var d=i.getRng(),m=d.startContainer,g=d.startOffset,p=d.endContainer,h=d.endOffset,v=dy(e);c=s.firstChild;while(c)if(UP(u,c)){if(HP(u,c)){n=c,c=c.nextSibling,o.remove(n);continue}t||(t=o.create(f,Ed(e)),c.parentNode.insertBefore(t,c),r=!0),n=c,c=c.nextSibling,t.appendChild(n)}else t=null,c=c.nextSibling;r&&v&&(d.setStart(m,g),d.setEnd(p,h),i.setRng(d),e.nodeChanged())}}},VP=function(e){Nd(e)&&e.on("NodeChange",B(jP,e))},qP=function(e){return _i(Cn.fromDom(e.getBody()),"*[data-mce-caret]").map((function(e){return e.dom})).getOrNull()},$P=function(e){e.selection.setRng(e.selection.getRng())},WP=function(e,t){t.hasAttribute("data-mce-caret")&&(Ra(t),$P(e),e.selection.scrollIntoView(t))},KP=function(e,t){var n=qP(e);if(n)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void WP(e,n)):void(Ca(n)&&(WP(e,n),e.undoManager.add()))},XP=function(e){e.on("keyup compositionstart",B(KP,e))},YP=ao,GP=function(e,t,n){return UB(t,e,n,CC,wC,YP)},JP=function(e,t,n){var r=function(e){return CC(e)||bC(e)},o=function(e){return wC(e)||yC(e)};return zB(t,e,n,r,o,YP)},QP=function(e){var t=e.dom.create(Nd(e));return(!en.ie||en.ie>=11)&&(t.innerHTML='<br data-mce-bogus="1">'),t},ZP=function(e,t,n){var r=Mg(e.getBody()),o=B(Sg,1===t?r.next:r.prev);if(n.collapsed&&bm(e)){var i=e.dom.getParent(n.startContainer,"PRE");if(!i)return;var a=o(Bl.fromRangeStart(n));if(!a){var u=QP(e);1===t?e.$(i).after(u):e.$(i).before(u),e.selection.select(u,!0),e.selection.collapse()}}},eL=function(e,t){var n=t?Pl.Forwards:Pl.Backwards,r=e.selection.getRng();return GP(n,e,r).orThunk((function(){return ZP(e,n,r),q.none()}))},tL=function(e,t){var n=t?1:-1,r=e.selection.getRng();return JP(n,e,r).orThunk((function(){return ZP(e,n,r),q.none()}))},nL=function(e,t){return eL(e,t).exists((function(t){return MB(e,t),!0}))},rL=function(e,t){return tL(e,t).exists((function(t){return MB(e,t),!0}))},oL=function(e,t){var n=t?wC:CC;return jB(e,t,n)},iL=function(e){return G(["figcaption"],Mn(e))},aL=function(e){var t=document.createRange();return t.setStartBefore(e.dom),t.setEndBefore(e.dom),t},uL=function(e,t,n){n?wr(e,t):Cr(e,t)},cL=function(e,t){var n=Cn.fromTag("br");return uL(e,n,t),aL(n)},sL=function(e,t,n,r){var o=Cn.fromTag(n),i=Cn.fromTag("br");return bo(o,r),wr(o,i),uL(e,o,t),aL(i)},fL=function(e,t,n,r){return""===t?cL(e,r):sL(e,r,t,n)},lL=function(e,t){var n=B(Bn,t);return Si(Cn.fromDom(e.container()),Ji,n).filter(iL)},dL=function(e,t,n){return t?cB(e.dom,n):uB(e.dom,n)},mL=function(e,t){var n=Cn.fromDom(e.getBody()),r=Bl.fromRangeStart(e.selection.getRng()),o=Nd(e),i=Ed(e);return lL(r,n).exists((function(){if(dL(n,t,r)){var a=fL(n,o,i,t);return e.selection.setRng(a),!0}return!1}))},gL=function(e,t){return!!e.selection.isCollapsed()&&mL(e,t)},pL=function(e){return Q(e,(function(e){return qe({shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0,action:_},e)}))},hL=function(e,t){return t.keyCode===e.keyCode&&t.shiftKey===e.shiftKey&&t.altKey===e.altKey&&t.ctrlKey===e.ctrlKey&&t.metaKey===e.metaKey},vL=function(e,t){return se(pL(e),(function(e){return hL(e,t)?[e]:[]}))},bL=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return function(){return e.apply(null,t)}},yL=function(e,t){return ae(vL(e,t),(function(e){return e.action()}))},CL=function(e,t){var n=t?Pl.Forwards:Pl.Backwards,r=e.selection.getRng();return UB(e,n,r,hC,vC,co).exists((function(t){return MB(e,t),!0}))},wL=function(e,t){var n=t?1:-1,r=e.selection.getRng();return zB(e,n,r,hC,vC,co).exists((function(t){return MB(e,t),!0}))},xL=function(e,t){var n=t?vC:hC;return jB(e,t,n)},SL=function(e,t){return{left:e.left-t,top:e.top-t,right:e.right+2*t,bottom:e.bottom+2*t,width:e.width+t,height:e.height+t}},kL=function(e,t){return se(t,(function(t){var n=SL(Qf(t.getBoundingClientRect()),-1);return[{x:n.left,y:e(n),cell:t},{x:n.right,y:e(n),cell:t}]}))},NL=function(e,t,n){return oe(e,(function(e,r){return e.fold((function(){return q.some(r)}),(function(e){var o=Math.sqrt(Math.abs(e.x-t)+Math.abs(e.y-n)),i=Math.sqrt(Math.abs(r.x-t)+Math.abs(r.y-n));return q.some(i<o?r:e)}))}),q.none())},EL=function(e,t,n,r,o){var i=Af(Cn.fromDom(n),"td,th,caption").map((function(e){return e.dom})),a=ne(kL(e,i),(function(e){return t(e,o)}));return NL(a,r,o).map((function(e){return e.cell}))},_L=function(e){return e.bottom},AL=function(e){return e.top},RL=function(e,t){return e.y<t},DL=function(e,t){return e.y>t},TL=B(EL,_L,RL),OL=B(EL,AL,DL),BL=function(e,t){return he(t.getClientRects()).bind((function(t){return TL(e,t.left,t.top)})).bind((function(e){return nB(fB(e),t)}))},PL=function(e,t){return ve(t.getClientRects()).bind((function(t){return OL(e,t.left,t.top)})).bind((function(e){return nB(sB(e),t)}))},LL=function(e,t,n){return n.breakAt.exists((function(n){return e(t,n).breakAt.isSome()}))},IL=function(e){return e.breakType===$O.Wrap&&0===e.positions.length},ML=function(e){return e.breakType===$O.Br&&1===e.positions.length},FL=function(e,t,n){var r=e(t,n);return IL(r)||!ro(n.getNode())&&ML(r)?!LL(e,t,r):r.breakAt.isNone()},UL=B(FL,rB),zL=B(FL,oB),HL=function(e,t,n){var r=Bl.fromRangeStart(t);return Xg(!e,n).exists((function(e){return e.isEqual(r)}))},jL=function(e,t,n,r){var o=e.selection.getRng(),i=t?1:-1;return!(!Gm()||!HL(t,o,n))&&(PT(i,e,n,!t,!1).each((function(t){MB(e,t)})),!0)},VL=function(e,t,n){return BL(t,n).orThunk((function(){return he(n.getClientRects()).bind((function(n){return tB(iB(e,Bl.before(t)),n.left)}))})).getOr(Bl.before(t))},qL=function(e,t,n){return PL(t,n).orThunk((function(){return he(n.getClientRects()).bind((function(n){return tB(aB(e,Bl.after(t)),n.left)}))})).getOr(Bl.after(t))},$L=function(e,t){var n=t.getNode(e);return Vr(n)&&"TABLE"===n.nodeName?q.some(n):q.none()},WL=function(e,t,n,r){var o=Nd(t);o?t.undoManager.transact((function(){var r=Cn.fromTag(o);bo(r,Ed(t)),wr(r,Cn.fromTag("br")),e?yr(Cn.fromDom(n),r):br(Cn.fromDom(n),r);var i=t.dom.createRng();i.setStart(r.dom,0),i.setEnd(r.dom,0),MB(t,i)})):MB(t,r.toRange())},KL=function(e,t,n){var r=$L(!!t,n),o=!1===t;r.fold((function(){return MB(e,n.toRange())}),(function(r){return Xg(o,e.getBody()).filter((function(e){return e.isEqual(n)})).fold((function(){return MB(e,n.toRange())}),(function(o){return WL(t,e,r,n)}))}))},XL=function(e,t,n,r){var o=e.selection.getRng(),i=Bl.fromRangeStart(o),a=e.getBody();if(!t&&UL(r,i)){var u=VL(a,n,i);return KL(e,t,u),!0}if(t&&zL(r,i)){u=qL(a,n,i);return KL(e,t,u),!0}return!1},YL=function(e,t,n){return q.from(e.dom.getParent(e.selection.getNode(),"td,th")).bind((function(r){return q.from(e.dom.getParent(r,"table")).map((function(o){return n(e,t,o,r)}))})).getOr(!1)},GL=function(e,t){return YL(e,t,jL)},JL=function(e,t){return YL(e,t,XL)},QL=function(e,t,n){var r=$t().os;yL([{keyCode:tv.RIGHT,action:bL(nL,e,!0)},{keyCode:tv.LEFT,action:bL(nL,e,!1)},{keyCode:tv.UP,action:bL(rL,e,!1)},{keyCode:tv.DOWN,action:bL(rL,e,!0)},{keyCode:tv.RIGHT,action:bL(GL,e,!0)},{keyCode:tv.LEFT,action:bL(GL,e,!1)},{keyCode:tv.UP,action:bL(JL,e,!1)},{keyCode:tv.DOWN,action:bL(JL,e,!0)},{keyCode:tv.RIGHT,action:bL(CL,e,!0)},{keyCode:tv.LEFT,action:bL(CL,e,!1)},{keyCode:tv.UP,action:bL(wL,e,!1)},{keyCode:tv.DOWN,action:bL(wL,e,!0)},{keyCode:tv.RIGHT,action:bL(GB,e,t,!0)},{keyCode:tv.LEFT,action:bL(GB,e,t,!1)},{keyCode:tv.RIGHT,ctrlKey:!r.isOSX(),altKey:r.isOSX(),action:bL(ZB,e,t)},{keyCode:tv.LEFT,ctrlKey:!r.isOSX(),altKey:r.isOSX(),action:bL(eP,e,t)},{keyCode:tv.UP,action:bL(gL,e,!1)},{keyCode:tv.DOWN,action:bL(gL,e,!0)}],n).each((function(e){n.preventDefault()}))},ZL=function(e,t){e.on("keydown",(function(n){!1===n.isDefaultPrevented()&&QL(e,t,n)}))},eI=function(e,t,n){yL([{keyCode:tv.BACKSPACE,action:bL(AP,e,!1)},{keyCode:tv.BACKSPACE,action:bL(sO,e,!1)},{keyCode:tv.DELETE,action:bL(sO,e,!0)},{keyCode:tv.BACKSPACE,action:bL(qT,e,!1)},{keyCode:tv.DELETE,action:bL(qT,e,!0)},{keyCode:tv.BACKSPACE,action:bL(cP,e,t,!1)},{keyCode:tv.DELETE,action:bL(cP,e,t,!0)},{keyCode:tv.BACKSPACE,action:bL(Tx,e,!1)},{keyCode:tv.DELETE,action:bL(Tx,e,!0)},{keyCode:tv.BACKSPACE,action:bL(lO,e,!1)},{keyCode:tv.DELETE,action:bL(lO,e,!0)},{keyCode:tv.BACKSPACE,action:bL(vP,e,!1)},{keyCode:tv.DELETE,action:bL(vP,e,!0)},{keyCode:tv.BACKSPACE,action:bL(TT,e,!1)},{keyCode:tv.DELETE,action:bL(TT,e,!0)},{keyCode:tv.BACKSPACE,action:bL(kT,e,!1)},{keyCode:tv.DELETE,action:bL(kT,e,!0)},{keyCode:tv.BACKSPACE,action:bL(mP,e,!1)},{keyCode:tv.DELETE,action:bL(mP,e,!0)}],n).each((function(e){n.preventDefault()}))},tI=function(e,t){yL([{keyCode:tv.BACKSPACE,action:bL(cO,e)},{keyCode:tv.DELETE,action:bL(cO,e)}],t)},nI=function(e,t){e.on("keydown",(function(n){!1===n.isDefaultPrevented()&&eI(e,t,n)})),e.on("keyup",(function(t){!1===t.isDefaultPrevented()&&tI(e,t)}))},rI=function(e){while(e){if(1===e.nodeType||3===e.nodeType&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}},oI=function(e,t){var n,r=t,o=e.dom,i=e.schema.getMoveCaretBeforeOnEnterElements();if(t){if(/^(LI|DT|DD)$/.test(t.nodeName)){var a=rI(t.firstChild);a&&/^(UL|OL|DL)$/.test(a.nodeName)&&t.insertBefore(o.doc.createTextNode(sa),t.firstChild)}var u=o.createRng();if(t.normalize(),t.hasChildNodes()){var c=new zi(t,t);while(n=c.current()){if(Zr(n)){u.setStart(n,0),u.setEnd(n,0);break}if(i[n.nodeName.toLowerCase()]){u.setStartBefore(n),u.setEndBefore(n);break}r=n,n=c.next()}n||(u.setStart(r,0),u.setEnd(r,0))}else ro(t)?t.nextSibling&&o.isBlock(t.nextSibling)?(u.setStartBefore(t),u.setEndBefore(t)):(u.setStartAfter(t),u.setEndAfter(t)):(u.setStart(t,0),u.setEnd(t,0));e.selection.setRng(u),nb(e,u)}},iI=function(e,t){var n,r,o=e.getRoot();n=t;while(n!==o&&"false"!==e.getContentEditable(n))"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},aI=function(e){return q.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock))},uI=function(e){return aI(e).fold(D(""),(function(e){return e.nodeName.toUpperCase()}))},cI=function(e){return aI(e).filter((function(e){return ra(Cn.fromDom(e))})).isSome()},sI=function(e,t){return e.firstChild&&e.firstChild.nodeName===t},fI=function(e){var t;return(null===(t=e.parentNode)||void 0===t?void 0:t.firstChild)===e},lI=function(e,t){return e&&e.parentNode&&e.parentNode.nodeName===t},dI=function(e){return e&&/^(OL|UL|LI)$/.test(e.nodeName)},mI=function(e){return dI(e)&&dI(e.parentNode)},gI=function(e){var t=e.parentNode;return/^(LI|DT|DD)$/.test(t.nodeName)?t:e},pI=function(e,t,n){var r=e[n?"firstChild":"lastChild"];while(r){if(Vr(r))break;r=r[n?"nextSibling":"previousSibling"]}return r===t},hI=function(e,t,n,r,o){var i=e.dom,a=e.selection.getRng();if(n!==e.getBody()){mI(n)&&(o="LI");var u=o?t(o):i.create("BR");if(pI(n,r,!0)&&pI(n,r,!1))if(lI(n,"LI")){var c=gI(n);i.insertAfter(u,c),fI(n)?i.remove(c):i.remove(n)}else i.replace(u,n);else if(pI(n,r,!0))lI(n,"LI")?(i.insertAfter(u,gI(n)),u.appendChild(i.doc.createTextNode(" ")),u.appendChild(n)):n.parentNode.insertBefore(u,n),i.remove(r);else if(pI(n,r,!1))i.insertAfter(u,gI(n)),i.remove(r);else{n=gI(n);var s=a.cloneRange();s.setStartAfter(r),s.setEndAfter(n);var f=s.extractContents();"LI"===o&&sI(f,"LI")?(u=f.firstChild,i.insertAfter(f,n)):(i.insertAfter(f,n),i.insertAfter(u,n)),i.remove(r)}oI(e,u)}},vI=function(e){Z(_f(Cn.fromDom(e),jn),(function(e){var t=e.dom;t.nodeValue=ga(t.nodeValue)}))},bI=function(e,t){return t&&"A"===t.nodeName&&e.isEmpty(t)},yI=function(e){return e&&/^(TD|TH|CAPTION)$/.test(e.nodeName)},CI=function(e){e.innerHTML='<br data-mce-bogus="1">'},wI=function(e,t){return e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t},xI=function(e,t){return t&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&"true"!==e.getContentEditable(t)},SI=function(e,t,n){var r,o=n,i=[];if(o){while(o=o.firstChild){if(e.isBlock(o))return;Vr(o)&&!t[o.nodeName.toLowerCase()]&&i.push(o)}r=i.length;while(r--)o=i[r],(!o.hasChildNodes()||o.firstChild===o.lastChild&&""===o.firstChild.nodeValue||bI(e,o))&&e.remove(o)}},kI=function(e,t,n){return!1===Zr(t)?n:e?1===n&&t.data.charAt(n-1)===da?0:n:n===t.data.length-1&&t.data.charAt(n)===da?t.data.length:n},NI=function(e){var t=e.cloneRange();return t.setStart(e.startContainer,kI(!0,e.startContainer,e.startOffset)),t.setEnd(e.endContainer,kI(!1,e.endContainer,e.endOffset)),t},EI=function(e){do{Zr(e)&&(e.nodeValue=e.nodeValue.replace(/^[\r\n]+/,"")),e=e.firstChild}while(e)},_I=function(e,t){var n,r,o=e.getRoot();n=t;while(n!==o&&"false"!==e.getContentEditable(n))"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},AI=function(e,t,n){var r=e.dom;q.from(n.style).map(r.parseStyle).each((function(e){var n=Ro(Cn.fromDom(t)),o=qe(qe({},n),e);r.setStyles(t,o)}));var o=q.from(n.class).map((function(e){return e.split(/\s+/)})),i=q.from(t.className).map((function(e){return ne(e.split(/\s+/),(function(e){return""!==e}))}));lo(o,i,(function(e,n){var o=ne(n,(function(t){return!G(e,t)})),i=We(We([],e,!0),o,!0);r.setAttrib(t,"class",i.join(" "))}));var a=["style","class"],u=Ae(n,(function(e,t){return!G(a,t)}));r.setAttribs(t,u)},RI=function(e,t){var n=Nd(e);if(n&&n.toLowerCase()===t.tagName.toLowerCase()){var r=Ed(e);AI(e,t,r)}},DI=function(e,t,n,r,o){var i,a,u,c,s,f,l=t||"P",d=e.dom,m=_I(d,r);if(a=d.getParent(r,d.isBlock),!a||!xI(d,a)){if(a=a||m,f=a===e.getBody()||yI(a)?a.nodeName.toLowerCase():a.parentNode.nodeName.toLowerCase(),!a.hasChildNodes())return i=d.create(l),RI(e,i),a.appendChild(i),n.setStart(i,0),n.setEnd(i,0),i;c=r;while(c.parentNode!==a)c=c.parentNode;while(c&&!d.isBlock(c))u=c,c=c.previousSibling;if(u&&e.schema.isValidChild(f,l.toLowerCase())){i=d.create(l),RI(e,i),u.parentNode.insertBefore(i,u),c=u;while(c&&!d.isBlock(c))s=c.nextSibling,i.appendChild(c),c=s;n.setStart(r,o),n.setEnd(r,o)}}return r},TI=function(e,t){t.normalize();var n=t.lastChild;n&&!/^(left|right)$/gi.test(e.getStyle(n,"float",!0))||e.add(t,"br")},OI=function(e,t){var n,r,o,i,a,u,c,s,f,l,d=e.dom,m=e.schema,g=m.getNonEmptyElements(),p=e.selection.getRng(),h=function(t){var n,o,a,u=r,c=m.getTextInlineElements();if(n=t||"TABLE"===s||"HR"===s?d.create(t||f):i.cloneNode(!1),a=n,!1===Rd(e))d.setAttrib(n,"style",null),d.setAttrib(n,"class",null);else do{if(c[u.nodeName]){if(ep(u)||xp(u))continue;o=u.cloneNode(!1),d.setAttrib(o,"id",""),n.hasChildNodes()?(o.appendChild(n.firstChild),n.appendChild(o)):(a=o,n.appendChild(o))}}while((u=u.parentNode)&&u!==w);return RI(e,n),CI(a),n},v=function(e){var t,n,a=kI(e,r,o);if(Zr(r)&&(e?a>0:a<r.nodeValue.length))return!1;if(r.parentNode===i&&l&&!e)return!0;if(e&&Vr(r)&&r===i.firstChild)return!0;if(wI(r,"TABLE")||wI(r,"HR"))return l&&!e||!l&&e;var u=new zi(r,i);Zr(r)&&(e&&0===a?u.prev():e||a!==r.nodeValue.length||u.next());while(t=u.current()){if(Vr(t)){if(!t.getAttribute("data-mce-bogus")&&(n=t.nodeName.toLowerCase(),g[n]&&"br"!==n))return!1}else if(Zr(t)&&!Ka(t.nodeValue))return!1;e?u.prev():u.next()}return!0},b=function(){a=/^(H[1-6]|PRE|FIGURE)$/.test(s)&&"HGROUP"!==x?h(f):h(),Dd(e)&&xI(d,c)&&d.isEmpty(i)?a=d.split(c,i):d.insertAfter(a,i),oI(e,a)};wv(d,p).each((function(e){p.setStart(e.startContainer,e.startOffset),p.setEnd(e.endContainer,e.endOffset)})),r=p.startContainer,o=p.startOffset,f=Nd(e);var y=!(!t||!t.shiftKey),C=!(!t||!t.ctrlKey);Vr(r)&&r.hasChildNodes()&&(l=o>r.childNodes.length-1,r=r.childNodes[Math.min(o,r.childNodes.length-1)]||r,o=l&&Zr(r)?r.nodeValue.length:0);var w=_I(d,r);if(w){(f&&!y||!f&&y)&&(r=DI(e,f,p,r,o)),i=d.getParent(r,d.isBlock),c=i?d.getParent(i.parentNode,d.isBlock):null,s=i?i.nodeName.toUpperCase():"";var x=c?c.nodeName.toUpperCase():"";"LI"!==x||C||(i=c,c=c.parentNode,s=x),/^(LI|DT|DD)$/.test(s)&&d.isEmpty(i)?hI(e,h,c,i,f):f&&i===e.getBody()||(f=f||"P",va(i)?(a=Ra(i),d.isEmpty(i)&&CI(i),RI(e,a),oI(e,a)):v()?b():v(!0)?(a=i.parentNode.insertBefore(h(),i),oI(e,wI(i,"HR")?a:i)):(n=NI(p).cloneRange(),n.setEndAfter(i),u=n.extractContents(),vI(u),EI(u),a=u.firstChild,d.insertAfter(u,i),SI(d,g,a),TI(d,i),d.isEmpty(i)&&CI(i),a.normalize(),d.isEmpty(a)?(d.remove(a),b()):(RI(e,a),oI(e,a))),d.setAttrib(a,"id",""),e.fire("NewBlock",{newBlock:a}))}},BI=function(e,t,n){var r,o=new zi(t,n),i=e.getNonEmptyElements();while(r=o.next())if(i[r.nodeName.toLowerCase()]||r.length>0)return!0},PI=function(e,t,n){var r=e.dom.createRng();n?(r.setStartBefore(t),r.setEndBefore(t)):(r.setStartAfter(t),r.setEndAfter(t)),e.selection.setRng(r),nb(e,r)},LI=function(e,t){var n,r,o=e.selection,i=e.dom,a=o.getRng();wv(i,a).each((function(e){a.setStart(e.startContainer,e.startOffset),a.setEnd(e.endContainer,e.endOffset)}));var u=a.startOffset,c=a.startContainer;if(1===c.nodeType&&c.hasChildNodes()){var s=u>c.childNodes.length-1;c=c.childNodes[Math.min(u,c.childNodes.length-1)]||c,u=s&&3===c.nodeType?c.nodeValue.length:0}var f=i.getParent(c,i.isBlock),l=f?i.getParent(f.parentNode,i.isBlock):null,d=l?l.nodeName.toUpperCase():"",m=!(!t||!t.ctrlKey);"LI"!==d||m||(f=l),c&&3===c.nodeType&&u>=c.nodeValue.length&&(BI(e.schema,c,f)||(n=i.create("br"),a.insertNode(n),a.setStartAfter(n),a.setEndAfter(n),r=!0)),n=i.create("br"),Fl(i,a,n),PI(e,n,r),e.undoManager.add()},II=function(e,t){var n=Cn.fromTag("br");br(Cn.fromDom(t),n),e.undoManager.add()},MI=function(e,t){UI(e.getBody(),t)||yr(Cn.fromDom(t),Cn.fromTag("br"));var n=Cn.fromTag("br");yr(Cn.fromDom(t),n),PI(e,n.dom,!1),e.undoManager.add()},FI=function(e){return ro(e.getNode())},UI=function(e,t){return!!FI(Bl.after(t))||Yg(e,Bl.after(t)).map((function(e){return ro(e.getNode())})).getOr(!1)},zI=function(e){return e&&"A"===e.nodeName&&"href"in e},HI=function(e){return e.fold(F,zI,zI,F)},jI=function(e){var t=B(VD,e),n=Bl.fromRangeStart(e.selection.getRng());return PO(t,e.getBody(),n).filter(HI)},VI=function(e,t){t.fold(_,B(II,e),B(MI,e),_)},qI=function(e,t){var n=jI(e);n.isSome()?n.each(B(VI,e)):LI(e,t)},$I=function(e,t){return aI(e).filter((function(e){return t.length>0&&Rn(Cn.fromDom(e),t)})).isSome()},WI=function(e){return $I(e,_d(e))},KI=function(e){return $I(e,Ad(e))},XI=ei.generate([{br:[]},{block:[]},{none:[]}]),YI=function(e,t){return KI(e)},GI=function(e){return function(t,n){var r=""===Nd(t);return r===e}},JI=function(e){return function(t,n){return cI(t)===e}},QI=function(e,t){return function(n,r){var o=uI(n)===e.toUpperCase();return o===t}},ZI=function(e){return QI("pre",e)},eM=function(){return QI("summary",!0)},tM=function(e){return function(t,n){return kd(t)===e}},nM=function(e,t){return WI(e)},rM=function(e,t){return t},oM=function(e){var t=Nd(e),n=iI(e.dom,e.selection.getStart());return n&&e.schema.isValidChild(n.nodeName,t||"P")},iM=function(e,t){return function(n,r){var o=oe(e,(function(e,t){return e&&t(n,r)}),!0);return o?q.some(t):q.none()}},aM=function(e,t){return kO([iM([YI],XI.none()),iM([eM()],XI.br()),iM([ZI(!0),tM(!1),rM],XI.br()),iM([ZI(!0),tM(!1)],XI.block()),iM([ZI(!0),tM(!0),rM],XI.block()),iM([ZI(!0),tM(!0)],XI.br()),iM([JI(!0),rM],XI.br()),iM([JI(!0)],XI.block()),iM([GI(!0),rM,oM],XI.block()),iM([GI(!0)],XI.br()),iM([nM],XI.br()),iM([GI(!1),rM],XI.br()),iM([oM],XI.block())],[e,!(!t||!t.shiftKey)]).getOr(XI.none())},uM=function(e,t){aM(e,t).fold((function(){qI(e,t)}),(function(){OI(e,t)}),_)},cM=function(e,t){t.isDefaultPrevented()||(t.preventDefault(),hE(e.undoManager),e.undoManager.transact((function(){!1===e.selection.isCollapsed()&&e.execCommand("Delete"),uM(e,t)})))},sM=function(e){e.on("keydown",(function(t){t.keyCode===tv.ENTER&&cM(e,t)}))},fM=function(e,t,n){yL([{keyCode:tv.END,action:bL(oL,e,!0)},{keyCode:tv.HOME,action:bL(oL,e,!1)},{keyCode:tv.END,action:bL(xL,e,!0)},{keyCode:tv.HOME,action:bL(xL,e,!1)},{keyCode:tv.END,action:bL(tP,e,!0,t)},{keyCode:tv.HOME,action:bL(tP,e,!1,t)}],n).each((function(e){n.preventDefault()}))},lM=function(e,t){e.on("keydown",(function(n){!1===n.isDefaultPrevented()&&fM(e,t,n)}))},dM=$t().browser,mM=function(e){var t=gf((function(){e.composing||lw(e)}),0);dM.isIE()&&(e.on("keypress",(function(e){t.throttle()})),e.on("remove",(function(e){t.cancel()})))},gM=function(e){mM(e),e.on("input",(function(t){!1===t.isComposing&&lw(e)}))},pM=$t(),hM=function(e,t,n){yL([{keyCode:tv.PAGE_UP,action:bL(tP,e,!1,t)},{keyCode:tv.PAGE_DOWN,action:bL(tP,e,!0,t)}],n)},vM=function(e){return e.stopImmediatePropagation()},bM=function(e){return e.keyCode===tv.PAGE_UP||e.keyCode===tv.PAGE_DOWN},yM=function(e,t,n){n&&!e.get()?t.on("NodeChange",vM,!0):!n&&e.get()&&t.off("NodeChange",vM),e.set(n)},CM=function(e,t){if(!pM.os.isOSX()){var n=Gs(!1);e.on("keydown",(function(t){bM(t)&&yM(n,e,!0)})),e.on("keyup",(function(r){!1===r.isDefaultPrevented()&&hM(e,t,r),bM(r)&&n.get()&&(yM(n,e,!1),e.nodeChanged())}))}},wM=function(e,t){var n=t.container(),r=t.offset();return Zr(n)?(n.insertData(r,e),q.some(Bl(n,r+e.length))):wg(t).map((function(n){var r=Cn.fromText(e);return t.isAtEnd()?yr(n,r):br(n,r),Bl(r.dom,e.length)}))},xM=B(wM,sa),SM=B(wM," "),kM=function(e){return function(t){return t.fold((function(t){return Gg(e.dom,Bl.before(t))}),(function(e){return Jg(e)}),(function(e){return Qg(e)}),(function(t){return Yg(e.dom,Bl.after(t))}))}},NM=function(e,t){return function(n){return ZC(e,n)?xM(t):SM(t)}},EM=function(e){return function(t){return e.selection.setRng(t.toRange()),e.nodeChanged(),!0}},_M=function(e){var t=Bl.fromRangeStart(e.selection.getRng()),n=Cn.fromDom(e.getBody());if(e.selection.isCollapsed()){var r=B(VD,e),o=Bl.fromRangeStart(e.selection.getRng());return PO(r,e.getBody(),o).bind(kM(n)).bind(NM(n,t)).exists(EM(e))}return!1},AM=function(e,t){yL([{keyCode:tv.SPACEBAR,action:bL(_M,e)}],t).each((function(e){t.preventDefault()}))},RM=function(e){e.on("keydown",(function(t){!1===t.isDefaultPrevented()&&AM(e,t)}))},DM=function(e){var t=QB(e);return XP(e),ZL(e,t),nI(e,t),sM(e),RM(e),gM(e),lM(e,t),CM(e,t),t},TM=function(e){return OE(e)?Gs(null):DM(e)},OM=function(){function e(e){var t;this.lastPath=[],this.editor=e;var n=this;"onselectionchange"in e.getDoc()||e.on("NodeChange click mouseup keyup focus",(function(n){var r=e.selection.getRng(),o={startContainer:r.startContainer,startOffset:r.startOffset,endContainer:r.endContainer,endOffset:r.endOffset};"nodechange"!==n.type&&sv(o,t)||e.fire("SelectionChange"),t=o})),e.on("contextmenu",(function(){e.fire("SelectionChange")})),e.on("SelectionChange",(function(){var t=e.selection.getStart(!0);!t||!en.range&&e.selection.isCollapsed()||Nh(e)&&!n.isSameElementPath(t)&&e.dom.isChildOf(t,e.getBody())&&e.nodeChanged({selectionChange:!0})})),e.on("mouseup",(function(t){!t.isDefaultPrevented()&&Nh(e)&&("IMG"===e.selection.getNode().nodeName?Ii.setEditorTimeout(e,(function(){e.nodeChanged()})):e.nodeChanged())}))}return e.prototype.nodeChanged=function(e){var t,n,r,o=this.editor.selection;this.editor.initialized&&o&&!Cm(this.editor)&&!this.editor.mode.isReadOnly()&&(r=this.editor.getBody(),t=o.getStart(!0)||r,t.ownerDocument===this.editor.getDoc()&&this.editor.dom.isChildOf(t,r)||(t=r),n=[],this.editor.dom.getParent(t,(function(e){if(e===r)return!0;n.push(e)})),e=e||{},e.element=t,e.parents=n,this.editor.fire("NodeChange",e))},e.prototype.isSameElementPath=function(e){var t,n=this.editor.$(e).parentsUntil(this.editor.getBody()).add(e);if(n.length===this.lastPath.length){for(t=n.length;t>=0;t--)if(n[t]!==this.lastPath[t])break;if(-1===t)return this.lastPath=n,!0}return this.lastPath=n,!1},e}(),BM=function(e){e.on("click",(function(t){e.dom.getParent(t.target,"details")&&t.preventDefault()}))},PM=function(e){e.parser.addNodeFilter("details",(function(e){Z(e,(function(e){e.attr("data-mce-open",e.attr("open")),e.attr("open","open")}))})),e.serializer.addNodeFilter("details",(function(e){Z(e,(function(e){var t=e.attr("data-mce-open");e.attr("open",v(t)?t:null),e.attr("data-mce-open",null)}))}))},LM=function(e){BM(e),PM(e)},IM=function(e){return Vr(e)&&ta(Cn.fromDom(e))},MM=function(e){var t=e.selection.getRng(),n=Bl.fromRangeStart(t),r=Bl.fromRangeEnd(t);if(Bl.isElementPosition(n)){var o=n.container();IM(o)&&Jg(o).each((function(e){return t.setStart(e.container(),e.offset())}))}if(Bl.isElementPosition(r)){o=n.container();IM(o)&&Qg(o).each((function(e){return t.setEnd(e.container(),e.offset())}))}e.selection.setRng(Px(t))},FM=function(e){e.on("click",(function(t){t.detail>=3&&MM(e)}))},UM=function(e){var t=e.getBoundingClientRect(),n=e.ownerDocument,r=n.documentElement,o=n.defaultView;return{top:t.top+o.pageYOffset-r.clientTop,left:t.left+o.pageXOffset-r.clientLeft}},zM=function(e){return e.inline?UM(e.getBody()):{left:0,top:0}},HM=function(e){var t=e.getBody();return e.inline?{left:t.scrollLeft,top:t.scrollTop}:{left:0,top:0}},jM=function(e){var t=e.getBody(),n=e.getDoc().documentElement,r={left:t.scrollLeft,top:t.scrollTop},o={left:t.scrollLeft||n.scrollLeft,top:t.scrollTop||n.scrollTop};return e.inline?r:o},VM=function(e,t){if(t.target.ownerDocument!==e.getDoc()){var n=UM(e.getContentAreaContainer()),r=jM(e);return{left:t.pageX-n.left+r.left,top:t.pageY-n.top+r.top}}return{left:t.pageX,top:t.pageY}},qM=function(e,t,n){return{pageX:n.left-e.left+t.left,pageY:n.top-e.top+t.top}},$M=function(e,t){return qM(zM(e),HM(e),VM(e,t))},WM=ao,KM=io,XM=function(e,t){return WM(t)&&t!==e},YM=function(e,t,n){return t!==n&&!e.dom.isChildOf(t,n)&&!WM(t)},GM=function(e){var t=e.cloneNode(!0);return t.removeAttribute("data-mce-selected"),t},JM=function(e,t,n,r){var o=e.dom,i=t.cloneNode(!0);o.setStyles(i,{width:n,height:r}),o.setAttrib(i,"data-mce-selected",null);var a=o.create("div",{class:"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return o.setStyles(a,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:r}),o.setStyles(i,{margin:0,boxSizing:"border-box"}),a.appendChild(i),a},QM=function(e,t){e.parentNode!==t&&t.appendChild(e)},ZM=function(e,t,n,r,o,i){var a=0,u=0;e.style.left=t.pageX+"px",e.style.top=t.pageY+"px",t.pageX+n>o&&(a=t.pageX+n-o),t.pageY+r>i&&(u=t.pageY+r-i),e.style.width=n-a+"px",e.style.height=r-u+"px"},eF=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},tF=function(e){return 0===e.button},nF=function(e,t){return{pageX:t.pageX-e.relX,pageY:t.pageY+5}},rF=function(e,t){return function(n){if(tF(n)){var r=ae(t.dom.getParents(n.target),ll(WM,KM)).getOr(null);if(XM(t.getBody(),r)){var o=t.dom.getPos(r),i=t.getBody(),a=t.getDoc().documentElement;e.set({element:r,dragging:!1,screenX:n.screenX,screenY:n.screenY,maxX:(t.inline?i.scrollWidth:a.offsetWidth)-2,maxY:(t.inline?i.scrollHeight:a.offsetHeight)-2,relX:n.pageX-o.x,relY:n.pageY-o.y,width:r.offsetWidth,height:r.offsetHeight,ghost:JM(t,r,r.offsetWidth,r.offsetHeight)})}}}},oF=function(e,t){var n=Ii.throttle((function(e,n){t._selectionOverrides.hideFakeCaret(),t.selection.placeCaretAt(e,n)}),0);return t.on("remove",n.stop),function(r){return e.on((function(e){var o=Math.max(Math.abs(r.screenX-e.screenX),Math.abs(r.screenY-e.screenY));if(!e.dragging&&o>10){var i=t.fire("dragstart",{target:e.element});if(i.isDefaultPrevented())return;e.dragging=!0,t.focus()}if(e.dragging){var a=nF(e,$M(t,r));QM(e.ghost,t.getBody()),ZM(e.ghost,a,e.width,e.height,e.maxX,e.maxY),n(r.clientX,r.clientY)}}))}},iF=function(e){var t=e.getSel().getRangeAt(0),n=t.startContainer;return 3===n.nodeType?n.parentNode:n},aF=function(e,t){return function(n){e.on((function(e){if(e.dragging){if(YM(t,iF(t.selection),e.element)){var r=GM(e.element),o=t.fire("drop",{clientX:n.clientX,clientY:n.clientY});o.isDefaultPrevented()||t.undoManager.transact((function(){eF(e.element),t.insertContent(t.dom.getOuterHTML(r)),t._selectionOverrides.hideFakeCaret()}))}t.fire("dragend")}})),cF(e)}},uF=function(e,t){return function(){e.on((function(e){e.dragging&&t.fire("dragend")})),cF(e)}},cF=function(e){e.on((function(e){eF(e.ghost)})),e.clear()},sF=function(e){var t=mf(),n=Hs.DOM,r=document,o=rF(t,e),i=oF(t,e),a=aF(t,e),u=uF(t,e);e.on("mousedown",o),e.on("mousemove",i),e.on("mouseup",a),n.bind(r,"mousemove",i),n.bind(r,"mouseup",u),e.on("remove",(function(){n.unbind(r,"mousemove",i),n.unbind(r,"mouseup",u)})),e.on("keydown",(function(e){e.keyCode===tv.ESC&&u()}))},fF=function(e){e.on("drop",(function(t){var n="undefined"!==typeof t.clientX?e.getDoc().elementFromPoint(t.clientX,t.clientY):null;(WM(n)||"false"===e.dom.getContentEditableParent(n))&&t.preventDefault()}))},lF=function(e){var t=function(t){if(!t.isDefaultPrevented()){var n=t.dataTransfer;n&&(G(n.types,"Files")||n.files.length>0)&&(t.preventDefault(),"drop"===t.type&&XR(e,"Dropped file type is not supported"))}},n=function(n){Qb(e,n.target)&&t(n)},r=function(){var r=Hs.DOM,o=e.dom,i=document,a=e.inline?e.getBody():e.getDoc(),u=["drop","dragover"];Z(u,(function(e){r.bind(i,e,n),o.bind(a,e,t)})),e.on("remove",(function(){Z(u,(function(e){r.unbind(i,e,n),o.unbind(a,e,t)}))}))};e.on("init",(function(){Ii.setEditorTimeout(e,r,0)}))},dF=function(e){sF(e),fF(e),Nm(e)&&lF(e)},mF=function(e){var t=gf((function(){if(!e.removed&&e.getBody().contains(document.activeElement)){var t=e.selection.getRng();if(t.collapsed){var n=FT(e,t,!1);e.selection.setRng(n)}}}),0);e.on("focus",(function(){t.throttle()})),e.on("blur",(function(){t.cancel()}))},gF=function(e){e.on("init",(function(){e.on("focusin",(function(t){var n=t.target;if(co(n)){var r=Uh(e.getBody(),n),o=ao(r)?r:n;e.selection.getNode()!==o&&IT(e,o).each((function(t){return e.selection.setRng(t)}))}}))}))},pF=io,hF=ao,vF=function(e,t){return Uh(e.getBody(),t)},bF=function(e){var t,n=e.selection,r=e.dom,o=r.isBlock,i=e.getBody(),a=Ym(e,i,o,(function(){return dy(e)})),u="sel-"+r.uniqueId(),c="data-mce-selected",s=function(e){return r.hasClass(e,"mce-offscreen-selection")},f=function(e){return e!==i&&(hF(e)||co(e))&&r.isChildOf(e,i)},l=function(e){return CC(e)||wC(e)||hC(e)||vC(e)},d=function(){var e=r.get(u);return e?e.getElementsByTagName("*")[0]:e},m=function(e){e&&n.setRng(e)},g=n.getRng,p=function(t,r,o,i){void 0===i&&(i=!0);var u=e.fire("ShowCaret",{target:r,direction:t,before:o});return u.isDefaultPrevented()?null:(i&&n.scrollIntoView(r,-1===t),a.show(o,r))},h=function(e){e.hasAttribute("data-mce-caret")&&(Ra(e),m(g()),n.scrollIntoView(e))},v=function(){e.on("mouseup",(function(t){var n=g();n.collapsed&&FR(e,t.clientX,t.clientY)&&MT(e,n,!1).each(m)})),e.on("click",(function(t){var o=vF(e,t.target);o&&(hF(o)&&(t.preventDefault(),e.focus()),pF(o)&&r.isChildOf(o,n.getNode())&&N())})),e.on("blur NewBlock",N),e.on("ResizeWindow FullscreenStateChanged",a.reposition);var u=function(e){var t=e.firstChild;if(S(t))return!1;var n=Bl.before(t);if(ro(n.getNode())&&1===e.childNodes.length)return!l(n);var r=Mg(e),o=r.next(n);return o&&!l(o)},c=function(e,t){var n=r.getParent(e,o),i=r.getParent(t,o);return n===i},h=function(t,n){var i=r.getParent(t,o),a=r.getParent(n,o);return!S(i)&&(!(t===a||!r.isChildOf(i,a)||!1!==hF(vF(e,i)))||!r.isChildOf(a,i)&&!c(i,a)&&u(i))};e.on("tap",(function(t){var n=t.target,r=vF(e,n);hF(r)?(t.preventDefault(),IT(e,r).each(k)):f(n)&&IT(e,n).each(k)}),!0),e.on("mousedown",(function(t){var o=t.target;if((o===i||"HTML"===o.nodeName||r.isChildOf(o,i))&&!1!==FR(e,t.clientX,t.clientY)){var a=vF(e,o);if(a)hF(a)?(t.preventDefault(),IT(e,a).each(k)):(N(),pF(a)&&t.shiftKey||zh(t.clientX,t.clientY,n.getRng())||(_(),n.placeCaretAt(t.clientX,t.clientY)));else if(f(o))IT(e,o).each(k);else if(!1===Qm(o)){N(),_();var u=IB(i,t.clientX,t.clientY);if(u&&!h(o,u.node)){t.preventDefault();var c=p(1,u.node,u.before,!1);m(c),e.getBody().focus()}}}})),e.on("keypress",(function(e){tv.modifierPressed(e)||hF(n.getNode())&&e.preventDefault()})),e.on("GetSelectionRange",(function(e){var n=e.range;if(t){if(!t.parentNode)return void(t=null);n=n.cloneRange(),n.selectNode(t),e.range=n}})),e.on("SetSelectionRange",(function(e){e.range=C(e.range);var t=k(e.range,e.forward);t&&(e.range=t)}));var v=function(e){return"mcepastebin"===e.id};e.on("AfterSetSelectionRange",(function(e){var t=e.range,n=t.startContainer.parentNode;y(t)||v(n)||_(),s(n)||N()})),e.on("copy",(function(e){var t=e.clipboardData;if(!e.isDefaultPrevented()&&e.clipboardData&&!en.ie){var n=d();n&&(e.preventDefault(),t.clearData(),t.setData("text/html",n.outerHTML),t.setData("text/plain",n.outerText||n.innerText))}})),dF(e),mF(e),gF(e)},b=function(e){return ya(e)||Ea(e)||_a(e)},y=function(e){return b(e.startContainer)||b(e.endContainer)},C=function(t){var n=e.schema.getShortEndedElements(),o=r.createRng(),i=t.startContainer,a=t.startOffset,u=t.endContainer,c=t.endOffset;return Oe(n,i.nodeName.toLowerCase())?0===a?o.setStartBefore(i):o.setStartAfter(i):o.setStart(i,a),Oe(n,u.nodeName.toLowerCase())?0===c?o.setEndBefore(u):o.setEndAfter(u):o.setEnd(u,c),o},w=function(t,o,i){var a=e.$,c=_i(Cn.fromDom(e.getBody()),"#"+u).fold((function(){return a([])}),(function(e){return a([e.dom])}));0===c.length&&(c=a('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>').attr("id",u),c.appendTo(e.getBody()));var s=r.createRng();o===i&&en.ie?(c.empty().append('<p style="font-size: 0" data-mce-bogus="all"> </p>').append(o),s.setStartAfter(c[0].firstChild.firstChild),s.setEndAfter(o)):(c.empty().append(sa).append(o).append(sa),s.setStart(c[0].firstChild,1),s.setEnd(c[0].lastChild,0)),c.css({top:r.getPos(t,e.getBody()).y}),c[0].focus();var f=n.getSel();return f.removeAllRanges(),f.addRange(s),s},x=function(n){var o=n.cloneNode(!0),i=e.fire("ObjectSelected",{target:n,targetClone:o});if(i.isDefaultPrevented())return null;var a=w(n,i.targetClone,o),u=Cn.fromDom(n);return Z(Af(Cn.fromDom(e.getBody()),"*[data-mce-selected]"),(function(e){Bn(u,e)||xo(e,c)})),r.getAttrib(n,c)||n.setAttribute(c,"1"),t=n,_(),a},k=function(e,t){if(!e)return null;if(e.collapsed){if(!y(e)){var n=t?1:-1,o=Cg(n,i,e),a=o.getNode(!t);if(Qm(a))return p(n,a,!!t&&!o.isAtEnd(),!1);var u=o.getNode(t);if(Qm(u))return p(n,u,!t&&!o.isAtEnd(),!1)}return null}var c=e.startContainer,s=e.startOffset,l=e.endOffset;if(3===c.nodeType&&0===s&&hF(c.parentNode)&&(c=c.parentNode,s=r.nodeIndex(c),c=c.parentNode),1!==c.nodeType)return null;if(l===s+1&&c===e.endContainer){var d=c.childNodes[s];if(f(d))return x(d)}return null},N=function(){t&&t.removeAttribute(c),_i(Cn.fromDom(e.getBody()),"#"+u).each(Er),t=null},E=function(){a.destroy(),t=null},_=function(){a.hide()};return en.ceFalse&&!OE(e)&&v(),{showCaret:p,showBlockCaretContainer:h,hideFakeCaret:_,destroy:E}},yF=function(e){var t=gn.each,n=tv.BACKSPACE,r=tv.DELETE,o=e.dom,i=e.selection,a=e.parser,u=en.gecko,c=en.ie,s=en.webkit,f="data:text/mce-internal,",l=c?"Text":"URL",d=function(t,n){try{e.getDoc().execCommand(t,!1,n)}catch(r){}},m=function(e){return e.isDefaultPrevented()},g=function(t){var n,r;t.dataTransfer&&(e.selection.isCollapsed()&&"IMG"===t.target.tagName&&i.select(t.target),n=e.selection.getContent(),n.length>0&&(r=f+escape(e.id)+","+escape(n),t.dataTransfer.setData(l,r)))},p=function(e){var t;return e.dataTransfer&&(t=e.dataTransfer.getData(l),t&&t.indexOf(f)>=0)?(t=t.substr(f.length).split(","),{id:unescape(t[0]),html:unescape(t[1])}):null},h=function(t,n){e.queryCommandSupported("mceInsertClipboardContent")?e.execCommand("mceInsertClipboardContent",!1,{content:t,internal:n}):e.execCommand("mceInsertContent",!1,t)},v=function(){var t=function(e){var t=o.create("body"),n=e.cloneContents();return t.appendChild(n),i.serializer.serialize(t,{format:"html"})},a=function(n){var r=t(n),i=o.createRng();i.selectNode(e.getBody());var a=t(i);return r===a};e.on("keydown",(function(t){var i,u,c=t.keyCode;if(!m(t)&&(c===r||c===n)){if(i=e.selection.isCollapsed(),u=e.getBody(),i&&!o.isEmpty(u))return;if(!i&&!a(e.selection.getRng()))return;t.preventDefault(),e.setContent(""),u.firstChild&&o.isBlock(u.firstChild)?e.selection.setCursorLocation(u.firstChild,0):e.selection.setCursorLocation(u,0),e.nodeChanged()}}))},b=function(){e.shortcuts.add("meta+a",null,"SelectAll")},y=function(){e.inline||o.bind(e.getDoc(),"mousedown mouseup",(function(t){var n;if(t.target===e.getDoc().documentElement)if(n=i.getRng(),e.getBody().focus(),"mousedown"===t.type){if(ya(n.startContainer))return;i.placeCaretAt(t.clientX,t.clientY)}else i.setRng(n)}))},C=function(){e.on("keydown",(function(t){if(!m(t)&&t.keyCode===n){if(!e.getBody().getElementsByTagName("hr").length)return;if(i.isCollapsed()&&0===i.getRng().startOffset){var r=i.getNode(),a=r.previousSibling;if("HR"===r.nodeName)return o.remove(r),void t.preventDefault();a&&a.nodeName&&"hr"===a.nodeName.toLowerCase()&&(o.remove(a),t.preventDefault())}}}))},w=function(){Range.prototype.getClientRects||e.on("mousedown",(function(t){if(!m(t)&&"HTML"===t.target.nodeName){var n=e.getBody();n.blur(),Ii.setEditorTimeout(e,(function(){n.focus()}))}}))},x=function(){e.on("click",(function(t){var n=t.target;/^(IMG|HR)$/.test(n.nodeName)&&"false"!==o.getContentEditableParent(n)&&(t.preventDefault(),e.selection.select(n),e.nodeChanged()),"A"===n.nodeName&&o.hasClass(n,"mce-item-anchor")&&(t.preventDefault(),i.select(n))}))},S=function(){var n=function(){var n=o.getAttribs(i.getStart().cloneNode(!1));return function(){var r=i.getStart();r!==e.getBody()&&(o.setAttrib(r,"style",null),t(n,(function(e){r.setAttributeNode(e.cloneNode(!0))})))}},r=function(){return!i.isCollapsed()&&o.getParent(i.getStart(),o.isBlock)!==o.getParent(i.getEnd(),o.isBlock)};e.on("keypress",(function(t){var o;if(!m(t)&&(8===t.keyCode||46===t.keyCode)&&r())return o=n(),e.getDoc().execCommand("delete",!1,null),o(),t.preventDefault(),!1})),o.bind(e.getDoc(),"cut",(function(t){var o;!m(t)&&r()&&(o=n(),Ii.setEditorTimeout(e,(function(){o()})))}))},k=function(){e.on("keydown",(function(e){if(!m(e)&&e.keyCode===n&&i.isCollapsed()&&0===i.getRng().startOffset){var t=i.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}}))},N=function(){e.on("keydown",(function(t){var n,r;if(!m(t)&&t.keyCode===tv.BACKSPACE){n=i.getRng();var a=n.startContainer,u=n.startOffset,c=o.getRoot();if(r=a,n.collapsed&&0===u){while(r&&r.parentNode&&r.parentNode.firstChild===r&&r.parentNode!==c)r=r.parentNode;"BLOCKQUOTE"===r.tagName&&(e.formatter.toggle("blockquote",null,r),n=o.createRng(),n.setStart(a,0),n.setEnd(a,0),i.setRng(n))}}}))},E=function(){var t=function(){d("StyleWithCSS",!1),d("enableInlineTableEditing",!1),Zd(e)||d("enableObjectResizing",!1)};wm(e)||e.on("BeforeExecCommand mousedown",t)},A=function(){var n=function(){t(o.select("a"),(function(e){var t=e.parentNode,n=o.getRoot();if(t.lastChild===e){while(t&&!o.isBlock(t)){if(t.parentNode.lastChild!==t||t===n)return;t=t.parentNode}o.add(t,"br",{"data-mce-bogus":1})}}))};e.on("SetContent ExecCommand",(function(e){"setcontent"!==e.type&&"mceInsertLink"!==e.command||n()}))},R=function(){Nd(e)&&e.on("init",(function(){d("DefaultParagraphSeparator",Nd(e))}))},D=function(){e.on("keyup focusin mouseup",(function(e){tv.modifierPressed(e)||i.normalize()}),!0)},T=function(){e.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}")},O=function(){e.inline||e.on("keydown",(function(){document.activeElement===document.body&&e.getWin().focus()}))},B=function(){e.inline||(e.contentStyles.push("body {min-height: 150px}"),e.on("click",(function(t){var n;if("HTML"===t.target.nodeName){if(en.ie>11)return void e.getBody().focus();n=e.selection.getRng(),e.getBody().focus(),e.selection.setRng(n),e.selection.normalize(),e.nodeChanged()}})))},P=function(){en.mac&&e.on("keydown",(function(t){if(tv.metaKeyPressed(t)&&!t.shiftKey&&(37===t.keyCode||39===t.keyCode)){t.preventDefault();var n=e.selection.getSel();n.modify("move",37===t.keyCode?"backward":"forward","lineboundary")}}))},L=function(){d("AutoUrlDetect",!1)},I=function(){e.on("click",(function(e){var t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)})),e.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")},M=function(){e.on("init",(function(){e.dom.bind(e.getBody(),"submit",(function(e){e.preventDefault()}))}))},F=function(){a.addNodeFilter("br",(function(e){var t=e.length;while(t--)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()}))},U=function(){e.on("dragstart",(function(e){g(e)})),e.on("drop",(function(t){if(!m(t)){var n=p(t);if(n&&n.id!==e.id){t.preventDefault();var r=cv(t.x,t.y,e.getDoc());i.setRng(r),h(n.html,!0)}}}))},z=_,H=function(){if(!u||e.removed)return!1;var t=e.selection.getSel();return!t||!t.rangeCount||0===t.rangeCount},j=function(){s&&(y(),x(),M(),b(),en.iOS&&(O(),B(),I())),u&&(w(),E(),T(),P())},V=function(){N(),v(),en.windowsPhone||D(),s&&(y(),x(),R(),M(),k(),F(),en.iOS?(O(),B(),I()):b()),en.ie>=11&&(B(),k()),en.ie&&(b(),L(),U()),u&&(C(),w(),S(),E(),A(),T(),P(),k())};return OE(e)?j():V(),{refreshContentEditable:z,isHidden:H}},CF=Hs.DOM,wF=function(e,t){var n=Cn.fromDom(e.getBody()),r=mr(dr(n)),o=Cn.fromTag("style");vo(o,"type","text/css"),wr(o,Cn.fromText(t)),wr(r,o),e.on("remove",(function(){Er(o)}))},xF=function(e){return e.inline?e.getElement().nodeName.toLowerCase():void 0},SF=function(e){return Ae(e,(function(e){return!1===x(e)}))},kF=function(e){var t=e.settings;return SF({block_elements:t.block_elements,boolean_attributes:t.boolean_attributes,custom_elements:t.custom_elements,extended_valid_elements:t.extended_valid_elements,invalid_elements:t.invalid_elements,invalid_styles:t.invalid_styles,move_caret_before_on_enter_elements:t.move_caret_before_on_enter_elements,non_empty_elements:t.non_empty_elements,schema:t.schema,self_closing_elements:t.self_closing_elements,short_ended_elements:t.short_ended_elements,special:t.special,text_block_elements:t.text_block_elements,text_inline_elements:t.text_inline_elements,valid_children:t.valid_children,valid_classes:t.valid_classes,valid_elements:t.valid_elements,valid_styles:t.valid_styles,verify_html:t.verify_html,whitespace_elements:t.whitespace_elements,padd_empty_block_inline_children:t.format_empty_lines})},NF=function(e){var t=e.settings,n=e.editorUpload.blobCache;return SF({allow_conditional_comments:t.allow_conditional_comments,allow_html_data_urls:t.allow_html_data_urls,allow_svg_data_urls:t.allow_svg_data_urls,allow_html_in_named_anchor:t.allow_html_in_named_anchor,allow_script_urls:t.allow_script_urls,allow_unsafe_link_target:t.allow_unsafe_link_target,convert_fonts_to_spans:t.convert_fonts_to_spans,fix_list_elements:t.fix_list_elements,font_size_legacy_values:t.font_size_legacy_values,forced_root_block:t.forced_root_block,forced_root_block_attrs:t.forced_root_block_attrs,padd_empty_with_br:t.padd_empty_with_br,preserve_cdata:t.preserve_cdata,remove_trailing_brs:t.remove_trailing_brs,inline_styles:t.inline_styles,root_name:xF(e),validate:!0,blob_cache:n,document:e.getDoc(),images_dataimg_filter:t.images_dataimg_filter})},EF=function(e){var t=e.settings;return qe(qe(qe({},NF(e)),kF(e)),SF({url_converter:t.url_converter,url_converter_scope:t.url_converter_scope,element_format:t.element_format,entities:t.entities,entity_encoding:t.entity_encoding,indent:t.indent,indent_after:t.indent_after,indent_before:t.indent_before}))},_F=function(e){var t=CA(NF(e),e.schema);return t.addAttributeFilter("src,href,style,tabindex",(function(t,n){var r,o,i=t.length,a=e.dom,u="data-mce-"+n;while(i--)if(r=t[i],o=r.attr(n),o&&!r.attr(u)){if(0===o.indexOf("data:")||0===o.indexOf("blob:"))continue;"style"===n?(o=a.serializeStyle(a.parseStyle(o),r.name),o.length||(o=null),r.attr(u,o),r.attr(n,o)):"tabindex"===n?(r.attr(u,o),r.attr(n,null)):r.attr(u,e.convertURL(o,n,r.name))}})),t.addNodeFilter("script",(function(e){var t=e.length;while(t--){var n=e[t],r=n.attr("type")||"no/type";0!==r.indexOf("mce-")&&n.attr("type","mce-"+r)}})),e.settings.preserve_cdata&&t.addNodeFilter("#cdata",(function(t){var n=t.length;while(n--){var r=t[n];r.type=8,r.name="#comment",r.value="[CDATA["+e.dom.encode(r.value)+"]]"}})),t.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",(function(t){var n=t.length,r=e.schema.getNonEmptyElements();while(n--){var o=t[n];o.isEmpty(r)&&0===o.getAll("br").length&&(o.append(new Ry("br",1)).shortEnded=!0)}})),t},AF=function(e){e.settings.auto_focus&&Ii.setEditorTimeout(e,(function(){var t;t=!0===e.settings.auto_focus?e:e.editorManager.get(e.settings.auto_focus),t.destroyed||t.focus()}),100)},RF=function(e){var t=e.dom.getRoot();e.inline||Nh(e)&&e.selection.getStart(!0)!==t||Jg(t).each((function(t){var n=t.getNode(),r=Gr(n)?Jg(n).getOr(t):t;en.browser.isIE()?Fb(e,r.toRange()):e.selection.setRng(r.toRange())}))},DF=function(e){e.bindPendingEventDelegates(),e.initialized=!0,Gh(e),e.focus(!0),RF(e),e.nodeChanged({initial:!0}),e.execCallback("init_instance_callback",e),AF(e)},TF=function(e){return e.inline?e.ui.styleSheetLoader:e.dom.styleSheetLoader},OF=function(e,t,n){var r=[new Ri((function(n,r){return TF(e).loadAll(t,n,r)}))];return e.inline?r:r.concat([new Ri((function(t,r){return e.ui.styleSheetLoader.loadAll(n,t,r)}))])},BF=function(e){var t=TF(e),n=Gd(e),r=e.contentCSS,o=function(){t.unloadAll(r),e.inline||e.ui.styleSheetLoader.unloadAll(n)},i=function(){e.removed?o():e.on("remove",o)};if(e.contentStyles.length>0){var a="";gn.each(e.contentStyles,(function(e){a+=e+"\r\n"})),e.dom.addStyle(a)}var u=Ri.all(OF(e,r,n)).then(i).catch(i);return e.settings.content_style&&wF(e,e.settings.content_style),u},PF=function(e){var t=e.settings,n=e.getDoc(),r=e.getBody();Xh(e),t.browser_spellcheck||t.gecko_spellcheck||(n.body.spellcheck=!1,CF.setAttrib(r,"spellcheck","false")),e.quirks=yF(e),Yh(e);var o=Jd(e);void 0!==o&&(r.dir=o),t.protect&&e.on("BeforeSetContent",(function(e){gn.each(t.protect,(function(t){e.content=e.content.replace(t,(function(e){return"\x3c!--mce:protected "+escape(e)+"--\x3e"}))}))})),e.on("SetContent",(function(){e.addVisual(e.getBody())})),e.on("compositionstart compositionend",(function(t){e.composing="compositionstart"===t.type}))},LF=function(e){OE(e)||e.load({initial:!0,format:"html"}),e.startContent=e.getContent({format:"raw"})},IF=function(e){!0!==e.removed&&(LF(e),DF(e))},MF=function(e,t){var n=e.settings,r=e.getElement(),o=e.getDoc();n.inline||(e.getElement().style.visibility=e.orgVisibility),t||e.inline||(o.open(),o.write(e.iframeHTML),o.close()),e.inline&&(CF.addClass(r,"mce-content-body"),e.contentDocument=o=document,e.contentWindow=window,e.bodyElement=r,e.contentAreaContainer=r);var i=e.getBody();i.disabled=!0,e.readonly=!!n.readonly,e.readonly||(e.inline&&"static"===CF.getStyle(i,"position",!0)&&(i.style.position="relative"),i.contentEditable=e.getParam("content_editable_state",!0)),i.disabled=!1,e.editorUpload=bD(e),e.schema=Hu(kF(e)),e.dom=Hs(o,{keep_values:!0,url_converter:e.convertURL,url_converter_scope:e,hex_colors:n.force_hex_style_colors,update_styles:!0,root_element:e.inline?e.getBody():null,collect:function(){return e.inline},schema:e.schema,contentCssCors:Vd(e),referrerPolicy:qd(e),onSetAttrib:function(t){e.fire("SetAttrib",t)}}),e.parser=_F(e),e.serializer=BA(EF(e),e),e.selection=S_(e.dom,e.getWin(),e.serializer,e),e.annotator=Mh(e),e.formatter=AD(e),e.undoManager=OD(e),e._nodeChangeDispatcher=new OM(e),e._selectionOverrides=bF(e),MP(e),LM(e),OE(e)||FM(e);var a=TM(e);OP(e,a),VP(e),zD(e);var u=PE(e);PF(e),u.fold((function(){BF(e).then((function(){return IF(e)}))}),(function(t){e.setProgressState(!0),BF(e).then((function(){t().then((function(t){e.setProgressState(!1),IF(e)}),(function(t){e.notificationManager.open({type:"error",text:String(t)}),IF(e)}))}))}))},FF=Hs.DOM,UF=function(e,t){if(document.domain!==window.location.hostname&&en.browser.isIE()){var n=lD("mce");e[n]=function(){MF(e)};var r='javascript:(function(){document.open();document.domain="'+document.domain+'";var ed = window.parent.tinymce.get("'+e.id+'");document.write(ed.iframeHTML);document.close();ed.'+n+"(true);})()";return FF.setAttrib(t,"src",r),!0}return!1},zF=function(e,t,n,r){var o=Cn.fromTag("iframe");return bo(o,r),bo(o,{id:e+"_ifr",frameBorder:"0",allowTransparency:"true",title:t}),Sf(o,"tox-edit-area__iframe"),o},HF=function(e){var t=yd(e)+"<html><head>";Cd(e)!==e.documentBaseUrl&&(t+='<base href="'+e.documentBaseURI.getURI()+'" />'),t+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />';var n=wd(e),r=xd(e),o=e.translate(Rm(e));return Sd(e)&&(t+='<meta http-equiv="Content-Security-Policy" content="'+Sd(e)+'" />'),t+='</head><body id="'+n+'" class="mce-content-body '+r+'" data-id="'+e.id+'" aria-label="'+o+'"><br></body></html>',t},jF=function(e,t){var n=e.translate("Rich Text Area"),r=zF(e.id,n,t.height,bd(e)).dom;r.onload=function(){r.onload=null,e.fire("load")};var o=UF(e,r);return e.contentAreaContainer=t.iframeContainer,e.iframeElement=r,e.iframeHTML=HF(e),FF.add(t.iframeContainer,r),o},VF=function(e,t){var n=jF(e,t);t.editorContainer&&(FF.get(t.editorContainer).style.display=e.orgDisplay,e.hidden=FF.isHidden(t.editorContainer)),e.getElement().style.display="none",FF.setAttrib(e.id,"aria-hidden","true"),n||MF(e)},qF=Hs.DOM,$F=function(e,t,n){var r=VR.get(n),o=VR.urls[n]||e.documentBaseUrl.replace(/\/$/,"");if(n=gn.trim(n),r&&-1===gn.inArray(t,n)){if(gn.each(VR.dependencies(n),(function(n){$F(e,t,n)})),e.plugins[n])return;try{var i=new r(e,o,e.$);e.plugins[n]=i,i.init&&(i.init(e,o),t.push(n))}catch(DH){tD(e,n,DH)}}},WF=function(e){return e.replace(/^\-/,"")},KF=function(e){var t=[];gn.each(Sm(e).split(/[ ,]/),(function(n){$F(e,t,WF(n))}))},XF=function(e){var t=gn.trim(Md(e)),n=e.ui.registry.getAll().icons,r=qe(qe({},AR.get("default").icons),AR.get(t).icons);xe(r,(function(t,r){Oe(n,r)||e.ui.registry.addIcon(r,t)}))},YF=function(e){var t=om(e);if(v(t)){e.settings.theme=WF(t);var n=qR.get(t);e.theme=new n(e,qR.urls[t]),e.theme.init&&e.theme.init(e,qR.urls[t]||e.documentBaseUrl.replace(/\/$/,""),e.$)}else e.theme={}},GF=function(e){return e.theme.renderUI()},JF=function(e){var t=e.getElement(),n=om(e),r=n(e,t);return r.editorContainer.nodeType&&(r.editorContainer.id=r.editorContainer.id||e.id+"_parent"),r.iframeContainer&&r.iframeContainer.nodeType&&(r.iframeContainer.id=r.iframeContainer.id||e.id+"_iframecontainer"),r.height=r.iframeHeight?r.iframeHeight:t.offsetHeight,r},QF=function(e){return{editorContainer:e,iframeContainer:e,api:{}}},ZF=function(e){var t=qF.create("div");return qF.insertAfter(t,e),QF(t)},eU=function(e){var t=e.getElement();return e.inline?QF(null):ZF(t)},tU=function(e){var t=e.getElement();return e.orgDisplay=t.style.display,v(om(e))?GF(e):N(om(e))?JF(e):eU(e)},nU=function(e,t){var n={show:q.from(t.show).getOr(_),hide:q.from(t.hide).getOr(_),disable:q.from(t.disable).getOr(_),isDisabled:q.from(t.isDisabled).getOr(F),enable:function(){e.mode.isReadOnly()||q.from(t.enable).map(M)}};e.ui=qe(qe({},e.ui),n)},rU=function(e){e.fire("ScriptsLoaded"),XF(e),YF(e),KF(e);var t=tU(e);nU(e,q.from(t.api).getOr({}));var n={editorContainer:t.editorContainer,iframeContainer:t.iframeContainer};return e.editorContainer=n.editorContainer?n.editorContainer:null,uD(e),e.inline?MF(e):VF(e,n)},oU=Hs.DOM,iU=function(e){return"-"===e.charAt(0)},aU=function(e,t){var n=$d(t),r=Wd(t);if(!1===ff.hasCode(n)&&"en"!==n){var o=""!==r?r:t.editorManager.baseURL+"/langs/"+n+".js";e.add(o,_,void 0,(function(){eD(t,o,n)}))}},uU=function(e,t,n,r){var o=om(t);if(v(o)){if(!iU(o)&&!Oe(qR.urls,o)){var i=lm(t);i?qR.load(o,t.documentBaseURI.toAbsolute(i)):qR.load(o,"themes/"+o+"/theme"+n+".js")}e.loadQueue((function(){qR.waitFor(o,r)}))}else r()},cU=function(e){return q.from(Fd(e)).filter((function(e){return e.length>0})).map((function(e){return{url:e,name:q.none()}}))},sU=function(e,t,n){return q.from(t).filter((function(e){return e.length>0&&!AR.has(e)})).map((function(t){return{url:e.editorManager.baseURL+"/icons/"+t+"/icons"+n+".js",name:q.some(t)}}))},fU=function(e,t,n){var r=sU(t,"default",n),o=cU(t).orThunk((function(){return sU(t,Md(t),"")}));Z(fo([r,o]),(function(n){e.add(n.url,_,void 0,(function(){ZR(t,n.url,n.name.getOrUndefined())}))}))},lU=function(e,t){gn.each(km(e),(function(t,n){VR.load(n,t,_,void 0,(function(){QR(e,t,n)})),e.settings.plugins+=" "+n})),gn.each(Sm(e).split(/[ ,]/),(function(n){if(n=gn.trim(n),n&&!VR.urls[n])if(iU(n)){n=n.substr(1,n.length);var r=VR.dependencies(n);gn.each(r,(function(n){var r={prefix:"plugins/",resource:n,suffix:"/plugin"+t+".js"},o=VR.createUrl(r,n);VR.load(o.resource,o,_,void 0,(function(){QR(e,o.prefix+o.resource+o.suffix,o.resource)}))}))}else{var o={prefix:"plugins/",resource:n,suffix:"/plugin"+t+".js"};VR.load(n,o,_,void 0,(function(){QR(e,o.prefix+o.resource+o.suffix,n)}))}}))},dU=function(e,t){var n=Ys.ScriptLoader;uU(n,e,t,(function(){aU(n,e),fU(n,e,t),lU(e,t),n.loadQueue((function(){e.removed||rU(e)}),e,(function(){e.removed||rU(e)}))}))},mU=function(e,t){return Ui.forElement(e,{contentCssCors:xm(t),referrerPolicy:qd(t)})},gU=function(e){var t=e.id;ff.setCode($d(e));var n=function(){oU.unbind(window,"ready",n),e.render()};if(rc.Event.domLoaded){if(e.getElement()&&en.contentEditable){var r=Cn.fromDom(e.getElement()),o=So(r);e.on("remove",(function(){ee(r.dom.attributes,(function(e){return xo(r,e.name)})),bo(r,o)})),e.ui.styleSheetLoader=mU(r,e),dm(e)?e.inline=!0:(e.orgVisibility=e.getElement().style.visibility,e.getElement().style.visibility="hidden");var i=e.getElement().form||oU.getParent(t,"form");i&&(e.formElement=i,mm(e)&&!Qr(e.getElement())&&(oU.insertAfter(oU.create("input",{type:"hidden",name:t}),t),e.hasHiddenInput=!0),e.formEventDelegate=function(t){e.fire(t.type,t)},oU.bind(i,"submit reset",e.formEventDelegate),e.on("reset",(function(){e.resetContent()})),!gm(e)||i.submit.nodeType||i.submit.length||i._mceOldSubmit||(i._mceOldSubmit=i.submit,i.submit=function(){return e.editorManager.triggerSave(),e.setDirty(!1),i._mceOldSubmit(i)})),e.windowManager=WR(e),e.notificationManager=jR(e),pm(e)&&e.on("GetContent",(function(e){e.save&&(e.content=oU.encode(e.content))})),hm(e)&&e.on("submit",(function(){e.initialized&&e.save()})),vm(e)&&(e._beforeUnload=function(){!e.initialized||e.destroyed||e.isHidden()||e.save({format:"raw",no_events:!0,set_dirty:!1})},e.editorManager.on("BeforeUnload",e._beforeUnload)),e.editorManager.add(e),dU(e,e.suffix)}}else oU.bind(window,"ready",n)},pU=function(e,t){return u_(e,t)},hU={"font-size":"size","font-family":"face"},vU=function(e,t,n){var r=function(t){return Ao(t,e).orThunk((function(){return"font"===Mn(t)?Te(hU,e).bind((function(e){return Co(t,e)})):q.none()}))},o=function(e){return Bn(Cn.fromDom(t),e)};return RS(Cn.fromDom(n),(function(e){return r(e)}),o)},bU=function(e){return e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")},yU=function(e,t){return q.from(Hs.DOM.getStyle(t,e,!0))},CU=function(e){return function(t,n){return q.from(n).map(Cn.fromDom).filter(Hn).bind((function(n){return vU(e,t,n.dom).or(yU(e,n.dom))})).getOr("")}},wU=CU("font-size"),xU=A(bU,CU("font-family")),SU=function(e){return Jg(e.getBody()).map((function(e){var t=e.container();return Zr(t)?t.parentNode:t}))},kU=function(e){return q.from(e.selection.getRng()).bind((function(t){var n=e.getBody(),r=t.startContainer===n&&0===t.startOffset;return r?q.none():q.from(e.selection.getStart(!0))}))},NU=function(e,t){return kU(e).orThunk(B(SU,e)).map(Cn.fromDom).filter(Hn).bind(t)},EU=function(e,t){return NU(e,R(q.some,t))},_U=function(e,t){if(/^[0-9.]+$/.test(t)){var n=parseInt(t,10);if(n>=1&&n<=7){var r=Td(e),o=Od(e);return o?o[n-1]||t:r[n-1]||t}return t}return t},AU=function(e){var t=e.split(/\s*,\s*/);return Q(t,(function(e){return-1===e.indexOf(" ")||st(e,'"')||st(e,"'")?e:"'"+e+"'"})).join(",")},RU=function(e,t){var n=_U(e,t);e.formatter.toggle("fontname",{value:AU(n)}),e.nodeChanged()},DU=function(e){return EU(e,(function(t){return xU(e.getBody(),t.dom)})).getOr("")},TU=function(e,t){e.formatter.toggle("fontsize",{value:_U(e,t)}),e.nodeChanged()},OU=function(e){return EU(e,(function(t){return wU(e.getBody(),t.dom)})).getOr("")},BU=function(e){return EU(e,(function(t){var n=Cn.fromDom(e.getBody()),r=RS(t,(function(e){return Ao(e,"line-height")}),B(Bn,n)),o=function(){var e=parseFloat(Eo(t,"line-height")),n=parseFloat(Eo(t,"font-size"));return String(e/n)};return r.getOrThunk(o)})).getOr("")},PU=function(e,t){e.formatter.toggle("lineheight",{value:String(t)}),e.nodeChanged()},LU=function(e){if("string"!==typeof e){var t=gn.extend({paste:e.paste,data:{paste:e.paste}},e);return{content:e.content,details:t}}return{content:e,details:{}}},IU=function(e,t){var n=LU(t);i_(e,n.content,n.details)},MU=gn.each,FU=gn.map,UU=gn.inArray,zU=function(){function e(e){this.commands={state:{},exec:{},value:{}},this.editor=e,this.setupCommands(e)}return e.prototype.execCommand=function(e,t,n,r){var o,i=!1,a=this;if(!a.editor.removed){if("mcefocus"!==e.toLowerCase()&&(/^(mceAddUndoLevel|mceEndUndoLevel|mceBeginUndoLevel|mceRepaint)$/.test(e)||r&&r.skip_focus?zb(a.editor):a.editor.focus()),r=a.editor.fire("BeforeExecCommand",{command:e,ui:t,value:n}),r.isDefaultPrevented())return!1;var u=e.toLowerCase();if(o=a.commands.exec[u])return o(u,t,n),a.editor.fire("ExecCommand",{command:e,ui:t,value:n}),!0;if(MU(this.editor.plugins,(function(r){if(r.execCommand&&r.execCommand(e,t,n))return a.editor.fire("ExecCommand",{command:e,ui:t,value:n}),i=!0,!1})),i)return i;if(a.editor.theme&&a.editor.theme.execCommand&&a.editor.theme.execCommand(e,t,n))return a.editor.fire("ExecCommand",{command:e,ui:t,value:n}),!0;try{i=a.editor.getDoc().execCommand(e,t,n)}catch(c){}return!!i&&(a.editor.fire("ExecCommand",{command:e,ui:t,value:n}),!0)}},e.prototype.queryCommandState=function(e){var t;if(!this.editor.quirks.isHidden()&&!this.editor.removed){if(e=e.toLowerCase(),t=this.commands.state[e])return t(e);try{return this.editor.getDoc().queryCommandState(e)}catch(n){}return!1}},e.prototype.queryCommandValue=function(e){var t;if(!this.editor.quirks.isHidden()&&!this.editor.removed){if(e=e.toLowerCase(),t=this.commands.value[e])return t(e);try{return this.editor.getDoc().queryCommandValue(e)}catch(n){}}},e.prototype.addCommands=function(e,t){void 0===t&&(t="exec");var n=this;MU(e,(function(e,r){MU(r.toLowerCase().split(","),(function(r){n.commands[t][r]=e}))}))},e.prototype.addCommand=function(e,t,n){var r=this;e=e.toLowerCase(),this.commands.exec[e]=function(e,o,i,a){return t.call(n||r.editor,o,i,a)}},e.prototype.queryCommandSupported=function(e){if(e=e.toLowerCase(),this.commands.exec[e])return!0;try{return this.editor.getDoc().queryCommandSupported(e)}catch(t){}return!1},e.prototype.addQueryStateHandler=function(e,t,n){var r=this;e=e.toLowerCase(),this.commands.state[e]=function(){return t.call(n||r.editor)}},e.prototype.addQueryValueHandler=function(e,t,n){var r=this;e=e.toLowerCase(),this.commands.value[e]=function(){return t.call(n||r.editor)}},e.prototype.hasCustomCommand=function(e){return e=e.toLowerCase(),!!this.commands.exec[e]},e.prototype.execNativeCommand=function(e,t,n){return void 0===t&&(t=!1),void 0===n&&(n=null),this.editor.getDoc().execCommand(e,t,n)},e.prototype.isFormatMatch=function(e){return this.editor.formatter.match(e)},e.prototype.toggleFormat=function(e,t){this.editor.formatter.toggle(e,t),this.editor.nodeChanged()},e.prototype.storeSelection=function(e){this.selectionBookmark=this.editor.selection.getBookmark(e)},e.prototype.restoreSelection=function(){this.editor.selection.moveToBookmark(this.selectionBookmark)},e.prototype.setupCommands=function(e){var t=this;this.addCommands({"mceResetDesignMode,mceBeginUndoLevel":_,"mceEndUndoLevel,mceAddUndoLevel":function(){e.undoManager.add()},mceFocus:function(t,n,r){hy(e,r)},"Cut,Copy,Paste":function(n){var r,o=e.getDoc();try{t.execNativeCommand(n)}catch(a){r=!0}if("paste"!==n||o.queryCommandEnabled(n)||(r=!0),r||!o.queryCommandSupported(n)){var i=e.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");en.mac&&(i=i.replace(/Ctrl\+/g,"⌘+")),e.notificationManager.open({text:i,type:"error"})}},unlink:function(){if(e.selection.isCollapsed()){var t=e.dom.getParent(e.selection.getStart(),"a");t&&e.dom.remove(t,!0)}else e.formatter.remove("link")},"JustifyLeft,JustifyCenter,JustifyRight,JustifyFull,JustifyNone":function(n){var r=n.substring(7);"full"===r&&(r="justify"),MU("left,center,right,justify".split(","),(function(t){r!==t&&e.formatter.remove("align"+t)})),"none"!==r&&t.toggleFormat("align"+r)},"InsertUnorderedList,InsertOrderedList":function(n){var r;t.execNativeCommand(n);var o=e.dom.getParent(e.selection.getNode(),"ol,ul");o&&(r=o.parentNode,/^(H[1-6]|P|ADDRESS|PRE)$/.test(r.nodeName)&&(t.storeSelection(),e.dom.split(r,o),t.restoreSelection()))},"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){t.toggleFormat(e)},"ForeColor,HiliteColor":function(e,n,r){t.toggleFormat(e,{value:r})},FontName:function(t,n,r){RU(e,r)},FontSize:function(t,n,r){TU(e,r)},LineHeight:function(t,n,r){PU(e,r)},Lang:function(e,n,r){t.toggleFormat(e,{value:r.code,customValue:r.customCode})},RemoveFormat:function(t){e.formatter.remove(t)},mceBlockQuote:function(){t.toggleFormat("blockquote")},FormatBlock:function(e,n,r){return t.toggleFormat(r||"p")},mceCleanup:function(){var t=e.selection.getBookmark();e.setContent(e.getContent()),e.selection.moveToBookmark(t)},mceRemoveNode:function(n,r,o){var i=o||e.selection.getNode();i!==e.getBody()&&(t.storeSelection(),e.dom.remove(i,!0),t.restoreSelection())},mceSelectNodeDepth:function(t,n,r){var o=0;e.dom.getParent(e.selection.getNode(),(function(t){if(1===t.nodeType&&o++===r)return e.selection.select(t),!1}),e.getBody())},mceSelectNode:function(t,n,r){e.selection.select(r)},mceInsertContent:function(t,n,r){IU(e,r)},mceInsertRawHTML:function(t,n,r){e.selection.setContent("tiny_mce_marker");var o=e.getContent();e.setContent(o.replace(/tiny_mce_marker/g,(function(){return r})))},mceInsertNewLine:function(t,n,r){uM(e,r)},mceToggleFormat:function(e,n,r){t.toggleFormat(r)},mceSetContent:function(t,n,r){e.setContent(r)},"Indent,Outdent":function(t){_P(e,t)},mceRepaint:_,InsertHorizontalRule:function(){e.execCommand("mceInsertContent",!1,"<hr />")},mceToggleVisualAid:function(){e.hasVisual=!e.hasVisual,e.addVisual()},mceReplaceContent:function(t,n,r){e.execCommand("mceInsertContent",!1,r.replace(/\{\$selection\}/g,e.selection.getContent({format:"text"})))},mceInsertLink:function(t,n,r){"string"===typeof r&&(r={href:r});var o=e.dom.getParent(e.selection.getNode(),"a");r.href=r.href.replace(/ /g,"%20"),o&&r.href||e.formatter.remove("link"),r.href&&e.formatter.apply("link",r,o)},selectAll:function(){var t=e.dom.getParent(e.selection.getStart(),io);if(t){var n=e.dom.createRng();n.selectNodeContents(t),e.selection.setRng(n)}},mceNewDocument:function(){e.setContent("")},InsertLineBreak:function(t,n,r){return qI(e,r),!0}});var n=function(t){return function(){var n=e.selection,r=n.isCollapsed()?[e.dom.getParent(n.getNode(),e.dom.isBlock)]:n.getSelectedBlocks(),o=FU(r,(function(n){return!!e.formatter.matchNode(n,t)}));return-1!==UU(o,!0)}};t.addCommands({JustifyLeft:n("alignleft"),JustifyCenter:n("aligncenter"),JustifyRight:n("alignright"),JustifyFull:n("alignjustify"),"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){return t.isFormatMatch(e)},mceBlockQuote:function(){return t.isFormatMatch("blockquote")},Outdent:function(){return SP(e)},"InsertUnorderedList,InsertOrderedList":function(t){var n=e.dom.getParent(e.selection.getNode(),"ul,ol");return n&&("insertunorderedlist"===t&&"UL"===n.tagName||"insertorderedlist"===t&&"OL"===n.tagName)}},"state"),t.addCommands({Undo:function(){e.undoManager.undo()},Redo:function(){e.undoManager.redo()}}),t.addQueryValueHandler("FontName",(function(){return DU(e)}),this),t.addQueryValueHandler("FontSize",(function(){return OU(e)}),this),t.addQueryValueHandler("LineHeight",(function(){return BU(e)}),this)},e}(),HU="data-mce-contenteditable",jU=function(e,t,n){Ef(e,t)&&!1===n?Nf(e,t):n&&Sf(e,t)},VU=function(e,t,n){try{e.getDoc().execCommand(t,!1,String(n))}catch(r){}},qU=function(e,t){e.dom.contentEditable=t?"true":"false"},$U=function(e){Z(Af(e,'*[contenteditable="true"]'),(function(e){vo(e,HU,"true"),qU(e,!1)}))},WU=function(e){Z(Af(e,"*["+HU+'="true"]'),(function(e){xo(e,HU),qU(e,!0)}))},KU=function(e){q.from(e.selection.getNode()).each((function(e){e.removeAttribute("data-mce-selected")}))},XU=function(e){e.selection.setRng(e.selection.getRng())},YU=function(e,t){var n=Cn.fromDom(e.getBody());jU(n,"mce-content-readonly",t),t?(e.selection.controlSelection.hideResizeRect(),e._selectionOverrides.hideFakeCaret(),KU(e),e.readonly=!0,qU(n,!1),$U(n)):(e.readonly=!1,qU(n,!0),WU(n),VU(e,"StyleWithCSS",!1),VU(e,"enableInlineTableEditing",!1),VU(e,"enableObjectResizing",!1),my(e)&&e.focus(),XU(e),e.nodeChanged())},GU=function(e){return e.readonly},JU=function(e){e.parser.addAttributeFilter("contenteditable",(function(t){GU(e)&&Z(t,(function(e){e.attr(HU,e.attr("contenteditable")),e.attr("contenteditable","false")}))})),e.serializer.addAttributeFilter(HU,(function(t){GU(e)&&Z(t,(function(e){e.attr("contenteditable",e.attr(HU))}))})),e.serializer.addTempAttr(HU)},QU=function(e){e.serializer?JU(e):e.on("PreInit",(function(){JU(e)}))},ZU=function(e){return"click"===e.type},ez=function(e,t){var n=function(t){return Bn(t,Cn.fromDom(e.getBody()))};return Ai(t,"a",n).bind((function(e){return Co(e,"href")}))},tz=function(e,t){if(ZU(t)&&!tv.metaKeyPressed(t)){var n=Cn.fromDom(t.target);ez(e,n).each((function(n){if(t.preventDefault(),/^#/.test(n)){var r=e.dom.select(n+',[name="'+ut(n,"#")+'"]');r.length&&e.selection.scrollIntoView(r[0],!0)}else window.open(n,"_blank","rel=noopener noreferrer,menubar=yes,toolbar=yes,location=yes,status=yes,resizable=yes,scrollbars=yes")}))}},nz=function(e){e.on("ShowCaret",(function(t){GU(e)&&t.preventDefault()})),e.on("ObjectSelected",(function(t){GU(e)&&t.preventDefault()}))},rz=gn.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input beforeinput contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend touchcancel"," "),oz=function(){function e(e){this.bindings={},this.settings=e||{},this.scope=this.settings.scope||this,this.toggleEvent=this.settings.toggleEvent||F}return e.isNative=function(e){return!!rz[e.toLowerCase()]},e.prototype.fire=function(e,t){var n=e.toLowerCase(),r=Yu(n,t||{},this.scope);this.settings.beforeFire&&this.settings.beforeFire(r);var o=this.bindings[n];if(o)for(var i=0,a=o.length;i<a;i++){var u=o[i];if(!u.removed){if(u.once&&this.off(n,u.func),r.isImmediatePropagationStopped())return r;if(!1===u.func.call(this.scope,r))return r.preventDefault(),r}}return r},e.prototype.on=function(e,t,n,r){if(!1===t&&(t=F),t){var o={func:t,removed:!1};r&&gn.extend(o,r);var i=e.toLowerCase().split(" "),a=i.length;while(a--){var u=i[a],c=this.bindings[u];c||(c=[],this.toggleEvent(u,!0)),c=n?We([o],c,!0):We(We([],c,!0),[o],!1),this.bindings[u]=c}}return this},e.prototype.off=function(e,t){var n=this;if(e){var r=e.toLowerCase().split(" "),o=r.length;while(o--){var i=r[o],a=this.bindings[i];if(!i)return xe(this.bindings,(function(e,t){n.toggleEvent(t,!1),delete n.bindings[t]})),this;if(a){if(t){var u=te(a,(function(e){return e.func===t}));a=u.fail,this.bindings[i]=a,Z(u.pass,(function(e){e.removed=!0}))}else a.length=0;a.length||(this.toggleEvent(e,!1),delete this.bindings[i])}}}else xe(this.bindings,(function(e,t){n.toggleEvent(t,!1)})),this.bindings={};return this},e.prototype.once=function(e,t,n){return this.on(e,t,n,{once:!0})},e.prototype.has=function(e){return e=e.toLowerCase(),!(!this.bindings[e]||0===this.bindings[e].length)},e}(),iz=function(e){return e._eventDispatcher||(e._eventDispatcher=new oz({scope:e,toggleEvent:function(t,n){oz.isNative(t)&&e.toggleNativeEvent&&e.toggleNativeEvent(t,n)}})),e._eventDispatcher},az={fire:function(e,t,n){var r=this;if(r.removed&&"remove"!==e&&"detach"!==e)return t;var o=iz(r).fire(e,t);if(!1!==n&&r.parent){var i=r.parent();while(i&&!o.isPropagationStopped())i.fire(e,o,!1),i=i.parent()}return o},on:function(e,t,n){return iz(this).on(e,t,n)},off:function(e,t){return iz(this).off(e,t)},once:function(e,t){return iz(this).once(e,t)},hasEventListeners:function(e){return iz(this).has(e)}},uz=Hs.DOM,cz=function(e,t){if("selectionchange"===t)return e.getDoc();if(!e.inline&&/^mouse|touch|click|contextmenu|drop|dragover|dragend/.test(t))return e.getDoc().documentElement;var n=nm(e);return n?(e.eventRoot||(e.eventRoot=uz.select(n)[0]),e.eventRoot):e.getBody()},sz=function(e){return!e.hidden&&!GU(e)},fz=function(e,t,n){sz(e)?e.fire(t,n):GU(e)&&tz(e,n)},lz=function(e,t){var n;if(e.delegates||(e.delegates={}),!e.delegates[t]&&!e.removed){var r=cz(e,t);if(nm(e)){if(mB||(mB={},e.editorManager.on("removeEditor",(function(){e.editorManager.activeEditor||mB&&(xe(mB,(function(t,n){e.dom.unbind(cz(e,n))})),mB=null)}))),mB[t])return;n=function(n){var r=n.target,o=e.editorManager.get(),i=o.length;while(i--){var a=o[i].getBody();(a===r||uz.isChildOf(r,a))&&fz(o[i],t,n)}},mB[t]=n,uz.bind(r,t,n)}else n=function(n){fz(e,t,n)},uz.bind(r,t,n),e.delegates[t]=n}},dz=qe(qe({},az),{bindPendingEventDelegates:function(){var e=this;gn.each(e._pendingNativeEvents,(function(t){lz(e,t)}))},toggleNativeEvent:function(e,t){var n=this;"focus"!==e&&"blur"!==e&&(n.removed||(t?n.initialized?lz(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&(n.dom.unbind(cz(n,e),e,n.delegates[e]),delete n.delegates[e])))},unbindAllNativeEvents:function(){var e=this,t=e.getBody(),n=e.dom;e.delegates&&(xe(e.delegates,(function(t,n){e.dom.unbind(cz(e,n),n,t)})),delete e.delegates),!e.inline&&t&&n&&(t.onload=null,n.unbind(e.getWin()),n.unbind(e.getDoc())),n&&(n.unbind(t),n.unbind(e.getContainer()))}}),mz=["design","readonly"],gz=function(e,t,n,r){var o=n[t.get()],i=n[r];try{i.activate()}catch(DH){return}o.deactivate(),o.editorReadOnly!==i.editorReadOnly&&YU(e,i.editorReadOnly),t.set(r),$h(e,r)},pz=function(e,t,n,r){if(r!==n.get()){if(!Oe(t,r))throw new Error("Editor mode '"+r+"' is invalid");e.initialized?gz(e,n,t,r):e.on("init",(function(){return gz(e,n,t,r)}))}},hz=function(e,t,n){var r;if(G(mz,t))throw new Error("Cannot override default mode "+t);return qe(qe({},e),(r={},r[t]=qe(qe({},n),{deactivate:function(){try{n.deactivate()}catch(DH){}}}),r))},vz=function(e){var t=Gs("design"),n=Gs({design:{activate:_,deactivate:_,editorReadOnly:!1},readonly:{activate:_,deactivate:_,editorReadOnly:!0}});return QU(e),nz(e),{isReadOnly:function(){return GU(e)},set:function(r){return pz(e,n.get(),t,r)},get:function(){return t.get()},register:function(e,t){n.set(hz(n.get(),e,t))}}},bz=gn.each,yz=gn.explode,Cz={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},wz=gn.makeMap("alt,ctrl,shift,meta,access"),xz=function(e){var t,n={};bz(yz(e.toLowerCase(),"+"),(function(e){e in wz?n[e]=!0:/^[0-9]{2,}$/.test(e)?n.keyCode=parseInt(e,10):(n.charCode=e.charCodeAt(0),n.keyCode=Cz[e]||e.toUpperCase().charCodeAt(0))}));var r=[n.keyCode];for(t in wz)n[t]?r.push(t):n[t]=!1;return n.id=r.join(","),n.access&&(n.alt=!0,en.mac?n.ctrl=!0:n.shift=!0),n.meta&&(en.mac?n.meta=!0:(n.ctrl=!0,n.meta=!1)),n},Sz=function(){function e(e){this.shortcuts={},this.pendingPatterns=[],this.editor=e;var t=this;e.on("keyup keypress keydown",(function(e){!t.hasModifier(e)&&!t.isFunctionKey(e)||e.isDefaultPrevented()||(bz(t.shortcuts,(function(n){if(t.matchShortcut(e,n))return t.pendingPatterns=n.subpatterns.slice(0),"keydown"===e.type&&t.executeShortcutAction(n),!0})),t.matchShortcut(e,t.pendingPatterns[0])&&(1===t.pendingPatterns.length&&"keydown"===e.type&&t.executeShortcutAction(t.pendingPatterns[0]),t.pendingPatterns.shift()))}))}return e.prototype.add=function(e,t,n,r){var o=this,i=o.normalizeCommandFunc(n);return bz(yz(gn.trim(e)),(function(e){var n=o.createShortcut(e,t,i,r);o.shortcuts[n.id]=n})),!0},e.prototype.remove=function(e){var t=this.createShortcut(e);return!!this.shortcuts[t.id]&&(delete this.shortcuts[t.id],!0)},e.prototype.normalizeCommandFunc=function(e){var t=this,n=e;return"string"===typeof n?function(){t.editor.execCommand(n,!1,null)}:gn.isArray(n)?function(){t.editor.execCommand(n[0],n[1],n[2])}:n},e.prototype.createShortcut=function(e,t,n,r){var o=gn.map(yz(e,">"),xz);return o[o.length-1]=gn.extend(o[o.length-1],{func:n,scope:r||this.editor}),gn.extend(o[0],{desc:this.editor.translate(t),subpatterns:o.slice(1)})},e.prototype.hasModifier=function(e){return e.altKey||e.ctrlKey||e.metaKey},e.prototype.isFunctionKey=function(e){return"keydown"===e.type&&e.keyCode>=112&&e.keyCode<=123},e.prototype.matchShortcut=function(e,t){return!!t&&(t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&(t.alt===e.altKey&&t.shift===e.shiftKey&&(!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0))))},e.prototype.executeShortcutAction=function(e){return e.func?e.func.call(e.scope):null},e}(),kz=function(){var e={},t={},n={},r={},o={},i={},a={},u=function(e,t){return function(n,r){return e[n.toLowerCase()]=qe(qe({},r),{type:t})}},c=function(e,t){return r[e.toLowerCase()]=t};return{addButton:u(e,"button"),addGroupToolbarButton:u(e,"grouptoolbarbutton"),addToggleButton:u(e,"togglebutton"),addMenuButton:u(e,"menubutton"),addSplitButton:u(e,"splitbutton"),addMenuItem:u(t,"menuitem"),addNestedMenuItem:u(t,"nestedmenuitem"),addToggleMenuItem:u(t,"togglemenuitem"),addAutocompleter:u(n,"autocompleter"),addContextMenu:u(o,"contextmenu"),addContextToolbar:u(i,"contexttoolbar"),addContextForm:u(i,"contextform"),addSidebar:u(a,"sidebar"),addIcon:c,getAll:function(){return{buttons:e,menuItems:t,icons:r,popups:n,contextMenus:o,contextToolbars:i,sidebars:a}}}},Nz=function(){var e=kz();return{addAutocompleter:e.addAutocompleter,addButton:e.addButton,addContextForm:e.addContextForm,addContextMenu:e.addContextMenu,addContextToolbar:e.addContextToolbar,addIcon:e.addIcon,addMenuButton:e.addMenuButton,addMenuItem:e.addMenuItem,addNestedMenuItem:e.addNestedMenuItem,addSidebar:e.addSidebar,addSplitButton:e.addSplitButton,addToggleButton:e.addToggleButton,addGroupToolbarButton:e.addGroupToolbarButton,addToggleMenuItem:e.addToggleMenuItem,getAll:e.getAll}},Ez=Hs.DOM,_z=gn.extend,Az=gn.each,Rz=gn.resolve,Dz=en.ie,Tz=function(){function e(e,t,n){var r=this;this.plugins={},this.contentCSS=[],this.contentStyles=[],this.loadedCSS={},this.isNotDirty=!1,this.editorManager=n,this.documentBaseUrl=n.documentBaseURL,_z(this,dz),this.settings=xR(this,e,this.documentBaseUrl,n.defaultSettings,t),this.settings.suffix&&(n.suffix=this.settings.suffix),this.suffix=n.suffix,this.settings.base_url&&n._setBaseUrl(this.settings.base_url),this.baseUri=n.baseURI,this.settings.referrer_policy&&(Ys.ScriptLoader._setReferrerPolicy(this.settings.referrer_policy),Hs.DOM.styleSheetLoader._setReferrerPolicy(this.settings.referrer_policy)),lf.languageLoad=this.settings.language_load,lf.baseURL=n.baseURL,this.id=e,this.setDirty(!1),this.documentBaseURI=new uA(this.settings.document_base_url,{base_uri:this.baseUri}),this.baseURI=this.baseUri,this.inline=!!this.settings.inline,this.shortcuts=new Sz(this),this.editorCommands=new zU(this),this.settings.cache_suffix&&(en.cacheSuffix=this.settings.cache_suffix.replace(/^[\?\&]+/,"")),this.ui={registry:Nz(),styleSheetLoader:void 0,show:_,hide:_,enable:_,disable:_,isDisabled:F};var o=this,i=vz(o);this.mode=i,this.setMode=i.set,n.fire("SetupEditor",{editor:this}),this.execCallback("setup",this),this.$=Bs.overrideDefaults((function(){return{context:r.inline?r.getBody():r.getDoc(),element:r.getBody()}}))}return e.prototype.render=function(){gU(this)},e.prototype.focus=function(e){this.execCommand("mceFocus",!1,e)},e.prototype.hasFocus=function(){return dy(this)},e.prototype.execCallback=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r,o=this,i=o.settings[e];if(i)return o.callbackLookup&&(r=o.callbackLookup[e])&&(i=r.func,r=r.scope),"string"===typeof i&&(r=i.replace(/\.\w+$/,""),r=r?Rz(r):0,i=Rz(i),o.callbackLookup=o.callbackLookup||{},o.callbackLookup[e]={func:i,scope:r}),i.apply(r||o,t)},e.prototype.translate=function(e){return ff.translate(e)},e.prototype.getParam=function(e,t,n){return ER(this,e,t,n)},e.prototype.hasPlugin=function(e,t){var n=G(Sm(this).split(/[ ,]/),e);return!!n&&(!t||void 0!==VR.get(e))},e.prototype.nodeChanged=function(e){this._nodeChangeDispatcher.nodeChanged(e)},e.prototype.addCommand=function(e,t,n){this.editorCommands.addCommand(e,t,n)},e.prototype.addQueryStateHandler=function(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)},e.prototype.addQueryValueHandler=function(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)},e.prototype.addShortcut=function(e,t,n,r){this.shortcuts.add(e,t,n,r)},e.prototype.execCommand=function(e,t,n,r){return this.editorCommands.execCommand(e,t,n,r)},e.prototype.queryCommandState=function(e){return this.editorCommands.queryCommandState(e)},e.prototype.queryCommandValue=function(e){return this.editorCommands.queryCommandValue(e)},e.prototype.queryCommandSupported=function(e){return this.editorCommands.queryCommandSupported(e)},e.prototype.show=function(){var e=this;e.hidden&&(e.hidden=!1,e.inline?e.getBody().contentEditable="true":(Ez.show(e.getContainer()),Ez.hide(e.id)),e.load(),e.fire("show"))},e.prototype.hide=function(){var e=this,t=e.getDoc();e.hidden||(Dz&&t&&!e.inline&&t.execCommand("SelectAll"),e.save(),e.inline?(e.getBody().contentEditable="false",e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(Ez.hide(e.getContainer()),Ez.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.fire("hide"))},e.prototype.isHidden=function(){return!!this.hidden},e.prototype.setProgressState=function(e,t){this.fire("ProgressState",{state:e,time:t})},e.prototype.load=function(e){var t,n=this,r=n.getElement();if(n.removed)return"";if(r){e=e||{},e.load=!0;var o=Qr(r)?r.value:r.innerHTML;return t=n.setContent(o,e),e.element=r,e.no_events||n.fire("LoadContent",e),e.element=r=null,t}},e.prototype.save=function(e){var t,n,r=this,o=r.getElement();if(o&&r.initialized&&!r.removed)return e=e||{},e.save=!0,e.element=o,t=e.content=r.getContent(e),e.no_events||r.fire("SaveContent",e),"raw"===e.format&&r.fire("RawSaveContent",e),t=e.content,Qr(o)?o.value=t:(!e.is_removing&&r.inline||(o.innerHTML=t),(n=Ez.getParent(r.id,"form"))&&Az(n.elements,(function(e){if(e.name===r.id)return e.value=t,!1}))),e.element=o=null,!1!==e.set_dirty&&r.setDirty(!1),t},e.prototype.setContent=function(e,t){return IA(this,e,t)},e.prototype.getContent=function(e){return LA(this,e)},e.prototype.insertContent=function(e,t){t&&(e=_z({content:e},t)),this.execCommand("mceInsertContent",!1,e)},e.prototype.resetContent=function(e){void 0===e?IA(this,this.startContent,{format:"raw"}):IA(this,e),this.undoManager.reset(),this.setDirty(!1),this.nodeChanged()},e.prototype.isDirty=function(){return!this.isNotDirty},e.prototype.setDirty=function(e){var t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.fire("dirty")},e.prototype.getContainer=function(){var e=this;return e.container||(e.container=Ez.get(e.editorContainer||e.id+"_parent")),e.container},e.prototype.getContentAreaContainer=function(){return this.contentAreaContainer},e.prototype.getElement=function(){return this.targetElm||(this.targetElm=Ez.get(this.id)),this.targetElm},e.prototype.getWin=function(){var e,t=this;return t.contentWindow||(e=t.iframeElement,e&&(t.contentWindow=e.contentWindow)),t.contentWindow},e.prototype.getDoc=function(){var e,t=this;return t.contentDocument||(e=t.getWin(),e&&(t.contentDocument=e.document)),t.contentDocument},e.prototype.getBody=function(){var e=this.getDoc();return this.bodyElement||(e?e.body:null)},e.prototype.convertURL=function(e,t,n){var r=this,o=r.settings;return o.urlconverter_callback?r.execCallback("urlconverter_callback",e,n,!0,t):!o.convert_urls||n&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length?e:o.relative_urls?r.documentBaseURI.toRelative(e):(e=r.documentBaseURI.toAbsolute(e,o.remove_script_host),e)},e.prototype.addVisual=function(e){pU(this,e)},e.prototype.remove=function(){jA(this)},e.prototype.destroy=function(e){VA(this,e)},e.prototype.uploadImages=function(e){return this.editorUpload.uploadImages(e)},e.prototype._scanForImages=function(){return this.editorUpload.scanForImages()},e.prototype.addButton=function(){throw new Error("editor.addButton has been removed in tinymce 5x, use editor.ui.registry.addButton or editor.ui.registry.addToggleButton or editor.ui.registry.addSplitButton instead")},e.prototype.addSidebar=function(){throw new Error("editor.addSidebar has been removed in tinymce 5x, use editor.ui.registry.addSidebar instead")},e.prototype.addMenuItem=function(){throw new Error("editor.addMenuItem has been removed in tinymce 5x, use editor.ui.registry.addMenuItem instead")},e.prototype.addContextToolbar=function(){throw new Error("editor.addContextToolbar has been removed in tinymce 5x, use editor.ui.registry.addContextToolbar instead")},e}(),Oz=Hs.DOM,Bz=gn.explode,Pz=gn.each,Lz=gn.extend,Iz=0,Mz=!1,Fz=[],Uz=[],zz=function(e){return"length"!==e},Hz=function(e){var t=e.type;Pz(Wz.get(),(function(n){switch(t){case"scroll":n.fire("ScrollWindow",e);break;case"resize":n.fire("ResizeWindow",e);break}}))},jz=function(e){e!==Mz&&(e?Bs(window).on("resize scroll",Hz):Bs(window).off("resize scroll",Hz),Mz=e)},Vz=function(e){var t=Uz;delete Fz[e.id];for(var n=0;n<Fz.length;n++)if(Fz[n]===e){Fz.splice(n,1);break}return Uz=ne(Uz,(function(t){return e!==t})),Wz.activeEditor===e&&(Wz.activeEditor=Uz.length>0?Uz[0]:null),Wz.focusedEditor===e&&(Wz.focusedEditor=null),t.length!==Uz.length},qz=function(e){return e&&e.initialized&&!(e.getContainer()||e.getBody()).parentNode&&(Vz(e),e.unbindAllNativeEvents(),e.destroy(!0),e.removed=!0,e=null),e},$z="CSS1Compat"!==document.compatMode,Wz=qe(qe({},az),{baseURI:null,baseURL:null,defaultSettings:{},documentBaseURL:null,suffix:null,$:Bs,majorVersion:"5",minorVersion:"10.9",releaseDate:"2023-11-15",editors:Fz,i18n:ff,activeEditor:null,focusedEditor:null,settings:{},setup:function(){var e,t,n=this,r="";t=uA.getDocumentBaseUrl(document.location),/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/"));var o=window.tinymce||window.tinyMCEPreInit;if(o)e=o.base||o.baseURL,r=o.suffix;else{for(var i=document.getElementsByTagName("script"),a=0;a<i.length;a++){var u=i[a].src||"";if(""!==u){var c=u.substring(u.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(u)){-1!==c.indexOf(".min")&&(r=".min"),e=u.substring(0,u.lastIndexOf("/"));break}}}if(!e&&document.currentScript){u=document.currentScript.src;-1!==u.indexOf(".min")&&(r=".min"),e=u.substring(0,u.lastIndexOf("/"))}}n.baseURL=new uA(t).toAbsolute(e),n.documentBaseURL=t,n.baseURI=new uA(n.baseURL),n.suffix=r,ny(n)},overrideDefaults:function(e){var t=e.base_url;t&&this._setBaseUrl(t);var n=e.suffix;e.suffix&&(this.suffix=n),this.defaultSettings=e;var r=e.plugin_base_urls;void 0!==r&&xe(r,(function(e,t){lf.PluginManager.urls[t]=e}))},init:function(e){var t,n=this,r=gn.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option table tbody tfoot thead tr th td script noscript style textarea video audio iframe object menu"," "),o=function(e,t){return e.inline&&t.tagName.toLowerCase()in r},i=function(e){var t=e.id;return t||(t=Te(e,"name").filter((function(e){return!Oz.get(e)})).getOrThunk(Oz.uniqueId),e.setAttribute("id",t)),t},a=function(t){var r=e[t];if(r)return r.apply(n,[])},u=function(e,t){return t.constructor===RegExp?t.test(e.className):Oz.hasClass(e,t)},c=function(e){var t=[];if(en.browser.isIE()&&en.browser.version.major<11)return nD("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tinymce.com/docs/get-started/system-requirements/"),[];if($z)return nD("Failed to initialize the editor as the document is not in standards mode. TinyMCE requires standards mode."),[];if(e.types)return Pz(e.types,(function(e){t=t.concat(Oz.select(e.selector))})),t;if(e.selector)return Oz.select(e.selector);if(e.target)return[e.target];switch(e.mode){case"exact":var n=e.elements||"";n.length>0&&Pz(Bz(n),(function(e){var n=Oz.get(e);n?t.push(n):Pz(document.forms,(function(n){Pz(n.elements,(function(n){n.name===e&&(e="mce_editor_"+Iz++,Oz.setAttrib(n,"id",e),t.push(n))}))}))}));break;case"textareas":case"specific_textareas":Pz(Oz.select("textarea"),(function(n){e.editor_deselector&&u(n,e.editor_deselector)||e.editor_selector&&!u(n,e.editor_selector)||t.push(n)}));break}return t},s=function(e){t=e},f=function(){var t,r=0,u=[],l=function(e,o,i){var a=new Tz(e,o,n);u.push(a),a.on("init",(function(){++r===t.length&&s(u)})),a.targetElm=a.targetElm||i,a.render()};Oz.unbind(window,"ready",f),a("onpageload"),t=Bs.unique(c(e)),e.types?Pz(e.types,(function(n){gn.each(t,(function(t){return!Oz.is(t,n.selector)||(l(i(t),Lz({},e,n),t),!1)}))})):(gn.each(t,(function(e){qz(n.get(e.id))})),t=gn.grep(t,(function(e){return!n.get(e.id)})),0===t.length?s([]):Pz(t,(function(t){o(e,t)?nD("Could not initialize inline editor on invalid inline target element",t):l(i(t),e,t)})))};return n.settings=e,Oz.bind(window,"ready",f),new Ri((function(e){t?e(t):s=function(t){e(t)}}))},get:function(e){return 0===arguments.length?Uz.slice(0):v(e)?ae(Uz,(function(t){return t.id===e})).getOr(null):E(e)&&Uz[e]?Uz[e]:null},add:function(e){var t=this,n=Fz[e.id];return n===e||(null===t.get(e.id)&&(zz(e.id)&&(Fz[e.id]=e),Fz.push(e),Uz.push(e)),jz(!0),t.activeEditor=e,t.fire("AddEditor",{editor:e}),gB||(gB=function(e){var n=t.fire("BeforeUnload");if(n.returnValue)return e.preventDefault(),e.returnValue=n.returnValue,n.returnValue},window.addEventListener("beforeunload",gB))),e},createEditor:function(e,t){return this.add(new Tz(e,t,this))},remove:function(e){var t,n,r=this;if(e){if(!v(e))return n=e,C(r.get(n.id))?null:(Vz(n)&&r.fire("RemoveEditor",{editor:n}),0===Uz.length&&window.removeEventListener("beforeunload",gB),n.remove(),jz(Uz.length>0),n);Pz(Oz.select(e),(function(e){n=r.get(e.id),n&&r.remove(n)}))}else for(t=Uz.length-1;t>=0;t--)r.remove(Uz[t])},execCommand:function(e,t,n){var r=this,o=r.get(n);switch(e){case"mceAddEditor":return r.get(n)||new Tz(n,r.settings,r).render(),!0;case"mceRemoveEditor":return o&&o.remove(),!0;case"mceToggleEditor":return o?(o.isHidden()?o.show():o.hide(),!0):(r.execCommand("mceAddEditor",!1,n),!0)}return!!r.activeEditor&&r.activeEditor.execCommand(e,t,n)},triggerSave:function(){Pz(Uz,(function(e){e.save()}))},addI18n:function(e,t){ff.add(e,t)},translate:function(e){return ff.translate(e)},setActive:function(e){var t=this.activeEditor;this.activeEditor!==e&&(t&&t.fire("deactivate",{relatedTarget:e}),e.fire("activate",{relatedTarget:t})),this.activeEditor=e},_setBaseUrl:function(e){this.baseURL=new uA(this.documentBaseURL).toAbsolute(e.replace(/\/+$/,"")),this.baseURI=new uA(this.baseURL)}});Wz.setup();var Kz,Xz,Yz=Math.min,Gz=Math.max,Jz=Math.round,Qz=function(e,t,n){var r=t.x,o=t.y,i=e.w,a=e.h,u=t.w,c=t.h,s=(n||"").split("");return"b"===s[0]&&(o+=c),"r"===s[1]&&(r+=u),"c"===s[0]&&(o+=Jz(c/2)),"c"===s[1]&&(r+=Jz(u/2)),"b"===s[3]&&(o-=a),"r"===s[4]&&(r-=i),"c"===s[3]&&(o-=Jz(a/2)),"c"===s[4]&&(r-=Jz(i/2)),rH(r,o,i,a)},Zz=function(e,t,n,r){var o,i;for(i=0;i<r.length;i++)if(o=Qz(e,t,r[i]),o.x>=n.x&&o.x+o.w<=n.w+n.x&&o.y>=n.y&&o.y+o.h<=n.h+n.y)return r[i];return null},eH=function(e,t,n){return rH(e.x-t,e.y-n,e.w+2*t,e.h+2*n)},tH=function(e,t){var n=Gz(e.x,t.x),r=Gz(e.y,t.y),o=Yz(e.x+e.w,t.x+t.w),i=Yz(e.y+e.h,t.y+t.h);return o-n<0||i-r<0?null:rH(n,r,o-n,i-r)},nH=function(e,t,n){var r=e.x,o=e.y,i=e.x+e.w,a=e.y+e.h,u=t.x+t.w,c=t.y+t.h,s=Gz(0,t.x-r),f=Gz(0,t.y-o),l=Gz(0,i-u),d=Gz(0,a-c);return r+=s,o+=f,n&&(i+=s,a+=f,r-=l,o-=d),i-=l,a-=d,rH(r,o,i-r,a-o)},rH=function(e,t,n,r){return{x:e,y:t,w:n,h:r}},oH=function(e){return rH(e.left,e.top,e.width,e.height)},iH={inflate:eH,relativePosition:Qz,findBestRelativePosition:Zz,intersect:tH,clamp:nH,create:rH,fromClientRect:oH},aH=function(e,t,n){void 0===n&&(n=1e3);var r=!1,o=null,i=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r||(r=!0,null!==o&&(clearTimeout(o),o=null),e.apply(null,t))}},a=i(e),u=i(t),c=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];r||null!==o||(o=setTimeout((function(){return u.apply(null,e)}),n))};return{start:c,resolve:a,reject:u}},uH=function(){var e={},t={},n=function(n,r){var o='Script at URL "'+r+'" failed to load',i='Script at URL "'+r+"\" did not call `tinymce.Resource.add('"+n+"', data)` within 1 second";if(void 0!==e[n])return e[n];var a=new Ri((function(e,a){var u=aH(e,a);t[n]=u.resolve,Ys.ScriptLoader.loadScript(r,(function(){return u.start(i)}),(function(){return u.reject(o)}))}));return e[n]=a,a},r=function(n,r){void 0!==t[n]&&(t[n](r),delete t[n]),e[n]=Ri.resolve(r)};return{load:n,add:r}},cH=uH(),sH=gn.each,fH=gn.extend,lH=function(){};lH.extend=Kz=function(e){var t=this,n=t.prototype,r=function(){var e,t,n,r=this;if(!Xz&&(r.init&&r.init.apply(r,arguments),t=r.Mixins,t)){e=t.length;while(e--)n=t[e],n.init&&n.init.apply(r,arguments)}},o=function(){return this},i=function(e,t){return function(){var r=this,o=r._super;r._super=n[e];var i=t.apply(r,arguments);return r._super=o,i}};Xz=!0;var a=new t;return Xz=!1,e.Mixins&&(sH(e.Mixins,(function(t){for(var n in t)"init"!==n&&(e[n]=t[n])})),n.Mixins&&(e.Mixins=n.Mixins.concat(e.Mixins))),e.Methods&&sH(e.Methods.split(","),(function(t){e[t]=o})),e.Properties&&sH(e.Properties.split(","),(function(t){var n="_"+t;e[t]=function(e){var t=this;return void 0!==e?(t[n]=e,t):t[n]}})),e.Statics&&sH(e.Statics,(function(e,t){r[t]=e})),e.Defaults&&n.Defaults&&(e.Defaults=fH({},n.Defaults,e.Defaults)),xe(e,(function(e,t){"function"===typeof e&&n[t]?a[t]=i(t,e):a[t]=e})),r.prototype=a,r.constructor=r,r.extend=Kz,r};var dH,mH=Math.min,gH=Math.max,pH=Math.round,hH=function(e){var t={},n=0,r=0,o=0,i=function(e,t,n){var r,o,i;r=0,o=0,i=0,e/=255,t/=255,n/=255;var a=mH(e,mH(t,n)),u=gH(e,gH(t,n));if(a===u)return i=a,{h:0,s:0,v:100*i};var c=e===a?t-n:n===a?e-t:n-e;return r=e===a?3:n===a?1:5,r=60*(r-c/(u-a)),o=(u-a)/u,i=u,{h:pH(r),s:pH(100*o),v:pH(100*i)}},a=function(e,t,i){if(e=(parseInt(e,10)||0)%360,t=parseInt(t,10)/100,i=parseInt(i,10)/100,t=gH(0,mH(t,1)),i=gH(0,mH(i,1)),0!==t){var a=e/60,u=i*t,c=u*(1-Math.abs(a%2-1)),s=i-u;switch(Math.floor(a)){case 0:n=u,r=c,o=0;break;case 1:n=c,r=u,o=0;break;case 2:n=0,r=u,o=c;break;case 3:n=0,r=c,o=u;break;case 4:n=c,r=0,o=u;break;case 5:n=u,r=0,o=c;break;default:n=r=o=0}n=pH(255*(n+s)),r=pH(255*(r+s)),o=pH(255*(o+s))}else n=r=o=pH(255*i)},u=function(){var e=function(e){return e=parseInt(e,10).toString(16),e.length>1?e:"0"+e};return"#"+e(n)+e(r)+e(o)},c=function(){return{r:n,g:r,b:o}},s=function(){return i(n,r,o)},f=function(e){var i;return"object"===typeof e?"r"in e?(n=e.r,r=e.g,o=e.b):"v"in e&&a(e.h,e.s,e.v):(i=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)[^\)]*\)/gi.exec(e))?(n=parseInt(i[1],10),r=parseInt(i[2],10),o=parseInt(i[3],10)):(i=/#([0-F]{2})([0-F]{2})([0-F]{2})/gi.exec(e))?(n=parseInt(i[1],16),r=parseInt(i[2],16),o=parseInt(i[3],16)):(i=/#([0-F])([0-F])([0-F])/gi.exec(e))&&(n=parseInt(i[1]+i[1],16),r=parseInt(i[2]+i[2],16),o=parseInt(i[3]+i[3],16)),n=n<0?0:n>255?255:n,r=r<0?0:r>255?255:r,o=o<0?0:o>255?255:o,t};return e&&f(e),t.toRgb=c,t.toHsv=s,t.toHex=u,t.parse=f,t},vH=function(e){var t=JSON.stringify(e);return v(t)?t.replace(/[\u0080-\uFFFF]/g,(function(e){var t=e.charCodeAt(0).toString(16);return"\\u"+"0000".substring(t.length)+t})):t},bH={serialize:vH,parse:function(e){try{return JSON.parse(e)}catch(t){}}},yH={callbacks:{},count:0,send:function(e){var t=this,n=Hs.DOM,r=void 0!==e.count?e.count:t.count,o="tinymce_jsonp_"+r;t.callbacks[r]=function(i){n.remove(o),delete t.callbacks[r],e.callback(i)},n.add(n.doc.body,"script",{id:o,src:e.url,type:"text/javascript"}),t.count++}},CH=qe(qe({},az),{send:function(e){var t,n=0,r=function(){!e.async||4===t.readyState||n++>1e4?(e.success&&n<1e4&&200===t.status?e.success.call(e.success_scope,""+t.responseText,t,e):e.error&&e.error.call(e.error_scope,n>1e4?"TIMED_OUT":"GENERAL",t,e),t=null):Ii.setTimeout(r,10)};if(e.scope=e.scope||this,e.success_scope=e.success_scope||e.scope,e.error_scope=e.error_scope||e.scope,e.async=!1!==e.async,e.data=e.data||"",CH.fire("beforeInitialize",{settings:e}),t=new XMLHttpRequest,t.overrideMimeType&&t.overrideMimeType(e.content_type),t.open(e.type||(e.data?"POST":"GET"),e.url,e.async),e.crossDomain&&(t.withCredentials=!0),e.content_type&&t.setRequestHeader("Content-Type",e.content_type),e.requestheaders&&gn.each(e.requestheaders,(function(e){t.setRequestHeader(e.key,e.value)})),t.setRequestHeader("X-Requested-With","XMLHttpRequest"),t=CH.fire("beforeSend",{xhr:t,settings:e}).xhr,t.send(e.data),!e.async)return r();Ii.setTimeout(r,10)}}),wH=gn.extend,xH=function(){function e(e){this.settings=wH({},e),this.count=0}return e.sendRPC=function(t){return(new e).send(t)},e.prototype.send=function(e){var t=e.error,n=e.success,r=wH(this.settings,e);r.success=function(e,o){e=bH.parse(e),"undefined"===typeof e&&(e={error:"JSON Parse error."}),e.error?t.call(r.error_scope||r.scope,e.error,o):n.call(r.success_scope||r.scope,e.result)},r.error=function(e,n){t&&t.call(r.error_scope||r.scope,e,n)},r.data=bH.serialize({id:e.id||"c"+this.count++,method:e.method,params:e.params}),r.content_type="application/json",CH.send(r)},e}(),SH=function(){return function(){var e={},t=[],n={getItem:function(t){var n=e[t];return n||null},setItem:function(n,r){t.push(n),e[n]=String(r)},key:function(e){return t[e]},removeItem:function(n){t=t.filter((function(e){return e===n})),delete e[n]},clear:function(){t=[],e={}},length:0};return Object.defineProperty(n,"length",{get:function(){return t.length},configurable:!1,enumerable:!1}),n}()};try{var kH="__storage_test__";dH=window.localStorage,dH.setItem(kH,kH),dH.removeItem(kH)}catch(DH){dH=SH()}var NH=dH,EH={geom:{Rect:iH},util:{Promise:Ri,Delay:Ii,Tools:gn,VK:tv,URI:uA,Class:lH,EventDispatcher:oz,Observable:az,I18n:ff,XHR:CH,JSON:bH,JSONRequest:xH,JSONP:yH,LocalStorage:NH,Color:hH,ImageUploader:hD},dom:{EventUtils:rc,Sizzle:Hc,DomQuery:Bs,TreeWalker:zi,TextSeeker:Gf,DOMUtils:Hs,ScriptLoader:Ys,RangeUtils:kv,Serializer:BA,StyleSheetLoader:Mi,ControlSelection:rv,BookmarkManager:Fh,Selection:S_,Event:rc.Event},html:{Styles:Vu,Entities:Au,Node:Ry,Schema:Hu,SaxParser:pA,DomParser:CA,Writer:Qy,Serializer:Zy},Env:en,AddOnManager:lf,Annotator:Mh,Formatter:AD,UndoManager:OD,EditorCommands:zU,WindowManager:WR,NotificationManager:jR,EditorObservable:dz,Shortcuts:Sz,Editor:Tz,FocusManager:jb,EditorManager:Wz,DOM:Hs.DOM,ScriptLoader:Ys.ScriptLoader,PluginManager:VR,ThemeManager:qR,IconManager:AR,Resource:cH,trim:gn.trim,isArray:gn.isArray,is:gn.is,toArray:gn.toArray,makeMap:gn.makeMap,each:gn.each,map:gn.map,grep:gn.grep,inArray:gn.inArray,extend:gn.extend,create:gn.create,walk:gn.walk,createNS:gn.createNS,resolve:gn.resolve,explode:gn.explode,_addCacheSuffix:gn._addCacheSuffix,isOpera:en.opera,isWebKit:en.webkit,isIE:en.ie,isGecko:en.gecko,isMac:en.mac},_H=gn.extend(Wz,EH),AH=function(t){try{e.exports=t}catch(n){}},RH=function(e){window.tinymce=e,window.tinyMCE=e};RH(_H),AH(_H)})()}).call(this,n("c8ba"))}}]);